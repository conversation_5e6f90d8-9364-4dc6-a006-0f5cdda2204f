#!/usr/bin/python
# -*- coding: utf-8 -*-#
# -------------------------------------------------------------------------------
# Name:         service_api_utils
# Description:  
# Author:       Spark
# Date:         22/9/2021
# -------------------------------------------------------------------------------
import logging
import json
import time
import copy
import io
from collections import OrderedDict
from datetime import datetime
from peewee import Tuple

from result import Result
from functools import wraps
from abd_utils.abd_util import (
    ApiSeqList, 
    create_api_duplicate_id_from_dict, 
    IGNORE_CONFIG,
    create_data_dict, 
    getMaxGroupNum, 
    delete_data_from_ids
)
from abd_utils.utils.func_base import (
    check_data_dict, 
    DEFAULT_MIN_VALUE, 
    DEFAULT_PAGE, 
    DEFAULT_PAGE_SIZE, 
    DEFAULT_PAGE_SIZE_MAX, 
    DEFAULT_7DAYS,
    gen_app_site_hash_id,
    gen_api_hash_id, 
    update_zk_trigger,
    RISK_LEVEL_OPTION_TO_LABEL,
    RISK_LEVEL_NUM_TO_OPTION
)
from abd_utils.utils.func_api_tag import PATH_TO_API_ENABLE_AUTO_TAG
from .service_mgr_utils import get_config, get_config_all, is_debug, has_api_permission, has_abd_server
from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.models.api_info_model import ApiInfoModel
from abd_utils.models.app_site_model import AppSiteModel
from abd_utils.models.api_defects_model import ApiDefectsModel
from abd_utils.models.api_defect_trace_log_model import ApiDefectTraceLogModel
from abd_utils.models.defect_name_summary_hour_model import DefectNameSummaryHourModel
from abd_utils.utils.func_api_relationship import SubpathApiFinder, convert_to_subpath_finder_api_info, check_api_conflict
from abd_utils.utils.func_api_relationship import PARAM_CONFLICT_FOUND, DUPLICATE_FOUND, CHECK_PASS, get_check_conflict_target_apis
from abd_utils.utils.func_ref import remove_ref_api, check_used_when_user_op, check_used_when_remove_app
from abd_utils.utils.func_base import gen_api_hash_id, update_zk_trigger, gen_api_endpoint
from abd_utils.schemas.api_info import ApiInfo
from abd_utils.utils import func_api
from abd_utils.utils.func_app_site import validate_servers, is_valid_app_usernames
from abd_utils.repositories.assets.app_site import AppSiteRepository
from abd_utils.utils import func_app_site
from abd_utils.models.ignore_api_info_model import IgnoreApiInfoModel
from abd_utils.repositories.assets.apis_detail import api_validate_forbidden_or_required_headers
from abd_utils.models.api_params_policies_model import ApiParamsPoliciesModel
from abd_utils.utils.func_database import Database
from abd_utils.utils.func_api_tag import BusinessTypeSettings
from api_base.api_const_define import CREATED_BY_RESTFUL
from abd_utils.utils.func_database import Database
from abd_utils.repositories.defects.defect import DefectRepository, merge_api_defect_status_priority
from abd_utils.utils.func_datetime_convert import TIME_FILTER_ALL, get_start_end_time_by_time_filter
from abd_utils.utils.func_risk import get_defect_kinds
from abd_utils.repositories.assets.business import BusinessRepository
from abd_utils.repositories.utils import get_array
from abd_utils.utils.func_industry_data_level import get_cur_industry_data_tag_levels
from abd_utils.validation_utils import validate_split_params, api_validate_path
from abd_utils.repositories.piis.pii import PiiRepository
from abd_utils.repositories.attacks.attack import AttackRepository
from abd_utils.utils.func_whitelist import modify_whitelist, append_pii_whitelist_by_api
from api_base.api_const_define import API_REQUEST_METHODS
from abd_utils.services.asset.export_config import ExportConfig

from abd_utils.services.app_site import AppSiteService
from abd_utils.services.asset.asset_service import AssetService

logger = logging.getLogger(__name__)

defect_type_kind = get_defect_kinds()

MIN_DATE = 1596273491  # 2020-8-1 17:18:11
MAX_DATE = 4102416000  # 2100-01-01 00:00:00
PAGE_SIZE_MAX = 1000

def translate_time(input_time):
    try:
        rt_time = int(input_time)
    except Exception as e:
        logger.error(e)
        return -1
    
    if rt_time < MIN_DATE:
        logger.error("Invalid start_time:{}".format(input_time))
        return -1
    
    return rt_time

def validate_api_time(start_time, end_time):
    if start_time is not None:
        start_time = translate_time(start_time)
        if start_time < 0:
            logger.error("Invalid start_time:{}".format(start_time))
            return -1, None, None

    if end_time is not None:
        end_time = translate_time(end_time)
        if end_time < 0:
            logger.error("Invalid end_time:{}".format(end_time))
            return -1, None, None

    if start_time is not None and end_time is not None:
        if end_time <= start_time:
            logger.error("end_time <= start_time")
            return -1, None, None

    return 0, start_time, end_time

def check_abd_enabled(func):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        if not (has_api_permission() or is_debug()):
            self.finish_failed(*Result.LICENSE_INVALID)
            return
        
        if not has_abd_server():
            self.finish_failed(*Result.API_ROLE_DISABLED)
            return

        return func(self, *args, **kwargs)
    return wrapper


def get_ignore_duplicated_id_set(ignore_list):
    return set([create_api_duplicate_id_from_dict(record) for record in ignore_list])


def get_auto_tag_enable():
    return get_config(PATH_TO_API_ENABLE_AUTO_TAG, True)

def get_api_list_from_pg(handler):
    validate_config = OrderedDict([
        ('page_no', {
            'value': lambda x: x.get('page_no', None),
            'validation_func': lambda x: x is None or (int(x) > DEFAULT_MIN_VALUE)
        }),
        ('page_size', {
            'value': lambda x: int(x.get('page_size', DEFAULT_PAGE_SIZE)),
            'validation_func': lambda x: x > DEFAULT_MIN_VALUE and x <= PAGE_SIZE_MAX
        }),
        ('start_time', {
            'value': lambda x: int(x.get('start_time', MIN_DATE)),
            'validation_func': lambda x: translate_time(x) > DEFAULT_MIN_VALUE
        }),
        ('end_time', {
            'value': lambda x: int(x.get('end_time', MAX_DATE)),
            'validation_func': lambda x: translate_time(x) > DEFAULT_MIN_VALUE
        }),
    ])
    try:
        params = _get_arguments(handler, validate_config)
        params = check_data_dict(params, validate_config)
        conditions = {
            'page_size': params['page_size'],
            
        }
        start_time = params.get('start_time', None)
        end_time = params.get('end_time', None)
        ret, _, _ = validate_api_time(start_time, end_time)
        if ret == -1:
            return False, Result.ARGUMENT_ERROR
        
        page_no = params.get('page_no', -1)
        if not page_no is None:
            conditions['page_no'] = int(page_no)
        
        if start_time and end_time:
            conditions['created_time'] =  'from:{},to:{}'.format(start_time, end_time)
        data = ApiRepository().get_all_api_list_detail(conditions)
        for item in data:
            # 保持字段写CSV导出一致
            for key, val in item.items():
                if key not in ExportConfig.get_csv_field_mapping() and key not in ['id', 'app_site_id']:
                    item.pop(key)
                    
        return True, {'api_list': data}
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR


def gen_api_info_by_schema1(args):
    def get_api_info_fields():
        from abd_utils.utils.func_database import DatabaseModel
        rt = set(dir(ApiInfoModel)) - set(dir(DatabaseModel))
        rt.add('id')
        return rt
    
    api_info = {}
    keys = get_api_info_fields()
    for key in keys:
        api_info[key] = args[key]

    return api_info

def gen_api_info_model_dict(args):
    return {
                'id': args.get('id', '1'),
                'updated_time': args.get('updated_time', 0),
                'created_time': args.get('created_time', 0),
                'is_online': args.get('is_online', True),
                'is_custom': args.get('is_custom', True),
                'app_site_id': args.get('app_site_id', '1'),
                'name': args.get('name', ''),
                'api_path': args.get('api_path', '/'),
                'endpoint': args.get('endpoint', '/'),
                'is_included_sub_path': args.get('is_included_sub_path', False),
                'method': args.get('method', 'GET'),
                'business_types': args.get('business_types', []),
                'industry_types': args.get('industry_types', []),
                'created_by': args.get('created_by', CREATED_BY_RESTFUL),
                'industry_levels': args.get('industry_levels', []),
                'split_param_enabled': args.get('split_param_enabled', False),
                'split_params': args.get('split_params', []),
        }

def validate_business_types(tags):
    if len(tags) != len(set(tags)):
        return False

    if len(tags) > 5:
        return False
    all_business_types = BusinessTypeSettings().get_business_name_list()
    all_business_types = set([tag.encode('utf8') for tag in all_business_types])
    for tag in tags:
        if tag.encode('utf8') not in all_business_types:
            return False
    return True

def validate_industry_types(tags, all_tags_levels):
    if len(tags) != len(set(tags)):
        return False

    if not isinstance(tags, list) or len(tags) > 5:
        return False
    
    if set(tags) - set(all_tags_levels.keys()):
        return False
    
    return True

def validate_required_header(headers):
    try:
        api_validate_forbidden_or_required_headers(headers, 'required_headers')
        return True
    except Exception as e:
        return False
    

def validate_forbidden_header(headers):
    try:
        api_validate_forbidden_or_required_headers(headers, 'forbidden_headers')
        return True
    except Exception as e:
        return False

def restful_validate_split_params(split_params):
    try:
        validate_split_params(split_params)
        return True
    except Exception as e:
        logging.exception(e)
        return False

def get_necessary_field(info, field):
    if field not in info:
        raise Exception('Invalid request body args: missing field {}'.format(field))
    return info.get(field)


def get_optional_field(info, field, default_value):
    """
    Used to get some optional field. If optional_field not in the info, return default value
    This method is used for new field added for existing APIs to get backward compatibility
    """
    if field not in info and default_value is None:
        raise Exception('Invalid request body args: missing field {}'.format(field))
    return info.get(field) or default_value



def get_necessary_field_fallback(info, field, fallback_field):
    if field not in info and fallback_field not in info:
        raise Exception('Invalid request body args: missing field {}'.format(field))
    return info.get(field, info.get(fallback_field))

RESTFUL_API_CONFIG = OrderedDict([
    ('app_site_name', {
        'value': lambda x: get_necessary_field(x, 'app_site_name'),  
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) >=1 and len(x) <= 128
    }),
    ('endpoint', {
        'value': lambda x: get_necessary_field_fallback(x, 'endpoint', 'api_path'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) >=1 and len(x) <= 500
    }),
    ('method', {
        'value': lambda x: get_necessary_field(x, 'method'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and x in set(API_REQUEST_METHODS)
    }),
    ('is_included_sub_path', {
        'value': lambda x: get_necessary_field(x, 'is_included_sub_path'),
        'validation_func': lambda x: x in (True, False) and isinstance(x, bool)
    }),
    ('api_name', {
        'value': lambda x: x.get('api_name', ''),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) >=0 and len(x) <= 50
    }),
    ('business_types', {
        'value': lambda x: x.get('business_types', []),
        'validation_func': lambda x: validate_business_types(x)
    }),
    ('enable_args', {
        'value': lambda x: x.get('enable_args', False),
        'validation_func': lambda x: x in (True, False) and isinstance(x, bool)
    }),
    ('api_max_query_args_cnt', {
        'value': lambda x: x.get('api_max_query_args_cnt', -1),
        'validation_func': lambda x: isinstance(x, int) and x >= -1 and x <= 100
    }),
    ('api_max_body_args_cnt', {
        'value': lambda x: x.get('api_max_body_args_cnt', -1),
        'validation_func': lambda x: isinstance(x, int) and x >= -1 and x <= 100
    }),
    ('api_max_body_size', {
        'value': lambda x: x.get('api_max_body_size', -1),
        'validation_func': lambda x: isinstance(x, int) and x >= -1 and x <= 1000000000
    }),
    ('required_headers', {
        'value': lambda x: x.get('required_headers', []),
        'validation_func': lambda x: validate_required_header(x)
    }),
    ('forbidden_headers', {
        'value': lambda x: x.get('forbidden_headers', []),
        'validation_func': lambda x: validate_forbidden_header(x)
    })
])

# 切分endpoint，返回api_path、split_param_enabled、split_params，一定是合法的
# 保证外层有try...except捕获处理异常
def validate_and_split_endpoint(endpoint, is_included_sub_path, path_prefix):

    if '?' not in endpoint:
        api_path = endpoint
        split_param_enabled = False
        split_params = []
    else:
        split_param_enabled = True
        endpoint_split = endpoint.split('?')
        assert len(endpoint_split) == 2 and endpoint_split[0] != '' and endpoint_split[1] != ''
        api_path = endpoint_split[0]    
        param_split = endpoint_split[1].split('=')
        assert len(param_split) == 2 and param_split[0] != '' and param_split[1] != ''
        param_value = param_split[1]
        pos_name_split = param_split[0].split('.')
        assert len(pos_name_split) >= 2
        param_pos = pos_name_split[0]
        param_name = '.'.join(pos_name_split[1:])
        split_params = [{'position': param_pos, 'name': param_name, 'value': [param_value]}]
        validate_split_params(split_params)
    
    # 如果匹配子路径，不含参数部分必须以/*结尾
    if is_included_sub_path:
        assert api_path.endswith('/*')
        api_path = api_path[:-2]
        if not api_path:
            api_path = '/'
    
    api_validate_path(api_path, path_prefix)
    
    return api_path, split_param_enabled, split_params

def add_apis_into_pg(apis, created_by, api_config=RESTFUL_API_CONFIG):

    app_site = None
    check_target_apis = None
    same_app_apis = []

    pending_apis = []
    pending_api_params = []
    now = int(time.time())
    
    for input_api_info in apis:
        try:
            unsupport_fields = set(input_api_info.keys()) - set(api_config.keys())
            if len(unsupport_fields) > 0:
                logging.error("unsupported fields:{}".format(unsupport_fields))
                return False, (Result.APP_OR_API_CONTAIN_UNALLOWED_FIELD[0], 'Field not allowed: {}'.format(",".join(str(x) for x in unsupport_fields)), 400)

            api_info = check_data_dict(input_api_info, api_config)
        except Exception as e:
            logging.exception(e)
            return False, Result.ARGUMENT_ERROR
        
        if app_site is None:
            # restful只加一条， scanner加的是同一应用的
            if created_by == CREATED_BY_RESTFUL:
                app_site_name = api_info.pop('app_site_name')
                api_info['name'] = api_info.pop('api_name') 
                app_sites = AppSiteModel.select(AppSiteModel).where(AppSiteModel.name == app_site_name)[:]
            else:
                # created by scanner
                app_sites = AppSiteModel.select(AppSiteModel).where(AppSiteModel.id == api_info.get('app_site_id'))[:]

            if len(app_sites) == 0:
                return False, Result.APP_SITE_NOT_EXISTS

            app_site = app_sites[0]
        
        # 切分校验endpoint, 取出api_path、split_param_enabled、split_params
        try:
            # 添加API可以传入endpoint，或 api_path.
            #     endpoint支持参数拆分配置， api_path不支持参数拆分
            endpoint = input_api_info.get('endpoint')
            if not endpoint:
                endpoint = input_api_info.get('api_path')
                if '?' in endpoint: 
                    return False, Result.ARGUMENT_ERROR

            api_path, split_param_enabled, split_params = validate_and_split_endpoint(endpoint, api_info['is_included_sub_path'], app_site.path_prefix)
            api_info['api_path'] = api_path
            api_info['split_param_enabled'] = split_param_enabled
            api_info['split_params'] = split_params
        except Exception as e:
            logging.exception(e)
            return False, Result.ARGUMENT_ERROR
        
        if check_target_apis is None:
            check_target_apis = get_check_conflict_target_apis({'app_site_id': app_site.id, 'split_param_enabled': api_info['split_param_enabled']})
        
        # 查重、参数冲突
        check_result = check_api_conflict(api_info, check_target_apis)
        if check_result['status'] == DUPLICATE_FOUND:
            return False, Result.API_EXIST_INVALID

        if check_result['status'] == PARAM_CONFLICT_FOUND:
            return False, Result.INVALID_API_INFO_SETTINGS
    
        api_info['app_site_id'] = app_site.id
        api_info['created_by'] = created_by
    
        try:
            api_info_model_dict = gen_api_info_model_dict(api_info)
            api_id = gen_api_hash_id(ApiInfo(api_info_model_dict))
            input_api_info['id'] = api_id  # 样例等其它数据保存时需要api_id
            api_info['id'] = api_id
            api_info['endpoint'] = gen_api_endpoint(api_info)
            api_info['created_time'] = now
            api_info['updated_time'] = now
            api_info['visited_time'] = now
        except Exception as e:
            logging.exception(e)
            return False, Result.INVALID_API_INFO_SETTINGS

        pending_apis.append(ApiInfoModel(**api_info))

        # 创建API后，再写入参数检测配置
        if api_info["enable_args"] == True:
            
            api_params_policies = {
                'api_id' : api_id,
                'enable_args': api_info['enable_args'],
                'required_headers': api_info['required_headers'],
                'forbidden_headers': api_info['forbidden_headers'],
                'api_max_body_args_cnt': api_info['api_max_body_args_cnt'],
                'api_max_body_size': api_info['api_max_body_size'],
                'api_max_query_args_cnt': api_info['api_max_query_args_cnt'],
                'api_params_rules': "[]"
            }
            pending_api_params.append(ApiParamsPoliciesModel(**api_params_policies))

    if pending_apis:

        # 提交的数据中，存在相同的API。
        api_ids = {item.id: 1 for item in pending_apis}
        if len(api_ids) != len(pending_apis):
            return False, Result.ARGUMENT_ERROR

        with Database.session.atomic():
            ApiInfoModel.bulk_create(pending_apis, batch_size=1000)
            if pending_api_params:
                ApiParamsPoliciesModel.bulk_create(pending_api_params, batch_size=1000)
            
        update_zk_trigger(func_api.TRIGGER_PATH, sync=True)

    return True, ''


def add_api_by_restful(handler):
    api_info = handler.json_args
    return add_apis_into_pg([api_info], CREATED_BY_RESTFUL)

def modify_api_into_pg(handler):

    api_info = handler.json_args
    try:

        for key in api_info.keys():
            # not allow to change related_domain and path_prefix because they result in app site id change
            if key not in ['id', 'api_name','business_types', 'industry_types']:
                return False, (Result.APP_OR_API_CONTAIN_UNALLOWED_FIELD[0], 'Field not allowed: {}'.format(key), 400)

        id = api_info.get('id', None)
        assert (id is not None)

    except Exception as e:
        return False, Result.INVALID_REQ_BODY
    
    all_industry_tags_levels = get_cur_industry_data_tag_levels()

    validation_func = {
        'id': (lambda x: isinstance(x, (str, unicode)) and len(x) >= 0 and len(x) <= 27),
        'api_name': (lambda x: isinstance(x, (str, unicode)) and len(x) >=0 and len(x) <= 50),
        'business_types': (lambda x: validate_business_types(x)),
        'industry_types': (lambda x: validate_industry_types(x, all_industry_tags_levels))
    }

    for key, value in api_info.items():
        if validation_func[key](value) == False:
            return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field {}'.format(key), 400)

    api_repo = ApiRepository()
    if not api_repo.is_exists(id):
        return False, Result.API_UNKNOWN_API_INVALID
    api = api_repo.get_api_by_id(id).dicts().get()

    name = api_info.get('api_name', api['name'])
    business_types = api_info.get('business_types', api['business_types'])
    industry_types = api_info.get('industry_types', api['industry_types'])

    app_site = AppSiteRepository().get_app_site_by_id(api['app_site_id'])
    if len(app_site) == 0:
        return False, Result.APP_SITE_NOT_EXISTS

    api['name'] = name
    api['business_types'] = business_types
    api['industry_types'] = industry_types
    api['industry_levels'] = [all_industry_tags_levels.get(tag) for tag in industry_types]

    logging.info('api={}'.format(api))
    change_cnt = api_repo.update_api_base_info_by_id(id, id, api)
    if change_cnt <= 0:
        return False, Result.API_UPDATE_FAILED

    update_zk_trigger(func_api.TRIGGER_PATH)

    return True, ''


def delete_api_from_pg(handler):

    if 'id' not in handler.json_args and 'api_id' not in handler.json_args:
        return False, Result.ARGUMENT_ERROR
        
    api_id = handler.json_args['id'] if 'id' in handler.json_args else handler.json_args.get('api_id', '')
    api_ids = ApiInfoModel.select(ApiInfoModel.id).where(ApiInfoModel.id == api_id)[:]
    if len(api_ids) == 0:
        return False, Result.API_UNKNOWN_API_INVALID
    
    ref_apis, ref_apps = check_used_when_user_op([api_id])

    if ref_apis.get('api_seq'):
        return False, Result.API_IN_API_SEQ_INVALID

    if ref_apis.get('account_tracking'):
        return False, Result.API_IN_API_ACCOUNT_TRACKING_INVALID

    if ref_apis.get('attack_policy'):
        return False, Result.API_IN_API_ATTACK_POLICY_INVALID
    
    ApiRepository().delete_by_ids([api_id], need_delete_null_app=True)

    return True, ''

 
def ignore_api_from_pg(handler):
    api_id = handler.json_args['id'] if 'id' in handler.json_args else handler.json_args.get('api_id', '')
    api_ids = ApiInfoModel.select(ApiInfoModel.id).where(ApiInfoModel.id == api_id)[:]
    if len(api_ids) == 0:
        return False, Result.API_UNKNOWN_API_INVALID
    
    ref_apis, ref_apps = check_used_when_user_op([api_id])

    if ref_apis.get('api_seq'):
        return False, Result.API_IN_API_SEQ_INVALID

    if ref_apis.get('account_tracking'):
        return False, Result.API_IN_API_ACCOUNT_TRACKING_INVALID

    if ref_apis.get('attack_policy'):
        return False, Result.API_IN_API_ATTACK_POLICY_INVALID
    
    repo = ApiRepository()
    repo.add_ignore([api_id])

    return True, ''

def onoff_api_from_pg(handler, enable, api_id):
    repo = ApiRepository()
    assert enable in ('on', 'off')

    apis = ApiInfoModel.select(ApiInfoModel).where(ApiInfoModel.id == api_id)
    if len(apis) == 0:
        return False, Result.API_UNKNOWN_API_INVALID
    
    is_online = True if enable == 'on' else False
    off_site_list, _ = repo.onoff([api_id], {}, is_online)
    if off_site_list:
        return False, Result.APP_SITE_OFFLINE
    return True, ''

 
def exports_api_from_pg(file_type, conditions={}):
    contents = AssetService().get_export_content(conditions, file_type)
    return contents


def get_app_site_list():
    _, app_sites = AppSiteService().get_list({"page":-1, "page_size": -1}, AppSiteService.SOURCE_RESTFUL)
    return app_sites

def _validate_app_site_info(app_site_info):
    # 校验字段合法性
    validation_length_dict = {
        'name'       : (1, 128),
        'responsible': (0, 50),
        'department' : (0, 50),
        'app_group_name' : (0, 128) 
    }

    for key, value in validation_length_dict.items():
        field = app_site_info.get(key, None)
        if field is None:
            continue

        if isinstance(field, (str, unicode)):
            field = field.strip()
            if (len(field) < value[0]) or (len(field) > value[1]):
                return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field {}'.format(key), 400)
        else:
            return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field {}'.format(key), 400)
    return True, ''

def add_app_site(app_site_info):
    # 校验必须字段是否存在
    necessary_keys = set(['name', 'related_domain'])
    missing_keys = list(necessary_keys - set(app_site_info.keys()))
    if missing_keys:
        return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args, missing field: {}'.format(', '.join(missing_keys)), 400)
    
    # 校验是否出现不允许传递的字段
    allowed_keys = set(['name', 'related_domain', 'responsible', 'username', 'department', 'path_prefix', 'app_group_name'])
    not_allowd_keys = list(set(app_site_info.keys()) - allowed_keys)
    if not_allowd_keys:
        return False, (Result.APP_OR_API_CONTAIN_UNALLOWED_FIELD[0],'Field not allowed: {}'.format(', '.join(not_allowd_keys)), 400)

    try:
        servers = app_site_info['related_domain']
        for related_domain in servers:
            related_domain['protocol'] = 'http'
        validate_servers(servers)
    except Exception as e:
        return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field related_domain', 400)
    
    is_validate, msg = _validate_app_site_info(app_site_info)
    if not is_validate:
        return is_validate, msg

    usernames = app_site_info.get('username', '')
    if usernames:
        if not is_valid_app_usernames(app_site_info['username']):
            return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field username', 400)
    else:
        app_site_info['username'] = 'admin'

    path_prefix = app_site_info.get('path_prefix', '')
    if path_prefix:
        try:
            func_app_site.validate_path_prefix(path_prefix)
        except:
            return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field path_prefix', 400)

    app_site_info['servers'] = servers
    app_site_info.pop('related_domain')
    app_site_id = gen_app_site_hash_id(servers[0], path_prefix)
    app_site_info['created_by'] = CREATED_BY_RESTFUL

    repo = AppSiteRepository()
    if repo.is_name_exists(app_site_info['name']):
        return False, Result.APP_SITE_NAME_EXISTS
    
    if repo.is_servers_and_pathprefix_exists(servers, path_prefix):
        return False, Result.APP_SITE_SERVERS_EXISTS
        
    with Database.session.atomic():
        if not repo.is_exists(app_site_id):
            obj = repo.create(app_site_id, app_site_info)
        else:
            return False, Result.APP_SITE_SERVERS_EXISTS

        if path_prefix:
            ApiRepository().assign_api_for_prefix_app(obj)

    update_zk_trigger([func_app_site.TRIGGER_PATH, func_app_site.TRIGGER_PG_USER])
        
    return True, ''


def modify_app_site(app_site_info):
    
    # 校验必须字段是否存在
    necessary_keys = set(['id'])
    missing_keys = list(necessary_keys - set(app_site_info.keys()))
    if missing_keys:
        return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args, missing field: {}'.format(', '.join(missing_keys)), 400)
    
    # 校验是否出现不允许传递的字段, 修改应用不允许修改关联域名
    allowd_keys = set(['id', 'name', 'responsible', 'username', 'department', 'app_group_name'])
    not_allowed_keys = list(set(app_site_info.keys()) -  allowd_keys)
    if not_allowed_keys:
        return False, (Result.APP_OR_API_CONTAIN_UNALLOWED_FIELD[0], 'Field not allowed: {}'.format(', '.join(not_allowed_keys)), 400)
    
    app_site_id = app_site_info.get('id', None)
    if not app_site_id:
        return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: missing field id', 400)

    is_validate, msg = _validate_app_site_info(app_site_info)
    if not is_validate:
        return is_validate, msg
    
    repo = AppSiteRepository()
    app_site = repo.get_app_site_by_id(app_site_id)
    if not app_site:
        return False, Result.APP_SITE_NOT_EXISTS

    usernames = app_site_info.get('username', '')
    is_user_updated = True
    if usernames:
        if not is_valid_app_usernames(usernames):
            return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: invalid field username', 400)
    else:
        is_user_updated = False
        usernames = app_site['username']
    
    name = app_site_info.get('name', None)
    if name:
        if repo.is_name_exists(name, self_id=app_site_id):
            return False, Result.APP_SITE_NAME_EXISTS
        app_site['name'] = name

    app_site['responsible'] = app_site_info.get('responsible', app_site.get('responsible'))
    app_site['department'] = app_site_info.get('department', app_site.get('department'))
    app_site['app_group_name'] = app_site_info.get('app_group_name', app_site.get('app_group_name'))
    app_site['username'] = usernames

    logging.info('app_site: {}'.format(json.dumps(app_site)))
    with Database.session.atomic():
        obj = repo.update(app_site_id, app_site_id, app_site)
        if obj is None:
            return False, Result.APP_SITE_SERVERS_EXISTS

    if is_user_updated:
        update_zk_trigger(func_app_site.TRIGGER_PG_USER)

    update_zk_trigger(func_app_site.TRIGGER_PATH)

    return True, ''


def delete_app_site(app_site_name):
    if not app_site_name:
        return False, (Result.INVALID_REQ_BODY[0], 'Invalid request body args: missing field name', 400)

    app_site_ids = AppSiteModel.select(AppSiteModel.id).where(AppSiteModel.name == app_site_name).dicts()[:]
    if len(app_site_ids) == 0:
        return False, Result.APP_SITE_NOT_EXISTS

    app_site_id = app_site_ids[0]['id']
    ref_apis, ref_apps = check_used_when_remove_app([app_site_id])

    if ref_apis or ref_apps:
        return False, Result.APP_OR_API_REFERENCE_FOUND
    
    # 删除应用时候检查关联性顺序: 攻击策略 -> 
    if ref_apps.get('attack_policy', None) is not None:
        return False, Result.APP_SITE_REFER_ATTACK_POLICY_INVALID

    app_repo = AppSiteRepository()
    api_repo = ApiRepository()
    app_site = app_repo.get_app_site_by_id(app_site_id)
    if app_site.get('path_prefix', None):
        main_app = api_repo.get_main_app_site(app_site)
        if main_app is not None:
            api_repo.return_api_for_prefix_app(app_site)

    app_repo.delete_by_id(app_site_id)

    update_zk_trigger(func_app_site.TRIGGER_PG_USER)
    
    update_zk_trigger(func_app_site.TRIGGER_PATH)

    return True, ''


def onoff_app_site(app_site_name, online_status):
    app_site_ids = AppSiteModel.select(AppSiteModel.id).where(AppSiteModel.name == app_site_name).dicts()[:]
    if len(app_site_ids) == 0:
        return False, Result.APP_SITE_NOT_EXISTS
    
    app_site_id = app_site_ids[0]['id']
    repo = AppSiteRepository()
    repo.set_online_status(app_site_id, online_status)
    
    update_zk_trigger(func_app_site.TRIGGER_PATH)

    return True, ''

 
def get_ignore_api_from_pg():
    return IgnoreApiInfoModel.select(IgnoreApiInfoModel).dicts()[:]

def is_invalid_user(user):
    user_list = get_config_all('user')
    if user == '':
        return False
    # logging.error("--------zk-self.user_list--:{}".format(self.user_list))
    if user_list:
        user_key = user_list.keys()
        if user not in user_key:
            return True
    else:
        return True
    return False


def import_api_list_into_pg(handler, file_content):
    
    f = io.BytesIO(file_content.encode('utf8'))
    f.content_type = 'text/csv'
    f.name = 'api_list.csv'

    new_api_count, updated_api_count, failed_api_count, no_change_api_count = AssetService().import_content(f, AssetService.TYPE_CSV)
    
    update_zk_trigger(func_api.TRIGGER_PATH)
    update_zk_trigger(func_app_site.TRIGGER_PATH)

    result = {
            'err_no': 0,
            'err_msg': 'Successfully import {} APIs. Failed to import {} APIs. {} APIs no changed and {} APIs updated'.format(new_api_count, failed_api_count, no_change_api_count, updated_api_count)
        }

    return True, result

def create_duplicate_ids_dict(api_list):
    return {create_api_duplicate_id_from_dict(api): index for index, api in enumerate(api_list)}


def has_exist_api(api_info, duplicate_id_dict):
    duplicate_id = create_api_duplicate_id_from_dict(api_info)
    # logger.error("----duplicate_id---:{}".format(duplicate_id))
    # logger.error("----duplicate_id_dict---:{}".format(duplicate_id_dict))
    if duplicate_id_dict.get(duplicate_id, None) is None:
        return False

    return True


def create_group_info(info):
    group = create_data_dict(info, GROUP_CONFIG)
    return group


def merge_group(group, group_all_list):
    for idx, item in enumerate(group_all_list):
        if item['id'] == group['id'] or item['name'] == group['name']:
            group_all_list.pop(idx)
            break
    group_all_list.append(group)

    return group_all_list


def remove_group_in_group_list(group_id, group_all_list):
    return delete_data_from_ids([group_id], group_all_list)


def check_max_group(group_list):
    rt = False
    if len(group_list) > getMaxGroupNum():
        logger.error('The number of API group exceeds {}.'.format(len(group_list)))
        rt = True

    return rt


def update_group_info_in_api_list(api_list, group):
    for idx, api in enumerate(api_list):
        if api['group_id'] == group['id']:
            api['group_name'] = group['name']

    return api_list


def is_in_api_seq(id_list):
    api_ref_id_list = ApiSeqList.get_refer_list(id_list)
    if len(api_ref_id_list) > 0:
        logger.debug("Api {} already used in api seq rule".format(",".join(api_ref_id_list)))
        return True
    return False


def is_invalid_api_id(zk_list, api_id):
    lookup_set = set([item.get('id') for item in zk_list])
    if api_id in lookup_set:
        return False
    return True


def get_api_by_id(zk_list, api_id):
    for item in zk_list:
        if api_id == item.get('id'):
            return item
    return None


def added_to_ignore_list(req_ignore_list, ignore_list):
    ignore_ids = get_ignore_duplicated_id_set(ignore_list)
    # process ignore_list
    for api in req_ignore_list:
        duplicate_id = create_api_duplicate_id_from_dict(api)
        if duplicate_id not in ignore_ids:
            ignore_api = create_data_dict(api, IGNORE_CONFIG)
            ignore_list.append(ignore_api)

    return ignore_list


# def create_ignore_api(api_info, ignore_list):
#     ignore_ids = get_ignore_duplicated_id_set(ignore_list)
#     ignore_api =  create_data_dict(api_info, IGNORE_CONFIG)
#     duplicate_id = create_api_duplicate_id_from_dict(api_info)
#     if duplicate_id not in ignore_ids:
#         ignore_list.append(ignore_api)

#     check_capacity('ignore_list', ignore_list)
#     ignore_list
#     return ignore_list


def create_export_discover_api_list_file(api_list, csv_file, export_fields, header=None):
    default_header = header
    if header is None:
        default_header = ','.join(export_fields)
    with open(csv_file, 'w') as f:
        content = [default_header]
        if api_list:
            for api in api_list:
                fields = []
                for field in export_fields:
                    field_value = api[field]
                    if isinstance(field_value, list):
                        field_value = ','.join(field_value)
                    if not isinstance(field_value, str):
                        field_value = str(field_value)
                    field_value = '"' + field_value + '"'
                    fields.append(field_value)
                # create string list
                content.append(','.join(fields))
        f.write('\n'.join(content).encode('utf-8'))

def validate_defect_status(defect_status):
    return defect_status in merge_api_defect_status_priority

def validate_get_defects_params(defect_status, page, page_size, start_time, end_time):
    if not validate_defect_status(defect_status):
        return False, Result.ARGUMENT_ERROR

    ret, _, _ = validate_api_time(start_time, end_time)
    if ret == -1:
        return False, Result.ARGUMENT_ERROR

    if page < 0 or page_size < 1 or page_size > 200:
        return False, Result.ARGUMENT_ERROR

    return True, Result.SUCCESS

def api_get_defects(handler):

    try:
        defect_status = int(handler.get_argument('defect_status', -1))
        page = int(handler.get_argument('page', 1))
        page_size = int(handler.get_argument('page_size', 10))
        start_time = int(handler.get_argument('start_time', MIN_DATE))
        end_time = int(handler.get_argument('end_time', int(time.time())))
    except:
        return False, Result.ARGUMENT_ERROR

    ret, ret_code = validate_get_defects_params(defect_status, page, page_size, start_time, end_time)
    if not ret:
        return False, ret_code

    time_filter = 'from:' + str(start_time) + ',to:' + str(end_time)
    condition = {'defect_status': defect_status, 
                 'page': page, 
                 'page_size': page_size, 
                 'time_filter': time_filter}
    

    defect_repo = DefectRepository()
    data, count = defect_repo.get_defects(condition)
    results = []
    api_ids = []
    for d in data[:]:
        api_ids.append(d['api_id'])
        d['discover_nums'] = int(d['discover_nums'])
        d['defect_kind'] = defect_type_kind.get(d['defect_name'].encode('utf8'), '未知')
        results.append(d)
    business_types = BusinessRepository().get_business_names_by_api_ids(api_ids)
    for item in results:
        business = business_types.get(item['api_id'], None)
        item['auto_business_types'] = get_array(business['auto_business_types'])
        item['manual_business_types'] = get_array(business['manual_business_types'])

    return True, {"data":results, "counts": count[defect_status]}

def validate_update_defects_params(new_status, api_infos, max_size=200):
    if not validate_defect_status(new_status):
        return False, Result.ARGUMENT_ERROR, None, None

    if not api_infos or not isinstance(api_infos, list):
        return False, Result.ARGUMENT_ERROR, None, None

    if len(api_infos) > max_size:
        return False, Result.MAX_INVALID, None, None

    api_id_defect_names = []
    all_infos = {}
    i = int(time.time())
    for info in api_infos:
        api_id = info.get('api_id', None)
        defect_name = info.get('defect_name', None)
        if not api_id or not defect_name:
            all_infos[str(i)] = info
            i += 1
            continue

        all_infos[api_id + defect_name] = info
        api_id_defect_names.append((api_id, defect_name))

    if not api_id_defect_names:
        return False, Result.ARGUMENT_ERROR, None, all_infos
    
    return True, Result.SUCCESS, api_id_defect_names, all_infos

def api_update_defect_status(handler):
    new_status = handler.json_args.get('new_status', -1)
    api_infos = handler.json_args.get('api_infos', [])

    ret, ret_code, api_id_defect_names, all_infos = validate_update_defects_params(new_status, api_infos)
    if not ret:
        return False, ret_code

    defect_repo = DefectRepository()
    _, start_time, end_time = get_start_end_time_by_time_filter(TIME_FILTER_ALL)
    api_query = defect_repo.get_defect_by_name_status(start_time, end_time, None)
    cond = Tuple(api_query.c.api_id, api_query.c.defect_name).in_(api_id_defect_names)
    api_info_objects = DefectNameSummaryHourModel.select(api_query.c.api_id,
                                                            api_query.c.defect_name,
                                                            api_query.c.defect_status,
                                                            ).from_(api_query).where(cond).dicts()
    logs = []
    now = datetime.fromtimestamp(int(time.time()))
    defects = []

    for api_info in api_info_objects:
        key = api_info['api_id'] + api_info['defect_name']
        if key in all_infos.keys():
            all_infos.pop(key)

        defect, created = ApiDefectsModel.get_or_create(api_id=api_info['api_id'],
                                                        name=api_info['defect_name'],
                                                        defaults={
                                                            'status': new_status,
                                                            'updated_time': now,
                                                            'created_time': now})

        if created or defect.status != new_status:
            logs.append({'api_id': api_info['api_id'],
                            'defect_name': api_info['defect_name'],
                            'username': handler.get_argument('tokenid', 'api_admin'),
                            'src_status': api_info['defect_status'],
                            'to_status': new_status,
                            'updated_time': now,
                            'created_time': now})
            if defect.status != new_status:
                defect.status = new_status
                defect.updated_time = now
                defects.append(defect)

    with Database.session.atomic():
        try:
            if len(defects) > 0:
                ApiDefectsModel.bulk_update(defects, ['status', 'updated_time'], batch_size=1000)
            defect_repo.batch_create(ApiDefectTraceLogModel, logs, batch_size=1000)
        except Exception as e:
            logging.error("update defect status, db failed: {}".format(e))
            logging.exception(e)

    if all_infos:
        logging.info("update defect status, some list failed: {}".format(list(all_infos.values())))
        return True,  {
            'err_no': Result.API_DEFECT_STATUS_PART[0],
            'err_msg': Result.API_DEFECT_STATUS_PART[1],
            "api_infos": all_infos.values()
        }
    else:
        return True, {}
    
def current_unix_timestamp():
    return int(time.time())

def _get_arguments(handler, validate_config):
    params = {}
    for key in validate_config.keys():
        value = handler.get_argument(key, None)
        if value:
            params[key] = value
    return params

def _bind_get_conditions(handler):
    """
    page default 1
    page_size default 50
    start_time default now - 7day
    end_time default now
    """
    now = current_unix_timestamp()
    validate_config = OrderedDict([
        ('page', {
            'value': lambda x: int(x.get('page', DEFAULT_PAGE)),
            'validation_func': lambda x: x > DEFAULT_MIN_VALUE
        }),
        ('page_size', {
            'value': lambda x: int(x.get('page_size', DEFAULT_PAGE_SIZE)),
            'validation_func': lambda x: x > DEFAULT_MIN_VALUE and x <= DEFAULT_PAGE_SIZE_MAX
        }),
        ('start_time', {
            'value': lambda x: int(x.get('start_time', now - DEFAULT_7DAYS)),
            'validation_func': lambda x: translate_time(x) > DEFAULT_MIN_VALUE
        }),
        ('end_time', {
            'value': lambda x: int(x.get('end_time', now)),
            'validation_func': lambda x: translate_time(x) > DEFAULT_MIN_VALUE
        }),
    ])
    
    params = _get_arguments(handler, validate_config)
    params = check_data_dict(params, validate_config)
    
    return {
        'page': params['page'],
        'page_size': params['page_size'],
        'time_filter': 'from:{},to:{}'.format(params['start_time'], params['end_time']),
        'search': {
            'keyword': '',
            'match': ''
        },
    }


def api_get_piis(handler):
    try:
        params = _bind_get_conditions(handler)
        results = PiiRepository().get_piis_list(params)
        return True, {"data":results.get('pii_list', []), "counts": results.get('total_count', DEFAULT_MIN_VALUE)}
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR

def api_get_attacks(handler):
    try:
        params = _bind_get_conditions(handler)

        # TODO: here is mock search 
        # Only attack list.
        params.update({
             # Default sorting
            'rank': {
                'type': 'desc',
                'field': 'start_time'
            }
        })
        total_count, events = AttackRepository().get_attack_event(params, 'guest')
        return True, {"data":events, "counts": total_count}
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR
    
def set_whitelist_by_key_module(handler):
    """
    setting whitelist by module, TODO: current unused.
    """
    try:
        key = handler.json_args.get('key', None)
        assert key != None
        module = handler.json_args.get('module', '')
        assert(module in ('piis', 'defects', 'attacks'))
        whitelist = handler.json_args.get('whitelist', None)
        assert whitelist != None
        modify_whitelist(key, module, whitelist)
        return True, {}
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR
    
def append_pii_whitelist_by_api_id_pii_type(handler):
    try:
        api_id = str(handler.json_args.get('api_id', '')).strip()
        assert api_id != ''
        pii_type = str(handler.json_args.get('pii_type', '')).strip()
        assert pii_type != ''
        resp = append_pii_whitelist_by_api(api_id, pii_type.encode('utf8'))
        if resp.get('message'):
            return False, Result.ARGUMENT_ERROR
        return True, {}
    except ApiInfoModel.DoesNotExist:
        return False, Result.API_UNKNOWN_API_INVALID
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR