#!/usr/bin/python
# -*- coding: utf-8 -*-#
# -------------------------------------------------------------------------------
# Name:         service_abd_scanner_utils
# Description:  
# Author:       spark
# Date:         2024/4/21
# -------------------------------------------------------------------------------
# Change Activity:
#               2024/4/21: spark created file
# -------------------------------------------------------------------------------
import logging
import json
import time
import io
import uuid
from collections import OrderedDict
from datetime import datetime

from result import Result
import module_ctrls.ubbv2.ubb_util as ubb_util
from abd_utils.utils.func_base import SHA1_STRING_LEN, SCANNER_LEVEL_NUM_TO_LABEL, check_data_dict, ExtraParam
from asp_utils.sailfish_util import query_from_sailfish_sql, _safe_sailfish_request
from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.models.api_info_model import ApiInfoModel
from abd_utils.models.app_site_model import AppSiteModel
from abd_utils.repositories.api_risk_level import ApiRiskLevelRepository
from abd_utils.repositories.assets.app_site import AppSiteRepository
from abd_utils.repositories.defects.scanner import ScannerRepository
from abd_utils.repositories.assets.apis_detail import api_validate_forbidden_or_required_headers
from api_base.api_const_define import CREATED_BY_SCANNER, API_REQUEST_METHODS
from .service_api_utils import add_apis_into_pg, get_necessary_field, get_necessary_field_fallback, \
    validate_business_types, validate_required_header, validate_forbidden_header, get_optional_field
from .service_api_utils import MIN_DATE
from .sailfish_sender import ApiSampleSender, ScannerAssetsSender
from .service_mgr_utils import get_all_sailfish_ip
from asp_utils.CommonLibs import valid_IPv4, valid_IPv6
from abd_utils.utils.func_api_file import ApiListMatcher
from abd_utils.utils.func_base import update_zk_trigger,escape_sql_str
from abd_utils.utils import func_api
from api_base.api_const_define import OWASP_API_CLASS_0, OWASP_API_CLASS_10


ID_STRING_LEN = 27
MAX_DEFECT_NAME_CLASS_SIZE = 255 + 1
MAX_BODY_HEADER_SIZE = 20 * 1024
MAX_DESC_SIZE = 10 * 1024 + 1
DEFECT_REPO = ScannerRepository()

def is_valid_length_str(input_str, length):
    try:
        input_bytes = input_str.encode('utf-8')
    except:
        return False
    return len(input_bytes) < length

DEFECT_CONF = OrderedDict([
    ('risk_name', {
        'value': lambda x: get_necessary_field(x, 'risk_name'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x.strip()) > len(u'[安全扫描]') and is_valid_length_str(x, MAX_DEFECT_NAME_CLASS_SIZE) and x.endswith('[安全扫描]')
    }), 
    ('risk_level', {
        'value': lambda x: get_necessary_field(x, 'risk_level'),
        'validation_func': lambda x: isinstance(x, int) and x in set([1, 2, 3])
    }),
    ('description', {
        'value': lambda x: get_necessary_field(x, 'description'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x.strip()) > 0 and is_valid_length_str(x, MAX_DESC_SIZE)
    }),
    ('solution', {
        'value': lambda x: get_necessary_field(x, 'solution'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x.strip()) > 0 and is_valid_length_str(x, MAX_DESC_SIZE)
    }),
    ('need_mask', {
        'value': lambda x: get_necessary_field(x, 'need_mask'),
        'validation_func': lambda x: isinstance(x, bool)
    }),
    ('risk_class', {
        'value': lambda x: get_necessary_field(x, 'risk_class'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x.strip()) > 0 and is_valid_length_str(x, MAX_DEFECT_NAME_CLASS_SIZE)
    }),
    ('update_time', {
        'value': lambda x: get_necessary_field(x, 'update_time'),
        'validation_func': lambda x: isinstance(x, int)
    }),
    ('owasp_class', {
        'value': lambda x: get_optional_field(x, 'owasp_class', OWASP_API_CLASS_0),
        'validation_func': lambda x: isinstance(x, int) and OWASP_API_CLASS_10 >= x >= OWASP_API_CLASS_0
    }),
])

RESTFUL_SCAN_API_CONFIG = OrderedDict([
    ('app_site_id', {
        'value': lambda x: get_necessary_field(x, 'app_site_id'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) == ID_STRING_LEN
    }),
    ('endpoint', {
        'value': lambda x: get_necessary_field_fallback(x, 'endpoint', 'api_path'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) >=1 and len(x) <= 500 and x.startswith('/')
    }),
    ('method', {
        'value': lambda x: get_necessary_field(x, 'method'),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and x in set(API_REQUEST_METHODS)
    }),
    ('is_included_sub_path', {
        'value': lambda x: get_necessary_field(x, 'is_included_sub_path'),
        'validation_func': lambda x: x in (True, False) and isinstance(x, bool)
    }),
    ('name', {
        'value': lambda x: x.get('api_name', ''),
        'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) >=0 and len(x) <= 50
    }),
    ('business_types', {
        'value': lambda x: x.get('business_types', []),
        'validation_func': lambda x: validate_business_types(x)
    }),
    ('enable_args', {
        'value': lambda x: x.get('enable_args', False),
        'validation_func': lambda x: x in (True, False) and isinstance(x, bool)
    }),
    ('api_max_query_args_cnt', {
        'value': lambda x: x.get('api_max_query_args_cnt', -1),
        'validation_func': lambda x: isinstance(x, int) and x >= -1 and x <= 100
    }),
    ('api_max_body_args_cnt', {
        'value': lambda x: x.get('api_max_body_args_cnt', -1),
        'validation_func': lambda x: isinstance(x, int) and x >= -1 and x <= 100
    }),
    ('api_max_body_size', {
        'value': lambda x: x.get('api_max_body_size', -1),
        'validation_func': lambda x: isinstance(x, int) and x >= -1 and x <= 1000000000
    }),
    ('required_headers', {
        'value': lambda x: x.get('required_headers', []),
        'validation_func': lambda x: validate_required_header(x)
    }),
    ('forbidden_headers', {
        'value': lambda x: x.get('forbidden_headers', []),
        'validation_func': lambda x: validate_forbidden_header(x)
    }),
    ('sample_list', {
        'value': lambda x: x.get('sample_list', []),
        'validation_func': lambda x: is_valid_samples(x)
    })
])

SAMPLE_CONFIG = OrderedDict([
        ('hostname', {
            'value': lambda x: get_necessary_field(x, 'hostname'),
            'validation_func': lambda x: isinstance(x, (str, unicode))
        }),
        ('path', {
            'value': lambda x: get_necessary_field(x, 'path'), 
            'validation_func': lambda x: isinstance(x, (str, unicode)) and x.startswith('/')
        }),
        ('src_ip', {
            'value': lambda x: get_necessary_field(x, 'src_ip'),  
            'validation_func': lambda x: valid_IPv4(x) or valid_IPv6(x)
        }),
        ('src_port', {
            'value': lambda x: get_necessary_field(x, 'src_port'),  
            'validation_func': lambda x: x==0
        }),
        ('status', {
            'value': lambda x: get_necessary_field(x, 'status'),  
            'validation_func': lambda x: isinstance(x, int) and x >= 100 and x < 600
        }),
        ('upstream_addr', {
            'value': lambda x: get_necessary_field(x, 'upstream_addr'),  
            'validation_func': lambda x: isinstance(x, (str, unicode))
        }),
        ('is_https', {
            'value': lambda x: get_necessary_field(x, 'is_https'),  
            'validation_func': lambda x:isinstance(x, bool)
        }),
        ('api_snap_reqheader', {
            'value': lambda x: get_necessary_field(x, 'api_snap_reqheader'), 
            'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x)>0 and len(x) <= MAX_BODY_HEADER_SIZE
        }),
        ('api_snap_respheader', {
            'value': lambda x: get_necessary_field(x, 'api_snap_respheader'), 
            'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x)>0 and len(x) <= MAX_BODY_HEADER_SIZE
        }),
        ('api_snap_reqbody', {
            'value': lambda x: x.get('api_snap_reqbody', ''),  
            'validation_func': lambda x: isinstance(x, (str, unicode)) and  len(x) <= MAX_BODY_HEADER_SIZE
        }),
        ('api_snap_respbody', {
            'value': lambda x: x.get('api_snap_respbody',''),  
            'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) <= MAX_BODY_HEADER_SIZE
        }),
        ('api_risk_name', {
            'value': lambda x: x.get('api_risk_name',[]),  
            'validation_func': lambda x: isinstance(x, list) and (x == [] or any(name.endswith('[安全扫描]') for name in x))
        }),
        ('api_risk_value', {
            'value': lambda x: x.get('api_risk_value',[]),  
            'validation_func': lambda x, **kw: isinstance(x, list) and check_risk_info(x, **kw)
        }),
        ('api_risk_value_position', {
            'value': lambda x: x.get('api_risk_value_position',[]),  
            'validation_func': lambda x: isinstance(x, list) and (x == [] or any(pos in set(['REQ_AGRS', 'REQ_HEADER', 'REQ_BODY', 'RESP_HEADER', 'RESP_BODY', '']) for pos in x))
        }),
    ])

def check_risk_info(risk_value, risk_name, risk_value_position):
    if not isinstance(risk_name, list) or not isinstance(risk_value, list) or not isinstance(risk_value_position, list):
        return False

    if len(risk_name) != len(risk_value) or len(risk_name) != len(risk_value_position):
        return False
    return True

    
def get_api_sample(json_args):
    api_ids = json_args.get('api_ids', None)

    if not api_ids or not isinstance(api_ids, list):
        return False, Result.ARGUMENT_ERROR

    if len(api_ids) > 10:
        return False, Result.MAX_INVALID
    
    unique_api_ids = []
    for api_id in api_ids:
        # inject escape
        id = escape_sql_str(str(api_id)) 
        if id not in unique_api_ids:
            unique_api_ids.append(id)
    
    api_id_arr = "','".join(unique_api_ids)
    api_id_arr = "'" + api_id_arr + "'"
    
    id_to_index = {item_id: index for index, item_id in enumerate(api_ids)}

    sql_template = '''
            SELECT
              api_id_sample as api_id,
              api_snap_reqheader as req_header,
              api_snap_reqbody as req_body,
              api_snap_respheader as resp_header,
              api_snap_respbody as resp_body
            from
              api_sample
            PREWHERE 
              api_id_sample in [{api_ids}]
            ORDER BY  timestamp DESC
            LIMIT
              1 BY api_id
        '''

    sql = sql_template.format(api_ids = api_id_arr)
    ret, err_msg, records = query_from_sailfish_sql(sql)
    sample_list = records or []
    return sorted(sample_list, key=lambda item: id_to_index.get(item['api_id'])), ''


def add_api_sample(sailfish_ip, samples):
    ApiSampleSender(sailfish_ip).insert_table(samples)

def add_scanner_asset(sailfish_ip, assets):
    ScannerAssetsSender(sailfish_ip).insert_table(assets)

def handle_risk_level(risk_names):
    risk_levels = []
    scanner_defects = DEFECT_REPO.get_scanner_defect_info()
    for risk_name in risk_names:
        defect_info = scanner_defects.get(risk_name, {})
        level = defect_info.get('risk_level', None)
        if not level or level not in [1,2,3]:
            level = 3
        risk_levels.append(SCANNER_LEVEL_NUM_TO_LABEL[level])
    return risk_levels


def is_valid_samples(samples):
    if not samples or not isinstance(samples, list):
        return False, Result.ARGUMENT_ERROR
        
    for sample in samples:
        extra_dict = {"api_risk_value": ExtraParam(validation_func={"risk_name": sample.get('api_risk_name', []),
                                                                        "risk_value_position": sample.get('api_risk_value_position', [])})}
        sample = check_data_dict(sample, SAMPLE_CONFIG, **extra_dict)

    return True
        

def add_api_by_scanner(handler, max=1000):
    sailfish_ips = get_all_sailfish_ip()
    if not sailfish_ips:
        return False, Result.SAILFISH_SERVER_NOT_EXISTS
    sailfish_ip = sailfish_ips[0]
    
    api_info_list = handler.json_args.get('api_info_list')
    logging.info('api_info_list: {}'.format(api_info_list))

    if not api_info_list or not isinstance(api_info_list, list):
        return False, Result.ARGUMENT_ERROR

    if len(api_info_list) > max:
        return False, Result.MAX_INVALID
    try:
        for api_info in api_info_list:
            check_data_dict(api_info, RESTFUL_SCAN_API_CONFIG)
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR
    
    try:
        api_list_matcher = ApiListMatcher()
        api_list_matcher.load()
    except:
        api_list_matcher = None

    
    pending_apis = []


    for api_info in api_info_list:            
        api_match_info = None

        if api_list_matcher:
            api_pattern = "{}/{}{}".format(api_info.get('app_site_id'), api_info.get('method'), api_info.get('endpoint'))
            api_match_info = api_list_matcher.match(api_pattern)

        
        if api_match_info is None:
            pending_apis.append(api_info)
        else:
            api_info['id'] = api_match_info.id

    ret, err = add_apis_into_pg(pending_apis, CREATED_BY_SCANNER, RESTFUL_SCAN_API_CONFIG)
    if not ret:
        return False, err

    pending_samples = []
    pending_scanner_assets = []

    # 添加样例，扫描
    for api_info in api_info_list:
        samples = api_info.get('sample_list', [])
        scanner_assets = get_scanner_asset_item(samples, api_info)

        pending_samples.extend(samples)
        pending_scanner_assets.extend(scanner_assets)

    add_api_sample(sailfish_ip, pending_samples)
    add_scanner_asset(sailfish_ip, pending_scanner_assets)

    return True, ''

def get_scanner_asset_item(samples, api_info):
    scanner_assets = []
    now_time = int(time.time())
    for sample in samples:
        scanner_asset = {}
        scanner_asset['api_asset_id'] = api_info.get('id')
        scanner_asset['api_risk_name'] = sample.get('api_risk_name', [])
        scanner_asset['api_risk_level'] = handle_risk_level(scanner_asset['api_risk_name'])
        scanner_asset['app_site_id'] = api_info.get('app_site_id')
        scanner_asset['endpoint'] = api_info.get('endpoint')
        scanner_asset['timestamp'] = now_time
        scanner_asset['scan_item_uuid'] = uuid.uuid4().int
        scanner_assets.append(scanner_asset)

        sample['api_id_sample'] = api_info.get('id')
        sample['timestamp'] = now_time
    return scanner_assets

def get_api_list_by_app(handler):
    CONFIG = OrderedDict([
        ('app_id', {
            'value': lambda x: get_necessary_field(x, 'app_id'),  
            'validation_func': lambda x: isinstance(x, (str, unicode)) and len(x) == ID_STRING_LEN
        }),
        ('start_time', {
            'value': lambda x: x.get('start_time', MIN_DATE),  
            'validation_func': lambda x: isinstance(x, (float, int, long)) and x >= MIN_DATE
        }),
        ('page', {
            'value': lambda x: get_necessary_field(x, 'page'), 
            'validation_func': lambda x: isinstance(x, int) and x >= 1
        }),
        ('page_size', {
            'value': lambda x: get_necessary_field(x, 'page_size'),  
            'validation_func': lambda x: isinstance(x, int) and x >= 1 and x <= 200
        })
    ])
    
    try:
        args = check_data_dict(handler.json_args, CONFIG)
    except Exception as e:
        logging.exception(e)
        return False, Result.ARGUMENT_ERROR

    app_id = args['app_id']
    start_time = args['start_time']
    page = args['page']
    page_size = args['page_size']
    timestamp_datetime = datetime.fromtimestamp(start_time)
    return ApiRepository().get_api_list_by_app_site(app_id, timestamp_datetime, page, page_size), ''


def get_access_sites_for_scanner(app_ids):
    sql_template = '''
        SELECT
            app_id,
            groupArray(DISTINCT if(is_https = 0, 'http', 'https') || '://' || if(http_host!='', http_host, hostname)) AS access_sites
        FROM
            access_log
        PREWHERE
            timestamp >= now() - toIntervalDay(7)
            AND has(attack_type, 'OK')
            AND app_id in ({})
        GROUP BY app_id
        LIMIT 10 BY app_id
    '''
    if not app_ids:
        return {}
    
    app_str_ids = []
    for app_id in app_ids:
        app_str_ids.append("'{}'".format(escape_sql_str(app_id)))

    sql = sql_template.format(", ".join(app_str_ids))
    ret, err_msg, records = query_from_sailfish_sql(sql)
    if records:
        return {r['app_id']: r['access_sites'] for r in records}
    return {}


def get_app_site_for_scan(site_info=None, max=10):
    site_ids = site_info.get('app_ids')
    if not site_ids or not isinstance(site_ids, list):
        return False, Result.ARGUMENT_ERROR

    if len(site_ids) > max:
        return False, Result.MAX_INVALID

    app_sites, _= AppSiteRepository().get_app_site_by_ids(site_ids)
    app_sites = app_sites[:]
    access_sites = get_access_sites_for_scanner(site_ids)
    for app in app_sites:
        app['access_sites'] = access_sites.get(app['id'], [])
    return app_sites, ''

def update_scan_risk_conf(handler):
    risk_level_repo = ApiRiskLevelRepository()
    defect_confs = handler.json_args.get('api_risk_config')
    if not defect_confs or not isinstance(defect_confs, list):
        return False, Result.ARGUMENT_ERROR
    
    for conf in defect_confs:
        try:
            conf = check_data_dict(conf, DEFECT_CONF)
        except Exception as e:
            logging.exception(e)
            return False, Result.ARGUMENT_ERROR
        
    # 添加配置到pg
    DEFECT_REPO.update_scanner_risk_conf(defect_confs)
    LEVEL_TO_EN = {
        1:"high",
        2:"middle",
        3:"low"
    }
    for conf in defect_confs:
        risk_level_repo.update_or_create_item(risks=[{'type': 'defect', 'name': conf['risk_name']}], level=LEVEL_TO_EN[conf.get('risk_level')])

    return True, Result.SUCCESS

def get_scan_risk_conf_info(data):
    return DEFECT_REPO.get_latest_update_info(), ''