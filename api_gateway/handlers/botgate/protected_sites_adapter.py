# encoding=utf-8
import base64
import hashlib
import os
import logging
import json
import re
import copy
import time

import IPy
from jsonschema import validate, RefResolver
from jsonschema._format import draft7_format_checker


from .botgate_result import BotgateResult as Result
from .service_mgr_utils import get_config, get_config_all, is_debug, \
    is_license_advanced_protection, is_license_advanced_waf_enabled, \
    is_supported_upstream_addr, is_license_primary_protection, is_license_essential_protection, has_gm_permission, has_mpp_permission
from asp_utils.utils import is_valid_regex, generate_server_id, get_manifest_info, is_port_already_in_use
from asp_utils.waf_util import get_waf_default_site_strategies_id_map_name
from asp_utils.CommonLibs import valid_IPv4, valid_IPv6, valid_IP_or_CIDR, valid_Domain, valid_IP
from .service_waf_utils import WafStrategyCtrl, WafConf
from asp_conf_ctrl import ConfDb
from .service_waf_utils import  SiteStrategy
logger = logging.getLogger(__name__)


class ConverterBase:

    is_mirror_mode = ConfDb().get_deploy_mode() == ConfDb.DEPLOY_MODE_MIRROR
    # API KEY
    API_KEY_SITE_NAME_TYPE = 'type'
    API_KEY_SITE_NAME_TYPE_DOMAIN = 'domain'
    API_KEY_SITE_NAME_TYPE_IPV4 = 'ipv4'
    API_KEY_SITE_NAME_TYPE_IPV6 = 'ipv6'
    API_KEY_SITE_NAME_TYPE_REGEX = 'regex'

    API_KEY_BUSINESS_PATH = 'business_path'
    API_KEY_ENABLE_BUSINESS_PATH = 'enable_business_path'

    API_KEY_SITE_LIST = 'site_list'
    API_KEY_SITE_CONFIG = 'config'

    API_KEY_SITE_NAME = 'site'
    API_KEY_PROTOCOL = 'protocol'
    API_KEY_PORT = 'port'
    API_KEY_CUSTOMIZED_SITE_NAME = 'name'
    API_KEY_ENABLE_SITE_CONF = 'enable_site_conf'
    API_KEY_TERMINAL_SETTING = 'terminal_setting'
    API_KEY_PROTECTION_MODE = 'protection_mode'
    API_KEY_CERTIFICATE = 'certificate'
    API_KEY_CERTIFICATE_KEY = 'certificate_key'
    API_KEY_ENABLE_INTERNATIONAL_CERT = 'enable_international_cert'
    API_KEY_GM_CERTIFICATE = 'gm_certificate'
    API_KEY_GM_ENC_CERTIFICATE = "enc_certificate"
    API_KEY_GM_ENC_CERTIFICATE_KEY = "enc_certificate_key"
    API_KEY_GM_SIGN_CERTIFICATE = 'certificate'
    API_KEY_GM_SIGN_CERTIFICATE_KEY = 'certificate_key'

    API_KEY_PROTECTION_MODE_INTERCEPT = 'intercept'
    API_KEY_PROTECTION_MODE_MONITOR = 'monitor'
    API_KEY_PROTECTION_MODE_PASSTHROUGH = 'passthrough'

    API_KEY_INVALID_ACTION = 'invalid_action'
    API_KEY_INVALID_ACTION_REDIRECT_PATH = 'invalid_action_redirect_path'
    API_KEY_UPSTREAM = 'upstream'
    API_KEY_UPSTREAM_LIST = 'upstream_list'
    API_KEY_UPSTREAM_LOAD_BALANCE = 'load_balance'

    API_KEY_UPSTREAM_HEALTH_CHECK = 'health_check'
    API_KEY_HEALTH_CHECK_TYPE = 'type'
    API_KEY_HEALTH_CHECK_INTERVAL = 'interval'
    API_KEY_HEALTH_CHECK_RETRY_TIMES = 'retry_times'
    API_KEY_HEALTH_CHECK_TIMEOUTS = 'timeout'
    API_KEY_HEALTH_CHECK_HTTP_MODE = 'http_mode'
    API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER = 'http_custom_request_header'
    API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY = 'http_custom_request_body'
    API_KEY_HEALTH_CHECK_PATH = 'path'
    API_KEY_HEALTH_CHECK_UA = 'user_agent'

    API_KEY_ALLOWED_HTTP_METHODS = 'allowed_http_methods'
    API_KEY_SELF_HEALTH_CHECK = 'self_health_check'
    API_KEY_INTERNAL_RES_PATH = 'internal_res_path'
    API_KEY_SRC_IP_STRATEGY = 'src_ip_strategy'
    API_KEY_SRC_IP_STRATEGY_LIST = 'src_ip_strategy_list'


    API_KEY_IP_STRATEGY = 'ip_strategy'
    API_KEY_PROTECT_ALL_IP = 'all_ip'
    API_KEY_PROTECT_IP_WHITE_LIST = 'ip_white_list'
    API_KEY_PROTECT_IP_PROTECTION_LIST = 'ip_protection_list'

    API_KEY_WEB_PRIMARY_STRATEGY = 'web_primary_strategy'
    API_KEY_WEB_ESSENTIAL_STRATEGY = 'web_essential_strategy'
    API_KEY_AUTOMATED_TOOL_INTERCEPT = 'automated_tool_interception'
    API_KEY_CRACK_BEHAVIOR_INTERCEPT = 'crack_behavior_interception'
    API_KEY_PREVENT_SCANNER = 'prevent_scanner'
    API_KEY_REQUEST_WHITE_LIST = 'request_white_list'
    API_KEY_RESPONSE_WHITE_LIST = 'response_white_list'
    API_KEY_AJAX_TOKEN_BYPASS_LIST = 'ajax_token_bypass_list'
    API_KEY_AJAX_TOKEN_PATH_LIST = 'ajax_token_path_list'
    API_KEY_AJAX_TOKEN_PATH_TYPE = 'ajax_token_path_type'

    API_KEY_WEB_POWER_STRATEGY = 'web_power_strategy'
    API_KEY_FORM_ENCRYPTION = 'form_encryption'
    API_KEY_COOKIE_ENCRYPTION = 'cookie_encryption'
    API_KEY_VERIFICATION_LIST = 'verification_list'
    API_KEY_ENCAPSULATION_LIST = 'encapsulation_list'
    API_KEY_ENABLE_AJAX_REQ_BODY_ENC = 'enable_ajax_req_body_enc'
    API_KEY_AJAX_REQ_BODY_ENC_LIST = 'ajax_req_body_enc_list'
    API_KEY_AJAX_REFERER_LIST = 'ajax_referer_list'
    API_KEY_ENABLE_AJAX_RESP_BODY_ENC = 'enable_ajax_resp_body_enc'
    API_KEY_AJAX_RESP_BODY_ENC_LIST = 'ajax_resp_body_enc_list'

    API_KEY_WAF_STRATEGY = 'waf_strategy'
    API_KEY_WAF_MONITOR_ONLY = 'monitor_only'
    API_KEY_WAF_WHITE_LIST = 'white_list'
    API_KEY_WAF_STRATEGY_TYPE = 'type'
    API_KEY_WAF_STRATEGY_TYPE_BASIC = 'basic'
    API_KEY_WAF_STRATEGY_TYPE_STANDARD = 'standard'
    API_KEY_WAF_STRATEGY_TYPE_STRICT = 'strict'
    API_KEY_WAF_STRATEGY_TYPE_CUSTOMIZED = 'customized'

    API_KEY_AI_WAF_STRATEGY = 'ai_waf_strategy'
    API_KEY_AI_WAF_MONITOR_ONLY = 'monitor_only'
    API_KEY_AI_WAF_WHITE_LIST = 'white_list'

    API_KEY_STATIC_RESOURCE_LIST = 'static_resource_list'

    API_KEY_WECHAT_STRATEGY = 'wechat_strategy'
    API_KEY_ALIPAY_STRATEGY = 'alipay_strategy'
    API_KEY_MPAAS_MPP_STRATEGY = 'mpaas_mpp_strategy'

    # ASP KEY
    ASP_KEY_SERVER_NAME_TYPE = 'ServerNameType'
    ASP_KEY_SERVER_NAME_TYPE_DOMAIN = 'Domain'
    ASP_KEY_SERVER_NAME_TYPE_IPV4 = 'IPv4'
    ASP_KEY_SERVER_NAME_TYPE_IPV6 = 'IPv6'
    ASP_KEY_SERVER_NAME_TYPE_REGEX = 'Regex'

    ASP_KEY_SERVER_NAME = 'ServerName'
    ASP_KEY_SERVER_NAME_OLD = 'name'
    ASP_KEY_IS_HTTPS = 'IsHttps'
    ASP_KEY_LISTEN_PORT = 'ListenPort'
    ASP_KEY_ENABLE_BUSINESS_PATH = 'enable_business_path'
    ASP_KEY_BUSINESS_PATH = 'business_path'
    ASP_KEY_USE_INTERNATIONAL_CERT = 'useInternationalCert'
    ASP_KEY_USE_BUILTIN_CERT = 'useBuiltInCert'
    ASP_KEY_CERTIFICATION = 'Certification'
    ASP_KEY_CERTIFICATION_FILENAME = 'cert_file_name'
    ASP_KEY_CERTIFICATION_KEY = 'CertificationKey'
    ASP_KEY_CERTIFICATION_KEY_FILENAME = 'key_file_name'
    ASP_KEY_USE_GM_CERT = 'useChinaSecurityCert'
    ASP_KEY_USE_GM_BUILTIN_CERT = 'useBuiltInChinaSecurityCert'
    ASP_KEY_GM_SIGN_CERTIFICATION_FILENAME = 'gm_sign_cert_file_name'
    ASP_KEY_GM_ENC_CERTIFICATION_FILENAME = 'gm_enc_cert_file_name'
    ASP_KEY_GM_ENC_CERTIFICATION_KEY = 'gm_enc_certificationKey'
    ASP_KEY_GM_ENC_CERTIFICATION = 'gm_enc_certification'
    ASP_KEY_GM_SIGN_CERTIFICATION_KEY_FILENAME = 'gm_sign_key_file_name'
    ASP_KEY_GM_SIGN_CERTIFICATION = 'gm_sign_certification'
    ASP_KEY_GM_ENC_CERTIFICATION_KEY_FILENAME = 'gm_enc_key_file_name'
    ASP_KEY_GM_SIGN_CERTIFICATION_KEY = 'gm_sign_certificationKey'


    ASP_KEY_TERMINAL_ENABLED = 'TerminalEnabled'
    ASP_KEY_IS_TERMINAL_HTTPS = 'IsTerminalHttps'
    ASP_KEY_TERMINAL_PORT = 'TerminalPort'
    ASP_KEY_SITE_CUSTOMIZED_NAME = 'site_customize_name'
    ASP_KEY_ENABLE_SITE_CONF = 'enable_site_conf'
    ASP_KEY_TXSAFE_ENABLED = 'enabled'
    ASP_KEY_IS_LEARNING_MODE = 'learning_mode'
    ASP_KEY_INVALID_ACTION = 'action'
    ASP_KEY_INVALID_ACTION_ENTRY_PATH = 'EntryPath'
    ASP_KEY_IS_UPSTREAM_HTTPS = 'IsUpstreamHttps'
    ASP_KEY_LOAD_BALANCE_STRATEGY = 'load_balancing_strategy'
    ASP_KEY_UPSTREAM_LIST = 'UpstreamList'
    ASP_KEY_PREVENT_SCANNER = 'prevent_scanner'
    ASP_KEY_SECURITY_LEVEL_MIN = 'security_level_min'
    ASP_KEY_SECURITY_LEVEL_MAX = 'security_level_max'

    ASP_KEY_HEALTH_CHECK = 'health_check'
    ASP_KEY_IS_HEALTH_CHECK_ENABLE = 'is_health_check_enable'
    ASP_KEY_HEALTH_CHECK_TYPE = 'health_check_type'
    ASP_KEY_HEALTH_CHECK_INTERVAL = 'health_check_interval'
    ASP_KEY_HEALTH_CHECK_RETRY_TIMES = 'health_check_reset_times'
    ASP_KEY_HEALTH_CHECK_TIMEOUTS = 'health_check_timeouts'
    ASP_KEY_HEALTH_CHECK_HTTP_MODE = 'health_check_http_mode'
    ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER = 'health_check_http_custom_request_header'
    ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY = 'health_check_http_custom_request_body'
    ASP_KEY_HEALTH_CHECK_PATH = 'health_check_path'
    ASP_KEY_HEALTH_CHECK_UA = 'health_user_agent'

    ASP_KEY_SECURITY_LEVEL_PRIMARY_TOKEN = 'primary_token'
    ASP_KEY_SECURITY_LEVEL_COOKIE_TOKEN = 'cookie_token'
    ASP_KEY_SECURITY_LEVEL_BOT = 'bot'
    ASP_KEY_SECURITY_LEVEL_CRACK = 'crack'
    ASP_KEY_SECURITY_LEVEL_URL = 'url'
    ASP_KEY_SECURITY_LEVEL_FORM = 'form'
    ASP_KEY_SECURITY_LEVEL_COOKIE_ENCRYPTION = 'cookie'
    ASP_KEY_SECURITY_LEVEL_WAF = 'waf'

    ASP_KEY_IP_LIST_SIWTCH = 'IpListSwitch'
    ASP_KEY_IP_LIST_SIWTCH_ALL_IP = 'all_ip_white_list'
    ASP_KEY_IP_LIST_SIWTCH_WHITE_LIST = 'ip_white_list'
    ASP_KEY_IP_LIST_SIWTCH_PROTECTION_LIST = 'ip_black_list'
    ASP_KEY_IP_WHITE_LIST = 'IpWhiteList'
    ASP_KEY_IP_PROTECTION_LIST = 'IpBlackList'

    ASP_KEY_PROTECTED_LIST = 'protected_list'
    ASP_KEY_PROTECTED_LIST_BOT = 'automated_tool_intercept'
    ASP_KEY_PROTECTED_LIST_CRACK = 'crack_behavior_interception'
    ASP_KEY_PROTECTED_LIST_WEB_PRIMARY = 'web_primary_protection'
    ASP_KEY_PROTECTED_LIST_WEB_ESSENTIAL = 'web_standard_protection'
    ASP_KEY_PROTECTED_LIST_WEB_POWER = 'web_advanced_protection'
    ASP_KEY_PROTECTED_LIST_WAF = 'injection_attack_interception'
    ASP_KEY_PROTECTED_LIST_LLM = 'llm_protection'
    ASP_KEY_PROTECTED_LIST_AIWAF = 'aiwaf_attack_interception'
    ASP_KEY_PROTECTED_LIST_AJAX_REQ_BODY_ENC = 'ajax_request_body_encryption'
    ASP_KEY_PROTECTED_LIST_AJAX_RESP_BODY_ENC = 'ajax_response_encryption'

    ASP_KEY_FULL_WHITE_LIST = 'FullWhiteList'
    ASP_KEY_FULL_WHITE_LIST_OUT = 'FullWhiteListOut'
    ASP_KEY_AJAX_TOKEN_BYPASS_LIST = 'ajax_token_bypass_list'
    ASP_KEY_AJAX_TOKEN_PATH_LIST = 'ajax_token_path_list'
    ASP_KEY_AJAX_TOKEN_PATH_TYPE = 'ajax_token_path_type'

    ASP_KEY_VERIFICATION_LIST = 'VerificationList'
    ASP_KEY_ENCAPSULATION_LIST_OUT = 'EncapsulationListOut'
    ASP_KEY_AJAX_REQ_BODY_ENC_LIST = 'AjaxRequestBodyEncryptionList'
    ASP_KEY_AJAX_REFERER_LIST = 'ajax_referer_list'
    ASP_KEY_AJAX_RESP_BODY_ENC_LIST = 'AjaxResponseEncryptionList'

    ASP_KEY_WAF_LEARNING_MODE = 'waf_learning_mode'
    ASP_KEY_WAF_WHITE_LIST = 'Inject_Whitelist'

    ASP_KEY_WAF_MODULES = 'WafEnabledModules'
    ASP_KEY_WAF_RULE_SET = 'rule_set'
    ASP_KEY_WAF_LOOSE_SET = 'looseSet'
    ASP_KEY_WAF_DEFAULT_SET = 'defaultSet'
    ASP_KEY_WAF_MAXIMUM_SET = 'maximumSet'
    ASP_KEY_WAF_CUSTOM_SET = 'customSet'
    ASP_KEY_WAF_STRATEGY = 'waf_strategy'

    WAF_RULE_SET_INDEX_BASIC = 0
    WAF_RULE_SET_INDEX_STANDARD = 1
    WAF_RULE_SET_INDEX_STRICT = 2
    ASP_KEY_WAF_RULE_SET_TABLE = {
        "command_excute_interception":          [True,  True,  True],
        "file_upload_interception":             [False, True,  True],
        "java_deserialization_interception":    [False, True,  True],
        "local_file_include_interception":      [False, True,  True],
        "php_injection_interception":           [False, True,  True],
        "protocol_attack_interception":         [False, True,  True],
        "remote_file_include_interception":     [False, True,  True],
        "scanner_detect_interception":          [True,  True,  True],
        "server_vulnerability_interception":    [False, True,  True],
        "session_fixation_interception":        [False, False, True],
        "sql_injection_interception":           [True,  True,  True],
        "web_shell_interception":               [False, False, True],
        "xss_injection_interception":           [True,  True,  True],
        "sensitive_info_filter_interception":   [False, False, False],
        "server_info_leakage_interception":     [False, False, False],
        "malicious_link_interception":          [False, False, False],
        "xml_attack_interception":              [False, True,  True],
        "sensitive_file_detection_interception":[False, True,  True],
        "java_code_interception":               [False, True,  True],
        "illegal_file_download_interception":   [False, False, False]
    }

    ASP_KEY_AI_WAF_MODULES = 'AIWaf_Modules'
    ASP_KEY_AI_WAF_ENABLE = 'aiwaf_enable'
    ASP_KEY_AI_WAF_LEARNING_MODE = 'aiwaf_learning_mode'
    ASP_KEY_AI_WAF_WHITE_LIST = 'aiwaf_whitelist'

    ASP_KEY_STATIC_RESOURCE_LIST = 'static_resource_list'

    ASP_KEY_LIMIT_EXCEPT = "limit_except"
    ASP_KEY_ENABLE_SELF_HEALTH_CHECK = "enable_self_health_check"
    ASP_KEY_SELF_HEALTH_CHECK_PATH = "self_health_check_path"
    ASP_KEY_INTERNAL_RES_PATH = 'internal_res_path'

    ASP_KEY_SRC_IP_USE_GLOBAL_SETTING = 'src_ip_use_global_setting'
    ASP_KEY_SRC_IP_STRATEGY_LIST = 'src_ip_strategy_list'

    ASP_KEY_WECHAT_MINI_PROTECTION = 'wechat_mini_protection'
    ASP_KEY_WECHAT_APP_REQ_BODY_ENCRYPT = 'wechat_app_req_body_encrypt'
    ASP_KEY_WECHAT_APP_RESP_BODY_ENCRYPT = 'wechat_app_resp_body_encrypt'
    ASP_KEY_WECHAT_ACCESS_WHITE_LIST = 'wechat_access_white_list'

    ASP_KEY_ALIPAY_MINI_PROTECTION = 'alipay_mini_program_protection'
    ASP_KEY_ALIPAY_MPP_REQ_BODY_ENCRYPT = 'alipay_mpp_req_body_encrypt'
    ASP_KEY_ALIPAY_MPP_RESP_BODY_ENCRYPT = 'alipay_mpp_resp_body_encrypt'
    ASP_KEY_ALIPAY_MPP_ACCESS_WHITE_LIST = 'alipay_mpp_access_white_list'

    ASP_KEY_MPAAS_MPP_PROTECTION = 'mpaas_mpp'
    ASP_KEY_MPAAS_MPP_RESP_BODY_ENCRYPT = 'mpaas_mpp_req_body_encrypt'
    ASP_KEY_MPAAS_MPP_REQ_BODY_ENCRYPT = 'mpaas_mpp_resp_body_encrypt'
    ASP_KEY_MPAAS_MPP_ACCESS_WHITE_LIST = 'mpaas_mpp_access_white_list'

    SERVER_NAME_TYPE_MAP = {
        API_KEY_SITE_NAME_TYPE_DOMAIN: ASP_KEY_SERVER_NAME_TYPE_DOMAIN,
        API_KEY_SITE_NAME_TYPE_IPV4: ASP_KEY_SERVER_NAME_TYPE_IPV4,
        API_KEY_SITE_NAME_TYPE_IPV6: ASP_KEY_SERVER_NAME_TYPE_IPV6,
        API_KEY_SITE_NAME_TYPE_REGEX: ASP_KEY_SERVER_NAME_TYPE_REGEX,
    }

    def __init__(self):
        pass

    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        raise NotImplementedError('implement me')

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        raise NotImplementedError('implement me')

    @classmethod
    def _to_restful_api_conf_get_all_sites(cls, asp_conf):
        return cls._to_restful_api_conf(asp_conf)

    @classmethod
    def _get_server_name(cls, asp_conf):
        server_name = asp_conf.get(cls.ASP_KEY_SERVER_NAME, "")
        if not server_name:
            server_name = asp_conf.get(cls.ASP_KEY_SERVER_NAME_OLD, "")
        return server_name

    @staticmethod
    def _need_https(func):
        def wrapper(cls, api_value, current_site_asp_conf):
            if not current_site_asp_conf.get(cls.ASP_KEY_IS_HTTPS) and not current_site_asp_conf.get(cls.ASP_KEY_IS_TERMINAL_HTTPS):
                classname = cls.__name__.strip('Converter')
                raise Exception("{} is applicable only to HTTPS websites.".format(classname))

            return func(cls, api_value, current_site_asp_conf)

        return wrapper


class ProtocolConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        # only create site will invoke this function
        return {cls.ASP_KEY_IS_HTTPS: api_value == 'https'}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_PROTOCOL: 'https' if asp_conf.get(cls.ASP_KEY_IS_HTTPS, False) else 'http'}


class SiteNameTypeConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        # domain => Domain
        return {cls.ASP_KEY_SERVER_NAME_TYPE: cls.SERVER_NAME_TYPE_MAP[api_value]}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        # Domain => domain
        server_name_type = asp_conf.get(cls.ASP_KEY_SERVER_NAME_TYPE, "").lower()

        if not server_name_type:
            server_name = cls._get_server_name(asp_conf)
            if valid_IPv6(server_name):
                server_name_type = 'ipv6'
            elif valid_IPv4(server_name):
                server_name_type = 'ipv4'
            else:
                server_name_type = 'domain'

        return {cls.API_KEY_SITE_NAME_TYPE: server_name_type}


class SiteNameConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        return {cls.ASP_KEY_SERVER_NAME: api_value}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_SITE_NAME: cls._get_server_name(asp_conf)}


class PortConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        return {cls.ASP_KEY_LISTEN_PORT: str(api_value)}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_PORT: int(asp_conf.get(cls.ASP_KEY_LISTEN_PORT, 80))}


class TerminalSettingConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        if 'enable' in api_value:
            asp_conf.update({cls.ASP_KEY_TERMINAL_ENABLED: api_value['enable']})

        if 'protocol' in api_value:
            asp_conf.update({cls.ASP_KEY_IS_TERMINAL_HTTPS: api_value['protocol'] == 'https'})

        if 'port' in api_value:
            asp_conf.update({cls.ASP_KEY_TERMINAL_PORT: str(api_value['port'])})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {
            cls.API_KEY_TERMINAL_SETTING: {
                'enable': asp_conf.get(cls.ASP_KEY_TERMINAL_ENABLED, False),
                'protocol': 'https' if asp_conf.get(cls.ASP_KEY_IS_TERMINAL_HTTPS, False) else 'http',
                'port': int(asp_conf.get(cls.ASP_KEY_TERMINAL_PORT, 80)),
            }}


class CustomizedSiteNameConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        return {cls.ASP_KEY_SITE_CUSTOMIZED_NAME: api_value}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_CUSTOMIZED_SITE_NAME: asp_conf.get(cls.ASP_KEY_SITE_CUSTOMIZED_NAME, "")}


class ProtectionModeConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        """
        api conf        <===>   asp conf
        'intercept'             'enabled': true, 'learning_mode', false,
        'monitor'               'enabled': true, 'learning_mode', true,
        'passthrough'           'enabled': false, 'learning_mode', false,
        """
        return {
            cls.ASP_KEY_TXSAFE_ENABLED:
                api_value == cls.API_KEY_PROTECTION_MODE_INTERCEPT or
                api_value == cls.API_KEY_PROTECTION_MODE_MONITOR,

            cls.ASP_KEY_IS_LEARNING_MODE: api_value == cls.API_KEY_PROTECTION_MODE_MONITOR,
        }

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        if asp_conf.get(cls.ASP_KEY_TXSAFE_ENABLED, False):
            if asp_conf.get(cls.ASP_KEY_IS_LEARNING_MODE, False):
                protection_mode = cls.API_KEY_PROTECTION_MODE_MONITOR
            else:
                protection_mode = cls.API_KEY_PROTECTION_MODE_INTERCEPT
        else:
            protection_mode = cls.API_KEY_PROTECTION_MODE_PASSTHROUGH
        return {cls.API_KEY_PROTECTION_MODE: protection_mode}


class InvalidActionConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        return {cls.ASP_KEY_INVALID_ACTION: api_value}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_INVALID_ACTION: asp_conf.get(cls.ASP_KEY_INVALID_ACTION, 'reject')}


class InvalidActionRedirectPathConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        return {cls.ASP_KEY_INVALID_ACTION_ENTRY_PATH: api_value}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_INVALID_ACTION_REDIRECT_PATH: asp_conf.get(cls.ASP_KEY_INVALID_ACTION_ENTRY_PATH, '/')}


class UpstreamConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}

        # protocol
        if 'protocol' in api_value:
            asp_conf.update({cls.ASP_KEY_IS_UPSTREAM_HTTPS: api_value['protocol'] == 'https'})

        # load balance
        if cls.API_KEY_UPSTREAM_LOAD_BALANCE in api_value:
            asp_conf.update({cls.ASP_KEY_LOAD_BALANCE_STRATEGY: api_value[cls.API_KEY_UPSTREAM_LOAD_BALANCE]})

        # upstream list
        if cls.API_KEY_UPSTREAM_LIST in api_value:
            upstream_list = []
            for item in api_value[cls.API_KEY_UPSTREAM_LIST]:
                # asp conf format [ip, port, enable, weight]
                upstream_list.append([
                    item['ip'],
                    '80' if cls.is_mirror_mode else str(item['port']),
                    True if cls.is_mirror_mode else item.get('enable', True),
                    1 if cls.is_mirror_mode else item.get('weight', 1)
                ])
            asp_conf.update({cls.ASP_KEY_UPSTREAM_LIST: upstream_list})

        # health check
        if cls.API_KEY_UPSTREAM_HEALTH_CHECK in api_value:
            asp_health_check_dict = current_site_asp_conf.get(cls.ASP_KEY_HEALTH_CHECK, {})

            # type
            api_health_check_dict = api_value[cls.API_KEY_UPSTREAM_HEALTH_CHECK]
            if cls.API_KEY_HEALTH_CHECK_TYPE in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_IS_HEALTH_CHECK_ENABLE:
                        api_health_check_dict[cls.API_KEY_HEALTH_CHECK_TYPE] != 'disable'
                })

                if api_health_check_dict[cls.API_KEY_HEALTH_CHECK_TYPE] in ['tcp', 'http']:
                    asp_health_check_dict.update({
                        cls.ASP_KEY_HEALTH_CHECK_TYPE: api_health_check_dict[cls.API_KEY_HEALTH_CHECK_TYPE]
                    })

            # interval
            if cls.API_KEY_HEALTH_CHECK_INTERVAL in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_INTERVAL: str(api_health_check_dict[cls.API_KEY_HEALTH_CHECK_INTERVAL])
                })

            # retry
            if cls.API_KEY_HEALTH_CHECK_RETRY_TIMES in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_RETRY_TIMES: str(api_health_check_dict[cls.API_KEY_HEALTH_CHECK_RETRY_TIMES])
                })

            # timeout
            if cls.API_KEY_HEALTH_CHECK_TIMEOUTS in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_TIMEOUTS: str(api_health_check_dict[cls.API_KEY_HEALTH_CHECK_TIMEOUTS])
                })

            # path
            if cls.API_KEY_HEALTH_CHECK_PATH in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_PATH: api_health_check_dict[cls.API_KEY_HEALTH_CHECK_PATH]
                })

            # user agent
            if cls.API_KEY_HEALTH_CHECK_UA in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_UA: api_health_check_dict[cls.API_KEY_HEALTH_CHECK_UA]
                })

            # http check mode
            if cls.API_KEY_HEALTH_CHECK_HTTP_MODE in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_HTTP_MODE: api_health_check_dict[cls.API_KEY_HEALTH_CHECK_HTTP_MODE]
                })

            # custom request headers
            if cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER in api_health_check_dict:
                if str(api_health_check_dict[cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER]).strip() == "":
                    raise Exception("HTTP health check custom request headers: {}, can not be '' or just spaces.".format(cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER))

                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER: api_health_check_dict[cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER]
                })

            # custom request body
            if cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY in api_health_check_dict:
                asp_health_check_dict.update({
                    cls.ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY: api_health_check_dict[cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY]
                })

            asp_conf.update({cls.ASP_KEY_HEALTH_CHECK: asp_health_check_dict})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        # protocol, load balance, health check
        asp_health_check_dict = asp_conf[cls.ASP_KEY_HEALTH_CHECK]
        api_upstream_conf = {
            'protocol': 'https' if asp_conf.get(cls.ASP_KEY_IS_UPSTREAM_HTTPS, False) else 'http',
            cls.API_KEY_UPSTREAM_LOAD_BALANCE: asp_conf.get(cls.ASP_KEY_LOAD_BALANCE_STRATEGY, 'ip_hash'),
            cls.API_KEY_UPSTREAM_HEALTH_CHECK: {
                cls.API_KEY_HEALTH_CHECK_TYPE:
                    'disable' if not asp_health_check_dict.get(cls.ASP_KEY_IS_HEALTH_CHECK_ENABLE, False)
                        else asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_TYPE, 'tcp'),
                cls.API_KEY_HEALTH_CHECK_INTERVAL:
                    int(asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_INTERVAL, 5)),
                cls.API_KEY_HEALTH_CHECK_RETRY_TIMES:
                    int(asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_RETRY_TIMES, 3)),
                cls.API_KEY_HEALTH_CHECK_TIMEOUTS:
                    int(asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_TIMEOUTS, 5)),
                cls.API_KEY_HEALTH_CHECK_PATH:
                    asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_PATH, '/'),
                cls.API_KEY_HEALTH_CHECK_UA:
                    asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_UA, 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)'),
                cls.API_KEY_HEALTH_CHECK_HTTP_MODE:
                    asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_HTTP_MODE, 'path_ua'),
                cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER:
                    asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER, 'GET / HTTP/1.0\r\nUser-Agent:Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)'),
                cls.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY:
                    asp_health_check_dict.get(cls.ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY, ''),
            }
        }

        # upstream list
        upstream_list = []
        for item in asp_conf.get(cls.ASP_KEY_UPSTREAM_LIST, []):
            u = {
                'ip': item[0]
            }
            if not cls.is_mirror_mode:
                u.update({
                    'port': int(item[1]),
                    'enable': item[2],
                    'weight': item[3] if len(item) > 3 else 1
                })
            upstream_list.append(u)
        api_upstream_conf.update({cls.API_KEY_UPSTREAM_LIST: upstream_list})
        return {cls.API_KEY_UPSTREAM: api_upstream_conf}


class AllowedMethodsConverter(ConverterBase):
    # 为了与UI上保持一致，保留了两个占位元素(以前是CONNECT和TRACE方法)
    ordered_allow_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "", "", "PATCH", "MKCOL", "COPY", "MOVE", "PROPFIND", "PROPPATCH", "LOCK", "UNLOCK"]
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        limit_except = [False] * len(cls.ordered_allow_methods)
        for method in api_value:
            index = cls.ordered_allow_methods.index(method)
            limit_except[index] = True

        return {cls.ASP_KEY_LIMIT_EXCEPT: limit_except}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        allowed_methods = []
        for i, method in enumerate(asp_conf.get(cls.ASP_KEY_LIMIT_EXCEPT, [])):
            if method:
                allowed_methods.append(cls.ordered_allow_methods[i])

        return {cls.API_KEY_ALLOWED_HTTP_METHODS: allowed_methods}


class SelfHealthCheckConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        asp_conf[cls.ASP_KEY_ENABLE_SELF_HEALTH_CHECK] = api_value.get('enable', False)
        asp_conf[cls.ASP_KEY_SELF_HEALTH_CHECK_PATH] = api_value.get('path', '/healthpath')

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        api_conf = {}
        api_conf['enable'] = asp_conf.get(cls.ASP_KEY_ENABLE_SELF_HEALTH_CHECK, False)
        if api_conf['enable']:
            api_conf['path'] = asp_conf.get(cls.ASP_KEY_SELF_HEALTH_CHECK_PATH, '/healthpath')

        return {cls.API_KEY_SELF_HEALTH_CHECK: api_conf}


class InternalResPathConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        path = api_value.strip()
        # 依据UI，如果输入仅为空白字符，应该strip掉，使用默认的 /
        path = path if path else '/'
        return {cls.ASP_KEY_INTERNAL_RES_PATH: path}

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_INTERNAL_RES_PATH: asp_conf.get(cls.ASP_KEY_INTERNAL_RES_PATH, '/')}

class SrcIPStrategyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        asp_conf[ConverterBase.ASP_KEY_SRC_IP_USE_GLOBAL_SETTING] = api_value['use_global_setting']
        rules = []
        for item in api_value.get(ConverterBase.API_KEY_SRC_IP_STRATEGY_LIST, []):
            rules.append({
                'src_ip_from_type': item['src_ip_from_type'],
                'src_ip_customed_name': item.get('src_ip_customed_name', ''),
                'xff_position': item.get('xff_position', 'last')
            })
        asp_conf[ConverterBase.ASP_KEY_SRC_IP_STRATEGY_LIST] = rules

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {
            'use_gloal_setting': asp_conf.get(cls.ASP_KEY_SRC_IP_USE_GLOBAL_SETTING, True),
            ConverterBase.API_KEY_SRC_IP_STRATEGY_LIST: asp_conf.get(cls.ASP_KEY_SRC_IP_STRATEGY_LIST, [])
        }


class WechatStrategyConverter(ConverterBase):
    @classmethod
    @ConverterBase._need_https
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}

        if 'enable' in api_value:
            enable = api_value['enable']
            protect_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST)
            protect_list.update({cls.ASP_KEY_WECHAT_MINI_PROTECTION: enable})
        if 'request_white_list' in api_value:
            asp_conf[cls.ASP_KEY_WECHAT_ACCESS_WHITE_LIST] = api_value['request_white_list']
        if 'enable_request_body_encrypt' in api_value:
            asp_conf[cls.ASP_KEY_WECHAT_APP_REQ_BODY_ENCRYPT] = api_value['enable_request_body_encrypt']
        if 'enable_response_body_encrypt' in api_value:
            asp_conf[cls.ASP_KEY_WECHAT_APP_RESP_BODY_ENCRYPT] = api_value['enable_response_body_encrypt']

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        if not asp_conf.get(cls.ASP_KEY_IS_HTTPS):
            return {}

        api_conf = {}
        protect_list = asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})
        api_conf['enable'] = protect_list.get(cls.ASP_KEY_WECHAT_MINI_PROTECTION, False)
        if api_conf['enable']:
            api_conf['request_white_list'] = asp_conf.get(cls.ASP_KEY_WECHAT_ACCESS_WHITE_LIST, [])
            api_conf['enable_request_body_encrypt'] = asp_conf.get(cls.ASP_KEY_WECHAT_APP_REQ_BODY_ENCRYPT, True)
            api_conf['enable_response_body_encrypt'] = asp_conf.get(cls.ASP_KEY_WECHAT_APP_RESP_BODY_ENCRYPT, True)

        return { cls.API_KEY_WECHAT_STRATEGY: api_conf }



class AlipayStrategyConverter(ConverterBase):
    @classmethod
    @ConverterBase._need_https
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}

        if 'enable' in api_value:
            enable = api_value['enable']
            protect_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST)
            protect_list.update({cls.ASP_KEY_ALIPAY_MINI_PROTECTION: enable})
        if 'request_white_list' in api_value:
            asp_conf[cls.ASP_KEY_ALIPAY_MPP_ACCESS_WHITE_LIST] = api_value['request_white_list']
        if 'enable_request_body_encrypt' in api_value:
            asp_conf[cls.ASP_KEY_ALIPAY_MPP_REQ_BODY_ENCRYPT] = api_value['enable_request_body_encrypt']
        if 'enable_response_body_encrypt' in api_value:
            asp_conf[cls.ASP_KEY_ALIPAY_MPP_RESP_BODY_ENCRYPT] = api_value['enable_response_body_encrypt']

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        if not asp_conf.get(cls.ASP_KEY_IS_HTTPS):
            return {}

        api_conf = {}
        protect_list = asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})
        api_conf['enable'] = protect_list.get(cls.ASP_KEY_ALIPAY_MINI_PROTECTION, False)
        if api_conf['enable']:
            api_conf['request_white_list'] = asp_conf.get(cls.ASP_KEY_ALIPAY_MPP_ACCESS_WHITE_LIST, [])
            api_conf['enable_request_body_encrypt'] = asp_conf.get(cls.ASP_KEY_ALIPAY_MPP_REQ_BODY_ENCRYPT, True)
            api_conf['enable_response_body_encrypt'] = asp_conf.get(cls.ASP_KEY_ALIPAY_MPP_RESP_BODY_ENCRYPT, True)

        return { cls.API_KEY_ALIPAY_STRATEGY: api_conf }


class MpaasMppStrategyConverter(ConverterBase):
    @classmethod
    @ConverterBase._need_https
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}

        if 'enable' in api_value:
            enable = api_value['enable']
            protect_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST)
            protect_list.update({cls.ASP_KEY_MPAAS_MPP_PROTECTION: enable})
        if 'request_white_list' in api_value:
            asp_conf[cls.ASP_KEY_MPAAS_MPP_ACCESS_WHITE_LIST] = api_value['request_white_list']
        if 'enable_request_body_encrypt' in api_value:
            asp_conf[cls.ASP_KEY_MPAAS_MPP_REQ_BODY_ENCRYPT] = api_value['enable_request_body_encrypt']
        if 'enable_response_body_encrypt' in api_value:
            asp_conf[cls.ASP_KEY_MPAAS_MPP_RESP_BODY_ENCRYPT] = api_value['enable_response_body_encrypt']

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        if not asp_conf.get(cls.ASP_KEY_IS_HTTPS):
            return {}

        api_conf = {}
        protect_list = asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})
        api_conf['enable'] = protect_list.get(cls.ASP_KEY_MPAAS_MPP_PROTECTION, False)
        if api_conf['enable']:
            api_conf['request_white_list'] = asp_conf.get(cls.ASP_KEY_MPAAS_MPP_ACCESS_WHITE_LIST, [])
            api_conf['enable_request_body_encrypt'] = asp_conf.get(cls.ASP_KEY_MPAAS_MPP_REQ_BODY_ENCRYPT, True)
            api_conf['enable_response_body_encrypt'] = asp_conf.get(cls.ASP_KEY_MPAAS_MPP_RESP_BODY_ENCRYPT, True)

        return { cls.API_KEY_MPAAS_MPP_STRATEGY: api_conf }


class IpStrategyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        if 'type' in api_value:
            if api_value['type'] == cls.API_KEY_PROTECT_ALL_IP:
                asp_conf.update({cls.ASP_KEY_IP_LIST_SIWTCH: cls.ASP_KEY_IP_LIST_SIWTCH_ALL_IP})
            elif api_value['type'] == cls.API_KEY_PROTECT_IP_WHITE_LIST:
                asp_conf.update({cls.ASP_KEY_IP_LIST_SIWTCH: cls.ASP_KEY_IP_LIST_SIWTCH_WHITE_LIST})
            elif api_value['type'] == cls.API_KEY_PROTECT_IP_PROTECTION_LIST:
                asp_conf.update({cls.ASP_KEY_IP_LIST_SIWTCH: cls.ASP_KEY_IP_LIST_SIWTCH_PROTECTION_LIST})

        if cls.API_KEY_PROTECT_IP_WHITE_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_IP_WHITE_LIST: api_value[cls.API_KEY_PROTECT_IP_WHITE_LIST]})

        if cls.API_KEY_PROTECT_IP_PROTECTION_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_IP_PROTECTION_LIST: api_value[cls.API_KEY_PROTECT_IP_PROTECTION_LIST]})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        api_ip_conf = {}
        asp_ip_strategy_type = asp_conf.get(cls.ASP_KEY_IP_LIST_SIWTCH, cls.ASP_KEY_IP_LIST_SIWTCH_ALL_IP)
        if asp_ip_strategy_type == cls.ASP_KEY_IP_LIST_SIWTCH_ALL_IP:
            api_ip_conf.update({'type': cls.API_KEY_PROTECT_ALL_IP})
        elif asp_ip_strategy_type == cls.ASP_KEY_IP_LIST_SIWTCH_WHITE_LIST:
            api_ip_conf.update({'type': cls.API_KEY_PROTECT_IP_WHITE_LIST})
        elif asp_ip_strategy_type == cls.ASP_KEY_IP_LIST_SIWTCH_PROTECTION_LIST:
            api_ip_conf.update({'type': cls.API_KEY_PROTECT_IP_PROTECTION_LIST})

        api_ip_conf.update({cls.API_KEY_PROTECT_IP_WHITE_LIST: asp_conf.get(cls.ASP_KEY_IP_WHITE_LIST, [])})
        api_ip_conf.update({cls.API_KEY_PROTECT_IP_PROTECTION_LIST: asp_conf.get(cls.ASP_KEY_IP_PROTECTION_LIST, [])})
        return {cls.API_KEY_IP_STRATEGY: api_ip_conf}


class WebPrimaryStrategyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        # check license
        if not is_license_primary_protection() and not is_debug():
            return {}
        asp_conf = {}

        security_level_list = current_site_asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        protected_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})

        if 'enable' in api_value:
            if api_value['enable']:
                # add when input is True
                if cls.ASP_KEY_SECURITY_LEVEL_PRIMARY_TOKEN not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_PRIMARY_TOKEN)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WEB_PRIMARY: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_PRIMARY_TOKEN in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_PRIMARY_TOKEN)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WEB_PRIMARY: False})

        if cls.API_KEY_AUTOMATED_TOOL_INTERCEPT in api_value:
            if api_value[cls.API_KEY_AUTOMATED_TOOL_INTERCEPT]:
                if cls.ASP_KEY_SECURITY_LEVEL_BOT not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_BOT)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_BOT: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_BOT in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_BOT)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_BOT: False})

        if cls.API_KEY_CRACK_BEHAVIOR_INTERCEPT in api_value:
            if api_value[cls.API_KEY_CRACK_BEHAVIOR_INTERCEPT]:
                # add when 1. default, 2. input is True
                if cls.ASP_KEY_SECURITY_LEVEL_CRACK not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_CRACK)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_CRACK: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_CRACK in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_CRACK)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_CRACK: False})

        security_level_list.sort()

        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MIN: security_level_list})
        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MAX: security_level_list})

        asp_conf.update({cls.ASP_KEY_PROTECTED_LIST: protected_list})

        if cls.API_KEY_REQUEST_WHITE_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_FULL_WHITE_LIST: api_value[cls.API_KEY_REQUEST_WHITE_LIST]})

        if cls.API_KEY_RESPONSE_WHITE_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_FULL_WHITE_LIST_OUT: api_value[cls.API_KEY_RESPONSE_WHITE_LIST]})

        if cls.API_KEY_AJAX_TOKEN_BYPASS_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_TOKEN_BYPASS_LIST: api_value[cls.API_KEY_AJAX_TOKEN_BYPASS_LIST]})

        if cls.API_KEY_AJAX_TOKEN_PATH_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_TOKEN_PATH_LIST: api_value[cls.API_KEY_AJAX_TOKEN_PATH_LIST]})

        if cls.API_KEY_AJAX_TOKEN_PATH_TYPE in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_TOKEN_PATH_TYPE: api_value[cls.API_KEY_AJAX_TOKEN_PATH_TYPE]})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        # check license
        if not is_license_primary_protection() and not is_debug():
            return {}
        security_level_list = asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        api_web_primary_conf = {
            'enable': cls.ASP_KEY_SECURITY_LEVEL_PRIMARY_TOKEN in security_level_list,
            cls.API_KEY_AUTOMATED_TOOL_INTERCEPT:
                cls.ASP_KEY_SECURITY_LEVEL_BOT in security_level_list,
            cls.API_KEY_CRACK_BEHAVIOR_INTERCEPT:
                cls.ASP_KEY_SECURITY_LEVEL_CRACK in security_level_list,
            cls.API_KEY_REQUEST_WHITE_LIST: asp_conf.get(cls.ASP_KEY_FULL_WHITE_LIST, []),
            cls.API_KEY_RESPONSE_WHITE_LIST: asp_conf.get(cls.ASP_KEY_FULL_WHITE_LIST_OUT, []),
            cls.API_KEY_AJAX_TOKEN_BYPASS_LIST: asp_conf.get(cls.ASP_KEY_AJAX_TOKEN_BYPASS_LIST, []),
            cls.API_KEY_AJAX_TOKEN_PATH_LIST: asp_conf.get(cls.ASP_KEY_AJAX_TOKEN_PATH_LIST, []),
            cls.API_KEY_AJAX_TOKEN_PATH_TYPE: asp_conf.get(cls.ASP_KEY_AJAX_TOKEN_PATH_TYPE, 0)
        }

        return {cls.API_KEY_WEB_PRIMARY_STRATEGY: api_web_primary_conf}


class WebEssentialStrategyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        # check license
        if not is_license_essential_protection() and not is_debug():
            return {}
        asp_conf = {}

        security_level_list = current_site_asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        protected_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})

        if 'enable' in api_value:
            if api_value['enable']:
                # add when input is True
                if cls.ASP_KEY_SECURITY_LEVEL_COOKIE_TOKEN not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_COOKIE_TOKEN)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WEB_ESSENTIAL: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_COOKIE_TOKEN in security_level_list and \
                        cls.ASP_KEY_SECURITY_LEVEL_URL not in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_COOKIE_TOKEN)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WEB_ESSENTIAL: False})

        if cls.API_KEY_AUTOMATED_TOOL_INTERCEPT in api_value:
            if api_value[cls.API_KEY_AUTOMATED_TOOL_INTERCEPT]:
                if cls.ASP_KEY_SECURITY_LEVEL_BOT not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_BOT)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_BOT: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_BOT in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_BOT)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_BOT: False})

        if cls.API_KEY_CRACK_BEHAVIOR_INTERCEPT in api_value:
            if api_value[cls.API_KEY_CRACK_BEHAVIOR_INTERCEPT]:
                # add when 1. default, 2. input is True
                if cls.ASP_KEY_SECURITY_LEVEL_CRACK not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_CRACK)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_CRACK: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_CRACK in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_CRACK)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_CRACK: False})

        security_level_list.sort()

        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MIN: security_level_list})
        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MAX: security_level_list})

        asp_conf.update({cls.ASP_KEY_PROTECTED_LIST: protected_list})

        if cls.API_KEY_PREVENT_SCANNER in api_value:
            asp_conf.update({cls.ASP_KEY_PREVENT_SCANNER: api_value[cls.API_KEY_PREVENT_SCANNER]})

        if cls.API_KEY_REQUEST_WHITE_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_FULL_WHITE_LIST: api_value[cls.API_KEY_REQUEST_WHITE_LIST]})

        if cls.API_KEY_RESPONSE_WHITE_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_FULL_WHITE_LIST_OUT: api_value[cls.API_KEY_RESPONSE_WHITE_LIST]})

        if cls.API_KEY_AJAX_TOKEN_BYPASS_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_TOKEN_BYPASS_LIST: api_value[cls.API_KEY_AJAX_TOKEN_BYPASS_LIST]})

        if cls.API_KEY_AJAX_TOKEN_PATH_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_TOKEN_PATH_LIST: api_value[cls.API_KEY_AJAX_TOKEN_PATH_LIST]})

        if cls.API_KEY_AJAX_TOKEN_PATH_TYPE in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_TOKEN_PATH_TYPE: api_value[cls.API_KEY_AJAX_TOKEN_PATH_TYPE]})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        # check license
        if not is_license_essential_protection() and not is_debug():
            return {}
        security_level_list = asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        api_web_essential_conf = {
            'enable': cls.ASP_KEY_SECURITY_LEVEL_COOKIE_TOKEN in security_level_list,
            cls.API_KEY_AUTOMATED_TOOL_INTERCEPT:
                cls.ASP_KEY_SECURITY_LEVEL_BOT in security_level_list,
            cls.API_KEY_CRACK_BEHAVIOR_INTERCEPT:
                cls.ASP_KEY_SECURITY_LEVEL_CRACK in security_level_list,
            cls.API_KEY_PREVENT_SCANNER: asp_conf.get(cls.ASP_KEY_PREVENT_SCANNER, False),
            cls.API_KEY_REQUEST_WHITE_LIST: asp_conf.get(cls.ASP_KEY_FULL_WHITE_LIST, []),
            cls.API_KEY_RESPONSE_WHITE_LIST: asp_conf.get(cls.ASP_KEY_FULL_WHITE_LIST_OUT, []),
            cls.API_KEY_AJAX_TOKEN_BYPASS_LIST: asp_conf.get(cls.ASP_KEY_AJAX_TOKEN_BYPASS_LIST, []),
            cls.API_KEY_AJAX_TOKEN_PATH_LIST: asp_conf.get(cls.ASP_KEY_AJAX_TOKEN_PATH_LIST, []),
            cls.API_KEY_AJAX_TOKEN_PATH_TYPE: asp_conf.get(cls.ASP_KEY_AJAX_TOKEN_PATH_TYPE, 0)
        }

        return {cls.API_KEY_WEB_ESSENTIAL_STRATEGY: api_web_essential_conf}


class WebPowerStrategyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        # check license
        if not is_license_advanced_protection() and not is_debug():
            return {}

        asp_conf = {}

        security_level_list = current_site_asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        protected_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})

        if 'enable' in api_value:
            if api_value['enable']:
                # add only when input is True
                if cls.ASP_KEY_SECURITY_LEVEL_URL not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_URL)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WEB_POWER: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_URL in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_URL)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WEB_POWER: False})

        if cls.API_KEY_ENABLE_AJAX_REQ_BODY_ENC in api_value:
            protected_list.update({cls.ASP_KEY_PROTECTED_LIST_AJAX_REQ_BODY_ENC: api_value[cls.API_KEY_ENABLE_AJAX_REQ_BODY_ENC]})
        if cls.API_KEY_ENABLE_AJAX_RESP_BODY_ENC in api_value:
            protected_list.update({cls.ASP_KEY_PROTECTED_LIST_AJAX_RESP_BODY_ENC: api_value[cls.API_KEY_ENABLE_AJAX_RESP_BODY_ENC]})

        if cls.API_KEY_FORM_ENCRYPTION in api_value:
            if api_value[cls.API_KEY_FORM_ENCRYPTION]:
                if cls.ASP_KEY_SECURITY_LEVEL_FORM not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_FORM)
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_FORM in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_FORM)

        if cls.API_KEY_COOKIE_ENCRYPTION in api_value:
            if api_value[cls.API_KEY_COOKIE_ENCRYPTION]:
                if cls.ASP_KEY_SECURITY_LEVEL_COOKIE_ENCRYPTION not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_COOKIE_ENCRYPTION)
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_COOKIE_ENCRYPTION in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_COOKIE_ENCRYPTION)

        security_level_list.sort()

        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MIN: security_level_list})
        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MAX: security_level_list})
        asp_conf.update({cls.ASP_KEY_PROTECTED_LIST: protected_list})

        if cls.API_KEY_VERIFICATION_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_VERIFICATION_LIST: api_value[cls.API_KEY_VERIFICATION_LIST]})

        if cls.API_KEY_ENCAPSULATION_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_ENCAPSULATION_LIST_OUT: api_value[cls.API_KEY_ENCAPSULATION_LIST]})

        if cls.API_KEY_AJAX_REQ_BODY_ENC_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_REQ_BODY_ENC_LIST: api_value[cls.API_KEY_AJAX_REQ_BODY_ENC_LIST]})

        if cls.API_KEY_AJAX_REFERER_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_REFERER_LIST: api_value[cls.API_KEY_AJAX_REFERER_LIST]})

        if cls.API_KEY_AJAX_RESP_BODY_ENC_LIST in api_value:
            asp_conf.update({cls.ASP_KEY_AJAX_RESP_BODY_ENC_LIST: api_value[cls.API_KEY_AJAX_RESP_BODY_ENC_LIST]})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        # check license
        if not is_license_advanced_protection() and not is_debug():
            return {}

        security_level_list = asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        protect_list = asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})
        api_web_power_conf = {
            'enable': cls.ASP_KEY_SECURITY_LEVEL_URL in security_level_list,
            cls.API_KEY_FORM_ENCRYPTION: cls.ASP_KEY_SECURITY_LEVEL_FORM in security_level_list,
            cls.API_KEY_COOKIE_ENCRYPTION: cls.ASP_KEY_SECURITY_LEVEL_COOKIE_ENCRYPTION in security_level_list,
            cls.API_KEY_VERIFICATION_LIST: asp_conf.get(cls.ASP_KEY_VERIFICATION_LIST, []),
            cls.API_KEY_ENCAPSULATION_LIST: asp_conf.get(cls.ASP_KEY_ENCAPSULATION_LIST_OUT, []),
            cls.API_KEY_AJAX_REQ_BODY_ENC_LIST: asp_conf.get(cls.ASP_KEY_AJAX_REQ_BODY_ENC_LIST, []),
            cls.API_KEY_AJAX_REFERER_LIST: asp_conf.get(cls.ASP_KEY_AJAX_REFERER_LIST, []),
            cls.API_KEY_AJAX_RESP_BODY_ENC_LIST: asp_conf.get(cls.ASP_KEY_AJAX_RESP_BODY_ENC_LIST, []),
            cls.API_KEY_ENABLE_AJAX_REQ_BODY_ENC: protect_list.get(cls.ASP_KEY_PROTECTED_LIST_AJAX_REQ_BODY_ENC, False),
            cls.API_KEY_ENABLE_AJAX_RESP_BODY_ENC: protect_list.get(cls.ASP_KEY_PROTECTED_LIST_AJAX_RESP_BODY_ENC, False)
        }

        return {cls.API_KEY_WEB_POWER_STRATEGY: api_web_power_conf}


class WafStrategyConverter(ConverterBase):

    CONVERT_WAF_OLD_CONFIG = {'looseSet':"1",'defaultSet':"2",'maximumSet':"3"}
    CUSTOM_STRATEGY_MIN_ID = 1000

    @classmethod
    def _get_asp_waf_strategy_conf_by_type(cls, waf_strategy_type):

        if not waf_strategy_type:
            return {}

        waf_strategy_type = str(waf_strategy_type)

        #setting waf customized  strategy.
        #waf_strategy_type format: customized_xxxxx
        if waf_strategy_type.startswith(cls.API_KEY_WAF_STRATEGY_TYPE_CUSTOMIZED) and '_' in waf_strategy_type:
            waf_strategy_id = waf_strategy_type.split('_')[1]
            siteStrategy = SiteStrategy()
            if siteStrategy.is_strategy_id_exists(waf_strategy_id):
                return {cls.ASP_KEY_WAF_STRATEGY: waf_strategy_id}

        # setting  waf default strategy.
        id_map_name, name_map_id = get_waf_default_site_strategies_id_map_name()
        waf_strategy_id = name_map_id.get(waf_strategy_type)
        if waf_strategy_id:
            return {cls.ASP_KEY_WAF_STRATEGY: waf_strategy_id}


        error = "waf strategy type: '{}' does not exist.".format(waf_strategy_type)
        logger.error(error)
        raise Exception(error)

    @classmethod
    def _get_api_waf_strategy_type(cls, asp_conf):

        asp_waf_stratety = asp_conf.get(cls.ASP_KEY_WAF_STRATEGY)
        if asp_waf_stratety:

            if int(asp_waf_stratety) > cls.CUSTOM_STRATEGY_MIN_ID:
                return "{}_{}".format(cls.API_KEY_WAF_STRATEGY_TYPE_CUSTOMIZED,asp_waf_stratety)

            id_map_name, name_map_id = get_waf_default_site_strategies_id_map_name()
            waf_strategy_name = id_map_name.get(str(asp_waf_stratety))
            if waf_strategy_name:
                return waf_strategy_name

            return "unknown"

        else:
            asp_waf_rule_set = asp_conf.get(cls.ASP_KEY_WAF_RULE_SET, cls.ASP_KEY_WAF_LOOSE_SET)
            if asp_waf_rule_set == cls.ASP_KEY_WAF_LOOSE_SET:
                return cls.API_KEY_WAF_STRATEGY_TYPE_BASIC
            elif asp_waf_rule_set == cls.ASP_KEY_WAF_DEFAULT_SET:
                return cls.API_KEY_WAF_STRATEGY_TYPE_STANDARD
            elif asp_waf_rule_set == cls.ASP_KEY_WAF_MAXIMUM_SET:
                return cls.API_KEY_WAF_STRATEGY_TYPE_STRICT
            elif asp_waf_rule_set == cls.ASP_KEY_WAF_CUSTOM_SET:
                return cls.API_KEY_WAF_STRATEGY_TYPE_CUSTOMIZED
            else:
                return "unknown"

    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}

        security_level_list = current_site_asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, [])
        protected_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})

        if 'enable' in api_value:
            if api_value['enable']:
                # add only when input is True
                if cls.ASP_KEY_SECURITY_LEVEL_WAF not in security_level_list:
                    security_level_list.append(cls.ASP_KEY_SECURITY_LEVEL_WAF)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WAF: True})
            else:
                if cls.ASP_KEY_SECURITY_LEVEL_WAF in security_level_list:
                    security_level_list.remove(cls.ASP_KEY_SECURITY_LEVEL_WAF)

                protected_list.update({cls.ASP_KEY_PROTECTED_LIST_WAF: False})

        security_level_list.sort()

        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MIN: security_level_list})
        asp_conf.update({cls.ASP_KEY_SECURITY_LEVEL_MAX: security_level_list})
        asp_conf.update({cls.ASP_KEY_PROTECTED_LIST: protected_list})

        if cls.API_KEY_WAF_MONITOR_ONLY in api_value:
            asp_conf.update({cls.ASP_KEY_WAF_LEARNING_MODE: api_value[cls.API_KEY_WAF_MONITOR_ONLY]})

        if cls.API_KEY_WAF_STRATEGY_TYPE in api_value:
            asp_conf.update(cls._get_asp_waf_strategy_conf_by_type(api_value[cls.API_KEY_WAF_STRATEGY_TYPE]))

        if cls.API_KEY_WAF_WHITE_LIST in api_value:
            # waf white list (Inject_Whitelist) format:
            # [
            #   'url',          # const
            #   'url path',     # configured url path
            #   'rule id',      # url id
            #   '',             # empty
            #   'comments',     #
            # ]
            asp_waf_white_list = []
            for item in api_value[cls.API_KEY_WAF_WHITE_LIST]:
                asp_waf_white_list.append(['url', item[0], item[1], '', item[2]])
            asp_conf.update({cls.ASP_KEY_WAF_WHITE_LIST: asp_waf_white_list})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        api_waf_conf = {
            'enable': cls.ASP_KEY_SECURITY_LEVEL_WAF in asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, []),
            cls.API_KEY_WAF_MONITOR_ONLY: asp_conf.get(cls.ASP_KEY_WAF_LEARNING_MODE, False),
            cls.API_KEY_WAF_STRATEGY_TYPE: cls._get_api_waf_strategy_type(asp_conf)
        }

        api_waf_white_list = []
        for item in asp_conf.get(cls.ASP_KEY_WAF_WHITE_LIST, []):
            api_waf_white_list.append([item[1], item[2], item[4]])

        api_waf_conf.update({cls.API_KEY_WAF_WHITE_LIST: api_waf_white_list})

        return {cls.API_KEY_WAF_STRATEGY: api_waf_conf}

    @classmethod
    def _to_restful_api_conf_get_all_sites(cls, asp_conf):
        api_waf_conf = {
            'enable': cls.ASP_KEY_SECURITY_LEVEL_WAF in asp_conf.get(cls.ASP_KEY_SECURITY_LEVEL_MIN, []),
            cls.API_KEY_WAF_MONITOR_ONLY: asp_conf.get(cls.ASP_KEY_WAF_LEARNING_MODE, False),
            cls.API_KEY_WAF_STRATEGY_TYPE: cls._get_api_waf_strategy_type(asp_conf)
        }

        return {cls.API_KEY_WAF_STRATEGY: api_waf_conf}


class AIWafStrategyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        # check license
        if not is_license_advanced_waf_enabled() and not is_debug():
            return {}

        asp_conf = {}
        asp_ai_waf_module = current_site_asp_conf.get(cls.ASP_KEY_AI_WAF_MODULES, {})
        protected_list = current_site_asp_conf.get(cls.ASP_KEY_PROTECTED_LIST, {})

        if 'enable' in api_value:
            asp_ai_waf_module.update({cls.ASP_KEY_AI_WAF_ENABLE: api_value['enable']})
            protected_list.update({cls.ASP_KEY_PROTECTED_LIST_AIWAF: api_value['enable']})

        if cls.API_KEY_AI_WAF_MONITOR_ONLY in api_value:
            asp_ai_waf_module.update({cls.ASP_KEY_AI_WAF_LEARNING_MODE: api_value[cls.API_KEY_AI_WAF_MONITOR_ONLY]})

        if cls.API_KEY_AI_WAF_WHITE_LIST in api_value:
            asp_ai_waf_module.update({cls.ASP_KEY_AI_WAF_WHITE_LIST: api_value[cls.API_KEY_AI_WAF_WHITE_LIST]})

        asp_conf.update({cls.ASP_KEY_AI_WAF_MODULES: asp_ai_waf_module})
        asp_conf.update({cls.ASP_KEY_PROTECTED_LIST: protected_list})

        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        # check license
        if not is_license_advanced_waf_enabled() and not is_debug():
            return {}

        asp_ai_waf_modules = asp_conf.get(cls.ASP_KEY_AI_WAF_MODULES, {})
        api_aiwaf_conf = {
            'enable': asp_ai_waf_modules.get(cls.ASP_KEY_AI_WAF_ENABLE, False),
            cls.API_KEY_AI_WAF_MONITOR_ONLY: asp_ai_waf_modules.get(cls.ASP_KEY_AI_WAF_LEARNING_MODE, False),
            cls.API_KEY_AI_WAF_WHITE_LIST: asp_ai_waf_modules.get(cls.ASP_KEY_AI_WAF_WHITE_LIST, []),
        }

        return {cls.API_KEY_AI_WAF_STRATEGY: api_aiwaf_conf}


class InternationalCertificateConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        if current_site_asp_conf.get('protocol') == 'http':
            return {}
        empty = api_value == ''
        return {
            cls.ASP_KEY_USE_INTERNATIONAL_CERT: True,
            cls.ASP_KEY_USE_BUILTIN_CERT: empty,
            cls.ASP_KEY_CERTIFICATION: base64.b64decode(api_value),
            cls.ASP_KEY_CERTIFICATION_FILENAME: '' if empty else time.strftime('%Y-%m-%dT%H:%M:%S') + '.crt'
        }

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {}


class InternationalCertificateKeyConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        if current_site_asp_conf.get('protocol') == 'http':
            return {}
        empty = api_value == ''
        return {
            cls.ASP_KEY_USE_INTERNATIONAL_CERT: True,
            cls.ASP_KEY_USE_BUILTIN_CERT: empty,
            cls.ASP_KEY_CERTIFICATION_KEY: base64.b64decode(api_value),
            cls.ASP_KEY_CERTIFICATION_KEY_FILENAME: '' if empty else time.strftime('%Y-%m-%dT%H:%M:%S') + '.key'
        }

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {}


class GMCertConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        if current_site_asp_conf.get('protocol') == 'http':
            return {}

        useBuiltin = api_value['type'] == 'builtin'
        result = {
            cls.ASP_KEY_USE_GM_CERT: api_value['enable'],
            cls.ASP_KEY_USE_GM_BUILTIN_CERT: useBuiltin
        }
        if not useBuiltin:
            result.update({
                cls.ASP_KEY_GM_SIGN_CERTIFICATION: base64.b64decode(api_value[ConverterBase.API_KEY_GM_SIGN_CERTIFICATE]),
                cls.ASP_KEY_GM_SIGN_CERTIFICATION_FILENAME: time.strftime('sign_%Y-%m-%dT%H:%M:%S.key'),
                cls.ASP_KEY_GM_SIGN_CERTIFICATION_KEY: base64.b64decode(api_value[ConverterBase.API_KEY_GM_SIGN_CERTIFICATE_KEY]),
                cls.ASP_KEY_GM_SIGN_CERTIFICATION_KEY_FILENAME: time.strftime('sign_%Y-%m-%dT%H:%M:%S.crt'),
                cls.ASP_KEY_GM_ENC_CERTIFICATION: base64.b64decode(api_value[ConverterBase.API_KEY_GM_ENC_CERTIFICATE]),
                cls.ASP_KEY_GM_ENC_CERTIFICATION_FILENAME: time.strftime('enc_%Y-%m-%dT%H:%M:%S.key'),
                cls.ASP_KEY_GM_ENC_CERTIFICATION_KEY: base64.b64decode(api_value[ConverterBase.API_KEY_GM_ENC_CERTIFICATE_KEY]),
                cls.ASP_KEY_GM_ENC_CERTIFICATION_KEY_FILENAME: time.strftime('enc_%Y-%m-%dT%H:%M:%S.crt')
            })

        return result

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        api_conf = {}
        if asp_conf.get(cls.ASP_KEY_IS_HTTPS):
            api_conf[cls.API_KEY_GM_CERTIFICATE] = {
                'enable': asp_conf.get(cls.ASP_KEY_USE_GM_CERT),
                'type': 'builtin' if asp_conf.get(cls.ASP_KEY_USE_GM_BUILTIN_CERT) else 'upload'
            }

        return api_conf


class StaticResourceListConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        staticResource = ''
        # static resource list
        if api_value:
            staticResource = re.sub(',+',',',api_value).strip('\r\n ,').lower()
        asp_conf.update({
            cls.ASP_KEY_STATIC_RESOURCE_LIST: staticResource
        })
        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_STATIC_RESOURCE_LIST: asp_conf.get(cls.ASP_KEY_STATIC_RESOURCE_LIST, '')}


class EnableSiteConfConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        asp_conf.update({
            cls.ASP_KEY_ENABLE_SITE_CONF: api_value != False
        })
        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_ENABLE_SITE_CONF: asp_conf.get(cls.ASP_KEY_ENABLE_SITE_CONF, True)}


class EnableBusinessPathConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        asp_conf.update({
            cls.ASP_KEY_ENABLE_BUSINESS_PATH: api_value == True
        })
        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_ENABLE_BUSINESS_PATH: asp_conf.get(cls.ASP_KEY_ENABLE_BUSINESS_PATH, False)}


class BusinessPathConverter(ConverterBase):
    @classmethod
    def _to_asp_conf(cls, api_value, current_site_asp_conf):
        asp_conf = {}
        asp_conf.update({
            cls.ASP_KEY_BUSINESS_PATH: str(api_value)
        })
        return asp_conf

    @classmethod
    def _to_restful_api_conf(cls, asp_conf):
        return {cls.API_KEY_BUSINESS_PATH: asp_conf.get(cls.ASP_KEY_BUSINESS_PATH, '')}


class ProtectedSitesAdapter(object):
    """
    Site conf Adapter
    """
    NGINX_UPSTREAMS_PATH = 'nginx/upstreams/'
    NGINX_WEBCONSOLE_PORT_PATH = 'nginx/web_console/port'
    NGINX_GLOBAL_SRC_IP_STRATEGY_LIST_PATH = 'nginx/src_ip_strategy_list'

    API_KEY_SITE_ID = 'id'

    CONVERTER_MAPPING_TABLE = [
        (ConverterBase.API_KEY_PROTOCOL, ProtocolConverter),
        (ConverterBase.API_KEY_SITE_NAME_TYPE, SiteNameTypeConverter),
        (ConverterBase.API_KEY_SITE_NAME, SiteNameConverter),
        (ConverterBase.API_KEY_PORT, PortConverter),
        (ConverterBase.API_KEY_CUSTOMIZED_SITE_NAME, CustomizedSiteNameConverter),
        (ConverterBase.API_KEY_PROTECTION_MODE, ProtectionModeConverter),
        (ConverterBase.API_KEY_UPSTREAM, UpstreamConverter),
        (ConverterBase.API_KEY_WAF_STRATEGY, WafStrategyConverter),
        (ConverterBase.API_KEY_AI_WAF_STRATEGY, AIWafStrategyConverter),
        (ConverterBase.API_KEY_CERTIFICATE_KEY, InternationalCertificateKeyConverter),
        (ConverterBase.API_KEY_ENABLE_SITE_CONF, EnableSiteConfConverter),
        (ConverterBase.API_KEY_ENABLE_BUSINESS_PATH, EnableBusinessPathConverter),
        (ConverterBase.API_KEY_BUSINESS_PATH, BusinessPathConverter),
    ]  # type: List[Tuple[str, ConverterBase]]

    if ConfDb().get_deploy_mode() != ConfDb.DEPLOY_MODE_MIRROR:
        CONVERTER_MAPPING_TABLE.extend([
            (ConverterBase.API_KEY_TERMINAL_SETTING, TerminalSettingConverter),
            (ConverterBase.API_KEY_INVALID_ACTION, InvalidActionConverter),
            (ConverterBase.API_KEY_INVALID_ACTION_REDIRECT_PATH, InvalidActionRedirectPathConverter),
            (ConverterBase.API_KEY_IP_STRATEGY, IpStrategyConverter),
            (ConverterBase.API_KEY_WEB_PRIMARY_STRATEGY, WebPrimaryStrategyConverter),
            (ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY, WebEssentialStrategyConverter),
            (ConverterBase.API_KEY_WEB_POWER_STRATEGY, WebPowerStrategyConverter),
            (ConverterBase.API_KEY_CERTIFICATE, InternationalCertificateConverter),
            (ConverterBase.API_KEY_GM_CERTIFICATE, GMCertConverter),
            (ConverterBase.API_KEY_ALLOWED_HTTP_METHODS, AllowedMethodsConverter),
            (ConverterBase.API_KEY_SELF_HEALTH_CHECK, SelfHealthCheckConverter),
            (ConverterBase.API_KEY_INTERNAL_RES_PATH, InternalResPathConverter),
            (ConverterBase.API_KEY_STATIC_RESOURCE_LIST, StaticResourceListConverter),
            (ConverterBase.API_KEY_SRC_IP_STRATEGY, SrcIPStrategyConverter),
            (ConverterBase.API_KEY_WECHAT_STRATEGY, WechatStrategyConverter),
            (ConverterBase.API_KEY_ALIPAY_STRATEGY, AlipayStrategyConverter),
            (ConverterBase.API_KEY_MPAAS_MPP_STRATEGY, MpaasMppStrategyConverter)

        ])

    CANNOT_BE_MODIFIED_API_ITMES = [
        ConverterBase.API_KEY_SITE_NAME_TYPE,
        ConverterBase.API_KEY_SITE_NAME,
        ConverterBase.API_KEY_PROTOCOL,
        ConverterBase.API_KEY_PORT,
        ConverterBase.API_KEY_ENABLE_BUSINESS_PATH,
        ConverterBase.API_KEY_BUSINESS_PATH,
    ]

    ALL_SITE_LIST_ITEMS = CANNOT_BE_MODIFIED_API_ITMES + [
        ConverterBase.API_KEY_CUSTOMIZED_SITE_NAME,
        ConverterBase.API_KEY_ENABLE_SITE_CONF,
        ConverterBase.API_KEY_PROTECTION_MODE,
        ConverterBase.API_KEY_WAF_STRATEGY
    ]

    default_site_asp_conf_json = os.path.abspath(os.path.join(__file__, '../default_site_asp_conf.json'))

    def __init__(self):
        # load default configuration, generated by NgxConfModel
        with open(self.default_site_asp_conf_json) as f:
            self.default_site_asp_conf = json.loads(f.read())

        logging.debug('default asp site conf was loaded...')

    @classmethod
    def get_webconsole_port(cls):
        return get_config(cls.NGINX_WEBCONSOLE_PORT_PATH)

    @classmethod
    def get_specified_site_asp_conf(cls, site_id):
        return get_config_all(cls.NGINX_UPSTREAMS_PATH + site_id)

    @classmethod
    def _create_site_id(cls, api_site_conf):
        site_name_type = api_site_conf[ConverterBase.API_KEY_SITE_NAME_TYPE]
        site_name = api_site_conf[ConverterBase.API_KEY_SITE_NAME]
        port = api_site_conf[ConverterBase.API_KEY_PORT]

        enable = api_site_conf.get(ConverterBase.API_KEY_ENABLE_BUSINESS_PATH, False)
        path = api_site_conf.get(ConverterBase.API_KEY_BUSINESS_PATH, '')

        server_name_type = ConverterBase.SERVER_NAME_TYPE_MAP[site_name_type]
        site_id, server_name = generate_server_id(server_name_type, site_name, port, enable, path)
        return site_id, server_name

    def _convert_asp_conf_to_api_conf(self, site_id, asp_conf):
        api_conf = {}
        if asp_conf:
            api_conf.update({self.API_KEY_SITE_ID: site_id})
            for api_key_name, converter in self.CONVERTER_MAPPING_TABLE:
                try:
                    api_conf.update(converter._to_restful_api_conf(asp_conf))
                except Exception as e:
                    logging.info(str(e.message))

        return api_conf

    def _convert_api_conf_to_asp_conf(self, api_site_conf, asp_site_conf):
        for k, convert in self.CONVERTER_MAPPING_TABLE:
            if k in api_site_conf:
                asp_site_conf.update(convert._to_asp_conf(api_site_conf[k], asp_site_conf))

        return asp_site_conf

    def _get_all_site_asp_conf(self):
        return get_config_all(self.NGINX_UPSTREAMS_PATH)

    def _site_api_conf_to_asp_conf(self, site_id, api_site_conf, asp_site_conf):
        try:
            # merge site configuration
            self._convert_api_conf_to_asp_conf(api_site_conf, asp_site_conf)

        except Exception as e:
            return {}, Result.ARGUMENT_ERROR, str(e)

        # add full path
        asp_conf = {}
        for k, v in asp_site_conf.items():
            asp_conf.update({self.NGINX_UPSTREAMS_PATH + site_id + '/' + k: v})

        return asp_conf, Result.SUCCESS, ''

    def get_sites_num(self):
        return len(self._get_all_site_asp_conf())

    def is_site_exist(self, site_id):
        return True if self.get_specified_site_asp_conf(site_id) else False

    def get_specified_site_api_conf(self, site_id):
        asp_site_conf = self.get_specified_site_asp_conf(site_id)
        return self._convert_asp_conf_to_api_conf(site_id, asp_site_conf)

    def get_all_sites_api_conf(self, with_default=False):
        asp_conf = self._get_all_site_asp_conf()
        converts = dict(self.CONVERTER_MAPPING_TABLE)
        is_mirror_mode = ConverterBase.is_mirror_mode
        open_all_ports = get_config('nginx/httpcap_all_ports', False)
        api_conf_list = []  # convert asp conf to api conf

        def convert_site_conf(site_id, conf):
            """将单个站点的 ASP 配置转换为 API 配置"""
            api_site_conf = {self.API_KEY_SITE_ID: site_id}
            for api_key in self.ALL_SITE_LIST_ITEMS:
                try:
                    api_site_conf.update(converts[api_key]._to_restful_api_conf_get_all_sites(conf))
                except Exception as e:
                    logging.info(str(e))  # 避免 AttributeError
            return api_site_conf

        def build_default_com_asp_conf():
            """构建 default.com:* 的 API 配置"""
            default_asp_site_conf = self._get_default_site_asp_conf()
            default_conf = {
                ConverterBase.ASP_KEY_SERVER_NAME: 'default.com',
                ConverterBase.ASP_KEY_SERVER_NAME_OLD: 'default.com',
                ConverterBase.ASP_KEY_LISTEN_PORT: 0,
                ConverterBase.ASP_KEY_IS_LEARNING_MODE: True,
                ConverterBase.API_KEY_PROTECTION_MODE: ConverterBase.API_KEY_PROTECTION_MODE_MONITOR,
                ConverterBase.ASP_KEY_SECURITY_LEVEL_MIN: ['bot', 'crack', 'waf']
            }

            site_strategy_used_list = WafConf().get_waf_site_strategy_used_list()
            for strategy_id, hosts in site_strategy_used_list.items():
                if "default.com:*" in hosts:
                    default_conf[ConverterBase.ASP_KEY_WAF_STRATEGY] = strategy_id
                    break

            default_asp_site_conf.update(default_conf)
            return convert_site_conf('default.com:*', default_asp_site_conf)

        # 转换所有站点的配置
        for site_id, asp_site_conf in asp_conf.items():
            api_conf_list.append(convert_site_conf(site_id, asp_site_conf))

        # 处理 default.com 站点
        if with_default and is_mirror_mode and open_all_ports and is_license_advanced_waf_enabled():
            api_conf_list.append(build_default_com_asp_conf())

        return {'sites': api_conf_list}

    def _get_default_site_asp_conf(self):
        # using default asp site conf as base
        asp_site_conf = copy.deepcopy(self.default_site_asp_conf)

        # DAP-11408: enable_X_Forwarded_For and enable_X_Real_IP default value depend on global src_ip_from configure
        global_src_ip_strategy_list = get_config(self.NGINX_GLOBAL_SRC_IP_STRATEGY_LIST_PATH)
        if global_src_ip_strategy_list is None:
            # For upgrade
            src_ip_from = get_config('nginx/src_ip_from', '')
            xff_position = get_config('nginx/xff_position', 'last')
            if src_ip_from == '':
                src_ip_from = '__SRCIP_TCPIP__'
            src_ip_from_type, src_ip_customed_name = (src_ip_from, '') if src_ip_from in ('__SRCIP_TCPIP__', 'X-Forwarded-For', 'X-Real-IP') else ('Custom', src_ip_from)
            global_src_ip_strategy_list = [{'src_ip_from_type': src_ip_from_type, 'src_ip_customed_name': src_ip_customed_name, 'xff_position': xff_position}]

        # Only 10 allowed
        global_src_ip_strategy_list = global_src_ip_strategy_list[:10] if isinstance(global_src_ip_strategy_list, list) else []

        for src_ip_strategy in global_src_ip_strategy_list:
            if src_ip_strategy.get("src_ip_from_type") in ('X-Forwarded-For', 'X-Real-IP'):
                asp_site_conf['enable_X_Forwarded_For'] = False
                asp_site_conf['enable_X_Real_IP'] = False
                break

        return asp_site_conf

    def construct_create_site_conf(self, api_site_conf):
        """
        Construct create site asp conf
        :return: site_id, asp_dict, Result, err_extra
        """

        ### Some default values special handle to be the same action with front end.
        def newSite_aspConfFieldsSpecialHandle(api_site_conf, asp_site_conf):
            # 0.current system has such conf
            asp_site_conf.update({'name': server_name})

            # 1.'mobile_config_version' will add one version when create new site.
            asp_site_conf.update({'mobile_config_version': asp_site_conf.get("mobile_config_version") + 1})

            # 2.'limit_except' are diffrent form with default values when create new site.
            asp_site_conf.update({'limit_except': [True, True, True, True, True, True, False, False, True, True, True, True, True, True, True, True]})

            # 3.'check_console_open' is false when the mode is debug.
            if get_manifest_info().get('is_debug'):
                asp_site_conf.update({'check_console_open_v2': False})

            # 4.'http2https_new_port' get from ListenPort when the protocol is https.
            if api_site_conf.get("protocol") == 'https':
                asp_site_conf.update({'http2https_new_port': str(api_site_conf.get("port"))})

            # https站点，如果不指定证书的时候，默认使用内置国际证书
            if api_site_conf.get(ConverterBase.API_KEY_PROTOCOL) == 'https' \
                    and ConverterBase.API_KEY_CERTIFICATE not in api_site_conf \
                    and api_site_conf.get(ConverterBase.API_KEY_ENABLE_INTERNATIONAL_CERT) != False \
                    and ConverterBase.API_KEY_GM_CERTIFICATE not in api_site_conf:
                asp_site_conf.update({
                    ConverterBase.ASP_KEY_USE_INTERNATIONAL_CERT: True,
                    ConverterBase.ASP_KEY_USE_BUILTIN_CERT: True
                })


            #上游 为域名的时候，强制关闭健康检查
            is_upstream_domain = False
            if ConverterBase.API_KEY_UPSTREAM in api_site_conf \
                    and ConverterBase.API_KEY_UPSTREAM_LIST in api_site_conf[ConverterBase.API_KEY_UPSTREAM]:
                for item in api_site_conf[ConverterBase.API_KEY_UPSTREAM][ConverterBase.API_KEY_UPSTREAM_LIST]:
                  if valid_Domain(item['ip']):
                      is_upstream_domain = True
                      break

            if is_upstream_domain:
                asp_upstream_dict = api_site_conf.get(ConverterBase.API_KEY_UPSTREAM, {})
                asp_health_check_dict = asp_upstream_dict.get(ConverterBase.API_KEY_UPSTREAM_HEALTH_CHECK, {})

                asp_health_check_new_dict= {
                    ConverterBase.API_KEY_HEALTH_CHECK_TYPE:
                        'disable',
                    ConverterBase.API_KEY_HEALTH_CHECK_INTERVAL:
                        int(asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_INTERVAL, 5)),
                    ConverterBase.API_KEY_HEALTH_CHECK_RETRY_TIMES:
                        int(asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_RETRY_TIMES, 3)),
                    ConverterBase.API_KEY_HEALTH_CHECK_TIMEOUTS:
                        int(asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_TIMEOUTS, 5)),
                    ConverterBase.API_KEY_HEALTH_CHECK_PATH:
                        asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_PATH, '/'),
                    ConverterBase.API_KEY_HEALTH_CHECK_UA:
                        asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_UA,
                                                  'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)'),
                    ConverterBase.API_KEY_HEALTH_CHECK_HTTP_MODE:
                        asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_HTTP_MODE, 'path_ua'),
                    ConverterBase.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER:
                        asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_HEADER,
                                                  'GET / HTTP/1.0\r\nUser-Agent:Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)'),
                    ConverterBase.API_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY:
                        asp_health_check_dict.get(ConverterBase.ASP_KEY_HEALTH_CHECK_HTTP_CUSTOM_REQUEST_BODY, ''),
                }
                asp_upstream_dict.update({ConverterBase.API_KEY_UPSTREAM_HEALTH_CHECK: asp_health_check_new_dict})
                api_site_conf.update({ConverterBase.API_KEY_UPSTREAM: asp_upstream_dict})


        site_id, server_name = self._create_site_id(api_site_conf)

        asp_site_conf = self.get_specified_site_asp_conf(site_id)
        if asp_site_conf:
            # site already exist
            return site_id, {}, Result.SITE_EXISTS, ''

        # using default asp site conf as base
        asp_site_conf = self._get_default_site_asp_conf()

        newSite_aspConfFieldsSpecialHandle(api_site_conf, asp_site_conf)
        asp_conf, res, err_extra = self._site_api_conf_to_asp_conf(site_id, api_site_conf, asp_site_conf)
        return site_id, asp_conf, res, err_extra

    def construct_modify_site_conf(self, site_id, api_site_conf):
        """
        Modify a site.
        """
        # get current configuration, and update it.
        asp_site_conf = self.get_specified_site_asp_conf(site_id)
        return self._site_api_conf_to_asp_conf(site_id, api_site_conf, asp_site_conf)

    def construct_batch_modify_site_conf(self, api_sites_conf):
        """
        Modify a site.
        """
        site_list = api_sites_conf[ConverterBase.API_KEY_SITE_LIST]
        api_site_conf = api_sites_conf[ConverterBase.API_KEY_SITE_CONFIG]
        asp_all_sites_conf = {}
        for site_id in site_list:
            # get current configuration, and update it.
            asp_site_conf = self.get_specified_site_asp_conf(site_id)
            asp_conf_one_site, res, err_extra = self._site_api_conf_to_asp_conf(site_id, api_site_conf, asp_site_conf)
            if res is not Result.SUCCESS:
                return asp_conf_one_site, res, err_extra

            asp_all_sites_conf.update(asp_conf_one_site)

        return asp_all_sites_conf, Result.SUCCESS, ''

    def construct_batch_create_site_conf(self, api_sites_conf):
        """
        Batch create sites.
        """
        sites = api_sites_conf.get('sites', [])
        asp_all_sites_conf = {}
        created_site_ids = []

        # Check for duplicate site_ids in the input
        site_id_counts = {}
        for i, site_conf in enumerate(sites):
            # create each site configuration
            site_id, asp_conf_one_site, res, err_extra = self.construct_create_site_conf(site_conf)
            if res is not Result.SUCCESS:
                error_msg = "Failed to batch create sites due to configuration error of site #{0} ({1})".format(i+1, site_conf.get('site', 'unknown'))
                return asp_conf_one_site, res, error_msg, []

            if site_id in site_id_counts:
                error_msg = "Duplicate sites detected: site #{0} and site #{1}".format(
                    site_id_counts[site_id] + 1, i + 1, site_id)
                return {}, Result.ARGUMENT_ERROR, error_msg, []
            site_id_counts[site_id] = i
            
            asp_all_sites_conf.update(asp_conf_one_site)
            created_site_ids.append(site_id)
        
        return asp_all_sites_conf, Result.SUCCESS, '', created_site_ids

    def construct_delete_site_conf(self, site_id):
        """
        When value of key is None, means the value will be replaced by '"_deleted": 1'
        """
        asp_conf = {
            self.NGINX_UPSTREAMS_PATH + site_id: None,
        }
        # update waf site strategy used list
        waf_conf = WafConf()
        waf_site_strategy_values = waf_conf.update_waf_site_strategy(site_strategy_id=None, server_name=site_id,
                                                                     action='delete')
        waf_strategy_values = WafStrategyCtrl().update_strategy_used_list(site_id, action='delete')
        if waf_site_strategy_values:
            asp_conf.update(**waf_site_strategy_values)
        if waf_strategy_values:
            asp_conf.update(**waf_strategy_values)
        return asp_conf


class ApiJsonValidator(object):
    create_site_schema_json = os.path.abspath(os.path.join(__file__, '../json_schema/create_site.json'))
    modify_site_schema_json = os.path.abspath(os.path.join(__file__, '../json_schema/modify_site.json'))
    batch_modify_site_schema_json = os.path.abspath(os.path.join(__file__, '../json_schema/batch_modify_site.json'))
    batch_create_site_schema_json = os.path.abspath(os.path.join(__file__, '../json_schema/batch_create_site.json'))

    is_mirror_mode = ConfDb().get_deploy_mode() == ConfDb.DEPLOY_MODE_MIRROR
    is_plugin_mode = ConfDb().get_deploy_mode() == ConfDb.DEPLOY_MODE_PLUGIN

    def __init__(self):
        # load schema
        with open(self.create_site_schema_json) as f:
            self.create_site_schema = json.loads(f.read())

        with open(self.modify_site_schema_json) as f:
            self.modify_site_schema = json.loads(f.read())

        with open(self.batch_modify_site_schema_json) as f:
            self.batch_modify_site_schema = json.loads(f.read())

        with open(self.batch_create_site_schema_json) as f:
            self.batch_create_site_schema = json.loads(f.read())

        schema_dir = os.path.dirname(self.create_site_schema_json)
        self.create_site_resolver = RefResolver(base_uri='file://' + schema_dir + '/', referrer=self.create_site_schema)
        self.modify_site_resolver = RefResolver(base_uri='file://' + schema_dir + '/', referrer=self.modify_site_schema)
        self.batch_modify_site_resolver = RefResolver(base_uri='file://' + schema_dir + '/', referrer=self.batch_modify_site_schema)
        self.batch_create_site_resolver = RefResolver(base_uri='file://' + schema_dir + '/', referrer=self.batch_create_site_schema)

        logging.debug('json schema were loaded...')

        self.re_hostname = re.compile(r'^(?=^.{3,128}$)[a-zA-Z][\-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][\-a-zA-Z0-9]{0,62})+$')

    @staticmethod
    def _will_primary_protection_be_enabled(api_site_conf, asp_site_conf=None):
        if ConverterBase.API_KEY_WEB_PRIMARY_STRATEGY in api_site_conf and \
                'enable' in api_site_conf[ConverterBase.API_KEY_WEB_PRIMARY_STRATEGY]:
            return api_site_conf[ConverterBase.API_KEY_WEB_PRIMARY_STRATEGY]['enable']
        elif asp_site_conf and ConverterBase.ASP_KEY_PROTECTED_LIST in asp_site_conf and \
                ConverterBase.ASP_KEY_PROTECTED_LIST_WEB_PRIMARY in asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST]:
            return asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST][ConverterBase.ASP_KEY_PROTECTED_LIST_WEB_PRIMARY]
        else:
            return False     # primary protection is disabled by default

    @staticmethod
    def _will_essential_protection_be_enabled(api_site_conf, asp_site_conf=None):
        if ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY in api_site_conf and \
                'enable' in api_site_conf[ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY]:
            return api_site_conf[ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY]['enable']
        elif asp_site_conf and ConverterBase.ASP_KEY_PROTECTED_LIST in asp_site_conf and \
                ConverterBase.ASP_KEY_PROTECTED_LIST_WEB_ESSENTIAL in asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST]:
            return asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST][ConverterBase.ASP_KEY_PROTECTED_LIST_WEB_ESSENTIAL]
        else:
            return False     # essential protection is disabled by default

    @staticmethod
    def _will_power_protection_be_enabled(api_site_conf, asp_site_conf=None):
        if not is_license_advanced_protection() and not is_debug():
            return False

        if ConverterBase.API_KEY_WEB_POWER_STRATEGY in api_site_conf and \
                'enable' in api_site_conf[ConverterBase.API_KEY_WEB_POWER_STRATEGY]:
            return api_site_conf[ConverterBase.API_KEY_WEB_POWER_STRATEGY]['enable']
        elif asp_site_conf and ConverterBase.ASP_KEY_PROTECTED_LIST in asp_site_conf and \
                ConverterBase.ASP_KEY_PROTECTED_LIST_WEB_POWER in asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST]:
            return asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST][ConverterBase.ASP_KEY_PROTECTED_LIST_WEB_POWER]
        else:
            return False

    @staticmethod
    def _will_waf_be_enabled(api_site_conf, asp_site_conf=None):
        if ConverterBase.API_KEY_WAF_STRATEGY in api_site_conf and \
                'enable' in api_site_conf[ConverterBase.API_KEY_WAF_STRATEGY]:
            return api_site_conf[ConverterBase.API_KEY_WAF_STRATEGY]['enable']
        elif asp_site_conf and ConverterBase.ASP_KEY_PROTECTED_LIST in asp_site_conf and \
                ConverterBase.ASP_KEY_PROTECTED_LIST_WAF in asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST]:
            return asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST][ConverterBase.ASP_KEY_PROTECTED_LIST_WAF]
        else:
            return False

    @staticmethod
    def _will_aiwaf_be_enabled(api_site_conf, asp_site_conf=None):
        if not is_license_advanced_waf_enabled() and not is_debug():
            return False

        if ConverterBase.API_KEY_AI_WAF_STRATEGY in api_site_conf and \
                'enable' in api_site_conf[ConverterBase.API_KEY_AI_WAF_STRATEGY]:
            return api_site_conf[ConverterBase.API_KEY_AI_WAF_STRATEGY]['enable']
        elif asp_site_conf and ConverterBase.ASP_KEY_PROTECTED_LIST in asp_site_conf and \
                ConverterBase.ASP_KEY_PROTECTED_LIST_AIWAF in asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST]:
            return asp_site_conf[ConverterBase.ASP_KEY_PROTECTED_LIST][ConverterBase.ASP_KEY_PROTECTED_LIST_AIWAF]
        else:
            return False

    @staticmethod
    def _will_mpp_be_enabled(api_site_conf, asp_site_conf=None):
        if not has_mpp_permission():
            return False

        result = {}
        # 获取之前的配置开关
        if asp_site_conf:
            protect_list = asp_site_conf.get(ConverterBase.ASP_KEY_PROTECTED_LIST, {})
            result[ConverterBase.API_KEY_WECHAT_STRATEGY] = protect_list.get(ConverterBase.ASP_KEY_WECHAT_MINI_PROTECTION, False)
            result[ConverterBase.API_KEY_ALIPAY_STRATEGY] = protect_list.get(ConverterBase.ASP_KEY_ALIPAY_MINI_PROTECTION, False)
            result[ConverterBase.API_KEY_MPAAS_MPP_STRATEGY] = protect_list.get(ConverterBase.ASP_KEY_MPAAS_MPP_PROTECTION, False)

        # 用当前的配置开关覆盖原来的开关状态
        if ConverterBase.API_KEY_WECHAT_STRATEGY in api_site_conf:
            result[ConverterBase.API_KEY_WECHAT_STRATEGY] = api_site_conf[ConverterBase.API_KEY_WECHAT_STRATEGY]['enable']
        if ConverterBase.API_KEY_ALIPAY_STRATEGY in api_site_conf:
            result[ConverterBase.API_KEY_ALIPAY_STRATEGY] = api_site_conf[ConverterBase.API_KEY_ALIPAY_STRATEGY]['enable']
        if ConverterBase.API_KEY_MPAAS_MPP_STRATEGY in api_site_conf:
            result[ConverterBase.API_KEY_MPAAS_MPP_STRATEGY] = api_site_conf[ConverterBase.API_KEY_MPAAS_MPP_STRATEGY]['enable']

        # 判断合并之后最终的开关状态
        return any(result.values())

    def _check_plugin_mode_mpp_conf(self, api_site_conf, mpp_field):
        if mpp_field not in api_site_conf:
            return True
        mpp_conf_val = api_site_conf[mpp_field]
        if 'enable_request_body_encrypt' in mpp_conf_val and mpp_conf_val['enable_request_body_encrypt']:
            return False
        if 'enable_response_body_encrypt' in mpp_conf_val and mpp_conf_val['enable_response_body_encrypt']:
            return False
        return True

    def _check_protection_mode(self, api_site_conf, asp_site_conf=None):
        mode = api_site_conf.get(ConverterBase.API_KEY_PROTECTION_MODE)
        if self.is_mirror_mode and mode and mode != ConverterBase.API_KEY_PROTECTION_MODE_MONITOR:
            raise Exception('The value of field protection_mode can only be "monitor" under current deployment.')

        if self.is_plugin_mode:
            # 插件部署模式
            error_field = None
            while True:
                # 1. 不支持https以及相关证书配置
                if api_site_conf.get(ConverterBase.API_KEY_PROTOCOL) == 'https':
                    error_field = ConverterBase.API_KEY_PROTOCOL
                    break
                if ConverterBase.API_KEY_CERTIFICATE in api_site_conf:
                    error_field = ConverterBase.API_KEY_CERTIFICATE
                    break
                if ConverterBase.API_KEY_CERTIFICATE_KEY in api_site_conf:
                    error_field = ConverterBase.API_KEY_CERTIFICATE_KEY
                    break
                if ConverterBase.API_KEY_ENABLE_INTERNATIONAL_CERT in api_site_conf:
                    error_field = ConverterBase.API_KEY_ENABLE_INTERNATIONAL_CERT
                    break
                if ConverterBase.API_KEY_GM_CERTIFICATE in api_site_conf:
                    error_field = ConverterBase.API_KEY_GM_CERTIFICATE
                    break

                # 2. 不支持配置非8080监听端口
                if api_site_conf.get(ConverterBase.API_KEY_PORT) != 8080:
                    error_field = ConverterBase.API_KEY_PORT
                    break

                # 3. 不支持配置上游服务器
                if ConverterBase.API_KEY_UPSTREAM in api_site_conf:
                    error_field = ConverterBase.API_KEY_UPSTREAM
                    break

                # 4. 不支持高级保护
                if ConverterBase.API_KEY_WEB_POWER_STRATEGY in api_site_conf:
                    error_field = ConverterBase.API_KEY_WEB_POWER_STRATEGY
                    break

                # 5. 不支持配置允许的HTTP请求方法
                if ConverterBase.API_KEY_ALLOWED_HTTP_METHODS in api_site_conf:
                    error_field = ConverterBase.API_KEY_ALLOWED_HTTP_METHODS
                    break

                # 6. 不支持配置对系统节点的健康检查
                if ConverterBase.API_KEY_SELF_HEALTH_CHECK in api_site_conf:
                    error_field = ConverterBase.API_KEY_SELF_HEALTH_CHECK
                    break

                # 7. 不支持配置内部资源路径
                if ConverterBase.API_KEY_INTERNAL_RES_PATH in api_site_conf:
                    error_field = ConverterBase.API_KEY_INTERNAL_RES_PATH
                    break

                # 8. 不支持小程序防护配置请求body加密和响应body加密
                if not self._check_plugin_mode_mpp_conf(api_site_conf, ConverterBase.API_KEY_WECHAT_STRATEGY):
                    error_field = ConverterBase.API_KEY_WECHAT_STRATEGY
                    break
                if not self._check_plugin_mode_mpp_conf(api_site_conf, ConverterBase.API_KEY_ALIPAY_STRATEGY):
                    error_field = ConverterBase.API_KEY_ALIPAY_STRATEGY
                    break
                if not self._check_plugin_mode_mpp_conf(api_site_conf, ConverterBase.API_KEY_MPAAS_MPP_STRATEGY):
                    error_field = ConverterBase.API_KEY_MPAAS_MPP_STRATEGY
                    break
                break
            if error_field:
                raise Exception('The value of field "{}" is incorrect under current deployment.'.format(error_field))

    def _check_protection_strategy(self, api_site_conf, asp_site_conf=None):

        strategy_list = [
            ConverterBase.API_KEY_WEB_PRIMARY_STRATEGY,
            ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY,
            ConverterBase.API_KEY_WEB_POWER_STRATEGY,
            # ConverterBase.API_KEY_MOBILE_STRATEGY,
            ConverterBase.API_KEY_WAF_STRATEGY,
            ConverterBase.API_KEY_AI_WAF_STRATEGY,
            ConverterBase.API_KEY_WECHAT_STRATEGY,
            ConverterBase.API_KEY_ALIPAY_STRATEGY,
            ConverterBase.API_KEY_MPAAS_MPP_STRATEGY
        ]

        if self.is_mirror_mode:
            # 镜像部署模式
            # 1. 仅允许 WAF 和 AI-WAF
            # 2. 允许不指定防护策略

            not_allowed_strategy = set(api_site_conf.keys()) & set(strategy_list) - set([ConverterBase.API_KEY_WAF_STRATEGY, ConverterBase.API_KEY_AI_WAF_STRATEGY])
            if len(not_allowed_strategy) > 0:
                raise Exception("The fields {} are not supported under current deployment.".format(not_allowed_strategy))

            if ConverterBase.API_KEY_AI_WAF_STRATEGY in api_site_conf \
                    and not is_license_advanced_waf_enabled() and not is_debug():
                raise Exception("The field ai_waf_strategy is invalid as your license does not support AI-WAF module.")

        elif is_license_primary_protection():
            # safeplus灵动版，不能开启标准保护和高级保护
            if ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY in api_site_conf or \
                    ConverterBase.API_KEY_WEB_POWER_STRATEGY in api_site_conf:
                raise Exception("Primary protection is enabled, cannot configure web_esstial_strategy and web_power_strategy")

            # 灵动版，必须选择动态，waf，AI-waf中的一个
            if not self._will_primary_protection_be_enabled(api_site_conf, asp_site_conf) and \
                    not self._will_waf_be_enabled(api_site_conf, asp_site_conf) and \
                    not self._will_aiwaf_be_enabled(api_site_conf, asp_site_conf):
                raise Exception("At least one protection strategy should be enabled: "
                                "'primary protection, waf or ai-waf'")

        else:
            # 不是灵动版本，不能配置初级保护
            if ConverterBase.API_KEY_WEB_PRIMARY_STRATEGY in api_site_conf:
                raise Exception("Primary protection is not enabled, cannot configure web_primary_strategy")

            # 1. license is not enabled, but try to configure
            if not is_license_advanced_protection() and not is_debug() \
                    and ConverterBase.API_KEY_WEB_POWER_STRATEGY in api_site_conf:
                raise Exception("Power protection is not enabled, cannot configure web_power_strategy")

            if not is_license_advanced_waf_enabled() and not is_debug() \
                    and ConverterBase.API_KEY_AI_WAF_STRATEGY in api_site_conf:
                raise Exception("AI-WAF is not enabled, cannot configure ai_waf_strategy")

            # 2. At least enable one protection strategy: essential protection, power protection, waf, ai-waf
            # 3. Please also enable essential protection mode when you want to enable power protection mode.
            if not self._will_essential_protection_be_enabled(api_site_conf, asp_site_conf):
                if self._will_power_protection_be_enabled(api_site_conf, asp_site_conf):
                    raise Exception("Essential protection mode should be enabled when power protection mode is enabled.")

                elif not self._will_waf_be_enabled(api_site_conf, asp_site_conf) and \
                        not self._will_aiwaf_be_enabled(api_site_conf, asp_site_conf) and \
                        not self._will_mpp_be_enabled(api_site_conf, asp_site_conf):
                    raise Exception("At least one protection strategy should be enabled: "
                                    "'essential protection, power protection, waf/ai-waf or mpp'")

        # 验证各个保护策略
        if ConverterBase.API_KEY_WEB_ESSENTIAL_STRATEGY in api_site_conf:
            self._validate_essential_strategy(api_site_conf, asp_site_conf)
        if ConverterBase.API_KEY_WEB_POWER_STRATEGY in api_site_conf:
            self._validate_power_strategy(api_site_conf, asp_site_conf)
        if ConverterBase.API_KEY_WECHAT_STRATEGY in api_site_conf or ConverterBase.API_KEY_ALIPAY_STRATEGY in api_site_conf or ConverterBase.API_KEY_MPAAS_MPP_STRATEGY in api_site_conf:
            self._validate_mpp_strategy(api_site_conf, asp_site_conf)
            
        # WAF策略和权限验证
        self._validate_waf_strategy_and_permission(api_site_conf, asp_site_conf)

    def _is_valid_mask(self, ip, mask):
        try:
            network_addr = IPy.IP(ip).make_net(mask)
            print(network_addr)
            return True
        except Exception as e:
            logging.info(str(e.message))
            return False

    def _is_valid_segment(self, start, end):
        try:
            start_int = IPy.IP(start).int()
            end_int = IPy.IP(end).int()
            if start_int <= end_int:
                return True
            else:
                return False
        except Exception as e:
            logging.info(str(e.message))
            return False

    def _check_ip_strategy(self, api_site_conf):
        if ConverterBase.API_KEY_IP_STRATEGY in api_site_conf:
            if 'type' in api_site_conf[ConverterBase.API_KEY_IP_STRATEGY]:
                ip_strategy_type = api_site_conf[ConverterBase.API_KEY_IP_STRATEGY]['type']
                if ip_strategy_type == ConverterBase.API_KEY_PROTECT_IP_WHITE_LIST:
                    if ConverterBase.API_KEY_PROTECT_IP_WHITE_LIST not in api_site_conf[ConverterBase.API_KEY_IP_STRATEGY]:
                        raise Exception("'ip_white_list' should be configured when 'ip_strategy' 'type' is 'ip_white_list'")
                    elif len(api_site_conf[ConverterBase.API_KEY_IP_STRATEGY][ConverterBase.API_KEY_PROTECT_IP_WHITE_LIST]) == 0:
                        raise Exception("'ip_white_list' cannot be empty when 'ip_strategy' 'type' is 'ip_white_list'")

                elif ip_strategy_type == ConverterBase.API_KEY_PROTECT_IP_PROTECTION_LIST:
                    if ConverterBase.API_KEY_PROTECT_IP_PROTECTION_LIST not in api_site_conf[ConverterBase.API_KEY_IP_STRATEGY]:
                        raise Exception("'ip_protection_list' should be configured when 'ip_strategy' 'type' is 'ip_protection_list'")
                    elif len(api_site_conf[ConverterBase.API_KEY_IP_STRATEGY][ConverterBase.API_KEY_PROTECT_IP_PROTECTION_LIST]) == 0:
                        raise Exception("'ip_protection_list' cannot be empty when 'ip_strategy' 'type' is 'ip_protection_list'")

            # check ip or mask
            ip_white_or_protection_list = []
            if ConverterBase.API_KEY_PROTECT_IP_WHITE_LIST in api_site_conf[ConverterBase.API_KEY_IP_STRATEGY]:
                ip_white_or_protection_list.extend(api_site_conf[ConverterBase.API_KEY_IP_STRATEGY][ConverterBase.API_KEY_PROTECT_IP_WHITE_LIST])

            if ConverterBase.API_KEY_PROTECT_IP_PROTECTION_LIST in api_site_conf[ConverterBase.API_KEY_IP_STRATEGY]:
                ip_white_or_protection_list.extend(
                    api_site_conf[ConverterBase.API_KEY_IP_STRATEGY][ConverterBase.API_KEY_PROTECT_IP_PROTECTION_LIST])

            for item in ip_white_or_protection_list:
                if item[3] == 'mask' and not self._is_valid_mask(item[0], item[1]):
                    raise Exception("{} is not a valid mask, {}".format(item[1], str(item)))
                elif item[3] == 'segment' and not self._is_valid_segment(item[0], item[1]):
                    raise Exception("{} is bigger than {}, {}".format(item[0], item[1], str(item)))

    def _get_upstream_health_check_type(self, api_site_conf, asp_site_conf=None):
        if ConverterBase.API_KEY_UPSTREAM in api_site_conf \
                and ConverterBase.API_KEY_UPSTREAM_HEALTH_CHECK in api_site_conf[ConverterBase.API_KEY_UPSTREAM] \
                and ConverterBase.API_KEY_HEALTH_CHECK_TYPE in api_site_conf[ConverterBase.API_KEY_UPSTREAM][ConverterBase.API_KEY_UPSTREAM_HEALTH_CHECK]:
            return api_site_conf[ConverterBase.API_KEY_UPSTREAM][ConverterBase.API_KEY_UPSTREAM_HEALTH_CHECK][ConverterBase.API_KEY_HEALTH_CHECK_TYPE]
        elif asp_site_conf and ConverterBase.ASP_KEY_HEALTH_CHECK in asp_site_conf:
            asp_health_check_module = asp_site_conf[ConverterBase.ASP_KEY_HEALTH_CHECK]
            return 'disable' if not asp_health_check_module.get(ConverterBase.ASP_KEY_IS_HEALTH_CHECK_ENABLE, False) \
                else asp_health_check_module.get(ConverterBase.ASP_KEY_HEALTH_CHECK_TYPE, 'tcp')
        else:
            return 'disable'

    def _get_upstream_protocol(self, api_site_conf, asp_site_conf=None):
        if ConverterBase.API_KEY_UPSTREAM in api_site_conf and 'protocol' in api_site_conf[ConverterBase.API_KEY_UPSTREAM]:
            return api_site_conf[ConverterBase.API_KEY_UPSTREAM]['protocol']
        elif asp_site_conf:
            return 'https' if asp_site_conf.get(ConverterBase.ASP_KEY_IS_UPSTREAM_HTTPS, False) else 'http'
        else:
            return 'http'

    def _get_upstream_ip_list(self, api_site_conf, asp_site_conf):
        upstream_ip_list = []
        if ConverterBase.API_KEY_UPSTREAM in api_site_conf \
                and ConverterBase.API_KEY_UPSTREAM_LIST in api_site_conf[ConverterBase.API_KEY_UPSTREAM]:
            for item in api_site_conf[ConverterBase.API_KEY_UPSTREAM][ConverterBase.API_KEY_UPSTREAM_LIST]:
                upstream_ip_list.append(item['ip'])
        elif asp_site_conf:
            for item in asp_site_conf.get(ConverterBase.ASP_KEY_UPSTREAM_LIST, []):
                upstream_ip_list.append(item[0])        # item 0 is ip
        return upstream_ip_list

    def _check_upstream(self, api_site_conf, asp_site_conf=None):
        if self.is_plugin_mode:
            # 插件部署模式
            # 不支持配置上游
            return

        upstreams = api_site_conf.get(ConverterBase.API_KEY_UPSTREAM, {}).get(ConverterBase.API_KEY_UPSTREAM_LIST, [])
        is_create_site = asp_site_conf == None

        if self.is_mirror_mode:
            # 镜像部署:
            # 1. 仅需提供上游服务器IP地址，或者CIDR格式的地址
            # 2. 新建http站点可以为空，https站点必须上游

            is_https = api_site_conf.get(ConverterBase.API_KEY_PROTOCOL) == 'https' \
                            or (asp_site_conf and asp_site_conf.get(ConverterBase.ASP_KEY_IS_HTTPS))
            if is_https and len(upstreams) == 0 and is_create_site:
                raise Exception("The field upstream is required for HTTPS websites.")

            for upstream in upstreams:
                ip = upstream.get('ip')
                if not valid_IP_or_CIDR(ip):
                    raise Exception("Invalid upstream IP(s) or network segment(s).")

            return

        # 下面是非镜像部署校验逻辑

        # 1. at least one upstream should be enabled.
        if not upstreams and is_create_site:
            raise Exception("The field upstream is not found or is invalid.")

        upstream_enabled = False
        for item in upstreams:
            if not ('ip' in item and 'port' in item):
                raise Exception("The field upstream is not found or is invalid.")

            ip = item['ip']
            if not (valid_IP(ip) or valid_Domain(ip)):
                raise Exception("The field upstream is not found or is invalid.")

            # at least one upstream should be enabled.
            if 'enable' not in item or item['enable']:
                upstream_enabled = True

            ret, err = is_supported_upstream_addr(item['ip'])
            if not ret:
                raise Exception(err)

            if 'weight' in item and not str(item['weight']).isdigit():
                raise Exception('weight are only allowed to be positive integer')

        if ConverterBase.API_KEY_UPSTREAM in api_site_conf \
                and ConverterBase.API_KEY_UPSTREAM_LIST in api_site_conf[ConverterBase.API_KEY_UPSTREAM] \
                and not upstream_enabled :
            raise Exception("At least one upstream should be enabled.")

        # 2. health check cannot be http when upstream protocol is https
        # 3. when any of the upstream ip is domain, health check should be disabled.
        health_check_type = self._get_upstream_health_check_type(api_site_conf, asp_site_conf)
        if health_check_type == 'http' or health_check_type == 'tcp':
            if health_check_type == 'http' and self._get_upstream_protocol(api_site_conf, asp_site_conf) == 'https':
                raise Exception("health_check 'type' cannot be 'http' when upstream protocol is 'https'.")

            upstream_ip_list = self._get_upstream_ip_list(api_site_conf, asp_site_conf)
            for upstream_ip in upstream_ip_list:
                if self.re_hostname.match(upstream_ip):
                    raise(Exception("health_check 'type' cannot be 'tcp' or 'http' when any upstream 'ip' is domain."))

    @staticmethod
    def _check_site_name_regex(api_site_conf):
        if api_site_conf[ConverterBase.API_KEY_SITE_NAME_TYPE] == ConverterBase.API_KEY_SITE_NAME_TYPE_REGEX:
            site_name = api_site_conf[ConverterBase.API_KEY_SITE_NAME]
            res, msg = is_valid_regex(site_name)
            if not res:
                raise Exception(msg)

    @staticmethod
    def _check_business_path_regex(api_site_conf):
        enable = api_site_conf.get(ConverterBase.API_KEY_ENABLE_BUSINESS_PATH, False)
        path = api_site_conf.get(ConverterBase.API_KEY_BUSINESS_PATH, '')
        if enable and path:
            res, msg = is_valid_regex(path)
            if not res:
                raise Exception('business path ' + msg)

    def _check_terminal_setting(self, api_site_conf):
        if ConverterBase.API_KEY_TERMINAL_SETTING in api_site_conf \
                and 'enable' in api_site_conf[ConverterBase.API_KEY_TERMINAL_SETTING] \
                and api_site_conf[ConverterBase.API_KEY_TERMINAL_SETTING]['enable']:
            if 'protocol' not in api_site_conf[ConverterBase.API_KEY_TERMINAL_SETTING] \
                    or 'port' not in api_site_conf[ConverterBase.API_KEY_TERMINAL_SETTING]:
                raise Exception("'protocol' and 'port' should be configured when 'terminal_setting' is enabled.")

    @classmethod
    def _check_cert_fields(cls, api_site_conf, asp_site_conf=None):
        if asp_site_conf is None:
            protocol = api_site_conf[ConverterBase.API_KEY_PROTOCOL]
        else:
            protocol = 'https' if asp_site_conf.get("IsHttps") else 'http'

        if protocol == 'http':
            # http cannot contains cert
            if ConverterBase.API_KEY_CERTIFICATE in api_site_conf \
                    or ConverterBase.API_KEY_CERTIFICATE_KEY in api_site_conf:
                raise Exception('protocol http cannot contains certificate or certificate_key')
            return

        # https

        if cls.is_mirror_mode:
            # 镜像部署，不支持使用内置证书，需要上传私钥文件，且不支持国密
            certificate_key = api_site_conf.get(ConverterBase.API_KEY_CERTIFICATE_KEY)
            if certificate_key == None:
                raise Exception("The field certificate_key is required.")
            cls._check_certificate_key(certificate_key)
            return

        # 检查国际证书有效性
        if ConverterBase.API_KEY_CERTIFICATE in api_site_conf \
                and ConverterBase.API_KEY_CERTIFICATE_KEY in api_site_conf:
            # OK, check cert
            cls._check_certificate(api_site_conf[ConverterBase.API_KEY_CERTIFICATE], api_site_conf[ConverterBase.API_KEY_CERTIFICATE_KEY])
        elif ConverterBase.API_KEY_CERTIFICATE not in api_site_conf \
                and ConverterBase.API_KEY_CERTIFICATE_KEY not in api_site_conf:
            # OK, do nothing
            pass
        else:
            # pass only one is not allowed
            raise Exception('The fields certificate and certificate_key should be included in requests in pairs.')

        # 检查国密证书有效性
        if ConverterBase.API_KEY_GM_CERTIFICATE in api_site_conf:
            if not has_gm_permission():
                raise Exception("The field gm_certificate is invalid as your license does not support GM Algorithm module.")

            if api_site_conf[ConverterBase.API_KEY_GM_CERTIFICATE]['enable'] == True \
                    and api_site_conf[ConverterBase.API_KEY_GM_CERTIFICATE]['type'] == 'upload':
                gm_certificate = api_site_conf[ConverterBase.API_KEY_GM_CERTIFICATE]
                certificate = gm_certificate[ConverterBase.API_KEY_CERTIFICATE]
                certificate_key = gm_certificate[ConverterBase.API_KEY_CERTIFICATE_KEY]
                enc_certificate = gm_certificate[ConverterBase.API_KEY_GM_ENC_CERTIFICATE]
                enc_certificate_key = gm_certificate[ConverterBase.API_KEY_GM_ENC_CERTIFICATE_KEY]

                cls._check_certificate(certificate, certificate_key)
                cls._check_certificate(enc_certificate, enc_certificate_key)

        if asp_site_conf:
            # 修改站点，必须保证修改后至少存在一组证书配置
            if ConverterBase.API_KEY_ENABLE_INTERNATIONAL_CERT in api_site_conf:
                enable_international_cert = api_site_conf[ConverterBase.API_KEY_ENABLE_INTERNATIONAL_CERT]
            else:
                # 隐式声明，提供了certificate时，隐含启用了国际证书（与老版本API行为保持一致）
                enable_international_cert = ConverterBase.API_KEY_CERTIFICATE in api_site_conf or asp_site_conf.get(ConverterBase.ASP_KEY_USE_INTERNATIONAL_CERT)

            enable_gm_cert = api_site_conf[ConverterBase.API_KEY_GM_CERTIFICATE]['enable'] if ConverterBase.API_KEY_GM_CERTIFICATE in api_site_conf else asp_site_conf.get(ConverterBase.ASP_KEY_USE_GM_CERT)

            if not (enable_international_cert or enable_gm_cert):
                raise Exception("Either the field enable_international_cert or gm_certificate needs to be enabled.")
        else:
            # 新建站点，允许证书都不带，但是不允许显示把两个证书都 Disable 掉
            enable_international_cert = api_site_conf.get(ConverterBase.API_KEY_ENABLE_INTERNATIONAL_CERT, True)
            enable_gm_cert = ConverterBase.API_KEY_GM_CERTIFICATE in api_site_conf and api_site_conf[ConverterBase.API_KEY_GM_CERTIFICATE]['enable']
            if enable_international_cert == False and enable_gm_cert == False:
                raise Exception("Either the field enable_international_cert or gm_certificate needs to be enabled.")


    @staticmethod
    def _check_certificate(cert, key):
        """
        check certificate format
        :param cert: bytes of certificate
        :param key: bytes of certificate_key
        :raise: ValueError
        :return: cert, key
        """
        if cert == '' and key == '':
            return '', ''

        try:
            cert = base64.b64decode(cert)
        except TypeError as e:
            raise ValueError("certificate is not base64 format: %s" % e.message)

        try:
            key = base64.b64decode(key)
        except TypeError as e:
            raise ValueError("certificate_key is not base64 format: %s" % e.message)

        if cert.find('--BEGIN CERTIFICATE--') < 0:
            raise ValueError("certificate format error")

        if key.find('PRIVATE KEY--') < 0:
            raise ValueError("certificate_key format error")

        return cert, key

    @staticmethod
    def _check_certificate_key(key):
        try:
            key = base64.b64decode(key)
        except TypeError as e:
            raise ValueError("certificate_key is not base64 format: %s" % e.message)

        if key.find('PRIVATE KEY--') < 0:
            raise ValueError("certificate_key format error")

        return key

    def _check_src_ip_strategy(self, api_site_conf):
        """
        1. 自定义头可以添加多个
        2. __SRCIP_TCPIP__ 最多出现一次
        3. X-Real-IP 最多出现一次
        4. X-Forwarded-For 最多出现三次（first/penultimate/last）
        """
        rules = []
        for item in api_site_conf.get(ConverterBase.API_KEY_SRC_IP_STRATEGY, {}).get(ConverterBase.API_KEY_SRC_IP_STRATEGY_LIST, []):
            tmp = []
            from_type = item['src_ip_from_type']
            tmp.append(from_type)
            if from_type == 'Custom':
                tmp.append(item['src_ip_customed_name'])
            elif from_type == 'X-Forwarded-For':
                tmp.append(item['xff_position'])
            rules.append(tuple(tmp))

        if len(rules) != len(set(rules)):
            raise ValueError("Duplicates are found in the field src_ip_strategy.")

    def _common_validity(self, api_site_conf, asp_site_conf=None):

        self._check_protection_mode(api_site_conf, asp_site_conf)

        # 1. at least one upstream should be enabled, 2. health check validity
        self._check_upstream(api_site_conf, asp_site_conf)

        # 1. advanced license, 2. any strategy enabled, 3. essential should be enabled when power strategy enabled
        self._check_protection_strategy(api_site_conf, asp_site_conf)

        # mask and ip range validity
        self._check_ip_strategy(api_site_conf)

        # terminal protocol and port need to be configured when terminal setting enabled
        self._check_terminal_setting(api_site_conf)

        # http site cannot pass cert and key
        self._check_cert_fields(api_site_conf, asp_site_conf)

        self._check_src_ip_strategy(api_site_conf)

    def _check_port_available(self, api_site_conf):
        listen_port = str(api_site_conf[ConverterBase.API_KEY_PORT])
        confdb = ConfDb()
        if is_port_already_in_use(listen_port, confdb):
            raise Exception('Port {0} is used already. Please change the port number or try again later.'.format(listen_port))

        all_upstreams = confdb.get_all('nginx/upstreams/')
        http_ports = set([])
        https_ports = set([])
        for _, site_conf in all_upstreams.items():
            if site_conf.get('IsHttps'):
                https_ports.add(site_conf.get('ListenPort'))
                if site_conf.get('enable_http2https'):
                    http_ports.add(site_conf.get('http2https_org_port'))
            else:
                http_ports.add(site_conf.get('ListenPort'))

        # external port should not conflict with admin port
        http_ports |= {'20146'}
        https_ports |= {confdb.get_value('nginx/web_console/port')}
        http_ports -= {None}
        https_ports -= {None}

        is_https = api_site_conf.get(ConverterBase.API_KEY_PROTOCOL) == 'https'
        error = False
        if is_https:
            if listen_port in http_ports:
                error = True
        else:
            if listen_port in https_ports:
                error = True

        if error:
            raise Exception('Sites that use different protocol (http and https) are not allowed to listen on the same port.')

    def check_create_site_json(self, api_site_conf):
        """
        json body check
        """
        # jsonschema check
        try:
            validate(instance=api_site_conf,
                    schema=self.create_site_schema,
                    resolver=self.create_site_resolver,
                    format_checker=draft7_format_checker)
        except Exception as e:
            logging.error(e.message)
            raise Exception("submit data is invalid")

        self._common_validity(api_site_conf)

        # check port is available
        self._check_port_available(api_site_conf)

        # check if the regex is correct, jsonschema is not enough
        self._check_site_name_regex(api_site_conf)

        self._check_business_path_regex(api_site_conf)

        # port cannot be webconsole port, 201xx will be checked by jsonschema
        webconsole_port = ProtectedSitesAdapter.get_webconsole_port()
        if int(webconsole_port) == api_site_conf[ConverterBase.API_KEY_PORT]:
            raise Exception("port: {}, it cannot be same as webconsole port.".format(webconsole_port))

    def check_modify_site_json(self, site_id, api_site_conf):
        """
        json body check
        """
        # jsonschema check
        try:
            validate(instance=api_site_conf,
                    schema=self.modify_site_schema,
                    resolver=self.modify_site_resolver,
                    format_checker=draft7_format_checker)
        except Exception as e:
            logging.error(e.message)
            raise Exception("submit data is invalid")

        # check key items which cannot be modified
        for k in ProtectedSitesAdapter.CANNOT_BE_MODIFIED_API_ITMES:
            if k in api_site_conf:
                raise Exception('{} cannot be modified after site created.'.format(k))

        asp_site_conf = ProtectedSitesAdapter.get_specified_site_asp_conf(site_id)
        if asp_site_conf:
            self._common_validity(api_site_conf, asp_site_conf)
        else:
            raise Exception('Site {} not exists.'.format(site_id))

    def check_batch_modify_site_json(self, api_body):
        """
        json body check
        """
        # jsonschema check
        try:
            validate(instance=api_body,
                    schema=self.batch_modify_site_schema,
                    resolver=self.batch_modify_site_resolver,
                    format_checker=draft7_format_checker)
        except Exception as e:
            logging.error(e.message)
            raise Exception("submit data is invalid")

        site_list = api_body[ConverterBase.API_KEY_SITE_LIST]
        api_site_conf = api_body[ConverterBase.API_KEY_SITE_CONFIG]

        # check key items which cannot be modified
        for k in ProtectedSitesAdapter.CANNOT_BE_MODIFIED_API_ITMES:
            if k in api_site_conf:
                raise Exception('{} cannot be modified after site created.'.format(k))

        for site_id in site_list:
            asp_site_conf = ProtectedSitesAdapter.get_specified_site_asp_conf(site_id)
            if asp_site_conf:
                self._common_validity(api_site_conf, asp_site_conf)
            else:
                raise Exception('Site {} not exists.'.format(site_id))

    def check_batch_create_site_json(self, api_body):
        """
        json body check for batch create sites
        """
        # jsonschema check
        try:
            validate(instance=api_body,
                    schema=self.batch_create_site_schema,
                    resolver=self.batch_create_site_resolver,
                    format_checker=draft7_format_checker)
        except Exception as e:
            logging.error(e.message)
            raise Exception("submit data is invalid")

        sites = api_body.get('sites', [])
        
        # validate each site configuration
        for site_conf in sites:
            self.check_create_site_json(site_conf)


    def _validate_essential_strategy(self, api_site_conf, asp_site_conf):
        """
        标准保护请求/响应白名单有不同的类型：
            正则
            路径后缀名
            路径开头
            路径结尾
            路径包含
            请求头User-Agent包含
        当为正则时，URL最大长度为6000，其他情况为1024
        当为后缀名时，多个后缀名用都好分割，每个后缀名长度不超过10字符
        """
        URL_AS_REGEX = 0
        URL_AS_SUFFIX_NAME = 1
        strategy = api_site_conf.get('web_essential_strategy', {})

        def check_suffix(suffixes):
            for suffix in suffixes.split(','):
                suffix_len = len(suffix)
                if suffix_len == 0 or suffix_len > 10:
                    raise Exception("The length of each suffix cannot be empty or exceed 10 characters.")
                if not re.match('^[a-zA-Z0-9]+$', suffix):
                    raise Exception("The suffix name only consists of letters and numbers.")

        for item in strategy.get('request_white_list', []):
            selected_type =  item[4] if len(item) == 5 else 0
            max_url_length = 6000 if selected_type == URL_AS_REGEX else 1024
            if len(item[0]) > max_url_length:
                raise Exception("The URL length of {} in web_essential_strategy can't exceed {}".format('request_white_list', max_url_length))

            if selected_type == URL_AS_SUFFIX_NAME:
                check_suffix(item[0])

        for item in strategy.get('response_white_list', []):
            selected_type = item[3] if len(item) == 4 else 0
            max_url_length = 6000 if selected_type == URL_AS_REGEX else 1024
            if len(item[0]) > max_url_length:
                raise Exception("The URL length of {} in web_essential_strategy can't exceed {}".format('response_white_list', max_url_length))

            if selected_type == URL_AS_SUFFIX_NAME:
                check_suffix(item[0])

        for field in ['ajax_token_bypass_list', 'ajax_token_path_list']:
            for item in strategy.get(field, []):
                if len(item[0]) > 256:
                    raise Exception("The URL length of {} in web_essential_strategy can't exceed {}".format(field, 256))

    def _validate_power_strategy(self, api_site_conf, asp_site_conf):
        strategy = api_site_conf.get('web_power_strategy', {})
        protected_list = {}
        if asp_site_conf:
            protected_list.update(asp_site_conf.get(ConverterBase.ASP_KEY_PROTECTED_LIST, {}))

        if ConverterBase.API_KEY_ENABLE_AJAX_REQ_BODY_ENC in strategy:
            protected_list[ConverterBase.ASP_KEY_PROTECTED_LIST_AJAX_REQ_BODY_ENC] = strategy[ConverterBase.API_KEY_ENABLE_AJAX_REQ_BODY_ENC]

        if ConverterBase.API_KEY_ENABLE_AJAX_RESP_BODY_ENC in strategy:
            protected_list[ConverterBase.ASP_KEY_PROTECTED_LIST_AJAX_RESP_BODY_ENC] = strategy[ConverterBase.API_KEY_ENABLE_AJAX_RESP_BODY_ENC]

        if ConverterBase.API_KEY_AJAX_REQ_BODY_ENC_LIST in strategy and not protected_list.get(ConverterBase.ASP_KEY_PROTECTED_LIST_AJAX_REQ_BODY_ENC):
            raise Exception("The {} value must be true when {} exist".format(ConverterBase.API_KEY_ENABLE_AJAX_REQ_BODY_ENC, ConverterBase.API_KEY_AJAX_REQ_BODY_ENC_LIST))

        if (ConverterBase.API_KEY_AJAX_RESP_BODY_ENC_LIST in strategy or ConverterBase.API_KEY_AJAX_REFERER_LIST in strategy) \
                and not protected_list.get(ConverterBase.ASP_KEY_PROTECTED_LIST_AJAX_RESP_BODY_ENC):
            raise Exception("The {} value must be true when {} / {} exist".format(ConverterBase.API_KEY_ENABLE_AJAX_RESP_BODY_ENC, ConverterBase.API_KEY_AJAX_RESP_BODY_ENC_LIST, ConverterBase.API_KEY_AJAX_REFERER_LIST))

        for field in ['verification_list', 'encapsulation_list', 'ajax_req_body_enc_list', 'ajax_referer_list', 'ajax_resp_body_enc_list']:
            for item in strategy.get(field, []):
                if len(item[0]) > 1024:
                    raise Exception("The URL length of {} in web_power_strategy can't exceed {}".format(field, 1024))

    def _validate_mpp_strategy(self, api_site_conf, asp_site_conf):
        field = 'request_white_list'
        for strategy in ['wechat_strategy', 'alipay_strategy', 'mpaas_mpp_strategy']:
            for item in api_site_conf.get(strategy, {}).get(field, []):
                if len(item[0]) > 1024:
                    raise Exception("The URL length of {} in {} can't exceed {}".format(field, strategy, 1024))

    def _validate_waf_strategy_and_permission(self, api_site_conf, asp_site_conf=None):
        """
        验证WAF策略字段和WAF权限的完整性
        1. 验证WAF策略字段的有效性
        2. 验证WAF权限是否匹配当前许可
        3. 验证策略类型是否有效
        """
        if ConverterBase.API_KEY_WAF_STRATEGY not in api_site_conf:
            return
            
        waf_strategy = api_site_conf[ConverterBase.API_KEY_WAF_STRATEGY]
        enable = waf_strategy.get('enable', False)
        
        # 检查WAF权限
        if enable and not is_license_advanced_waf_enabled() and not is_debug():
            raise Exception("The field waf_strategy is invalid as your license does not support WAF module.")
