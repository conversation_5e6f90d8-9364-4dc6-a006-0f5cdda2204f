# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2018-10-24 10:24+0800\n"
"PO-Revision-Date: 2018-10-24 10:24+0800\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

msgid ""
" (An integer: 0 - 10. It is the number of previous passwords not allowed to "
"use. 0 means no constraint.)"
msgstr ""

msgid " (An integral number: "
msgstr "(An integer: "

#, python-brace-format
msgid " ({0} selected)"
msgstr ""

msgid " Cannot upgrade. "
msgstr "You cannot update the system."

msgid " Cannot upload. "
msgstr "You cannot upload the model."

msgid " GB"
msgstr ""

msgid " If you have any question, please contact customer service."
msgstr ""

msgid ""
" Please click \"Protection\" in the top navigation bar of the WebConsole "
"page and then click \"Add Websites\"."
msgstr ""

msgid ""
" Please click \"Save All\" at the bottom of the page to enable protection "
"over the whole website."
msgstr ""

msgid ""
" Please enter domain name and server IP respectively in \"Website\" and "
"\"Server\" (clicking \"Add\" in the page can add more servers)."
msgstr ""

msgid ""
" Please enter port number respectively for \"Website\" and \"Server\". By "
"default, the port number for HTTP is 80 and HTTPS is 443."
msgstr ""

msgid ""
" Please select a communication protocol for \"Website\" and \"Server\" "
"respectively."
msgstr ""

msgid " Wrong password! Please try again."
msgstr ""

msgid ""
" You cannot enjoy any update or maintenance services beyond the expiration "
"date."
msgstr ""
" No upgrade or maintenance services are available beyond the expiration "
"date."

msgid " You cannot enjoy any update or maintenance services now."
msgstr " No upgrade or maintenance services are available now."

msgid " Your system is working under Transparent Mode."
msgstr " Your system is working in Pass-Through Mode."

msgid ""
" Your system will work under Transparent Mode beyond the expiration date."
msgstr ""
" Your system will work in Pass-Through Mode beyond the expiration date."

msgid " Your system will work under Transparent Mode then."
msgstr " Your system will work in Pass-Through Mode then."

msgid " are not allowed here."
msgstr ""

msgid " expired."
msgstr " has expired. "

msgid " in CSRF Path List. "
msgstr ""

msgid " in Cache path. "
msgstr ""

msgid " in HTTP Request Headers. "
msgstr ""

msgid " in Learning List"
msgstr "Learning Paths: "

msgid " in Not Learning List"
msgstr "Excluded Paths: "

msgid " in WAF White List. "
msgstr " in WAF Whitelist. "

msgid " is not in effect."
msgstr ""

#, python-format
msgid "\"%(path)s\" does not exist"
msgstr ""

#, python-brace-format
msgid "\"{0}\" is the file currently being used"
msgstr ""

#, python-format
msgid "%(ip)s prefix length supports %(min)s ~ %(max)s"
msgstr "%(ip)s prefix length: %(min)s ~ %(max)s"

#, python-format
msgid ""
"%(startTag)sWhen a Dynamic Application Protection node is enabled with a "
"Traffic Self-Learning role, it can generate a recommended protection list by"
" learning data flows. %(endTag)sTo ensure a comprehensive learning result, "
"please enable Traffic Self-Learning role on all Dynamic Application "
"Protection nodes."
msgstr ""

msgid "' < > { } and space"
msgstr ""

msgid "' and space"
msgstr ""

#, python-format
msgid "(Amount: %(tag1)s / %(tag2)s)"
msgstr ""

msgid "(Customized)"
msgstr "(Custom)"

msgid "(Not recommended)"
msgstr ""

msgid ")"
msgstr ""

msgid ","
msgstr ", "

msgid ", And the rest of the characters are replaced verbatim with"
msgstr ""
"characters of sensitive info unchanged, and replace the rest of it with"

msgid ", because the netmask is "
msgstr ""

msgid ", but does not provide dynamic token protections to those requests."
msgstr ", but does not add protection tokens to those requests."

msgid "."
msgstr ". "

msgid "1 hour ago"
msgstr "1 Hour Ago"

msgid ""
"1. Fuzzy matching and case insensitive for URL. Example: "
"http://www.mysite.co<br/>2. Match only path not parameters for URL.<br/>3. "
"HTTP and HTTPS default ports (80 and 443) should not be entered, otherwise "
"URL cannot be matched."
msgstr ""
"1. Fuzzy matching and case insensitive for URL. Example: "
"http://www.mysite.com<br/>2. Match only path not parameters for URL.<br/>3. "
"HTTP and HTTPS default ports (80 and 443) should not be entered, otherwise "
"URL cannot be matched."

#, python-brace-format
msgid ""
"1. Go to page {0}, find a node bearing the role {1} and enable the role {2} "
"for it."
msgstr ""

msgid ""
"1. If both items are left blank, the access frequency of the same IP will be"
" counted;"
msgstr ""
"1. Any IP with an access frequency matches the Rate will trigger the "
"strategy if leaving both parameter empty."

msgid ""
"1. Multiple nodes provide better processing capacities than a single-node "
"deployment."
msgstr ""

msgid ""
"1. Offline Request Body Obfuscation means obfuscating request bodies before "
"the handshake between the SDK and the protection system is completed."
msgstr ""

msgid ""
"1. Strong protection policies are used for HTTPS websites. IE11 and earlier "
"versions are not supported."
msgstr ""

msgid ""
"1. The request header only supports ordinary strings. 2. Maximum of 20 "
"request headers 3.The total length cannot exceed 1024 4. Separate each "
"request header with a comma"
msgstr ""

msgid "100 points attenuated to 0 points takes about "
msgstr "The fading process from 100 to 0 takes about"

msgid "1st-Gen China ID Card"
msgstr ""

msgid "1st-Gen ID Card"
msgstr ""

msgid "1~65535"
msgstr ""

msgid "1～10000"
msgstr ""

#, python-brace-format
msgid "2. Go to tab {0}, save your configurations and start learning."
msgstr ""

msgid "2. Multiple nodes ensure a higher availability."
msgstr ""

msgid "2. Scanner client feature identification and interception;"
msgstr ""

msgid "2. Up to 10 requests can be obfuscated offline."
msgstr ""

msgid ""
"2. When only the user name parameter is filled in, the access frequency of "
"the same user name will be counted;"
msgstr ""
"2. Any pair of IP and username with an access frequency matches the Rate "
"will trigger the strategy if leaving password parameter empty."

msgid "20 characters are allowed at most for a name"
msgstr ""

msgid "200 characters at most"
msgstr ""

msgid "200 entries at most"
msgstr ""

msgid "2nd-Gen China ID Card"
msgstr ""

msgid "2nd-Gen ID Card"
msgstr ""

msgid "3. Add bot traps/special crawler paths to identify scanner behavior."
msgstr ""

#, python-brace-format
msgid "3. Go to tab {0}, generate compliance strategies."
msgstr ""

msgid ""
"3. It is only effective when the Mini-Program Request Body Obfuscation is "
"enabled in [Websites] settings."
msgstr ""

msgid ""
"3. When only password parameters are filled in, the access frequency of the "
"same password will be counted;"
msgstr ""
"3. Any pair of IP and password with an access frequency matches the Rate "
"will trigger the strategy if leaving username parameter empty."

msgid "30-Day Average Daily Volume"
msgstr ""

msgid "30-Day Exceeded Volume Count"
msgstr ""

msgid "30-Day Max Daily Volume"
msgstr ""

msgid ""
"4. Under current tab, bond strategies generated in last step to websites as "
"required."
msgstr ""

msgid ""
"4. When both items are filled in, the access frequency of the same user name"
" and password will be counted respectively."
msgstr ""
"4. Any username or password with an access frequency matches the Rate will "
"trigger the strategy if both parameters are entered."

msgid "4K"
msgstr ""

msgid "5 characters at most"
msgstr ""

msgid "500 characters at most"
msgstr ""

msgid "802.3ad"
msgstr "Mode 4: 802.3ad LACP"

msgid "8K"
msgstr ""

msgid "; ' < > "
msgstr ""

msgid ""
"<span class=\"notice\">both of the list need to be configured.</span> 1. if "
"the Ajax/Fetch request was sent from an allian site, Ajax/Fetch Response "
"Encryption List should be configured here, but Ajax/Fetch Referer List "
"should be configured under that allian site. 2. If the Ajax/Fetch request "
"was sent from iframe, the iframe url need to be configured in Ajax/Fetch "
"referer list."
msgstr ""
"<span class=\"notice\">Note: </span> 1. if the Ajax/Fetch request is sent "
"from an affiliated site, then Ajax/Fetch Response should be configured here,"
" but Ajax/Fetch Referer should be configured for that affiliated site. 2. If"
" the Ajax/Fetch request is sent from an iframe, the iframe URL should be "
"configured in the Ajax/Fetch referer list."

msgid ""
"<span name=\"abd_node_count\"></span> node(s) of this function exist in "
"current cluster."
msgstr ""

msgid ""
"<span name=\"ai_node_count\"></span> node(s) of this function exist in "
"current cluster."
msgstr ""

msgid ""
"<span name=\"api_gateway_node_count\"></span> node(s) of this function exist"
" in current cluster."
msgstr ""

msgid ""
"<span name=\"bta_node_count\"></span> node(s) of this function exist in "
"current cluster."
msgstr ""

msgid ""
"<span name=\"fsl_node_count\"></span> node(s) of this function exist in "
"current cluster."
msgstr ""

msgid ""
"<span name=\"protect_node_count\"></span> node(s) of this function exist in "
"current cluster."
msgstr ""

msgid ""
"<span name=\"sailfish_count\"></span> node(s) of this function exist in "
"current cluster."
msgstr ""

msgid "<span name=\"upstreamCount\"></span> website(s) added in total"
msgstr "<span name=\"upstreamCount\"></span> website(s) in total"

msgid ""
"A Chrome browser of the latest version is required to display Command Line."
msgstr ""

msgid ""
"A Chrome browser of the latest version is required to display Statistics."
msgstr ""

msgid ""
"A JWT encrypted with a weak signature algorithm such as MD5 and HMAC-SHA1 "
"allows attackers to use forged tokens to gain access."
msgstr ""

msgid ""
"A JWT with a long-term validity means high possibility of being used by "
"attackers to gain access."
msgstr ""

msgid ""
"A JWT without signature algorithm allows attackers to use forged tokens to "
"gain access."
msgstr ""

msgid ""
"A comma at the beginning or end, or multiple consecutive commas are not "
"allowed."
msgstr ""

#, python-brace-format
msgid "A core dump occurred({timestamp})"
msgstr ""

msgid ""
"A dialog requesting access permission might appear in browsers at client "
"ends if this feature is enabled. Any changes made to existing settings of "
"the feature will trigger an instant deletion of the data collected "
"previously.<br/>Notes: This feature may cause a drop in system performance "
"during collecting."
msgstr ""

msgid ""
"A hot standby system consists of a primary and a standby node online "
"simultaneously. The primary node would be the only one working, while the "
"redundant node always waiting to take over in case the primary node "
"encounters any failure."
msgstr ""
"It consists of a primary and a standby node. The primary node would be the "
"only active one, while the redundant node remains inactive until the primary"
" node fails."

msgid ""
"A hot standby system consists of a primary and a standby node online "
"simultaneously. they are both working and waiting to take over in case the "
"other node encounters any failure."
msgstr ""
"It consists of a primary and a standby node. Both are active and can take "
"over all the load in case the other node fails."

msgid "A keyword can only contain {} characters at most"
msgstr ""

#, python-format
msgid ""
"A maximum of %(maxCount)s versions are supported. The oldest version will be"
" overwritten when exceeding the upper limit."
msgstr ""

msgid "A maximum of 10 external website URLs are allowed."
msgstr ""

msgid "A maximum of 500 records are allowed to be added."
msgstr ""

msgid ""
"A monitoring tool that provides uninterrupted access to a website, minimizes"
" downtime and optimizes performance to ensure that a website is online 24/7 "
"and runs smoothly. If an outage occurs, these tools provide alerts via "
"email, phone, or text message."
msgstr ""

msgid "A node is joining cluster. Please wait and try again"
msgstr ""

msgid "A node is modify role. Please wait and try again"
msgstr ""

msgid "A primary node must be created first."
msgstr ""

msgid ""
"A successful login will log out the same user signed in on other "
"browsers/devices."
msgstr ""

msgid ""
"A threat-awareness session is a request analyzer built on demand. When the "
"number of requests with the same characteristics (such as source IP) exceeds"
" the threshold, a new threat awareness session is created to analyze these "
"requests."
msgstr ""
"A BTA session is a request analyzer built on demand. When the number of "
"requests with the same feature (such as source IP) exceeds the threshold, a "
"new BTA session is created to analyze these requests."

msgid ""
"A tool that does not belong to a type defined by the system but has detected"
" crawler characteristics."
msgstr ""

msgid ""
"A tool that feeds back information retrieved from the Internet to users "
"according to a certain algorithm."
msgstr ""

msgid ""
"A tool that scans a web page to automatically detect security weaknesses."
msgstr ""

msgid ""
"A tool that scans a web page to find out whether there are malicious viruses"
" or trojans."
msgstr ""

msgid ""
"A tool that tests a website or application (e.g. performance testing, "
"automation testing, etc.)."
msgstr ""

msgid "ABD Service"
msgstr ""

msgid "AES Algorithm SDK"
msgstr ""

msgid "AI Analysis Results"
msgstr ""

msgid "AI Detection"
msgstr "AI Detection"

msgid "AI Threat Analyst"
msgstr ""

msgid "AI detection"
msgstr "AI Detection"

msgid "AI intelligent detection"
msgstr "AI Detection"

#, python-brace-format
msgid "AI service unavailable but {upstream_key} use it."
msgstr ""
"AI WAF enabled on website {upstream_key} is not working as the corresponding"
" service is not found."

msgid "AI-WAF"
msgstr ""

msgid "AI-WAF request whitelist"
msgstr "AI-WAF Whitelist"

msgid "AI-WAF request whitelist has duplicate configuration."
msgstr "AI-WAF Whitelist has duplicate configuration."

msgid "AI_Threat_Analyst"
msgstr "AI Threat Analyst"

msgid ""
"ALLOW-FROM means the page can only be displayed in a frame of specified "
"origin."
msgstr ""

msgid "AND"
msgstr ""

msgid "API"
msgstr ""

msgid "API Abuse(RAT-034)"
msgstr ""

msgid "API Attack Policies"
msgstr "API Attack Detection Strategies"

msgid "API Attack Policies file format is incorrect"
msgstr "Invalid file format of API Attack Detection Strategies."

msgid "API Attack Policies file format is incorrect."
msgstr "Invalid file format of API Attack Detection Strategies."

msgid "API Attack Policies file should not be empty"
msgstr "Empty file cannot be used."

msgid "API Attack Policies temporary file does not exist"
msgstr "The temporary file of API Attack Detection Strategies does not exist."

msgid "API Attack Policies temporary file is destroyed."
msgstr ""
"The temporary file of API Attack Detection Strategies has been deleted."

msgid "API Attack Policy"
msgstr "API Attack Detection Strategy"

msgid "API Attack Policy Risk Level"
msgstr ""

msgid "API Counts"
msgstr "APIs"

msgid "API Customized Rules"
msgstr "API Recognition Rule"

msgid "API Gateway"
msgstr "System API"

msgid "API Intelligent Merging"
msgstr ""

#, python-brace-format
msgid "API Intelligent Merging: {0}"
msgstr ""

msgid "API Means"
msgstr "API Monitor"

msgid "API Merge Whitelist"
msgstr ""

msgid "API Monitor"
msgstr ""

msgid "API Name"
msgstr ""

msgid "API OFFLINE"
msgstr ""

msgid "API PII"
msgstr ""

msgid "API Path"
msgstr ""

msgid "API Security"
msgstr "API Analytics"

msgid "API Type"
msgstr ""

msgid "API account tracking access token mapping"
msgstr ""

msgid "API account tracking refresh token mapping"
msgstr ""

msgid "API already exists!"
msgstr ""

msgid "API awareness, discovery, monitoring and protection."
msgstr "Discovering, managing and protecting API assets."

msgid "API call order scene"
msgstr "Statistics of API Call Sequence"

msgid "API conf error. Please upload a valid system file."
msgstr ""

msgid "API does not exist!"
msgstr ""

msgid "API file only support UTF-8 encoding"
msgstr "Only support UTF-8 encoding"

msgid "API file should not be empty"
msgstr ""

msgid "API file(Swagger) is invalid file,must be in ZIP or JSON format"
msgstr ""

msgid "API file(Swagger) is not openapi 3.x version file"
msgstr ""

msgid "API file(Swagger) is not valid Swagger (openapi 3.x) file,missing {}"
msgstr ""

msgid "API file(Swagger) is not valid json file"
msgstr ""

msgid "API has been removed"
msgstr ""

msgid "API interface"
msgstr "API"

msgid "API list"
msgstr "API List"

msgid "API param detection rules error:{}"
msgstr ""

msgid "API period sample collection"
msgstr "Timed Sampling"

msgid "API risk sample collection"
msgstr "Full Sampling"

msgid ""
"API users need to calculate a signature value using Token ID and Token "
"value, as well as some other necessary parameters, and send the signature "
"value along with these parameters as request parameters. Only after the "
"request is verified can the system service interface be accessed. If you "
"suspect that the key verification information has been leaked, you can "
"ensure safe access by updating Token value and recalculating to get a new "
"signature. Please refer to the interface instructions for API request "
"construction."
msgstr ""
"API users need to calculate a signature value using TokenID, TokenValue, and"
" other necessary parameters, and then send the signature value along with "
"these parameters as request parameters. Only after the request is verified "
"can the system API be accessed. If you think the key verification "
"information has been leaked, you can update the token to generate a new "
"signature. Please refer to the API Documentation for detailed usage."

msgid ""
"API with parameter position {} and parameter name {} already exists in this "
"path, other parameter positions and names is not allowed."
msgstr ""
"In the same parameter position ({}) of this path, another parameter name "
"({}) already exists."

msgid "API/API Name"
msgstr ""

msgid "API1-Broken Object Level Authorization"
msgstr ""

msgid "API10-Unsafe Consumption of APIs"
msgstr ""

msgid "API2-Broken Authentication"
msgstr ""

msgid "API3-Broken Object Property Level Authorization"
msgstr ""

msgid "API4-Unrestricted Resource Consumption"
msgstr ""

msgid "API5-Broken Function Level Authorization"
msgstr ""

msgid "API6-Unrestricted Access to Sensitive Business Flows"
msgstr ""

msgid "API7-Server Side Request Forgery"
msgstr ""

msgid "API8-Security Misconfiguration"
msgstr ""

msgid "API9-Improper Inventory Management"
msgstr ""

msgid "API_GATEWAY"
msgstr "System API"

msgid "APP Blacklist"
msgstr "App Blacklist"

msgid "APP Counter"
msgstr "App Counter"

msgid "APP Counter Scene"
msgstr "App Counter Scenario"

msgid "APP Group Control(RAT-049)"
msgstr ""

msgid "APP Security Protection Overview"
msgstr "Application Security Overview"

msgid "APP Site does not exist!"
msgstr ""

msgid "APP blacklist rule"
msgstr "App blacklist rule"

msgid "APP counter rule"
msgstr "App counter rule"

msgid "APP does not exists!"
msgstr ""

msgid "APP high frequent rule"
msgstr "App high frequency rule"

msgid "APP high frequent rule status"
msgstr "App high frequency rule status"

msgid "APP reputation rule"
msgstr "App reputation rule"

msgid "APP reputation rule status"
msgstr "App reputation rule status"

msgid "ARGS"
msgstr ""

msgid "ARGS_GET"
msgstr ""

msgid "ARGS_GET_NAMES"
msgstr ""

msgid "ARGS_NAMES"
msgstr ""

msgid "ARGS_POST"
msgstr ""

msgid "ARGS_POST_NAMES"
msgstr ""

msgid "AUTOGEN-NAME-{}"
msgstr ""

msgid "Abnormal 4XX requests count(ds_4xx_count)"
msgstr ""

msgid "Abnormal 5XX requests count(ds_5xx_count)"
msgstr ""

msgid "Abnormal App data(ds_app_data)"
msgstr ""

msgid "Abnormal HTTP method(ds_other_method)"
msgstr ""

msgid "Abnormal HTTP version(ds_protocol_version)"
msgstr ""

msgid "Abnormal Handshake"
msgstr ""

msgid "Abnormal IP/FP switch(ds_if_distinct_count)"
msgstr ""

msgid "Abnormal JS verify Bot type(ds_js_bot)"
msgstr ""

msgid "Abnormal Requests"
msgstr ""

msgid "Abnormal Statistics node(s) found in this cluster."
msgstr "Abnormal Big Data Analysis node(s) found in this cluster."

msgid "Abnormal Terminal(RAT-048)"
msgstr ""

msgid "Abnormal UA verify Bot type(ds_ua_bot)"
msgstr ""

msgid "Abnormal UA/JS mismatch(ds_js_ua_ummatch)"
msgstr ""

msgid "Abnormal abnormal request count(ds_attack_type_count)"
msgstr ""

msgid "Abnormal app install count(ds_install_distinct_count)"
msgstr ""

msgid "Abnormal app signature request count(sic)"
msgstr ""

msgid "Abnormal app signature(ds_abnormal_app_signature)"
msgstr ""

msgid "Abnormal behavior(ds_abnormal_behavior)"
msgstr ""

msgid "Abnormal bot type(ds_bot_type)"
msgstr ""

msgid "Abnormal business fail rate(ds_fail_rate)"
msgstr ""

msgid "Abnormal business frequency(ds_butype_freq)"
msgstr ""

msgid "Abnormal business max frequency(ds_max_butype_freq)"
msgstr ""

msgid "Abnormal business no input requests percent(ds_zero_input_percent)"
msgstr ""

msgid "Abnormal business requests percent(ds_butype_percent)"
msgstr ""

msgid "Abnormal charging(ds_charging)"
msgstr ""

msgid "Abnormal device infomation request count(dpsc)"
msgstr ""

msgid "Abnormal device infomation(ds_abnormal_device_info)"
msgstr ""

msgid "Abnormal distinct path(ds_distinct_path)"
msgstr ""

msgid "Abnormal handshake count(ds_handshake)"
msgstr ""

msgid "Abnormal inject rules(ds_inject_rule)"
msgstr ""

msgid "Abnormal invalid format(ds_invalid_format_count)"
msgstr ""

msgid ""
"Abnormal mobile devices (including tablet and other handheld devices), SDK "
"only. Abnormal mobile devices usually refer to phones with unconventional "
"modifications to carry out app cracking, code reverse, batch control and "
"other illegal uses. Modifications to mobile phones include, but are not "
"limited to, obtaining root permissions, installing Hook frameworks, "
"installing phone modification software, and installing automation tools."
msgstr ""

msgid "Abnormal operation request!"
msgstr ""

msgid "Abnormal operation! Please try again!"
msgstr ""

msgid "Abnormal reputation score(ds_reputation)"
msgstr ""

msgid ""
"Abnormal requests count(atc) (exclude Attack_Type=OK or "
"blocked_rule='SCENE_REPUTATION' or invalid_request_action=pass/NULL)"
msgstr ""

msgid "Abnormal requests frequency(ds_req_freq)"
msgstr ""

msgid "Abnormal server response data."
msgstr ""

msgid "Abnormal session time(ds_session_length)"
msgstr ""

msgid "Abnormal static resource percent(ds_static_res_percent)"
msgstr ""

msgid "Abnormal total requests frequency(ds_tc_freq)"
msgstr ""

msgid "Abnormal used account(ds_user_distinct_count)"
msgstr ""

msgid "Abnormal used failed account(ds_user_failed_distinct_count)"
msgstr ""

msgid "Abnormal user count(ds_account_count)"
msgstr ""

msgid "Accept-Encoding in HTTP header."
msgstr "Accept-Encoding"

msgid "Access"
msgstr ""

msgid "Access Log Sender"
msgstr ""

msgid "Access Pattern Intelligent Detection"
msgstr "Intelligent Analysis of Access Pattern"

msgid "Access Site List"
msgstr "Websites"

msgid "Access white path has duplicate configuration."
msgstr ""

msgid "Accesses Record"
msgstr ""

msgid ""
"According to business needs, the interface is restricted from directly "
"passing database query statement parameters. According to business needs, "
"only some parameters should be opened. The final query statement should be "
"controlled by the backend. At the same time, attention should be paid to "
"filtering illegal characters in the parameters."
msgstr ""
"Ensure APIs do not accept request parameters that use database queies as "
"their values or include disallowed characters."

msgid ""
"According to business needs, the interface is restricted from passing "
"command parameters. According to business needs, only some parameters should"
" be opened. The final executed system command should be controlled by the "
"backend. At the same time, attention should be paid to filtering illegal "
"characters in the parameters to prevent hackers from constructing malicious "
"parameters and causing the system to execute illegal system commands."
msgstr ""
"Ensure APIs do not accept request parameters that use system commands as "
"their values or include disallowed characters."

msgid ""
"According to the returned prompt information, the attacker may enumerate the"
" login user names in the system, and then brute force their passwords or "
"perform other more advanced attacks based on the collected user names."
msgstr ""
"Inappropriated messages returned to clients who failed to log in may help "
"attackers find out corrent usernames or passwords that have been created on "
"websites."

msgid "Account"
msgstr ""

msgid "Account Creation(OAT-019)"
msgstr ""

msgid "Account Reputation"
msgstr ""

msgid "Account Tracking"
msgstr ""

msgid "Account brute force attacks"
msgstr "Account Brute Force Attacks"

msgid "Account cannot be empty"
msgstr "Account is required."

msgid "Account cannot contain the following characters:"
msgstr ""

msgid "Account high-frequency calls"
msgstr "Account High-Frequency Calls"

msgid "Account is locked"
msgstr ""

msgid "Account locked due to prolonged inactivity"
msgstr "Idle Account Locking"

msgid "Account name"
msgstr ""

msgid "Account should be directly followed by hostname, e.g. adminwww.a.com"
msgstr ""

#, python-brace-format
msgid "Account {receiver} for receiving alarm SMS."
msgstr "Set account {receiver} for receiving alarm SMS."

#, python-brace-format
msgid "Account {receiver} for receiving alarm emails."
msgstr "Set account {receiver} for receiving alarm emails."

msgid ""
"Acquisition of goods or services using the application in a manner that a "
"normal user would be unable to undertake manually. Although Scalping may "
"include monitoring awaiting availability of the goods or services, and then "
"rapid action to beat normal users to obtain these, Scalping is not a 'last "
"minute' action like OAT-013 Sniping, nor just related to automation on "
"behalf of the user such as in OAT-006 Expediting. This is because Scalping "
"includes the additional concept of limited availability of sought-after "
"goods or services, and is most well known in the ticketing business where "
"the tickets acquired are then resold later at a profit by the "
"scalpers/touts. This can also lead to a type of user denial of service, "
"since the goods or services become unavailable rapidly."
msgstr ""

msgid "Action"
msgstr ""

msgid "Action "
msgstr "Action"

msgid "Action Rate"
msgstr ""

msgid "Actions"
msgstr "Operations"

msgid "Activate"
msgstr ""

msgid "Activated Fingerprint"
msgstr ""

msgid "Activated Node Fingerprint"
msgstr ""

msgid ""
"Activation failed, the number of mini-programs in the configuration file "
"exceeds the permitted range of the license authorization."
msgstr ""

msgid ""
"Activation succeeded. The page will be refreshed to get the latest "
"authorization information..."
msgstr ""

msgid "Active"
msgstr ""

msgid "Active Status"
msgstr ""

msgid "Active anti-scan"
msgstr "Block Scanning"

msgid "Activity Duration"
msgstr "Activity Lasts"

msgid "Activity Start Time"
msgstr "Activity Starts at"

msgid "Ad Fraud(OAT-003)"
msgstr ""

msgid "Adapter Type"
msgstr ""

msgid "Add"
msgstr ""

msgid "Add "
msgstr ""

#, python-format
msgid ""
"Add %(startTag)s the big data analysis S %(endTag)s node IP of the external "
"system to the current cluster to query the report data of the external "
"system."
msgstr ""
"Enter the %(startTag)s Big Data Analysis S (Query) %(endTag)s node IP of the"
" external system to query the log data of that system."

#, python-format
msgid ""
"Add %(startTag)s the big data analysis S (query) %(endTag)s node IP  of the "
"external system to the current cluster, allowing the external system to "
"query the report data of the current cluster."
msgstr ""
"Enter the %(startTag)s Big Data Analysis S (Query) %(endTag)s node IP of the"
" external system to allow it to query the log data of the current cluster."

msgid "Add Account"
msgstr ""

msgid "Add Action"
msgstr ""

msgid "Add Counter"
msgstr ""

msgid "Add Custom Rule"
msgstr "Create Site Custom Rule"

msgid "Add Field"
msgstr "Add Header"

msgid "Add Route"
msgstr ""

msgid "Add Rule"
msgstr ""

msgid "Add SMS Receiver"
msgstr "Set up phone numbers to receive alarm SMS messages."

msgid "Add Share Memory"
msgstr "Add Shared Memory"

msgid "Add Site Customize Name Failed!"
msgstr ""

msgid "Add Site Customize Name OK!"
msgstr ""

msgid "Add URL"
msgstr ""

msgid "Add URL token to following paths"
msgstr "Include List"

msgid "Add Websites"
msgstr "Add a Website"

msgid "Add a default gateway for the protection system."
msgstr ""

msgid ""
"Add an external statistics system to the current cluster to read log data "
"from the external statistics system."
msgstr ""
"Add an external analytics system to the current cluster to read log data "
"from that system."

msgid "Add business"
msgstr ""

msgid "Add business data collection successful"
msgstr "Business data collection added successfully"

msgid "Add cluster IP whitelist"
msgstr ""

msgid "Add custom field"
msgstr "Custom Field"

msgid "Add custom geo ip"
msgstr "Added Geolocation Correction rules"

msgid "Add external big data analysis S (query) node failed: Duplicate ip."
msgstr ""

msgid ""
"Add external big data analysis S (query) node failed: Invalid IPV4 address."
msgstr ""

msgid ""
"Add external big data analysis S (query) node failed: Invalid parameter "
"type."
msgstr ""

msgid "Add external big data analysis S (query) node failed: save failed."
msgstr ""

msgid ""
"Add external big data analysis S (query) node failed: the number exceeds 20."
msgstr ""

msgid ""
"Add external the big data analysis S node failed: Duplicate ip address."
msgstr ""
"Failed to add the external big data analysis S node: Duplicate ip address."

msgid ""
"Add external the big data analysis S node failed: Invalid IPV4 address."
msgstr ""
"Failed to add the external big data analysis S node: Invalid IPV4 address."

msgid ""
"Add external the big data analysis S node failed: Invalid pramameter type."
msgstr ""
"Failed to add the external big data analysis S node: Invalid parameter type."

msgid ""
"Add external the big data analysis S node failed: The number of Sailfish "
"external nodes exceeds 20."
msgstr ""
"Failed to add the external big data analysis S node: The number of external "
"big data analysis nodes exceeds 20."

msgid "Add external the big data analysis S node failed: save failed."
msgstr "Failed to add the external big data analysis S node: Failed to save."

msgid "Add ignore list API"
msgstr "Added one API to the Ignore List"

msgid "Add requests that require encryption verification"
msgstr ""

msgid "Add responses that does not require encryption"
msgstr ""

msgid "Add sensitive information white list"
msgstr "Added PII Whitelist"

#, python-brace-format
msgid "Add the external big data analysis S (query) node IP: {ex_query_node}."
msgstr "Added a source node IP {ex_query_node} to allow external log query"

#, python-brace-format
msgid "Add the external big data analysis S node IP {new_external_node_ip}."
msgstr "Added a target node IP {new_external_node_ip} to query external logs"

msgid "Add to WAF White List"
msgstr "Add WAF Whitelist"

msgid "Add to node list"
msgstr ""

msgid "Add up to 20 configuration data!"
msgstr ""

msgid "Add user-defined risk config: {}."
msgstr "Added a custom defect: {}."

msgid "Add website name"
msgstr ""

msgid "Added Manually"
msgstr ""

msgid "Added to WAF White List"
msgstr "Added to WAF Whitelist"

msgid "Added_one_application_while_merging"
msgstr "Added one application and merged existing one(s) into"

msgid "Adding Charset"
msgstr "Add Charset"

msgid "Address"
msgstr ""

msgid "Address of debug page:"
msgstr ""

msgid "Adjust Priority"
msgstr ""

msgid "Adjust datetime successfully.Cluster datetime synchronization failed."
msgstr ""

msgid ""
"Adjust datetime successfully.Cluster datetime synchronization will be "
"conducted later."
msgstr ""

msgid "Adjust system datetime"
msgstr ""

msgid "Admin Account"
msgstr ""

msgid "Admin Username"
msgstr ""

msgid "Administrator"
msgstr ""

msgid ""
"Administrator: Full access, including viewing and changing system "
"configuration."
msgstr ""

msgid ""
"Adopt a business scheduling process to improve business processing "
"capabilities."
msgstr ""

msgid "Advance sniping"
msgstr "Sniping in advance"

msgid "Advanced"
msgstr ""

msgid "Advanced Configuration"
msgstr "Misc Configurations"

msgid "Advanced Data Decoder"
msgstr "Advanced Decoding"

msgid "Advanced Persistent Bot"
msgstr ""

msgid "Advanced Verify"
msgstr "Advanced Verification"

#, python-brace-format
msgid "Advanced Verify feature: {0}"
msgstr "Advanced Verification feature: {0}"

msgid "Advanced WAF"
msgstr ""

msgid "Advanced Web Configuration"
msgstr "Misc Web Configurations"

msgid "Advanced expression"
msgstr ""

msgid "Advanced protection"
msgstr ""

msgid "After 5 seconds, the page will <a href="
msgstr ""

msgid ""
"After adding, modifying, or deleting a WeChat App, you must click Save to "
"take effect"
msgstr ""
"After adding, modifying, or deleting an app, you must click Save to take "
"effect"

msgid ""
"After bridge transparent mode is enabled, traffic between downstream and "
"upstream would be forwarded without protection."
msgstr ""
"After bridge pass-through mode is enabled, traffic between downstream and "
"upstream would be forwarded without protection."

msgid ""
"After bypass is enabled, traffic between downstream and upstream would be "
"forwarded without protection."
msgstr ""

msgid ""
"After confirm, only source address in cluster or white list can access "
"system manage page and remote login"
msgstr ""
"After saving, you can only log in to the system from source addresses "
"included in the list."

msgid ""
"After eliminating the alarm, if the system state exception is not lifted, "
"the alarm will not continue, whether to immediately eliminate the alarm?"
msgstr ""

msgid ""
"After enabling, the system will cache the specified URL and return to the "
"cached page when the client requests it again to avoid visiting the page "
"that has been maliciously tampered with."
msgstr ""
"This feature caches pages that match the entries under \"Pages to be "
"Cached\", so that visitors always get intact pages in case websites have "
"been tampered with illegally."

msgid ""
"After entering the advanced filter from the filter interface, you cannot "
"return to the filter. If you need to go back to filter, you need to click "
"the Cancel button and re-enter the previous interface."
msgstr ""

msgid ""
"After it is enabled, front network devices can send request to the "
"configured access path to check the health status of the site."
msgstr ""
"It allows network devices logically located before the system to check the "
"health status of nodes in the system."

msgid ""
"After opening, it will monitor http traffic on all ports in the current "
"network, but it may have a certain impact on performance. If system "
"resources are in short supply, please turn off this option and manually add "
"the port designated by the site to collect traffic to improve performance."
msgstr ""

msgid ""
"After port status forward is enabled, it will synchronize the working status"
" between the network ports transmitting traffic in pairs for the system."
msgstr ""

msgid ""
"After saving, the current rule will be applied to the selected site. Are you"
" sure to save?"
msgstr ""

msgid ""
"After saving, the current strategy will be applied to the selected site. Are"
" you sure to save?(This function will be closed if the site is unchecked)"
msgstr ""
"The site(s) checked in previous dialog will be bonded to the strategy you "
"are editting now. While those unchecked will be bonded to Basic Strategy "
"Template by default. Click Save to confirm."

msgid ""
"After saving, the current strategy will be applied to the selected site. Are"
" you sure to save?(Unchecked sites will automatically apply the default "
"policy)"
msgstr ""
"The site(s) checked in previous dialog will be bonded to the strategy you "
"are editting now. While those unchecked will be bonded to Basic Strategy "
"Template by default. Click Save to confirm."

msgid ""
"After specifying an IP, it will not be intercepted by threat intelligence."
msgstr ""
"Requests sent from the following whitelist will not be inspected with threat"
" intelligence features."

msgid ""
"After the big data analysis P (query) node is deleted, the system will "
"automatically change a P (storage) node to a query node and re-accelerate "
"the statistics system. The query performance of the statistics system will "
"be reduced in a short time. "
msgstr ""

msgid ""
"After the reset, App Mobile Protection with the old salt value generated "
"mobile certificate will be invalidated"
msgstr ""

msgid ""
"After this function is enabled, only abnormal access logs are recorded, and "
"archive log filtering is automatically disabled."
msgstr ""
"When enabled, only abnormal access logs will be recorded, and Archived Log "
"Filter feature will be automatically disabled."

msgid ""
"After token value is updated, the old token cannot be used. Confirm the "
"update?"
msgstr ""

msgid "Age"
msgstr ""

msgid "Agent CPU quota"
msgstr "Capture Agent CPU Limit"

msgid "Agent CPU quota allows configuration range 1-100"
msgstr ""
"Maximum CPU rate of the host system used by capture agent. Maximum CPU cores"
" used = CPU Cores * Maximum CPU Rate (rounded up). Rate 100 means using all "
"CPU cores, and rate 50 means using half of CPU cores."

msgid "Agent packet capture memory"
msgstr "Capture Agent Memory Limit"

msgid "Agent packet capture memory allows configuration range 1-20"
msgstr ""
"Maximum memory rate of the host system used by capture agent for traffic "
"capturing. Range: 1%-20% (maximum capturing memory no more than 4G)."

msgid "AiServiceUnavailable"
msgstr "AI service unavailable"

msgid "Ajax/Fetch Referer List"
msgstr "Ajax/Fetch Referer"

msgid "Ajax/Fetch Referer List cannot be blank!"
msgstr "Ajax/Fetch Referer is required."

msgid ""
"Ajax/Fetch Referer List is made up of regular expressions. html page with "
"destination URLs that match any of the following expressions will hook more "
"Ajax/Fetch method for supporting Ajax/Fetch response body encryption."
msgstr ""
"HTML pages with destination URLs that match any of the following rules will "
"hook more Ajax/Fetch methods to obfuscate Ajax/Fetch responses."

msgid "Ajax/Fetch Req URL_Token_Free"
msgstr "Ajax/Fetch Request URL Token"

msgid "Ajax/Fetch Req URL_Token_Free path(s) cannot be left blank."
msgstr "Ajax/Fetch Request URL Token is required."

msgid "Ajax/Fetch Request Body Encryption"
msgstr "Obfuscate Ajax/Fetch Request"

msgid "Ajax/Fetch Request Body Encryption List"
msgstr "Obfuscate Ajax/Fetch Request"

msgid "Ajax/Fetch Response Encryption"
msgstr "Obfuscate Ajax/Fetch Response"

msgid "Ajax/Fetch Response Encryption List"
msgstr "Ajax/Fetch Response"

msgid "Ajax/Fetch request body encryption list cannot be blank!"
msgstr "Obfuscate Ajax/Fetch Request is required."

msgid ""
"Ajax/Fetch request body encryption list is made up of regular expressions. "
"Ajax/Fetch request body with URLs that match any of the following "
"expressions will be encrypted when Ajax/Fetch request body encryption is "
"enabled."
msgstr ""
"Ajax/Fetch request body with URLs that match any of the following rules will"
" be obfuscated."

msgid "Ajax/Fetch response encryption list cannot be blank!"
msgstr "Ajax/Fetch Response is required."

msgid ""
"Ajax/Fetch response encryption list is made up of regular expressions. "
"Ajax/Fetch responses body with URLs that match any of the following "
"expressions will be encrypted when Ajax/Fetch response encryption is "
"enabled."
msgstr ""
"Ajax/Fetch responses body with URLs that match any of the following rules "
"will be obfuscated."

msgid "Alarm"
msgstr ""

msgid "Alarm Duration"
msgstr "Duration"

msgid "Alarm History"
msgstr ""

msgid "Alarm ID"
msgstr ""

msgid "Alarm Name"
msgstr ""

msgid "Alarm Severity"
msgstr ""

msgid "Alarm Test"
msgstr ""

msgid "Alarm Threshold"
msgstr ""

msgid "Alarms"
msgstr ""

msgid "Alipay Mini Program Protection"
msgstr "Alipay Mini-Program"

msgid "Alipay Mini Program Protection body obfuscation setting"
msgstr "Alipay Mini-Program Body Obfuscation"

msgid "Alipay mini program request whitelist"
msgstr "Alipay Mini-Program Whitelist"

msgid "Alipay mini progrma SDK"
msgstr "Alipay Mini-Program SDK"

msgid "Alipay_MPP"
msgstr ""

msgid "All"
msgstr ""

msgid "All Location"
msgstr "Any Position in Requests"

msgid "All Log"
msgstr ""

msgid "All Rule Count: "
msgstr "Rules in Total: "

msgid "All Time"
msgstr ""

msgid "All Transparent"
msgstr ""

msgid "All Upstream Health Status"
msgstr "Upstream health status (all)"

msgid ""
"All abnormal HTTP requests blocked by the system can be found in this page. "
"Double-clicking a line or the URL in a line can display details."
msgstr ""
"Abnormal HTTP requests (maximum: 1000) identified by the system are "
"displayed here. Double-click an item or the URL to check the details."

msgid "All changes and log datas will be lost. Proceed anyway?"
msgstr "All changes and log data will be lost. Proceed anyway?"

msgid "All contains only four methods: GET, POST, DELETE, and PUT."
msgstr "Only GET, POST, DELETE and PUT are supported here."

msgid "All requests for the following domain names will be blocked."
msgstr "All requests to the following Hosts will be blocked."

#, python-brace-format
msgid "All sites managed by {owner} are in transparent mode."
msgstr ""

msgid "All x86 nodes in the heterogeneous cluster have been upgraded."
msgstr ""

msgid "Allow"
msgstr ""

msgid ""
"Allow Apps to receive a notification realized by inserting a special token "
"in response headers once server-side protection system comes back online "
"from the latest offline state."
msgstr ""
"Insert a special token in response headers when the protection system comes "
"back from a recent failure to notify the apps that the system is back "
"online."

msgid "Allow access to protected sites through management network adapter"
msgstr "Website Accessible via eth0"

msgid ""
"Allow all IPv4 addresses of this network adapter to access the WebConsole"
msgstr "Allow all IPv4 addresses of the network adapter to access WebConsole"

msgid ""
"Allow all IPv6 addresses of this network adapter to access the WebConsole"
msgstr "Allow all IPv6 addresses of the network adapter to access WebConsole"

msgid "Allow all ip addresses of this network adapter to access the nginx"
msgstr ""

msgid ""
"Allow environment data collected from Apps to be forwarded to server-side "
"protection system without applying any protection measures at client side."
msgstr ""

msgid "Allow external systems to query logs"
msgstr "Allow External Log Query"

msgid "Allowed Daily Volumn"
msgstr ""

msgid "Allowed HTTP Request Methods"
msgstr "Allowed HTTP Methods"

msgid "Allowed at most 30 characters."
msgstr "30 characters are allowed."

#, python-brace-format
msgid "Already enter emergency mode({reason}), bridge transparent"
msgstr "Already enter emergency mode({reason}), bridge pass-through"

#, python-brace-format
msgid "Already enter emergency mode({reason}), {modules} passthrough"
msgstr "Already enter emergency mode({reason}), {modules} pass-through"

msgid "Already exist"
msgstr "Already exists"

#, python-brace-format
msgid "Already have {0}"
msgstr ""

msgid "Already tried to activate. Please check the status later."
msgstr ""

msgid "Ambient light, distance when use the mobile phone"
msgstr "Ambient light and using distance"

msgid "An abnormal result is returned when registering the app."
msgstr ""

msgid "An abnormal result is returned when removing a registered app."
msgstr ""

msgid "An abnormal result is returned."
msgstr ""

msgid "An account access is greater than 1,000 times per minute"
msgstr "Same account called for more than 1,000 times in a minute."

msgid "An account uses an IP belonging to more than 3 provinces per day"
msgstr ""
"Same account called from IPs that are located in more than 3 provinces in a "
"day."

msgid ""
"An automated tool that automatically accesses links and generates a preview "
"of a content summary, such as a preview tool for a search engine."
msgstr ""

msgid "An automated tool that scraping valuable content from a website."
msgstr ""

msgid ""
"An automatic tool used to check service availability and prevent service "
"exceptions from affecting services. The tool is used to improve service "
"availability and stability."
msgstr ""

msgid "An empty textbox found in CSRF Path List."
msgstr "URL is required."

msgid "An empty textbox found in WAF White List!"
msgstr "Empty entry is not allowed."

#, python-brace-format
msgid "An empty textbox found in {0}."
msgstr "{0} cannot be empty."

msgid ""
"An invalid URL was detected in the protection list of the mobile "
"certificate."
msgstr ""

msgid "Analysis And Process"
msgstr "False Positive Analysis"

msgid "Analysis Content"
msgstr ""

msgid "Analysis Log"
msgstr ""

msgid ""
"Analysis completed. Please check the results in the Analysis Log below."
msgstr ""

msgid "Analysis failed:"
msgstr ""

msgid "Analyst"
msgstr ""

msgid "Analyzing logs and providing statistical reports (Sailfish)."
msgstr ""

msgid "And the last few characters of the feature is retained"
msgstr "and the last"

msgid "Android emulator device"
msgstr ""

msgid "Another User is importing, import again after a minute"
msgstr "Another user is importing, please try again later."

msgid "Anti Res_leech"
msgstr "Anti-Leech"

msgid "Any"
msgstr ""

msgid "Apache CouchDB Business Component"
msgstr ""

msgid "Apache CouchDB Business Component APIs Auto-detection"
msgstr "Auto-Recognize Apache-CouchDB-Business-Component APIs"

msgid "Apache Druid Business Component"
msgstr ""

msgid "Apache Druid Business Component APIs Auto-detection"
msgstr "Auto-Recognize Apache-Druid-Business-Component APIs"

msgid "Api Seq Rule Config Error"
msgstr ""

msgid "Api Seq Rule Repeated"
msgstr ""

msgid "Api Sequence Learning: start learning task"
msgstr "Calling Sequence Self-Learning: started learning"

msgid "Api Sequence Learning: stop learning task"
msgstr "Calling Sequence Self-Learning: stopped learning"

msgid "Api cant repeat"
msgstr ""

msgid "Api id and Api path cannot be empty"
msgstr "API ID and API path are required."

msgid "App"
msgstr "Application"

msgid "App Access Policy"
msgstr ""

msgid "App Blacklist Scene"
msgstr "App Blacklist Scenario"

msgid "App Fingerprint"
msgstr ""

msgid "App High Frequence Scene"
msgstr "App High Frequency Scenario"

msgid "App Manual Rule"
msgstr ""

msgid "App Reputation Scene"
msgstr "App Reputation Scenario"

msgid "App name"
msgstr ""

msgid "App package name"
msgstr ""

msgid "App site does not exist, please refresh app site list and try again."
msgstr ""
"Application Name does not exist, please refresh app site list and try again."

msgid "App site is already exists."
msgstr "Application Name already exists"

msgid "App site name already exists"
msgstr "Application Name already exists"

msgid "App verification code"
msgstr ""

msgid "App verification code|App package name|Platform|App name|Notes|Actions"
msgstr ""
"App verification code|App package name|Platform|App name|Notes|Operations"

msgid ""
"App verification code|App package name|Platform|App name|Source|Actions"
msgstr ""
"App verification code|App package name|Platform|App name|Source|Operations"

msgid "AppSite list"
msgstr "Application list"

msgid "Append"
msgstr ""

msgid "Appended"
msgstr "Appended "

msgid ""
"Applicable to Multiple nodes with support for Log Archiving Network Adapter."
msgstr ""
"Applicable to multiple-node deployment with support for separation of Log "
"Archiving Network Adapter."

msgid ""
"Applicable to scenarios where a dual-node high-availability solution is "
"required and network traffic is going based on routing. Each node in this "
"deployment forwards traffic to servers according to routing configurations "
"after it finishes data processing."
msgstr ""

msgid ""
"Applicable to scenarios where network traffic is going based on routing. The"
" cluster forwards traffic to servers according to routing configurations "
"after it finishes data processing."
msgstr ""

msgid ""
"Applicable to scenarios where there are no load balancers and high "
"reliability is required."
msgstr ""
"Applicable to scenarios where there are no load balancers and high "
"availability is required."

msgid "Applicable to single node with single network adapter."
msgstr ""

msgid "Application"
msgstr ""

msgid "Application Group Name"
msgstr ""

msgid "Application IP"
msgstr ""

msgid "Application Name"
msgstr ""

msgid "Application Path Prefix"
msgstr "Path Prefix"

msgid "Application Site"
msgstr "Websites"

#, python-brace-format
msgid "Application Site {0}"
msgstr "Bonding Websites to {0}"

msgid ""
"Application programming interfaces are pre-defined functions designed to "
"provide applications and developers with the ability to access a set of "
"routines based on certain software or hardware without having to access "
"source code or understand the details of internal working mechanisms. Simply"
" put, it is a way to read/write data through a predefined channel. Then "
"interface security becomes all the more important. The impact of using, "
"managing, and coordinating services on security must be understood. Poor "
"security of APIs might compromise the security issues of the entire project "
"including confidentiality, integrity, availability, and accountability."
msgstr ""

msgid ""
"Applications in development environments such as gitlab and nexus are "
"accessible on the Extranet, which may lead to code leakage or important "
"information leakage."
msgstr ""
"Applications in private networks such as gitlab nad nexus are found "
"accessable on Internet. It may lead to exposure of source code or important "
"information."

msgid "Apply"
msgstr ""

#, python-brace-format
msgid "Apply IP is not {0} to {1}"
msgstr ""

#, python-brace-format
msgid "Apply IP {0} to {1}"
msgstr ""

#, python-brace-format
msgid "Apply Method {0} to {1}"
msgstr ""

#, python-brace-format
msgid "Apply Protocol Version {0} to {1}"
msgstr ""

#, python-brace-format
msgid "Apply regular expression {0} to {1}"
msgstr ""

#, python-brace-format
msgid "Apply the mouse trace collection to {upstream_keys}."
msgstr ""

#, python-brace-format
msgid "Apply the regular expression {0} to the value of all {1} in {2}"
msgstr ""

#, python-brace-format
msgid "Apply the regular expression {0} to the value of {1} named {2} in {3}"
msgstr ""

msgid "Approach"
msgstr ""

msgid "Archived Log"
msgstr ""

msgid "Archived Log Filter"
msgstr ""

msgid "Archived Log has been Downloaded"
msgstr ""

msgid "Are sure you want to delete all custom dashboards and data models?"
msgstr ""

msgid "Are sure you want to use the statistics secret?"
msgstr ""

#, python-brace-format
msgid "Are you sure that you want to permanently delete configuration: {0}"
msgstr ""

msgid ""
"Are you sure to change the certificate password? The password is different "
"from the last time, and will make the app not work with the previous "
"certificates."
msgstr ""
"Changing certificate password will cause failures in use of Apps. Proceed "
"anyway?"

msgid "Are you sure to disable the current site configuration?"
msgstr "Are you sure you want to disable the website?"

msgid ""
"Are you sure to discard the existing settings and return to the default "
"values?"
msgstr "Are you sure you want to restore to factory settings?"

msgid "Are you sure to enable the current site configuration?"
msgstr "Are you sure you want to enable the website?"

msgid "Are you sure to end learning?"
msgstr "Are you sure you want to stop learning?"

msgid "Are you sure to install?"
msgstr "Are you sure you want to install it?"

#, python-brace-format
msgid "Are you sure to keep learning {0}?"
msgstr "Are you sure you want to keep learning on {0}?"

#, python-brace-format
msgid "Are you sure to relearning {0}?"
msgstr "Are you sure you want to restart learning on {0}?"

msgid "Are you sure to start learning?"
msgstr "Are you sure you want to start learning?"

#, python-brace-format
msgid "Are you sure you want to apply the changed role on node {0}?"
msgstr ""

#, python-brace-format
msgid "Are you sure you want to delete all the rules for path: {0} [{1}]?"
msgstr ""

msgid "Are you sure you want to delete it?"
msgstr ""

#, python-brace-format
msgid "Are you sure you want to delete strategy: {0}?"
msgstr ""

msgid "Are you sure you want to delete the item?"
msgstr ""

#, python-brace-format
msgid "Are you sure you want to delete the role {0}?"
msgstr ""

#, python-brace-format
msgid "Are you sure you want to delete the rule: {0}?"
msgstr ""

msgid "Are you sure you want to enable it?"
msgstr ""

msgid "Are you sure you want to generate a certificate?"
msgstr ""

msgid "Are you sure you want to remove this item from the registry?"
msgstr ""

msgid "Are you sure you want to reset the cluster IP address blacklist?"
msgstr ""

msgid "Are you sure you want to restore to rollback to the previous corpus?"
msgstr "Are you sure you want to roll back?"

msgid "Are you sure you want to restore to rollback to the previous ruleset?"
msgstr "Are you sure you want to roll back?"

msgid "Are you sure you want to resume using the original package?"
msgstr ""

msgid "Are you sure you want to rollback?"
msgstr ""

msgid "Are you sure you want to switch the selected website(s) to {0}?"
msgstr ""

msgid "Are you sure you want to undo all edits?"
msgstr ""

msgid "Argument Position"
msgstr "Parameter Position"

msgid "Arrangement"
msgstr ""

msgid "As username"
msgstr ""

msgid "Ask to Enable Cookie"
msgstr ""

msgid "Asset merge whitelist capacity exceeded, unable to add new entries."
msgstr ""

msgid "Associated Domain Name"
msgstr "Domain Name"

msgid ""
"At least one API Gateway role in the cluster is required to use the Capture "
"Agent function."
msgstr ""
"At least one node in the cluster needs to enable System API role to use the "
"Capture Agent function."

msgid "At least one IPv4 address is required when enable access WebConsole"
msgstr ""
"At least one IPv4 address is required when enable access WebConsole via IPv4"
" address"

msgid ""
"At least one IPv6 address is required when enable access WebConsole via IPv6"
" address"
msgstr ""

msgid "At least one ip address is required when enable access nginx"
msgstr ""

msgid ""
"At present, the system works in the traffic mirroring mode, and the "
"programmable countermeasure rules only monitor and cannot be intercepted."
msgstr ""

msgid "Attack Detection"
msgstr ""

msgid "Attack Event Details Collection Settings"
msgstr "Attack Details Logging"

msgid "Attack Name"
msgstr ""

msgid "Attack Policy ID"
msgstr ""

msgid "Attack Source"
msgstr "Source"

msgid "Attack Type"
msgstr ""

msgid "Attack logging threshold"
msgstr "Attack Logging Filter"

msgid "Attack type"
msgstr "Attack Type"

msgid "Attacked Target"
msgstr ""

msgid ""
"Attention: When a https site enable http/2 protocol, the other sites with "
"the same listen port will enable automatically; When you disable it, you "
"must do it in all https site with the same listen port."
msgstr ""
"Note: When HTTP/2 Protocol is enabled for one HTTPS site, it will be "
"automatically enabled for other sites with the same listening port. If you "
"want to disable it, you must disable it at all these sites."

msgid "Attenuation value"
msgstr "Fading Speed"

msgid "Auditor"
msgstr ""

msgid ""
"Auditor: Limited access rights, only partial system configuration can be "
"viewed, but cannot be modified."
msgstr ""

msgid "Aulnerability Attack"
msgstr "Vulnerability Attack"

msgid "Australia Passport Number"
msgstr "Australia Passport"

msgid "Australia Phone Number"
msgstr "Australia Mobile Phone"

msgid "Authentication Algorithm"
msgstr ""

msgid "Authentication Code"
msgstr ""

msgid "Authentication Data in URL Parameter"
msgstr ""

msgid "Authentication Mode"
msgstr "Authentication Method"

msgid "Authentication Password "
msgstr ""

msgid ""
"Authentication information such as token or SessionID is found in the URL. "
"The information in the URL will appear in the log and may also be sent to a "
"third party through referer. At the same time, the URL may also be seen by "
"the user on the browser page, resulting in the information being obtained by"
" others."
msgstr ""
"Authorization data such as tokens or session IDs included in URL parameters "
"may be obtained by third parties through logs, referers or browsers' address"
" bars."

msgid "Authentication mode can only be Remote or Local."
msgstr "Authentication method can only be Remote or Local."

msgid "Authentication succeeded on primary server."
msgstr ""

msgid "Authentication succeeded on secondary server."
msgstr ""

msgid "Authentication token"
msgstr "Authentication Token"

msgid "Authentication token has been updated"
msgstr ""

msgid "Authority"
msgstr "Assignments"

msgid "Authorization Scheme"
msgstr ""

msgid "Auto Added"
msgstr ""

msgid "Auto Refresh"
msgstr ""

msgid "Auto Remove Unused APIs"
msgstr ""

msgid "Auto detect websocket"
msgstr "Pass-Through All"

msgid "Auto recover failed."
msgstr ""

msgid "Auto recover timeout."
msgstr ""

msgid "Auto recovered."
msgstr ""

msgid "Auto update"
msgstr "Auto Update"

msgid "Auto-Blacklist"
msgstr ""

msgid "Auto-Blacklist Clear"
msgstr "Remove Auto-Blacklist"

msgid "Auto-Blacklist Statistics of Prevent Scanner"
msgstr "Auto-Blacklist Statistics of Block Scanning"

msgid "Auto-Blacklist Statistics of Sniping Scene"
msgstr "Auto-Blacklist Statistics of Sniping Scenario"

msgid "Auto_black_Accout_Hostname"
msgstr ""

msgid "Auto_black_account"
msgstr ""

msgid "Auto_black_fp"
msgstr ""

msgid "Auto_black_ip"
msgstr ""

msgid "Automated Tools Interception"
msgstr "Block Automated Tools"

msgid "Automated cleanup of inactive APIs is not enabled in settings."
msgstr ""

msgid ""
"Automated repeated clicking or requesting or submitting content, a ecting "
"application-based metrics such as counts and measures of frequency and/or "
"rate. The metric or measurement may be visible to users (e.g. betting odds, "
"likes, market/ dynamic pricing, visitor count, poll results, reviews) or "
"hidden (e.g. application usage statistics, business performance indicators)."
" Metrics may affect individuals as well as the application owner, e.g. user "
"reputation, influence others, gain fame, or undermine someone else's "
"reputation. For malicious alteration of digital advertisement metrics, see "
"OAT-003 Ad Fraud instead."
msgstr ""

msgid "Automatic Business Classification"
msgstr "Business Type (Auto)"

msgid "Automatic Industry Classification"
msgstr "Industry Classification"

msgid "Automatic calculation"
msgstr ""

msgid "Automatic installation"
msgstr "Install automatically"

msgid "Automatic recovery"
msgstr ""

msgid "Automatically close the debug page after 00:00:00 seconds."
msgstr ""

msgid "Automatically detect updates"
msgstr "Auto Update Cache"

msgid "Automatically exit developer mode after 00:00:00 seconds."
msgstr ""

msgid "Available Rollback LLM Corpus"
msgstr ""

msgid "Available Rollback Ruleset"
msgstr ""

msgid ""
"Available authenticator APP has [Google Authenticator], [Microsoft "
"Authenticator(Only SHA-1)] etc."
msgstr ""
"Authenticator apps supported include [Google Authenticator], [ShenfenBao], "
"etc."

msgid "BACKUP_HA"
msgstr ""

msgid "BTA"
msgstr ""

msgid "BTA Session"
msgstr ""

msgid "Back"
msgstr ""

msgid "Back to Lab"
msgstr ""

msgid "Backup Node"
msgstr "Backup Mode"

msgid "Bad Behavior"
msgstr "User Behavior"

msgid "Bad Behavior Scene"
msgstr "User Behavior Scenario"

msgid "Bank International Code"
msgstr ""

msgid "Base Data Decoder"
msgstr ""

msgid ""
"Based on the current network structure, select the IP layer or request "
"header X-Real-IP, X-Forwarded-For, or custom request header as the source "
"IP. You can add up to 10 rules and match the rules in order from top to "
"bottom."
msgstr ""
"Specify positions to obtain source IPs. It can be either inherited from "
"Global Settings or set by adding positions here. In the latter case, 10 "
"positions are allowed to be added at most. The system will be looking into "
"these positions in an order from top to bottom until it finds one."

msgid "Basic"
msgstr ""

msgid "Basic Details"
msgstr ""

msgid "Basic Service (5x8 hours)"
msgstr ""

msgid "Basic Settings"
msgstr ""

msgid "Basic equipment information"
msgstr ""

msgid "Basic information such as app name/installation time"
msgstr ""

msgid "Basic settings"
msgstr "Basic Settings"

msgid "Basic+Advance"
msgstr ""

msgid "Basic+Advance+Debug"
msgstr ""

msgid "Batch Set Validity"
msgstr ""

msgid "Batch editing"
msgstr "Batch Edit"

msgid "Batch use of parameter self-learning results:"
msgstr "Parameter Self-Learning: batch used "

msgid ""
"Be caution with the option Web Protection. The data buffered at client end "
"might cause issues when visiting websites after the system exits emergency "
"mode."
msgstr ""

msgid ""
"Be cautious of the use of option All Transparent. The data buffered at "
"client end might cause issues when visiting websites after the system exits "
"emergency mode."
msgstr ""
"Be cautious of the use of option All Transparent when any protected sites "
"have enabled Web Protection. In that case, the data buffered at client end "
"might cause issues when visiting websites after the system exits emergency "
"mode."

msgid "Before 30 days"
msgstr ""

msgid "Before 60 days"
msgstr ""

msgid "Begin to rollback"
msgstr ""

msgid ""
"Begin to upgrade patch, upgrade results are subject to actual conditions"
msgstr ""
"Began to update the system with update result pending on actual conditions"

msgid "Behavior"
msgstr ""

msgid "Between Client and DAP"
msgstr "Between Client and System"

msgid "Between DAP and Server"
msgstr "Between System and Server"

msgid "Big Screen Display"
msgstr "Large Screen"

msgid "Big Screen Editor"
msgstr ""

msgid "Big data analysis P (query)"
msgstr ""

msgid "Big data analysis P (storage)"
msgstr ""

msgid "Big data analysis S (query)"
msgstr "Big Data Analysis S (Query)"

msgid "Big data analysis S (storage)"
msgstr "Big Data Analysis S (Storage)"

msgid "Binding Type"
msgstr "Bonding Type"

msgid "Binding network port"
msgstr "Bonding Port"

msgid "Birth Certificate"
msgstr ""

msgid "Birthday"
msgstr ""

msgid "Blacklist"
msgstr ""

msgid "Blacklist Scene"
msgstr "Blacklist Scenario"

msgid "Blacklist of Prompt Words"
msgstr "Prompt Blacklist"

msgid "Blank"
msgstr "Blank page"

msgid "Blank means all clients"
msgstr ""

msgid "Blank means all ports"
msgstr ""

msgid "Blank means all servers"
msgstr ""

msgid "Blank means not configured"
msgstr ""

msgid "Block"
msgstr ""

msgid "Block Bot Tools"
msgstr ""

msgid "Block Code"
msgstr "Status Code"

msgid "Block Counts"
msgstr ""

msgid "Block Mode"
msgstr ""

msgid "Block PHP Code"
msgstr "Any Executable PHP"

msgid "Block Statistics"
msgstr ""

msgid "Block note on Response"
msgstr "Featured Response for Abnormal Request"

msgid "Block note on response has been set."
msgstr "Error Message in Response has been set"

msgid "Block: stop the request and send response to the client"
msgstr ""

msgid "Blocked Requests"
msgstr ""

msgid "Blocking Period"
msgstr ""

msgid "Blocking action"
msgstr ""

msgid "Blocking on Mirror Deployment"
msgstr ""

msgid "Blood Type"
msgstr ""

msgid "Bond mode is for business ports"
msgstr "Bonding is for External Network Adapters"

msgid "Bot List"
msgstr ""

msgid "Bot Name"
msgstr "Name"

msgid "Bot Tools Recognition: Modified Advanced Persistent Bot"
msgstr ""

msgid "Bot Tools Recognition: Modified Block Bot Tools"
msgstr ""

msgid "Bot feature detection"
msgstr "Bot Feature Detection"

msgid "Bot type(bctN)"
msgstr ""

msgid "Bot/Scanner"
msgstr ""

msgid "Bot_Check"
msgstr ""

msgid ""
"Both IPv4 and IPv6 addresses can be provided in CIDR (IPv6 addresses in "
"mixed notation are not supported)."
msgstr ""

msgid ""
"Both of Ajax/Fetch referer list and Ajax/Fetch response encryption list "
"should be configured to enable Ajax/Fetch response encryption feature, "
"continue?"
msgstr ""
"Both Ajax/Fetch Referer and Ajax/Fetch Response should be configured to "
"enable Ajax/Fetch Response Obfuscation feature. Continue?"

msgid "Bridge Transparent"
msgstr "Bridge Pass-Through"

msgid "Browser Proxy Detection"
msgstr ""

msgid "Browser Supported"
msgstr ""

msgid "Browser not supported."
msgstr ""

msgid "Brute Force Protection"
msgstr "Brute Force"

msgid ""
"Brute force attack against application payment card processes to identify "
"the missing values for start date, expiry date and/or card security code "
"(CSC), also referred to in many ways, including card validation number 2 "
"(CVN2), card validation code (CVC), card verification value (CV2) and card "
"identification number (CID). When these values are known as well as the "
"Primary Account Number (PAN), OAT-001 Carding is used to validate the "
"details, and OAT-012 Cashing Out to obtain goods or cash."
msgstr ""

msgid ""
"Brute force, dictionary (word list) and guessing attacks used against "
"authentication processes of the application to identify valid account "
"credentials. This may utilise common usernames or passwords, or involve "
"initial username evaluation. The use of stolen credential sets (paired "
"username and passwords) to authenticate at one or more services is OAT-008 "
"Credential Stuffing."
msgstr ""

msgid "Build"
msgstr ""

msgid "Build:"
msgstr ""

msgid "Built in certificate"
msgstr "Built-in certificate"

msgid ""
"Bulk account creation, and sometimes profile population, by using the "
"application's account sign-up processes. The accounts are subsequently "
"misused for generating content spam, laundering cash and goods, spreading "
"malware, a ecting reputation, causing mischief, and skewing search engine "
"optimisation (SEO), reviews and surveys. Account Creation generates new "
"accounts - see OAT-007 Credential Cracking and OAT-008 Credential Stuffing "
"for threat events that use existing accounts."
msgstr ""

msgid "BurpSuite"
msgstr ""

msgid "Burst"
msgstr ""

msgid "Business 1 statistics(butype1)"
msgstr ""

msgid "Business 2 statistics(butype2)"
msgstr ""

msgid "Business 3 statistics(butype3)"
msgstr ""

msgid "Business Attack"
msgstr ""

msgid "Business Attacks"
msgstr ""

msgid "Business Classification"
msgstr "Business Type"

msgid "Business Data Collection"
msgstr ""

msgid "Business License Registration Number"
msgstr "Business License Number"

msgid "Business Name"
msgstr ""

msgid "Business Path"
msgstr ""

msgid "Business Threat Awareness"
msgstr ""

msgid ""
"Business Threat Awareness is a functionality used to analyze and evaluate "
"security status of websites based on advanced Internet security "
"technologies."
msgstr ""
"BTA is an AI-powered feature used to analyze and evaluate security status of"
" websites."

msgid "Business Threat Awareness session"
msgstr "BTA Session"

msgid "Business Threat Awareness(Application)"
msgstr ""

msgid "Business Threat Awareness(Business)"
msgstr ""

msgid "Business Transparent"
msgstr "Module Pass-Through"

msgid "Business Type"
msgstr ""

msgid "Business Type Keys(butype):"
msgstr ""

msgid "Business Type Whitelist"
msgstr "Whitelist for Auto Recognition of Business Type"

msgid "Business failed user set(btb)"
msgstr ""

msgid "Business max requests frequency(btf)"
msgstr ""

msgid ""
"Business name cannot exceed 16 characters in Chinese or 16 characters in "
"English."
msgstr ""

msgid "Business process settings"
msgstr ""

msgid "Business process settings: Off"
msgstr "Disabled Business process settings"

msgid "Business process settings: On"
msgstr "Enabled Business process settings"

msgid "Business success count"
msgstr ""

msgid "Business success keywords"
msgstr "Business Success Keywords"

msgid "Business success user set(btn)"
msgstr ""

msgid "Business type ID(btid)"
msgstr ""

msgid "Business type does not exists!"
msgstr ""

msgid "Busniess total requests count(brc)"
msgstr ""

msgid "Busniess total requests(btc)"
msgstr ""

msgid ""
"By excluding specific paths or restricting specific paths, the system "
"specifies which Ajax/Fetch requests are added URL tokens."
msgstr ""
"The settings below are used to specify the paths to which Ajax/Fetch "
"requests should or should not be attached with URL tokens"

msgid "Bypass Transparent"
msgstr "Network Card Bypass"

msgid "Bypass/Bridge Settings"
msgstr ""

msgid "CAPTCHA Defeat(OAT-009)"
msgstr ""

msgid "CC Attack Protection"
msgstr "CC Attack"

msgid "CPU"
msgstr ""

msgid "CPU Cores"
msgstr ""

msgid "CPU Model"
msgstr ""

msgid "CPU Overload"
msgstr ""

msgid "CSRF"
msgstr ""

msgid "CSV file"
msgstr "CSV File"

msgid "CTI engine version"
msgstr "Engine Version"

msgid "Cache clear failed."
msgstr ""

msgid "Cache cleared."
msgstr ""

msgid "Cache disk space"
msgstr "Max Cache Space"

msgid "Cache expiration time"
msgstr "Cache Expires Every"

msgid "Cache path"
msgstr "Pages to be Cached"

msgid "Call Order"
msgstr "Calling Order"

msgid "Call Time"
msgstr "Called at"

msgid "Call order"
msgstr "Calling Sequence"

msgid "Call stack detection"
msgstr ""

msgid "Calling-Order Self-Learning"
msgstr "Calling Sequence Self-Learning"

msgid "Calling-Order Self-Learning Configuration modified successfully."
msgstr "Calling Sequence Self-Learning modified successfully"

msgid "Calls during off-hours"
msgstr "Calls in Off-Hours"

msgid "Calls from one IP is greater than 1,000 per minute"
msgstr "Same IP called for more than 1,000 times in a minute."

msgid "Can not get user authority list"
msgstr ""

msgid "Can only contain letters, numbers, Chinese and underscores"
msgstr ""

msgid "Can't change builtin rule."
msgstr ""

msgid "Can't launch sniffer because of insufficient system space"
msgstr ""

msgid "Canadian Landline/Phone Number"
msgstr "Canada Telephone/Mobile Phone"

msgid "Cancel"
msgstr ""

msgid "Cannot connect to master node"
msgstr ""
"Cannot connect to master node. Possible reason: [Cluster Security "
"Enhancement] function is enabled in the cluster."

msgid "Cannot connect to master node."
msgstr ""

msgid ""
"Cannot connect to the upgrade site, please check the network configuration "
"is normal."
msgstr ""

msgid "Cannot delete inbuilt risk."
msgstr ""

msgid "Cannot load public key."
msgstr ""

msgid "Cannot rollback because API Role changed"
msgstr ""

msgid ""
"Cannot rollback because NIC configuration or cluster nodes has been changed."
msgstr ""
"Cannot rollback because NIC configurations or cluster nodes have been "
"changed."

msgid "Cant find the field {} in the filter."
msgstr ""

msgid "Cant find the resource file"
msgstr ""

msgid "Cant find the resource waf file"
msgstr ""

msgid "Cant find the {} name resource file"
msgstr ""

msgid "Cant find the {} name resource waf file"
msgstr ""

msgid "Cant find the {} resource file: {}"
msgstr ""

msgid "Cant find the {} resource waf file: {}"
msgstr ""

msgid "Cant get the infomation of server"
msgstr ""

msgid "Captcha Templates"
msgstr "CAPTCHA Templates"

msgid "Capture Agent"
msgstr ""

msgid "Capture Agent Token"
msgstr ""

msgid "Capture command"
msgstr ""

msgid "Capture non-HTTP protocol traffic"
msgstr "Capture Non-HTTP Traffic"

msgid "Capture packets from all nodes in the cluster"
msgstr "Capture packets of all nodes"

msgid "Capture time"
msgstr "Capture time (seconds)"

msgid "Capturing packets"
msgstr ""

msgid "Card Cracking(OAT-010)"
msgstr ""

msgid "Card Validity period"
msgstr "Card Validity Period"

msgid "Card Verification Code"
msgstr ""

msgid "Carding(OAT-001)"
msgstr ""

msgid ""
"Caution! Changing the time of the system may lead to cluster malfunction."
msgstr ""

msgid "Center"
msgstr ""

msgid "Centralized License Server"
msgstr ""

msgid ""
"Centralized authorization License file need to active in centralized "
"authorization server"
msgstr ""

msgid "Certificate Confirm Password"
msgstr "Confirm Password"

msgid "Certificate Encrypted Password"
msgstr ""

msgid "Certificate Password"
msgstr ""

msgid "Certificate Validity Period"
msgstr ""

msgid "Certificate file"
msgstr ""

msgid "Certificate file has been uploaded."
msgstr ""

msgid "Certificate type"
msgstr "Certificate Type"

msgid "Challenge"
msgstr ""

msgid "Challenge Policy"
msgstr ""

msgid "Challenge Verification Strategy"
msgstr ""

msgid ""
"Challenge the IP that has not passed the challenge until the challenge is "
"successful."
msgstr ""

msgid "Challenge: return reCAPTCHA(with Dynamic Challenge open)"
msgstr "Challenge: return CAPTCHA page (Dynamic Challenge must be enabled)"

msgid "Change"
msgstr ""

msgid "Change BTA configuration"
msgstr "Modified BTA settings"

msgid "Change Password"
msgstr ""

msgid "Change business data collection priority successful"
msgstr ""

msgid "Change business data collection successful"
msgstr ""

msgid "Change log"
msgstr ""

msgid "Change node role"
msgstr ""

msgid "Change the detect direction will clear the settings,are you sure?"
msgstr ""
"Changing target will reset current settings to default. Proceed anyway?"

msgid ""
"Change the time manually or specify a NTP server to update the time "
"automatically."
msgstr ""

msgid "Change user password: "
msgstr ""

msgid "Changed Keepalived state"
msgstr "Changed Keepalived state"

msgid "Changed LogSender State"
msgstr "Changed Logsender state"

msgid "Changed {} priority"
msgstr ""

msgid "Changes not saved will be lost. Sure to continue?"
msgstr ""

msgid ""
"Changing the time of the system may lead to cluster malfunction. Proceed "
"anyway?"
msgstr ""

msgid "Channel Number"
msgstr ""

msgid "Character"
msgstr ""

msgid "Character set type"
msgstr "Character Name"

msgid "Charge requests count(bcc)"
msgstr ""

msgid "Check All"
msgstr "Enable All"

msgid "Check Free Memory"
msgstr ""

msgid "Check Path"
msgstr ""

msgid "Check User-Agent"
msgstr ""

msgid "Check for updates"
msgstr "Check for Updates"

msgid "Check log only on Proxy Node"
msgstr ""

msgid "Check the box to disable this account."
msgstr ""

msgid ""
"Check whether the interface has horizontal permission verification, increase"
" the complexity of traversable parameters, and avoid using parameters that "
"are easy to guess, such as short numbers; monitor interface access to "
"promptly discover the risk of batch traversal of data through the interface."
msgstr ""
"Enable horizontal authentication and increase complexity of iterable "
"parameters, e.g., use longer numbers."

msgid "Check your network connection or contact Technical Support"
msgstr ""

msgid "Checked All"
msgstr "Select All"

msgid "China"
msgstr ""

msgid "China Security Cipher"
msgstr ""

msgid "China UnionPay Bank Card Number"
msgstr "China UnionPay Card Number"

msgid "China UnionPay Card"
msgstr ""

msgid "China UnionPay Debit Card"
msgstr ""

msgid "ChinaMobile Mini Program Protection"
msgstr "ChinaMobile Mini-Program"

msgid "Chinese Address"
msgstr ""

msgid "Chinese Interception Response"
msgstr ""

msgid ""
"Chinese Interception Response cannot be empty. In addition to Chinese words,"
" the characters allowed include numbers, letters, whitespaces, dots(.), "
"underscores(_), dashes, parentheses, asterisks, plus, as well as commas, "
"periods, exclamation marks, colons, semicolons and quote marks in Chinese or"
" English."
msgstr ""

msgid "Chinese Name"
msgstr ""

msgid "Choose Prompt Injection Protection strategy"
msgstr ""

msgid "Choose LLM Sensitive Information Protection strategy"
msgstr ""

msgid "City"
msgstr ""

msgid "City count(ds_city)"
msgstr ""

msgid "City count(lct)"
msgstr ""

msgid "Classification"
msgstr "Categorization"

msgid "Clean app data count(ucc)"
msgstr ""

msgid "Clear"
msgstr ""

msgid "Clear "
msgstr ""

msgid "Clear All"
msgstr "Disable All"

msgid "Clear IP Blacklist"
msgstr "Clear IP Blacklist"

msgid "Clear IP Whitelist"
msgstr "Clear IP Whitelist"

msgid "Clear all "
msgstr "Remove all"

msgid "Clear cache"
msgstr "Clear Cache"

msgid "Cleared"
msgstr "Cleared "

msgid "Clearing Amount"
msgstr ""

msgid "Click \"Add Websites\" to configure websites that need protection."
msgstr "Click \"Add a Website\" to configure a website that needs protection."

msgid "Click here to login and download"
msgstr "Click here to download the update package"

msgid "Click of ad"
msgstr "Click Ad"

msgid "Click the button above to add the counter"
msgstr "Click [Add Counter] above to create a counter"

msgid "Click the right button to refresh the list below."
msgstr ""

msgid "ClickHouse Business Component"
msgstr ""

msgid "ClickHouse Business Component APIs Auto-detection"
msgstr "Auto-Recognize ClickHouse-Business-Component APIs"

msgid "Clickhouse Server"
msgstr ""

msgid ""
"Clicking \"Register\" of the corresponding Apps in the following list after "
"completing integrity checking."
msgstr ""
"Click \"Register\" of verified apps in the following list to add the apps to"
" the above app list."

msgid "Client Environment Data Channel"
msgstr "Environment Data Pass-Through"

msgid "Client IP"
msgstr ""

msgid "Client IP Count"
msgstr ""

msgid "Client IP Top 100"
msgstr ""

msgid "Client IP number:"
msgstr "Source IPs:"

msgid "Client Injection Attack Detection"
msgstr "Client-End Injection Detection"

msgid "Client Port"
msgstr ""

msgid "Client and server should be enabled, in dual link mode"
msgstr ""

msgid "Client or server should be enabled first"
msgstr ""

msgid "Client porxy requests count(cpc)"
msgstr ""

msgid "Client to Server"
msgstr ""

msgid "Close"
msgstr "Close"

msgid "Close API Args Valid just Monitor"
msgstr "Disabled Parameter Detection in Monitor Mode"

msgid "Close Ask to Enable Cookie"
msgstr "Disabled Ask to Enable Cookie"

msgid "Close Block note on Resonse"
msgstr "Disabled Error Message in Response"

msgid "Close GM Algorithm"
msgstr "Disabled GM Algorithm"

msgid "Close Slow Http Attack Protect"
msgstr "Disabled Slow Http Attack Protection"

msgid "Close Strictly Match Host Header"
msgstr "Disabled Strictly Match Host Header"

msgid "Close Web Protection Token Advanced Scheme"
msgstr ""

msgid "Close automatically merge applications by port"
msgstr "Disabled Automatically Merge Applications by Port"

msgid "Close business data collection"
msgstr "Disabled business data collection"

msgid "Close to re-select"
msgstr ""

msgid "Closed"
msgstr "Closed"

msgid "Closing the pop-up will stop the analysis process. Confirm to close?"
msgstr ""

msgid "Cloud Evaluation"
msgstr ""

msgid "Cluster"
msgstr ""

msgid "Cluster Deployment"
msgstr "Cluster"

msgid "Cluster Fingerprint"
msgstr ""

msgid "Cluster IP"
msgstr ""

msgid "Cluster IP whitelist cannot be blank"
msgstr "Cluster IP Whitelist is required."

msgid "Cluster IP whitelist |"
msgstr "Cluster IP Whitelist |"

msgid "Cluster Info has been obtained"
msgstr ""

msgid "Cluster Name"
msgstr ""

msgid "Cluster Node List"
msgstr ""

msgid "Cluster Password"
msgstr ""

msgid "Cluster cannot connect to centralized authorization server"
msgstr ""

msgid "Cluster deployment has two outstanding advantages:"
msgstr ""

msgid "Cluster fingerprint has changed"
msgstr ""

msgid "Cluster management error"
msgstr ""

msgid "Cluster name only support numbers, letters and Chinese."
msgstr ""

msgid "ClusterNodeDisconnected"
msgstr "Cluster node disconnected"

msgid "ClusterVersionInconsistent"
msgstr "Inconsistent node versions"

msgid "Code"
msgstr ""

msgid "Collapse"
msgstr ""

msgid "Collect Client Features"
msgstr "Client Feature Collection"

msgid "Collect Client Info"
msgstr ""

msgid "Collect Header without Cookie/User-Agent/Host/Referer"
msgstr "Headers (excluding Cookie/User-Agent/Host/Referer)"

msgid "Collect all Attack"
msgstr "All"

msgid "Collect all Cookie"
msgstr "Cookies"

msgid "Collect all Post"
msgstr "POST Bodies"

msgid "Collect and analyze the flow through the web server."
msgstr "Collecting and analyzing the traffic going to the web servers."

msgid "Collect by per second"
msgstr "Sampling per Second"

msgid ""
"Collect information such as device battery level, screen size and screen "
"brightness from the mini-program client."
msgstr ""

msgid "Collect mode"
msgstr "Logging Mode"

msgid "Collect per second"
msgstr "Sampling"

msgid "Collect response body"
msgstr "Response Bodies"

msgid "Collect response log body"
msgstr "Include Response Body"

msgid ""
"Collecting accessible data and/or processed output from the application. "
"Some scraping may use fake or compromised accounts, or the information may "
"be accessible without authentication. The scraper may attempt to read all "
"accessible paths and parameter values for web pages and APIs, collecting the"
" responses and extracting data from them. Scraping may occur in real time, "
"or be more periodic in nature. Some Scraping may be used to gain insight "
"into how it is constructed and operates - perhaps for cryptanalysis, reverse"
" engineering, or session analysis. When another application is being used as"
" an intermediary between the user(s) and the real application, see OAT-020 "
"Account Aggregation. If the intent is to obtain cash or goods, see OAT-012 "
"Cashing Out instead."
msgstr ""

msgid ""
"Collecting and archiving logs from servers of Dynamic Application "
"Protection. Only 1 node of Log Archiving is allowed in a cluster."
msgstr ""
"Collecting and archiving unformatted logs from servers of Dynamic "
"Application Protection. Only one log archiving node is allowed in a cluster."

msgid "Collection Path"
msgstr ""

msgid "Collection field length"
msgstr "Field Collection Length"

msgid ""
"Collection length must be a positive integer bigger than 0 and less than "
"65536."
msgstr ""

msgid "Collection options"
msgstr "Data Collection"

msgid "Combine fields"
msgstr ""

msgid "Command Execution"
msgstr ""

msgid "Command Line"
msgstr ""

msgid "Command line page"
msgstr ""

msgid "Comment"
msgstr "Notes"

#, python-brace-format
msgid "Comment cannot exceed {0} characters"
msgstr ""

msgid "Common Keys(commonKeys)"
msgstr ""

msgid "Common Vulnerabilities"
msgstr "CVE Vulnerabilities"

msgid "Communicating with primary authentication server"
msgstr ""

msgid "Communicating with secondary authentication server"
msgstr ""

msgid "Communication with primary authentication server failed."
msgstr ""

msgid "Community"
msgstr ""

msgid "Company"
msgstr ""

msgid "Company Name"
msgstr ""

msgid "Compare with the Original"
msgstr ""

msgid "Compatibility time"
msgstr "Buffer Time"

msgid "Compatible Mode"
msgstr ""

msgid "Complete path, e.g. /a/b/c"
msgstr ""

msgid ""
"Completely Automated Public Turing test to tell Computers and Humans Apart "
"(CAPTCHA) challenges are used to distinguish normal users from bots. "
"Automation is used in an attempt to analyse and determine the answer to "
"visual and/or aural CAPTCHA tests and related puzzles. Apart from "
"conventional visual and aural CAPTCHA, puzzle solving mini games or "
"arithmetical exercises are sometimes used. Some of these may include "
"context-specific challenges. The process that determines the answer may "
"utilise tools to perform optical character recognition, or matching against "
"a prepared database of pre-generated images, or using other machine reading,"
" or human farms."
msgstr ""

msgid "Compliance Detection"
msgstr "Compliance Validation"

msgid "Compress system logs for download."
msgstr ""

msgid "Compulsory challenge success"
msgstr ""

msgid "Condition"
msgstr "Trigger"

msgid "Config Custom UA"
msgstr "Specify UA"

msgid "Configuration"
msgstr ""

msgid "Configuration APIs Auto-detection"
msgstr "Auto-Recognize Configuration APIs"

msgid "Configuration changed for blocking on mirror deployment"
msgstr ""

#, python-brace-format
msgid "Configuration error ({0}): "
msgstr ""

msgid "Configuration exception"
msgstr ""

msgid "Configuration has a syntax error."
msgstr ""

msgid "Configuration saved successfully"
msgstr ""

msgid "Configuration should be"
msgstr ""

msgid ""
"Configure APP Interested UI widget IDs to assist in identifying the trigger "
"source of the request. Widget IDs are separated by comma in English. Each "
"path supports a maximum of 10 control IDs."
msgstr ""
"Specify app UI widget IDs that can help identify the trigger of requests. "
"IDs must be separated by commas, and a maximum of 10 IDs are allowed for "
"each path."

msgid "Configure Intranet Address"
msgstr "[Configure Private Address]"

msgid "Configure extensions for static resource files, separated by commas."
msgstr ""
"Enter extensions of the files that do not need protection. Use commas to "
"separate them."

msgid "Configure parsed access log output in other clusters, Servers:"
msgstr ""
"Do not forget to add the following server(s) into the entry Transmit "
"Formatted Access Log of the cluster that sends logs."

msgid ""
"Configure the URL of the external website that is allowed to access the "
"protected websites. Regular expressions are supported. A maximum of 256 "
"characters are allowed."
msgstr ""

msgid ""
"Configure the maximum number of HTTP requests per second that a single "
"traffic collection node can handle, and the excess number will be discarded."
" Integer (1-30000)"
msgstr ""
"Maximum number of HTTP requests processed by a single Traffic Capturing node"
" per second. Excessive requests will be discarded. Integer 1-30000."

msgid ""
"Configure the maximum number of network packets that a single traffic "
"collection node can process per second, and the excess number will be "
"discarded. Integer (1-2048)"
msgstr ""
"Maximum number of network packets processed by a single Traffic Capturing "
"node per second. Excessive packets will be discarded. Integer 1-2048."

msgid ""
"Configure the upper limit of network traffic per second for a single traffic"
" collection node, and the excess traffic will be discarded. "
"Integer(1-100000)"
msgstr ""
"Maximum traffic processed by a single Traffic Capturing node per second. "
"Excessive traffic will be discarded. Integer 1-100000."

msgid "Configure traffic restriction for each source host"
msgstr "Source host traffic threshold"

msgid "Confirm Password"
msgstr ""

msgid "Confirm to add"
msgstr ""

msgid "Confirm to add the WAF whitelist configured as follows:"
msgstr ""
"Are you sure you want to add the WAF whitelist configured as flollows:"

#, python-brace-format
msgid "Confirm to delete the of {0}: {1}?"
msgstr "Are you sure you want to delete {0}: {1}?"

#, python-brace-format
msgid "Confirm to delete the strategy of {0}: {1}?"
msgstr "Are you sure you want to delete the strategy of {0}: {1}?"

#, python-brace-format
msgid "Confirm to delete the strategy: {0}?"
msgstr "Are you sure you want to delete the strategy: {0}?"

msgid "Confirm to delete this rule?"
msgstr "Are you sure you want to delete this rule?"

msgid "Confirm to delete?"
msgstr "Are you sure you want to delete?"

msgid "Conflict with WebConsole"
msgstr ""

msgid "Congratulations! The system has been activated successfully!"
msgstr ""

msgid "Connecting Internet/load balancer and internal servers."
msgstr "Connecting Internet/load balancer and upstream servers."

msgid "Connecting Internet/load balancer."
msgstr ""

msgid "Connecting internal servers."
msgstr "Connecting upstream servers."

msgid ""
"Connecting protection system WebConsole (IPv4 Only), Internet/load balancer "
"and internal servers simultaneously."
msgstr ""
"Connecting to the WebConsole (IPv4 Only), Internet/load balancer and "
"upstream servers."

msgid "Connecting the protection system WebConsole."
msgstr "Connecting the WebConsole."

msgid "Connection Overload"
msgstr ""

msgid "Connection Timeout"
msgstr "Timeout"

msgid "Connection error."
msgstr ""

msgid "Connections"
msgstr ""

msgid "Contact administrator to reset password."
msgstr ""

msgid "Contain any"
msgstr "Contain Any"

#, python-brace-format
msgid "Contains duplicate paths: {0}."
msgstr ""

msgid "Contains the same configuration items"
msgstr ""

#, python-brace-format
msgid "Contains {0}"
msgstr ""

msgid "Content Crawling"
msgstr "Data Scraping"

msgid "Content Keyword"
msgstr ""

msgid "Content Preview"
msgstr "Webpage Preview"

msgid "Content Type"
msgstr ""

msgid "Content Type URL List exceed 256"
msgstr ""

msgid "Content Type URL cannot be blank"
msgstr "Content Type URL is required."

msgid "Content extractor"
msgstr "Data Scraping"

msgid "Content-Type Correction"
msgstr "Correct Content-Type"

msgid ""
"Continue to apply the actions configured in the \"Execution Strategy\" above"
" for a set time period."
msgstr "Continue to apply the above policy for a set time period."

msgid "Continuous Blocking"
msgstr "Keep "

msgid "Contract Amount"
msgstr ""

msgid "Contract Number"
msgstr ""

msgid ""
"Control the data to be collected by SDK by clicking each collection point "
"option."
msgstr "Specify the data to be collected by the SDK"

msgid "Cookie"
msgstr ""

msgid "Cookie Count:"
msgstr ""

msgid "Cookie Encryption"
msgstr "Cookie Encryption"

msgid "Cookie ID set(cic)"
msgstr ""

msgid "Cookie Length:"
msgstr ""

msgid "Cookie Name"
msgstr ""

msgid "Cookie Names"
msgstr "Cookie Name"

msgid "Cookie Signature"
msgstr ""

msgid "Cookie Sticky"
msgstr ""

msgid "Cookie Tamper Protection"
msgstr "Cookie Security"

msgid "Cookie Token Error"
msgstr ""

msgid "Cookie changed count(ds_cookie_count)"
msgstr ""

msgid "Cookie name cannot be empty."
msgstr "Cookie name is required."

msgid "Copied"
msgstr "Copied."

#, python-brace-format
msgid "Copy Strategy For {0}"
msgstr ""

msgid "Core dump"
msgstr ""

msgid "Coredump"
msgstr ""

msgid "Coredump Overload"
msgstr ""

msgid "Corporate ID"
msgstr ""

msgid "Corporate ID "
msgstr ""

msgid "Could not delete role used by user."
msgstr ""

msgid "Count"
msgstr ""

msgid "Count of current IP / Count of total hit"
msgstr ""

msgid "Count of current payload / Count of total hit"
msgstr ""

msgid "Count of unique IPs"
msgstr "IP Count"

msgid "Count of unique accounts"
msgstr "Number of Accounts"

msgid "Count of unique cities"
msgstr "Number of Cities"

msgid "Count of unique provinces"
msgstr "Number of Provinces"

msgid "Counter"
msgstr ""

msgid "Counter Name"
msgstr ""

msgid "Counter Scene"
msgstr "Counter Scenario"

msgid "Counter of Programmable Defending"
msgstr ""

msgid "Country"
msgstr ""

msgid "Coupon verification"
msgstr "Coupon Verification"

msgid "Cover"
msgstr ""

msgid "Crack"
msgstr ""

msgid "Crack Behaviors Interception"
msgstr "Block Cracking"

msgid "Crash Path"
msgstr ""

msgid "Create"
msgstr ""

msgid "Create API and merge subpath API"
msgstr "Added one API and merged existing one(s)"

msgid "Create Global Custom Rule"
msgstr "Create Global Custom Rule"

msgid "Create New Role"
msgstr ""

msgid "Create Rule"
msgstr ""

msgid "Create Strategy"
msgstr ""

msgid "Create a Cluster"
msgstr ""

msgid "Create a Primary Node"
msgstr ""

msgid "Create a Primary or Standby Node"
msgstr ""

msgid "Create a Standby Node"
msgstr ""

msgid "Create a new cluster which more nodes could join later."
msgstr "Create a new cluster for other nodes to join later."

msgid "Create at least one rule."
msgstr ""

msgid "Create ignore API and delete subpath API"
msgstr "Added one API to the Ignore List while deleting sub-path API(s)"

msgid "Create or Join a Cluster"
msgstr ""

#, python-brace-format
msgid "Create strategy of {0}"
msgstr "Create Strategy of {0}"

msgid "CreateAndDelete"
msgstr "Add"

msgid "CreateAndMerge"
msgstr "Add"

msgid "CreateOnly"
msgstr "Add"

msgid "Created at"
msgstr ""

msgid "Credential Cracking(OAT-007)"
msgstr ""

msgid "Credential Stuffing(OAT-008)"
msgstr ""

msgid "Credibility"
msgstr "Credibility"

msgid "Credit Card"
msgstr ""

msgid "Credit card CV2"
msgstr "Credit Card CV2"

msgid "Credit card number"
msgstr "Credit Card Number"

msgid "Credit card valid date"
msgstr "Credit Card Expiration Date"

msgid "Critical"
msgstr ""

msgid "CtiDb Upgrade"
msgstr "CTI Updates"

msgid "CtiDb upgrade package"
msgstr "Intelligence update package "

msgid "CtiDb version"
msgstr "Database Version"

msgid "Current Admin IP"
msgstr ""

msgid "Current Date & Time:"
msgstr ""

msgid "Current Fingerprint"
msgstr ""

msgid "Current Status"
msgstr "Status"

msgid ""
"Current activated Master Node number is less than half of the total Master "
"Node number"
msgstr ""

msgid "Current application strategy ID"
msgstr "Strategy ID"

msgid "Current application strategy name"
msgstr "Strategy Name"

msgid ""
"Current cluster fingerprint has changed and its reliability has reduced. "
"Please renew the license ASAP."
msgstr ""

#, python-brace-format
msgid "Current gateway of the system: {0}"
msgstr ""

#, python-brace-format
msgid "Current license engine version: {0}"
msgstr ""

msgid ""
"Current mpp count is out of license limited. Overrun mpp that are ranked "
"lower in the list will not be protected."
msgstr ""
"Current mini-program count has exceeded license limit. Mini-Program at the "
"bottom of the list will not be protected."

msgid "Current node cannot connect to Master Node"
msgstr ""

msgid "Current node is not working properly."
msgstr ""

msgid ""
"Current regular expression library is similar to PCRE and does not support "
"expressions to match Chinese."
msgstr ""

msgid "Current status:"
msgstr "Learning Progress"

msgid "Current upstream is not working properly."
msgstr ""

msgid "Current version"
msgstr ""

msgid "Current version information"
msgstr "Versions"

#, python-brace-format
msgid ""
"Current website number has already reached the ceiling ({0}), You can not "
"add any more website."
msgstr ""

msgid "Custom"
msgstr ""

msgid "Custom 1"
msgstr ""

msgid "Custom 2"
msgstr ""

msgid "Custom 3"
msgstr ""

msgid "Custom Field 1"
msgstr ""

msgid "Custom Field 2"
msgstr ""

msgid "Custom Field 3"
msgstr ""

msgid "Custom Field 4"
msgstr ""

msgid "Custom Field 5"
msgstr ""

msgid ""
"Custom Role： Its permissions are subject to the settings made when "
"Administrator was creating it."
msgstr ""

msgid "Custom Strategy"
msgstr ""

msgid "Custom Text"
msgstr ""

msgid "Custom UA, support regex, the max length is 256"
msgstr "Regex is supported. Max length: 256"

#, python-brace-format
msgid ""
"Custom Website Name must not exceed {0} Chinese characters or {1} English "
"characters."
msgstr ""

msgid ""
"Custom Website Name only support numbers, letters, Chinese, and characters "
"such as: spaces, dot(.), underline(_), hyphen(-), parentheses, asterisk(*), "
"plus(+), comma(,)."
msgstr ""

msgid "Custom headers only support letters, numbers, underscores, and hyphens"
msgstr ""

msgid "Custom hostname"
msgstr "Custom Hostname"

msgid "Custom pii cannot set to builtin."
msgstr ""

msgid "Custom query statement"
msgstr "Query Expression"

msgid "Custom request"
msgstr ""

msgid "Custom rules"
msgstr ""

msgid "Customer ID"
msgstr ""

msgid "Customer Name"
msgstr ""

msgid "Customer Registration Name"
msgstr ""

msgid "Customer Sign-in Name"
msgstr ""

msgid "Customer Type"
msgstr ""

msgid "Customer defined reason 1(user_defined_reason1)"
msgstr ""

msgid "Customer defined reason 2(user_defined_reason2)"
msgstr ""

msgid "Customer defined reason 3(user_defined_reason3)"
msgstr ""

msgid "Customer defined reason 4(user_defined_reason4)"
msgstr ""

msgid "Customer defined reason 5(user_defined_reason5)"
msgstr ""

msgid "Customizable css style for custom template pages."
msgstr ""

msgid "Customize"
msgstr "Custom"

msgid "Customize CSS for template pages."
msgstr ""

msgid "Customize Images"
msgstr ""

msgid "Customize health check request body"
msgstr ""

msgid "Customize health check request headers"
msgstr ""

msgid ""
"Customize the location corresponding to the IP address, or correct the wrong"
" location."
msgstr "Enter the location information for an IP or network segment."

msgid "Customized Rules"
msgstr "API Recognition Rule"

msgid "Customized Rules ID"
msgstr "API Recognition Rule ID"

msgid "Customized risk count is over limit."
msgstr ""

msgid "Customized title:"
msgstr "Dashboard Title"

msgid "DAP Statistic License Information"
msgstr "Analytics License Information"

msgid "DAP System License Information"
msgstr "System License Information"

msgid "DAP port"
msgstr "System port"

msgid "DENY means the page cannot be displayed in any frame."
msgstr ""

msgid "DNS"
msgstr ""

msgid "DNS Server is unreachable, DNS search is not work."
msgstr ""

msgid "DNS Verification"
msgstr ""

msgid "DNS error. Cannot parse domain name or service."
msgstr ""

msgid "DNS service is unavailable."
msgstr "DNS service is not available."

msgid "Daily Licensed Volume"
msgstr ""

msgid "Data Encryption"
msgstr "Data Obfuscation"

msgid "Data Exposures"
msgstr ""

msgid "Data Filter"
msgstr ""

msgid "Data Parser"
msgstr "Parser"

msgid "Data collection"
msgstr "Data Collection"

msgid "Data collection frequency"
msgstr ""

msgid "Data collection length"
msgstr "Data Collection Length"

msgid "Data error in getting the unregistered app list."
msgstr ""

msgid "Data packets/second"
msgstr "segment/s"

msgid "Data query"
msgstr "Data Query"

msgid "Data_Collection"
msgstr "Business Data Collection"

msgid "Database Name"
msgstr ""

msgid "Database Query"
msgstr ""

msgid "Database attacks"
msgstr "Credential Stuffing Attacks"

msgid "Date"
msgstr ""

msgid "Date & Time(Change)"
msgstr "Date & Time"

msgid "Date & Time(Configuration)"
msgstr "Date & Time"

msgid "Days"
msgstr "days"

msgid "Debug"
msgstr ""

msgid "Debug Page"
msgstr ""

msgid ""
"Debug information is found in the return data of the interface. Debug "
"information may leak sensitive status or configuration details inside the "
"application, resulting in the risk of leakage of application or platform "
"data."
msgstr ""
"Debugging information included in responses may expose sensitive data or "
"configurations of internal systems."

msgid "Debug page has been disabled."
msgstr ""

msgid "Debug page has been enabled."
msgstr ""

msgid "Debug patch cannot be installed on release patch"
msgstr ""

msgid "Decision Threshold"
msgstr "Threshold"

msgid "Decode Recur Level"
msgstr "Decoding Depth"

msgid "Default"
msgstr ""

msgid "Default 60min"
msgstr ""

msgid "Default Action"
msgstr ""

msgid "Default Gateway"
msgstr ""

msgid "Default Gateway Configuration"
msgstr ""

msgid "Default Level"
msgstr "Standard"

msgid "Default Route already exists."
msgstr ""

msgid "Default Value"
msgstr ""

msgid "Default is random selection ID"
msgstr "Default: random ID"

msgid "Default route already exists."
msgstr ""

msgid "Defect Feature"
msgstr "Proof"

msgid "Defect Name"
msgstr ""

msgid "Defect Source"
msgstr "Found by"

msgid "Defect Status"
msgstr ""

msgid "Defect Type"
msgstr ""

msgid "Defense policy configuration"
msgstr "Policies"

msgid "Defined Fields"
msgstr "Custom Field"

msgid "Definition of static resource file"
msgstr "Static Resource File"

msgid "Degree of completion"
msgstr "Learning Progress"

msgid "Delay"
msgstr ""

msgid "Delete"
msgstr ""

msgid "Delete "
msgstr ""

msgid "Delete Account"
msgstr ""

msgid "Delete DNS server configuration."
msgstr ""

msgid "Delete NTP Server."
msgstr ""

msgid "Delete Strategy"
msgstr ""

#, python-brace-format
msgid "Delete account {username} used to send alert emails."
msgstr ""

msgid "Delete api and move to merge whitelist."
msgstr ""

msgid "Delete app"
msgstr ""

msgid "Delete business data collection successful"
msgstr ""

#, python-brace-format
msgid "Delete cluster node {node_ip}."
msgstr ""

msgid "Delete custom geo ip"
msgstr ""

#, python-brace-format
msgid ""
"Delete external big data analysis S (query) node failed: node {0} does not "
"exist."
msgstr ""

msgid "Delete external big data analysis S (query) node failed: save failed."
msgstr ""

msgid "Delete failed"
msgstr ""

msgid "Delete resource file failed"
msgstr ""

msgid "Delete resource file {}:{} from zk failed."
msgstr ""

msgid "Delete resource waf file failed"
msgstr ""

msgid "Delete resource waf file {}:{} from zk failed."
msgstr ""

msgid "Delete successfully"
msgstr "Deleted successfully"

#, python-brace-format
msgid ""
"Delete the external big data analysis S (query) node IP: {ex_query_node}."
msgstr "Deleted a source node IP {ex_query_node} to allow external log query"

#, python-brace-format
msgid "Delete the external big data analysis S node IP:{ex_node_ip}."
msgstr "Deleted a targe node IP {ex_node_ip} to query external logs"

#, python-brace-format
msgid ""
"Delete the external big data analysis S node failed: node {0} does not "
"exist."
msgstr ""

#, python-brace-format
msgid ""
"Delete the network administrator {username}@{gateway} used to send alarm "
"short messages."
msgstr ""

msgid "Delete this field"
msgstr ""

msgid "Delete user-defined risk config, risk id: {}."
msgstr "Deleted a custom defect with ID: {}."

msgid "Deleted"
msgstr ""

#, python-brace-format
msgid ""
"Deleting Node {0} will result in the following consequences. Are you sure to"
" delete?"
msgstr ""

msgid "Denial Count"
msgstr "Block Times"

msgid "Denial of Inventory(OAT-021)"
msgstr ""

msgid "Denial of Service(OAT-015)"
msgstr ""

msgid "Department"
msgstr ""

msgid "Deployment Model"
msgstr ""

msgid "Deployment-mode"
msgstr "Mode"

msgid "Description"
msgstr ""

msgid "Description is required"
msgstr ""

msgid "Description is required."
msgstr ""

msgid "Description:"
msgstr "Description: "

msgid "Descriptions"
msgstr "Description"

msgid "Desensitized storage of sensitive information"
msgstr "Logging with PII Masking"

msgid "Dest IP Address"
msgstr ""

msgid "Dest Port"
msgstr ""

msgid "Destination"
msgstr ""

msgid "Destination IP"
msgstr ""

#, python-brace-format
msgid "Destination IP should be {0}, because the netmask is {1}"
msgstr ""

msgid "Destination Port"
msgstr ""

msgid "Destination address"
msgstr ""

msgid "Details"
msgstr ""

msgid "Details of Network Adapters"
msgstr "Network Adapters"

#, python-brace-format
msgid "Details of parameter {0}"
msgstr ""

msgid "Detect Direction"
msgstr "Target"

msgid "Detect Regular Expression"
msgstr "Regular Expression"

msgid "Detect if UserAgent is a bot through machine learning."
msgstr ""

msgid "Detection Location"
msgstr "Checking Positions"

msgid "Detection of Automatic Tools"
msgstr ""

msgid "Detection position and detection value cannot be empty.{}"
msgstr "Detection position and detection value are required.{}"

msgid ""
"Detects sensitive information in traffic for non-HTTP protocols. The maximum"
" number of ports is limited to 10, and multiple ports are split by commas."
msgstr ""
"It allows the system to detect sensitive information in traffic for non-HTTP"
" protocols. The maximum number of ports is limited to 10, and multiple ports"
" are split by commas."

msgid "Developer Mode"
msgstr ""

msgid "Developer mode has been disabled."
msgstr ""

msgid ""
"Development-related services need to be restricted to development networks."
msgstr "Private applications needs to be accessable only on local networks."

msgid "Device MTU"
msgstr "Capturing Network Interface MTU"

msgid "Device MTU allows configuration range 1500-9000"
msgstr "Range: 1500-9000"

msgid "Device Parameter Abnormal"
msgstr "Abnormal-Parameter Device"

msgid "Device Type"
msgstr ""

msgid "Device identity related information"
msgstr ""

msgid "Device with various hacking tools such as xposed"
msgstr "Device with hacking tools such as Xposed"

msgid "Different policies can be applied to specific Apps. "
msgstr "Assign protection policies to specific apps."

msgid "Direct Connection Control"
msgstr "Network Connection Control"

msgid "Directory '/' doesn't have enough space"
msgstr ""

msgid "Directory '/var' doesn't have enough space"
msgstr ""

msgid "Directory Browsing Not Disabled"
msgstr "Directory Exposure"

msgid "Directory browsing is disabled in the server configuration."
msgstr "Disable remote browsing of system directories."

msgid ""
"Directory browsing is not disabled when configuring services such as Apache,"
" which may lead to the leakage of project files, such as log files, project "
"configurations, backup files, etc."
msgstr ""
"Internal directories on servers are exposed to visitors. It may lead to leak"
" of logs, configurations and backup files."

msgid "Directory indexes are not allowed here."
msgstr ""

msgid "Disable"
msgstr ""

msgid "Disable /123456."
msgstr ""

msgid "Disable 2fa successfully!"
msgstr "Two-factor authentication disabled successfully"

msgid "Disable AI detection - AI-UA module."
msgstr "Disabled AI Detection - AI-UA module"

msgid "Disable Enhance Cluster Security."
msgstr "Disabled Cluster Security Enhancement."

msgid "Disable Hide Hardware Info."
msgstr "Disable Hide Hardware Info."

msgid "Disable IP Blacklist"
msgstr "Disabled IP Blacklist"

msgid "Disable IP Whitelist"
msgstr "Disabled IP Whitelist"

msgid "Disable Keep-Alive on Server Side"
msgstr ""

msgid "Disable Network Ddos Protect."
msgstr "Disable Network-Layer DDoS protection."

msgid "Disable No Repeat Login."
msgstr "Disabled No Repeat Login"

msgid "Disable QPS_Management."
msgstr "Disable QPS Management"

msgid "Disable Response for Abnormal Requests"
msgstr ""

msgid "Disable Role Management."
msgstr ""

msgid "Disable Scheduled Export."
msgstr "Disabled Scheduled Export."

msgid "Disable Sensitive Log Filter."
msgstr "Disabled PII Masking."

#, python-brace-format
msgid "Disable Website Config Editor for the upstream site {key}."
msgstr "Disabled Website Configuration Editor for site {key}."

msgid "Disable XSS Protections."
msgstr ""

msgid "Disable advanced verify."
msgstr "Disabled Advanced Verification"

msgid "Disable bridge transparent"
msgstr "Disable bridge pass-through"

msgid "Disable bypass"
msgstr "Disable Bypass"

msgid "Disable defect: {}"
msgstr "Disabled Detection of Defect: {}"

msgid "Disable encoding ajax arguments."
msgstr ""

msgid "Disable expert mode."
msgstr "Disabled Expert Mode"

msgid "Disable integrity check failed."
msgstr "Failed to disable app integrity"

msgid "Disable keep source IP"
msgstr "Disabled Keep Source IP"

msgid "Disable login CAPTCHA."
msgstr "Disable login CAPTCHA"

msgid "Disable none risk display setting successfully"
msgstr ""

msgid "Disable port status forward"
msgstr "Disabled Port Status Notification"

msgid "Disable source port keep"
msgstr "Disabled Keep Source Port"

msgid "Disable the archive log filter configuration."
msgstr "Disabled Archived Log Filter"

msgid "Disable the formatted access log output configuration."
msgstr "Disabled Transmit Formatted Access Log"

msgid "Disable the full traffic log."
msgstr "Disabled Full Traffic Log Filter"

msgid "Disable the mouse trace collection"
msgstr "Disabled Mouse Tracking"

msgid "Disable the operate log output configuration."
msgstr "Disabled Transmit Operation Log"

msgid "Disable the original access log output configuration."
msgstr "Disabled Transmit Raw Access Log"

msgid ""
"Disable the original access log output directly through the proxy node."
msgstr "Disabled Transmit Raw Access Log - Transmit from Protection Nodes"

msgid "Disable the receive parsed access log."
msgstr "Disabled Receive Parsed Access Log"

msgid "Disable two-factor authentication."
msgstr "Disabled two-factor authentication"

msgid "Disable web filter."
msgstr ""

msgid "Disabled"
msgstr ""

msgid "Disabled Header"
msgstr "Forbidden Headers"

msgid ""
"Disabled allow access to protected sites through management network adapter"
msgstr ""

msgid ""
"Disabling LLM service will result in the deletion of the uploaded local LLM "
"model."
msgstr ""

msgid "Disallow"
msgstr ""

msgid "Disallow DTDs"
msgstr "Disallow DTDs"

msgid "Disallow external entities"
msgstr "Disallow External Entities"

msgid "Disallow processing instructions"
msgstr "Disallow Processing Instructions"

#, python-brace-format
msgid ""
"Disconnected [{node_id}]({node_ip}) in this cluster. Please check and fix it"
msgstr "Disconnected [{node_id}]({node_ip}) in this cluster."

msgid "Discovered at"
msgstr "Created at"

msgid "Discovery Time"
msgstr "Created at"

msgid "Disk Capacity"
msgstr ""

msgid "Disk Quota for Log Analysis"
msgstr ""

msgid "Disk Quota for Log Archiving"
msgstr ""

msgid "Disk Space Allowed"
msgstr ""

msgid "Disk Space Available"
msgstr ""

#, python-brace-format
msgid ""
"Disk space {0}G for Log Analysis and {1}G for Log Archiving is bigger than "
"the available {2}G!"
msgstr ""

#, python-brace-format
msgid "Disk space {0}G for Log Analysis is bigger than the available {1}G!"
msgstr ""

#, python-brace-format
msgid "Disk space {0}G for Log Archiving is bigger than the available {1}G!"
msgstr ""

msgid "Display of ad"
msgstr "Display Ad"

msgid "Distinguish between IPv4 and IPv6"
msgstr ""

msgid "Do Not Collect Outbond Traffic"
msgstr "Do Not Capture Outbond Traffic"

msgid "Do Not Protect Following IPs"
msgstr "Include List (Not Protect Below IPs)"

msgid "Do Not enter multiple regexes."
msgstr ""

msgid "Do not add URL token to following paths (Default)"
msgstr "Exclude List (Default)"

msgid ""
"Do not support named references or position 0 reference in replace content."
msgstr ""

#, python-brace-format
msgid "Do you want to remove user: {0} permanently?"
msgstr ""

#, python-brace-format
msgid "Do you want to unlock user: {0}?"
msgstr ""

msgid "Docker Business Component"
msgstr ""

msgid "Docker Business Component APIs Auto-detection"
msgstr "Auto-Recognize Docker-Business-Component APIs"

msgid "Document Type"
msgstr "ID Type"

msgid "Domain"
msgstr ""

msgid "Domain Name"
msgstr ""

msgid "Don't verify request with access path as follow"
msgstr ""
"Requests that match the whitelist conditions will not be verified by the "
"system."

msgid "Download"
msgstr ""

msgid "Download APIs Auto-detection"
msgstr "Auto-Recognize Download APIs"

msgid "Download Control"
msgstr ""

msgid "Download Counts"
msgstr ""

msgid "Download Plugin SDK"
msgstr ""

msgid "Download SDK"
msgstr ""

msgid "Download SSL Certification"
msgstr "Download SSL Certificate"

msgid "Download SSL certificate"
msgstr ""

msgid "Download Template Package"
msgstr "Download Templates"

msgid "Download Third-Party Tools"
msgstr ""

msgid "Download failed"
msgstr ""

msgid "Download link:"
msgstr "Download link: "

msgid "Download the latest goodbots"
msgstr "Download the latest version"

#, python-brace-format
msgid "Download {file_name} form {file_path}"
msgstr ""

msgid "Downloads Count"
msgstr ""

msgid "Drop"
msgstr ""

msgid "Drug Purchase Record"
msgstr ""

msgid "Drug and Food Allergy information"
msgstr "Drug and Food Allergy Information"

msgid "Dual Network Adapter"
msgstr ""

msgid "Duplicate Content Type URL"
msgstr ""

msgid "Duplicate IP"
msgstr ""

msgid "Duplicate configuration detected."
msgstr ""

msgid "Duplicate content found"
msgstr ""

#, python-brace-format
msgid "Duplicate items: {0}"
msgstr ""

msgid "Duplicate name"
msgstr ""

msgid "Duplicate parameters are not allowed in the same location."
msgstr ""

msgid "Duplicate ports entered."
msgstr ""

msgid "Duplicate prompts."
msgstr ""

msgid "Duplicate rule exists!"
msgstr ""

msgid "Duplicate(s) found between/among Ajax/Fetch Req URL_Token_Free paths."
msgstr "Duplicate(s) found in Ajax/Fetch Request URL Token paths."

msgid "Duplicated configurations"
msgstr ""

msgid "Duplicated name"
msgstr ""

msgid "Dynamic Application Protection"
msgstr ""

msgid "Dynamic Challenge"
msgstr ""

msgid "Dynamic Good Bot Cache Full"
msgstr "Full goodbot cache"

msgid "Dynamic Password"
msgstr ""

msgid "Dynamic protection configuration does not match layout."
msgstr ""

msgid "Dynamically modifying node roles"
msgstr ""

#, python-brace-format
msgid "Dynamically modifying {0} </ b> role of <b> node"
msgstr "Modify roles of <b>Node {0}</b>"

msgid "E-mail Address"
msgstr ""

#, python-brace-format
msgid "ERRNO_{0}"
msgstr ""

msgid "ETCD Business Component"
msgstr ""

msgid "ETCD Business Component APIs Auto-detection"
msgstr "Auto-Recognize ETCD-Business-Component APIs"

msgid ""
"Each IP or network segment in the file should be positioned in a separate "
"line, and can have a validity time (Unix Timestamp in seconds) specified for"
" each entry, separated with a comma."
msgstr ""

msgid "Each path is counted separately"
msgstr "Calculate per Path"

msgid "Each path supports up to 10 IDs!"
msgstr ""

msgid "Each prompt must be a dictionary"
msgstr ""

msgid "Each prompt must contain 'content' fields"
msgstr ""

msgid "Edit"
msgstr ""

msgid "Edit "
msgstr ""

msgid "Edit Custom Rule"
msgstr ""

msgid "Edit Global Custom Rule"
msgstr ""

msgid "Edit Role"
msgstr ""

#, python-brace-format
msgid "Edit strategy of {0}"
msgstr ""

msgid "Edit this model"
msgstr ""

msgid "EditAndMerge"
msgstr "Edit"

msgid "EditOnly"
msgstr "Edit"

msgid "Edite Rule"
msgstr ""

msgid "Edited_one_application_while_merging"
msgstr "Edited one application and merged existing one(s) into"

msgid "Editing Rules"
msgstr ""

msgid ""
"Editing the network interface and saving it may cause network failure or "
"cluster abnormality. Are you sure to save it?"
msgstr ""
"Making changes to network interface may cause network failures or cluster "
"mulfunctions. Proceed anyway?"

msgid "Educational Background"
msgstr ""

msgid "Effect Scope"
msgstr "Effective Scope"

msgid "Effective"
msgstr "Enabled"

msgid "Effective Date"
msgstr ""

msgid "Effective after saving"
msgstr ""

msgid "Elasticsearch Business Component"
msgstr ""

msgid "Elasticsearch Business Component APIs Auto-detection"
msgstr "Auto-Recognize Elasticsearch-Business-Component APIs"

msgid "Eliminate Alarm"
msgstr "Cancel Alarm"

msgid "Email Address"
msgstr ""

msgid "Emergency Mode"
msgstr ""

msgid "Emergency Mode HardWare Bypass"
msgstr "Emergency mode (traffic pass-through)"

msgid "Emergency Mode SoftWare Bypass"
msgstr "Emergency mode (WAF pass-through)"

msgid "Emergency_Mode"
msgstr "Emergency Mode"

msgid "Employee ID"
msgstr ""

msgid "Employee Name"
msgstr ""

msgid "Empty"
msgstr ""

msgid "Empty keywords are not allowed"
msgstr ""

msgid "Empty means no change"
msgstr "Empty means no change"

msgid "Empty risk library!"
msgstr ""

msgid "Empty value is not allowed."
msgstr ""

msgid "Emulator"
msgstr ""

msgid "Emulator requests count(erc)"
msgstr ""

msgid "Enable"
msgstr "Enable"

msgid "Enable AI detection - AI-UA module."
msgstr "Enabled AI Detection - AI-UA module."

msgid "Enable Bot Detection"
msgstr ""

msgid "Enable Brute Force Protection Strategy"
msgstr "Brute Force Strategy"

msgid "Enable Business Path"
msgstr "Specify Path"

msgid "Enable CC Attack Protection Strategy"
msgstr "CC Attack Strategy"

msgid "Enable CSRF Protection Strategy"
msgstr "CSRF Protection Strategy"

msgid "Enable Check SQL"
msgstr "Executable SQL Statement"

msgid "Enable Cookie Tamper Protection Strategy"
msgstr "Cookie Security Strategy"

msgid "Enable Cookie Token Protection"
msgstr ""

msgid "Enable Encrypt Argument"
msgstr "Enable Parameter Encryption"

msgid "Enable Encrypt Path"
msgstr "Enable Path Encryption"

msgid "Enable Enhance Cluster Security."
msgstr "Enabled Cluster Security Enhancement"

msgid "Enable HTTP Protocol"
msgstr "HTTP Protocol Compliance Strategy"

msgid "Enable Hide Hardware Info."
msgstr "Enable Hide Hardware Info."

msgid "Enable Honey Pot Check"
msgstr "Honey Pot Strategy"

msgid "Enable IP Blacklist"
msgstr "Enabled IP Blacklist"

msgid "Enable IP Whitelist"
msgstr "Enabled IP Whitelist"

msgid "Enable Illegal Download Protection Strategy"
msgstr "Illegal Download Strategy"

msgid "Enable Network Ddos Protect."
msgstr "Enabled Network-Layer DDoS protection"

msgid "Enable No Repeat Login."
msgstr "Enabled No Repeat Login"

msgid "Enable QPS_Management."
msgstr "Enable QPS Management"

msgid "Enable Regional Access Control Strategy"
msgstr "Regional Control Strategy"

msgid "Enable Res_leech Strategy"
msgstr "Anti-Leech Strategy"

msgid "Enable Response for Abnormal Requests"
msgstr ""

msgid "Enable Role Management."
msgstr ""

msgid "Enable Scheduled Export."
msgstr "Enabled Scheduled Export"

msgid "Enable Sensitive Log Filter."
msgstr "Enabled PII Masking."

msgid "Enable Statistics Secret"
msgstr ""

msgid "Enable URL Token Protection"
msgstr ""

msgid "Enable Vulnerability Scan Protection Strategy"
msgstr "Vulnerability Scan Strategy"

msgid "Enable Waf Modules"
msgstr "Select a Site Strategy"

msgid "Enable Weak Password Check"
msgstr "Weak Password Strategy"

#, python-brace-format
msgid ""
"Enable Website Config Editor edits the site configuration of the upstream "
"site {key}."
msgstr "Enabled Website Configuration Editor for site {key}."

msgid "Enable XML Attack Protection Strategy"
msgstr "XML Attack Strategy"

msgid ""
"Enable XSS protections and block pages from loading if XSS is detected."
msgstr ""

msgid "Enable advanced verify."
msgstr "Enabled Advanced Verification"

msgid "Enable bridge transparent"
msgstr "Bridge Pass-Through"

msgid "Enable bypass"
msgstr "Enable Bypass"

msgid "Enable defect failed."
msgstr "Failed to enabled defect detection."

msgid "Enable defect: {}"
msgstr "Enabled Detection of Defect: {}"

msgid "Enable developer mode"
msgstr ""

msgid "Enable expert mode."
msgstr "Enabled Expert Mode"

msgid "Enable for Post/Cookie/Header/Response-body collect length."
msgstr ""
"This refers to the max length of total data collected from the above "
"selected sources (e.g. POST Bodies or Cookies)"

msgid "Enable full flow collection"
msgstr ""

msgid "Enable integrity check failed."
msgstr "Failed to enabled app integrity"

msgid "Enable it in overview page or disable ai in upstream."
msgstr ""

msgid ""
"Enable it to allow the system to recognize slow HTTP attacks and block "
"corresponding requests."
msgstr ""

msgid "Enable it to hide hardware information in Cluster Node List."
msgstr ""

msgid "Enable keep source IP"
msgstr "Enabled Keep Source IP"

msgid "Enable login CAPTCHA."
msgstr "Enabled login CAPTCHA"

msgid "Enable none risk display setting successfully"
msgstr ""

msgid ""
"Enable or disable the detection of the following automatic tools depending "
"on the actual needs."
msgstr ""

msgid "Enable port status forward"
msgstr "Enabled Port Status Notification"

msgid "Enable souce port keep"
msgstr "Enabled Keep Source Port"

msgid "Enable the archive log filter configuration."
msgstr "Enabled Archived Log Filter"

msgid "Enable the formatted access log output configuration."
msgstr "Enabled Transmit Formatted Access Log"

msgid "Enable the full traffic log."
msgstr "Enabled Full Traffic Log Filter"

msgid "Enable the mouse trace collection"
msgstr "Enabled Mouse Tracking"

msgid "Enable the operate log output configuration."
msgstr "Enabled Transmit Operation Log"

msgid "Enable the original access log output configuration."
msgstr "Enabled Transmit Raw Access Log"

msgid "Enable the original access log output directly through the proxy node."
msgstr "Enabled Transmit Raw Access Log - Transmit from Protection Nodes"

msgid "Enable the receive parsed access log."
msgstr "Enabled Receive Parsed Access Log"

msgid ""
"Enable this feature before next ruleset update to ensure new rules can be "
"displayed in the lower part of this section and all of them are enabled with"
" their actions set to Monitor."
msgstr ""

msgid "Enable two-factor authentication."
msgstr "Enabled two-factor authentication"

msgid "Enable web filter."
msgstr "Enabled Web Filter"

msgid "Enable webrtc."
msgstr ""

msgid "Enable/Disable info is blank."
msgstr ""

msgid "Enabled"
msgstr ""

msgid "Enabled Rules"
msgstr "Enabled"

msgid ""
"Enabled allow access to protected sites through management network adapter"
msgstr ""

msgid "Enables XSS Protections."
msgstr ""

msgid ""
"Enabling it will allow certain proportion of logs to be archived to the log "
"archiving server in accordance with the specified percentage."
msgstr ""
"When enabled, the system will allow certain proportion of logs to be "
"archived to the log archiving server according to the percentage entered."

msgid ""
"Enabling it will bring better compatibility with the websites being "
"protected."
msgstr ""
"Enabling it will bring better compatibility to the websites at the expense "
"of weaker security."

msgid ""
"Enabling it will force IE9 and lower version to display webpages with the "
"highest-version rendering mode, with a purpose to provide better "
"compatibility for some websites."
msgstr ""
"Enabling it will force IE9 and lower version to display webpages with the "
"highest-version rendering mode, in order to provide better compatibility for"
" some websites."

msgid ""
"Enabling it would prompt users to enable Cookie in their browsers when "
"Cookie is disabled."
msgstr ""

msgid "Enabling it would strictly match request Host."
msgstr ""
"Block requests with Host that does not match the domain name configured for "
"the website."

msgid "Enabling it would use GM algorithm to encrypt/decrypt token."
msgstr ""

msgid "Encapsulation List cannot be blank!"
msgstr "Encapsulation List is required."

msgid "Encode Type"
msgstr ""

msgid "Encryption Algorithm"
msgstr ""

msgid "Encryption Password"
msgstr ""

msgid "Encryption certificate file"
msgstr ""

msgid "Encryption certificate file has been uploaded."
msgstr ""

msgid "Encryption private key file"
msgstr ""

msgid "Encryption private key file has been uploaded."
msgstr ""

msgid "End"
msgstr ""

msgid "End Characters"
msgstr ""

msgid "End IP"
msgstr ""

msgid "End Time"
msgstr "End Time"

msgid "End of Wizard"
msgstr ""

msgid "End to learn"
msgstr "Stop learning"

msgid "End with"
msgstr "End with"

msgid "End with(no regular expression)"
msgstr ""

msgid "Endpoint is not valid: {}"
msgstr ""

msgid "Endpoint should start with path prefix: {}"
msgstr ""

msgid "English Interception Response"
msgstr ""

msgid ""
"English Interception Response cannot be empty. In addition to Chinese words,"
" the characters allowed include numbers, letters, whitespaces, dots(.), "
"underscores(_), dashes, parentheses, asterisks, plus, as well as commas, "
"periods, exclamation marks, colons, semicolons and quote marks in Chinese or"
" English."
msgstr ""

msgid "Enhance Cluster Security"
msgstr "Cluster Security Enhancement"

msgid ""
"Enhance RAS safety by banning IPs not included in the whitelist from "
"accessing specific ports in the cluster."
msgstr ""
"Enhance system security by banning IPs not in the whitelist from accessing "
"specific ports of the system."

msgid ""
"Ensure that a single site only have one operator. Go to tab Assignments now "
"to remove operators."
msgstr ""

msgid "Ensure tokens are generated with a shorter lifetime."
msgstr ""

msgid "Enter Statistics Control Center"
msgstr ""

msgid "Enter Threshold"
msgstr ""

msgid "Enter Threshold Must Be Greater Than Exit Threshold"
msgstr ""

msgid ""
"Enter Threshold greater than or equal to 1 and less than or equal to 100"
msgstr ""

msgid ""
"Enter Threshold greater than or equal to 10 and less than or equal to "
"10000000"
msgstr ""

msgid ""
"Enter Threshold greater than or equal to 8 and less than or equal to 100"
msgstr ""

msgid "Enter Wizard"
msgstr ""

#, python-brace-format
msgid ""
"Enter a name with a length from {0} to {1} characters that may include "
"letters, numbers, underlines(_) and hyphens(-)."
msgstr ""

msgid "Enter a note"
msgstr ""

msgid "Enter a path"
msgstr "Enter a path"

msgid "Enter a regular expression"
msgstr "Regular Expression"

msgid "Enter a role name"
msgstr ""

msgid "Enter a single regex (Do Not enter multiple regexes)."
msgstr ""

msgid ""
"Enter a status code sent to clients when they are denied by WAF. This "
"setting has a low priority than that in strategies and rules."
msgstr ""
"Enter a status code sent to clients when they are denied by WAF. This "
"setting has a lower priority than that in strategies and rules."

msgid ""
"Enter a trusted site that allows sharing the protection path.Entering a "
"blank indicates that all third-party sites are blocking the protection path "
"from the current site. Note: 1. Enter the domain name or IP; 2. Configurable"
" wildcard,for example: ∗.abc.com,www.abc.∗; 3. Ignore case by default."
msgstr ""
"Enter trusted sites considered exceptions to CSRF. Notes: <br/>1. The sites "
"entered can be domain names or IPs, without port numbers. <br/>2. Wildcard *"
" is allowed (e.g., *.abc.com, www.abc.*, and 2::3*). <br/>3. Case "
"insensitive."

msgid "Enter an IP different from that of Primary Authentication Server."
msgstr ""

#, python-brace-format
msgid "Enter an integer from {0} to {1}."
msgstr ""

msgid "Enter an url"
msgstr "Enter a URL"

#, python-brace-format
msgid "Enter characters with a length from {0} to {1}."
msgstr ""

msgid ""
"Enter source IPs of the clients to be collected from. Leaving it empty means"
" collecting from any clients. (IPv4 in CIDR and IPv6  are supported. Use "
"commas to separate multiple addresses.)"
msgstr ""
"Enter source IPs of the clients to be collected from. Leaving it empty means"
" collecting from any clients. (IPv4 and IPv6 in CIDR are supported. Use "
"commas to separate multiple addresses.)"

msgid "Enter the exact URL must start with /."
msgstr ""

msgid ""
"Enter the number of characters that do not need masking at front and end "
"part of PII, as well as a character used as the masking symbol for the rest "
"part."
msgstr ""

msgid "Enter the website entry path to improve analysis accuracy"
msgstr ""

msgid "Enter to add a new node, Multiple values must be separated by commas."
msgstr "Use commas to separate multiple IPs"

#, python-brace-format
msgid "Enter {0} to {1} characters."
msgstr ""

msgid ""
"Entering multiple values is not supported, and regular expressions are not "
"supported."
msgstr "Enter only one value. Regular expression is not allowed."

msgid "Entries"
msgstr ""

msgid "Entry path"
msgstr ""

msgid "Enumerated type {} must be non-empty string"
msgstr ""

msgid "Enumeration"
msgstr ""

msgid "Equal"
msgstr "Equal to"

msgid "Error Code"
msgstr ""

msgid "Error Log"
msgstr "Abnormal Request Log"

msgid "Error Message: "
msgstr ""

msgid "Error Page Templates"
msgstr ""

msgid "Error Type"
msgstr ""

msgid "Error applying configuration information"
msgstr "An error occurs when applying configurations"

msgid "Error call sdk interface count(ecic)"
msgstr ""

msgid "Error call sdk interface(ds_interface_call)"
msgstr ""

#, python-brace-format
msgid "Error found while reading field {0} : {1}."
msgstr ""

msgid "Error opt!"
msgstr ""

msgid "Error risk library config!"
msgstr ""

msgid "Error risk library data!"
msgstr ""

msgid "Error risk patch file!"
msgstr ""

#, python-brace-format
msgid "Error value: {0}"
msgstr ""

msgid "Error: Fail to save rule in Zookeeper"
msgstr ""

msgid "Error: llm config error"
msgstr ""
"Error：Abnormal LLM settings. Please try again after you check the settings."

msgid "Error: llm connection error"
msgstr ""

msgid ""
"Error: llm is loading or the response timed out, please try again later"
msgstr ""

msgid "Error: llm not enable"
msgstr ""

msgid "Error: llm request error"
msgstr ""

msgid "Error: llm response error"
msgstr ""

msgid "Error: llm too many requests error"
msgstr "Error: LLM service is busy. Please try again later."

msgid "Error: llm unknown error"
msgstr "Error: unknown LLM error"

msgid "Errors when verify configuration, see logs for detail information."
msgstr ""

msgid "Essential Web Protection"
msgstr ""

msgid "Establish a normal business flow model by analyzing user flow"
msgstr "Self-learning access data to generate a business traffic model."

msgid "Ethnicity"
msgstr ""

msgid "Evaluation"
msgstr ""

msgid "Event"
msgstr ""

msgid "Event Details"
msgstr ""

msgid "Every Day"
msgstr ""

msgid "Every Month"
msgstr ""

msgid "Every Week"
msgstr ""

msgid "Exact"
msgstr "Exact match"

msgid "Exact Match"
msgstr ""

msgid "Example"
msgstr ""

msgid "Example: asp, php-5, exe_5"
msgstr ""

msgid "Examples: abc.php, 123.exe, config.json"
msgstr ""

msgid "Exceed limit for {} day(s)"
msgstr ""

msgid "Exceed max mpp counts"
msgstr "Exceeded max mini-program number"

msgid "Exceeded Max Header Length"
msgstr ""

msgid "Exceeded Max Regex Length in Custom rules."
msgstr ""

msgid "Exceeded Max Regex Length in Threat Intelligence White List."
msgstr "Exceeded Max Regex Length in Threat Intelligence Whitelist."

msgid "Exceeded Max Resp Header Length"
msgstr ""

msgid "Exceeded limit of password length."
msgstr ""

msgid "Exception occurs when joining cluster"
msgstr ""

msgid ""
"Exception occurs when license is being updated, please refresh the page and "
"try again"
msgstr ""

msgid "Exception occurs when license is being updated."
msgstr ""

msgid "Exception occurs when statistics license is being updated."
msgstr "Exception occurs when analytics license is being updated."

msgid "Exception path"
msgstr ""

msgid "Exception when extracting MPP SDK content"
msgstr ""

msgid "Exceptions might occur during deployment. "
msgstr ""

msgid "Excessive Ajax configurations"
msgstr ""

msgid "Exclude Error Response"
msgstr ""

msgid "Exclude(no regular expression)"
msgstr ""

msgid "Execute Action"
msgstr "Action"

msgid "Execution time"
msgstr "Transmitted at"

msgid "Exit Full Screen"
msgstr ""

msgid "Exit Threshold"
msgstr ""

msgid ""
"Exit Threshold greater than or equal to 1 and less than or equal to 100"
msgstr ""

msgid ""
"Exit Threshold greater than or equal to 10 and less than or equal to "
"10000000"
msgstr ""

msgid ""
"Exit Threshold greater than or equal to 300 and less than or equal to 3600"
msgstr ""

msgid "Exit threshold since last happened time"
msgstr ""

msgid "Expected parameters:"
msgstr ""

msgid "Expert Mode"
msgstr ""

msgid "Expiration"
msgstr "Expiration"

msgid "Expiration Date"
msgstr ""

msgid "Expiration date is required."
msgstr "Expiration date is required."

msgid "Expiration time"
msgstr "Expiration"

msgid "Expire Date"
msgstr "Expire in"

msgid "Expired"
msgstr ""

msgid "Explanation"
msgstr ""

msgid "Export"
msgstr ""

msgid "Export Exception"
msgstr ""

msgid "Export System Settings"
msgstr ""

msgid "Export Timeout"
msgstr ""

msgid "Export or import Programmable Defending settings."
msgstr ""

msgid "Export process is in progress, try again later"
msgstr ""

msgid "Export threat events"
msgstr "Export Threat Events"

msgid "Export to file"
msgstr "Export"

msgid "Export/Import System Settings"
msgstr "Export or import system settings."

msgid "Exposure of Vulnerable Applications in Extranet"
msgstr ""

msgid "Expression not allowed"
msgstr ""

msgid "Extension name"
msgstr "File Extensions"

msgid "External Network Adapter"
msgstr "External"

msgid "External Website Web Requests"
msgstr ""

msgid "External big data analysis node IP"
msgstr "Target Node IP"

msgid "External query node IP"
msgstr "Source Node IP"

msgid "External/Internal Network Adapter"
msgstr "External/Internal"

msgid "Extranet"
msgstr "Internet"

msgid "Extranet IP"
msgstr "Public IP"

msgid ""
"Extranet access is found on the login interface that is set to not allow "
"Extranet access"
msgstr "Requests from Internet are received on a private API."

msgid "Extranet logins"
msgstr "Login from Internet"

msgid "Extreme"
msgstr ""

msgid "F5 BigIP Middleware"
msgstr ""

msgid "F5 BigIP Middleware APIs Auto-detection"
msgstr "Auto-Recognize F5-BigIP-Middleware APIs"

msgid "FILES_CONTENT"
msgstr ""

msgid "FILES_NAMES"
msgstr ""

msgid "Factory Reset"
msgstr ""

msgid "Factory reset failed."
msgstr ""

msgid "Factory reset succeeded."
msgstr ""

msgid "Factory reset will set DAP to the initial status."
msgstr "Factory reset will set the system to the initial status."

msgid "Fail To Upload File"
msgstr "Failed to upload file"

msgid "Fail to create role!"
msgstr ""

msgid "Fail to delete node!"
msgstr ""

msgid "Fail to delete role!"
msgstr ""

msgid "Fail to disable log filter!"
msgstr "Fail to disable PII Masking!"

msgid "Fail to disable role management!"
msgstr ""

msgid "Fail to download file"
msgstr "Failed to download file"

msgid "Fail to download resource file"
msgstr ""

msgid "Fail to download resource waf file"
msgstr ""

msgid "Fail to edit role!"
msgstr ""

msgid "Fail to enable developer mode. "
msgstr ""

msgid "Fail to enable log filter!"
msgstr "Fail to enable PII Masking!"

msgid "Fail to enable role management!"
msgstr ""

msgid "Fail to query server information"
msgstr ""

msgid "Fail to query share memory"
msgstr "Failed to query shared memory"

msgid "Fail to rollback llm corpus"
msgstr ""

msgid "Fail to rollback ruleset"
msgstr ""

msgid "Fail to set shared dict."
msgstr ""

msgid "Fail to sync file in the cluster"
msgstr ""

msgid "Failed"
msgstr ""

msgid ""
"Failed calls refer to response codes of 4xx or 5xx, or business_success is "
"false (business data collection needs to be configured)"
msgstr ""
"The calls that received status codes of 4xx or 5xx or have a field "
"business_success (if Business Data Collection enabled) with value \"false\" "
"in Analytics are considered failed ones."

msgid "Failed removed unused APIs."
msgstr ""

msgid "Failed to Disable."
msgstr ""

msgid "Failed to Enable."
msgstr ""

msgid ""
"Failed to activate statistics license. Please check the details in the "
"statistics subsystem."
msgstr "Failed to activate the analytics license."

msgid "Failed to activate the license"
msgstr ""

msgid "Failed to add to the WAF White List."
msgstr "Failed to add to the WAF Whitelist."

msgid ""
"Failed to allow access to protected sites through management network adapter"
msgstr ""

msgid "Failed to apply cluster configuration"
msgstr ""

msgid "Failed to authenticate."
msgstr ""

msgid "Failed to auto-update goodbot"
msgstr "Failed to auto-update goodbot database"

msgid "Failed to change the role."
msgstr ""

msgid "Failed to configure MPP list"
msgstr ""

msgid "Failed to create node!"
msgstr ""

msgid "Failed to create task"
msgstr ""

#, python-brace-format
msgid "Failed to create. A strategy can have {0} rules at most."
msgstr ""

#, python-brace-format
msgid "Failed to create. {0} rules are allowed in total at most."
msgstr ""

msgid "Failed to delete"
msgstr ""

msgid "Failed to delete node!"
msgstr ""

msgid "Failed to delete rule."
msgstr ""

msgid "Failed to delete strategy."
msgstr "Failed to delete strategy."

msgid "Failed to disable Login CAPTCHA"
msgstr ""

msgid "Failed to disable No Repeat Login"
msgstr ""

msgid "Failed to disable developer mode."
msgstr ""

msgid "Failed to edit risk setting"
msgstr ""

msgid "Failed to enable Login CAPTCHA"
msgstr ""

msgid "Failed to enable No Repeat Login"
msgstr ""

msgid "Failed to enable block note on response."
msgstr ""

msgid "Failed to enable policy on good bots."
msgstr "Failed to enable [Allow Good Bots]."

msgid "Failed to enable policy on keep source IP."
msgstr "Failed to enable [Keep Source IP]."

msgid ""
"Failed to generate a certificate and an abnormal response result is "
"returned."
msgstr ""

msgid "Failed to generate a certificate. Please activate license."
msgstr ""

msgid "Failed to get data, please try again"
msgstr ""

msgid "Failed to get network interface status"
msgstr ""

msgid "Failed to get the unregistered app list."
msgstr ""

msgid "Failed to get weak password dictionary."
msgstr ""

msgid "Failed to import system settings"
msgstr ""

msgid ""
"Failed to import system settings, the deployment mode in the imported system"
" configuration does not match the current deployment mode"
msgstr ""

msgid ""
"Failed to import system settings, the layout in the imported system "
"configuration does not match the current layout"
msgstr ""

msgid "Failed to import websites"
msgstr ""

msgid "Failed to join cluster"
msgstr " "

#, python-brace-format
msgid "Failed to join cluster: {0}"
msgstr ""

msgid "Failed to log in to WebConsole"
msgstr ""

msgid "Failed to log in, wrong username or password."
msgstr ""

msgid "Failed to manual remove alarm"
msgstr ""

msgid "Failed to modify Multi-Client Protection Policy."
msgstr ""

msgid "Failed to modify browser proxy detection"
msgstr ""

msgid "Failed to modify site configuration state"
msgstr ""

msgid "Failed to query QPS configuration"
msgstr ""

msgid "Failed to read list"
msgstr ""

msgid "Failed to reboot the system because of connection exception."
msgstr ""

msgid "Failed to record license info"
msgstr ""

msgid "Failed to refresh, please try again later."
msgstr ""

msgid "Failed to register the app."
msgstr ""

msgid "Failed to remove auto black Account"
msgstr ""

msgid "Failed to remove auto black Account_Hostname"
msgstr ""

msgid "Failed to remove auto black FP"
msgstr ""

msgid "Failed to remove auto black IP"
msgstr ""

msgid "Failed to remove the registered app."
msgstr ""

msgid "Failed to restore model {} in zk"
msgstr ""

msgid "Failed to restore model {} threshold in zk"
msgstr ""

#, python-brace-format
msgid "Failed to restore the default report app for the following nodes: {0}"
msgstr ""

msgid "Failed to save Default Action"
msgstr ""

msgid "Failed to save Monitor-Only New Rules"
msgstr ""

msgid "Failed to save Multi-Client Protection Policy."
msgstr ""

msgid "Failed to save configuration"
msgstr ""

msgid ""
"Failed to save due to changed configurations. Please refresh the page and "
"try again."
msgstr ""

msgid "Failed to save password Security."
msgstr ""

msgid "Failed to save the configuration."
msgstr ""

msgid "Failed to save threat type setting"
msgstr ""

msgid "Failed to save upstream configuration"
msgstr ""

msgid "Failed to save."
msgstr ""

msgid ""
"Failed to save. Configurations do not comply with OpenAI's specifications."
msgstr ""

msgid "Failed to separate cluster nodes"
msgstr ""

#, python-brace-format
msgid ""
"Failed to set  archivelog filter. Error code: {0}, cmd: {1}, messages: {2}"
msgstr ""

#, python-brace-format
msgid ""
"Failed to set  phoenix_extra_indexes. Error code: {0}, cmd: {1}, messages: "
"{2}"
msgstr ""

msgid "Failed to set File Monitoring"
msgstr ""

msgid "Failed to set LLM Model Configuration"
msgstr ""

msgid "Failed to set Threat Intelligence"
msgstr ""

msgid "Failed to set Threat Intelligence mode"
msgstr ""

msgid "Failed to set aaa configuration! Check logs for details."
msgstr ""

msgid "Failed to set bta lua model {} in zk"
msgstr ""

msgid "Failed to set cluster configuration"
msgstr ""

msgid "Failed to set configuration. Try again later"
msgstr ""

#, python-brace-format
msgid ""
"Failed to set disk space for Log Analysis. Error code: {0}, cmd: {1}, "
"message: {2}"
msgstr ""

#, python-brace-format
msgid ""
"Failed to set disk space for Log Archiving is bigger than the available. "
"Error code: {0}, cmd: {1}, messages: {2}"
msgstr ""

#, python-brace-format
msgid ""
"Failed to set log multi processer. Error code: {0}, cmd: {1}, messages: {2}"
msgstr ""

msgid "Failed to set model threshold in zk"
msgstr ""

msgid "Failed to set network layer access control rules."
msgstr ""

msgid "Failed to set session count"
msgstr ""

msgid "Failed to set session max len"
msgstr ""

msgid "Failed to set session timeout"
msgstr ""

msgid "Failed to shutdown the system because of connection exception."
msgstr ""

msgid "Failed to stop analyse"
msgstr ""

msgid "Failed to switch"
msgstr ""

msgid "Failed to sync the protection list"
msgstr " "

msgid "Failed to unlock the user"
msgstr ""

msgid "Failed to update QPS."
msgstr ""

msgid "Failed to update QPS: "
msgstr ""

msgid "Failed to update api scanner list"
msgstr " "

msgid "Failed to update token."
msgstr ""

msgid "Failed to upgrade, the detail"
msgstr ""

msgid "Failed to upload LLM package."
msgstr ""

msgid "Failed to upload MPP SDK"
msgstr " "

msgid "Failed to upload error page template"
msgstr ""

msgid "Failed to upload geographical position lib file"
msgstr ""

msgid "Failed to upload license file"
msgstr " "

msgid "Failed to upload the file"
msgstr ""

msgid "Failed to verify signature of license."
msgstr ""

#, python-brace-format
msgid "Failed to {0} Sensitive Log Filter function"
msgstr "Failed to {0} PII Masking."

#, python-brace-format
msgid "Failed to {0} role management function"
msgstr ""

msgid "Failure message"
msgstr ""

msgid ""
"Falsification of the number of times an item such as an advert is clicked "
"on, or the number of times an advertisement is displayed. Performed by "
"owners of web sites displaying ads, competitors and vandals. See OAT-016 "
"Skewing instead for similar activity that does not involve web-placed "
"advertisements."
msgstr ""

msgid "Family Status"
msgstr ""

msgid "Favicon"
msgstr ""

msgid "Feature Name"
msgstr ""

msgid "Featured Response for Abnormal Request"
msgstr ""

msgid "Field Name"
msgstr "Header Name"

msgid "Field name {} is invalid!"
msgstr ""

msgid "Field server_key not found in the JSON file"
msgstr ""

msgid "Field type"
msgstr ""

msgid "Field upstream not found in the JSON file"
msgstr ""

msgid "Field {} can be used alone!"
msgstr "Field {} must not be used alone"

msgid "Field {} can not be used in function {} ."
msgstr ""

msgid "Field {} only allows the operator: {}!"
msgstr ""

msgid ""
"Fields that appear blank or do not exist in the statistics system have a "
"default value. When you need to use a filter to match these fields, you "
"should set these fields to the default values for successful matching. "
"Default values of different types of fields are listed below to ensure "
"proper configuration in this scenario."
msgstr ""
"Fields that appear blank or do not exist in the analytics system have a "
"default value. When you need to use a filter to match these fields, you "
"should set these fields to the default values for successful matching. "
"Default values of different types of fields are listed below to ensure "
"proper configuration in this scenario."

msgid "File Already Expired"
msgstr ""

msgid "File Already Expired or is not cached"
msgstr "This file has not been cached or has been deleted."

msgid "File Analysis"
msgstr ""

msgid "File Content"
msgstr ""

msgid "File Format"
msgstr ""

msgid "File Monitoring"
msgstr "File Transmission Monitoring"

msgid "File Monitoring Config"
msgstr "File Transmission Monitoring Config"

msgid "File Monitoring creation failed."
msgstr "File Transmission Monitoring creation failed."

msgid "File Name"
msgstr ""

msgid "File Size"
msgstr ""

msgid "File Size Threshold (byte)"
msgstr ""

msgid "File Uploaded Successfully"
msgstr "File upload completed."

msgid "File content is empty"
msgstr ""

msgid "File content is incorrectly encoded."
msgstr ""

msgid "File content is not valid CSV"
msgstr ""

msgid "File extension detection"
msgstr "Extensions"

msgid "File format"
msgstr ""

msgid "File name"
msgstr "Filename"

msgid "File size"
msgstr ""

msgid "File size (Byte)"
msgstr ""

msgid "File size exceed 5M."
msgstr ""

msgid "File size limit (byte)"
msgstr ""

#, python-brace-format
msgid "File {0} is empty"
msgstr ""

msgid "File_Transmission_Monitoring"
msgstr ""

msgid "Filename"
msgstr "Filename"

msgid "Filename Extension Filter Against HTTP Requests"
msgstr "File Extension in Requests"

msgid "Filename Extensions"
msgstr "Extensions"

msgid "Filename Filter Against HTTP Requests"
msgstr "Filename in Requests"

msgid "Filter"
msgstr ""

msgid "Filter "
msgstr ""

msgid "Filter Server’s Status Codes"
msgstr "Status Code"

msgid "Filter by IP"
msgstr ""

msgid "Filtering Condition"
msgstr ""

msgid "Financial Industry Classification"
msgstr "Financial Industry"

msgid "Fingerprint"
msgstr ""

msgid "Fingerprint Reputation"
msgstr ""

msgid "Fingerprint cannot be empty"
msgstr "Fingerprint is required."

msgid "Fingerprint0 count(fp0c)"
msgstr ""

msgid "Fingerprinting(OAT-004)"
msgstr ""

msgid "Fingerprints are not unique to individual device."
msgstr ""

msgid "First Discovered"
msgstr "First Match at"

msgid "First-Generation Mainland China ID Card"
msgstr "China 1st-Gen ID Card"

msgid "First-Generation Mainland China ID Card Number"
msgstr "China First-Generation Identity Card Number"

msgid "Fix it now"
msgstr ""

msgid "Flow Assignment"
msgstr "Learning Task"

msgid "Flow Collection"
msgstr "Traffic Capturing"

msgid "Flow Transparent"
msgstr "Traffic Pass-Through "

msgid "Flow collection"
msgstr "Traffic Capturing"

msgid "Flow restriction is applied to the target host"
msgstr "Destination host traffic threshold"

msgid "Flow self-learning"
msgstr "Traffic Self-Learning"

msgid "FlowLearn Service"
msgstr "Traffic Self-Learning Service"

msgid "Follow the steps listed below to add and apply compliance strategies."
msgstr ""

msgid "Following relate domain already exists in other app site"
msgstr "The domain name as shown below already exists in another application."

msgid ""
"Following relate domain and path prefix already exists in other app site"
msgstr ""
"The domain name and path prefix as shown below already exist in another "
"application."

msgid ""
"Follows apps have the same related domains and are referenced by {}, not "
"support to merge."
msgstr ""
"The application as shown below already has the same domain name and is "
"configured in {}, therefore cannot be merged."

msgid "Follows apps have the same related domains."
msgstr "The application as shown below already has the same domain name."

msgid "Font"
msgstr ""

msgid "Font color"
msgstr "Font Color"

msgid "Font size"
msgstr "Font Size"

msgid "Footprinting(OAT-018)"
msgstr ""

msgid ""
"For a better experience with the Webconsole, you can install the SSL "
"certificate provided below on your browsers."
msgstr ""

msgid ""
"For data that needs to be desensitized, it is necessary to ensure that the "
"data returned in the backend has been desensitized, and desensitization "
"cannot be done only in the frontend; for interfaces that have not been "
"modified in time, it is necessary to monitor the access to the interface, "
"pay attention to data exposure, and promptly discover the risk of abnormal "
"data acquisition."
msgstr ""
"Do data masking at backend and ensure it covers every piece of sensitive "
"information."

msgid ""
"For details of selected strategies, go to the page WAF > Compliance "
"Validation > Parameter Compliance."
msgstr ""

msgid "For example: \".asp, .php, .exe\""
msgstr "For example: .asp, .php, .exe"

msgid "For example: 8.8.8.8 or 8.8.8.8/30"
msgstr ""

msgid "For example: 8.8.8.8, 8.8.4.4."
msgstr ""

msgid ""
"For non-administrator, please contact the administrator to reset the "
"password. For the administrator, please click Next."
msgstr ""

msgid ""
"For parameters for accessing files, the complete file path should be "
"prohibited from being passed in the parameters, and it is necessary to "
"filter illegal characters in the parameters, such as ../, file://, etc."
msgstr ""
"Ensure APIs do not accept request parameters that use full paths of files as"
" their values or include disallowed characters such as ../ and file://."

msgid ""
"For setting protection scope and client-server two-way authentication. "
msgstr "Set the protection scope and client-server two-way authentication. "

msgid ""
"For the application of template, one ID will be randomly selected from the "
"entered IDs. If it is left blank, one template will be selected randomly "
"from the following templates"
msgstr ""
"A template will be randomly selected from the configured IDs. When the "
"configuration is left blank, a template will be selected randomly from all "
"the below templates."

msgid "Forbidden Action"
msgstr ""

msgid "Force Handshake authentication"
msgstr "Handshake Authentication"

msgid "ForceShield"
msgstr "Riversecurity"

msgid "ForceShield Dynamic Application Protection"
msgstr "Botgate"

msgid "Foreign Permanent Resident ID Card to China"
msgstr ""

msgid "Forgot your password?"
msgstr ""

msgid "Form Data Obfuscation"
msgstr "Obfuscate Form Data"

msgid "Form Decryption Error"
msgstr ""

msgid "Formatted Log Processor"
msgstr ""

msgid "Formatted Log Receiver"
msgstr ""

msgid "Forward"
msgstr ""

msgid "Forward "
msgstr ""

msgid "Forward: send current request to specific upstream"
msgstr "Forward: send the request to a designated server"

msgid "Forwarding target"
msgstr "Target"

msgid "Forwarding time"
msgstr "Timing"

msgid "Found by"
msgstr "Created by"

msgid ""
"Found referenced apps, or referenced APIs in apps. Please refresh the app "
"list and try again."
msgstr ""

msgid "France Passport Number"
msgstr "France Passport"

msgid "France Social Security Number"
msgstr ""

msgid "France Tax Identification Number"
msgstr ""

msgid "France VAT Number"
msgstr ""

msgid "Free"
msgstr ""

msgid "Free Memory"
msgstr ""

msgid "Frequence"
msgstr "Frequency"

msgid "Fri."
msgstr ""

msgid "From"
msgstr ""

msgid "Front Characters"
msgstr ""

msgid "Full Features"
msgstr ""

msgid "Full File Analysis"
msgstr ""

msgid "Full Match"
msgstr ""

msgid "Full Path"
msgstr ""

msgid "Full Request"
msgstr ""

msgid "Full Traffic Log"
msgstr "Full Traffic Log Filter "

msgid "Full flow collection is disabled"
msgstr ""

msgid "Full flow collection is enabled"
msgstr ""

msgid "Full-Screen Display"
msgstr ""

msgid "Function"
msgstr ""

msgid "Function tips"
msgstr ""

msgid "Function {} does not exist."
msgstr ""

msgid "Function {} only allows two input parameters."
msgstr ""

msgid "Fuzzy"
msgstr "Fuzzy match"

msgid "Fuzzy Match"
msgstr ""

msgid "Fuzzy Match without args"
msgstr ""

msgid "GET Only"
msgstr ""

msgid "GM Algorithm"
msgstr ""

msgid "GM Algorithm SDK"
msgstr ""

msgid "GPS position data"
msgstr ""

msgid "GRE protocol analysis"
msgstr "GRE Parsing"

msgid "Gateway"
msgstr ""

msgid "Gender"
msgstr ""

msgid "General"
msgstr ""

msgid "General Analytics"
msgstr ""

msgid "General Protection"
msgstr ""

msgid "General templates"
msgstr ""

msgid "Generate"
msgstr ""

msgid "Generate Certificate"
msgstr ""

msgid "Generate Mobile certificate."
msgstr "Generated mobile certificate"

msgid "Generate QRcode"
msgstr ""

msgid "Generate Strategy"
msgstr ""

msgid "Generate certificate failed!"
msgstr ""

msgid "Generate certificate file failed!"
msgstr ""

msgid "Geo Lib Update"
msgstr "Geolib Update"

msgid ""
"Geographical position lib file has been uploaded. Please check the latest "
"lib file in 1 minute."
msgstr ""
"Geolib has been uploaded. You can view the updated information in about 1 "
"minute."

msgid "Geolocation Correction"
msgstr ""

msgid ""
"Get the verification code by visiting the following URL or scanning the QR "
"code."
msgstr ""

msgid "Get verification code"
msgstr "Get Verification Code"

msgid "Global Custom Rule"
msgstr "Global Custom Rules"

msgid "Global Custom Rule details"
msgstr ""

msgid "Global Settings"
msgstr ""

msgid "Global WAF Whitelist"
msgstr "Global Whitelist"

msgid "Global WAF whitelist added failed"
msgstr "Global whitelist added failed"

msgid "Global WAF whitelist added successfully"
msgstr "Global whitelist added successfully"

msgid "Global WAF whitelist saved failed"
msgstr "Global whitelist saved failed"

msgid "Global WAF whitelist saved successfully"
msgstr "Saved Global Whitelist"

msgid "Global Whitelist"
msgstr ""

msgid "Global_Settings"
msgstr "Global Settings"

msgid "Go Back"
msgstr ""

msgid "Go Back to Login"
msgstr ""

msgid "Go To WebConsole"
msgstr "Go to WebConsole"

msgid "Go to"
msgstr "Go to Archived Log"

msgid ""
"Go to <b> Self-Learning </b> > <b> Learning Outcomes </b> to set compliance "
"strategies."
msgstr ""

msgid "Go to Data Collection Settings"
msgstr "Go to Business Data Collection"

msgid ""
"Go to System > Log > Archive Log to export the intelligence library file."
msgstr ""

msgid ""
"Go to System > Log > Archive Log to view and export the intelligence library"
" file named \"threat_info\"."
msgstr "Go to System > Logs > Archived Log to export threat events."

msgid "Go to home."
msgstr ""

msgid "GoodBot requests count(gbc)"
msgstr ""

msgid "Goodwill Robot"
msgstr "Good Bots"

msgid "GracePeriod14d"
msgstr "14-day grace period"

msgid "Grafana Business Component"
msgstr ""

msgid "Grafana Business Component APIs Auto-detection"
msgstr "Auto-Recognize Grafana-Business-Component APIs"

msgid "Gravity accelerometer"
msgstr ""

msgid "Greater than"
msgstr ""

msgid "Group"
msgstr ""

msgid "Group control"
msgstr "Group Control"

msgid "Gyroscope"
msgstr ""

#, python-brace-format
msgid "HA state has changed. Node role: {role}; Current state: {state}"
msgstr ""

msgid "HTML File Management"
msgstr ""

msgid "HTTP"
msgstr ""

msgid "HTTP 2.0 is not supported by this feature."
msgstr ""

msgid "HTTP Capture Advanced Configuration"
msgstr "Advanced Settings"

msgid "HTTP Capture Configuration"
msgstr "Traffic Capturing"

msgid "HTTP Capture Plugin Configuration"
msgstr ""

msgid "HTTP Capture Service"
msgstr ""

msgid "HTTP Port"
msgstr ""

msgid "HTTP Protocol Compatibility"
msgstr ""

msgid "HTTP Protocol Detections"
msgstr "HTTP Protocol Compliance"

msgid "HTTP Protocol Version"
msgstr ""

msgid "HTTP Request Headers"
msgstr ""

msgid "HTTP Request Methods to be Blocked"
msgstr "Request Methods"

msgid "HTTP Response Headers"
msgstr ""

msgid "HTTP Version, Colon-separated(pro)"
msgstr ""

msgid "HTTP header name"
msgstr ""

msgid "HTTP header value"
msgstr ""

msgid ""
"HTTP requests containing any of the following character strings will be "
"blocked by the system. Characters such as<span class='notice'> "
"'&nbsp;;&nbsp;<&nbsp;></span> are not allowed to be entered."
msgstr ""
"The requests with any of the keyword in their URLs will be blocked. (Do not "
"enter <span class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span>)"

msgid ""
"HTTP requests containing any of the following filename extensions will be "
"blocked by the system. Characters such as <span "
"class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span> are not allowed to be "
"entered."
msgstr ""
"The requests with any of the extensions in their URLs will be blocked. (Do "
"not enter <span class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span>)"

msgid ""
"HTTP responses containing any of the following character strings will be "
"blocked by the system. Characters such as <span "
"class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span> are not allowed to be "
"entered."
msgstr ""
"The responses with any of keywords will be blocked. (Do not enter <span "
"class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span>)"

msgid "HTTP to HTTPS"
msgstr ""

msgid ""
"HTTP(S) requests/responses of specified Method will also be forwarded "
"without protection policies."
msgstr ""
"HTTP(S) requests of special methods specified below and the corresponding "
"responses will be passed through without any protection policies."

msgid "HTTP/2 Protocol"
msgstr ""

msgid "HTTPS server IP cannot be empty."
msgstr "HTTPS server IP is reuqired."

msgid "HTTPS site names cannot exceed 64 characters."
msgstr ""

msgid "Hadoop Business Component"
msgstr ""

msgid "Hadoop Business Component APIs Auto-detection"
msgstr "Auto-Recognize Hadoop-Business-Component APIs"

msgid "Handling Fee"
msgstr ""

msgid "Handshake Path"
msgstr ""

msgid "Hardware"
msgstr ""

msgid "Have no this node: {}."
msgstr ""

msgid "Have no username field."
msgstr ""

msgid "Header Field"
msgstr "Header"

msgid "Header Name"
msgstr ""

msgid "Health Check"
msgstr "Node Health Check"

msgid "Health Checks"
msgstr "Health Check"

msgid "Health Examination"
msgstr "Health Check"

msgid "Health check"
msgstr "Health Check"

msgid "Heartbeat Network Port"
msgstr "Heartbeat"

msgid "Heartbeat Network Port Adapter"
msgstr "Heartbeat Network Adapter"

msgid "Height"
msgstr ""

msgid "Help"
msgstr ""

msgid "Hide"
msgstr ""

msgid "Hide Hardware Info"
msgstr ""

msgid "Hide WAF White List Log"
msgstr "Hide WAF Whitelist Entries"

msgid "High"
msgstr ""

msgid "High '/' usage"
msgstr ""

msgid "High '/var' usage"
msgstr ""

msgid "High Frequence"
msgstr "High Frequency"

msgid "High Frequence Scene"
msgstr "High Frequency Scenario"

msgid "High Frequence Statistics of Prevent Scanner"
msgstr "Frequency Statistics of Block Scanning"

msgid "High Nginx CPU usage"
msgstr ""

msgid "High Nginx memory usage"
msgstr ""

msgid "High Risk"
msgstr ""

msgid "High partition root usage"
msgstr "High disk usage (/)"

msgid "High partition var usage"
msgstr "High disk usage (/var)"

msgid "High system CPU usage"
msgstr "High CPU usage"

msgid "High system memory usage"
msgstr "High memory usage"

msgid "Hint:"
msgstr "Note: "

msgid "History Password Time"
msgstr "passwords"

msgid "Hit Count"
msgstr ""

msgid "Hit the max mpp count limit"
msgstr "Hit the max mini-program protect count limit"

msgid "Hobbies"
msgstr ""

msgid "Home Address"
msgstr ""

msgid "Honey Pot Check"
msgstr "Honey Pot"

msgid "Honey Pot Path"
msgstr ""

msgid "Hong Kong ID Card"
msgstr ""

msgid "Hong Kong Passport"
msgstr ""

msgid "Hong Kong Phone Number"
msgstr "Hong Kong Mobile Phone"

msgid "Hong Kong SAR ID Card"
msgstr "Hong Kong SAR ID Card"

msgid "Hong Kong SAR Passport"
msgstr "Hong Kong SAR Passport"

msgid "Hong Kong and Macau Residence Permit"
msgstr "Residence Permit for Hong Kong and Macao Residents"

msgid "Hong Kong and Macau Residence Permit of the People's Republic of China"
msgstr ""
"Residence Permit Issued by the People's Republic of China for Hong Kong and "
"Macao Residents"

msgid "Hong Kong and Macau Residents' Travel Permit to Chinese Mainland"
msgstr "Mainland Travel Permit for Hong Kong and Macao Residents"

msgid "Hong Kong and Macau Travel Permit"
msgstr "Exit-Entry Permit for Travelling to and from Hong Kong and Macao"

msgid "Hook framework detection"
msgstr ""

msgid "Hospitalization Number"
msgstr ""

msgid "Hospitalization Record"
msgstr ""

msgid "Host"
msgstr "Host"

msgid "Host Header"
msgstr "Host"

msgid "Host Header:"
msgstr ""

msgid "Host Name"
msgstr ""

msgid "Host can't have upper case char"
msgstr ""

msgid "Host in HTTP header."
msgstr "Host"

msgid "Hot Standby Deployment"
msgstr "Hot Standby"

msgid "Hot Standby Settings"
msgstr ""

msgid "Hotel Check-in Record"
msgstr ""

msgid "Hour"
msgstr ""

msgid "Hour:Minute"
msgstr ""

msgid "How to Analyze"
msgstr ""

msgid "Human-Bot Verification"
msgstr ""

msgid "Human-Bot Verification APIs Auto-detection"
msgstr "Auto-Recognize Human-Bot-Verification APIs"

msgid "ICMP Flood Defense:"
msgstr "ICMP Flood Protection"

msgid "ID"
msgstr "ID"

msgid ""
"IEEE 802.3ad Dynamic link aggregation. Creates aggregation groups that share"
" the same speed and duplex settings. Utilizes all slaves in the active "
"aggregator according to the 802.3ad specification."
msgstr ""
"IEEE 802.3ad Dynamic link aggregation. Creates aggregation groups that share"
" the same speed and duplex settings. Utilizes all adapters in the active "
"aggregator according to the 802.3ad specification."

msgid "IMEI Detection"
msgstr "International Mobile Equipment Identity"

msgid "IP"
msgstr ""

msgid "IP (Class C)"
msgstr ""

msgid "IP ({}) already exists"
msgstr ""

msgid "IP ({}) does not exist"
msgstr ""

msgid ""
"IP 0.0.0.0 means all IP.If you set IP address,then service means the IP you "
"entered."
msgstr ""
"Specify IP or IP segment of the web server(s). Leaving the configuration "
"empty or entering 0.0.0.0 means all IPs."

msgid "IP Address"
msgstr ""

msgid "IP Blacklist"
msgstr ""

msgid "IP Filter"
msgstr "Client IPs"

msgid "IP Hash"
msgstr ""

msgid "IP Reputation"
msgstr ""

msgid "IP Verification"
msgstr ""

msgid "IP Version"
msgstr ""

msgid "IP Whitelist"
msgstr ""

msgid "IP abuse by accounts"
msgstr "Account IP Abuse"

#, python-brace-format
msgid "IP address and gateway are not in the same network segment:{0}: {1}."
msgstr ""

#, python-brace-format
msgid "IP address conflict with {0} port:{1}: {2}."
msgstr ""

msgid "IP address conflict."
msgstr ""

msgid "IP blacklist reset for blocking on mirror deployment"
msgstr ""

msgid "IP cannot be empty"
msgstr "IP is required."

msgid "IP fingerprint"
msgstr "IP + Fingerprint"

msgid "IP has been deleted from auto black ip"
msgstr ""

msgid "IP high-frequency calls"
msgstr "IP High-Frequency Calls"

msgid "IP is locked"
msgstr ""

msgid "IP is required."
msgstr ""

msgid ""
"IP is unreachable possibly because the cluster safety switch of the "
"specified node is turned on or the port cannot be accessed."
msgstr ""

msgid "IP must be the string type"
msgstr ""

msgid "IP of primary authentication server is blank."
msgstr ""

msgid "IP of secondary authentication server is blank."
msgstr ""

msgid "IP or Domain"
msgstr ""

msgid "IP or IP/prefix"
msgstr "IP or network segment with prefix"

msgid "IP or URL is required"
msgstr ""

msgid ""
"IP or network segment, e.g. ************* or ***********/24 or fd00::1 or "
"fd00::0/16"
msgstr ""

msgid "IP should be "
msgstr ""

msgid "IP whitelist count(ipwc)"
msgstr ""

msgid "IP-Based Protection"
msgstr "IP Whitelist"

msgid "IP/FP change count(ifc)"
msgstr ""

msgid "IPv4"
msgstr ""

msgid "IPv4 Address"
msgstr ""

msgid ""
"IPv4 address of management network adapter of any node in the cluster is "
"required."
msgstr ""
"Enter the eth0 IPv4 address of any existing node and the admin password to "
"join a cluster."

msgid "IPv4 of management network adapter"
msgstr "IPv4 of eth0"

msgid ""
"IPv4, IPv6(surrounded with square brackets), domain names and regular "
"expressions(start with ~^) are supported. The default value means all hosts."
msgstr ""
"IPv4, IPv6, domain names and regex (start with ~^) are supported. The "
"default regex ~^.*$ means all hosts."

msgid "IPv4/IPv6/Domain"
msgstr ""

msgid "IPv6 Address"
msgstr ""

msgid "IPv6 address conflict."
msgstr ""

msgid ""
"Identification of valid token codes providing some form of user benefit "
"within the application. The benefit may be a cash alternative, a non-cash "
"credit, a discount, or an opportunity such as access to a limited offer. For"
" cracking of usernames, see OAT-007 Credential Cracking instead."
msgstr ""

msgid "Identify good bot by static static goodbots file, or custom UA."
msgstr ""
"Identify good bots by means of database query, DNS reverse lookup, or "
"specified UA."

msgid ""
"Identify scanners by trap link, special crawler path and other path-related "
"features."
msgstr ""

msgid ""
"If Host is the only condition configured, all rules are not effective to the"
" requests with any of the hosts."
msgstr ""

msgid ""
"If Method is the only condition configured, all rules are not effective to "
"the requests with any of the methods."
msgstr ""

msgid ""
"If PIIs such as key ID card number or mobile phone number is found in the "
"URL, the URL will appear in the log, and it may also be sent to a third "
"party through referer. At the same time, the URL may also be seen by the "
"user on the browser page, resulting in information being peeped."
msgstr ""
"PII such as personal identities or mobile phone numbers included in URL "
"parameters may be obtained by third parties through logs, referers or "
"browsers' address bars."

msgid ""
"If Rule ID is the only condition configured, the specified rules are "
"disabled."
msgstr ""

msgid ""
"If Source IP is the only condition configured, all rules are not effective "
"to the requests sent from the IPs."
msgstr ""

msgid ""
"If URL is the only condition configured, all rules are not effective to the "
"requests with any of the URLs."
msgstr ""

msgid ""
"If a large amount of PIIs is found in the returned content of the interface,"
" it means that the interface does not limit the amount of returned data, "
"which is easy to cause a large amount of data leakage."
msgstr ""
"Multiple sensitive information pieces of the same type included in a single "
"response have not been masked or deleted."

msgid ""
"If a plain text password is found in the returned content of the interface, "
"the attacker can monitor or intercept it during the transmission process to "
"obtain the password information."
msgstr ""
"Plaintext passwords included in responses may be obtained by third parties "
"when they are being transferred on networks."

msgid ""
"If access matches the following path, cross-domain requests will be blocked."
msgstr ""
"Cross-domain requests that match entries under \"Requests to be Checked\" "
"will be checked against their referers."

msgid ""
"If agent in used and method rule or body rule is configured, Please upgrade "
"it to version 1.7 or higher，otherwise filter rule in agent cannot work "
"normally."
msgstr ""

msgid ""
"If agent in used and path rule is configured, Please upgrade it to version "
"1.4 or higher，otherwise filter rule in agent cannot work normally."
msgstr ""

msgid ""
"If an executable database query statement is found in the request parameters"
" of the interface, the attacker can modify the parameters to bypass the "
"system restrictions and query important data in the system, or even execute "
"malicious statements, causing the system data to be destroyed."
msgstr ""
"Database queries are found in parameters of requests that have been accepted"
" by APIs. It may lead to major damages to your facilities."

msgid ""
"If an executable system command is found in the request parameters of the "
"interface, the attacker can modify the parameters to bypass the system "
"restrictions and execute the system command, which may lead to the leakage "
"of important data in the system, the attacker obtains the server control "
"authority or causes the system service to be unavailable."
msgstr ""
"System commands are found in parameters of requests that have been accepted "
"by APIs. It may lead to major damages to your facilities."

msgid "If can`t access port 20147,please use the following link."
msgstr ""

msgid "If enable it again, you must add the site to mini program by manually."
msgstr ""
"If you enable it again, you must add the site to mini-program manually."

msgid ""
"If enabled, PII contained in access log stored in the system will be masked "
"according to the following settings."
msgstr ""

msgid ""
"If enabled, the files being downloaded are saved in the system as copies as "
"long as they meet any conditions above."
msgstr ""

msgid ""
"If need more security, please enable enchance_cluster_security manually."
msgstr "For more security, please enable [Enhance Cluster Security] manually."

msgid ""
"If password or password hash information is found in the returned content of"
" the interface, the attacker can monitor or intercept it during the "
"transmission process to obtain password information."
msgstr ""
"Passwords or hashed passwords included in responses may be obtained by third"
" parties when they are being transferred on networks."

msgid "If the master server or password has not been changed, it is optional"
msgstr "Empty means no change"

msgid ""
"If the password is found in the URL, the URL will appear in the log, and it "
"may also be sent to a third party through referer. At the same time, the URL"
" may also be seen by the user on the browser page, resulting in information "
"being peeped."
msgstr ""
"Passwords included in URL parameters may be obtained by third parties "
"through logs, referers or browsers' address bars."

msgid ""
"If the robot suspiciousness (captcha_bot_prob) is greater than the set "
"value, it is determined that the challenge has failed."
msgstr ""

msgid ""
"If the statistics subsystem would be out of service within a period of time "
"if violation of Indexing Volume Limit happened more than 5 times in the last"
" 30 days."
msgstr ""
"Analytics would be out of service within a period of time if indexing volume"
" has been exceeded more than 5 times in 30 days."

msgid ""
"If the status code of a response from server to client matches any one of "
"the following, the response will be filtered. Characters such as <span "
"class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span> are not allowed to be "
"entered."
msgstr ""
"The responses with any of the status codes will be blocked. (Do not enter "
"<span class='notice'>'&nbsp;;&nbsp;&lt;&nbsp;&gt;</span>)"

msgid ""
"If the status code of a response from server to client matches any one of "
"the following, the response will be filtered. Note that only positive "
"integers are allowed below."
msgstr ""
"The responses with any of the status codes below will be blocked. Note that "
"only positive integers are allowed below."

msgid ""
"If there is no big data analysis P (storage) node in cluster and there is "
"big data analysis S node in cluster, please change the report type to "
"sailfish."
msgstr ""

msgid ""
"If you do not receive test email, please check whether your sender and "
"receiver are correct or not."
msgstr ""

msgid ""
"If you do not receive test message, please check whether your gateway and "
"receiver number are correct or not."
msgstr ""

msgid ""
"If you modify User Unique ID, all learning results will be cleared. Are you "
"sure to modify the configuration?"
msgstr ""

msgid "Ignore"
msgstr ""

msgid ""
"Ignore API total count must not exceed {}. Please reduce ignore API(s) to "
"below the limit and operate again."
msgstr ""

msgid "Ignore api already existed!"
msgstr ""

msgid "Ignored"
msgstr ""

msgid "Illegal Download Protection"
msgstr "Illegal Download"

msgid "Illegal path"
msgstr ""

msgid "Import"
msgstr ""

msgid "Import Attack Policy File Failed"
msgstr ""

msgid "Import System Settings"
msgstr ""

msgid "Import Websites"
msgstr ""

msgid "Import failed"
msgstr ""

msgid ""
"Import failed. The number of mini-programs in the settings file has exceeded"
" the licensed limit."
msgstr ""

msgid ""
"Import failed. The number of mobile apps in the settings file has exceeded "
"the licensed limit."
msgstr ""

msgid ""
"Import failed. The number of protected websites in the settings file has "
"exceeded the licensed limit."
msgstr ""

msgid "Import risk library to improve SDK protection ability."
msgstr ""

msgid "Import successfully"
msgstr ""

msgid ""
"Import system settings maybe overwrite some currently settings. Are you sure"
" to import?"
msgstr ""
"The imported system settings will overwrite existing settings. Are you sure "
"to import?"

#, python-brace-format
msgid ""
"Import the WAF whitelist configuration for the site {upstream_key} from the "
"security log."
msgstr ""

#, python-brace-format
msgid ""
"Import the WAF whitelist configuration for the site {upstream_key} from the "
"waf misreport analyse."
msgstr ""
"Import the WAF whitelist configuration for the site {upstream_key} from the "
"False Positive Analysis."

#, python-brace-format
msgid ""
"Import the alipay mpp request whitelist configuration for the site "
"{upstream_key} from the security log."
msgstr ""
"Import the alipay mini-program request whitelist configuration for the site "
"{upstream_key} from the security log."

#, python-brace-format
msgid ""
"Import the encapsulation list configuration for the site {upstream_key} from"
" the security log."
msgstr ""

#, python-brace-format
msgid ""
"Import the mobile mpp request whitelist configuration for the site "
"{upstream_key} from the security log."
msgstr ""
"Import the ChinaMobile mini-program request whitelist configuration for the "
"site {upstream_key} from the security log."

#, python-brace-format
msgid ""
"Import the request whitelist configuration for the site {upstream_key} from "
"the security log."
msgstr ""

#, python-brace-format
msgid ""
"Import the wechat request whitelist configuration for the site "
"{upstream_key} from the security log."
msgstr ""

msgid "Imported API list"
msgstr ""

msgid "Imported Application list"
msgstr ""

msgid "In Effect"
msgstr ""

msgid "In Effective"
msgstr "Effective"

msgid ""
"In interface design, saving user passwords in cookies may be intercepted by "
"man-in-the-middle attacks to obtain password information, or leak password "
"information when the browser is borrowed by others."
msgstr ""
"Passwords included Cookies may be obtained by third parties through MITM "
"attacks or clients' computers."

msgid "In the List"
msgstr ""

msgid ""
"In the SMS sending interface, the mobile phone number and SMS content are "
"found in the request parameters, and malicious attackers may use the "
"interface to send malicious SMS to the specified mobile phone."
msgstr ""
"SMS messages are found in parameters of requests that have been accepted by "
"APIs. It may allow attackers send spam SMS messages to anyone."

msgid ""
"In the return of interface events, it is found that for the same type of "
"PIIs, both desensitized and non-desensitized data are returned to the front "
"end at the same time. The original PIIs that is not desensitized can be "
"found by viewing the detailed request and return of the interface, but the "
"purpose of data desensitization is not truly achieved."
msgstr ""
"Data masking did not cover all sensitive information of the same type such "
"as phone numbers in responses."

msgid "Inactive"
msgstr ""

msgid "Inappropriate Prompt of Failed Logins"
msgstr "Inappropriate Login Message"

msgid "Inappropriate Prompt of Login Errors"
msgstr ""

msgid "Include"
msgstr ""

msgid "Include invalid regular expression of {}"
msgstr ""

msgid "Include sensitive path(ds_sensitive_path)"
msgstr ""

msgid "Include(no regular expression)"
msgstr ""

msgid "Inconsistent Desensitization Policies"
msgstr "Inconsistent Data Masking"

msgid "Inconsistent deployment model"
msgstr ""

msgid "Inconsistent passwords entered"
msgstr ""

msgid "Incorrect Enable/Disable info."
msgstr ""

msgid "Incorrect IP"
msgstr ""

msgid "Incorrect Mobile App Requests"
msgstr ""

msgid "Incorrect admin password. Please check again"
msgstr ""

msgid "Incorrect authentication code."
msgstr ""

msgid "Incorrect destination address"
msgstr ""

msgid ""
"Incorrect format: multiple ports or port ranges must be seperated with "
"commas. Port ranges must be in the format of start port-end port, and port "
"value be a 1024-65000 integer."
msgstr ""

msgid "Incorrect gateway"
msgstr ""

msgid "Incorrect netmask"
msgstr ""

msgid "Incorrect network information"
msgstr ""

msgid "Incorrect password."
msgstr ""

msgid "Incorrect prefix length"
msgstr ""

msgid ""
"Incorrect server password or source IP of authenticaton requests is not "
"found on the server."
msgstr ""
"Incorrect server password or source IP of authenticaton requests is not "
"found on the server."

msgid "Incorrect username or password, or account/IP is locked."
msgstr ""

#, python-format
msgid "Index of %(directory)s"
msgstr ""

msgid "Indexing Field"
msgstr "Include Fields"

msgid "Indexing Volume Limit happened more than 5 times in the last 30 days."
msgstr ""

msgid "Indexing volume has exceeded limit"
msgstr ""

msgid "India Driver's License Number"
msgstr ""

msgid "India GST Number"
msgstr ""

msgid "India Goods and Services Tax Number"
msgstr ""

msgid "Indispensable"
msgstr ""

msgid "Industry Classification"
msgstr ""

msgid "Industry Data Classification"
msgstr "Industry Categorization"

msgid "Industry Level"
msgstr ""

msgid "Industry data cannot contain duplicate Tags:{}"
msgstr ""

msgid "Industry types"
msgstr ""

msgid "InfluxDB Business Component"
msgstr ""

msgid "InfluxDB Business Component APIs Auto-detection"
msgstr ""

msgid "Info"
msgstr ""

msgid "Information about the app is running on a high-risk device"
msgstr ""

msgid ""
"Information gathering with the objective of learning as much as possible "
"about the composition, configuration and security mechanisms of the "
"application. Unlike Scraping, Footprinting is an enumeration of the "
"application itself, rather than the data. It is used to identify all the URL"
" paths, parameters and values, and process sequences (i.e. to determine "
"entry points, also collectively called the attack surface). As the "
"application is explored, additional paths will be identified which in turn "
"need to be examined. Footprinting can also include brute force, dictionary "
"and guessing of file and directory names. Fuzzing may also be used to "
"identify further application resources and capabilities. However, it does "
"not include attempts to exploit weaknesses."
msgstr ""

msgid "Initiated a scan task with task result pending on actual conditions"
msgstr ""

msgid "Initiated a verify task"
msgstr ""

msgid "Inject Attack Types"
msgstr ""

msgid "Inject Location"
msgstr ""

msgid "Inject Payload"
msgstr ""

msgid "Inject Rule ID"
msgstr ""

msgid "Inject attack(ds_inject)"
msgstr ""

msgid "Inject requests count(ijc)"
msgstr ""

msgid "Inject rule count(irc)"
msgstr ""

msgid "Injections"
msgstr ""

msgid "Input DNS"
msgstr ""

msgid "Input custom host and ip, one rule for each rule, and split with space"
msgstr ""
"Enter Hostname-IP mapping rules, separated by newline. Hostname and IP "
"should be separated by space, and port numbers are required for both "
"hostname and IP. Example: www.a.com:80 ***********:80"

msgid "Input data is out of range"
msgstr ""

msgid "Input string is invalid."
msgstr ""

msgid "Insert JS"
msgstr ""

msgid ""
"Insert event ID in the response correspondingly when an abnormal request is "
"blocked by WAF."
msgstr ""

msgid "Insert the following header when the server responds to the client."
msgstr ""
"The values entered for headers below will be used to replace those in "
"responses or insert in before forwarding to clients."

msgid "InsertJS"
msgstr "Insert JS"

msgid "InsertJS: insert JS code to the response file"
msgstr "Insert JS: insert JS code into the response"

msgid "Install"
msgstr ""

msgid "Install "
msgstr ""

msgid "Install automation tools requests count(iac)"
msgstr ""

msgid "Install automation tools(ds_install_automation_tool)"
msgstr ""

msgid "Install count(uic)"
msgstr ""

msgid "Install debug requests count(idc)"
msgstr ""

msgid "Install debug tool(ds_install_debug_tool)"
msgstr ""

msgid "Install manually after downloading"
msgstr "Install manually after download"

msgid "Install modification requests count(imc)"
msgstr ""

msgid "Install modification tools(ds_install_modify_tool)"
msgstr ""

msgid "Install now"
msgstr ""

msgid "Install virsual environment tool(ds_install_virsual_environment_tool)"
msgstr ""

msgid "Install virsual environment tools request count(ivec)"
msgstr ""

msgid "Installation Successful."
msgstr "Installed successfully."

msgid "Installation between debug and release patch is not allowed"
msgstr ""

msgid "Installation between patches of different layout is not allowed"
msgstr ""

msgid "Installation failed."
msgstr "Install failed."

msgid "Installation method"
msgstr "Installation Options"

msgid "Installed"
msgstr ""

msgid "Insufficient NIC, unable to configure"
msgstr ""

msgid "Insufficient disk space"
msgstr ""

msgid ""
"Insufficient memory, please increase memory or reduce query time range."
msgstr ""

msgid "Integer"
msgstr ""

msgid "Integer:"
msgstr ""

msgid "Integrity Checking"
msgstr "App Integrity"

msgid "Integrity check has been disabled."
msgstr "App Integrity has been disabled"

msgid "Integrity check has been enabled."
msgstr "App Integrity has been enabled"

msgid "Intelligent detection of access mode"
msgstr "Access Pattern Intelligent Detection"

msgid "Intercept the risk requests from Apps."
msgstr "Block high-risk requests from below apps"

msgid "Interest"
msgstr ""

msgid "Interested widget not hited count(iwhc)"
msgstr ""

msgid "Interested widget not hited(ds_widget_not_hited)"
msgstr ""

msgid "Interface Permissions"
msgstr "Access Permissions"

msgid "Interface exception"
msgstr ""

msgid "Interference Port"
msgstr ""

msgid "Internal Network Adapter"
msgstr "Internal"

msgid "Internal Resource Path"
msgstr ""

msgid "International Credit Card"
msgstr ""

msgid "International Credit Card Number"
msgstr ""

msgid "Internet Data Collection"
msgstr ""

msgid "Internet Data Collection APIs Auto-detection"
msgstr "Auto-Recognize Internet-Data-Collection APIs"

msgid "Internet Data Exposure"
msgstr ""

msgid "Internet Data Exposure APIs Auto-detection"
msgstr "Auto-Recognize Internet-Data-Exposure APIs"

msgid "Internet Protocol Version 4"
msgstr ""

msgid "Internet Protocol Version 6"
msgstr ""

msgid "Interval"
msgstr "Every"

msgid "Intranet"
msgstr "Private Network"

msgid "Intranet Address"
msgstr "Private IP"

msgid "Intranet IP"
msgstr "Private IP"

msgid "Intranet PIIs Access"
msgstr "Private-Network PII Access"

msgid "Intranet PIIs Human-Bot Access"
msgstr "Private-Network PII Human-Bot Access"

msgid "Intranet PIIs Human-Bot Access APIs Auto-detection"
msgstr "Auto-Recognize Private-Network-PII Human-Bot-Access APIs"

msgid "Introduction of advanced filter and syntax description"
msgstr ""

msgid "Invalid API Args"
msgstr ""

msgid "Invalid Account"
msgstr ""

msgid "Invalid Account and Hostname"
msgstr ""

msgid "Invalid Certificate file."
msgstr ""

msgid "Invalid DNS"
msgstr ""

msgid "Invalid DNS."
msgstr ""

msgid "Invalid DNS.."
msgstr ""

msgid "Invalid Defect Name"
msgstr ""

msgid "Invalid FP"
msgstr ""

msgid "Invalid Format"
msgstr ""

msgid "Invalid Frequency"
msgstr ""

msgid "Invalid Host Header"
msgstr ""

msgid "Invalid IP"
msgstr ""

msgid "Invalid IP address"
msgstr ""

msgid "Invalid IP address of external network adapter."
msgstr ""

msgid "Invalid IP address of internal network adapter."
msgstr ""

msgid "Invalid IP address of management network adapter."
msgstr ""

msgid "Invalid IP address: {}"
msgstr ""

msgid "Invalid IP found in Custom rules."
msgstr ""

msgid "Invalid IP found in Threat Intelligence White List."
msgstr "Invalid IP or network segment found in Threat Intelligence Whitelist."

msgid "Invalid IP or IP network segment。"
msgstr "Invalid IP or IP network segment."

msgid "Invalid JSON data for remote authentication server."
msgstr ""

msgid "Invalid JSON file"
msgstr ""

msgid "Invalid LLM path"
msgstr ""

msgid "Invalid List format of {}, actual format is {}"
msgstr ""

msgid "Invalid MPP SDK"
msgstr ""

msgid "Invalid MPP type"
msgstr ""

msgid "Invalid Mac Address"
msgstr ""

msgid "Invalid PII class: {}"
msgstr ""

msgid "Invalid Patch"
msgstr ""

msgid "Invalid Port"
msgstr ""

msgid "Invalid Private key file."
msgstr ""

msgid "Invalid RegEx of WebConsole Host White Lists."
msgstr "Invalid RegEx of WebConsole Host Whitelist."

msgid "Invalid Request Parameter"
msgstr ""

msgid "Invalid Risk Type"
msgstr ""

msgid "Invalid String format of {}, actual format is {}"
msgstr ""

msgid "Invalid WAF rule file."
msgstr "Invalid file."

msgid "Invalid WSDL file."
msgstr ""

msgid "Invalid WebConsole Host White Lists"
msgstr "Invalid WebConsole Host Whitelist"

msgid "Invalid X-Frame-Option settings"
msgstr ""

msgid "Invalid X-XSS-Protection settings"
msgstr ""

msgid "Invalid XSD file."
msgstr ""

#, python-brace-format
msgid "Invalid adapter settings for:{0}: {1}."
msgstr ""

msgid "Invalid appID"
msgstr "Invalid AppID"

msgid "Invalid appName"
msgstr "Invalid app name"

msgid "Invalid attack policy ID"
msgstr ""

msgid "Invalid boolean type: {}"
msgstr ""

msgid "Invalid business type ID"
msgstr ""

msgid "Invalid character "
msgstr ""

msgid "Invalid character(s) found "
msgstr ""

msgid "Invalid character(s) found in CSRF Path List."
msgstr ""

msgid "Invalid character(s) found in CSRF site whitelist!"
msgstr ""

#, python-brace-format
msgid "Invalid character(s) found in {0}."
msgstr ""

msgid "Invalid character(s) found!"
msgstr "Invalid character(s) found in path(s)!"

msgid "Invalid confirmation code."
msgstr ""

msgid "Invalid data"
msgstr ""

msgid "Invalid data type"
msgstr ""

msgid "Invalid decode sequence"
msgstr ""

msgid "Invalid domain"
msgstr ""

msgid "Invalid encryption certificate key file."
msgstr ""

msgid "Invalid encryption private key file."
msgstr ""

msgid "Invalid expression(s)."
msgstr ""

msgid "Invalid field format: {}"
msgstr ""

msgid "Invalid field type: {}"
msgstr ""

msgid "Invalid field value: {}"
msgstr ""

msgid "Invalid file"
msgstr ""

msgid "Invalid filename format of resource file"
msgstr ""

msgid "Invalid filename format of resource waf file"
msgstr "Invalid file."

msgid "Invalid filter item: {}"
msgstr ""

msgid "Invalid fingerprint. Please contact us."
msgstr ""

msgid "Invalid format in WAF Cache Headers !"
msgstr ""

msgid "Invalid format."
msgstr ""

msgid "Invalid gateway of external network adapter."
msgstr ""

msgid "Invalid gateway of management network adapter."
msgstr ""

msgid "Invalid input"
msgstr ""

msgid "Invalid int type :{} is {}"
msgstr ""

msgid "Invalid key name"
msgstr ""

msgid "Invalid keyword type"
msgstr ""

msgid "Invalid license file"
msgstr ""

msgid "Invalid licensed period. Please contact us."
msgstr ""

msgid "Invalid mpp handshake path"
msgstr "Invalid mini-program handshake path"

msgid "Invalid name, A name can only consist of letters and numbers."
msgstr ""

msgid "Invalid netmask of external network adapter."
msgstr ""

msgid "Invalid netmask of internal network adapter."
msgstr ""

msgid "Invalid netmask of management network adapter."
msgstr ""

msgid "Invalid operation type"
msgstr ""

msgid "Invalid parameter"
msgstr ""

msgid "Invalid parameter in virtual IP."
msgstr ""

msgid "Invalid path"
msgstr ""

msgid "Invalid path prefix."
msgstr ""

msgid "Invalid port of management network adapter."
msgstr ""

msgid "Invalid post body"
msgstr ""

msgid "Invalid prompt list format"
msgstr ""

msgid "Invalid regular expression"
msgstr ""

msgid "Invalid regular expression of AI WAF White List."
msgstr "Invalid regular expression of AI WAF Whitelist."

msgid "Invalid regular expression of CSRF."
msgstr ""

msgid "Invalid regular expression of Content-Type Correction List."
msgstr ""

msgid "Invalid regular expression of Host."
msgstr ""

msgid "Invalid regular expression of Mobile Protection White List."
msgstr "Invalid regular expression of Mobile Protection Whitelist."

msgid "Invalid regular expression of Request Path."
msgstr ""

msgid "Invalid regular expression of Response Path."
msgstr ""

msgid "Invalid regular expression of URL."
msgstr ""

msgid "Invalid regular expression of WAF decode url parameter."
msgstr ""

msgid "Invalid regular expression of WAF decode url."
msgstr ""

msgid "Invalid regular expression of White List."
msgstr "Invalid regular expression of whitelist."

msgid "Invalid regular expression of user-defined rules. Id: {}. Place: {}"
msgstr ""

msgid "Invalid regular expression of user-defined rules. Place: {}"
msgstr ""

msgid "Invalid regular expression of websocket path."
msgstr ""

#, python-brace-format
msgid "Invalid regular expression of {0}."
msgstr ""

msgid "Invalid regular expression: {}"
msgstr ""

msgid "Invalid related domains."
msgstr ""

msgid "Invalid request"
msgstr ""

msgid "Invalid risk."
msgstr ""

msgid "Invalid route IP."
msgstr ""

msgid "Invalid route gateway."
msgstr ""

msgid "Invalid route netmask."
msgstr ""

msgid "Invalid rule ID {}"
msgstr ""

msgid "Invalid rule status"
msgstr ""

#, python-brace-format
msgid "Invalid rule:{0}, cause:{1}."
msgstr ""

msgid "Invalid service port"
msgstr ""

msgid "Invalid signature certificate file."
msgstr ""

msgid "Invalid signature private key file."
msgstr ""

msgid "Invalid source IP acquisition strategy"
msgstr ""

msgid "Invalid static goodbot version."
msgstr ""

msgid "Invalid strategy name."
msgstr ""

#, python-brace-format
msgid "Invalid template ID: [{0}]"
msgstr ""

msgid "Invalid timeout"
msgstr ""

msgid "Invalid token id"
msgstr ""

msgid "Invalid trap IP"
msgstr ""

msgid "Invalid trap port"
msgstr ""

msgid "Invalid upload file."
msgstr ""

msgid "Invalid uploading risk library signature!"
msgstr ""

msgid "Invalid upstream weight"
msgstr ""

msgid "Invalid username or password."
msgstr ""

msgid "Invalid virtual IP address."
msgstr ""

msgid "Invalid virtual Prefix Length ."
msgstr ""

msgid "Invalid virtual netmask."
msgstr ""

msgid "Invalid waf whitelist id."
msgstr ""

msgid "Invalid waf whitelist path."
msgstr ""

msgid "Invalid weak password!"
msgstr "Invalid weak password."

msgid "Invalid wechat appid"
msgstr "Invalid AppID"

msgid "Invalid zip file."
msgstr ""

#, python-brace-format
msgid ""
"Invalid! Please enter a number greater than {0} and less than or equal to "
"{1}"
msgstr ""

msgid "Invalid_Format request count(ivf)"
msgstr ""

msgid "Invoice Number"
msgstr ""

msgid "Invoice Registration Information"
msgstr ""

msgid "Is Appeared"
msgstr ""

msgid "Is it the default template?"
msgstr "Use Default Template?"

msgid "Is it transparent"
msgstr ""

msgid "Issue Challenge"
msgstr ""

msgid ""
"It allows clients to keep source IP when communicate with upstream servers. "
"Note: it should use the same IP protocol from client to Botgate and from "
"Botgate to upstream server; and using this feature may cause link problem "
"between client and upstream server."
msgstr ""
"It allows requests to keep the client-end source IP unchanged when "
"communicating with upstream servers. Note: the same IP protocol should be "
"used from client to system and from the system to upstream server; and "
"enabling/disabling this feature may cause connection problems between client"
" and upstream server."

msgid ""
"It allows the DAP system to add a Charset in Content-Type field of response "
"headers if no Charset is found in the field."
msgstr ""
"It allows the system add charset to the Content-Type header of responses if "
"charset is not found."

msgid ""
"It can quickly identify potential security threats based on the intelligent "
"threat detection engine and AI large model analysis."
msgstr ""

msgid ""
"It is a Chrome and WebKit only directive that tells the user-agent to report"
" potential XSS attacks to a single URL. Data will be posted to the report "
"URL in JSON format."
msgstr ""

msgid ""
"It is also known as SMS bomber, which is used to send repeated spam messages"
" to a mobile phone number through special software for the purpose of "
"harassment and prank. Its technique is actually very simple. Nowadays, more "
"and more online functions such as shopping, registration and information "
"confirmation, require mobile phone verification, all requiring to send a "
"verification code to the phone. SMS bomber takes advantages of these "
"verification codes, and uses special software to keep asking for "
"verification codes. Therefore, those legitimate websites accidentally act as"
" an accomplice to keep sending verification messages to the victims, just "
"like throwing numerous bombs. Important text messages previously saved on "
"mobile phones would be lost because hundreds of thousands of spam text "
"messages are received at the same time. Some mobile phones do not support "
"batch deletion of all messages, and some mobile phones will automatically "
"delete old text messages, which may cause the loss of important information."
msgstr ""

#, python-brace-format
msgid "It is duplicated with {0} in custom rules."
msgstr ""

msgid ""
"It is necessary to check whether the query parameters related to the return "
"quantity in the interface are restricted to prevent the interface from being"
" maliciously used to obtain a large amount of data at a time; for interfaces"
" that have not been modified in time, it is necessary to monitor the "
"interface access situation, pay attention to data exposure, and promptly "
"discover the risk of abnormal data acquisition."
msgstr ""
"Put a limit on parameters that are used to specify data amount returned by "
"servers."

msgid ""
"It is necessary to encrypt the password or use the https protocol to "
"transmit it to reduce the possibility of leaking the password when the "
"traffic is monitored."
msgstr "Encrypt passwords or use HTTPs."

msgid ""
"It is necessary to limit the interface to return password information to "
"reduce the possibility of passwords being obtained by others; for interfaces"
" that have not been modified, it is necessary to monitor interface access to"
" promptly discover the risk of malicious acquisition of PIIs."
msgstr "Do not include passwords in responses."

msgid ""
"It is necessary to set rules to restrict the user's password to avoid using "
"passwords that are easy to be guessed by others. For existing weak "
"passwords, they need to be modified in time. For passwords that are not "
"modified in time, it is necessary to monitor the interface access to "
"promptly discover the risk of account brute force login or account login "
"abnormality."
msgstr ""
"Ensure users' passwords are complex enough against cracking. For existing "
"weak ones, notify users to change them and keep tracking APIs in case "
"confidential stuffing occurs."

msgid ""
"It is recommended to modify the judgment echo information of the website "
"login page to be consistent: incorrect username or password."
msgstr ""
"Make messages uncertain when clients failed to log in, e.g., Incorrect "
"username or password."

msgid ""
"It is recommended to use the request body to pass authentication information"
" instead of URL parameters."
msgstr ""
"Use request bodies to transfer authorization data instead of URL parameters."

msgid ""
"It is recommended to use the request body to pass the PIIs instead of the "
"URL parameter to pass the PIIs."
msgstr "Use request bodies to transfer PII instead of URL parameters."

msgid ""
"It is recommended to use the request body to pass the password instead of "
"the URL parameter to pass the password."
msgstr "Use request bodies to transfer passwords instead of URL parameters."

msgid ""
"It is used to enable or disable CAPTCHA on the login page of the WebConsole."
msgstr ""

msgid ""
"It monitors the status of browser console at visitors' end to prevent "
"cracking attempts against website servers."
msgstr ""
"Enabling it will put interruptions in debugging process running in browsers "
"at the client end, preventing attempts of hacking."

msgid ""
"It refers to the automatic tool that exploits the security holes or "
"weaknesses of the system to launch behaviors that damage or threaten the "
"system security (such as injection attacks, CC attacks, etc.)"
msgstr ""

msgid ""
"It refers to the crawler program provided by each official platform to crawl"
" its own platform, such as wechat official crawler."
msgstr ""

msgid "It should NOT include"
msgstr ""

msgid "Italy Phone Number"
msgstr "Italy Mobile Phone"

msgid "Italy Taxpayer Identification Number"
msgstr ""

msgid "Italy VAT Number"
msgstr ""

msgid "Item Name"
msgstr ""

msgid "JDBC connection string"
msgstr ""

msgid "JS BOT requests count(jbc)"
msgstr ""

msgid "JS Expression"
msgstr ""

msgid "JSON (Type Detection)"
msgstr ""

msgid "JWT with Long-Term Validity"
msgstr ""

msgid "JWT with Weak Signature Algorithm"
msgstr ""

msgid "JWT without Signature Algorithm"
msgstr ""

msgid "Japan Passport Number"
msgstr "Japan Passport"

msgid "Japan Phone Number"
msgstr "Japan Mobile Phone"

msgid "Jenkins Business Component"
msgstr ""

msgid "Jenkins Business Component APIs Auto-detection"
msgstr "Auto-Recognize Jenkins-Business-Component APIs"

msgid "Job Title"
msgstr ""

msgid "Join an Existing Cluster"
msgstr ""

msgid "Kafka Business Component"
msgstr ""

msgid "Kafka Business Component APIs Auto-detection"
msgstr "Auto-Recognize Kafka-Business-Component APIs"

msgid ""
"Kafka Topic only supports letters, numbers and characters '-', '_', '.'"
msgstr ""

msgid "Keep HTTP Version of Request"
msgstr "Keep HTTP Version of Requests"

msgid "Keep Studing"
msgstr "Continue"

msgid "Keep learning setup failed."
msgstr ""

msgid "Keep learning setup successfully."
msgstr ""

msgid "Keep noticed or contact Technical Support"
msgstr ""

msgid "Kernel version does not support"
msgstr ""

msgid "Key Business Data Collection"
msgstr ""

msgid ""
"Key business of this website has been configured in \"Business Threat "
"Awareness\". You need to delete the key business in the BTA page before "
"delete the website."
msgstr ""

msgid "Key cannot be the same"
msgstr ""

msgid "Key word for successful login"
msgstr ""

msgid "Keyword Filter Against HTTP Requests"
msgstr "Keywords in Request URLs"

msgid "Keyword Filter Against HTTP Responses"
msgstr "Keywords in Responses"

msgid "Keyword can only contain (a-zA-Z0-9-_.) symbols"
msgstr ""

msgid "Keywords"
msgstr ""

msgid "Kibana Business Component"
msgstr ""

msgid "Kibana Business Component APIs Auto-detection"
msgstr "Auto-Recognize Kibana-Business-Component APIs"

msgid "Kubernetes Business Component"
msgstr ""

msgid "Kubernetes Business Component APIs Auto-detection"
msgstr "Auto-Recognize Kubernetes-Business-Component-APIs"

msgid "LLM API"
msgstr ""

msgid "LLM APIs Auto-detection"
msgstr ""

msgid "LLM Access Path"
msgstr "LLM Path"

msgid "LLM Corpus Upgrade"
msgstr "Corpus Management"

msgid "LLM Model Change to Local"
msgstr ""

msgid "LLM Model Change to Remote"
msgstr ""

msgid "LLM Model Close"
msgstr "LLM Model Settings have been closed"

msgid "LLM Model Open"
msgstr "LLM Model Settings have been opened"

msgid "LLM Model Remote Config Changed"
msgstr ""

msgid "LLM Name"
msgstr ""

msgid "LLM Package"
msgstr ""

msgid "Prompt Injection Protection"
msgstr ""

msgid "LLM Protection"
msgstr ""

msgid "LLM Sensitive Information Protection"
msgstr "PII"

msgid "LLM Service"
msgstr ""

msgid "LLM Service Settings"
msgstr ""

msgid "LLM Service detection"
msgstr ""

msgid "LLM Settings"
msgstr ""

msgid "LLM corpus version"
msgstr "LLM Corpus Version"

msgid "LLM engine version"
msgstr "LLM Engine Version"

#, python-brace-format
msgid ""
"LLM engine version {0} doesn't support the corpus {1}. Please select  the "
"collect llm version"
msgstr ""

#, python-brace-format
msgid ""
"LLM engine version {0} doesn't support the corpus {1}. The minimum LLM "
"corpus version is {2}. Please select correct llm_corpus"
msgstr ""

msgid "LLM path must start with https:// or http://"
msgstr ""

msgid "LLM_Protection"
msgstr ""

msgid "LLM_Settings"
msgstr "LLM Settings"

msgid "LOCATION_OTHER"
msgstr ""

msgid "Labs"
msgstr ""

msgid "Lack of Authentications"
msgstr "Unauthorized Access"

msgid ""
"Lack of limit on login rate. Attackers can make high-frequency login "
"attempts by using cracking methods such as credential stuffing."
msgstr ""

msgid "Landline Number in China"
msgstr "Telephone Number in China"

msgid "Landline number"
msgstr "China Telephone"

msgid "Large Screen Display"
msgstr ""

msgid "Last 24 hours"
msgstr "Last 24 Hours"

msgid "Last 30 days"
msgstr "Last 30 Days"

msgid "Last 7 days"
msgstr "Last 7 Days"

msgid "Last Call Time"
msgstr "Last Call at"

msgid "Last For (min)"
msgstr ""

msgid "Last Login at"
msgstr ""

msgid "Last Over-limit Date"
msgstr "Last Exceeding Time"

msgid "Last Update of GeoLib"
msgstr ""

msgid "Last Update of IPv4"
msgstr ""

msgid "Last Update of IPv6"
msgstr ""

msgid "Last month"
msgstr "Last Month"

msgid "Last week"
msgstr "Last Week"

msgid "Latest Config Saved in WebConsole"
msgstr "Latest Configuration Saved in WebConsole"

msgid "Latest Config Saved in Website Config Editor"
msgstr "Latest Configuration Saved in Website Configuration Editor"

msgid "Latitude"
msgstr ""

msgid "Latitude and Longitude"
msgstr ""

msgid "Leakage of Debug Information "
msgstr "Debugging Information Exposure"

msgid "Leakage of Multiple PII Types"
msgstr ""

msgid "Learned Parameter Patterns"
msgstr ""

msgid ""
"Learned parameter patterns are about to overwrite existing parameter rules "
"because the same parameter names and locations are found in the entries on "
"both sides. Do you want to continue?"
msgstr ""

msgid "Learning"
msgstr "Learning..."

msgid "Learning Cycle"
msgstr ""

msgid ""
"Learning Cycle greater than or equal to 1 and less than or equal to 60."
msgstr ""

msgid "Learning Domain"
msgstr ""

msgid "Learning List"
msgstr "Learning Paths"

msgid "Learning Outcomes"
msgstr ""

msgid "Learning Progress"
msgstr ""

msgid "Learning task setup failed."
msgstr ""

msgid "Learning task setup successfully."
msgstr "Learning task saved successfully."

msgid "Learning task status setup failed."
msgstr ""

msgid "Least Connected"
msgstr "Least Connections"

msgid "Left"
msgstr ""

msgid "Length"
msgstr ""

msgid "Length Limits of HTTP Request Header Fields"
msgstr "Max Length of Request Header"

msgid "Length Limits of HTTP Response Header Fields"
msgstr "Max Length of Response Header"

msgid "Length Range"
msgstr "Range of Length"

msgid "Length in bytes"
msgstr "Length"

msgid "Length of cluster name should between 1 and 50"
msgstr ""

msgid "Less than"
msgstr ""

msgid "Letters, numbers, underscores (_), and dashes (-)"
msgstr "letters, numbers, underscores (_), and dashes (-)"

msgid "License"
msgstr ""

msgid "License Expired"
msgstr ""

msgid "License File"
msgstr ""

msgid "License ID"
msgstr ""

msgid "License ID: "
msgstr ""

msgid "License Master"
msgstr ""

msgid "License Plate Number"
msgstr "China License Plate"

msgid "License Salt"
msgstr ""

msgid "License Slaves"
msgstr ""

msgid "License Status"
msgstr ""

msgid "License Type"
msgstr ""

msgid "License file fingerprint does not match the system."
msgstr ""

msgid "License file type does not match the system."
msgstr ""

msgid "License have no salt, this feature may not work as expected."
msgstr ""

msgid "License master is unavailable. Please check and fix it."
msgstr ""

msgid "License will be invalid soon"
msgstr ""

msgid "License will expire soon"
msgstr ""

msgid "LicenseError"
msgstr "License error"

msgid "LicenseWarning"
msgstr "License warning"

msgid "Licensed Protection Level"
msgstr ""

msgid "Like: IsRoot == True AND FingerPrint == test_fp_value"
msgstr ""

msgid "Line-by-Line Analysis"
msgstr ""

#, python-brace-format
msgid ""
"Line-by-Line Analysis is only applicable for {0} files, and each line must "
"be a complete request. A maximum of {1} lines can be analyzed."
msgstr ""

msgid "Linear accelerometer"
msgstr ""

msgid "List"
msgstr ""

msgid ""
"Lists of authentication credentials stolen from elsewhere are tested against"
" the application’s authentication mechanisms to identify whether users have "
"re-used the same login credentials. The stolen usernames (often email "
"addresses) and password pairs could have been sourced directly from another "
"application by the attacker, purchased in a criminal marketplace, or "
"obtained from publicly available breach data dumps. Unlike OAT-007 "
"Credential Cracking, Credential Stuffing does not involve any brute-forcing "
"or guessing of values; instead credentials used in other applications are "
"being tested for validity."
msgstr ""

msgid ""
"Lists of full credit and/or debit card data are tested against a merchant’s "
"payment processes to identify valid card details. The quality of stolen data"
" is often unknown, and Carding is used to identify good data of higher "
"value. Payment cardholder data may have been stolen from another "
"application, stolen from a different payment channel, or acquired from a "
"criminal marketplace."
msgstr ""

msgid "Load Balancing Strategy"
msgstr "Load Balancing"

msgid "Load method"
msgstr "Approach"

msgid "Loan Amount"
msgstr ""

msgid "Local"
msgstr ""

msgid ""
"Local Authentication: Logging in locally with an username and password."
msgstr ""

msgid "Local Html Protection List"
msgstr "Local HTML Pages"

msgid "Local LLM"
msgstr ""

msgid "Location"
msgstr ""

msgid "Location Information"
msgstr ""

msgid "Location abuse by accounts"
msgstr "Account Location Abuse"

msgid "Location of Detection"
msgstr "Found in"

msgid "Lock period"
msgstr "Locking"

msgid "Locked"
msgstr ""

msgid "Log"
msgstr ""

msgid "Log Analysis"
msgstr ""

msgid "Log Analysis S"
msgstr "Big Data Analysis S"

msgid "Log Archive Server exists already!"
msgstr ""

msgid "Log Archiving"
msgstr ""

msgid "Log Archiving Network Adapter"
msgstr ""

msgid "Log Network Adapter"
msgstr "Log"

msgid "Log Source"
msgstr "Include Logs"

msgid "Log in"
msgstr ""

msgid "LogDiskQuotaLimitExceeded"
msgstr "Disk quota exceeded"

msgid "LogSender disconnected."
msgstr ""

msgid "Logged out from webconsole because timeout"
msgstr "Logged out from WebConsole due to expired session"

msgid "Loggin all logs"
msgstr "Keep All"

msgid "Loggin high-risk logs"
msgstr "Keep High-Risk Logs"

msgid "Logging threshold"
msgstr "Logs"

msgid "Login"
msgstr ""

msgid "Login APIs Auto-detection"
msgstr "Auto-Recognize Login APIs"

msgid "Login CAPTCHA"
msgstr ""

msgid "Login CAPTCHA disabled successfully"
msgstr ""

msgid "Login CAPTCHA enabled successfully"
msgstr ""

msgid "Login Failure Locked"
msgstr "Account Locking"

msgid "Login Interface Logo"
msgstr ""

msgid "Login Success"
msgstr ""

msgid "Login failed counts"
msgstr "Failed Login Attempts"

msgid "Login failed period"
msgstr "Counting Timeframe"

msgid "Login failure lock configuration is set successfully."
msgstr "Account Locking saved successfully"

#, python-brace-format
msgid ""
"Login failure lock configuration is set successfully: login failed period is"
" set to {login_failed_period}, login failed count is set to "
"{login_failed_counts}, lock period is set to {lock_period}"
msgstr ""
"Modified Failed Login Locks settings: Counting Timeframe set to "
"{login_failed_period}, Failed Login Attempts set to {login_failed_counts}, "
"Locking set to {lock_period}"

msgid "Login failure lock configuration setting failed"
msgstr ""

msgid "Login failure lock configuration setting failed."
msgstr ""

msgid "Login is required."
msgstr ""

msgid "Login limit on user source address"
msgstr "Login Source Address Control"

msgid "Login/Account"
msgstr ""

msgid "Logout"
msgstr "Log out"

msgid "Logs"
msgstr ""

msgid "Longitude"
msgstr ""

msgid "Loose"
msgstr "Basic"

msgid "Loose Level"
msgstr "Basic"

#, python-brace-format
msgid "Lost connection with all upsteams {upstream_key}."
msgstr "Lost connection with all upstreams: {upstream_key} ."

#, python-brace-format
msgid "Lost connection with upsteam {upstream_key} {upstream_server}."
msgstr ""

msgid "Lost element in risk library!"
msgstr ""

msgid "Lots of request without ui count(ds_lots_of_req_without_ui)"
msgstr ""

msgid "Low"
msgstr ""

msgid "Low Risk"
msgstr ""

msgid "Lowercase Letters"
msgstr ""

msgid "Lua Interface"
msgstr ""

msgid "MAC Address"
msgstr ""

msgid "MAC Address Detection"
msgstr "MAC Address"

msgid "MASTER_HA"
msgstr ""

msgid "MIME type"
msgstr "MIME Type"

msgid "MIME type detection"
msgstr "MIME Types"

msgid "MPP SDK type mismatch"
msgstr ""

msgid "MPP plugin is disabled"
msgstr ""

msgid "MPP token timeout must be positive number in 1 ~ 2592000"
msgstr "Mini-program token timeout must be positive number from 1 to 2592000"

msgid "Macao ID Card"
msgstr ""

msgid "Macao Passport"
msgstr "Macao Passport"

msgid "Macao Resident ID Card"
msgstr ""

msgid "Macao SAR Passport"
msgstr "Macao SAR Passport"

msgid "Macau Phone Number"
msgstr "Macao Mobile Phone"

msgid "Magisk Type"
msgstr ""

msgid "Magisk detection"
msgstr ""

msgid "Magisk environment count(imag)"
msgstr ""

msgid "Magisk environment(ds_magisk)"
msgstr ""

msgid "Magnetometer"
msgstr ""

msgid "Mail Settings"
msgstr ""

msgid "Main IP(mip)"
msgstr ""

msgid "Mainland China Passport Number"
msgstr "Chinese Mainland Passport Number"

msgid "Mainland China Phone Number"
msgstr "Chinese Mainland Phone Number"

msgid "Major"
msgstr ""

msgid "Make sure to clear the alarm"
msgstr ""

msgid "Malicious Attacks"
msgstr ""

msgid "Malicious attacker"
msgstr "Malicious Attacks"

msgid "Malicious bots"
msgstr "Malicious Bots"

msgid ""
"Malicious content can include malware, IFRAME distribution, photographs & "
"videos, advertisements, referrer spam and tracking / surveillance code. The "
"content might be less overtly malicious but be an attempt to cause mischief,"
" undertake search engine optimisation (SEO) or to dilute / hide other posts."
" The mass abuse of broken form-to-email and form-to-SMS functions to send "
"messages to unintended recipients is not included in this threat event, or "
"any other in this ontology, since those are considered to be the "
"exploitation of implementation flaws alone . For multiple use that distorts "
"metrics, see OAT-016 Skewing instead."
msgstr ""

msgid "Management IP of Primary Node"
msgstr "eth0 IP of the Primary Node "

msgid "Management Interface Style"
msgstr ""

msgid "Management Network Adapter"
msgstr "Management"

msgid "Management interface SSL certificate."
msgstr ""

msgid "Management site"
msgstr ""

msgid "Mandatory"
msgstr "Required"

msgid "Manual Business Classification"
msgstr "Business Type (Manual)"

msgid "Manual Industry Classification"
msgstr "Manual Industry Classifications"

msgid "Manual Recovered"
msgstr ""

msgid "Manual Recovery Time"
msgstr ""

msgid "Manual Rule Editor"
msgstr ""

msgid "Manual Rules"
msgstr ""

msgid "Manual entry"
msgstr ""

msgid "Manual recover failed."
msgstr ""

msgid "Manual recover timeout."
msgstr ""

msgid "Manual recovered."
msgstr ""

msgid "Manual recovery"
msgstr ""

msgid "Manual upgrade"
msgstr "Manual Update"

msgid "Manually update the WAF rule set"
msgstr ""

msgid "Manually update the cti db"
msgstr ""

msgid "Manufacturer/Equipment name and other device basic information"
msgstr ""

msgid "Marital Status"
msgstr ""

msgid "Masking"
msgstr ""

msgid "Masking Symbol"
msgstr ""

msgid "Masking Symbol cannot be empty. A masking symbol could be a number or"
msgstr ""

msgid "Massive leakage of PIIs"
msgstr "Massive PII Leak"

msgid "Master Node"
msgstr ""

msgid "Master-Backup mode"
msgstr "Master-Backup Mode"

msgid "Master-Master mode"
msgstr "Master-Master Mode"

msgid "Matching Method"
msgstr ""

msgid "Max CDATA length"
msgstr "Max CDATA Length"

msgid "Max CPU Cores"
msgstr ""

msgid "Max Memory Size"
msgstr ""

msgid "Max Protected Nodes"
msgstr ""

msgid "Max Protected Websites"
msgstr ""

msgid "Max Proxy Nodes"
msgstr ""

msgid "Max QPS"
msgstr ""

msgid "Max Upload File Size"
msgstr "Max File Size"

msgid "Max attribute count"
msgstr "Max Attribute Count"

msgid "Max attribute name length"
msgstr "Max Attribute Name Length"

msgid "Max attribute value length"
msgstr "Max Attribute Value Length"

msgid "Max child node count"
msgstr "Max Child Node Count"

msgid "Max document size"
msgstr "Max Document Size"

msgid "Max element count"
msgstr "Max Element Count"

msgid "Max element name length"
msgstr "Max Element Name Length"

msgid "Max tree depth"
msgstr "Max Tree Depth"

msgid "Max. Rate"
msgstr "Max Frequency"

msgid "Maximum Body Size"
msgstr "Maximum Body Size(byte)"

msgid "Maximum Flow Limit of Single Node"
msgstr "Single Node Traffic Limit"

msgid "Maximum Length of Collected Msg"
msgstr "Captured Message Size Limit"

msgid "Maximum Number of Body Parameters"
msgstr "Maximum Number of Body Parameters(Form)"

msgid "Maximum Number of Query Parameters"
msgstr ""

msgid "Maximum PPS Limit of Single Node"
msgstr "Single Node PPS Limit"

msgid "Maximum TPS Limit of Single Node"
msgstr "Single Node TPS Limit"

msgid "Maximum length"
msgstr ""

msgid "Maximum length of Accept"
msgstr "Max Length of Accept"

msgid "Maximum length of Accept-charset"
msgstr "Max Length of Accept-Charset"

msgid "Maximum length of Cookie"
msgstr "Max Length of Cookie"

msgid "Maximum length of POST Body args"
msgstr "Max Number of POST Body Args"

msgid "Maximum length of Range span"
msgstr "Max Value of Range"

msgid "Maximum length of Referer"
msgstr "Max Length of Referer"

msgid "Maximum length of Request Args"
msgstr "Max Length of Request Args"

msgid "Maximum length of Request Header name"
msgstr "Max Length of Request Header Name"

msgid "Maximum length of Request Header value"
msgstr "Max Length of Request Header Value"

msgid "Maximum length of URL"
msgstr "Max Length of URL"

msgid "Maximum length of User-agent"
msgstr "Max Length of User-Agent"

msgid "Maximum number of Cookie"
msgstr "Max Number of Cookies"

msgid "Maximum number of Range"
msgstr "Max Number of Ranges"

msgid "Maximum number of Request Header"
msgstr "Max Number of Request Headers"

msgid "Maximum number of Request URL args"
msgstr "Max Number of Request URL Args"

msgid "Maximum value of Content-Length"
msgstr "Max Value of Content-Length"

msgid "Medical Case Information"
msgstr ""

msgid "Medication Dose"
msgstr ""

msgid "Medication Fee"
msgstr ""

msgid "Medication Name"
msgstr ""

msgid "Medium"
msgstr ""

msgid "Medium Risk"
msgstr ""

msgid "Memory"
msgstr ""

msgid "Memory Capacity"
msgstr ""

msgid "Memory Overload"
msgstr ""

msgid "Merge"
msgstr ""

msgid "Message Roles"
msgstr ""

msgid "Method"
msgstr ""

msgid "Method Without (),/:;<=>?@[\\]{}"
msgstr ""

msgid "Method cannot be empty."
msgstr "Method is required."

msgid "Mexico Citizen and Resident ID Code"
msgstr ""

msgid "Mexico ID Number"
msgstr "Mexico ID Card"

msgid "Micro APP"
msgstr "Mini-Program"

msgid "Middle Risk"
msgstr ""

msgid "Military Officer's Certificate"
msgstr "Military Officer Certificate"

msgid "Military Officer's Certificate of the People's Liberation Army"
msgstr "Military Officer Certificate of the People's Liberation Army"

msgid "Min document size"
msgstr "Min Document Size"

msgid "Min. Event Count"
msgstr "Min Events"

msgid "Mini Program Protection"
msgstr "Mini-Programs"

msgid "Mini-Program Requests"
msgstr ""

msgid "MiniProgram Token"
msgstr "Mini-Program Token"

msgid "Minimum"
msgstr ""

msgid "Minimum Length"
msgstr ""

msgid "Minor"
msgstr ""

msgid "Minute"
msgstr ""

msgid "Mirror Port"
msgstr ""

msgid "Mirroring Deployment"
msgstr "Mirroring"

msgid "Misconfigurations"
msgstr ""

msgid "Missile Map Location:"
msgstr "Attack Target Location"

msgid "Missing NIC information, unable to flash"
msgstr ""

msgid "Missing information for this network adapter."
msgstr ""

msgid "Missing necessary parameter."
msgstr ""

msgid "Mitigated"
msgstr ""

msgid "Mixed"
msgstr ""

msgid "Mobile"
msgstr ""

msgid "Mobile APP"
msgstr ""

msgid "Mobile App H5 Requests"
msgstr ""

msgid "Mobile App Native Requests"
msgstr ""

msgid "Mobile Certificate"
msgstr ""

msgid "Mobile Certificate Reset"
msgstr ""

msgid "Mobile Certificate Reset Failed"
msgstr ""

msgid "Mobile Certificate Reset Successed"
msgstr ""

msgid "Mobile Data Collection"
msgstr "Environment Data Collection"

msgid "Mobile Mini Program Protection"
msgstr "ChinaMobile Mini-Program"

msgid "Mobile Mini Program Protection body obfuscation setting"
msgstr "ChinaMobile Mini-Program Body Obfuscation"

msgid "Mobile Phone"
msgstr ""

msgid "Mobile Protection"
msgstr "Mobile Apps"

msgid "Mobile Response Token maximumLen Value"
msgstr ""

msgid "Mobile Token Name"
msgstr ""

msgid ""
"Mobile certificate note cannot contain [colon, newline, slash and "
"backslash]."
msgstr ""

msgid "Mobile mini program request whitelist"
msgstr "ChinaMobile Mini-Program Whitelist"

msgid "Mobile mini progrma SDK"
msgstr "ChinaMobile Mini-Program SDK"

msgid "Mobile requests count(mrc)"
msgstr ""

msgid "Mobile special local html list cannot be blank!"
msgstr "Mobile special local html list is required."

msgid "Mobile templates"
msgstr ""

msgid "MobilePad requests count(mpc)"
msgstr ""

msgid "Mode"
msgstr ""

msgid "Modifiable Response Data"
msgstr "Return Amount Alterable"

msgid "Modification Time"
msgstr "Modified at"

msgid "Modified reserved ports settings"
msgstr ""

msgid "Modify"
msgstr ""

msgid "Modify Account"
msgstr ""

msgid "Modify Bot Check Settings"
msgstr ""

msgid "Modify Cluster Name"
msgstr "Modified Cluster Name"

msgid "Modify Cluster Security Enhancement - Cluster IP whitelist."
msgstr "Modified Cluster Security Enhancement - Cluster IP Whitelist."

msgid "Modify Connections Emergency Mode Config"
msgstr "Modified Emergency Mode settings"

#, python-brace-format
msgid "Modify DNS server configuration to '{new}'."
msgstr "Modified DNS server configuration to '{new}'"

#, python-brace-format
msgid "Modify NTP Server from '{new}'."
msgstr "Modified NTP server to '{new}'"

msgid "Modify Policy on Good Bots"
msgstr "Modified policy of Allow Good Bots"

msgid "Modify QPS configuration"
msgstr ""

msgid "Modify Report Type To Phoenix"
msgstr ""

msgid "Modify Report Type To Sailfish"
msgstr ""

msgid "Modify Response Token Maximum Length"
msgstr ""

msgid "Modify SNMP configuration."
msgstr "Modified SNMP configuration"

msgid "Modify Scheduled Export - Frequency."
msgstr ""

msgid "Modify Scheduled Export - Transmit Mode."
msgstr ""

msgid "Modify Screen Search Head IP"
msgstr ""

msgid "Modify Time"
msgstr "Last Modified"

msgid "Modify Webconsole custom theme"
msgstr ""

msgid "Modify alarm advanced settings."
msgstr "Modified alarm advanced settings"

msgid "Modify alarm settings"
msgstr "Modified alarm settings"

#, python-brace-format
msgid "Modify alarm threshold of {key} to {value}"
msgstr ""

msgid "Modify api scanner list"
msgstr ""

msgid "Modify app data collection"
msgstr "Modified data collection"

msgid "Modify app data collection configurations"
msgstr ""

msgid "Modify application site"
msgstr "Edit"

msgid "Modify custom geo ip"
msgstr ""

msgid "Modify defect config failed."
msgstr ""

msgid "Modify defect level failed."
msgstr ""

msgid "Modify defect level to {}"
msgstr "Changed a defect risk level to {}"

msgid "Modify direct connection control"
msgstr "Modified Network Connection Control rules"

msgid "Modify hot standby configuration."
msgstr "Modified Hot Standby Settings"

#, python-brace-format
msgid "Modify login password expiration time to {period}."
msgstr "Modify login password expiration time to {period}."

#, python-brace-format
msgid "Modify management interface port number to '{new}'."
msgstr ""

msgid "Modify network route."
msgstr "Modified network route"

msgid "Modify password config."
msgstr "Modify password config."

msgid "Modify permission policy of site."
msgstr "Modified site permission policy"

msgid "Modify register app failed!"
msgstr ""

msgid "Modify register app success!"
msgstr ""

msgid "Modify risk config."
msgstr "Changed settings of a defect."

msgid "Modify sensitive information filtering settings."
msgstr "Modify PII Masking."

msgid "Modify system session expiration time."
msgstr "Modified system session expiration time"

msgid "Modify the archive log filter configuration."
msgstr "Modified Archived Log Filter"

msgid "Modify the formatted access log output configuration."
msgstr "Modified Transmit Formatted Access Log"

msgid "Modify the operate log output configuration."
msgstr "Modified Transmit Operation Log"

msgid "Modify the original access log output configuration."
msgstr "Modified Transmit Raw Access Log"

#, python-brace-format
msgid ""
"Modify the storage node extension configuration, add node: {add_nodes}, "
"delete node: {del_nodes}."
msgstr ""

#, python-brace-format
msgid ""
"Modify the storage node extension configuration, add node: {add_nodes}."
msgstr ""

#, python-brace-format
msgid ""
"Modify the storage node extension configuration, delete node: {del_nodes}."
msgstr ""

msgid "Modify the user's long-term inactivity lock"
msgstr ""

msgid "Modified login source address control"
msgstr "Modified login source address control"

#, python-brace-format
msgid "Modify {iface} network route."
msgstr "Modified {iface} network route"

msgid "Modifying remote account is not allowed."
msgstr ""

msgid "Module"
msgstr ""

#, python-brace-format
msgid "Module \"{0}\" in license has expired"
msgstr ""

msgid "Module Strategy"
msgstr "Rule Module Strategy"

msgid "Module name"
msgstr ""

#, python-brace-format
msgid "Module: [{0}] Settings failed"
msgstr ""

msgid "Modules Strategy"
msgstr "Strategy"

msgid "Mon."
msgstr ""

msgid "Monitor"
msgstr ""

msgid "Monitor Mode"
msgstr ""

msgid "Monitor Mode Only"
msgstr "Monitor Mode"

msgid "Monitor bot"
msgstr "Website Monitoring"

msgid "Monitor-Only New Rules"
msgstr ""

msgid "Monitoring Browser Console"
msgstr "Anti-Debugging"

msgid ""
"More than half of master nodes in this cluster are not working properly, "
"causing failure of cluster settings synchronization and the statistics "
"system. Please check and fix it."
msgstr ""
"More than half of master nodes in this cluster are not working properly, "
"resulting in synchronization failure of cluster settings and malfuctional "
"analytics system."

msgid "More than {} keywords are not allowed"
msgstr ""

msgid "Mouse Tracking"
msgstr ""

msgid "Move Down"
msgstr ""

msgid "Move Up"
msgstr ""

msgid "Move failed"
msgstr ""

msgid "Mpaas_MPP"
msgstr ""

msgid "Mpp Body"
msgstr "Mini-Program Body"

msgid "Mpp Header"
msgstr "Mini-Program Header"

msgid "Mpp Manual Rule"
msgstr "Mini-Program Manual Rule"

msgid "Mpp Prorection and Mobile Protection do not support it."
msgstr ""
"This feature is not applicable to Mini-Program Protection and Mobile App "
"Protection."

msgid "Multi-Client Protection Policy"
msgstr ""

msgid "Multi-Client Protection Policy has been modified successfully."
msgstr ""

msgid "Multi-Client Protection Policy has been saved successfully."
msgstr ""

msgid "Multiple DNS servers can be specified here with commas as separators."
msgstr ""

msgid ""
"Multiple URL are separated by newline and regular expressions are supported."
msgstr ""
"Allows regular expressions and only one path or regular expression in a "
"single line."

msgid ""
"Multiple e-mail addresses are allowed which should be separated by newline."
msgstr ""

msgid ""
"Multiple paths are separated by carriage returns, and regular expressions "
"are supported"
msgstr ""

msgid ""
"Multiple paths are separated by commas. Regular expressions are not "
"supported"
msgstr "Use commas to separate multiple paths. Regex is not supported."

msgid ""
"Multiple phone numbers are allowed which should be separated by newline."
msgstr ""

msgid "Multiple secure request count(ds_multiple_secure_req)"
msgstr ""

msgid "Multiple server fingerprint count(ds_multiple_fp)"
msgstr ""

msgid ""
"Multiple sites are separated by commas and regular expressions are not "
"supported"
msgstr "Use commas to separate multiple sites. Regex is not supported."

msgid ""
"Multiple suffixes are separated by commas. Regular expressions are not "
"supported"
msgstr ""

msgid "Multiple template IDS can be configured, separated by commas"
msgstr "Multiple template IDs can be configured separated by commas"

msgid ""
"Multiple types of PIIs are found in the return content of the interface. The"
" interface may not desensitize or delete important PIIs in the return data, "
"resulting in a greater impact after data leakage."
msgstr ""
"Different types of PII included in a single response have not been masked or"
" deleted."

msgid "Multiple user agent(ds_user_agent_count)"
msgstr ""

msgid ""
"Multiple values are separated by commas, and network segments are supported,"
" for example: ***********/24"
msgstr ""

msgid ""
"Multiple values are separated by commas, and port segment are supported, for"
" example: 80:85"
msgstr ""

msgid ""
"Multiple values are separated by commas, wildcards are supported, for "
"example: *.a.com"
msgstr ""

msgid ""
"Multiple values are separated by commas, wildcards are supported, for "
"example: *api/exp.html"
msgstr ""

msgid "Multiple values must be separated by commas."
msgstr ""

msgid "NIC"
msgstr ""

msgid "NIC flash"
msgstr ""

msgid "NIC1 and NIC2 cannot choose the same configuration"
msgstr ""

#, python-brace-format
msgid "NIC: {0} flash"
msgstr ""

msgid ""
"NOTE: 1MB shared memory can store about 8000 statistics. When it is "
"exceeded, the oldest data (LRU algorithm) will be deleted."
msgstr ""

msgid "NTP Server"
msgstr ""

msgid "NTP Service"
msgstr ""

msgid "Name"
msgstr "Name"

msgid "Name by cluster key"
msgstr ""

msgid "Name by license salt key"
msgstr ""

msgid "Name by license salt key & cross-cluster decryption"
msgstr ""

msgid "Nationality"
msgstr ""

msgid "Navigation Bar Logo"
msgstr ""

msgid "Navigation Bar Logo Background Image"
msgstr ""

msgid "Navigation Bar Logo Thumbnail"
msgstr ""

msgid "Need MPP appid for download plugin sdk"
msgstr ""

msgid "Need select a protected site at least"
msgstr "At least one website must be selected"

msgid "Need to finish wizard."
msgstr ""

msgid "Negate"
msgstr "Negate"

msgid "Neither key nor value can be empty"
msgstr ""

msgid "Netherlands VAT Number"
msgstr ""

msgid "Netmask"
msgstr ""

msgid "Network"
msgstr ""

msgid "Network Adapter Settings"
msgstr ""

msgid "Network Adapters"
msgstr ""

msgid "Network Configuration"
msgstr ""

msgid "Network Ddos Protect Config"
msgstr "Network DDoS policies changed"

msgid "Network Ddos Protect Switch"
msgstr "Network-Layer DDoS Switch"

#, python-brace-format
msgid "Network Ddos Protect: {0}"
msgstr "Network-Layer DDoS protections: {0}"

msgid "Network Error!"
msgstr ""

msgid "Network Interface"
msgstr "Network Adapter"

msgid "Network Proxy/VPN"
msgstr ""

msgid "Network Type"
msgstr ""

#, python-brace-format
msgid "Network config is incorrect:{0}: {1}."
msgstr ""

msgid "Network interface status updated"
msgstr ""

msgid "Network layer access control"
msgstr "Network-Layer Access Control"

msgid "Network_Ddos_Protect"
msgstr "Network-Layer DDoS"

msgid "Never"
msgstr "Never"

msgid "New"
msgstr "Add"

msgid "New Admin IP"
msgstr ""

msgid "New Comer"
msgstr "New User"

msgid "New Comer Scene"
msgstr "New User Scenario"

msgid "New Configuration"
msgstr "Add"

msgid "New SSL certificate has been uploaded"
msgstr ""

msgid "New node IP"
msgstr ""

msgid "New password "
msgstr "New Password"

#, python-brace-format
msgid ""
"New password should be different from the lastest {0} passwords that have "
"been used before"
msgstr ""

msgid "New rules"
msgstr ""

msgid "Next"
msgstr ""

msgid "Nginx"
msgstr ""

msgid "No"
msgstr ""

msgid "No Access"
msgstr "Block Mode"

msgid "No Bond"
msgstr "None"

msgid "No DNS Server, DNS search is not work."
msgstr "DNS Verification cannot work as DNS servers are not found."

msgid "No Data"
msgstr "Not Found"

msgid "No Mini Progrma"
msgstr "No mini-program"

msgid "No Repeat Http Header"
msgstr "No Duplicate Headers"

msgid "No Repeat Login"
msgstr ""

msgid "No Repeat Login disabled successfully"
msgstr ""

msgid "No Repeat Login enabled successfully"
msgstr ""

msgid "No Response"
msgstr ""

msgid "No Response from share memory"
msgstr "No Response from shared memory"

msgid "No Risk"
msgstr ""

msgid "No URL Integrity Verifying"
msgstr "Skip URL Integrity Check"

msgid "No Use of Previous"
msgstr ""

msgid "No appRule file or template found."
msgstr ""

msgid "No available list resource file"
msgstr ""

msgid "No configuration has been changed!"
msgstr ""

msgid "No crc32 integrity Verifying on URLs that with mobile token."
msgstr ""
"Do not have CRC32 check on URL integrity at requests with mobile tokens."

msgid "No email account is configured."
msgstr ""

msgid "No input requests count(btz)"
msgstr ""

msgid "No license found"
msgstr ""

msgid "No logging"
msgstr "Keep Nothing"

#, python-brace-format
msgid ""
"No more API Monitor roles are allowed as there are alrealy {0} exsiting in "
"current cluster."
msgstr ""

msgid "No more than 256 characters are allowed"
msgstr ""

msgid "No mppRule file or template found."
msgstr "No mini-program file or template found."

msgid "No prefix business access(ds_pre_bu_zero)"
msgstr ""

msgid "No previous version"
msgstr ""

msgid "No referer sniping"
msgstr "Sniping without referer"

msgid "No result"
msgstr ""

#, python-brace-format
msgid "No result matches '{0}'"
msgstr ""

msgid "No results found in WAF Rules."
msgstr "Not Found"

msgid "No results found."
msgstr "Not Found."

msgid "No rule file or template found."
msgstr ""

msgid "No scanner configured"
msgstr ""

msgid "No update history"
msgstr "No Available"

msgid "No valid template found."
msgstr ""

msgid "No websites have been added yet."
msgstr ""

msgid "Node"
msgstr ""

msgid "Node Fingerprint"
msgstr ""

msgid "Node Functions"
msgstr ""

msgid "Node ID | Management IP | File Size |"
msgstr "Node ID | IP | File Size |"

msgid "Node Number"
msgstr ""

msgid "Node Private"
msgstr "Private Route"

msgid "Node Role in Cluster"
msgstr ""

msgid "Node Status"
msgstr ""

msgid "Node has been deleted!"
msgstr ""

msgid "Node list"
msgstr ""

msgid "Node list is empty."
msgstr ""

#, python-brace-format
msgid ""
"Node service ({service}) abnormal in this cluster. Please check and fix it"
msgstr "Node service ({service}) is abnormal in this cluster."

msgid "Node status cannot be viewed on a big data storage node!"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: At least 1 IPv4 address is needed for current NIC as it has "
"role admin or log"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Edit NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Edit NIC {iface_name} failed"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Edit NIC {iface_name} successfully"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Edit NIC {iface_name} timeout"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: IPv4 gateway and address not in the same network for NIC "
"{iface_name}"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: IPv4 route and address not in the same network for NIC "
"{iface_name}"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: IPv6 gateway and address not in the same network for NIC "
"{iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IP of master node in cluster"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 address for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 gateway for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 netmask for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 route for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 route gateway for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 route netmask for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv4 route network for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv6 address for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv6 gateway for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid IPv6 netmask for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid NIC name {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Invalid VLAN ID for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: More than 1 IPv4 addresses for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: More than 1 IPv6 addresses for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: More than 30 IP addresses for NIC {iface_name}"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: NIC {iface_name} have duplicated IPv4 addresses"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: NIC {iface_name} have duplicated IPv6 addresses"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: NIC {iface_name} should have 1 IPv4 address"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: NIC {iface_name} should have 1 IPv4 address without VLAN"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: NIC {iface_name} should have 1 IPv4 or IPv6 address"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: NIC {iface_name} should have 1 IPv6 address without VLAN"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Only admin interface could add route"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Other NIC has IPv4 gateway already"
msgstr ""

#, python-brace-format
msgid "Node {node_id}: Other NIC has IPv6 gateway already"
msgstr ""

#, python-brace-format
msgid ""
"Node {node_id}: Please disable enhance cluster security before edit admin "
"NIC {iface_name}"
msgstr ""
"Node {node_id}: Please disable [Cluster Security Enhancement] before edit "
"admin NIC {iface_name}"

#, python-brace-format
msgid "Node {node_id}: {iface_name} is not allowed to edit"
msgstr ""

#, python-brace-format
msgid "Node {node_ip} role change."
msgstr ""

msgid "NodeDisconnected"
msgstr "Master node disconnected"

msgid "NodeServiceError"
msgstr "Node service error"

msgid "Non-Tapping"
msgstr ""

msgid "Non-response filter switch"
msgstr "Exclude Response-Missing Traffic"

msgid "None"
msgstr "Skip"

msgid "None Strategy"
msgstr "None"

msgid "None description"
msgstr "None"

msgid ""
"None: current request will not be blocked by Programmable Defending but "
"other modules are still working"
msgstr ""
"Skip: skip Programmable Defending but still be inspected by other modules "
"other modules are still working"

msgid "Normal"
msgstr ""

msgid "Normal Log"
msgstr "Normal Request Log"

msgid "Not Collection"
msgstr ""

msgid "Not Connected"
msgstr ""

msgid "Not Enabled"
msgstr "Disabled"

msgid "Not Found"
msgstr ""

msgid "Not In Effect"
msgstr ""

msgid "Not Learning List"
msgstr "Excluded Paths"

msgid "Not allowed to assign authority"
msgstr ""

msgid "Not allowed to update default attack policy"
msgstr ""

msgid "Not allowed to update default business type"
msgstr ""

msgid "Not empty"
msgstr ""

msgid "Not equal"
msgstr "Not Equal to"

msgid "Not equal to"
msgstr ""

msgid "Not in the List"
msgstr ""

msgid "Not include"
msgstr "Not Include"

msgid ""
"Not recommended for Dynamic Application Protection node to avoid resource "
"overload."
msgstr ""
"LLM Service is not recommended for Dynamic Application Protection node to "
"avoid resource overload."

msgid "Not recommended for Traffic Capturing node to avoid resource overload."
msgstr ""

msgid "Not recommended to enable the LLM Service detection for Traffic Capturing node to avoid resource overload."
msgstr ""

msgid "Not required"
msgstr "Not Required"

msgid "Not start learning"
msgstr "Stopped"

#, python-brace-format
msgid "Not yet enabled 【Flow learn server】, go to {0}."
msgstr ""

msgid "Note"
msgstr ""

msgid "Note:"
msgstr "Note: "

msgid ""
"Note: After enabling, some functions of the system may be affected (for "
"example, the configuration changes of the upstream server cannot be "
"monitored in time, or the request is intercepted by the system when the "
"request is large). Please use it with caution."
msgstr ""

msgid ""
"Note: Only selected module defense functions take effect after being "
"enabled. Exercise caution when using these functions."
msgstr ""
"Note: Only selected module functions will be effective after this feature is"
" enabled. Please use it with caution."

msgid ""
"Note: Resource Files cannot be exported via this operation, but can only be "
"downloaded separately."
msgstr ""

msgid ""
"Note: System versions, system time, and protection configurations among "
"different clusters must be identical to ensure correct cross-cluster web "
"protection."
msgstr ""

msgid "Note: System will reboot to configure port bonding."
msgstr "Note: System needs to reboot to apply port bonding."

msgid "Note: System will reboot to delete port bonding configurations."
msgstr "Note: System needs to reboot to release port bonding."

msgid "Note: The entered extension can only contain"
msgstr "Note: The extensions entered can only consist of characters from "

msgid ""
"Note: This certificate will work on both the WebConsole and HTTPS protected "
"sites that use the built-in certificate."
msgstr ""

msgid ""
"Note: To avoid interruption to applications, please ensure the required "
"Programmable Defending resource files have already been imported."
msgstr ""

#, python-format
msgid ""
"Note: You need to add %(startTag)s the big data analysis S (query) "
"%(endTag)s node IP of the current cluster to the 'Allow external system to "
"query logs' list of the target system."
msgstr ""
"Note: You need to log in to the external system and go to [Allow External "
"Log Query] settings to add the %(startTag)s Big Data Analysis S (Query) "
"%(endTag)s node IP of this cluster."

msgid "Note: expressions cannot contain"
msgstr "Note: expressions cannot contain "

msgid ""
"Note: the sysetm looks for exact match of file extensions. For instance, "
"there is no match between asp and aspp."
msgstr ""

msgid ""
"Note: the system looks for exact match of filenames. For instance, there is "
"no match between abc.php and aabc.php or abc.phpp."
msgstr ""

msgid ""
"Note: this feature may cause browser compatibility issues, so be sure to "
"conduct a complete compatibility verification before going online."
msgstr ""

msgid ""
"Note: this operation enables multiple sub-functions at the same time. In "
"some special cases, website might not be accessed:"
msgstr ""

msgid "Notes"
msgstr ""

msgid ""
"Notes: When it is enabled, it can cause errors if requests pass pass through"
" the protection system and are sent directly to the upstream servers."
msgstr ""

msgid ""
"Note：<span class='notice'>Note: For Ajax/Fetch requests without adding URL "
"tokens, the 'Ajax/Fetch Request Body Obfuscation' and 'Ajax/Fetch Response "
"Body Obfuscation' features of Web Advanced Protection do not take "
"effect.</span>"
msgstr ""
"Note: <span class='notice'>The features [Obfuscate Ajax/Fetch Request] and "
"[Obfuscate Ajax/Fetch Response] under Power Web Protection will not be "
"applied to the Ajax/Fetch requests without URL tokens.</span>"

msgid ""
"Note：<span class='notice'>Note: When the path that does not require adding a"
" URL token is within the scope of the 'Verification List' of Web Advanced "
"Protection, the request will not pass the system verification.</span>"
msgstr ""
"Note: <span class='notice'>If a request without URL token is in the scope of"
" the 'Verification List' of Power Web Protection, it will fail that "
"verification process.</span>"

msgid ""
"Note：<span class='notice'>This configuration item is not applicable to "
"Ajax/Fetch requests because it carries a one-time URL token. </span>"
msgstr ""

msgid ""
"Note：path should be the same with the websocket request, support regular "
"expression"
msgstr ""
"Note：The path should be the same with the WebSocket request URI and can "
"support regular expression."

msgid "Notice"
msgstr ""

msgid "Notice: A certificate is required when HTTPS is selected for \"Website\"."
msgstr ""

msgid ""
"Notice: For the syntax used to specify Apps, please refer to \"Specific "
"Apps\" in Help page."
msgstr ""
"Notice: For syntax used in the Filtering Condition, please refer to the "
"corresponding content in Online Help Document."

msgid ""
"Notice: The Health Check would be disabled if domain names are used instead "
"of servers' IPs."
msgstr ""
"Notice: The Health Check for servers would be disabled automatically if a "
"domain name, instead of an IP, is entered for any of the servers below."

msgid ""
"Notice: as standby node has no log services installed and is allowed to be "
"working as a backup for only 14 days, please restore the primary as soon as "
"possible."
msgstr ""

msgid ""
"Notice: the feature is effective only when server-side protection system is "
"working under Transparent Mode."
msgstr ""
"Notice: the feature is effective only when the system is working in Pass-"
"through Mode."

msgid ""
"Notice: this feature is only applicable to Apps that can store JavaScript "
"code."
msgstr ""
"Notice: this feature is only applicable to apps that can store JavaScript "
"code."

msgid "Now"
msgstr ""

msgid "Number of Attacks in 7 days"
msgstr "7-Day Attacks"

msgid "Number of Bot Calls in 7 days"
msgstr "7-Day Bots"

msgid ""
"Number of CPU cores or memory space on this node has exceeded limit "
"according to the license."
msgstr ""
". The CPU cores or memory space on this node exceeds capacity of current "
"license."

msgid "Number of Calls"
msgstr ""

msgid "Number of Calls Initiated"
msgstr "Number of Calls"

msgid "Number of Calls in 7 days"
msgstr "7-Day Calls"

msgid "Number of Discoveries"
msgstr "Matches"

msgid "Number of Failed Calls"
msgstr ""

msgid "Number of IPs in Blacklist"
msgstr "Blacklist IPs"

msgid "Number of IPs in Whitelist"
msgstr "Whitelist IPs"

msgid "Number of PII Types"
msgstr ""

msgid ""
"Number of PII types: the number of PII types involved in the request, for "
"example, if 2 names and 1 mobile phone number are detected in 1 request, the"
" count is 2 (two types of name and mobile phone number)"
msgstr ""
"Number of PII Types: shows how many PII types found in requests, e.g., names"
" of 2 people and one mobile phone number in a request are categorized into 2"
" PII types (name and phone number) "

msgid ""
"Number of PIIs detected, for example, if 2 names and 1 mobile phone number "
"are detected in 1 request, the count is 3"
msgstr ""
"Shows how many PII pieces have been found, e.g., name of 2 people and one "
"mobile phone number in a request are 3 pieces of PII."

msgid "Number of PIIs items"
msgstr "Number of PII"

msgid "Number of PIIs requests"
msgstr "Number of PII Requests"

msgid "Number of Retries"
msgstr ""

msgid "Number of Templates"
msgstr ""

msgid "Number of events is less than"
msgstr "Minimum Events"

msgid "Number of loops (support) parameters"
msgstr ""

msgid "Number of parameters:"
msgstr "Parameter Count:"

msgid "Number of paths"
msgstr ""

msgid "Number of requests with PIIs detected"
msgstr "Number of requests with PII detected"

msgid "Number of requests:"
msgstr "Requests:"

msgid "Number:"
msgstr ""

msgid "Numbers"
msgstr ""

msgid "OAT-001 Carding"
msgstr ""

msgid "OAT-002 Token Cracking"
msgstr ""

msgid "OAT-003 Ad Fraud"
msgstr ""

msgid "OAT-004 Fingerprinting"
msgstr ""

msgid "OAT-005 Scalping"
msgstr ""

msgid "OAT-007 Credential Cracking"
msgstr ""

msgid "OAT-008 Credential Stuffing"
msgstr ""

msgid "OAT-009 CAPTCHA Defeat"
msgstr ""

msgid "OAT-010 Card Cracking"
msgstr ""

msgid "OAT-011 Scraping"
msgstr ""

msgid "OAT-013 Sniping"
msgstr ""

msgid "OAT-014 Vulnerability Scanning"
msgstr ""

msgid "OAT-015 Denial of Service"
msgstr ""

msgid "OAT-016 Skewing"
msgstr ""

msgid "OAT-017 Spamming"
msgstr ""

msgid "OAT-018 Footprinting"
msgstr ""

msgid "OAT-019 Account Creation"
msgstr ""

msgid "OAT-021 Denial of Inventory"
msgstr ""

msgid "OEM Brand String For Alarm"
msgstr ""

msgid "OFF"
msgstr ""

msgid "OK"
msgstr ""

msgid "ON"
msgstr ""

msgid ""
"Obfuscates app request body and response body (local static pages only Ajax "
"request body to take effect) to prevent interception."
msgstr ""
"Obfuscates app request body and response body to prevent interception. For "
"local static pages, only Ajax request body will be obfuscated."

msgid "Obfuscates app request path and args to prevent interception."
msgstr ""

msgid "Obtaining Source IP"
msgstr "Source IP"

msgid "Occupation"
msgstr ""

msgid "Occurred from"
msgstr ""

msgid "Occurrence Time"
msgstr ""

msgid "Offline"
msgstr ""

msgid "Offline Request Body Obfuscation"
msgstr ""

msgid "Offline Upgrade"
msgstr "Offline Update"

msgid "Ok"
msgstr ""

msgid "Old Custom Strategy"
msgstr ""

msgid "Old Strategy"
msgstr ""

msgid "Old password "
msgstr ""

msgid "On extreme mode, only WAF module is working, the others fail"
msgstr ""

msgid "Once the switch is turned on, HTTP/2 will be disabled automatically."
msgstr ""

msgid "One Click Disconnection"
msgstr "Quick Block"

#, python-brace-format
msgid "One Click Switch to {action}"
msgstr ""

msgid "OneClickSwitch"
msgstr ""

msgid "Online"
msgstr ""

msgid "Online Awareness"
msgstr "Online Notification"

msgid "Online Payment Account"
msgstr ""

msgid "Online Upgrade"
msgstr "Online Update"

msgid "Online/Offline Status"
msgstr "Online/Offline"

msgid "Only 20 headers can cache"
msgstr ""

msgid "Only 5 trigger conditions can be added"
msgstr "Only 5 trigger conditions can be added"

msgid "Only Complete Html"
msgstr "Must-Have HTML Tags"

msgid "Only Complete Javascript"
msgstr "Executable JS Only"

msgid "Only Complete Ognl"
msgstr "Executable OGNL Only"

msgid ""
"Only TXT file can be uploaded and it must not contain more than 10,000 data "
"entries."
msgstr ""

msgid ""
"Only TXT file can be uploaded and it must not contain more than 100,000 data"
" entries."
msgstr ""

#, python-brace-format
msgid ""
"Only a part of learned parameter patterns were turned into generate rules "
"because {0} rules are allowed at most."
msgstr ""

msgid ""
"Only adds app protection token to requests accessing the following local "
"static pages. Note: when disabled, the system provides mobile protection to "
"all local static pages."
msgstr ""
"When enabled, it only provides protection tokens to requests accessing the "
"following local static pages. Note: when disabled, the system provides "
"tokens to all local static pages."

msgid "Only allow admin to modify the administrator account!"
msgstr ""

msgid "Only allow assign site for operator."
msgstr ""

msgid "Only allow assign site for statistic viewer."
msgstr ""

msgid "Only available for api request"
msgstr ""

#, python-brace-format
msgid "Only certificate file with [{0}] extension  are supported."
msgstr ""

msgid "Only create API"
msgstr "Added one API"

msgid "Only create ignore API"
msgstr "Added one API to the Ignore List"

msgid ""
"Only displays the block statistics since the last time the configuration was"
" saved (refreshed every 5s)."
msgstr ""

msgid ""
"Only enable \"Use China security standard certificate\", so website can only"
" be accessed by China security browsers. Confirm save?"
msgstr ""

msgid ""
"Only hosts in the whitelist (except for the node IP) can access the "
"WebConsole. Multiple hosts can be specified here with space as separators."
msgstr ""
"Only requests with hosts exist in the whitelist (except that requested host "
"is the IP of current node)are allowed to access WebConsole. Multiple hosts "
"can be entered and separated with spaces."

msgid "Only https site or terminal to https, and not Regex site."
msgstr ""
"Only applicable for websites of which the domain names are not regular "
"expressions and the protocol is either HTTPS or switched to HTTPS through "
"port offset"

msgid "Only icons in ICO format can be uploaded"
msgstr ""

msgid "Only images in PNG format can be uploaded"
msgstr ""

msgid "Only letters is allowed and exceed 10,with commas as separators."
msgstr ""
"A Method should only consist of no more than 10 letters.  Multiple Methods "
"need to be separated with commas."

msgid "Only listen on VIP"
msgstr "Listen only on VIP"

msgid "Only number is allowed and exceed 10,with commas as separators."
msgstr ""
"A rule ID should be a number not more than 10 digits. Multiple IDs need to "
"be separated with commas."

msgid ""
"Only one network card is active and the other is standby. If one of the "
"lines is disconnected, the other lines will be automatically backed up."
msgstr ""
"Only one network adapter is active and the other is on standby. If the "
"active line is disconnected, the other line will automatically be active."

msgid ""
"Only one standby node is allowed to be added to the cluster. At present, "
"there is one primary node and one standby node."
msgstr ""

msgid "Only printable ASCII characters are supported."
msgstr ""

#, python-brace-format
msgid "Only private key file with [ {0} ] extension are supported."
msgstr ""

msgid ""
"Only requests forwarded by specified devices (specified by IPs) can access "
"the protection sites. Those that skip the devices and directly access the "
"sites will be denied."
msgstr ""
"Specify network-layer IPs that are allowed to access the protected websites."

msgid "Only support LLMs that comply with OpenAI's specifications."
msgstr ""

msgid "Only supports letters, numbers, space and special characters -/_."
msgstr "Enter only letters, numbers, space or special characters -/_."

msgid "Only supports path and path parameters"
msgstr ""

msgid "Only supports up to 50."
msgstr ""

msgid "Only the following browsers are supported."
msgstr ""

#, python-brace-format
msgid "Only {0} or fewer decimal places are allowed."
msgstr ""

msgid "Open"
msgstr "Enabled"

msgid "Open API Args Valid just Monitor"
msgstr "Enabled Parameter Detection in Monitor Mode"

msgid "Open Ask to Enable Cookie"
msgstr "Enabled Ask to Enable Cookie"

msgid "Open Block note on Resonse"
msgstr "Enabled Error Message in Response"

msgid "Open GM Algorithm"
msgstr "Enabled GM Algorithm"

msgid "Open Slow Http Attack Protect"
msgstr "Enabled Slow Http Attack Protection"

msgid "Open Status"
msgstr "Status"

msgid "Open Strictly Match Host Header"
msgstr "Enabled Strictly Match Host Header"

msgid "Open Web Protection Token Advanced Scheme"
msgstr ""

msgid "Open Web Protection Token Advanced Scheme: Name by cluster key"
msgstr ""

msgid "Open Web Protection Token Advanced Scheme: Name by license salt key"
msgstr ""

msgid ""
"Open Web Protection Token Advanced Scheme: Name by license salt key & cross-"
"cluster decryption"
msgstr ""

msgid "Open automatically merge applications by port"
msgstr "Enabled Automatically Merge Applications by Port"

msgid "Open business data collection"
msgstr "Enabled business data collection"

msgid "Operate Log Output"
msgstr "Transmit Operation Log"

msgid "Operation"
msgstr "Operations"

msgid "Operation Log"
msgstr ""

msgid "Operation Record"
msgstr ""

msgid "Operation error"
msgstr ""

msgid "Operation error!"
msgstr ""

msgid "Operation failed"
msgstr ""

msgid "Operation log has been exported!"
msgstr "Exported operation log"

msgid "Operation timeout."
msgstr ""

msgid "Operator"
msgstr ""

msgid ""
"Operator: Limited access rights, including viewing and modifying some system"
" configurations."
msgstr ""

msgid "Optical Fiber"
msgstr ""

msgid "Option does not exist"
msgstr ""

msgid "Optional"
msgstr ""

msgid "Organization Code"
msgstr ""

msgid "Organization Type"
msgstr ""

msgid "Original Access Log Output"
msgstr "Transmit Raw Access Log"

msgid "Original Package"
msgstr ""

msgid "Other"
msgstr ""

msgid "Other Characters"
msgstr ""

msgid "Other error!"
msgstr ""

msgid "Others"
msgstr ""

msgid "Out of range"
msgstr ""

msgid "Output directly through the proxy node"
msgstr "Transmit from Protection Nodes"

msgid "Overseas group control"
msgstr "Overseas Group Control"

msgid "Overseas login"
msgstr "Overseas Login"

msgid "Overview"
msgstr ""

msgid "Overwrite"
msgstr ""

msgid "Overwrote"
msgstr "Overwrote with "

#, python-brace-format
msgid "PATH: {path} METHOD: {method} ERR_NO: {err_no}"
msgstr ""

msgid "PC templates"
msgstr ""

msgid "PII (Personally Identifiable Information) Masking"
msgstr ""

msgid "PII Detection"
msgstr ""

msgid "PII Detection Rate"
msgstr ""

msgid "PII Location"
msgstr "Found in"

msgid "PII Sample"
msgstr ""

msgid "PII Type"
msgstr ""

msgid "PII Types"
msgstr ""

msgid "PII in URL Parameter"
msgstr ""

msgid "PIIs Outbound Transmission"
msgstr "PII Outbound Transfer"

msgid "PIIs Outbound Transmission APIs Auto-detection"
msgstr "Auto-Recognize PII-Outbound-Transfer APIs"

msgid "PIIs Unique Count"
msgstr "PII Unique Count"

msgid "PIIs in URL"
msgstr "PII in URL"

msgid "POST Only"
msgstr ""

msgid "Package Size of GeoLib"
msgstr ""

msgid "Package Size of IPv4"
msgstr ""

msgid "Package Size of IPv6"
msgstr ""

msgid "Package Version"
msgstr ""

msgid "Packet capture memory"
msgstr "Capturing Memory Limit"

msgid "Packet capture memory allows configuration range 1-20"
msgstr ""
"Maximum memory rate of the protection system used by traffic capturing. "
"Range: 1%-20% (maximum capturing memory no more than 4G)."

msgid "Packet capture time. Valid range is 10 ~ 600 seconds"
msgstr ""

msgid "Page Path"
msgstr ""

msgid "Parameter Compliance"
msgstr ""

msgid "Parameter Compliance Strategy"
msgstr ""

msgid "Parameter Detection"
msgstr ""

msgid "Parameter Location"
msgstr ""

msgid "Parameter Name"
msgstr "Parameter Name"

msgid "Parameter Non-compliance"
msgstr "Noncompliant Parameter"

msgid "Parameter Rules"
msgstr ""

#, python-brace-format
msgid "Parameter Rules ({0} / {1})"
msgstr ""

msgid "Parameter Splitting Rules"
msgstr "Include-Parameter Condition"

msgid "Parameter Type"
msgstr "Parameter Type"

msgid "Parameter Value"
msgstr ""

msgid ""
"Parameters that directly pass the file path or malicious parameters that can"
" traverse the directory are found in the interface. Malicious attackers may "
"access any file in the server by modifying the parameters, resulting in the "
"leakage of important data files in the server."
msgstr ""
"Parameters in requests that have been accepted by APIs contains full path of"
" target files. It may be used by attackers to access sensitive files on "
"servers."

msgid "Parsed Access Log Output"
msgstr "Transmit Formatted Access Log"

msgid "Pass"
msgstr "Allow"

msgid "Pass Authentications"
msgstr "Password Authentications"

msgid "Pass action"
msgstr ""

msgid ""
"Pass through requests from certain IPs for testing or automation purposes."
msgstr ""
"The requests from IPs specified below and corresponding responses will be "
"forwarded directly without applying any protection measures."

msgid ""
"Pass: current request will not be blocked by RAS but the response will be "
"encapsulated"
msgstr ""
"Allow: do not intercept the request but still encapsulate the response"

msgid "Passport Number"
msgstr "China Passport"

msgid "Password"
msgstr ""

msgid "Password "
msgstr ""

msgid "Password Answer Hint"
msgstr ""

msgid "Password Changed at"
msgstr ""

msgid "Password Complexity"
msgstr ""

msgid "Password Leakages"
msgstr ""

msgid "Password Policy"
msgstr ""

msgid "Password Rules"
msgstr ""

msgid "Password Security saved successfully."
msgstr "Password Policy saved successfully."

msgid "Password Transmission"
msgstr "Password Transfer"

msgid "Password Transmission APIs Auto-detection"
msgstr "Auto-Recognize Password-Transfer APIs"

msgid "Password brute force attacks"
msgstr "Password Brute Force Attacks"

msgid "Password can't same as username."
msgstr ""

msgid "Password cannot be blank"
msgstr "Password is required."

msgid "Password cannot be empty"
msgstr "Password is required."

msgid "Password has been changed!"
msgstr ""

msgid "Password in Cookies"
msgstr ""

msgid "Password in Cookies "
msgstr "Password in Cookie"

msgid "Password in Responses"
msgstr "Password in Response"

msgid "Password in URL"
msgstr ""

msgid "Password in URL Parameter"
msgstr ""

msgid "Password is required to join the cluster."
msgstr ""

msgid "Password is required."
msgstr ""

msgid "Password parameter"
msgstr "Password Parameter"

msgid ""
"Password parameter cannot exceed 512 characters in Chinese or 1024 "
"characters in English."
msgstr ""

msgid "Password token has expired."
msgstr ""

msgid "Passwords do not match!"
msgstr ""

msgid "Passwords don't match"
msgstr ""

msgid "Passwords don't match."
msgstr ""

msgid "Patch has been uploaded"
msgstr "Uploaded system update package"

msgid "Path"
msgstr ""

msgid "Path Ends With"
msgstr ""

msgid "Path Extensions"
msgstr ""

msgid "Path Includes"
msgstr ""

msgid "Path Prefix"
msgstr ""

msgid "Path Starts With"
msgstr ""

msgid "Path and UA"
msgstr ""

msgid "Path cannot be blank."
msgstr "Path is required."

msgid "Path ends with a wildcard, e.g. /a/b/*"
msgstr ""

#, python-brace-format
msgid "Path must begin with {0}"
msgstr ""

msgid "Path regular expression is invalid"
msgstr ""

msgid ""
"Path sequences can be obtained in the following ways as input source data "
"for the intelligent detection"
msgstr ""
"Path sequences can be obtained in the following ways as input source data "
"for the intelligent detection."

msgid "Path templates cannot be empty"
msgstr ""

msgid "Path templates cannot have duplicate names"
msgstr ""

#, python-brace-format
msgid ""
"Path templates must only contain letters, numbers, underscores (_), and "
"dashes(-), and the length of the templates cannot exceed {0}"
msgstr ""

#, python-brace-format
msgid "Path templates only supports the following formats: /a/{id} or /a;{id}"
msgstr ""

#, python-brace-format
msgid "Path with templates, e.g. /a/{id} or /a;{id}"
msgstr ""

msgid "Path-based Scanner Identification"
msgstr ""

msgid ""
"Path-based method. The system will encrypt paths or parameters or both in "
"specific requests."
msgstr ""
"Path-based method. The system will encrypt paths or parameters or both in "
"specific requests."

msgid ""
"Paths must begin with / and each path must be entered in a separate line. "
"Paths can be in the following forms: "
msgstr ""

msgid "Pay by credit card"
msgstr "Credit Card Payment"

msgid "Payee Account"
msgstr ""

msgid "Payload Analysis"
msgstr ""

msgid "Payload Position"
msgstr ""

msgid "Payload Top 100"
msgstr ""

msgid "Payment"
msgstr ""

msgid "Payment Account Number"
msgstr ""

msgid "Payment Password/Login Password /Query Password, etc."
msgstr ""

msgid "Payment Tag"
msgstr ""

msgid "Penguin Service"
msgstr ""

msgid "Penguin master is down, read_only is only"
msgstr ""

msgid "Per Day"
msgstr "1 day"

msgid "Per Hour"
msgstr "1 hour"

msgid "Per Minute"
msgstr "1 minute"

msgid "Per Second"
msgstr "1 second"

msgid "Percentage"
msgstr ""

msgid "Performance balancing"
msgstr ""

msgid "Permission policy"
msgstr "Assign"

msgid "Permission_Config"
msgstr "Permission"

msgid "Perpetual"
msgstr "Never"

msgid "Personal Identification Number"
msgstr ""

msgid "Personal Identification Number (PIN)"
msgstr ""

msgid "PhoenixLicMasterError"
msgstr "Master statistics node error"

msgid "Phone Number"
msgstr "China Mobile Phone"

msgid "Phone Numbers"
msgstr ""

msgid "Phone battery power and charging status"
msgstr "Phone battery and charging status"

msgid "Phone screen orientation"
msgstr ""

msgid "Pii type {} does not exists"
msgstr ""

msgid "Pii type {} is already exists."
msgstr ""

msgid "Pii whitelist configuration has reached limit"
msgstr "Cannot add more. PII whitelist is full. "

msgid "Place an order"
msgstr "Purchase"

msgid "Place of Belonging"
msgstr "Location"

msgid "Place of Birth"
msgstr ""

msgid "Plaintext Password Leakages"
msgstr ""

msgid "Plaintext Password in Responses"
msgstr "Plaintext Password in Response"

msgid "Plaintext Passwords"
msgstr "Plaintext Password"

msgid "Platform"
msgstr ""

msgid "Platform Internal Crawlers"
msgstr "Official Crawlers"

msgid "Platform inside"
msgstr "Official Crawlers"

msgid "Platinum Service (7x24 hours)"
msgstr ""

msgid "Please Select items"
msgstr "Select a file"

msgid "Please Upload File"
msgstr "Upload File"

msgid "Please activate license!"
msgstr ""

msgid "Please add at least one path"
msgstr ""

msgid "Please add at least one trigger condition"
msgstr ""

msgid "Please apply more quota from administrator and recover it."
msgstr ""

msgid "Please apply more quota from administrator."
msgstr ""

msgid "Please avoid querying the remaining space during peak business hours"
msgstr "Avoid checking free memory during peak business hours"

#, python-brace-format
msgid ""
"Please check the service status of the upstream {upstream_server} as soon as"
" possible."
msgstr ""

msgid ""
"Please check upstream's web page or clean web cache as soon as possible."
msgstr ""

msgid "Please choose a hostname."
msgstr ""

msgid "Please choose at least one api to mask"
msgstr ""

#, python-brace-format
msgid "Please choose to Append to or Overwrite the existing {0}."
msgstr ""

msgid "Please click Go Back button and try again."
msgstr ""

msgid ""
"Please click Go Back button or type https://IP of network adapter eth0:20145"
" to enter the WebConsole and then select [System – Advanced Settings – "
"Factory Reset] to go through the Wizard again."
msgstr ""
"Please click the Back button or type https://[eth0 IP]:20145 to log in to "
"the WebConsole and go to System > General page for Factory Reset to go "
"through the Wizard again."

msgid "Please click the path on the left to check the learning results."
msgstr "Select a path from the outcomes listed on the left to view details"

msgid "Please configure a valid IP or IP/prefix."
msgstr ""

msgid "Please configure at least one business of this type"
msgstr ""

msgid "Please configure at least one protected site first"
msgstr ""

msgid ""
"Please contact customer service to get a super token and use it to reset "
"password. The super token is a one-time key and should be used within 30 "
"minutes."
msgstr ""

msgid ""
"Please contact support as soon as possible to obtain a new license and "
"activate."
msgstr ""

msgid "Please contact us for a new license."
msgstr ""

msgid ""
"Please contact your ForceShield partner or email to ForceShield by "
"<EMAIL> for a new license."
msgstr ""

msgid ""
"Please do not select roles both big data analysis P and big data analysis S!"
msgstr ""

msgid ""
"Please enable Dynamic Challenge and save it before viewing the template "
"introduction on Proxy node"
msgstr ""

msgid ""
"Please enable at least one from File size limit (byte), File extension "
"detection and MIME type detection."
msgstr "Enable at least one condition."

msgid "Please enable at least one password complexity option."
msgstr ""

msgid "Please ensure the file conforms with WAF"
msgstr "Please ensure the file conforms with virtual patch rules"

msgid "Please ensure the range of input field {} is valid"
msgstr ""

msgid ""
"Please ensure the sum of disk space specified here is always lower than or "
"equal to the available."
msgstr ""

msgid "Please ensure the type of input field {} is valid"
msgstr ""

msgid "Please enter Activation Key"
msgstr ""

msgid "Please enter a account name!"
msgstr "Please enter a account name."

msgid "Please enter a correct IP or network segment."
msgstr ""

msgid "Please enter a correct IP."
msgstr ""

msgid "Please enter a correct IPv4 address."
msgstr ""

msgid "Please enter a correct IPv4 or IPv6 or domain address."
msgstr "Please enter a correct IPv4 or IPv6 or domain address"

msgid "Please enter a correct VRID (1 - 255)"
msgstr ""

msgid "Please enter a correct absolute path."
msgstr ""

msgid "Please enter a correct cookie token usable times."
msgstr "Enter an integer between 1 and 10000."

msgid "Please enter a correct port number"
msgstr ""

msgid "Please enter a correct port number （Range: 1 ~ 65535）"
msgstr ""

msgid "Please enter a correct port number."
msgstr ""

msgid "Please enter a correct res_suffix"
msgstr "Invalid extension(s) found in Static Resources."

msgid "Please enter a correct rule id with a positive number."
msgstr ""

msgid "Please enter a correct server address."
msgstr ""

msgid "Please enter a correct site whitelist"
msgstr ""

msgid "Please enter a correct suffix"
msgstr "Incorrect extensions entered."

msgid "Please enter a correct urllist"
msgstr "Invalid path(s) found in Path Whitelist."

#, python-brace-format
msgid ""
"Please enter a integral number greater than or equal to {0} and less than or"
" equal to {1}"
msgstr "Enter an integer between {0} and {1}."

msgid "Please enter a learning object."
msgstr "Please enter a learning path."

msgid "Please enter a legal IPv4 address"
msgstr ""

msgid "Please enter a legal IPv6 address"
msgstr ""

msgid "Please enter a new password!"
msgstr ""

msgid "Please enter a password."
msgstr ""

msgid "Please enter a username!"
msgstr ""

msgid "Please enter a username."
msgstr ""

msgid "Please enter a valid IP address"
msgstr ""

msgid "Please enter a valid IPV4 address"
msgstr ""

msgid "Please enter a valid frequency string."
msgstr ""

msgid "Please enter a valid port number"
msgstr ""

msgid "Please enter an IPv4 or IPv4/Mask"
msgstr ""

msgid "Please enter an IPv4/IPv6 address or a qualified domain name"
msgstr ""

msgid "Please enter an available IP address"
msgstr ""

msgid "Please enter a valid source address"
msgstr ""

#, python-brace-format
msgid "Please enter an integer between {0} and {1}."
msgstr ""

msgid ""
"Please enter an integer greater than or equal to 0 and less than or equal to"
" 10 for the historical password check strategy."
msgstr ""

msgid ""
"Please enter an integer greater than or equal to 1 and less than or equal to"
" 365 for the validity period."
msgstr ""

msgid ""
"Please enter an integer greater than or equal to 8 and less than or equal to"
" 32 for the password length."
msgstr ""

msgid "Please enter an integer or decimal."
msgstr ""

msgid "Please enter an integer."
msgstr ""

#, python-brace-format
msgid "Please enter an integers from {0} to {1}"
msgstr ""

msgid "Please enter cache disk space, Integer from 1 to 100."
msgstr "Enter an integer between 1 to 100."

msgid "Please enter cache expiration time, Integer from 1 to 60."
msgstr "Enter an integer between 1 to 60."

msgid "Please enter integers"
msgstr ""

msgid "Please enter single file size, Integer from 1 to 100000."
msgstr "Enter an integer from 1 to 100000."

msgid "Please enter the authentication code"
msgstr ""

msgid "Please enter the correct IP address"
msgstr "Please enter a correct IP address"

msgid "Please enter the correct IPv4 or IPv6 address"
msgstr "Please enter a correct IPv4 or IPv6 address"

msgid ""
"Please enter the correct domain name or IP, note that the domain name does "
"not contain the port"
msgstr ""
"Please enter a correct domain name or IP. Note that the domain name does not"
" contain the port"

msgid "Please enter the correct gateway"
msgstr ""

msgid "Please enter the correct netmask"
msgstr ""

msgid "Please enter the correct netmask/prefix"
msgstr ""

msgid ""
"Please enter the correct path, note that you may need to url-encode the "
"special characters"
msgstr ""

msgid "Please enter the correct template ID."
msgstr ""

msgid "Please enter the correct vlan id (value range: 1 ~ 4094 or blank)"
msgstr ""

msgid ""
"Please enter the new IPv4 address in the browser address bar to access."
msgstr ""

msgid "Please enter the payload to be analyzed"
msgstr ""

msgid "Please enter the rule id or rule name or rule summary"
msgstr "Please enter a rule ID, a rule name or a short description."

msgid "Please enter the rule in correct format."
msgstr ""

msgid "Please enter the verification code."
msgstr ""

msgid "Please enter username."
msgstr ""

msgid "Please fill in the field name."
msgstr ""

msgid "Please fill in the length limit."
msgstr ""

msgid ""
"Please fill in the regular expression, multiple values are not supported."
msgstr "Enter only one regular expression."

msgid "Please firmly remember the password because it cannot be recovered!"
msgstr ""

msgid "Please follow the instructions below to configure the system."
msgstr ""

msgid ""
"Please go through a wizard first before implementing protections for "
"websites."
msgstr ""

msgid "Please go to the LLM Service node to upload the LLM package."
msgstr ""

msgid ""
"Please go to the [Websites] page to simplify the above Ajax-related "
"configurations for each Website."
msgstr ""

msgid "Please going to [LAB] > [Website Config Editor] to edit."
msgstr "Please going to [LAB] > [Website Configuration Editor] to edit."

msgid "Please input Websocket path，example：/rest/api/websocket or /path/entry"
msgstr "Please enter WebSocket path (e.g. /rest/api/websocket or /path/entry)"

msgid "Please input a correct method"
msgstr ""

msgid "Please make sure MaxMind GeoLib is included in the uploaded file"
msgstr ""

msgid "Please make sure eth0 eth1 eth2 are present"
msgstr ""

msgid ""
"Please make sure that at least one configuration for each rule is not empty."
msgstr ""

msgid ""
"Please make sure the defined request headers/body are correct to avoid "
"health check errors."
msgstr ""

msgid ""
"Please note that characters such as <span class='notice'>&apos;</span> and "
"<span class='notice'>space</span> are not allowed here."
msgstr ""
"Note: characters such as <span class='notice'>&apos;</span> and <span "
"class='notice'>space</span> are not allowed here."

msgid "Please reboot"
msgstr ""

msgid "Please reset it in System -> Log -> Settings."
msgstr ""

msgid "Please save the configuration before starting learning"
msgstr ""

msgid "Please select an Expiration time"
msgstr ""

msgid "Please select an LLM type."
msgstr ""

msgid "Please select at least one HTTP request method."
msgstr ""

msgid "Please select at least one from name, severity, and status."
msgstr ""

#, python-brace-format
msgid "Please select at least one item from {0}."
msgstr ""

msgid "Please select at least one province."
msgstr ""

msgid "Please select at least one role"
msgstr ""

msgid "Please select at least one role."
msgstr ""

msgid "Please select matched type of domain name or input valid domian"
msgstr ""

msgid "Please select mime types of cache path."
msgstr ""

msgid "Please select one of the HTTPS certificate type."
msgstr ""

msgid ""
"Please select the SDK of the corresponding algorithm to download, and then "
"integrate the downloaded SDK into the mini program."
msgstr ""
"Please download the SDK of the required algorithm, and then integrate the "
"downloaded SDK into the mini-program."

msgid "Please select the detection location and set the regular expression."
msgstr "Checking Positions are required."

msgid "Please select the file to be analyzed"
msgstr ""

msgid ""
"Please select the type of rule and specify the location and regular "
"expression."
msgstr ""
"Select a category for new rule and specify positions and regular expressions"
" for it."

#, python-brace-format
msgid "Please set the {0} with {1} and regular expression."
msgstr ""

msgid ""
"Please specify header fields of HTTP requests and their length limits "
"accordingly. The length is measured in bytes."
msgstr "The length is measured in bytes."

msgid ""
"Please specify header fields of HTTP response and their length limits "
"accordingly. The length is measured in bytes."
msgstr "The length is measured in bytes."

msgid "Please start at least one service"
msgstr ""

msgid ""
"Please try to restart the node. If the reboot has not been restored, please "
"contact technical support."
msgstr ""
"Please try to restart the node. If the node does not work properly after "
"reboot, please contact technical support."

msgid ""
"Please type https://IP of network adapter eth0:20145 enter the WebConsole "
"and then select [System - Advanced Settings - Factory Reset] to go through "
"the Wizard again."
msgstr ""

msgid "Please upgrade the inconsistent nodes separately."
msgstr ""

msgid "Please upload a valid China security certificate and private key."
msgstr ""

msgid "Please upload a valid certificate and private key."
msgstr ""

msgid "Please upload a valid international certificate and private key."
msgstr ""

msgid "Please upload a valid system file."
msgstr ""

msgid "Please upload the LLM package."
msgstr ""

msgid "Please upload the version 1 update package"
msgstr ""

msgid "Please upload valid RSA private key file."
msgstr ""

msgid "Please upload valid certificate file."
msgstr ""

msgid "Please upload valid private key file."
msgstr ""

msgid ""
"Please use \",\" to separate character strings entered in the following "
"textbox."
msgstr "Use commas to separate multiple keywords."

msgid ""
"Please use \",\" to separate filename extensions entered in the following "
"textbox."
msgstr "Use commas to separate multiple extensions."

msgid ""
"Please use \",\" to separate status codes entered in the following textbox."
msgstr "Use commas to separate multiple status codes."

msgid "Please use update patch that includes base packages"
msgstr ""

msgid ""
"Please wait for a while, if it does not recover automatically, please "
"contact technical support."
msgstr ""

msgid "Please wait for the rollback to complete."
msgstr ""

msgid "Please wait for the upgrade to complete."
msgstr ""

msgid "Plug-in Deployment"
msgstr "Plug-in"

msgid "Policy"
msgstr ""

msgid "Policy ID conflicts in the import file, such as ID"
msgstr ""

msgid "Policy on Good Bots"
msgstr "Allow Good Bots"

msgid "Policy on Requests from Mobile SDK V1"
msgstr ""

msgid "Policy on good bots has been set."
msgstr "[Allow Good Bots] has been set."

msgid "Policy on keep source IP"
msgstr "Keep Source IP"

msgid "Policy on keep source IP has been set."
msgstr "[Keep Source IP] has been set."

msgid "Port"
msgstr ""

msgid "Port "
msgstr ""

msgid "Port Binding"
msgstr "Port Bonding"

msgid "Port Offset"
msgstr ""

msgid "Port for Proxy"
msgstr ""

msgid "Port number of primary authentication server is blank."
msgstr ""

msgid "Port number of secondary authentication server is blank."
msgstr ""

msgid "Port of primary authentication server is not available."
msgstr ""

#, python-brace-format
msgid "Port range error {0}, start port cannot be greater than end port."
msgstr ""

msgid "Port status forward"
msgstr "Port Status Notification"

msgid "Port value must be an integer from 1024 to 65000."
msgstr ""

msgid "Port value: 1024-65000 integer"
msgstr ""

#, python-brace-format
msgid ""
"Port {0} is used already. Please change the port number or try again later."
msgstr ""

msgid "Power"
msgstr ""

msgid "Power Protection"
msgstr "Advanced Protection"

msgid "Power Web Protection"
msgstr ""

msgid ""
"Precise URL supports parameters, regular expressions do not support "
"parameters."
msgstr "Parameters are supported only in explicit URLs."

msgid "Prefix"
msgstr "Prefix match"

msgid "Prefix Length"
msgstr ""

msgid "Prepaid Card Number"
msgstr ""

msgid "Prevent core js reentry."
msgstr ""

msgid "Preview"
msgstr ""

msgid "Preview bot"
msgstr "Webpage Preview"

msgid "Preview the template"
msgstr ""

msgid "Previous"
msgstr ""

msgid "Previous version info:"
msgstr ""

msgid "Primary Node IP"
msgstr ""

msgid "Primary Server"
msgstr ""

msgid "Primary Web Protection"
msgstr ""

msgid "Private key file"
msgstr ""

msgid "Private key file has been uploaded."
msgstr ""

msgid "Product Name"
msgstr ""

msgid "Product Price"
msgstr ""

msgid "Production"
msgstr ""

msgid "Programmable Defending"
msgstr ""

msgid "Programmable Defending Settings"
msgstr ""

msgid "Programmable Defending,Web Protection"
msgstr ""

msgid "Promotion/Red packet scramble"
msgstr "Promotion"

msgid "Prompt"
msgstr ""

msgid "Prompt cannot be empty."
msgstr ""

msgid "Prompt list exceeds maximum length of {}"
msgstr ""

msgid "Protect All IPs (Default)"
msgstr "None (Default)"

msgid "Protect Following IPs Only"
msgstr "Exclude List (Protect Below IPs Only)"

msgid "Protect Plugin"
msgstr "Protect Plug-in"

msgid "Protect all servers"
msgstr ""

msgid "Protected Host:"
msgstr ""

msgid "Protection"
msgstr "Websites"

msgid "Protection Level"
msgstr "Level"

msgid "Protection List"
msgstr ""

msgid "Protection List Version"
msgstr "Version"

msgid "Protection Method"
msgstr ""

msgid "Protection Mode"
msgstr ""

msgid "Protection Policy"
msgstr ""

msgid "Protection System To Protected Site SSL Algorithm"
msgstr "Upstream SSL Algorithm"

msgid "Protection against Slow Attacks"
msgstr ""

msgid "Protection agaist Prompt Injection"
msgstr "Prompt Injection Protection"

msgid "Protection is not enabled because no license is found."
msgstr ""

msgid "Protection path"
msgstr "Requests to be Checked"

msgid "Protocol"
msgstr ""

msgid "Protocol Version"
msgstr ""

msgid "Provide common basic functions and interfaces for AI applications"
msgstr "Providing common basic functions and interfaces for AI applications."

msgid ""
"Provide threat intelligence related services, including intelligence "
"management, intelligence generation, intelligence synchronization, etc. Only"
" one threat intelligence service node can be configured in a cluster."
msgstr ""
"Generating a reputation database based on threat intelligence. Only one "
"reputation service node is allowed in a cluster."

msgid "Provide users with RAS service interface"
msgstr "Providing system APIs."

msgid ""
"Providing basic functions and interfaces for LLM applications. Only one LLM "
"service node is allowed in a cluster."
msgstr ""

msgid "Providing protections for target websites"
msgstr "Providing protections for target websites."

msgid "Province"
msgstr ""

msgid "Proxy"
msgstr ""

msgid "Proxy Detection"
msgstr ""

msgid "Proxy SSL Ciphers cannot be blank"
msgstr "Proxy SSL Ciphers are required."

msgid "Proxy SSL Protocols cannot be blank"
msgstr "Proxy SSL Protocols are required."

msgid "Proxy server"
msgstr ""

msgid "Proxy server: Please enter a correct IPv4 address."
msgstr ""

msgid "Proxy server: Please enter a correct port number."
msgstr ""

msgid "Proxy_SSL_Ciphers"
msgstr ""

msgid "Proxy_SSL_Protocols"
msgstr ""

msgid ""
"Put a limit on login attempts from a single user or IP within a certain time"
" frame."
msgstr ""

msgid ""
"Put the interception note into response body, indicating the request is "
"blocked by the system."
msgstr ""
"Insert an error message into the response body to explain why the request is"
" blocked by the system."

msgid "QPS Management disabled successfully"
msgstr ""

msgid "QPS Management enabled successfully"
msgstr ""

msgid "QPS has been updated: "
msgstr ""

#, python-brace-format
msgid ""
"QPS of sites managed by {owner} is more than the quota {quota} at {date}."
msgstr ""

msgid "QPS quota"
msgstr ""

msgid "QPSError"
msgstr "QPSExceedLimit"

msgid "QPSWarning"
msgstr ""

msgid "QPS_Management"
msgstr "QPS Management"

msgid "Query"
msgstr ""

msgid "Query APIs Auto-detection"
msgstr "Auto-Recognize Query APIs"

msgid "Query external system logs"
msgstr "Query External Logs"

msgid "Query report data is abnormal, lack of big data analysis node."
msgstr ""

msgid ""
"Query report data is abnormal. Please ensure that the big data analysis "
"node(P) works properly."
msgstr ""

msgid ""
"Query report data is abnormal. Please ensure that the big data analysis "
"node(S) works properly."
msgstr ""

msgid "Query statement"
msgstr ""

msgid "Query time"
msgstr ""

msgid "Quick Switch"
msgstr ""

msgid "Quit"
msgstr ""

msgid "RAT-033 SMS Bombing"
msgstr ""

msgid "RAT-034 API Abuse"
msgstr ""

msgid "RAT-048 Abnormal Terminal"
msgstr ""

msgid "RAT-049 APP Group Control"
msgstr ""

msgid "RAT-050 Unknown Threat"
msgstr ""

msgid "RCS Evaluation"
msgstr ""

msgid "REMOTE_ADDR"
msgstr ""

msgid "REQUEST_BODY"
msgstr ""

msgid "REQUEST_HEADERS"
msgstr ""

msgid "REQUEST_HEADERS_NAMES"
msgstr ""

msgid "REQUEST_LINE"
msgstr ""

msgid "REQUEST_METHOD"
msgstr ""

msgid "REQUEST_PROTOCOL"
msgstr ""

msgid "REQUEST_REFERER"
msgstr ""

msgid "REQUEST_URL"
msgstr ""

msgid "REQUEST_USER_AGENT"
msgstr ""

msgid "RESPONSE_BODY"
msgstr ""

msgid "RESPONSE_HEADERS"
msgstr ""

msgid "RESPONSE_HEADERS_NAMES"
msgstr ""

msgid "RESPONSE_STATUS"
msgstr ""

msgid "RES_LEECH policy"
msgstr ""

msgid "RSS Crawlers"
msgstr ""

msgid "RSS protocol based crawler."
msgstr ""

msgid "RSS reader"
msgstr "RSS Crawlers"

msgid "Range"
msgstr ""

msgid "Range 10 ~ 600 seconds. 60 by default."
msgstr "10-600. Default: 60"

msgid "Ranking of Blocked URLs"
msgstr "Blocked Requests"

msgid "Ranking of Requested URLs"
msgstr ""

msgid "Reached maximum number of users"
msgstr ""

msgid "Read"
msgstr ""

msgid "Read/Write"
msgstr ""

msgid "Real Estate Unit Number"
msgstr ""

msgid "Realtime Detection"
msgstr ""

msgid "Reboot"
msgstr ""

msgid "Reboot node"
msgstr ""

#, python-brace-format
msgid "Reboot node protection engine: {node_ip}"
msgstr "Reboot protect engine at node: {node_ip}"

msgid "Reboot node: "
msgstr ""

msgid "Reboot protection engine"
msgstr "Reboot Protection Engine"

msgid "Reboot protection engine failed"
msgstr "Protection engine rebooted unsuccessfully"

msgid "Reboot protection engine success"
msgstr "Protection engine rebooted successfully"

msgid "Reboot the node."
msgstr ""

msgid "Reboot the system?"
msgstr ""

msgid "Reboot this node protection engine?"
msgstr "Are you sure you want restart protection engine of current node?"

msgid "Reborn"
msgstr "Revived"

msgid "Receive Bytes"
msgstr ""

msgid "Receive Error"
msgstr ""

msgid "Receive Packet Loss"
msgstr ""

msgid "Receive Packets"
msgstr ""

msgid "Receive Parsed Access Log"
msgstr ""

msgid "Receiver Numbers"
msgstr "Phone Number"

msgid "Recent download time"
msgstr ""

msgid "Recently Discovered"
msgstr "Latest Match at"

msgid "Recommended list"
msgstr ""

msgid ""
"Recommended list: The current learning results have exceeded the maximum "
"limit, please contact technical support."
msgstr ""

msgid "Recommended list: generated automatically by self-learning module"
msgstr ""

msgid "Recover"
msgstr ""

msgid "Recovered"
msgstr ""

msgid "Recovery Time"
msgstr "Release Time"

msgid "Recovery to the original package failed."
msgstr ""

msgid "Redirect"
msgstr "Redirect"

msgid "Redirect Path"
msgstr "To Path"

msgid "Redirect action"
msgstr "Redirect to"

msgid "Redirect: set status code 302 and redirect to the other path"
msgstr "Redirect: return status code 302 and redirect to another path"

msgid ""
"Reduce CPU cores or memory space on this node and reactivate license. For "
"more information, please go to \"System\" > \"License\" page on this node."
msgstr ""
"Reduce CPU cores or memory space on this node. For more information, please "
"go to \"System\" > \"License\" page on this node."

msgid "Reduce level of protection by User-Agent."
msgstr ""

msgid "Refer path of the sniping activity"
msgstr "Referer path of the sniping page"

msgid "Referer"
msgstr ""

msgid "Referer Path"
msgstr ""

msgid "Referer Path Statistics of Sniping Scene"
msgstr "Referer Path Statistics of Sniping Scenario"

msgid "Referer: "
msgstr ""

msgid ""
"Refers to the search engine in fetching website data, the page cached in its"
" own server behavior, so that the page is not available, through the "
"snapshot can normally view the original content of the website."
msgstr ""

msgid ""
"Refers to through the analysis of search engine ranking rules, so as to "
"understand the search engine search methods, methods to grab web content, as"
" well as keyword search results ranking technology."
msgstr ""

msgid "Refresh"
msgstr ""

msgid "Refund Amount"
msgstr ""

msgid "Reg Rule ID"
msgstr "Rule ID"

msgid "Reg Rule ID:"
msgstr "Rule ID:"

msgid "Reg URL"
msgstr "URL"

msgid "Reg URL:"
msgstr "URL:"

msgid "Regex"
msgstr ""

msgid "Regex Model"
msgstr "Regular Expression"

msgid "Regex is incorrect"
msgstr ""

msgid "Regex match"
msgstr "Regex"

#, python-format
msgid "Regex(%s) is incorrect"
msgstr ""

msgid "Region"
msgstr ""

msgid "Regional Access Control"
msgstr "Regional Control"

msgid "Regional lock"
msgstr "Regional Locks"

msgid "Register"
msgstr "Registration"

msgid "Register App"
msgstr ""

msgid "Register app"
msgstr ""

msgid ""
"Register app failed! The maximum number of registered apps allowed for the "
"cluster is ({})."
msgstr ""

msgid "Registered"
msgstr ""

msgid "Registration"
msgstr ""

msgid "Registration APIs Auto-detection"
msgstr "Auto-Recognize Registration APIs"

msgid "Registration is done"
msgstr ""

msgid "Regular"
msgstr ""

msgid "Regular expression capturing groups do not match references."
msgstr ""

msgid "Regular expression of WAF White List is invalid."
msgstr "Regular expression of WAF Whitelist is invalid."

msgid "Regular expression of search content is not valid."
msgstr ""

msgid "Regular expression of search path is not valid."
msgstr ""

msgid "Regular expressions do not support single quotes and spaces"
msgstr ""

msgid ""
"Regular expressions prohibit the entry of double quotes, spaces, tabs, page "
"feeds, and line breaks, as well as invisible characters other than "
"0x20-0x7e!"
msgstr ""

msgid "Reject"
msgstr ""

msgid "Related information for environment awareness"
msgstr "Data related device environment"

msgid ""
"Relaxation Time Greater than or equal to 10 and less tan or equal to 1000000"
msgstr ""

msgid "Relaxation Time(s)"
msgstr "Stay Exit (sec)"

msgid "Relearning setup failed."
msgstr ""

msgid "Relearning setup successfully."
msgstr ""

msgid "Release patch cannot be installed on debug patch"
msgstr ""

#, python-brace-format
msgid "Release the locked state of {username}"
msgstr ""

msgid "Religious Beliefs"
msgstr ""

msgid "Reload"
msgstr ""

msgid ""
"Remark a name for the applet, support English letters, numbers, Chinese, "
"spaces and underscores."
msgstr ""
"Name the mini-program. Allow letters, numbers, Chinese characters, spaces "
"and underscores."

msgid "Remote"
msgstr ""

msgid "Remote Authentication Server"
msgstr "Remote Auth Server"

msgid ""
"Remote Authentication: Logging in remotely through authentication server."
msgstr ""

msgid "Remote LLM"
msgstr ""

msgid "Remote Mac Address"
msgstr ""

msgid "Remote authentication is not enabled."
msgstr ""

msgid "Remote authentication server is not enabled"
msgstr ""

msgid ""
"Remote login to the WebConsole or ssh is only allowed from the following "
"addresses."
msgstr ""

msgid "Remove"
msgstr ""

msgid "Remove "
msgstr ""

msgid ""
"Remove a specified type of reputation library data. An asterisk (*) means "
"removing all."
msgstr ""
"Remove reputation of an IP, fingerprint or account as specified below. * "
"means all the reputations for the type specified."

msgid "Remove failed"
msgstr ""

msgid "Remove rule"
msgstr ""

msgid "Remove success"
msgstr "Removed successfully."

msgid "Removed Successfully!"
msgstr "Removed successfully."

msgid "Rendering with the Latest IE Version"
msgstr "Render with the Latest IE"

msgid "Repayment Amount"
msgstr ""

msgid "Repeated Challenge"
msgstr "Repeat Challenge"

msgid "Replace"
msgstr ""

msgid "Replace With"
msgstr ""

msgid ""
"Replace strings in servers' responses that match the following conditions "
"with the ones specified below."
msgstr "Find and replace specified strings in responses with a new one."

msgid "Replacement Method"
msgstr "Approach"

msgid "Replacement in Response"
msgstr "Replace Response"

msgid "Replacement rules have been modified"
msgstr "Modified Replace Response rules"

msgid "Report"
msgstr ""

msgid "Report Type"
msgstr ""

msgid "Report URL"
msgstr ""

msgid "Reputation"
msgstr "Reputations"

msgid "Reputation Remove"
msgstr "Remove Reputations"

msgid "Reputation Secne"
msgstr "Reputation Scenario"

msgid "Reputation Server Server exists already!"
msgstr ""

msgid "Reputation Server exists already!"
msgstr ""

msgid "Reputation decay rate"
msgstr "Reputation Fading Speed"

msgid "Reputation lib file import"
msgstr "Import Third-Party Reputations"

msgid "Reputation lib output"
msgstr "Auto Transmit BTA Reputations"

msgid "Reputation library attenuation speed"
msgstr "Fading Speed of Reputations"

msgid "Reputation score(rep)"
msgstr ""

msgid "Reputation server IP"
msgstr ""

msgid "Reputation update time："
msgstr "Reputations Update Time: "

msgid "Request"
msgstr ""

msgid "Request Body"
msgstr "Request Body"

msgid "Request Body Detection Range (KB)"
msgstr "Request Body (KB)"

msgid "Request Body Length"
msgstr ""

msgid "Request Cookie Name"
msgstr "Request Cookie Name"

msgid "Request Cookie Value"
msgstr "Request Cookie Name & Value"

msgid "Request Count"
msgstr ""

msgid "Request Counts"
msgstr "Requests"

msgid "Request File Multipart"
msgstr "File Content Uploaded"

msgid "Request File Name"
msgstr "File Name Uploaded"

msgid "Request Header"
msgstr ""

msgid "Request Header Length"
msgstr ""

msgid "Request Header Name"
msgstr "Request Header Name"

msgid "Request Header Referer"
msgstr "Request Referer"

msgid "Request Header UA"
msgstr "Request UA"

msgid "Request Header Value"
msgstr "Request Header Name & Value"

msgid "Request Length"
msgstr ""

msgid "Request Method"
msgstr "Method"

msgid "Request PII Type"
msgstr ""

msgid "Request Parameter"
msgstr ""

msgid "Request Parameter Name"
msgstr "Request Parameter Name"

msgid "Request Parameter Value"
msgstr "Request Parameter Name & Value"

msgid "Request Parameters"
msgstr ""

msgid "Request Path"
msgstr ""

msgid "Request Raw"
msgstr ""

msgid "Request Stage"
msgstr "Request Stage"

msgid "Request URL"
msgstr "Request URL"

msgid "Request Verification List"
msgstr "Verification List"

msgid ""
"Request Verification List is made up of regular expressions. HTTP requests "
"with destination URLs that match any of the following expressions will be "
"verified by the system. "
msgstr ""
"HTTP requests on URLs that match any of the following rules and the "
"corresponding responses will be verified by Power Web Protection. "

msgid "Request White List"
msgstr "Request Whitelist"

msgid ""
"Request White List is made up of regular expressions. HTTP requests with "
"destination URLs that match any of the following expressions will be "
"forwarded without being verified by the system."
msgstr ""
"The requests that match any of the following conditions will not be "
"processed by Essential Web Protection."

msgid "Request body"
msgstr ""

msgid "Request body obfuscation"
msgstr "Request Body Obfuscation"

msgid "Request exception"
msgstr ""

msgid "Request failed. Please try again later."
msgstr ""

msgid "Request forward"
msgstr "Pass Through Requests"

msgid "Request headers"
msgstr ""

msgid "Request method count(met)"
msgstr ""

msgid "Request method:"
msgstr "Request Method:"

msgid "Request path cannot be blank!"
msgstr "Request path is required."

msgid "Request statistics"
msgstr "Request Statistics"

msgid "Request timeout"
msgstr ""

msgid "Request uri relaxed mode"
msgstr ""

msgid "Requested URLs"
msgstr "History Requests"

msgid "Required Header"
msgstr "Required Headers"

msgid "Requirement"
msgstr "Condition"

msgid "Reserve user comment."
msgstr "Keep user\\'s HTML comments"

msgid "Reserved Ports"
msgstr ""

msgid "Reset"
msgstr ""

msgid "Reset Password"
msgstr ""

msgid "Reset Statistics to Defaults"
msgstr "Reset Analytics to Defaults"

msgid ""
"Reset the system to factory settings. All the changes made to the system "
"will be lost permanently."
msgstr ""

msgid "Residence Permit"
msgstr ""

msgid "Resolution:"
msgstr " Resolution: "

msgid "Resource File Configuration"
msgstr ""

msgid "Resource Locator"
msgstr ""

msgid "Resource file name should not be empty"
msgstr ""

msgid "Resource file only support UTF-8 encoding"
msgstr ""

msgid "Resource file should not be empty"
msgstr ""

msgid "Resource file size has exceeded the maximum"
msgstr ""

msgid "Resource waf file name should not be empty"
msgstr ""

msgid "Resource waf file only support UTF-8 encoding"
msgstr ""

msgid "Resource waf file should not be empty"
msgstr ""

msgid "Resource waf file size has exceeded the maximum"
msgstr ""

msgid "Response"
msgstr ""

msgid "Response Body"
msgstr "Response Body"

msgid "Response Body Detection Range (KB)"
msgstr "Response Body (KB)"

msgid "Response Body Length"
msgstr ""

msgid "Response Content Type"
msgstr "Content Type"

msgid "Response Content-Type"
msgstr ""

msgid "Response Encapsulation List"
msgstr "Obfuscation List"

msgid ""
"Response Encapsulation List is made up of regular expressions. HTTP "
"responses with destination URLs that match any of the following expressions "
"will be encapsulated by the system. "
msgstr ""
"HTTP requests on URLs that match any of the following rules and the "
"corresponding responses will be obfuscated by Power Web Protection. "

msgid "Response Header"
msgstr "Header Name"

msgid "Response Header Field"
msgstr "Header"

msgid "Response Header Length"
msgstr ""

msgid "Response Header Name"
msgstr "Header Name"

msgid "Response Headers"
msgstr "Header Name & Value"

msgid "Response Length"
msgstr ""

msgid "Response PII Type"
msgstr ""

msgid "Response Stage"
msgstr "Response Stage"

msgid "Response Status"
msgstr "Status Codes"

msgid "Response White List"
msgstr "Response Whitelist"

msgid "Response White List cannot be blank!"
msgstr "Response Whitelist is required."

msgid ""
"Response White List is made up of regular expressions. HTTP responses with "
"destination URLs that match any of the following expressions will be "
"forwarded without being protected by the system."
msgstr ""
"The responses that match any of the following conditions will not be "
"processed by Essential Web Protection."

msgid "Response body obfuscation"
msgstr "Response Body Obfuscation"

msgid "Response data exception"
msgstr ""

msgid "Response exception"
msgstr ""

msgid "Response path cannot be blank!"
msgstr "Response path is required."

msgid "Response timeout"
msgstr ""

msgid "Responsible Person"
msgstr ""

msgid "Responsible for cluster management and synchronization among nodes."
msgstr ""

msgid "Restore"
msgstr ""

msgid "Restore BTA threat model"
msgstr ""

msgid "Restore BTA threat model and threshold"
msgstr ""

msgid "Restore BTA threat threshold"
msgstr ""

msgid "Restore Original Template Package"
msgstr ""

msgid "Restore the default customized rules"
msgstr "Reset the API Recognition Rules to default"

msgid "Restore the default static Resource"
msgstr "Reset the Static Resources to default"

msgid "Restore the default template"
msgstr ""

msgid "Restricted path list"
msgstr "Paths"

msgid "Restriction of visit"
msgstr "Rate"

msgid "Result"
msgstr ""

msgid "Retirement Certificate"
msgstr ""

msgid "Reverse Proxy HA"
msgstr "Reverse Proxy - HA"

msgid "Reverse proxy Deployment"
msgstr "Reverse Proxy"

msgid "Revoke Edits"
msgstr ""

msgid "Right"
msgstr ""

msgid "Risk Interception"
msgstr "Risk Control"

msgid "Risk Level"
msgstr ""

msgid "Risk Library"
msgstr ""

msgid "Risk Threshold"
msgstr ""

msgid "Risk UI Event"
msgstr "Risky UI Event"

msgid "Risk identification model"
msgstr "Risk Model"

msgid "Risk name already exists."
msgstr "Defect name already exists."

msgid "Risk stack trace count(rstc)"
msgstr ""

msgid "Risk stack trace(ds_stack_trace)"
msgstr ""

msgid "Robot Decision Threshold"
msgstr ""

msgid "Role"
msgstr ""

msgid "Role Management Switch"
msgstr ""

msgid "Role Permissions"
msgstr "Permissions"

msgid "Role can only be administrator or guest."
msgstr ""

msgid "Role has been created: "
msgstr ""

msgid "Role has been deleted already, please refresh this page again."
msgstr ""

msgid "Role has been deleted: "
msgstr ""

msgid "Role has been edited: "
msgstr ""

#, python-brace-format
msgid "Role management function is {0}"
msgstr ""

msgid "Role name"
msgstr ""

msgid "Role name is required."
msgstr ""

msgid ""
"Role name must contain only letters, numbers and underscores, and cannot "
"start with underscores!"
msgstr ""

#, python-brace-format
msgid ""
"Role {role} doesn't exist, or you do not have this permission. Please "
"contact your administrator."
msgstr ""

msgid "Roles"
msgstr ""

msgid "Roll Back"
msgstr ""

#, python-brace-format
msgid "Rollback LLM Corpus to {v}"
msgstr ""

#, python-brace-format
msgid "Rollback WAF Ruleset to {v}"
msgstr ""

msgid "Rollback succeeded"
msgstr ""

msgid "Rolling back"
msgstr ""

msgid "Room Number"
msgstr ""

msgid "Root requests count(rrc)"
msgstr ""

msgid "Rooted"
msgstr ""

msgid "Rooted android device and jailbroken iOS device"
msgstr ""

msgid "Rotation"
msgstr ""

msgid "Round Robin"
msgstr ""

msgid ""
"Round-robin policy. Transmit packets in sequential order from the first "
"available slave through the last. This mode provides load balancing and "
"fault tolerance."
msgstr ""
"Round-robin policy. Transmit packets in sequential order from the first "
"available adapter through the last. This mode provides load balancing and "
"fault tolerance."

msgid "Route Deployment"
msgstr "Routing Proxy"

msgid "Route Proxy HA"
msgstr "Routing Proxy - HA"

msgid "Route conflict."
msgstr ""

msgid "Routing"
msgstr ""

msgid "Rule"
msgstr ""

msgid "Rule Config"
msgstr "Edit"

msgid "Rule Configuration"
msgstr "Rule Status"

msgid "Rule Content"
msgstr ""

msgid "Rule Details"
msgstr "Details"

msgid "Rule Editor"
msgstr ""

msgid "Rule ID"
msgstr ""

msgid "Rule ID must be a number."
msgstr ""

msgid "Rule ID: "
msgstr ""

msgid "Rule Info"
msgstr ""

msgid "Rule Modules"
msgstr ""

msgid "Rule Name"
msgstr ""

msgid "Rule Set"
msgstr "Strategy"

msgid "Rule Summary"
msgstr "Rule Summary"

msgid "Rule Summary: "
msgstr "Rule Summary"

msgid "Rule Type"
msgstr "Category"

msgid "Rule {} from Global Custom Rules under WAF found this attack."
msgstr "This attack is found by A Global Custom Rule {} of WAF."

msgid "Rule {} is already {}"
msgstr ""

msgid "Rules List"
msgstr ""

msgid "Rules Number:"
msgstr ""

msgid "Rules Order"
msgstr ""

msgid "Rules in current scene are {}"
msgstr "Rules in current scenario are {}"

msgid "Ruleset Upgrade"
msgstr "Ruleset Management"

msgid "Ruleset upgrade package"
msgstr "Update Package"

msgid "Ruleset version"
msgstr "Ruleset"

msgid "Russia Phone Number"
msgstr "Russia Mobile Phone"

msgid ""
"SAMEORIGIN means the page can only be displayed in a frame of the same "
"origin as the page itself."
msgstr ""

msgid "SDK Common Keys(commonKeys):"
msgstr ""

msgid ""
"SDK only. 'Batch control' systems refer to those that mirror the operation "
"interface of multiple mobile phones on the screen of one computer through "
"automated control integration technology so that one computer can control "
"dozens or even hundreds of mobile phones. This system numbers the operation "
"interface of each mobile phone on the computer and can achieve one-to-one "
"control over each phone. 'Batch control' system is usually composed of "
"software and hardware. The hardware part includes a batch control host, a "
"hub, a computer host and a terminal mobile phone; the software part uses a "
"batch control system that supports distributed control function. Batch "
"control of mobile phones can be realized in a LAN environment."
msgstr ""

msgid "SEO"
msgstr "Search Engine Optimization"

msgid "SIM card data"
msgstr ""

msgid "SKU"
msgstr ""

msgid "SMS Bombing(RAT-033)"
msgstr ""

msgid "SMS Content"
msgstr ""

msgid "SMS Gateway"
msgstr ""

msgid "SMS Receiver"
msgstr "Sent To"

msgid "SMS Sending"
msgstr ""

msgid "SMS Sending APIs Auto-detection"
msgstr "Auto-Recognize SMS-Sending APIs"

msgid "SMS Sent by"
msgstr "Sent By"

msgid "SMS Settings"
msgstr ""

msgid "SMS Verification Code"
msgstr ""

msgid "SMS interface"
msgstr "SMS Interface"

msgid "SMTP Port"
msgstr ""

msgid "SMTP Server"
msgstr ""

msgid "SMTP server denied communication."
msgstr ""

msgid "SMTP server failed to accept data."
msgstr ""

msgid "SNMP"
msgstr ""

msgid "SNMP Support"
msgstr ""

msgid "SOAP File"
msgstr ""

msgid "SOAP Verification"
msgstr ""

msgid "Duplicate source addresses"
msgstr ""

msgid "SP Number"
msgstr ""

msgid "SP Number "
msgstr ""

msgid "SQL Injection"
msgstr ""

msgid "SQL injection model"
msgstr "SQL Injection Model"

msgid "SSE 4.2 instruction set is not supported"
msgstr ""

msgid "SSL Algorithm"
msgstr ""

msgid "SSL Certificate"
msgstr ""

msgid "SSL Ciphers cannot be blank"
msgstr "SSL Ciphers are required."

msgid "SSL Protocol cannot be blank"
msgstr "SSL Protocol is required."

msgid "SSL_Ciphers"
msgstr ""

msgid "SSL_Protocol"
msgstr ""

msgid "Sailfish Server"
msgstr ""

msgid "Salt values can be replicated in lincence"
msgstr ""

msgid "Same Source IP Calls Frequency"
msgstr "Same IP Access Frequency"

msgid ""
"Same account repeat login restricted. Please login again to continue editing"
msgstr ""

msgid "Sat."
msgstr ""

msgid "Save"
msgstr ""

msgid "Save "
msgstr ""

msgid "Save Adjust Priority"
msgstr "Save Priority"

msgid "Save All"
msgstr ""

msgid "Save Site Names"
msgstr ""

msgid "Save Successfully"
msgstr "Saved successfully."

msgid "Save configuration failed."
msgstr ""

msgid "Save data collection configurations failed!"
msgstr ""

msgid "Save data collection configurations success!"
msgstr "Data collection configurations saved successfully"

msgid "Save failed"
msgstr "Failed to save."

msgid "Save failed."
msgstr "Failed to save."

msgid "Save goodbot configuration failed."
msgstr ""

msgid "Save response token maximum length failed!"
msgstr ""

msgid "Save response token maximum length success!"
msgstr ""

msgid "Save risk library config failed!"
msgstr ""

msgid "Save risk library failed!"
msgstr ""

msgid "Save risk library successed!"
msgstr ""

msgid "Save success"
msgstr "Saved successfully."

msgid ""
"Save success, but license have no salt, this feature may not work as "
"expected."
msgstr ""

msgid ""
"Save success, the page needs to jump to the home page to enable "
"configuration."
msgstr ""

msgid "Saved"
msgstr "Saved successfully."

msgid ""
"Saved successful,and has been merged into the WAF whitelist configuration "
"with path {}."
msgstr ""

msgid "Saved successfully."
msgstr "Saved successfully."

#, python-brace-format
msgid ""
"Saving this policy will affect the following {0} where this policy is "
"applied. Confirm to save?<br/>{1}"
msgstr ""

#, python-brace-format
msgid ""
"Saving this rule will affect the following {0} where this rule is applied. "
"Confirm to save?<br/>{1}"
msgstr ""

msgid "Scalping(OAT-005)"
msgstr ""

msgid "Scheduled Export Failed"
msgstr ""

msgid "Scheduled Export Successed"
msgstr ""

msgid "Scheduled_Export"
msgstr ""

msgid "Schema File"
msgstr ""

msgid "Schema Verification"
msgstr ""

msgid "School Name"
msgstr ""

msgid "Scoring basis:"
msgstr ""

msgid "Scraping(OAT-011)"
msgstr ""

msgid "Screen"
msgstr ""

msgid "Screens List"
msgstr "Display Configurations"

msgid "Search"
msgstr ""

msgid "Search "
msgstr ""

msgid "Search Content"
msgstr ""

msgid "Search Engine"
msgstr "Search Engines"

msgid "Search Engine Optimization"
msgstr ""

msgid "Search Engines"
msgstr ""

msgid "Search Path"
msgstr ""

msgid "Search Websites"
msgstr ""

msgid "Search website(s)"
msgstr ""

msgid "Second"
msgstr "seconds"

msgid "Second-Generation Mainland China ID Card"
msgstr "China 2nd-Gen ID Card"

msgid "Second-Generation Mainland China ID Card Number"
msgstr "China Second-Generation Identity Card Number"

msgid "Secondary Server"
msgstr ""

msgid "Securities Account"
msgstr ""

msgid "Security Attributes"
msgstr ""

msgid "Security Level"
msgstr ""

msgid "Security Log"
msgstr ""

msgid "Security Specifications"
msgstr "Security Protocols"

msgid "Security log has been exported!"
msgstr "Exported security log"

msgid "Security scan"
msgstr ""

msgid "Select File"
msgstr ""

msgid ""
"Select ON or OFF from the drop-down menu below to enable or disable support "
"for SNMP."
msgstr ""

msgid "Select Websites"
msgstr ""

msgid "Select a certificate file"
msgstr ""

msgid "Select a private key file"
msgstr ""

msgid "Select a role for this user."
msgstr ""

msgid "Select an authentication mode for this user."
msgstr "elect an authentication method for this user."

msgid ""
"Select an error page template below to replace the default pages sent to "
"visitors’ browsers when errors occur."
msgstr "Replace the default error pages with selected pages below."

msgid "Select the item in the left field list to add a filter"
msgstr "Select a field from the left list to add a filter"

msgid "Select the llm corpus upgrade package"
msgstr "LLM Corpus Package"

msgid "Select the rule base upgrade package"
msgstr "Update Package"

msgid "Select the threat intelligence db upgrade package"
msgstr "Upload Update Package"

msgid ""
"Selection and holding of items from a limited inventory or stock, but which "
"are never actually bought, or paid for, or confirmed, such that other users "
"are unable to buy/ pay/confirm the items themselves. It differs from OAT-005"
" Scalping in that the goods or services are never actually acquired by the "
"attacker. Denial of Inventory is most commonly thought of as taking "
"ecommerce items out of circulation by adding many of them to a cart/basket; "
"the attacker never actually proceeds to checkout to buy them but contributes"
" to a possible stock-out condition. A variation of this automated threat "
"event is making reservations (e.g. hotel rooms, restaurant tables, holiday "
"bookings, flight seats), and/or click-and-collect without payment. But this "
"exhaustion of inventory availability also occurs in other types of web "
"application such as in the assignment of non-goods like service allocations,"
" product rations, availability slots, queue positions, and budget "
"apportionments. If server resources are reduced see OAT-015 Denial of "
"Service instead. Like OAT-005 Scalping , Denial of Inventory also reduces "
"the availability of goods or services."
msgstr ""

msgid "Self-Adaption Data Parser/Decoder"
msgstr "Parser/Decoder"

msgid "Send Bytes"
msgstr ""

msgid "Send Error"
msgstr ""

msgid "Send Packet Loss"
msgstr ""

msgid "Send Packets"
msgstr ""

msgid ""
"Send all logs to all target servers, each server receives the full amount of"
" logs"
msgstr ""

msgid ""
"Send all logs to each target server in a relatively balanced and non-"
"repeating manner, and each server receives part of the logs"
msgstr ""

msgid ""
"Send all reputations to all target servers, each server receives the full "
"amount of reputations"
msgstr "Transmit a complete copy of reputations to each server respectively."

msgid ""
"Send all reputations to each target server in a relatively balanced and non-"
"repeating manner, and each server receives part of the reputations"
msgstr "Spread shards of a copy of reputations evenly on all the server."

msgid "Send failed:"
msgstr ""

msgid "Send in full amount"
msgstr "Complete Copy"

msgid "Send test email successfully."
msgstr "Test email sent successfully"

msgid ""
"Sending the new X-Content-Type-Options response header with the value "
"nosniff will prevent Internet Explorer from MIME-sniffing a response away "
"from the declared content-type."
msgstr "It forbids MIME type sniffing by browsers."

msgid "Sensitive File Detection"
msgstr "PII File Detection"

msgid "Sensitive Information Desensitization Switch"
msgstr "PII Masking Switch"

#, python-brace-format
msgid "Sensitive Log Filter function is {0}"
msgstr "PII Masking is {0}."

msgid "Sensitive information"
msgstr "PII"

msgid "Sensitive paths count(sep)"
msgstr ""

msgid "Sent by"
msgstr "Sent By"

msgid "Sent successfully:"
msgstr ""

msgid "Sent to"
msgstr "Sent To"

msgid "Separate multiple value with commas"
msgstr ""

msgid "Separated with \",\""
msgstr "Separated with commas"

msgid "Serial Number"
msgstr "ID"

msgid "Server"
msgstr ""

msgid "Server Address:"
msgstr "Server Address"

msgid "Server IP"
msgstr ""

msgid "Server IP/IP Segment"
msgstr ""

msgid "Server List: "
msgstr ""

msgid "Server Offline With Token"
msgstr "Server Offline Token"

msgid "Server Password"
msgstr ""

msgid "Server address, only supports IPv4"
msgstr ""

msgid "Server connection timeout."
msgstr ""

msgid "Server error! Check logs for details."
msgstr ""

msgid "Server interface is abnormal"
msgstr ""

msgid "Server internal error"
msgstr ""

msgid "Server password is blank."
msgstr ""

msgid "Server protocol is blank."
msgstr ""

msgid "Server protocol should be RADIUS."
msgstr ""

msgid "Server to Client"
msgstr ""

msgid "Server weight for load balancing"
msgstr ""

msgid "Server-Side Request Forgery"
msgstr ""

msgid "Servers"
msgstr ""

msgid "Service Effective Date"
msgstr ""

msgid "Service Expiration Date"
msgstr ""

msgid "Service Monitor"
msgstr ""

msgid "Service Port"
msgstr ""

msgid "Service Status"
msgstr ""

msgid "Service Type"
msgstr ""

msgid "Service failure is found on this node."
msgstr " failure is found on this node."

msgid "Service is abnormal or initializing on this node."
msgstr " is abnormal or in the process of initializtion on this node."

msgid "Service status detection request exception"
msgstr ""

msgid "ServiceManagerState"
msgstr ""

msgid "Session ID <span class='unique'>((ip/fp)sid)"
msgstr ""

msgid "Session end time(et)"
msgstr ""

msgid "Session start time(st)"
msgstr ""

msgid "Session type(kt, IP = 1, Fingerprint=2)"
msgstr ""

msgid "SessionID"
msgstr ""

msgid "Set AAA configuration."
msgstr "Set Remote Authentication Server"

msgid "Set BTA session count"
msgstr ""

msgid "Set BTA session life"
msgstr "Modified BTA session settings"

msgid "Set BTA session max len"
msgstr ""

msgid "Set BTA session timeout"
msgstr ""

msgid "Set Network Ddos Protect Mode"
msgstr "Modified Network-Layer DDoS mode"

msgid "Set SMS sender Account"
msgstr "Set up an account to send alarm SMS messages."

msgid "Set WAF Auto Adaption Decoder"
msgstr "WAF Parser/Decoder"

msgid "Set WAF Block Config"
msgstr "Status code for blocking by WAF changed"

msgid "Set WAF Detection Size"
msgstr "Inspection Size"

msgid "Set WAF Settings Log Collect"
msgstr "WAF Attack Details Logging"

msgid "Set WAF Upload File Inflate"
msgstr "WAF Upload File Inflate"

msgid "Set WAF attack log threshold"
msgstr ""

msgid "Set WAF for api request only"
msgstr "WAF for api request only"

msgid "Set Web Protection Token Advanced Scheme: Name by cluster key"
msgstr ""

msgid "Set Web Protection Token Advanced Scheme: Name by license salt key"
msgstr ""

msgid ""
"Set Web Protection Token Advanced Scheme: Name by license salt key & cross-"
"cluster decryption"
msgstr ""

msgid "Set WebConsole Host White Lists configuration."
msgstr "Set WebConsole Host Whitelist configuration."

#, python-brace-format
msgid ""
"Set a validity period for the uploaded {0} in batch. The value entered must "
"be an integer and the period cannot exceed {1} years. {2} means the list is "
"valid permanently."
msgstr ""

#, python-brace-format
msgid "Set account {username} for sending alert emails."
msgstr "Set account {username} for sending alert emails"

#, python-brace-format
msgid "Set max disk space for log analysis to {size}GB."
msgstr "Set disk space allowed for log analysis to {size}GB."

#, python-brace-format
msgid "Set max disk space for log archiving to {size}GB."
msgstr "Set disk space allowed for log archiving to {size}GB."

msgid "Set max size of upload file（1-1048576）MB."
msgstr ""
"Set a maximum file size (in MB) allowed to be uploaded to the protected "
"websites."

msgid "Set network layer access control rules successfully."
msgstr ""

msgid ""
"Set some internal system parameters to enhance data processing throughput."
msgstr ""

msgid ""
"Set the following information in a valid request forwarded to the server."
msgstr ""
"The values entered for headers below will be used to replace those in "
"requests or insert in before forwarding to servers."

msgid "Set the interval of SDK collection(Unit:min)."
msgstr "Set a frequency (in minutes) for the SDK to collect data."

#, python-brace-format
msgid ""
"Set the network administrator {username}@{gateway} to send alarm short "
"messages."
msgstr ""

msgid "Set the report log filter configuration."
msgstr "Modified Statistics Log Filter configurations"

msgid ""
"Set up a policy to prevent malicious behaviors by placing limits on reuse "
"times of tokens within the specified time."
msgstr ""
"Set up a policy to prevent malicious behaviors by placing limits on reuse "
"times of tokens in POST requests within the specified time."

msgid "Set up account(s) to receive alarm e-mails."
msgstr ""

msgid "Set up an account to send alarm e-mails."
msgstr ""

msgid "Set user behavior rules."
msgstr ""

msgid "Disabled login source address control"
msgstr ""

msgid "Enabled login source address control"
msgstr ""

msgid "Set_Offline"
msgstr "Took offline "

msgid "Set_Online"
msgstr "Took online "

msgid ""
"Sets the time during which systems remains overloaded to trigger alarms; and"
" the time during which usage stays below threshold to clear alarms."
msgstr ""
"An alarm is raised if any of the following system resources remains "
"overloaded for the period of time you entered. An alarm is released if the "
"corresponding resource usage stays below threshold for the period of time "
"you entered."

msgid "Setting API lifecycle"
msgstr "Setting API Lifecycle"

msgid ""
"Setting an excessively large collection length can significantly impact "
"system performance. Please configure it with caution. Integer(1-30)"
msgstr ""
"Set with caution as too large size will severely impact system performance. "
"Integer 1-30."

msgid "Setting failed"
msgstr ""

msgid "Settings"
msgstr "Settings"

msgid "Severity"
msgstr ""

msgid "Share Dict List"
msgstr ""

msgid "Share Memory"
msgstr "Shared Memory"

msgid "Share dict name"
msgstr ""

msgid "Share dict size(MB)"
msgstr ""

#, python-brace-format
msgid "Share memory {0} dose not exist"
msgstr "Shared memory {0} dose not exist"

msgid "Should be an integer."
msgstr ""

msgid "Should but without body encryption count(wec)"
msgstr ""

msgid "Should but without body encryption(ds_without_body_enc)"
msgstr ""

msgid "Show only the last 10 records"
msgstr ""

msgid "Shutdown"
msgstr ""

msgid "Shutdown node"
msgstr ""

msgid "Shutdown node: "
msgstr ""

msgid "Shutdown the system?"
msgstr ""

msgid "Signature certificate file"
msgstr ""

msgid "Signature certificate file has been uploaded."
msgstr ""

msgid "Signature private key file"
msgstr ""

msgid "Signature private key file has been uploaded."
msgstr ""

msgid "Similarity"
msgstr ""

msgid "Singapore ID Card"
msgstr ""

msgid "Singapore Passport Number"
msgstr "Singapore Passport"

msgid "Single Leakage of Multiple PIIs"
msgstr ""

msgid "Single Network Adapter"
msgstr ""

msgid "Single Response of Multiple PII Types"
msgstr "PII Types in Single Response"

msgid "Single Response of Multiple PIIs"
msgstr "PII Pieces in Single Response"

msgid "Single file size exceed 20M."
msgstr ""

msgid "Single pii type at most can specified {} apis"
msgstr ""

msgid "Single protection list has exceeded 1M"
msgstr ""

msgid "Single-Node Quick Deployment"
msgstr "Single-Node"

msgid "Site / Account name"
msgstr ""

msgid "Site Custom Rules"
msgstr ""

msgid "Site Strategy"
msgstr "Site Strategy"

msgid "Site Strategy Applied"
msgstr "Used by Site Strategies"

msgid "Site configuration state has been modified"
msgstr ""

msgid "Site could have one operator only."
msgstr ""
"A single site can have 5 operators at most. Assigning only one operator to a"
" site is recommended if you want to use QPS Management later."

msgid "Site could have one statistic viewer only."
msgstr "A single site can have 5 analytics viewers at most."

#, python-brace-format
msgid ""
"Site:{upstream} uri: {uri} similarity {similarity}%, suspected to be "
"tampered."
msgstr ""

msgid "Sites Strategy"
msgstr "Site Strategy"

msgid ""
"Sites that use different protocol (http and https) are not allowed to listen"
" on the same port."
msgstr ""

msgid "Site|Operator|StatisticViewer|Action"
msgstr "Site|Operator|Analytics Viewer|Operations"

msgid "Size"
msgstr ""

msgid "Skewing(OAT-016)"
msgstr ""

msgid "Slow_Http_Attack"
msgstr "Slow Http Attack"

msgid "Smart Transparent"
msgstr "Smart Pass-Through"

msgid "Snapshot"
msgstr "Webpage Snapshot"

msgid "Sniffer"
msgstr ""

msgid ""
"Sniffer is a tool used to capture packets traveling along networks so that "
"technicians could conduct analysis based on the data captured."
msgstr ""

msgid "Sniping"
msgstr ""

msgid "Sniping Path"
msgstr "Path"

msgid "Sniping Scene"
msgstr "Sniping Scenario"

msgid "Sniping Type"
msgstr ""

msgid "Sniping in advance"
msgstr "Sniping in Advance"

msgid "Sniping(OAT-013)"
msgstr ""

msgid "Soft Mutil-open"
msgstr "Multi-Instance (Software-Based)"

msgid "Some log data will be lost."
msgstr ""

msgid ""
"Some nodes in the cluster are offline, and the query results will not be "
"from complete data."
msgstr ""
"Some nodes in the cluster are offline, and the query results might not be "
"complete"

msgid "Sorted By"
msgstr "Abnormalities"

msgid "Source"
msgstr ""

msgid "Source Address Extranet Detection Rate"
msgstr "Public IP Detection Rate"

msgid "Source Address Intranet Detection Rate"
msgstr "Privte IP Detection Rate"

msgid "Source IP"
msgstr "Request Source IP"

msgid "Source IP "
msgstr ""

msgid "Source IP Address"
msgstr ""

msgid "Source Node"
msgstr ""

msgid "Source Port"
msgstr ""

msgid "Source/Attack Target/Rule ID"
msgstr "Source/Attacked Target/Rule ID"

msgid "Source: "
msgstr ""

msgid "South Korea Passport Number"
msgstr "South Korea Passport"

msgid "Spamming(OAT-017)"
msgstr ""

msgid "Spark Business Component"
msgstr ""

msgid "Spark Business Component APIs Auto-detection"
msgstr "Auto-Recognize Spark-Business-Component APIs"

msgid "Specific Apps"
msgstr "App-Specific Protections"

msgid "Specific Features"
msgstr ""

msgid ""
"Specific requests are sent to the application eliciting information in order"
" to profile the application. This probing typically examines HTTP header "
"names and values, session identifier names and formats, contents of error "
"page messages, URL path case sensitivity, URL path patterns, file "
"extensions, and whether software-specific files and directories exist. "
"Fingerprinting is often reliant on information leakage and this profiling "
"may also reveal some network architecture/topology. The fingerprinting may "
"be undertaken without any direct usage of the application, e.g. by querying "
"a store of exposed application properties such as held in a search engine's "
"index. Fingerprinting seeks to identity application components, whereas "
"OAT-018 Footprinting is a more detailed analysis of how the application "
"works."
msgstr ""

msgid "Specify Parameter"
msgstr "Required Parameter"

msgid "Specify a path in regular expression"
msgstr ""

msgid "Specify a port number for accessing the WebConsole."
msgstr "A port used to access the WebConsole."

msgid "Specify an IP to be detected, IP network segment is supported."
msgstr "Enter a single IP or a Network Segment."

msgid ""
"Specify an IP to be removed from malicious IP list of User Behavior Rules:"
msgstr ""
"Specify an IP to be removed from malicious IP list created by User Behavior "
"Rules"

msgid "Specify an IP to be threat whitelist, IP network segment is supported."
msgstr ""
"Enter IPv4 or IPv6 addresses. Network segments in CIDR format are supported."
" Use commas to separate multiple addresses."

msgid "Specify an IP to be waf whitelist, IP network segment is supported."
msgstr ""
"Support IPv4 and IPv6 CIRD addresses (separate multiple addresses with "
"commas)"

msgid "Specify parameter cannot exceed 100 characters."
msgstr ""

msgid ""
"Specify positions to obtain source IPs. 10 positions are allowed to be added"
" at most. The system will be looking into these positions in an order from "
"top to bottom until it finds one."
msgstr ""
"Specify positions to obtain source IPs. 10 positions are allowed to be added"
" at most. The system will be looking into these positions in an order from "
"top to bottom until it finds one."

msgid ""
"Specify the Cookie value to be detected. Note: * represents any cookie name."
msgstr "Enter Cookie name (* means any names) and value in pairs."

msgid "Specify the URL of the resource to be cached."
msgstr "Add URLs to cache the corresponding pages."

msgid "Specify the URL to be protected."
msgstr "Add URLs that need to be checked."

msgid "Specify the data not to be collected, with commas as separators."
msgstr ""

msgid ""
"Specify the network segments from which packets will be captured, as well as"
" IPs and ports accordingly."
msgstr ""

msgid ""
"Specify the ports which are reserved for the protection system, so that "
"these ports will not be used by automatic port assignments. Multiple ports "
"or port ranges can be entered and seperated with commas. Port ranges must be"
" in the format of start port-end port. (Example: 2000,60000-60009,3000)."
msgstr ""

msgid ""
"Specify the protocol, domain name and port number of the website to be "
"protected."
msgstr "Specify IP or domain name of the website to be protected."

msgid ""
"Specify the protocols, IPs, port numbers and weight of servers of the "
"website added above. "
msgstr "Add upstream web servers of the protected website."

msgid ""
"Specify the protocols, IPv4s, port numbers and weight of servers of the "
"website added above. "
msgstr ""

msgid ""
"Specify the protocols, IPv6s, port numbers and weight of servers of the "
"website added above. "
msgstr ""

msgid ""
"Specify the request header to be detected. Note: * represents any request "
"header."
msgstr "Enter header name (* means any names) and value in pairs."

msgid ""
"Specify the request parameter value to be detected. Note: * represents any "
"parameter name."
msgstr "Enter parameter name (* means any names) and value in pairs."

msgid ""
"Specify the response header to be detected. Note: * represents any response "
"header."
msgstr "Enter header name (* means any names) and value in pairs."

msgid "Speed"
msgstr "Rate"

msgid "Speedup Mode"
msgstr ""

msgid "Split params extends the limit {}"
msgstr ""

msgid "Split polling"
msgstr "Partitioned Copy"

msgid "Spring Boot Business Component"
msgstr ""

msgid "Spring Boot Business Component APIs Auto-detection"
msgstr "Auto-Recognize Spring-Boot-Business-Component APIs"

msgid "Stage Token_init stage request count(stic)"
msgstr ""

msgid "Start"
msgstr ""

msgid "Start Analyzing"
msgstr ""

msgid "Start At"
msgstr ""

msgid "Start IP"
msgstr ""

msgid "Start Time"
msgstr ""

msgid "Start analysis"
msgstr "Start Analysis"

msgid "Start date is later than end date!"
msgstr ""

msgid "Start port must be smaller than end port in port ranges."
msgstr ""

msgid "Start to learn"
msgstr "Start learning"

msgid "Start with"
msgstr "Start with"

msgid "Start with(no regular expression)"
msgstr ""

msgid "Static Query"
msgstr ""

msgid "Static Resource"
msgstr "Static Resources"

msgid "Static Resource Suffix Name"
msgstr "Static Resources"

msgid "Static goodbots version"
msgstr "Good Bot Database Version"

msgid "Static resource count(sc)"
msgstr ""

msgid "Statistic Policy"
msgstr "Statistics"

msgid "StatisticViewer"
msgstr "Analytics Viewer"

msgid ""
"StatisticViewer: Limited access rights, only partial statistic can be "
"viewed, but cannot be modified."
msgstr "Analytics Viewer: Limited permission to read some analytics features."

msgid "Statistical Dimension"
msgstr "Key"

msgid "Statistics"
msgstr "Analytics"

msgid "Statistics Control Center"
msgstr ""

msgid "Statistics Log Filter"
msgstr ""

msgid "Statistics Report"
msgstr "Analytics Reports"

msgid "Statistics Secret"
msgstr ""

msgid "Statistics Secret has been disabled."
msgstr ""

msgid "Statistics Secret has been enabled."
msgstr ""

msgid "Statistics has been reset to defaults."
msgstr "Analytics has been reset to defaults."

msgid "Statistics of API Extranet Upstream Address request"
msgstr "Statistics of extranet-upstream API requests"

msgid "Statistics of API Intranet Source IP request"
msgstr "Statistics of API requests from internal networks"

msgid "Statistics of API PII request"
msgstr "Statistics of API PII requests"

msgid "Statistics of API request"
msgstr "Statistics of API requests"

msgid "Statistics of Auto-Blacklist(Account)"
msgstr ""

msgid "Statistics of Auto-Blacklist(Account+Hostname)"
msgstr ""

msgid "Statistics of Auto-Blacklist(Class C IP Addresses)"
msgstr "Statistics of Auto-Blacklist (Class C IP Address)"

msgid "Statistics of Auto-Blacklist(Fingerprint)"
msgstr "Statistics of Auto-Blacklist (Fingerprint)"

msgid "Statistics of Auto-Blacklist(IP address)"
msgstr "Statistics of Auto-Blacklist (IP address)"

msgid "Statistics of High Frequence Scene"
msgstr "Statistics of High Frequency Scenario"

msgid "Statistics of New Comer Scene"
msgstr "Statistics of New User Scenario"

msgid "Statistics of The Same API Source IP request"
msgstr "Statistics of same-source-IP API requests"

msgid "Statistics subsystem is not activated"
msgstr "Analytics system is not activated"

msgid "Statistics_License"
msgstr "Analytics License"

msgid "Statistics_Report"
msgstr "Analytics Reports"

msgid "Status"
msgstr ""

msgid "Status 4XX count(4xx)"
msgstr ""

msgid "Status 5XX count(5xx)"
msgstr ""

msgid "Status 5xx count(btx)"
msgstr ""

msgid "Status Code"
msgstr ""

msgid "Status Code for Blocking"
msgstr "Status Code for Abnormal Request"

msgid "Status code(s) to be filtered"
msgstr "Status Codes"

msgid "Step 1: Query the path sequence"
msgstr ""

msgid "Step 2: Confirm the path sequence"
msgstr ""

msgid "Step 3: Analysis results"
msgstr ""

msgid "Steps for short-term exercise"
msgstr ""

msgid "Stop"
msgstr ""

msgid "Storage node expansion"
msgstr ""

msgid "Strategy"
msgstr "Block via"

msgid "Strategy Config"
msgstr "Edit"

msgid "Strategy Copy"
msgstr "Copy"

msgid "Strategy Details"
msgstr "Details"

msgid "Strategy Disabled"
msgstr "Disabled"

msgid "Strategy ID"
msgstr ""

msgid "Strategy Name"
msgstr ""

msgid "Strategy Template"
msgstr ""

msgid "Strategy apply failed."
msgstr ""

msgid "Strategy creation failed."
msgstr ""

#, python-brace-format
msgid "Strategy details of {0}"
msgstr ""

msgid "Strategy name is existed."
msgstr ""

msgid "Stretch"
msgstr ""

msgid "Strict"
msgstr ""

msgid "Strict Level"
msgstr "Strict"

msgid "Strictly Match Host Header"
msgstr ""

msgid "String"
msgstr ""

msgid "Student ID"
msgstr ""

msgid "Study Again"
msgstr "Relearn"

msgid "Study Statistics"
msgstr "Analystics"

msgid "Style Selection"
msgstr ""

msgid "Submit comments"
msgstr "Submit Comment"

msgid "Submit verification code"
msgstr "Submit Verification Code"

msgid "Subscribe Evaluation"
msgstr ""

msgid "Subtotal"
msgstr ""

msgid "Succeed To Upload File"
msgstr "File successfully uploaded"

msgid "Succeeded to upload error page template"
msgstr ""

msgid "Success to configure MPP list"
msgstr ""

msgid "Success to modify browser proxy detection"
msgstr "Modified Browser Proxy Detection"

msgid "Success to save weak password dictionary."
msgstr ""

msgid "Success to set Threat Intelligence mode"
msgstr "Modified Threat Intelligence mode"

msgid "Success to upload MPP SDK"
msgstr ""

msgid ""
"Successfully allow access to protected sites through management network "
"adapter"
msgstr ""

msgid "Successfully edit risk setting"
msgstr ""

msgid "Successfully logged in to WebConsole"
msgstr ""

msgid "Successfully logged out from WebConsole"
msgstr ""

msgid "Successfully manual remove alarm"
msgstr ""

msgid "Successfully removed unused APIs."
msgstr ""

msgid "Successfully reset risk setting to default"
msgstr ""

msgid "Successfully saved!"
msgstr ""

msgid "Suffix"
msgstr "Suffix match"

msgid "Suggested Actions"
msgstr ""

msgid ""
"Suitable for receiving mirrored traffic from web servers and analyzing it "
"for inspection."
msgstr ""

msgid "Summary refresh failed"
msgstr ""

msgid "Sun."
msgstr ""

msgid "Super Token"
msgstr ""

msgid "Super Token cannot be blank!"
msgstr "Super Token is required."

msgid "Support Code"
msgstr ""

msgid "Support entering multiple suffix names, separated by commas."
msgstr "Enter extensions, separated by commas."

msgid "Support parameter must be integer between 2 to 5000"
msgstr ""

msgid "Supported"
msgstr ""

msgid "Supported Modules"
msgstr ""

msgid "Sure"
msgstr "OK"

#, python-brace-format
msgid "Sure to clear the {0}?"
msgstr ""

msgid "Sure to delete the node "
msgstr ""

#, python-brace-format
msgid "Sure to delete the node: {0}?"
msgstr ""

msgid "Suspected web page tampering"
msgstr "Possible webpage tampering"

msgid "Switch View"
msgstr ""

msgid "Switch bypass automatically"
msgstr "Bypass switched on"

msgid "Switch bypass off automatically"
msgstr ""

msgid "Switch bypass on automatically"
msgstr ""

msgid "Switch industry type from {} to {}"
msgstr "Changed industry categorization from {} to {}."

msgid "Switch verify app"
msgstr "App Integrity"

msgid "Switch verify app fail, please check is_app_verify value!"
msgstr ""

msgid "Switch verify app failed!"
msgstr ""

msgid "Switch verify app success!"
msgstr ""

msgid "Sync from other RAS cluster (exclude third party reputation)"
msgstr "From Another Cluster"

msgid "Sync resource file {}:{} from zk failed."
msgstr ""

msgid "Sync resource waf file {}:{} from zk failed."
msgstr ""

msgid "Syntax Analysis"
msgstr ""

msgid "Syntax Modules"
msgstr "Semantic Modules"

msgid "Syntax Strategy"
msgstr "Semantic Module Strategy"

msgid "Syntax error found in list file"
msgstr ""

msgid "Syntax error found in rule file"
msgstr ""

msgid "Syntax error of the advanced filter."
msgstr ""

msgid "Syntax error or no BTA service"
msgstr ""

msgid "Syntax errors found in rule:"
msgstr ""

msgid "Syntax of Rules"
msgstr "Syntax Document"

msgid "Syntax tips"
msgstr ""

msgid "System"
msgstr ""

msgid "System Config has been exported"
msgstr "Exported system settings"

msgid "System Hooked"
msgstr ""

msgid "System Log"
msgstr ""

msgid "System Log has been Downloaded"
msgstr ""

msgid "System Log has been Generated"
msgstr ""

msgid "System Management Port"
msgstr "WebConsole Port"

msgid "System Mutil-open"
msgstr "Multi-Instance (System-Based)"

msgid "System Session Expiration Time"
msgstr "Login Expiration"

msgid "System Settings"
msgstr ""

msgid "System Update"
msgstr ""

msgid "System Version"
msgstr ""

msgid "System function been hooked request count(hlc)"
msgstr ""

msgid "System function been hooked(ds_system_function_hooked)"
msgstr ""

msgid "System installation request exception"
msgstr ""

msgid "System log generate failed"
msgstr ""

msgid "System parameter settings"
msgstr ""

msgid "System parameter settings: Off"
msgstr "Disabled System parameter settings"

msgid "System parameter settings: On"
msgstr "Enabled System parameter settings"

msgid "System rollback request exception"
msgstr ""

msgid "System session expiration time is set successfully."
msgstr ""

msgid "System session expiration time setting failed."
msgstr ""

msgid "System settings has been imported"
msgstr ""

msgid "System settings has been imported!"
msgstr ""

msgid ""
"System versions vary among nodes in this cluster, it may cause unpredictable"
" risks."
msgstr ""
"System versions vary among nodes in this cluster. It may cause unpredictable"
" risks."

msgid ""
"Systematic enumeration and examination of identifiable, guessable and "
"unknown content locations, paths, file names, parameters, in order to find "
"weaknesses and points where a security vulnerability might exist. "
"Vulnerability Scanning includes both malicious scanning and friendly "
"scanning by an authorised vulnerability scanning engine. It differs from "
"OAT-011 Scraping in that its aim is to identify potential vulnerabilities. "
"The exploitation of individual vulnerabilities is not included in the scope "
"of this ontology, but this process of scanning, along with OAT-018 "
"Footprinting, OAT-004 Fingerprinting and OAT-011 Scraping often form part of"
" application penetration testing."
msgstr ""

msgid "TCP"
msgstr ""

msgid "TCP Flood Defense:"
msgstr "TCP Flood Protection"

msgid "TOTP Token"
msgstr ""

msgid "Tag failed: {} not exist"
msgstr ""

msgid ""
"Tag-based method. The system will encrypt both paths and parameters in "
"requests initiated from the selected HTML tags."
msgstr ""
"Tag-based method. The system will encrypt both paths and parameters in "
"requests initiated from the selected HTML tags."

msgid "Taiwan Compatriot Certificate"
msgstr "Taiwan Compatriot Permit"

msgid "Taiwan ID Card"
msgstr ""

msgid "Taiwan Passport Number"
msgstr "Taiwan Passport"

msgid "Taiwan Phone Number"
msgstr "Taiwan Mobile Phone"

msgid "Taiwan Region Passport Number"
msgstr "Taiwan Region Passport"

msgid "Taiwan Residence Permit"
msgstr "Residence Permit for Taiwan Residents"

msgid "Taiwan Residence Permit of the People's Republic of China"
msgstr ""
"Residence Permit Issued by the People's Republic of China for Taiwan "
"Residents"

msgid "Taiwan Resident ID Card"
msgstr ""

msgid "Taiwan Residents' Travel Permit to Chinese Mainland"
msgstr "Mainland Travel Permit for Taiwan Residents"

msgid "Taking effect on Missile Map. Default location will apply if blank"
msgstr ""
"Specify the longitude and latitude of the attack target in the map. Leave "
"both empty to have the default location. The drop-down menu at the end "
"provides a quick selection from major cities."

msgid "Tamper Cache path"
msgstr ""

msgid "Tapping"
msgstr ""

msgid "Target"
msgstr ""

msgid "Target API: {} not exist"
msgstr ""

msgid "Target IP"
msgstr ""

msgid "Target Port"
msgstr ""

msgid "Target app site: {} not exist"
msgstr ""

msgid "Task sent successfully. Waiting for background to change the role"
msgstr ""
"Roles changed successfully.<br/>Please wait for the changes to take effect."

msgid "Tax Registration certificate Number"
msgstr "Tax Identification Number"

msgid "Template"
msgstr ""

msgid "Template ID"
msgstr ""

msgid "Template Introduce"
msgstr ""

msgid "Template Style Editor"
msgstr ""

#, python-brace-format
msgid "Template does not exist: [{0}]"
msgstr ""

msgid "Template package uploaded failed."
msgstr ""

msgid "Template package uploaded successfully."
msgstr ""

msgid "Templates Count"
msgstr ""

msgid "Templates Introduce"
msgstr "About the templates"

msgid "Templates Version"
msgstr ""

msgid "Test"
msgstr ""

msgid "Test result"
msgstr ""

msgid "Test tool"
msgstr "Testing Tools"

msgid "Testing Tools"
msgstr ""

msgid "Text alignment"
msgstr "Text Alignmen"

msgid "Thailand Phone Number"
msgstr "Thailand Mobile Phone"

msgid ""
"The AI-WAF request whitelist consists of regular expressions. The system "
"will not verify HTTP requests whose destination URL address matches any of "
"the following expressions."
msgstr ""
"HTTP requests to the following whitelist path will not be processed by AI-"
"WAF. Regular expressions are supported."

msgid "The API parameter name cannot be empty and cannot exceed {} characters"
msgstr ""

msgid ""
"The API parameter name only supports letters, numbers, and special "
"characters ._- (JSON format . as a delimiter)"
msgstr ""

msgid ""
"The API parameter value cannot be empty and cannot exceed {} characters"
msgstr ""

msgid ""
"The API parameter value only supports letters, numbers, Chinese characters, "
"and special characters ._-:/"
msgstr ""

msgid ""
"The APPVersion number is incorrect and it should not exceed 20 characters."
msgstr ""

msgid ""
"The Big Data Analysis (Storage) node with the smallest ID in the cluster "
"will automatically become the Query node, but user-defined queries and "
"dashboards will be lost."
msgstr ""
"The Big Data Analysis (Storage) node with the smallest ID in the cluster "
"will automatically become the Query node, but user-defined queries and "
"dashboards will be lost. "

msgid ""
"The DAP system will process responses containing paths specified below with "
"appropriate measures according to the content type selected."
msgstr ""
"It allows the system process responses on a path based on the Content-Type "
"specified here other than that declared by servers."

msgid "The HTTP requests with methods selected below will be blocked."
msgstr "The HTTP request methods selected below will be blocked."

#, python-brace-format
msgid "The IP already exists : {0}"
msgstr ""

msgid "The IP black list file has been uploaded"
msgstr "IP Blacklist file uploaded"

msgid "The IP white list file has been uploaded"
msgstr "IP Whitelist file uploaded"

msgid ""
"The Maximum length of Request Args refers to the sum of the length of the "
"URL parameter and the length of the body parameter."
msgstr "It is the sum of URL args and body."

msgid "The Node {}: "
msgstr ""

msgid ""
"The Policy and Cache File (if enabled) are executed if clients download any "
"files that meet one of the conditions below."
msgstr ""

msgid ""
"The SDKVersion number is incorrect and it should not exceed 20 characters."
msgstr ""

msgid ""
"The SMS sending interface should be called by the backend fixed template to "
"the frontend, and the frontend cannot directly determine the content of the "
"SMS sent."
msgstr ""
"Use SMS templates returned from backend to send SMS messages instead of "
"sending directly at frontend."

msgid "The SMTP port entered is incorrect."
msgstr ""

msgid "The SMTP server entered is incorrect."
msgstr ""

msgid ""
"The SSL certificate for download here is randomly generated by and can only "
"be used on the current cluster."
msgstr ""
"The SSL certificate for download here is randomly generated by, and can only"
" be used on, the current cluster."

msgid ""
"The SSRF vulnerability allows attackers to execute malicious requests on the"
" server. Attackers may exploit SSRF vulnerabilities to access internal "
"sensitive files, bypass firewalls, initiate port scanning, or attack "
"internal systems, among other malicious activities."
msgstr ""

msgid ""
"The Statistics subsystem did not find a perpetual license. Please update "
"license when available."
msgstr ""
"The analytics system is not activated by a formal license. Please update a "
"license when available."

msgid "The Template Introduce of Dynamic Challenge "
msgstr ""

msgid "The URL protection list of the mobile certificate cannot be blank."
msgstr ""

msgid "The Uploaded license file is expired."
msgstr ""

msgid "The VRID must be unique."
msgstr ""

msgid ""
"The WAF policy you configured does not exist in the system, please close to "
"re-select or confirm to create the policy by default in the system?"
msgstr ""

msgid "The action is to intercept"
msgstr ""

msgid "The action is to replace"
msgstr ""

msgid "The activation of the statistics system has failed."
msgstr "Analytics system activation failed"

msgid ""
"The active anti-scan function is enabled, and the system will enable "
"security configuration by default."
msgstr ""
"Block Scanning is enabled, and the system will enable security configuration"
" by default."

msgid "The adapter is abnormal"
msgstr ""

msgid "The adapter is normal"
msgstr ""

msgid "The adapter is not enabled"
msgstr ""

msgid ""
"The admin apdapter can add an IPv4 route to ensure that the modified network"
" is reachable"
msgstr "Add an IPv4 route for eth0 to ensure it is reachable after modified"

msgid "The app is already registered!"
msgstr ""

msgid ""
"The apps in the following list have passed the integrity check and can "
"access the server."
msgstr ""
"Apps in the following list mean that they have passed the integrity check "
"and are allowed to access the servers."

msgid "The block does not exist."
msgstr ""

msgid ""
"The browser does not support Command Line, please download the lastest "
"version of  chrome."
msgstr ""

msgid "The business type does not exist, Please refresh the page."
msgstr ""

msgid ""
"The change disable alipay mini program protection, the site will be "
"automatically remove from alipay mini program protection, as follow: "
msgstr ""
"This change would disable Alipay Mini-Program protection, the site will be "
"automatically remove from Alipay Mini-Program protection, as follow: "

msgid ""
"The change disable mobile mini program protection, the site will be "
"automatically remove from mobile mini program protection, as follow: "
msgstr ""
"This change would disable ChinaMobile Mini-Program protection, the site will"
" be automatically remove from ChinaMobile Mini-Program protection, as "
"follow: "

msgid ""
"The change disable wechat mini program protection, the site will be "
"automatically remove from wechat mini program protection, as follow: "
msgstr ""
"This change would disable WeChat Mini-Program protection, the site will be "
"automatically remove from WeChat Mini-Program protection, as follow: "

msgid "The character length in Comment has exceeded the maximum number of 50."
msgstr ""

msgid "The chosen size will be opened in a new tab"
msgstr ""
"Set display configurations and click a resolution to open a dashboard for "
"large screen."

msgid "The cluster IP whitelist is incorrect:"
msgstr ""

msgid ""
"The collected password field is used for weak password identification. This "
"is optional."
msgstr "(Optional) Collect password parameters to identiy weak passwords."

msgid ""
"The compatibility time has not been modified. Suggest changing to one day "
"after the current time. Are you sure to modify the current policy?"
msgstr ""
"The buffer time has not been modified. It is recommended that you give it 24"
" hours or more from now.Proceed anyway?"

msgid "The compressed file uploaded cannot exceed 200MB"
msgstr ""

msgid "The configuration format is wrong."
msgstr ""

msgid "The configuration has been save."
msgstr ""

#, python-brace-format
msgid "The configuration has exceeded {0}"
msgstr ""

msgid ""
"The configuration is successfully saved to the local, but cannot be "
"synchronized to the cluster due to a cluster failure. The configuration may "
"be overwritten after the cluster is restored!"
msgstr ""

msgid ""
"The configured WAF whitelist will be applied to all protected sites. When "
"only the IP is specified, it means that the request response for that IP "
"does not accept any rule checks. When only the rule ID is specified, it "
"means that the rule does not check for any IP request responses."
msgstr ""
"The whitelists created here are effective to WAF functionalites of all the "
"websites that are protected by the system. A whitelist created only with "
"Source IP, Host, URL or Method will allow corresponding requests to avoid "
"all WAF rules, whereas the one created with only Rule ID will disable the "
"corresponding WAF rules."

msgid ""
"The connection live time, timeout will no longer collect this connection "
"message, the default is 20 seconds, and the allowed configuration range is "
"20-120 seconds"
msgstr ""

msgid "The content of the first few characters"
msgstr "Keep the first"

msgid "The content of the reputation library to be exported is not specified."
msgstr ""

msgid "The content you entered does not meet requirements."
msgstr ""

msgid "The cookie name contains illegal characters, please re-enter."
msgstr ""

msgid ""
"The ctidb base upgrade package does not exist, please click to check for "
"updates."
msgstr "The update package does not exist, please click to check for updates."

msgid "The ctidb base upgrade package has been updated."
msgstr "The intelligence package has been updated."

#, python-brace-format
msgid "The current available quota for the cluster is {0}"
msgstr ""

msgid ""
"The current deployment mode does not support the import of the selected site"
" configuration"
msgstr ""

msgid ""
"The current node hardware type is {}, and the upgrade package type is {}, "
"which does not match."
msgstr ""

msgid ""
"The current rule will not take effect: Key + counter combination needs to be"
" configured in any rule"
msgstr ""
"The current rule cannot take effect unless the selected Key + Counter is "
"configured in any scenario."

msgid ""
"The data collection inverval should be a number between 1 and 1440(60*24)!"
msgstr ""

msgid "The data collection selection is overflow!"
msgstr ""

msgid ""
"The data has been changed. Please confirm the switch because the edited data"
" will be lost after the switch!"
msgstr ""

msgid "The decompressed file cannot exceed 500MB"
msgstr ""

msgid ""
"The default setting is highly compatible (this configuration does not "
"support IE6. To support IE6, manually add the SSLv3 protocol). If safety is "
"paramount concern, use the following settings:"
msgstr ""
"The default setting has good compatibility (To support IE, add SSLv3 "
"protocol). For maximum security, use the following settings:"

msgid ""
"The defining characteristic of Sniping is an action undertaken at the latest"
" opportunity to achieve a particular objective, leaving insufficient time "
"for another user to bid/offer. Sniping can also be the automated "
"exploitation of system latencies in the form of timing attacks. Careful "
"timing and prompt action are necessary parts. It is most well known as "
"auction sniping, but the same threat event can be used in other types of "
"applications. Sniping normally leads to some disbenefit for other users, and"
" sometimes that might be considered a form of denial of service. In "
"contrast, OAT-005 Scalping is the acquisition of limited availability of "
"sought-after goods or services, and OAT-006 Expediting is the general "
"hastening of progress."
msgstr ""

msgid "The deploy mode of the imported file is not supported"
msgstr ""

#, python-brace-format
msgid "The depth of the path has exceeded {0}"
msgstr ""

msgid "The destination network IP is incorrect"
msgstr ""

msgid ""
"The detection direction({}) and detection({}) position of the custom rule "
"are inconsistent"
msgstr ""

msgid ""
"The dynamic good bot cache is full. The function of auto detect good bot is "
"shut down."
msgstr ""

msgid "The ecrypt password don't match with the salt!"
msgstr ""

msgid "The empty function only allows known name."
msgstr ""

msgid "The empty function only allows one input parameter."
msgstr ""

msgid "The end period cannot be earlier than the start period"
msgstr ""

msgid "The field name should be on the left of the comparison operator!"
msgstr ""

msgid ""
"The field name should be to the left of the operator, and the field value "
"should be to the right of the operator. For example, input_all_count >= 10."
msgstr ""

msgid "The field of {} only allows false/true."
msgstr ""

#, python-format
msgid ""
"The file cannot be empty, and the size cannot exceed 1MB. A maximum of "
"%(maxCount)s files can be uploaded."
msgstr ""

msgid ""
"The file cannot be empty, and the size cannot exceed 1MB. A maximum of 50 "
"files can be uploaded. "
msgstr ""
"Any file that is empty or bigger than 1 MB is not allowed. 50 files can be "
"uploaded at most."

msgid "The file contains rules in invalid format."
msgstr ""

msgid "The file is big more than 100M."
msgstr ""

msgid "The file is empty"
msgstr ""

msgid "The file is too big."
msgstr ""

#, python-brace-format
msgid "The file to be imported must not exceed {0}."
msgstr ""

msgid ""
"The filename extension can only contain letters, numbers, underscores (_), "
"and dashes (-)."
msgstr ""

msgid "The filled data format is incorrect"
msgstr ""

msgid "The filters cannot exceed 1024 characters!"
msgstr ""

msgid "The first parameter of function {} must be the name."
msgstr ""

msgid ""
"The first three DNS IPs will be selected automatically if more than three "
"are provided."
msgstr ""

msgid "The following domain names are restricted to users in some regions."
msgstr "The following Hosts are restricted to users in some regions."

msgid "The following information shows details of network adapters."
msgstr ""

msgid ""
"The following list shows the operators supported by each type of fields."
msgstr ""

msgid "The following path will be added to the URL of Internal Resources."
msgstr ""
"Enter the path under which the system stores internal assets that are "
"supposed to be requested by clients."

msgid ""
"The following template IDs can be used in the configuration of [Execution "
"Strategy] and [Challenge Policy]. Multiple template IDs can be configured, "
"separated by commas (,). For example: 3001,3002."
msgstr ""
"The following template IDs can be used in the configuration of Programmable "
"Defending Policies. Multiple template IDs can be configured, separated by "
"commas (,). For example: 3001,3002."

msgid ""
"The following weak passwords are for reference only. Please modify them "
"based on the actual business specific password algorithm, otherwise you will"
" not be able to hit."
msgstr ""

msgid "The format of menu_access_right is wrong."
msgstr ""

msgid "The host is not in the list of protected websites"
msgstr ""

msgid "The image size cannot exceed 1M"
msgstr ""

msgid ""
"The imported Programmable Defending settings will overwrite existing "
"settings (Resource files needs to be downloaded and uploaded separately). "
"Are you sure to import?"
msgstr ""

msgid "The imported file is changed, please import again"
msgstr ""

msgid "The indicator is \"{}\", The trigger is \"{}{}\""
msgstr "The indicator is \"{}\" and the trigger is \"{} {}\""

msgid "The input cannot be empty!"
msgstr ""

msgid "The input has exceeded 500 bytes!"
msgstr ""

msgid "The input of charset cannot be blank."
msgstr "The input of charset is required."

msgid "The input of charset has exceeded the limit of 28 characters."
msgstr ""

msgid ""
"The input parameters of the network layer access control rule are incorrect."
msgstr ""

msgid "The input value is empty"
msgstr ""

msgid "The interface IP version is not match"
msgstr ""

msgid ""
"The interface settings are successful, but cluster synchronization may take "
"time. If it does not take effect, please manually refresh the interface"
msgstr ""

msgid ""
"The interface that can query PIIs or perform sensitive operations can be "
"accessed without authentication. Attackers can access the interface without "
"logging in to obtain a large amount of PIIs or perform sensitive operations."
msgstr "Sensitive data or interface may be accessed by unauthorized clients."

msgid ""
"The interface that can query PIIs or perform sensitive operations needs to "
"verify the identity of the visitor to prevent those who have not passed the "
"identity verification from accessing such interfaces; for interfaces that "
"have not been modified in time, it is necessary to monitor the interface "
"access to promptly discover the risk of abnormal operations through the "
"interface."
msgstr ""
"Enable identity authentication on APIs that return sensitive data or allow "
"sensitive activities."

msgid "The interval at which a new token would be generated."
msgstr ""

msgid "The interval should be a positive integer from 5 to 1440."
msgstr ""

msgid "The is_limited parameter is wrong."
msgstr ""

msgid ""
"The json format API parameter name can be at most {} levels, and each level "
"cannot be empty."
msgstr ""

msgid "The keywords are repeated"
msgstr ""

msgid "The language of this node does not match the cluster's"
msgstr ""

msgid "The layout of the imported file is not supported"
msgstr ""

msgid "The layout of this node does not match the cluster's"
msgstr ""

msgid "The length of each suffix cannot exceed 10 characters."
msgstr ""

msgid "The length of filenames should be less than 64 characters"
msgstr ""

msgid "The length of the String is limited to {}"
msgstr ""

msgid "The length of the String {} is limited to {}"
msgstr ""

msgid "The length of the String {} is out of range"
msgstr ""

msgid "The length of the comments should be less than 100 characters."
msgstr ""

#, python-brace-format
msgid "The length of the path has exceeded {0} characters"
msgstr ""

msgid "The length of the role name should be 4 to 64 characters."
msgstr ""

#, python-brace-format
msgid ""
"The length of the role name should be {0} to {1} characters. Letters, "
"numbers and underscores are allowed. Do Not start with an underscore."
msgstr ""

msgid "The length of the username should be 6 to 60 characters."
msgstr ""

msgid "The length of time from generation of a token to its expiration."
msgstr ""

#, python-brace-format
msgid "The length of {0} can not exceed {1} characters."
msgstr ""

msgid "The length of {} exceeds {}."
msgstr ""

msgid "The length of {} should be 0"
msgstr ""

msgid ""
"The license does not include a salt value so the feature may not work as "
"expected."
msgstr ""

msgid "The license has been activated!"
msgstr "Activated license"

#, python-brace-format
msgid "The line More than {0}"
msgstr "The entry has more than {0} lines"

msgid "The line More than {}"
msgstr "The entry has more than {0} lines"

msgid "The list cannot be empty"
msgstr ""

#, python-format
msgid "The list has reached the limit of %(count)s items"
msgstr ""

msgid "The list resource file {} does not exist."
msgstr ""

msgid ""
"The management address has changed and the current session is no longer "
"available."
msgstr "eth0 has changed and the current session is no longer available."

msgid "The match content must be a string, and can't is empty string."
msgstr ""

msgid ""
"The max disk space you set for Log Analysis or Log Archiving exceeds the "
"upper limit."
msgstr ""

#, python-brace-format
msgid "The max number of whitelists cannot exceed {0}"
msgstr ""

#, python-brace-format
msgid ""
"The maximum number of registered apps allowed for the cluster is "
"({0}).Please delete some app(s) from the list of Registered Apps and try "
"again"
msgstr ""

#, python-brace-format
msgid "The maximum number of {0} has been reached {1}"
msgstr "The number of {0} has reached {1}."

msgid "The maximum size of the advanced filter is 2000."
msgstr ""

#, python-brace-format
msgid ""
"The memory of ({0}G) has exceeded the licensed limit ({1}G). Your system is "
"working under Transparent Mode. Please fix it and activate license again."
msgstr ""
"The memory of ({0}G) has exceeded the licensed limit ({1}G). Your system is "
"working in Pass-Through Mode. Please fix it."

#, python-brace-format
msgid "The memory size on this node has exceeded the upper limit: {0}G"
msgstr ""

msgid "The minimum of threat should be less than the maximum of threat"
msgstr ""

msgid ""
"The mobile phone can access the following URL or scan the QR code to obtain "
"the user login two-factor authentication code. If you have previously "
"entered, you do not need to re-enter"
msgstr ""

msgid "The mpp handshake path is too long"
msgstr "The mini-program handshake path is too long"

msgid "The name already exists. Please change a name."
msgstr ""

msgid ""
"The name of the uploaded file can only contain letters, numbers, "
"underscores, and decimal points, and the number of characters cannot exceed "
"64. It only support the UTF-8 encoding format"
msgstr ""
"Only UTF-8 encoded files are allowed. File name can contain up to 64 "
"characters, including letters, numbers, underscores and dots."

msgid ""
"The name should be composed with characters/numbers or _ and the length "
"should be less than 64 characters"
msgstr ""

msgid "The network adapter of this node does not match the cluster's"
msgstr ""

msgid "The network configuration information is incorrect, please check!"
msgstr ""

msgid "The new ctidb base upgrade package is not checked."
msgstr "The new update package is not checked."

msgid "The new ctidb has been uploaded. Are you sure to install?"
msgstr "The new package has been uploaded. Install now?"

msgid "The new llm corpus has been uploaded. Are you sure to install?"
msgstr ""

msgid "The new rule base upgrade package is not checked."
msgstr "Update packages are not found."

msgid "The new ruleset has been uploaded. Are you sure to install?"
msgstr "The new ruleset pacakge has been uploaded. Install now?"

#, python-brace-format
msgid ""
"The new site name {0} and naming type {1} conflict with the existing site. "
"Please reconfigure."
msgstr ""

msgid "The new submission is the same as the current one."
msgstr ""

msgid "The new version has been uploaded. Are you sure to install?"
msgstr "The new version has been uploaded. Install now?"

msgid ""
"The nodes that are not responsible for cluster management and "
"synchronization."
msgstr "Not responsible for cluster management and synchronization."

msgid "The number of Api Seq in List is more than 20"
msgstr ""

#, python-brace-format
msgid ""
"The number of CPU cores on this node has exceeded the upper limit: {0} cores"
msgstr ""

msgid "The number of CPU cores or memory size has exceeded the upper limit."
msgstr ""

#, python-brace-format
msgid "The number of DNS characters has exceeded the upper limit of {0}."
msgstr "The number of DNS characters has reached the upper limit {0}."

#, python-brace-format
msgid "The number of IPs entered exceeds the upper limit {0}"
msgstr ""

#, python-brace-format
msgid "The number of IPs is {0}, exceeding the maximum limit of {1}"
msgstr ""

msgid "The number of PIIs accessed by an IP in one day is greater than 1,000"
msgstr "Same IP accessed more than 1000 pieces of PII in a day."

msgid "The number of PhoneNumber entered has exceed the limit of 50."
msgstr ""

msgid ""
"The number of accesses by an IP outside the set working hours in one day is "
"greater than 100"
msgstr "Same IP called more than 100 times in off-hours."

msgid "The number of api customized rule conditions exceeds {}."
msgstr ""

msgid "The number of api customized rules exceeds {}."
msgstr ""

msgid "The number of business type rules exceeds maximum limit {}."
msgstr ""

msgid ""
"The number of characters before and after reserved is a non-negative "
"integer, The replacement character is an English alphabet or number or"
msgstr ""
"The numbers put in the boxes should be non-negative integers. The character "
"used to replace sensitive info could be from alphabet, numbers, and"

msgid ""
"The number of characters before and after reserved is a non-negative "
"integer."
msgstr ""

#, python-brace-format
msgid "The number of characters entered exceeds the upper limit {0}"
msgstr ""

#, python-brace-format
msgid ""
"The number of cpu core number ({0}) has exceeded the licensed limit ({1}). "
"Your system is working under Transparent Mode. Please fix it and activate "
"license again."
msgstr ""
"The number of cpu core number ({0}) has exceeded the licensed limit ({1}). "
"Your system is working in Pass-Through Mode. Please fix it."

msgid "The number of data entries in the file has exceeded 10,000"
msgstr ""

msgid "The number of data entries in the file has exceeded 100,000"
msgstr ""

#, python-brace-format
msgid "The number of domain entered exceeds the upper limit {0}"
msgstr ""

msgid "The number of e-mail addresses entered has exceed the limit of 50."
msgstr ""

#, python-brace-format
msgid ""
"The number of external big data analysis node IP has reached the upper limit"
" {0}"
msgstr ""

#, python-brace-format
msgid "The number of external query node IP has reached the upper limit {0}"
msgstr ""

msgid ""
"The number of failed login requests for an IP in one minute is greater than "
"10, and the number of unique accounts is greater than 10"
msgstr ""
"More than 10 accounts from Same IP failed to log in for more than 10 times "
"in a minute."

msgid ""
"The number of failed login requests for an account in one minute is greater "
"than 20"
msgstr "Same account failed to log in for more than 20 times in a minute."

#, python-brace-format
msgid ""
"The number of items in the current list after filtering is greater than "
"{export_limit} items!, continue to export {export_limit} items or cancel?"
msgstr ""

msgid ""
"The number of login requests for an IP in one minute is greater than 20, and"
" the number of unique accounts is greater than 20"
msgstr ""
"More than 20 accounts from Same IP failed to log in for more than 20 times "
"in a minute."

#, python-brace-format
msgid ""
"The number of module strategies exceeds {0}. You cannot create a new "
"strategy."
msgstr ""

#, python-brace-format
msgid ""
"The number of nodes in this cluster has exceeded the upper limit ({0})."
msgstr ""

#, python-brace-format
msgid "The number of nodes in this cluster has exceeded the upper limit: {0} "
msgstr ""

msgid "The number of obtained nodes is abnormal."
msgstr ""

#, python-brace-format
msgid "The number of path entered exceeds the upper limit {0}"
msgstr ""

#, python-brace-format
msgid "The number of ports entered exceeds the upper limit {0}"
msgstr ""

msgid "The number of ports exceeded 3000."
msgstr ""

#, python-brace-format
msgid ""
"The number of proxy nodes ({0}) in the cluster has exceeded the licensed "
"limit ({1}). Please delete some node(s) and try again."
msgstr ""

msgid "The number of regular expression rule exceeds maximum limit {}."
msgstr ""

#, python-brace-format
msgid "The number of request body entered exceeds the upper limit {0}"
msgstr ""

msgid "The number of resource files has reached the limit of 200."
msgstr ""

msgid "The number of resource waf files has reached the limit of 50."
msgstr ""

msgid "The number of roles has exceeded 100."
msgstr ""

#, python-brace-format
msgid "The number of rule exceeds {0}. You cannot create a new rule."
msgstr ""

msgid "The number of rules exceeds maximum limit."
msgstr ""

msgid "The number of rules is more than 100."
msgstr ""

#, python-brace-format
msgid "The number of rules is more than {0}."
msgstr ""

#, python-brace-format
msgid ""
"The number of site strategies exceeds {0}. You cannot create a new strategy."
msgstr ""

#, python-brace-format
msgid ""
"The number of strategies exceeds {0}. You cannot create a new strategy."
msgstr ""

#, python-brace-format
msgid "The number of string in host entered exceeds the upper limit {0}"
msgstr ""

#, python-brace-format
msgid "The number of string in path entered exceeds the upper limit {0}"
msgstr ""

msgid ""
"The number of unique IPs used by an account per day is greater than 100"
msgstr "Same account called from more than 100 IPs in a day."

msgid "The number of unique accounts used by an IP per day is greater than 20"
msgstr "More than 20 accounts called from same IP in a day."

msgid ""
"The number of unique accounts used by an overseas IP in one day is greater "
"than 5"
msgstr "More than 5 accounts called from same overseas IP in a day."

#, python-brace-format
msgid ""
"The number of upstreams ({0}) has exceeded the licensed limit ({1}). Please "
"delete some upstream(s) and try again."
msgstr ""

msgid "The number of users has exceeded 100."
msgstr ""

msgid ""
"The number of verification code requests for an IP in one minute is greater "
"than 20"
msgstr "Same IP sent more than 20 requests in a minute."

msgid ""
"The number of weak password characters has exceeded the maximum of 10,000."
msgstr ""

#, python-brace-format
msgid ""
"The number of weak password characters has exceeded the maximum of {0}."
msgstr ""

msgid "The number of {} exceeds {}."
msgstr ""

msgid "The operator does not exist."
msgstr ""

msgid "The operator of Enumeration only allows ==/~="
msgstr ""

msgid "The password has expired. Please reset one."
msgstr ""

msgid ""
"The password must have at least 8 characters, and should contain at least "
"three of the four types: uppercase letters, lowercase letters, numbers and "
"special characters"
msgstr ""

msgid "The path depth has exceeded the limit depth of 100"
msgstr ""

msgid "The path entered should not exceed 512 characters."
msgstr ""

msgid ""
"The path length cannot be larger than 128 characters, the path cannot be "
"blank or /"
msgstr ""

msgid "The path of AI-WAF request whitelist cannot be blank."
msgstr "The path of AI-WAF Whitelist is required."

msgid "The path of access white path cannot be blank."
msgstr "The path of access white path is required."

msgid "The path only supports letters, numbers, and special characters -/_."
msgstr ""

msgid "The pii builtin content, cannot modified it."
msgstr ""

msgid "The pii {} is not valid."
msgstr ""

msgid "The plug-in is not functioning properly."
msgstr ""

msgid "The policy does not exist, Please refresh the page."
msgstr ""

msgid "The precent of mismatch version count(mvc)"
msgstr ""

msgid "The precent of mismatch version(ds_mismatch_config)"
msgstr ""

msgid "The protection list data is incomplete"
msgstr ""

msgid ""
"The protection list to be edited does not exist. Please refresh the page and"
" try again."
msgstr ""

msgid ""
"The protocol for \"Website\" should be as same as the original one used for "
"your website."
msgstr ""

msgid ""
"The purpose of uploading the mini program SDK here is to upgrade the SDK. "
"The extension of the uploaded file must be.bin. After uploading, you need to"
" download the SDK again in the following mini program list and integrate it "
"into the corresponding mini program."
msgstr ""
"Upload a new mini-program SDK for upgrading purpose. It must be a BIN file. "
"After uploading, you must download the SDK again in the following Mini-"
"Program List and integrate it into the corresponding mini-program."

msgid ""
"The query parameters related to the return quantity are found in the "
"interface request. By modifying the parameters, a large amount of PIIs can "
"be obtained at a time."
msgstr ""
"Parameters in requests can be altered to make servers return corresponding "
"amount of data."

msgid "The quota parameter is wrong."
msgstr ""

msgid ""
"The range of attenuation value is limited to 0.1 to 100, accurate to one "
"decimal place."
msgstr ""
"Fading speed is a number accurate to 1 decimal place from the range of 0.1 -"
" 100."

msgid "The recommended protocol for \"Server\" is HTTP."
msgstr ""

msgid "The regex is not valid."
msgstr ""

msgid "The register_apps is empty!"
msgstr ""

msgid ""
"The relationship between the filter and the rule triggers is And, which "
"means both of the two conditions must be met to trigger an execution "
"strategy."
msgstr ""
"The logical relationship between the filter and the rule trigger is AND, "
"which means both conditions must be met to initiate the actions"

msgid "The replacement character is an English alphabet or number or"
msgstr ""

msgid ""
"The request header value length detection in this configuration item does "
"not include separately configured request headers."
msgstr ""
"This option is not effective to headers that have options of their own in "
"this dialog."

msgid "The request parameter information is incorrect."
msgstr ""

msgid "The reserved quota has exceeded limit."
msgstr ""

msgid "The resource file does not exist. Please try it again after reload it"
msgstr ""

msgid ""
"The resource waf file does not exist. Please try it again after reload it"
msgstr ""

msgid ""
"The responses with any of the keywords below will be blocked. (Do not enter "
"<span class='notice'>&apos;&nbsp;;&nbsp;&lt;&nbsp;&gt;</span>)"
msgstr ""

msgid "The result of saving the configuration is abnormal."
msgstr ""

msgid "The role name already exists"
msgstr ""

msgid "The role name is reserved"
msgstr ""

msgid "The rule base server is abnormal."
msgstr ""

msgid ""
"The rule base upgrade package does not exist, please click to check for "
"updates."
msgstr ""

msgid "The rule base upgrade package has been updated."
msgstr "Updated successfully."

#, python-brace-format
msgid ""
"The rule cannot be deleted as it is currently bonded to {0} {1}: <br/>{2}"
msgstr ""

msgid "The rule does not exist"
msgstr ""

msgid "The rule filter number shall never exceed the maximum: 10"
msgstr ""

msgid ""
"The rules created here are used to filter traffic based on elements such "
"source/destination IPs, destination ports and domain names. The system "
"compares each request with the rules one by one from top to bottom. If a "
"request hits any single rule, the system executes the action (either Capture"
" or Do not capture) of that rule and stops going further to the rest rules. "
"Those requests do not hit any rule will be captured anyway."
msgstr ""
"The rules created here are used to filter traffic based on elements such "
"source/destination IPs, destination ports and domain names. The system "
"compares each request with the rules one by one from top to bottom. If a "
"request hits any single rule, the system executes the action (either Capture"
" or Do not capture) of that rule and stops going further to the rest of the "
"rules. Those requests that do not hit any rule will be captured anyway."

msgid "The salt is malformed"
msgstr ""

msgid "The salt is malformed!"
msgstr ""

msgid "The same IP already exists"
msgstr ""

msgid ""
"The same name has already existed in configuration or the same path and same"
" method and same specify parameter have already existed in the domain name."
msgstr ""

msgid "The same path does not support different MIME types."
msgstr ""

msgid "The same rules cannot be repeated in the expression."
msgstr ""

msgid "The second parameter of function {} must be the string."
msgstr ""

msgid ""
"The server returned data is incomplete and may cause the page operation to "
"not take effect. Please try this feature later."
msgstr ""

msgid "The session has expired. Continue editing after login."
msgstr "The session has expired. Please log in again to proceed."

msgid ""
"The site configuration has been disabled. You can go to the [Websites] page "
"to enable it."
msgstr ""

msgid "The site does not exist."
msgstr ""

msgid "The site does not exist. Please refresh the page and try again."
msgstr ""

msgid "The site was used by these Alipay mini program protection, as follow: "
msgstr "Sites used by Alipay Mini-Program protection are as follows: "

msgid "The site was used by these Mobile mini program protection, as follow: "
msgstr "Sites used by ChinaMobile Mini-Program protection are as follows: "

msgid "The site was used by these wechat app, as follow: "
msgstr "Sites used by WeChat Mini-Program protection are as follows: "

msgid "The size of a single range interval."
msgstr "Enter the maximum value allowed for a Range."

msgid "The size of folder has reached the limit of 200M."
msgstr ""

msgid "The size of industry data custom tags is more than {} KB"
msgstr ""

msgid "The size should be a positive integer smaller than 1024 MB"
msgstr ""

msgid ""
"The source IP address and destination IP address can support a single IP and"
" a range, where the range uses CIDR notation."
msgstr ""

msgid "The specified version cannot be found"
msgstr ""

msgid "The start and end periods cannot be the same"
msgstr ""

msgid "The start and end validity periods cannot be the same"
msgstr ""

msgid "The start value should be lower than the end value."
msgstr ""

msgid "The statistics subsystem has been out of service"
msgstr "The analytics system has been out of service"

msgid "The statistics subsystem has expired"
msgstr "The analytics system has expired"

msgid "The statistics subsystem has expired. Please update license."
msgstr "The analytics system has expired. Please update license."

msgid "The sub-path between / cannot be empty"
msgstr ""

msgid ""
"The suffix name only supports letters and numbers, and multiple values are "
"separated by English commas."
msgstr ""

msgid "The system allows and protects selected HTTP request methods."
msgstr ""

msgid "The system allows selected WebDAV request methods"
msgstr ""

msgid ""
"The system blocks requests to the URLs that include any of the file "
"extensions entered below. Each extension should only consist of letters, "
"numbers, underscores or hyphens. Multiple extensions should be separated "
"with commas."
msgstr ""

msgid ""
"The system blocks requests to the URLs that include any of the filenames "
"entered below （characters ; ' < > are not allowed in filenames）. Multiple "
"filenames should be separated with commas."
msgstr ""

msgid "The system cannot be rolled back because of changed cluster status"
msgstr ""

msgid ""
"The system cannot be rolled back because of no previous version is found"
msgstr ""

msgid ""
"The system configuration cannot be imported because the cluster status is "
"abnormal. Please try again after the cluster is restored."
msgstr ""

msgid "The system has not been activated."
msgstr "Please upload a valid license file to activate the system."

msgid ""
"The system processes requests according to the following settings if it is "
"enabled."
msgstr ""

msgid ""
"The system processes responses according to the following settings if it is "
"enabled."
msgstr ""

#, python-brace-format
msgid ""
"The system service port is not configured with {0}, so {0} upstream is not "
"reachable. Please change the server IP to {1} to ensure the protection takes"
" effect"
msgstr ""

msgid ""
"The system will treat the blacklist prompts as malicious content when "
"conducting risk assessment of prompt injection protections."
msgstr ""

msgid ""
"The system will treat the whitelist prompts as safe content when conducting "
"risk assessment of prompt injection protections."
msgstr ""

msgid ""
"The system works as a detection service center and provides feedback to the "
"plugin."
msgstr ""

msgid "The task of creating cluster already exists."
msgstr ""

msgid "The template has been restored to the original package."
msgstr ""

msgid ""
"The threat awareness reputation is automatically output to the specified "
"server port, using syslog UDP protocol or kafka protocol."
msgstr ""
"Transmit BTA reputations to a server automatically through syslog or Kafka."

msgid ""
"The timeout (in seconds) of establishing connection between the DAP system "
"and the protected server,usually no longer than 75 seconds."
msgstr ""
"Set a timeout (seconds) for establishing a connection between the protection"
" system and the upstream server, usually no longer than 75 seconds."

msgid ""
"The timeout (in seconds) of reading a response from the protected server. "
"The timeout is set only between two successive read operations, not for the "
"whole response. If the protected server does not transmit anything within "
"this time, the connection will be closed."
msgstr ""
"Set a timeout (seconds) for the protection system to receive a response from"
" the upstream server. Two consecutive timeouts mean closed connection."

msgid ""
"The timeout (in seconds) of transmitting a request to the protected server. "
"The timeout is set only between two successive write operations, not for the"
" whole request. If the protected server does not receive anything within "
"this time, the connection will be closed."
msgstr ""
"Set a timeout (seconds) for the protection system to send a request to the "
"upstream server. Two consecutive timeouts mean closed connection."

msgid "The timeout period must be less than the validity period"
msgstr ""

msgid "The topic length cannot exceed 249."
msgstr ""

msgid ""
"The total length of the URL protection list of the mobile certificate cannot"
" exceed {} characters."
msgstr ""

msgid ""
"The total size of Ajax-related configurations (Ajax/Fetch Request URL Token,"
" Obfuscate Ajax/Fetch Request, Obfuscate Ajax/Fetch Response) have exceeded "
"50KB, which will affect system performance."
msgstr ""

msgid "The total size of all protection lists has exceeded 100M"
msgstr ""

msgid "The type, field and operator of the filter cant be empty"
msgstr ""

msgid "The uploaded MPP SDK does not match the system version"
msgstr ""

msgid "The uploaded error page template carries an invalid suffix name."
msgstr ""

msgid "The user does not exist."
msgstr ""

msgid ""
"The user name parameter and password parameter are used to specify the "
"statistical dimensions of brute force cracking. Both are optional, including"
" the following four cases:"
msgstr "Username and password parameters are optional settings.<br/>Notes:"

msgid "The user-defined HTTP header name includes invalid character(s)."
msgstr ""

msgid "The user-defined HTTP request header name cannot be blank."
msgstr "The user-defined HTTP request header name is required."

msgid ""
"The user-defined HTTP request header name has exceeded the limit of 128 "
"characters."
msgstr ""

msgid ""
"The user-defined HTTP request header value has exceeded the limit of 128 "
"characters."
msgstr ""

msgid "The user-defined HTTP response header name cannot be blank."
msgstr "The user-defined HTTP response header name is required."

msgid ""
"The user-defined HTTP response header name has exceeded the limit of 128 "
"characters."
msgstr ""

msgid ""
"The user-defined HTTP response header value has exceeded the limit of 128 "
"characters."
msgstr ""

msgid "The username should not start with API_ or api_."
msgstr ""

#, python-brace-format
msgid "The value cannot exceed {0} characters"
msgstr ""

msgid "The value entered exceeds limit."
msgstr ""

#, python-brace-format
msgid ""
"The value entered must be an integer and the period cannot exceed {0} years."
msgstr ""

msgid "The value length of mobile part config cannot exceed {} characters!"
msgstr ""

msgid "The value of detect direction cannot be modified"
msgstr ""

msgid "The value of tag cannot contain commas"
msgstr ""

msgid "The value range is "
msgstr ""

msgid "The version of the imported file is not supported"
msgstr ""

msgid "The version of this node does not match the cluster's"
msgstr ""

msgid ""
"The waiting time between the request and the response is collected. The "
"timeout will set the response code to 555(Applicable to WebSocket too)"
msgstr ""
"The waiting time between capturing the HTTP request and HTTP response. "
"Range: 1-60. Status code 555 will be returned for timeout.(Applicable to "
"WebSocket too)"

msgid "The website already exists!"
msgstr ""

msgid ""
"The website you are adding already exists in the system. Are you sure you "
"want to overwrite it?"
msgstr ""

msgid "The whitelist rule exists."
msgstr ""

#, python-brace-format
msgid ""
"The {0} you configured does not exist in the system, please close to re-"
"select or confirm to create the policy by default in the system?"
msgstr ""

#, python-brace-format
msgid ""
"The {0} you configured does not exist in the system, please close to re-"
"select."
msgstr ""

msgid ""
"There are <span name=\"cluster_node_count\"></span> nodes in current "
"cluster, including <span name=\"cluster_manage_count\"></span> Master "
"Node(s)."
msgstr ""

msgid "There are already {} custom pii types, please refresh the page."
msgstr ""

msgid "There are duplicate configuration items in Http Forward List."
msgstr "Duplicates are found."

msgid ""
"There are duplicate configuration items in the Ajax/Fetch Referer List."
msgstr "Duplicates are found."

msgid ""
"There are duplicate configuration items in the Ajax/Fetch request body "
"encryption list."
msgstr "Duplicates are found."

msgid ""
"There are duplicate configuration items in the Ajax/Fetch response "
"encryption list."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the CSRF Path List."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the WAF white list."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the custom field."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the encapsulation list."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the request white list."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the response white list."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in the verification list."
msgstr ""

#, python-brace-format
msgid "There are duplicate configuration items in the {0}."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items in websocket path."
msgstr "Duplicates are found."

msgid "There are duplicate configuration items with in cookie_names."
msgstr "Duplicates are found."

#, python-brace-format
msgid "There are duplicate configuration items with row {0}."
msgstr "Duplicates are found."

msgid "There are duplicate configuration."
msgstr "Duplicates are found."

#, python-brace-format
msgid "There are duplicate sensitiveItems:{0}"
msgstr "Duplicates are found."

msgid "There are duplicate wechat appid"
msgstr "Duplicates are found."

#, python-brace-format
msgid "There are duplicate {0} in the ids."
msgstr "Duplicates are found."

msgid ""
"There are no obvious attack characteristics, but the analysis of the "
"request's access characteristics of the entire session indicates it is an "
"abnormal session."
msgstr ""

msgid ""
"There are no system service interface nodes in the cluster. You can go to "
"[System Overview] to dynamically add system service interface roles for "
"nodes."
msgstr ""

msgid ""
"There are not available sites, you should enable wechat app protection in "
"[proxy setting]"
msgstr ""

msgid ""
"There are strategy that do not exist in the configuration. Please refresh "
"and try again."
msgstr ""

msgid "There are three roles that a network adapter could play:"
msgstr ""

msgid ""
"There are two methods can be used to encrypt request paths and parameters: "
"tag-based and path-based method. <span class='notice'>Path-based method has "
"a higher priority </span>"
msgstr ""
"There are two methods can be used to encrypt request paths and parameters: "
"tag-based and path-based method. <span class='notice'>Path-based method has "
"a higher priority </span>"

msgid ""
"There are weak passwords in the interface request, and weak passwords may be"
" cracked by account brute force."
msgstr "The passwords included in requests are found easy to be cracked."

msgid ""
"There are websites that do not exist in the configuration. Please refresh "
"and try again."
msgstr ""

msgid "There is a duplicate ID in the import file, such as ID"
msgstr ""

msgid "There is no log analysis p node (query) online!"
msgstr ""

msgid "There is no log analysis s node (query) online!"
msgstr "There is no Big Data Analysis S (Query) node online!"

msgid ""
"There should be a least one Master Node in a cluster. An odd number of "
"Master Nodes, e.g. 3 or 5 is highly recommended to ensure cluster delivers "
"service with stability."
msgstr ""
"There should be a least one Master Node in a cluster. An odd number of "
"Master Nodes, e.g. 1 or 3 is highly recommended to ensure cluster stability."

msgid ""
"There will be no API Monitor role in the cluster, and the API protection "
"functions will not be available."
msgstr ""
"There will be no API Monitor role in the cluster, and the API protection "
"functions will not be available. "

msgid "These requests were organized by user ID."
msgstr ""

msgid "Third Party Reputation Import"
msgstr "Import Third-Party Reputations"

msgid "This Node Only"
msgstr ""

msgid ""
"This account expires at the time specified. Check the box to disable account"
" expiration."
msgstr ""
"This account expires on the date specified. Check the box to disable account"
" expiration."

msgid ""
"This change would automatically disable \"X-Real-IP\" and \"X-Forwarded-"
"For\" in \"HTTP Request Headers\" for the following sites:"
msgstr ""

msgid "This configuration already exists in this website."
msgstr ""

msgid ""
"This configuration item is a global rule, which can only be edited and "
"applied in the global user-defined rules on the WAF>Advanced Protection "
"page."
msgstr ""
"The global custom rules created in WAF > Global Custom Rules and Applied on "
"current website."

msgid ""
"This feature automatically learn patterns that indicate the order of APIs "
"being requested. It provides users learning outputs to help them create "
"rules to protect APIs."
msgstr ""
"When it is enabled, the system uses an AI-powered module to learn the "
"patterns of API calling sequences based on a large number of API requests. "
"The learning results can help improve API security policies."

msgid ""
"This feature enables the intelligent merging of similar APIs into an API "
"template."
msgstr ""

msgid "This feature is disabled in the current deployment mode"
msgstr ""

msgid ""
"This feature is only effective under Block Mode. Enabling it will force WAF "
"to work under Monitor Mode. This mode will not block abnormal requests, but "
"keep records in Statistics."
msgstr ""
"This feature is used to put WAF under Monitor Mode separately when the whole"
" system is working under Block Mode. If enabled, WAF takes no actions on "
"abnormal requests but keeps records in Analytics."

msgid ""
"This feature is used to deny HTTP requests on network switches by using TCP "
"Reset technology. However, it is not guaranteed that the technology can "
"always work as expected owing to some limitations of itself. If the feature "
"is enabled, the system places abnormal requests source IPs in a blacklist "
"and initiates TCP Reset so that the requests come from the same source "
"afterwards cannot reach servers. You can specify how long source IPs are "
"kept in the blacklist."
msgstr ""

msgid ""
"This feature requires activation of the authorization before it can be "
"enabled."
msgstr ""

msgid "This feature requires license authorization before it can be enabled."
msgstr ""

msgid ""
"This feature works only in block mode. When it is enabled, AI-WAF only "
"monitors illegal requests and logs them to statistics, but does not block "
"them."
msgstr ""
"This feature is used to put AI-WAF under Monitor Mode separately when the "
"whole system is working under Block Mode. If enabled, AI-WAF takes no "
"actions on abnormal requests but keeps records in Analytics."

msgid ""
"This function is unavailable since LLM Service is not enabled in the "
"cluster."
msgstr ""

msgid ""
"This function is unavailable since no LLM package is detected. Please go to "
"the LLM Service node to upload the package."
msgstr ""

msgid ""
"This function is unavailable since no LLM package is detected. Please upload"
" the package."
msgstr ""

msgid ""
"This help page will provide users with a quick and easy configuration guide "
"to achieve DAP system’s protection coverage on customers’ websites through a"
" few steps."
msgstr ""
"This help page will provide users with a quick and easy configuration guide "
"to achieve protection coverage on customers’ websites through a few steps."

msgid "This icon will be displayed on browser tabs"
msgstr ""

msgid ""
"This image will be used as the background of the navigation bar logo on API "
"Monitor page"
msgstr ""

msgid ""
"This is not a Proxy node, so it cannot check the status of the server(s)."
msgstr ""

msgid "This logo will be dispalyed when the main navigation bar collapses"
msgstr ""

msgid "This logo will be displayed on the main login page"
msgstr ""

msgid ""
"This logo will be displayed on the main navigation bar, API Monitor's login "
"page and navigation bar"
msgstr ""

msgid "This month"
msgstr "This Month"

msgid "This node cannot communicate with group."
msgstr "This node has failed to communicate with the cluster."

msgid "This node is being upgraded."
msgstr "This node is being updated."

msgid "This node is rolling back."
msgstr ""

msgid ""
"This page sorts and organizes WAF attack requests based on site, URL, and "
"rule ID. You can click Details to view the corresponding client IP "
"statistics and payload statistics, For reference by implementers and add WAF"
" whitelist."
msgstr ""
"WAF events are listed below with host, path, and WAF rule ID. You can click "
"Details to view the corresponding client IP and payload information as a "
"reference before you add any entris to WAF whitelist."

msgid ""
"This policy is only for invalid response policy processing in the request "
"direction, not applicable to the response direction."
msgstr "Select an action as the response to invalid requests."

msgid ""
"This role has been deleted. Refresh the page to view roles currently exist."
msgstr ""

msgid "This server is not the Proxy server"
msgstr ""

msgid "This server is used to authenticate accounts logging in remotely."
msgstr ""
"This server is used to remotely authenticate login accounts of the "
"WebConsole."

msgid ""
"This site is currently disabled and cannot be modified via this site editor."
msgstr ""

msgid ""
"This type of deployment does not require any change in existing forwarding "
"rules, network topology or the source IPs in network packages, so it is "
"invisible to other devices within the network."
msgstr ""
"Applicable to scenarios where network topology and requests/responses are "
"not expected to be modified."

msgid ""
"This warning will no longer be reminded after clicking (only the warning "
"status is removed, the problem may still exist)."
msgstr ""

msgid "This week"
msgstr "This Week"

msgid "Threat Awareness Reputation Automatic Output"
msgstr "Auto Transmit BTA Reputations"

msgid "Threat Awareness Reputation Export"
msgstr "Export BTA Reputations"

msgid "Threat Impact"
msgstr ""

msgid "Threat Intelligence"
msgstr ""

msgid "Threat Intelligence Config"
msgstr "Settings"

msgid "Threat Intelligence Service"
msgstr "Reputation Service"

msgid "Threat Intelligence Switch"
msgstr ""

msgid "Threat Intelligence White List"
msgstr "Threat Intelligence Whitelist"

msgid "Threat Intelligence whitelist update"
msgstr "Updated Threat Intelligence Whitelist"

msgid "Threat Intent"
msgstr ""

msgid "Threat Location"
msgstr ""

msgid "Threat Payload"
msgstr ""

msgid "Threat Suggest"
msgstr ""

msgid "Threat Type"
msgstr ""

msgid "Threat Value"
msgstr "Threat Score"

#, python-brace-format
msgid "Threat intelligence feature: {0}"
msgstr "Threat Intelligence feature: {0}"

msgid "Threat scoring model statistical common keys"
msgstr ""

msgid "Threat_Intelligence"
msgstr "Threat Intelligence"

msgid "Threshold"
msgstr "Threshold"

msgid "Threshold of Bot Identification"
msgstr "Bot Identification Threshold"

msgid "Thurs."
msgstr ""

msgid "Tile"
msgstr ""

msgid "Time"
msgstr ""

msgid "Time "
msgstr ""

msgid "Time Interval"
msgstr ""

msgid "Time Window"
msgstr ""

msgid "Time Window greater than or equal to 1 and less than or equal to 300."
msgstr ""

msgid "Time(s)"
msgstr "times"

msgid "Time(s)/120 Seconds"
msgstr ""

msgid "Timeout"
msgstr ""

msgid "TimeoutSec"
msgstr "Timeout"

msgid "Timer"
msgstr ""

msgid "Times Per Hour"
msgstr ""

msgid "Times per Second"
msgstr " times/second"

msgid "To"
msgstr ""

msgid "To Block"
msgstr ""

msgid "To Disabled"
msgstr ""

msgid "To Monitor"
msgstr ""

msgid "To Pass-Through"
msgstr ""

msgid ""
"To avoid important PIIs in cookies, relevant PIIs should be saved in "
"sessions or databases by the server."
msgstr ""
"Store sensitive information in sessions or databases instead of Cookies."

msgid ""
"To avoid interruption to applications, please make sure to import the "
"required resource files before importing this settings file."
msgstr ""

msgid "To be Confirmed"
msgstr "Unconfirmed"

msgid "To be Mitigated"
msgstr ""

msgid ""
"To collect, analyze, detect, score, and identify automated attack behaviors "
"on business data by using intelligent models. Complies with the OWASP "
"automated attack specification."
msgstr ""
"Business Threat Awareness: collecting business data and identifying "
"automated threats based data modeling."

msgid ""
"To continue the protection services, please contact us for a new license."
msgstr ""

msgid ""
"To create a standby node, the IP of management network adapter, as well as "
"the admin password of the primary node are required."
msgstr ""
"Enter the eth0 IPv4 address of the primary node and the admin password to "
"create a standby node."

msgid ""
"To enable this function,Dynamic Application Protection must be turned on."
msgstr ""
"To enable this function, Dynamic Application Protection must be turned on."

msgid "To enable this function,Flow collection must be turned on."
msgstr ""

msgid ""
"To modify the admin adapter IP, you need to enter the new IP addresses of "
"all master nodes in the cluster"
msgstr ""
"After modifying eth0's IP, you need to enter the new IP addresses of all "
"master nodes in the cluster"

msgid ""
"To restore the protection status, choose System > Network Settings and "
"manually disable bridge transparent transmission."
msgstr ""
"To restore the protection status, choose System > Network Settings and "
"manually disable bridge pass-through."

msgid "To upload an LLM package, please operate on the LLM Service node."
msgstr ""

msgid "Today"
msgstr ""

msgid "Token Cracking(OAT-002)"
msgstr ""

msgid "Token Exception Types"
msgstr ""

msgid "Token Reuse For"
msgstr ""

msgid "Token Scheme"
msgstr ""

msgid "Token Security Policy"
msgstr ""

msgid "Token validity of static resource requests will not be verified."
msgstr ""

msgid "TokenID|TokenValue|CreateTime|Permission|Operation"
msgstr ""

msgid "Too many shared dicts"
msgstr ""

msgid "Too old license file is not supported."
msgstr ""

msgid "Tool code feature"
msgstr "Bot Tools Recognition"

msgid "Topic cannot be empty."
msgstr "Topic is required."

msgid "Topic: letters, numbers, -, _, ."
msgstr ""

msgid "Total Requests"
msgstr "All Requests"

msgid "Total number of accounts collected, excluding duplicate accounts"
msgstr "Total number of accounts collected."

msgid "Total requests count(tc)"
msgstr ""

msgid "Traffic Filtering"
msgstr ""

msgid "Traffic Filtering is disabled"
msgstr ""

msgid "Traffic Filtering is enabled"
msgstr ""

msgid ""
"Traffic goes from private IPs to public IPs will not be collected if "
"enabled."
msgstr ""

msgid "Transaction Channel"
msgstr ""

msgid "Transaction Details"
msgstr ""

msgid "Transaction Serial Number"
msgstr ""

msgid "Transmit system settings regularly"
msgstr "Transmit System Settings"

msgid "Transmit system settings regularly execution record"
msgstr "Transmit Records"

msgid "Transparency"
msgstr ""

msgid "Transparent"
msgstr "Pass-Through"

msgid "Transparent Deployment"
msgstr "Transparent"

msgid "Transparent Mode"
msgstr "Pass-Through Mode"

msgid "Transparent transmission module Select at least one item"
msgstr ""

msgid ""
"Transparent: current request will not be blocked by RAS and the response "
"will not be encapsulated"
msgstr ""
"Pass-Through: do not intercept the request and do not encapsulate the "
"response"

msgid "Trap Server IP"
msgstr ""

msgid "Travel Permit to Taiwan"
msgstr "Taiwan Travel Permit"

msgid "Travel Permit to Taiwan for Chinese Mainland Residents"
msgstr "Taiwan Travel Permit for Chinese Mainland Residents"

msgid "Traversable Interface Parameters"
msgstr ""

msgid "Traversable Parameters"
msgstr "Iterable Parameter"

msgid ""
"Traversable parameters are found in the interface input parameters. "
"Attackers can use scripts to pull batches of data according to the traversal"
" characteristics of the parameters."
msgstr ""
"Iterable parameters are found in requests that have been accepted by APIs. "
"It may allow attackers fetch data in batch using scripts."

msgid "Trial"
msgstr ""

msgid "Trial Version"
msgstr ""

#, python-brace-format
msgid "Trial module \"{0}\" in license has expired"
msgstr ""

msgid "Trial-run Expression"
msgstr ""

msgid "Triggering Severity"
msgstr "Trigger Severity"

msgid "Triple Network Adapters"
msgstr ""

msgid "Trusted Sites"
msgstr "Trusted Sites"

msgid "Try to activate license successfully"
msgstr ""

msgid "Try to activate license unsuccessfully"
msgstr ""

msgid "Tues."
msgstr ""

msgid "Turn off API Intelligence Merge"
msgstr "Disabled API Intelligence Merge"

msgid "Turn off Threat Intelligence"
msgstr ""

msgid "Turn off automatic upgrade"
msgstr "Turn off auto update"

msgid "Turn on API Intelligence Merge"
msgstr "Enabled API Intelligence Merge"

msgid "Turn on Threat Intelligence"
msgstr "Enabled Threat Intelligence"

msgid "Twisted Pair Cable"
msgstr ""

msgid "Two configurations with empty paths are not allowed."
msgstr ""

msgid "Two or more physical network ports are required for this deployment."
msgstr ""

msgid "Two-factor Authentication"
msgstr "Two-Factor Authentication"

msgid "Two-factor Authentication Code"
msgstr "Two-Factor Authentication Code"

msgid "Two-factor authentication is off."
msgstr "Two-Factor Authentication is disabled"

msgid "Two-factor authentication is turned on."
msgstr "Two-Factor Authentication is enabled"

msgid "Type"
msgstr ""

msgid "Type of input({}) must be list"
msgstr ""

msgid "UA (User-Agent)"
msgstr ""

msgid "UA BOT requests count(ubc)"
msgstr ""

msgid "UA rule can not be empty"
msgstr ""

msgid "UBBv2"
msgstr "Programmable Defending"

msgid "UBBv2 advance rules"
msgstr "Manual Rule Editor"

msgid "UDP Flood Defense:"
msgstr "UDP Flood Protection"

msgid "UI Widget Configuration"
msgstr "UI Widgets"

msgid "UI event collection"
msgstr "UI-event data"

msgid "UI widget touch configuration cannot be blank!"
msgstr "UI widget touch is required."

msgid "UK Driver's License Number"
msgstr ""

msgid "UK Phone Number"
msgstr "UK Mobile Phone"

msgid "URL"
msgstr ""

msgid "URL Access Control"
msgstr ""

msgid "URL Confusion"
msgstr "URL Obfuscation"

msgid "URL Confusion Path"
msgstr "URL Obfuscation Path"

msgid "URL Extension"
msgstr ""

msgid "URL Keyword"
msgstr ""

msgid "URL Link"
msgstr ""

msgid "URL List"
msgstr ""

msgid "URL Token Error"
msgstr ""

msgid "URL White List"
msgstr "WAF Whitelist"

msgid "URL cannot be empty."
msgstr "URL is required."

msgid "URL contains authentication information"
msgstr "Authorization Data in URL"

msgid ""
"URL protection list means SDK only protects requests and responses in the "
"list. Domain names and IPs can be configured. Ports and paths are supported,"
" e.g., 127.0.0.1:8080、mytest.com:9001/test.asp. Regex, protocols and "
"parameters are not supported."
msgstr ""
"The SDK only protects requests and responses in the URL list below. You can "
"enter domain names or IPs followed by ports or paths, e.g. 127.0.0.1:8080 or"
" www.mytest.com:9001/test.asp. Regex, protocols and parameters are not "
"supported."

msgid ""
"URL tokens are added only for Ajax/Fetch requests at the specified path, and"
" no other paths are added."
msgstr ""
"The Ajax/Fetch requests on paths listed below will be attached with URL "
"tokens."

msgid ""
"URL tokens are not added for Ajax/Fetch requests with the specified path (to"
" prevent requests exceeding the allowed size of the network after adding "
"tokens), other paths are added by default."
msgstr ""
"The Ajax/Fetch requests on paths listed below will not be attached with URL "
"tokens."

msgid "URL:"
msgstr ""

msgid "URL: "
msgstr ""

msgid "US Individual Taxpayer Identification Number"
msgstr ""

msgid "US Landline/Phone Number"
msgstr "US Telephone/Mobile Phone"

msgid "US Social Security Number"
msgstr ""

msgid "Ui event count(uttc)"
msgstr ""

msgid "Ui touch short time count(uisc)"
msgstr ""

msgid "Ui touch short time(ds_ui_touch_short_time)"
msgstr ""

msgid "Ui touch without move count(uimc)"
msgstr ""

msgid "Ui touch without move(ds_ui_touch_without_move)"
msgstr ""

msgid "Unable to connect to big data analysis server, please try again later."
msgstr "Failed to connect to Big Data Analysis Service. Try again later."

msgid "Unable to get configurable menu"
msgstr ""

msgid "Unable to get role list"
msgstr ""

msgid "Unable to recover after deleted, are you sure to continue?"
msgstr "It cannot be restored after deleted. Proceed anyway?"

msgid "Uncategorized"
msgstr "Other Types"

msgid "Unchecked Command Executions"
msgstr "Arbitrary Command Execution"

msgid "Unchecked Database Queries"
msgstr "Arbitrary Database Query"

msgid "Unchecked File Downloads"
msgstr "File Download"

msgid "Unchecked SMS Interfaces"
msgstr "SMS Abuse"

msgid "Unclassified Crawlers"
msgstr "Other Types"

msgid "Uniform Social Credit Code"
msgstr "China Unified Social Credit Code"

msgid "UnionPay Debit Card"
msgstr ""

msgid "Unique paths count(pat)"
msgstr ""

msgid "Unknonw Field!"
msgstr ""

msgid "Unknown"
msgstr ""

msgid "Unknown Threat(RAT-050)"
msgstr ""

msgid "Unknown error"
msgstr ""

msgid "Unknown error."
msgstr ""

msgid "Unknown exception from server"
msgstr ""

msgid "Unknown exception in recovery report"
msgstr ""

msgid "Unlimit"
msgstr ""

msgid "Unlimited"
msgstr ""

msgid "Unlimited Login Rate"
msgstr "Unrestricted Login Rate"

msgid "Unlock user successfully"
msgstr ""

msgid "Unmatch UA requests count(umu)"
msgstr ""

msgid "Unrecognized variable {} on the right of operator!"
msgstr ""

msgid "Unrecovered"
msgstr ""

msgid "Unsupport key"
msgstr ""

msgid "Unsupported"
msgstr ""

msgid "Unsupported Resource type"
msgstr ""

msgid "Unsupported Resource waf type"
msgstr ""

#, python-brace-format
msgid "Unsupported license type {0}. Please contact us."
msgstr ""

#, python-brace-format
msgid "Unsupported license version: {0}. "
msgstr ""

msgid "Unsupported request method!"
msgstr ""

#, python-brace-format
msgid "Unsupported service type {0}. Please contact us."
msgstr ""

msgid ""
"Unsupported user-agent string(s) entered in Request White List.The "
"characters such as"
msgstr ""
"Unsupported user-agent string(s) entered in Request Whitelist.The characters"
" such as"

msgid "Up to 5 configurations can be added"
msgstr ""

msgid "Update"
msgstr ""

msgid "Update BTA threat model"
msgstr ""

msgid "Update BTA threat threshold"
msgstr ""

#, python-brace-format
msgid "Update Error Page Templates Settings: {0}"
msgstr "Updated error page template for code: {0}"

msgid "Update LLM prompt blacklist"
msgstr ""

msgid "Update LLM prompt whitelist"
msgstr ""

#, python-brace-format
msgid "Update Max Upload File Size Value: {0}"
msgstr "Changed Maximum File Size to: {0}"

msgid "Update Obtaining Source IP"
msgstr "Updated Obtain Source IP"

msgid "Update Time"
msgstr ""

msgid "Update resource file failed."
msgstr ""

msgid "Update resource file {}:{} failed."
msgstr ""

msgid "Update resource waf file failed."
msgstr ""

msgid "Update resource waf file {}:{} failed."
msgstr ""

msgid "Update succeeded."
msgstr "Updated successfully."

msgid "Update time"
msgstr "Update Time"

msgid "Update token exception."
msgstr ""

#, python-brace-format
msgid "Update token of {token_id} failed"
msgstr "Failed to update token of {token_id}"

#, python-brace-format
msgid "Update token of {token_id} successfully"
msgstr "Updated token of {token_id}"

msgid "Updating is in progress and might take longer than usual."
msgstr ""

msgid "Upgrade"
msgstr ""

#, python-brace-format
msgid "Upgrade LLM CORPUS to {new}."
msgstr "Upgrade LLM Corpus to {new}."

msgid "Upgrade Period:"
msgstr "Upgrade Every"

msgid "Upgrade Time:"
msgstr "Upgrade At"

#, python-brace-format
msgid "Upgrade WAF Ruleset to {new}."
msgstr ""

#, python-brace-format
msgid "Upgrade ctidb to {new}."
msgstr "Update intelligence to {new}."

msgid "Upgrade cycle"
msgstr "Update Every"

#, python-brace-format
msgid ""
"Upgrade cycle: Please enter a integral number greater than or equal to {0} "
"and less than or equal to {1}"
msgstr "Enter an integer between 1 to 30."

msgid "Upgrade site"
msgstr "Server"

#, python-brace-format
msgid "Upgrade threat intelligence db to {new}."
msgstr ""

msgid "Upgrade time"
msgstr "Start At"

msgid "Upgrade time: Please select a time"
msgstr ""

msgid "Upgrading"
msgstr ""

msgid "Upload"
msgstr ""

msgid "Upload APIs Auto-detection"
msgstr "Auto-Recognize Upload APIs"

msgid "Upload Date"
msgstr ""

msgid "Upload File Inflate"
msgstr ""

msgid "Upload File Management"
msgstr ""

msgid "Upload HTML file"
msgstr "Upload HTML File"

msgid "Upload JS file"
msgstr "Upload JS File"

msgid "Upload Origin SDK"
msgstr "Upload Original SDK"

msgid "Upload Template"
msgstr ""

msgid "Upload Template Package"
msgstr ""

#, python-brace-format
msgid "Upload a ({0}px * {1}px) icon image"
msgstr ""

#, python-brace-format
msgid "Upload a ({0}px * {1}px) png image"
msgstr ""

msgid "Upload certificate"
msgstr ""

#, python-brace-format
msgid "Upload error page template: {0}"
msgstr ""

msgid "Upload failed"
msgstr ""

msgid "Upload geolocation library."
msgstr "Geolib upload"

msgid "Upload good_bots file"
msgstr ""

msgid "Upload list file"
msgstr "Upload List File"

msgid "Upload reCaptcha template"
msgstr "Upload CAPTCHA templates"

msgid "Upload reputation file"
msgstr "From a File"

msgid "Upload success"
msgstr ""

msgid "Upload wsdl file"
msgstr "Upload WSDL File"

msgid "Upload xsd file"
msgstr "Upload XSD File"

#, python-brace-format
msgid "Upload {0}"
msgstr ""

msgid "Uploaded"
msgstr ""

msgid "Uploaded File Detection  Range (KB)"
msgstr "Upload File (KB)"

msgid "Uploaded empty file."
msgstr ""

msgid "Uploaded invalid database file"
msgstr "Invalid geolib file."

msgid "Uploading succeeded. Activate now?"
msgstr ""

msgid "Uploads Count"
msgstr ""

msgid "Uppercase Letters"
msgstr ""

#, python-brace-format
msgid "Upstream Config has been Exported: {upstream}"
msgstr "Exported website settings: {upstream}"

msgid "Upstream Extranet Detection Rate"
msgstr ""

msgid "Upstream Health Status"
msgstr "Upstream health status (single)"

msgid "Upstream Port"
msgstr ""

msgid "Upstream is not allowed to be set to loopback address."
msgstr ""

msgid "Url With Empty Referer"
msgstr "Allow Paths without Referers"

msgid ""
"Usage may resemble legitimate application usage, but leads to exhaustion of "
"resources such as file system, memory, processes, threads, CPU, and human or"
" financial resources. The resources might be related to web, application or "
"databases servers or other services supporting the application, such as "
"third party APIs, included third-party hosted content, or content delivery "
"networks (CDNs). The application may be affected as a whole, or the attack "
"may be against individual users such as account lockout. This ontology’s "
"scope excludes other forms of denial of service that a ect web applications,"
" namely HTTP Flood DoS (GET, POST, Header with/without TLS), HTTP Slow DoS, "
"IP layer 3 DoS, and TCP layer 4 DoS. Those protocol and lower layer aspects "
"are covered adequately in other taxonomies and lists."
msgstr ""

msgid "Usage of nginx CPU"
msgstr ""

msgid "Usage of nginx memory"
msgstr ""

msgid "Usage of partition root"
msgstr ""

msgid "Usage of partition var"
msgstr ""

msgid "Usage of system CPU"
msgstr ""

msgid "Usage of system memory"
msgstr ""

msgid "Use"
msgstr ""

msgid "Use All"
msgstr ""

msgid "Use China security standard certificate"
msgstr "GM Standard Certificate"

msgid "Use JS expressions to collect specific features from clients."
msgstr ""

msgid "Use POST"
msgstr ""

#, python-brace-format
msgid "Use Website Config Editor edit upstream {key} site configuration."
msgstr ""
"Use Website Configuration Editor to edit the configuration of site {key}."

msgid ""
"Use a strong signature algorithm to encrypt tokens. For existing APIs that "
"accept weak tokens, keep tracking them to discover requests with forged "
"tokens."
msgstr ""

msgid "Use advanced filter"
msgstr "Advanced Filter"

msgid "Use automation tools requests count(uac)"
msgstr ""

msgid "Use commas to separate multiple extensions (e.g., exe, php, html)"
msgstr ""

msgid ""
"Use commas to separate multiple sites. Regular expressions are not "
"supported."
msgstr "Use commas to separate multiple sites. Regex is not supported."

msgid "Use debug requests count(udc)"
msgstr ""

msgid "Use global configuration"
msgstr "Inherit from Global Settings"

msgid "Use international standard certificate"
msgstr "International Standard Certificate"

msgid "Use modification requests count(umc)"
msgstr ""

msgid "Use multiple user agent count(muac)"
msgstr ""

msgid "Use non-Chinese IP to log in to a business set as login"
msgstr "Use non-Chinese IP to log in."

msgid ""
"Use relational operators including and, or, not, and parentheses (). "
"Parentheses can be used in a nested structure."
msgstr ""

msgid ""
"Use signature algorithm to encrypt tokens. For existing APIs that accept "
"weak tokens, keep tracking them to discover requests with forged tokens."
msgstr ""

msgid "Use system-provided dual-open tools request count(sdoc)"
msgstr ""

msgid ""
"Use system-provided dual-open tools(ds_use_system_provided_dual_open_tool)"
msgstr ""

msgid "Use the new salt to reset the mobile certificate-related configuration"
msgstr ""

msgid "Use the parameter patterns on the right side to generate rules."
msgstr ""

msgid ""
"Use this button to reset new rules to their default actions. Note! This "
"button can be used only once!"
msgstr ""

msgid "Use uniDbg requests count(udfc)"
msgstr ""

msgid "Use uniDbg(ds_unidbg)"
msgstr ""

msgid "Use virsual environment tool(ds_use_virsual_environment_tool)"
msgstr ""

msgid "Use virsual environment tools request count(uvec)"
msgstr ""

msgid "Use virtual camera count(uvc)"
msgstr ""

msgid "Use virtual camera(ds_virtual_camera)"
msgstr ""

msgid "Use virtual location count(uvl)"
msgstr ""

msgid "Use virtual location(ds_virtual_location)"
msgstr ""

msgid "Use with caution"
msgstr "Caution"

msgid "Used Volumn"
msgstr ""

msgid "Used automation tools(ds_use_automation_tool)"
msgstr ""

msgid "Used client proxy(ds_client_proxy)"
msgstr ""

msgid "Used debug tool(ds_use_debug_tool)"
msgstr ""

msgid "Used emulator(ds_emulator)"
msgstr ""

msgid "Used modification tools(ds_use_modify_tool)"
msgstr ""

msgid "Used root device(ds_root)"
msgstr ""

msgid "User"
msgstr ""

msgid "User Agent cannot be blank"
msgstr "User Agent is required."

msgid "User Behavior Rules"
msgstr ""

msgid "User Define"
msgstr "User Defined"

msgid "User List"
msgstr "Accounts"

msgid "User already exists!"
msgstr ""

msgid "User behavior rules."
msgstr ""

msgid "User count(btu)"
msgstr ""

msgid "User count(uuc)"
msgstr ""

msgid "User has been created: "
msgstr "Added a new user: "

msgid "User has been deleted already, please refresh this page again."
msgstr ""

msgid "User has been deleted: "
msgstr ""

msgid "User info has been saved:"
msgstr ""

msgid "User's unique identifier"
msgstr "User Identifier"

msgid "User-Agent"
msgstr ""

msgid "User-Agent Includes"
msgstr ""

msgid "Username"
msgstr ""

msgid "Username "
msgstr ""

msgid "Username doesn't exist."
msgstr ""

msgid ""
"Username exceeds limit or invalid user exists. Please refresh and try again"
msgstr ""

msgid "Username in Password"
msgstr ""

msgid "Username is required."
msgstr ""

msgid ""
"Username must contain only letters, numbers and underscores, and cannot "
"start with underscores!"
msgstr ""

msgid ""
"Username must contain only letters, numbers, dot, hyphen and underscores, "
"and cannot start with dot, hyphen or underscores!"
msgstr ""

msgid "Username parameter"
msgstr "Username Parameter"

msgid ""
"Users are only allowed to access static web pages (HTML, HTM) and resource "
"files in the following domain names, and the rest are blocked."
msgstr ""
"For the following Hosts (certain paths can be excluded), only static pages "
"(HTML and HTM) and resource files can be requested."

msgid ""
"Users are only allowed to access the GET and HEAD methods in the following "
"domain names."
msgstr "The following Hosts can only be accessed by GET and HEAD methods."

msgid "Using a separate network adapter (eth3) for HA heartbeat network port."
msgstr ""
"Using a separate network adapter (eth3) for transferring HA heartbeat "
"traffic."

msgid ""
"Using a separate network adapter (eth3) for transferring logs to Log "
"Archiving Node."
msgstr ""

msgid "VRID"
msgstr ""

msgid "VRID conflict"
msgstr ""

msgid "VRID range 1~255."
msgstr ""

msgid "VXLAN Analysis"
msgstr "VXLAN Parsing"

msgid "Validate and restrict user input or use a whitelist."
msgstr ""

msgid "Value"
msgstr ""

msgid "Value Range"
msgstr "Range of Value"

msgid "Value can not be empty"
msgstr ""

msgid "Value cannot be blank"
msgstr "Value is required."

msgid "Value is not empty when operation is empty!"
msgstr ""

msgid "Value must be between 1 and 99"
msgstr ""

msgid "Various data requiring permissions"
msgstr "Data requiring permissions"

msgid "Vehicle Frame Code"
msgstr "Vehicle Identification Number"

msgid "Vehicle Identification Code"
msgstr "Vehicle Identification Number"

msgid "Verification Code"
msgstr ""

msgid "Verification Code Acquisition"
msgstr "Get Verification Code"

msgid "Verification List cannot be blank!"
msgstr "Verification List is required."

msgid "Verification code brute force attacks"
msgstr "Verification Code Brute Force Attacks"

msgid "Verification code validation remaining time:"
msgstr ""

msgid "Verification is passed."
msgstr ""

msgid "Verification succeeded"
msgstr ""

msgid "Verify"
msgstr ""

msgid "Verify advanced operation code failed!"
msgstr ""

msgid "Verify failed, check your server time or setting NTP server."
msgstr ""

msgid "Verify license result: invalid license"
msgstr "License verification result: invalid license"

msgid "Verify license result: license expired"
msgstr "License verification result: license expired"

msgid "Verify license result: valid license"
msgstr "License verification result: valid license"

msgid "Verify sailfish license failed"
msgstr ""

msgid "Verify successfully!"
msgstr "Verified successfully"

msgid ""
"Verifying checksums and package names to prevent cracked Apps from accessing"
" servers."
msgstr ""
"Verify checksums and package names to prevent cracked apps from accessing "
"the servers."

msgid "Version"
msgstr ""

msgid "Version Mismatch."
msgstr ""

msgid "Version Update"
msgstr ""

msgid "Version number"
msgstr "Version"

msgid "View"
msgstr ""

msgid "View Detail"
msgstr ""

msgid "View Two-Factor Authentication Info"
msgstr ""

msgid "Virtual Camera"
msgstr ""

msgid "Virtual IP Address"
msgstr "Virtual IPv4 Address"

msgid "Virtual IP address only supports IPV4."
msgstr ""

msgid "Virtual IP prefix length"
msgstr "Virtual Prefix Length"

msgid ""
"Virtual IPs will be assigned only to external network adapter, with a VRID "
"ranging from 1 ~ 255."
msgstr ""
"The Virtual IP (VIP) will be assigned only to external network adapter, with"
" a VRID ranging from 1 ~ 255."

msgid "Virtual IPv6 Address"
msgstr ""

msgid "Virtual Location"
msgstr ""

msgid "Virtual Netmask"
msgstr ""

msgid "Virtual camera detection"
msgstr ""

msgid "Virtual location detection"
msgstr ""

msgid "Virus Scanners"
msgstr ""

msgid "Virus scanner"
msgstr "Virus Scanners"

msgid "Vote"
msgstr ""

msgid "Vulnerability Attack"
msgstr ""

msgid "Vulnerability Attacks"
msgstr ""

msgid "Vulnerability Scan Protection"
msgstr "Vulnerability Scan"

msgid "Vulnerability Scanners"
msgstr ""

msgid "Vulnerability Scanning(OAT-014)"
msgstr ""

msgid "Vulnerability scanner"
msgstr "Vulnerability Scanners"

msgid "Vulnerable Applications Accessible from the Extranet"
msgstr "Vulnerable Application Exposed on Internet"

msgid "WAF"
msgstr ""

msgid "WAF Analysis And Process"
msgstr "False Positive Analysis"

msgid "WAF Attack Details"
msgstr ""

msgid "WAF Cache Headers"
msgstr ""

msgid "WAF Cache Headers cannot be empty !"
msgstr ""

msgid "WAF Cache Headers has Duplicates !"
msgstr ""

msgid "WAF Cache Speedup"
msgstr ""

msgid "WAF Detection Settings"
msgstr "Inspection Size"

msgid "WAF Enable"
msgstr "Status"

msgid "WAF Misreport Analysis"
msgstr "False Positive Analysis"

msgid "WAF Rule Name"
msgstr "Rule Name"

msgid "WAF Rule Name: "
msgstr "Rule Name"

msgid "WAF Rules Management"
msgstr "WAF Virtual Patch"

msgid "WAF Strategy saved successfully, and then start save protected site."
msgstr "WAF Strategy saved successfully. Now starting to save the site."

msgid "WAF White List"
msgstr "WAF Whitelist"

msgid "WAF White List has been added!"
msgstr "WAF Whitelist has been added!"

msgid ""
"WAF Whitelist Method,Only letters is allowed and exceed 10,with commas as "
"separators."
msgstr ""
"A Method should be letters not more than 10 letters. Multiple Methods need "
"to be separated with commas."

msgid ""
"WAF Whitelist Rule ID,Only number is allowed and exceed 10,with commas as "
"separators."
msgstr ""
"A rule ID should be a number not more than 10 digits. Multiple IDs need to "
"be separated with commas."

msgid "WAF engine version"
msgstr "WAF Engine"

#, python-brace-format
msgid ""
"WAF engine version {0} doesn't support the ruleset {1}. Please select  the "
"collect waf version"
msgstr ""

#, python-brace-format
msgid ""
"WAF engine version {0} doesn't support the ruleset {1}. The minimum WAF "
"ruleset version is {2}. Please select correct ruleset"
msgstr ""

msgid "WAF policy"
msgstr ""

msgid "WAF rule file has been imported."
msgstr "Upload is done."

msgid ""
"WAF rules will not be applied to requests of which URL and ARGS are both "
"found in any entry below."
msgstr ""
"Leaving URL box empty means the rules specified on the right are disabled "
"for any requests. Leaving Rule ID box empty means requests pointing to the "
"URLs specified will not be processed by any rules."

#, python-brace-format
msgid "WAF whitelist only can add up to {0} rules."
msgstr ""

msgid "WAF,Programmable Defending"
msgstr ""

msgid "WAF,Programmable Defending,Web Protection"
msgstr ""

msgid "WAF,Web Protection"
msgstr ""

msgid "WIFI data"
msgstr ""

msgid "WL-Proxy-Client-IP in HTTP header"
msgstr "WL-Proxy-Client-IP"

msgid "WSDL file has been uploaded."
msgstr ""

msgid "Waf General Protection"
msgstr "General Protection"

msgid "Waf Modules"
msgstr "Module"

msgid "Waf Power Protection"
msgstr "Advanced Protection"

msgid ""
"Waf will skip all requests except API when switch is on. Waf will detect all"
" requests when switch is off."
msgstr ""

msgid ""
"Waiting for node recovery. If the node does not recover by itself, reboot "
"the node."
msgstr ""

msgid "Warning"
msgstr ""

msgid ""
"Warning! Not enough master nodes online! No master node can be deleted!"
msgstr ""

msgid ""
"Warning! The node that you want to delete is offline. Are you sure to delete"
" it anyway?"
msgstr "Warning! You are about to delete an offline node. Proceed anyway?"

#, python-brace-format
msgid "Warning！{0}{1} Deleting this node may cause errors. Proceed anyway?"
msgstr ""

msgid "Water Mark"
msgstr "Watermark"

msgid "Watermark Editor"
msgstr ""

msgid "Watermark URL"
msgstr ""

msgid "Watermark text"
msgstr "Watermark Text"

msgid "Watermark text cannot be empty."
msgstr "Watermark text is required."

msgid "WeChat ID"
msgstr ""

msgid "WeChat Mini-Program Protection"
msgstr "WeChat Mini-Program"

msgid "WeChat mini-program request whitelist"
msgstr "WeChat Mini-Program Whitelist"

msgid "WeChat mini-progrma SDK"
msgstr "WeChat Mini-Program SDK"

msgid "Weak Password Check"
msgstr "Weak Password"

msgid "Weak Passwords"
msgstr "Weak Password"

msgid "Weak password count(wpc)"
msgstr ""

msgid "Weak password dictionary"
msgstr ""

msgid "Weak password dictionary does not allow null."
msgstr ""

msgid "Weak password dictionary update"
msgstr ""

msgid "Weak password requests(btd)"
msgstr ""

msgid "Weak password success(bte)"
msgstr ""

msgid "Web Access Policy"
msgstr ""

msgid "Web Advanced Security Scheme"
msgstr "Protection Scheme"

msgid "Web Common Keys(commonKeys):"
msgstr ""

msgid "Web Dynamic Challenge"
msgstr ""

msgid "Web Filter"
msgstr ""

msgid "Web Filter Switch"
msgstr ""

msgid ""
"Web Filter inspects HTTP requests against a set of rules and blocks the ones"
" that failed inspections."
msgstr ""
"Web Filter inspects HTTP requests and responses against a set of rules and "
"blocks those failed inspections."

msgid "Web Manual Rule"
msgstr ""

msgid "Web Primary Protection Strategy"
msgstr ""

msgid "Web Protection"
msgstr ""

msgid "Web Protection Token Advanced Scheme"
msgstr ""

msgid "Web Standard Security Scheme"
msgstr "Protection Scheme"

#, python-brace-format
msgid "Web filtering feature: {0}"
msgstr "Web Filter feature: {0}"

msgid "Web page tamper-proof"
msgstr "Web Page Anti-Tamper"

msgid "WebConsole"
msgstr ""

msgid "WebConsole Host Header White Lists"
msgstr "WebConsole Host Whitelist"

msgid "Webpage Snapshots"
msgstr "Webpage Snapshot"

msgid "Website "
msgstr "Website"

msgid "Website Config Editor"
msgstr "Website Configuration Editor"

msgid "Website Config Editor has data but not enabled."
msgstr "Website Configuration Editor has data but not enabled."

msgid "Website Config Editor is enabled."
msgstr "Website Configuration Editor is enabled."

msgid "Website Config Editor only work on Proxy."
msgstr "Website Configuration Editor only works on Proxy node."

msgid "Website Configuration"
msgstr ""

msgid "Website Monitoring"
msgstr ""

msgid "Website Name"
msgstr "Site Name"

msgid "Website Name in Apply Site"
msgstr "Site Name"

msgid "Website Settings"
msgstr "Website"

msgid "Website added"
msgstr ""

msgid "Website deleted"
msgstr ""

msgid "Website lock"
msgstr "Website Locks"

msgid "Website modified"
msgstr ""

msgid ""
"Website name conflict with website of other operator, please contact "
"administrator."
msgstr ""

msgid "Website shield"
msgstr "Website Shield"

msgid "Website static"
msgstr "Static Websites"

msgid "Websites"
msgstr ""

msgid "Websites:"
msgstr ""

msgid "Websocket"
msgstr "WebSocket"

msgid "Websocket path cannot be blank."
msgstr "WebSocket path is required."

msgid "Websocket path is too long."
msgstr "WebSocket path is too long."

msgid "Wechat Mini Program Protection"
msgstr "WeChat Mini-Program"

msgid "Wechat appID can not be empty"
msgstr "AppID can not be empty."

msgid "Wechat appName can not be empty"
msgstr "App name can not be empty."

msgid "Wechat body obfuscation setting"
msgstr "WeChat Mini-Program Body Obfuscation"

msgid "Wechat_MPP"
msgstr ""

msgid "Wed."
msgstr ""

msgid "Weight"
msgstr ""

msgid "Well-known search engine"
msgstr "Popular Search Engines"

msgid ""
"When \"Obtaining Source IP\" is configured as X-Forwarded-For or X-Real-IP, "
"it is recommended to disable X-Real-IP and X-Forwarded-For in \"HTTP Request"
" Headers\". Confirm save?"
msgstr ""
"When \"Obtain Source IP\" is configured as X-Forwarded-For or X-Real-IP, it "
"is recommended to disable X-Real-IP and X-Forwarded-For in \"HTTP Request "
"Headers\". Confirm save?"

msgid ""
"When Traffic Pass-Through is selected, you need to select the modules to be "
"disabled for traffic inspections. In this case, the system exits emergency "
"mode automatically if usages of the resources currently enabled drop below "
"the Exit Threshold and stay there for the period of time specified in Stay "
"Exit (sec).<br/>When the option WAF Pass-Through (i.e., bridge pass-through)"
" is selected, the system cannot exit emergency mode itself. You need to "
"disable Bridge Pass-Through in the page System > Network Configuration to "
"help the system go back to work.<br/>Intelligent Pass-Through allows the "
"system adjust protection levels dynamically to prevent overload of system "
"resources."
msgstr ""
"When Module Pass-Through is selected, you need to select modules that need "
"to stop  traffic inspections. In this case, the system exits emergency mode "
"automatically if usages of the resources currently selected drop below the "
"Exit Threshold and stay there for the period of time specified in Stay Exit "
"(sec).<br/><br/>When Traffic Pass-Through (i.e., bridge pass-through) is "
"selected, the system cannot exit emergency mode itself. You need to disable "
"Bridge Pass-Through in the page System > Network Configuration to put the "
"system back to work.<br/><br/>Smart Pass-Through allows the system adjust "
"protection levels dynamically to prevent overload of system resources."

msgid ""
"When adding a field name, you can directly click in the field list on the "
"left to automatically add fields to the expression in the advanced filter, "
"or you can manually type it."
msgstr ""

msgid ""
"When all monitoring indicators meet the exit conditions and the duration "
"exceeds Relaxation Time, the system exits the emergency mode."
msgstr ""
"The system exits emergency mode if usages of the resources currently enabled"
" drop below the numbers you entered in Exit Threshold and stay that way for "
"the period of time specified in Stay Exit (sec)."

msgid ""
"When all monitoring indicators meet the exit conditions, the protected state"
" is automatically restored."
msgstr ""

msgid ""
"When designing the interface, check whether the debugging information "
"related to program operation has been removed to prevent the exposure of "
"internal sensitive information."
msgstr "Ensure debugging information does not return to clients."

msgid ""
"When designing the interface, some PIIs can be desensitized according to "
"business needs, and the amount of data returned by the interface at a time "
"can be limited; for interfaces that have not been modified in time, it is "
"necessary to monitor the interface access, pay attention to data exposure, "
"and promptly discover the risk of abnormal data acquisition."
msgstr ""
"Ensure sensitive data is masked or deleted before they are returned to "
"clients."

msgid ""
"When designing the interface, you can desensitize or delete important PIIs "
"types according to business needs to avoid the interface returning multiple "
"types of PIIs at a time; for interfaces that have not been modified in time,"
" it is necessary to monitor the interface access situation, pay attention to"
" data exposure, and promptly discover the risk of abnormal data acquisition."
msgstr ""
"Ensure sensitive data is masked or deleted before they are returned to "
"clients."

msgid ""
"When enabled, VXLAN traffic of the specified port will be resolved. The "
"default ports are 4879 and 8472. You can specify up to 10 ports separated by"
" commas."
msgstr ""
"Enable it to parse the VXLAN traffic sent to specific ports. Default port: "
"4789,8472. Up to ten port numbers can be configured with commas as "
"separators."

msgid ""
"When enabled, all users in the system must pass two-factor authentication "
"before they can log in."
msgstr ""
"When enabled, all WebConsole users must pass a two-factor authentication "
"before they can successfully log in."

msgid ""
"When enabled, allow the monitoring of management network adapter in multiple"
" network card mode in order to access protected sites through the management"
" network adapter"
msgstr ""

msgid "When enabled, it applies Block Mode on the effective protection list."
msgstr ""

msgid ""
"When enabled, new resources will be requested from the upstream server after"
" the cache expires and compared with the cache resources. When the "
"similarity is greater than or equal to the threshold,the cache is "
"automatically updated."
msgstr ""
"Updating cached pages automatically when they are expired and the similarity"
" between upstream and them is equal to or higher than the threshold set "
"below."

msgid ""
"When enabled, receive parsed access log from other clusters. Make sure that "
"the other clusters are the same as the current cluster versions."
msgstr ""
"When enabled, the system receives parsed access log from other clusters. "
"Make sure that other clusters have the same system version."

msgid "When enabled, the GRE traffic sent to this machine will be parsed"
msgstr "Enable it to parse the GRE traffic sent to the protection system."

msgid ""
"When enabled, the system does not check the app protection token of requests"
" accessing the following path."
msgstr ""
"For app requests accessing the whitelist path, protection tokens will not be"
" verified by the system."

msgid ""
"When functions are used, the system only supports string fields. The field "
"should be entered as the first parameter, for example: start_with(path, "
"\"/login\")，empty(path). The following list shows the meaning of available "
"functions"
msgstr ""

msgid ""
"When handshake path is changed, SDK must be integrated again to ensure "
"synchronized configuration."
msgstr ""

msgid "When it is enabled, Login/Account Menu will display Roles tab."
msgstr ""

msgid ""
"When it is enabled, System/Log/Settings Menu will display related config."
msgstr ""
"If enabled，PII Masking configurations can be found in \"System\" > \"Logs\" "
"> \"Settings\" page. "

msgid ""
"When it is enabled, WebConsole will provide network ddos protect feature."
msgstr ""
"Enable it to display Network-Layer DDoS entry in navigation bar of the "
"WebConsole."

msgid ""
"When it is enabled, WebConsole will provide threat intelligence feature."
msgstr ""
"When it is enabled, WebConsole will display Threat Intelligence feature in "
"the navigation bar."

msgid "When it is enabled, WebConsole will provide web filtering feature."
msgstr ""
"When it is enabled, WebConsole will display Web Filter feature in the "
"navigation bar."

msgid ""
"When it is enabled, the functions of \"Client Injection Attack Detection\" "
"and \"Path and Parameter Encryption\" in Advanced Web Protection will be "
"visible, configurable and effective."
msgstr ""
"When it is enabled, the functions of \"Client Injection Attack Detection\" "
"and \"Path and Parameter Encryption\" in Power Web Protection will be "
"visible for configuration."

msgid ""
"When partial cardholder data is available, and the expiry date and/or "
"security code are not known, the process is instead known as OAT-010 Card "
"Cracking. The use of stolen cards to obtain cash or goods is OAT-012 Cashing"
" Out."
msgstr ""

msgid ""
"When plaintext password transmission is found in the interface request, "
"attackers can monitor or intercept it during the transmission process to "
"obtain the user's real password."
msgstr ""
"Plaintext passwords are found in requests that have been accepted by APIs. "
"They may be easily obtained by third parties."

msgid ""
"When resource overloads occur on a node in the system, the node enters "
"emergency mode and forwards requests according to the settings here."
msgstr ""

msgid ""
"When switched off, this site configuration will be disabled and site service"
" no longer available."
msgstr ""

msgid ""
"When the activity has started, the following actions will be applied on "
"requests that have not visited the referer path."
msgstr ""
"Actions specified below will be applied to requests that have not visited "
"the required referer path to place the order."

msgid ""
"When the content-type is text/html, text/plain, text/xml, the response body "
"will be detected"
msgstr ""

msgid ""
"When the following functions are used, the system only supports ip or string"
" fields. The field should be entered as the first parameter and the file "
"name as the second parameter. For example: in_list(src_ip, "
"\"ips.txt\")，not_in_list(args, \"args_list\"). Attention: they do not "
"support regular expression. The following list shows the meaning of "
"available functions."
msgstr ""

msgid ""
"When the function is enabled, the account will be locked when the account "
"has not been used for the set time. This function is not valid for "
"administrators"
msgstr ""
"When enabled, accounts (except administrators) that stay idle for a period "
"of time as specified will be locked. "

msgid ""
"When the score generated by the model below exceeds this threshold, it is "
"considered as a threat event."
msgstr ""

msgid ""
"When there are multiple clients (e.g. mini-programs, mobile apps) for one "
"application but the corresponding protection modules (e.g. mini-program "
"protection, mobile app protection) are not enabled, you can exempt these "
"traffics from dynamic protection measure."
msgstr ""
"When there are multiple clients (e.g. mini-programs, external website) for "
"one application but the corresponding protection modules are not enabled, "
"you can exempt these traffics from dynamic protection measure."

msgid ""
"When you need to remove some IP addresses from the automatic blacklist, fill"
" in the corresponding IP addresses, and then click Remove."
msgstr ""
"Enter an IP and click Remove to move it out of the automatic blacklist."

msgid ""
"When you want to encrypt Ajax/Fetch response body, please configure 2 lists:"
" 1. Ajax/Fetch Referer List; 2. Ajax/Fetch Response Encryption List;"
msgstr ""
"To obfuscate an Ajax/Fetch response body, both Ajax/Fetch Referer and "
"Ajax/Fetch Response should be configured."

#, python-brace-format
msgid "When {0}, {1}"
msgstr ""

msgid "Whether to Add Manually"
msgstr "Added Manually"

msgid "Whether to Enable Parameter Splitting"
msgstr "Include Parameter"

msgid "Whether to Match Sub-paths"
msgstr "Include Subdirectory"

msgid "Whether to save file"
msgstr "Cache File"

msgid ""
"While Enabled, requests without response will be dropped (status "
"code:555/556)"
msgstr ""
"Enable it to discard requests with no responses (status code 555/556)."

msgid "White Brand"
msgstr ""

msgid "White List"
msgstr "Mobile Whitelist"

msgid "White List cannot be blank!"
msgstr "Whitelist is required."

msgid "White list can not blank"
msgstr "Whitelist is required."

msgid "Whitelist of Prompt Words"
msgstr "Prompt Whitelist"

msgid "Widget IDs"
msgstr ""

msgid "Width"
msgstr ""

#, python-brace-format
msgid "Wildcard character must end with {0}"
msgstr ""

msgid "Will go to home page in a few seconds..."
msgstr ""

msgid ""
"With this condition enabled, the Policy and and Cache File (if enabled) are "
"executed if clients download any files of a MIME type entered here."
msgstr ""

msgid ""
"With this condition enabled, the Policy and and Cache File (if enabled) are "
"executed if clients download any files with a size higher than the value "
"entered here."
msgstr ""

msgid ""
"With this condition enabled, the Policy and and Cache File (if enabled) are "
"executed if clients download any files with an extension entered here."
msgstr ""

msgid ""
"Within 10 minutes before the start of the activity, the key values of all "
"requests that access the sniping path in advance will be recorded; when the "
"activity has started, the following actions will be applied on requests "
"whose key values had been recorded."
msgstr ""
"For requests that access the sniping path within 10 minutes before the "
"activity starts, the Key of these requests will be recorded, and actions "
"specified below will be applied to requests with the same Key when activity "
"starts."

msgid "Wizard"
msgstr ""

msgid "Wizard has been finished"
msgstr ""

msgid "Wizard is finished already."
msgstr ""

msgid "Work Address"
msgstr ""

msgid "Worker Node"
msgstr ""

msgid "X-Forwarded-For in HTTP header"
msgstr "X-Forwarded-For"

msgid "X-Real-IP in HTTP header"
msgstr "X-Real-IP"

msgid "X86 Device"
msgstr ""

msgid "XML (Type Detection)"
msgstr ""

msgid "XML Attack Protection"
msgstr "XML Attack"

msgid "XML Basic Verification"
msgstr ""

msgid "XSD file has been uploaded."
msgstr ""

msgid ""
"XSS and SQL injection attack detection is performed on the request content "
"on the client side to prevent abnormal requests from being forwarded to the "
"backend and occupying system resources."
msgstr ""
"Detect XSS and SQL attacks on the client side before the requests are sent "
"to the backend to save system resources."

msgid "XSS injection model"
msgstr "XSS Injection Model"

msgid "Yes"
msgstr ""

msgid "Yesterday"
msgstr ""

msgid ""
"You can add routes for the system by clicking the Add Route button. When "
"there is the same route (destination network, same subnet mask), the private"
" route of this node will cover the non private route."
msgstr ""
"You can add routes for the system by clicking the Add Route button. Private "
"routes only work for the current node, while public routes take effects on "
"the whole cluster."

msgid ""
"You can also upload error pages defined by yourself. Please make sure the "
"pages you upload are encoded in UTF-8."
msgstr ""
"You can also upload a customized error page for the replacement. Note: the "
"HTML page you upload must be UTF-8 encoded."

msgid ""
"You can also upload your own certificate and private key to the system or "
"place them in other positions such as a load balancer to solve the issue."
msgstr ""

msgid ""
"You can enter up to 2000 characters in the advanced expression input box."
msgstr ""

msgid "You can re-select the WAF strategy"
msgstr ""

msgid "You can select \"Use built-in certificate\" from the pop-up options,"
msgstr ""

msgid ""
"You can upload a CA certificate and private key created by yourself with "
"tools such as OpenSSL."
msgstr ""

msgid "You cannot modify policies created by other users."
msgstr ""

msgid "You do not have this permission. Please contact your administrator."
msgstr ""

msgid "You have been born again."
msgstr ""

msgid "You have no permission to access."
msgstr ""

msgid "You have no permission!"
msgstr ""

msgid "You need to do it on node 1."
msgstr ""

msgid ""
"You need to re-authorize after deleting this statistics master authorization"
" node. "
msgstr ""

msgid "You need to select at least one PII type"
msgstr ""

msgid "You need to select at least one vulnerable application"
msgstr ""

msgid ""
"You will lose all your configurations except IP and must initialize the DAP "
"with Wizard."
msgstr ""
"You will lose all your configurations except IP and must initialize the "
"system with wizard"

msgid "You've successfully logged out."
msgstr ""

msgid "Your "
msgstr "The "

msgid ""
"Your Chrome version is too old. Please update it to the latest version to "
"ensure it can work properly with the RAS systsem."
msgstr ""
"Your Chrome version is too old. Please update it to the latest version to "
"ensure it can work properly with the systsem."

msgid ""
"Your current browser was detected as FireFox. Save after changing the port. "
"If the management system still does not automatically switch to the new "
"address after a few minutes, please go to the address bar to manually "
"operate."
msgstr ""
"Enter another port to replace the default one. The page will refresh "
"automatically at the new port after saving changes successfully."

msgid ""
"Your e-mail is applied to Dynamic Application Protection System. When System"
" trigger alarm, you will receive mail notify."
msgstr ""

#, python-brace-format
msgid ""
"Your module \"{module}\" in license has expired on {expiration} and stopped "
"supporting service. Please contact us for a new license."
msgstr ""

msgid ""
"Your phone number is applied to Dynamic Application Protection System. When "
"System trigger alarm, you will receive sms message notify."
msgstr ""

msgid "Your session has been timed out."
msgstr ""

msgid "Your token is invalid."
msgstr ""

#, python-brace-format
msgid ""
"Your trial module \"{module}\" in license has expired on {expiration} and "
"stopped working. Please contact us for a new license."
msgstr ""

#, python-brace-format
msgid ""
"Your {license_or_service} expired on {expiration}.{maintenance} Please "
"contact us for a new license."
msgstr ""

#, python-brace-format
msgid ""
"Your {license_or_service} will be in effect on {start_date}. Please contact "
"us for a new license."
msgstr ""

#, python-brace-format
msgid ""
"Your {license_or_service} will be invalid on {expired_date}.{maintenance} "
"Please contact us for a new license."
msgstr ""

#, python-brace-format
msgid ""
"Your {license_or_service} will expire on {expiration}.{maintenance} Please "
"contact us for a new license."
msgstr ""

msgid "Zombie"
msgstr "Inactive"

msgid "[ Ask to Enable Cookie ] has been set."
msgstr "[Ask to Enable Cookie] has been set."

msgid "[ GM Algorithm ] has been set."
msgstr "[GM Algorithm] has been set."

msgid "[ Slow Http Attack Protect ] has been set."
msgstr "[Slow Http Attack Protection] has been set."

msgid "[ Strictly Match Host Header ] has been set."
msgstr "[Strictly Match Host Header] has been set."

#, python-brace-format
msgid ""
"[ {0} ] Please enter a integral number greater than or equal to {1} and less"
" than or equal to {2}"
msgstr "[{0}] Please enter an integer between {1} and {2}."

#, python-brace-format
msgid ""
"[ {0} ] Please enter a integral number greater than or equal to {1} and less"
" than or equal to {2}."
msgstr "[{0}] Please enter an integer between {1} and {2}."

#, python-brace-format
msgid ""
"[ {0} ] Please enter a number greater than or equal to {1} and less than or "
"equal to {2}"
msgstr "[{0}] Please enter a number ranging from {1} to {2}."

#, python-brace-format
msgid "[ {0} ] cannot be blank"
msgstr "[ {0} ] is required."

#, python-brace-format
msgid "[ {0} ] cannot be blank."
msgstr "[ {0} ] is reuqired."

#, python-brace-format
msgid "[ {0} ] cannot contain spaces"
msgstr ""

#, python-brace-format
msgid "[ {0} ] cannot contain spaces."
msgstr ""

#, python-brace-format
msgid "[ {0} ] should not exceed {1} characters."
msgstr ""

msgid "[Attenuation value] The value ranges from 0.1 to 100."
msgstr ""

msgid ""
"[CSRF] The total cache disk space exceeds the limit of 1G. Please reduce the"
" current or other sites settings for cache disk space."
msgstr ""

msgid "[Errno 111] Connection refused"
msgstr ""

msgid "[Errno 113] No route to host"
msgstr ""

msgid "[Interface instructions]"
msgstr "[API Documentation]"

msgid "[Security Scan]"
msgstr "Security Scan"

#, python-brace-format
msgid "[System]-[Command Line] page: {0}"
msgstr ""

#, python-brace-format
msgid "[{0}] contain invalid character(s) "
msgstr ""

#, python-brace-format
msgid "[{0}] contain invalid character(s). "
msgstr "Invalid character(s) found in path(s)!"

#, python-brace-format
msgid "[{0}] not selected "
msgstr ""

#, python-brace-format
msgid ""
"[{0}] only support numbers, letters, Chinese, and characters such as: "
"spaces, dot(.), underline(_), hyphen(-), parentheses, asterisk(*), plus(+)."
msgstr ""

#, python-brace-format
msgid "[{0}] {1} duplicate configuration"
msgstr ""

msgid "a maximum of 10 custom UA can be added"
msgstr ""

msgid "active-backup"
msgstr "Mode 1: active-backup"

msgid "add new watermark rule"
msgstr "Added a new watermark rule"

msgid "add_register_apps failed! found {} is empty"
msgstr ""

msgid "admin"
msgstr ""

msgid "admin user is disabled."
msgstr ""

msgid "alarm_code should be 2 or 3."
msgstr ""

msgid "all APP blacklist rules"
msgstr "all app blacklist rules"

msgid "all APP counter rules"
msgstr "all app counter rules"

msgid "all APP high frequent rules"
msgstr "all app high frequency rules"

msgid "all APP reputation rules"
msgstr "all app reputation rules"

msgid "all attack types will log all logs by default"
msgstr "The system keeps all attack logs by default. "

msgid "all bad behavior rules"
msgstr "All user behavior rules"

msgid "all blacklist rules"
msgstr "All blacklist rules"

msgid "all counter rules"
msgstr "All counter rules"

msgid "all high frequent rules"
msgstr "All high frequency rules"

msgid "all new comer rules"
msgstr "All new user rules"

msgid "all reputation rules"
msgstr "All reputation rules"

msgid "all request"
msgstr "All Requests"

msgid "all sniping rules"
msgstr "All sniping rules"

msgid "all traffic taken"
msgstr ""

msgid "allowed URL"
msgstr ""

msgid "already has a same replacement rule"
msgstr ""

msgid "analysis file"
msgstr ""

msgid "analysis file line by line"
msgstr ""

msgid "analysis payload"
msgstr ""

msgid "analyze entire file, file name is: {}"
msgstr ""

msgid "analyze user input payload, location is: {}, payload is: {}"
msgstr ""

msgid ""
"and install on your computer and trust it to avoid certificate warning "
"messages when accessing the WebConsole or the HTTPS protected sites that use"
" the built-in certificate."
msgstr ""

msgid "api customized rule name cant repeat"
msgstr ""

msgid "api customized rule regular exception exceed {}"
msgstr ""

msgid "api defect auto convert"
msgstr "Auto Defect Status"

msgid "api not exist"
msgstr ""

msgid "api_params_policy is not json"
msgstr ""

msgid "api_params_policy is not list"
msgstr ""

msgid "app sites file only support UTF-8 encoding"
msgstr ""

msgid "app sites file should not be empty"
msgstr ""

msgid "appID"
msgstr "AppID"

msgid "appName"
msgstr "App Name"

msgid "attack list"
msgstr "Attack List"

msgid "auto black ip have been cleared"
msgstr ""

msgid "auto business white list at most can specified {} apis"
msgstr "auto business whitelist at most can specified {} apis"

msgid "auto convert to handled {} status to {}"
msgstr "auto updated status \"{}\" to \"{}\""

msgid "auto convert to unhandled {} status to {}"
msgstr "auto updated status \"{}\" to \"{}\""

msgid "auto tag white list at most can specified {} apis"
msgstr "auto tag whitelist at most can specified {} apis"

msgid "bad behavior rule"
msgstr "User behavior rule"

msgid "balance-rr"
msgstr "Mode 0: balance-rr"

msgid "base64 decode"
msgstr "Base64"

msgid "blacklist rule"
msgstr "Blacklist rule"

msgid "brute_force_strategy id: {} is not digit"
msgstr ""

msgid "brute_force_strategy id: {} is not exist in waf brute_force_strategies"
msgstr ""

msgid "business type rule key {} does not exist!"
msgstr ""

msgid "business type rule operation {} does not exist!"
msgstr ""

msgid "business type rule value {} type does not match."
msgstr ""

msgid ""
"but it is not a trusted one and will lead to alerts displayed on the "
"visitors' browsers."
msgstr ""

msgid "bytes"
msgstr ""

msgid "cancelled"
msgstr ""

msgid "cannot be blank"
msgstr "is required."

msgid "cannot be empty"
msgstr "is required."

msgid "character length cannot exceed 20"
msgstr ""

msgid "characters"
msgstr ""

msgid "common token"
msgstr ""

msgid "config"
msgstr "Configuration"

msgid "connecting Internet."
msgstr "connecting to the Internet."

msgid "connecting internal servers."
msgstr "connecting to internal servers."

msgid "connecting the protection system WebConsole for management."
msgstr "connecting to the protection system WebConsole for management."

msgid "contains"
msgstr ""

msgid "copy"
msgstr ""

msgid "corpus has been successfully rollbacked to "
msgstr ""

msgid "counter rule"
msgstr "Counter rule"

msgid "create pii type"
msgstr "Added a PII type"

msgid "cti db does not exist"
msgstr ""

msgid "cti db failed to install"
msgstr ""

msgid "cti update config"
msgstr "CTI Updates settings"

msgid "ctidb failed to install"
msgstr "Failed to install package"

msgid "ctidb succeed to install"
msgstr "Installed package successfully"

msgid "custom strategy_{}"
msgstr ""

msgid "days"
msgstr ""

msgid "decode parse"
msgstr "Parser/Decoder"

msgid "deep analyze"
msgstr ""

msgid "default site of full flow collection"
msgstr ""

msgid "default token"
msgstr ""

msgid "defect list"
msgstr "Defect List"

msgid "delete"
msgstr ""

msgid "delete pii type"
msgstr "Deleted a PII type"

msgid "delete site cache"
msgstr ""

msgid "delete site cache files failed"
msgstr ""

msgid "delete watermark rule"
msgstr ""

msgid "desensitize display of sensitive information"
msgstr "Display Masking"

msgid "disabled"
msgstr ""

msgid ""
"domain names include wildcard must be shorter than 10 levels divided by dot."
msgstr ""

msgid ""
"Due to the disabling of admin account, the following IP users are forced to log out: "
"{ips}."
msgstr ""

#, python-brace-format
msgid ""
"Due to the password change, the following IP users are forced to log out: "
"{ips}."
msgstr ""

#, python-brace-format
msgid ""
"Due to the enabling of no repeat login, the following IP "
"users are forced to log out: {ips}."
msgstr ""

msgid "duplicate port number"
msgstr ""

msgid "e.g."
msgstr "e.g. "

#, python-brace-format
msgid "e.g., if target protocol is HTTP/1.1, you need to enter {0}."
msgstr "Only the regex for version number is necessary."

msgid "edit pii type"
msgstr "Changed settings of a PII type"

msgid "empty path"
msgstr ""

msgid "enable the role"
msgstr ""

msgid "enabled"
msgstr ""

#, python-brace-format
msgid "encrypted paths and parameters are not supported for lable value: {0}."
msgstr ""

msgid "end time can not be empty"
msgstr ""

msgid "end time can not ealier than start time"
msgstr "end time can not earlier than start time"

msgid "equal to"
msgstr "Equal to"

msgid "error"
msgstr ""

msgid "error code"
msgstr ""

msgid ""
"eth0 works as External, Internal and Management Network Adapter "
"simultaneously."
msgstr ""

msgid ""
"eth0 works as management network adapter and eth1 as external and internal "
"network adapter."
msgstr ""
"eth0 works as Management Network Adapter and eth1 as External and Internal "
"Network Adapter."

msgid ""
"eth0 works as management network adapter, eth1 as external network adapter "
"and eth2 as internal network adapter."
msgstr ""
"eth0 works as Management Network Adapter, eth1 as External Network Adapter "
"and eth2 as Internal Network Adapter."

msgid "export {} to {} file."
msgstr "Exported {} in {} file."

msgid "external"
msgstr ""

msgid "fail to threat intelligence db"
msgstr "Fail to threat intelligence db"

msgid "fail to verify llm_corpus"
msgstr "Fail to verify corpus"

msgid "fail to verify modelfile"
msgstr ""

msgid "fail to verify ruleset"
msgstr "Fail to verify ruleset"

msgid "failed"
msgstr "Failed"

msgid "failed to auto-update WAF ruleset"
msgstr "Failed to auto update WAF ruleset"

msgid "failed to auto-update threat intelligence"
msgstr "Failed to auto update threat intelligence"

msgid "failed to find the next node to upgrade"
msgstr ""

msgid "failed to start nginx"
msgstr ""

msgid "fetching result"
msgstr ""

msgid "file size limit"
msgstr "Upload File"

msgid ""
"filename format is xxxx.data or xxxx.conf, xxxx are combined by "
"char,number,- or _<br/>In order to ensure uniqueness of rule IDs in the "
"file, ID range of 10000~15000 is recommended."
msgstr ""
"Filename extensions can be .data or .conf. Filename can contain letters, "
"numbers,- or _<br/>To ensure uniqueness of rule IDs, IDs should range from "
"10000 to 15000."

msgid "first detected"
msgstr "Found the first match"

msgid "good_bots"
msgstr ""

msgid "greater than "
msgstr "Greater than"

msgid "hex decode"
msgstr "Hex"

msgid "high frequent rule"
msgstr "High frequency rule"

msgid "high frequent rule status"
msgstr "high frequency rule status"

msgid "hour unit"
msgstr "hour"

msgid "html decode"
msgstr "HTML"

msgid "http_protocol_strategy"
msgstr ""

msgid "ignore importing"
msgstr ""

msgid "implication"
msgstr ""

msgid "import failed, API file format error"
msgstr ""

msgid "import failed, app sites file format error"
msgstr ""

msgid "industry data tag {} does not exist!"
msgstr ""

msgid "intercept"
msgstr ""

msgid "internal"
msgstr ""

msgid "invalid"
msgstr ""

msgid "invalid operation type"
msgstr ""

msgid "is not valid phone number"
msgstr ""

msgid "is_app_verify must be true or false!"
msgstr ""

msgid "json Parse"
msgstr "JSON"

msgid "keep source port"
msgstr "Fixed Source Port"

msgid "keepalived"
msgstr ""

#, python-brace-format
msgid "key:{0} is not used in {1} scene"
msgstr "key:{0} is not used in {1} scenario"

msgid "key:{} is not used in api customized rule"
msgstr ""

msgid "key_type='account'"
msgstr ""

msgid "length has exceeded limit."
msgstr ""

msgid "less than"
msgstr "Less than"

msgid "license"
msgstr ""

msgid "limit minutes"
msgstr ""

msgid "limit switch"
msgstr "Calculation"

msgid "limit times"
msgstr ""

msgid "line by line analysis, file name is: {}"
msgstr ""

msgid "line: {} content: {} result:done"
msgstr ""

msgid "llm corpus failed to install"
msgstr ""

msgid "llm corpus succeed to install"
msgstr "Installed LLM corpus successfully"

msgid "malicious IP of User Behavior Rules:"
msgstr "Removed IP from the malicious IP list created by User Behavior Rules"

msgid "max length is 1024"
msgstr ""

msgid "max request count"
msgstr ""

msgid "max request dispersion"
msgstr ""

msgid "method value {} is not correct"
msgstr ""

msgid "min sample count"
msgstr ""

msgid "mini program list"
msgstr "Mini-Program List"

msgid "minutes"
msgstr "min"

msgid "minutes (should be less than the validity period)"
msgstr "minutes (It should be less than Expiration)"

msgid "modify"
msgstr "Modify"

msgid "modify industry type,failed: industry type is not str or unicode"
msgstr ""

msgid "modify industry type,failed: {} not exist"
msgstr ""

msgid "modify web filter rules."
msgstr "Modified Web Filter rules"

msgid "module_strategy"
msgstr ""

msgid "ms"
msgstr ""

msgid "msg_id_any"
msgstr "Any"

msgid "msg_id_attack_whitelist"
msgstr "Attack Detection Whitelist"

msgid "msg_id_business_type"
msgstr "Business Type"

msgid "msg_id_created_by_auto"
msgstr "Realtime Detection"

msgid "msg_id_created_by_import"
msgstr "Import"

msgid "msg_id_created_by_manual"
msgstr "Manual Addition"

msgid "msg_id_created_by_resutul"
msgstr "RESTful API"

msgid "msg_id_created_by_scanner"
msgstr "Security Scan"

msgid "msg_id_customer_name"
msgstr "Customer Name"

msgid "msg_id_defect_whitelist"
msgstr "Defect Whitelist"

msgid "msg_id_medium"
msgstr "Medium"

msgid "msg_id_none"
msgstr "None"

msgid "msg_id_offline"
msgstr "Offline"

msgid "msg_id_online"
msgstr "Online"

msgid "msg_id_path"
msgstr "Path"

msgid "msg_id_path2"
msgstr "Path"

msgid "msg_id_pii_whitelist"
msgstr "PII Whitelist"

msgid "msg_id_province"
msgstr "Province"

msgid "msg_id_referer"
msgstr "Referer"

msgid "msg_id_regex"
msgstr "Regex"

msgid "msg_id_request_body"
msgstr "Request Body"

msgid "msg_id_request_header"
msgstr "Request Header"

msgid "msg_id_request_method"
msgstr "Request Method"

msgid "msg_id_response_body"
msgstr "Response Body"

msgid "msg_id_response_header"
msgstr "Response Header"

msgid "msg_id_risk_level"
msgstr "Risk Level"

msgid "msg_id_risk_level2"
msgstr "Risk Level"

msgid "msg_id_source_ip"
msgstr "Source IP"

msgid "msg_id_source_port"
msgstr "Source Port"

msgid "msg_id_username"
msgstr "Account Name"

msgid "msg_id_waf_desc_cve"
msgstr "CVE ID:"

msgid "msg_id_waf_desc_cve_na"
msgstr "CVE ID: N/A"

msgid "msg_id_waf_description"
msgstr "Description: "

msgid "msgid_industry_level"
msgstr "Industry Level"

msgid "multipart Parse"
msgstr "Multipart"

msgid "named"
msgstr ""

msgid "needs to be a private route"
msgstr ""

msgid "new comer rule"
msgstr "New user rule"

msgid "new comer rule status"
msgstr "new user rule status"

msgid "nginx CPU's usage"
msgstr ""

msgid "nginx memory's usage"
msgstr ""

msgid "no site_name specified"
msgstr ""

msgid "none traffic taken"
msgstr ""

msgid "not contains"
msgstr ""

msgid "not execute yet"
msgstr "Not executed yet"

msgid "not http"
msgstr "PII list of other protocols"

msgid "num of industry data levels exceed max 5!"
msgstr ""

msgid "num of industry data tags exceed max 5!"
msgstr ""

msgid ""
"only support numbers, letters, spaces, carriage return, line feed and "
"characters such as: ! @ & = + ; ? () [] * : / , . _ -"
msgstr ""

msgid "only support one scanner"
msgstr ""

msgid "partition root's usage"
msgstr ""

msgid "partition var's usage"
msgstr ""

msgid "path"
msgstr "Path"

msgid "path and args encryption"
msgstr "Path and Parameter Encryption"

msgid "path count"
msgstr " "

msgid "path sequence can not be empty"
msgstr ""

msgid "path sequence count can not greater than 10000"
msgstr ""

msgid "php unserialize"
msgstr "PHP Deserialization"

msgid "pii file"
msgstr "PII List of File"

msgid "pii list"
msgstr "PII List of API"

msgid "pii type"
msgstr ""

msgid "please enter a positive integers"
msgstr ""

msgid "please enter integers"
msgstr "Select a file"

msgid "please enter positive integers"
msgstr ""

msgid "please submit only once"
msgstr ""

msgid "plus escape"
msgstr "Plus"

msgid "point/hour"
msgstr ""

msgid "proxy_connect_timeout"
msgstr "Connection timeout"

msgid "proxy_read_timeout"
msgstr "Receiving timeout"

msgid "proxy_send_timeout"
msgstr "Sending timeout"

msgid "query error"
msgstr ""

msgid "query expression can not be empty"
msgstr ""

msgid "quotes"
msgstr ""

msgid "reCaptcha"
msgstr "CAPTCHA"

msgid "records"
msgstr " "

msgid "redirect token"
msgstr ""

msgid "reg expression can not be empty"
msgstr ""

msgid "release date"
msgstr "Release Date"

msgid "reputation rule"
msgstr "Reputation rule"

msgid "reputation rule status"
msgstr ""

msgid "request body size limit"
msgstr "Request Body"

msgid "request type"
msgstr "Request Filter"

msgid "res_leech_strategy"
msgstr ""

msgid "res_leech_strategy: {} is not digit"
msgstr ""

msgid "res_leech_strategy: {} is not exist in waf res_leech strategy"
msgstr ""

msgid "res_suffix should not be null"
msgstr ""

msgid "reset failed!"
msgstr ""

msgid "reset share memory"
msgstr "reset shared memory"

msgid "reset success!"
msgstr ""

msgid "resp body size limit"
msgstr "Response Body"

msgid "response token"
msgstr ""

msgid "return html page"
msgstr "Return HTML"

msgid "return_html: return specific html file to the client"
msgstr "Return HTML: return specific HTML file to the client"

msgid "rollback"
msgstr "Rollback"

msgid "rules"
msgstr ""

msgid "ruleset has been successfully rollbacked to "
msgstr "Finished rollback to ruleset "

msgid "same ruleset version"
msgstr "Not Available"

msgid "second(s)"
msgstr "seconds"

msgid "select {} strategy does not exist,please reselect."
msgstr ""

msgid "self-learning: continue learning (Host: {})"
msgstr "Self-learning: continued learning (Host: {})"

msgid "self-learning: continue learning (Host: {}, path: {})"
msgstr "Self-learning: continued learning (Host: {}, path: {})"

msgid "self-learning: end learning task"
msgstr "Self-learning: stopped learning task"

msgid "self-learning: modify learning task"
msgstr "Self-learning: modified learning task"

msgid "self-learning: relearning (Host: {})"
msgstr "Self-learning: relearned (Host: {})"

msgid "self-learning: relearning (Host: {}, path: {})"
msgstr "Self-learning: relearned (Host: {}, path: {})"

msgid "self-learning: start learning task"
msgstr "Self-learning: started learning task"

msgid "self-param-learning: change learning task"
msgstr "Parameter Self-Learning: modified learning"

msgid "self-param-learning: continue app site"
msgstr "Parameter Self-Learning: continued to learn application"

msgid "self-param-learning: continue path"
msgstr "Parameter Self-Learning: continued to learn API"

msgid "self-param-learning: end learning task"
msgstr "Parameter Self-Learning: stopped learning"

msgid "self-param-learning: relearning app site"
msgstr "Parameter Self-Learning: restarted to learn application"

msgid "self-param-learning: relearning path"
msgstr "Parameter Self-Learning: restarted to learn API"

msgid "self-param-learning: start learning task"
msgstr "Parameter Self-Learning: started learning"

msgid "sensitiveItems must be a list. sensitiveItems:{}"
msgstr ""

msgid "server interval error: 500"
msgstr ""

msgid "service"
msgstr ""

msgid "session id can not be empty"
msgstr ""

#, python-brace-format
msgid "set mobile certificate exception: {err}"
msgstr ""

msgid "sgip 1.2"
msgstr ""

msgid "share memory"
msgstr "Shared memory"

msgid "site shield"
msgstr "Site shield rule"

msgid "site_strategy"
msgstr ""

msgid "slash decode"
msgstr "Slash"

msgid "sniping rule"
msgstr "Sniping rule"

msgid "space"
msgstr ""

msgid "start time can not be empty"
msgstr ""

msgid "start_time == end_time"
msgstr ""

msgid "static resource exceed max count"
msgstr "Static Resource exceed max count"

msgid "static resource format error"
msgstr "Static Resource format error"

msgid "static resource include invalid character"
msgstr "Static Resource include invalid character"

msgid "static resource list can not repeat"
msgstr "Static Resource list can not repeat"

msgid "staticBotDetection"
msgstr "Static Bot Detection"

msgid "status of all APP blacklist rules"
msgstr "status of all app blacklist rules"

msgid "status of all APP counter rules"
msgstr "status of all app counter rules"

msgid "status of all APP high frequent rules"
msgstr "status of all app high frequency rules"

msgid "status of all APP reputation rules"
msgstr "status of all app reputation rules"

msgid "status of all bad behavior rules"
msgstr "status of all user behavior rules"

msgid "status of all blacklist rules"
msgstr ""

msgid "status of all counter rules"
msgstr ""

msgid "status of all high frequent rules"
msgstr "status of all high frequency rules"

msgid "status of all new comer rules"
msgstr "status of all new user rules"

msgid "status of all reputation rules"
msgstr ""

msgid "status of all sniping rules"
msgstr ""

msgid "strategy id: {} is not digit"
msgstr ""

msgid "strategy id: {} is not exist,please re_select the strategy."
msgstr ""

msgid "strategy_id:{} does not exist"
msgstr ""

msgid "submit status failed."
msgstr ""

msgid "succeed to threat intelligence db"
msgstr "Verified CTI Package"

msgid "succeed to verify llm_corpus"
msgstr "Verified LLM Corpus"

msgid "succeed to verify modelfile"
msgstr "Uploaded LLM Package"

msgid "succeed to verify ruleset"
msgstr "Verified ruleset"

msgid "success"
msgstr ""

msgid "system CPU's usage"
msgstr ""

msgid "system memory's usage"
msgstr ""

msgid "test failure."
msgstr ""

msgid "test successfully."
msgstr ""

msgid "th"
msgstr ""

msgid "the access path supports parameters but not regular expressions."
msgstr ""
"The path with parameters for health check is allowed, but not the one "
"specified using a regular expression."

msgid "the length of custom UA was limited to 256."
msgstr ""

msgid "threat intelligence db failed to install"
msgstr ""

#, python-brace-format
msgid ""
"threat intelligence engine version {0} doesn't support the threat "
"intelligence db {1}. Please select  the collect threat intelligence version"
msgstr ""

#, python-brace-format
msgid ""
"threat intelligence engine version {0} doesn't support the threat "
"intelligence db {1}. The minimum threat intelligence db version is {2}. "
"Please select correct threat intelligence db"
msgstr ""

msgid "time(s)"
msgstr ""

msgid "timeout(second)"
msgstr "Timeout(seconds)"

msgid "to"
msgstr "of"

msgid "trigger timer"
msgstr ""

msgid "unknow speed"
msgstr ""

msgid "unknown error"
msgstr ""

msgid "unknown mode."
msgstr ""

msgid "update_config"
msgstr ""

msgid "upgrade server is unreachable!"
msgstr "The server is unreachable"

msgid "url decode"
msgstr "URL"

msgid "user input payload"
msgstr ""

msgid "utf7 decode"
msgstr "UTF-7"

msgid "vulnerability attack request"
msgstr "Injection Requests"

msgid "waf rule file uploaded successfully"
msgstr "Uploaded Successfully"

msgid "waf rule file uploaded unsuccessfully"
msgstr "Upload failed"

msgid "waf ruleset does not exist"
msgstr "WAF ruleset does not exist"

msgid "waf ruleset failed to install"
msgstr "Failed to install WAF ruleset"

msgid "waf ruleset succeed to install"
msgstr "Installed WAF ruleset successfully"

msgid "waf site strategy"
msgstr "WAF site strategy"

msgid "waf update config"
msgstr "WAF ruleset update configurations"

#, python-brace-format
msgid "waf {0}"
msgstr "{0}"

#, python-brace-format
msgid "waf {0} module strategy"
msgstr "WAF {0} module strategy"

#, python-brace-format
msgid "waf {0} strategy"
msgstr "{0} strategy"

#, python-brace-format
msgid "waf {0} syntax strategy"
msgstr "WAF {0} strategy"

#, python-brace-format
msgid "waf {0}{1} strategy Application Site"
msgstr "Bonding {0} strategy {1} to websites"

msgid "waf_strategy is null"
msgstr "WAF strategy is null"

msgid "waf_strategy: {} is not digit"
msgstr "WAF strategy: {} is not digit"

msgid "waf_strategy: {} is not exist in waf site strategy"
msgstr "WAF strategy: {} is not exist in WAF site strategy"

msgid "watermark rules have been modified"
msgstr ""

msgid "web_filter"
msgstr ""

msgid "webconsole_custom_theme"
msgstr ""

msgid "websocket path cannot be blank!"
msgstr "websocket path is required."

msgid ""
"worker_connections is used to specify the maximum number of connections that"
" each worker process can handle. Calculation formula: (Required Single "
"Machine Concurrent Connections * 2 / CPU Cores) + Appropriate Redundancy. "
"Note: High worker_connections can result to high memory usage. Please ensure"
" sufficient memory to avoid impacting the concurrent connection count."
msgstr ""

msgid "xml Parse"
msgstr "XML"

msgid "zip include file exceed 500."
msgstr ""

msgid "zip include invalid file."
msgstr ""

#, python-brace-format
msgid "{0} Chinese characters or {1} English characters"
msgstr ""

#, python-brace-format
msgid "{0} Command line page failed."
msgstr ""

#, python-brace-format
msgid "{0} Failed to change website name."
msgstr ""

msgid "{0} Scene Error: key({}) = v({}), msg:{}"
msgstr "{0} Scenario error: key({}) = v({}), msg:{}"

#, python-brace-format
msgid "{0} advanced verify failed"
msgstr "{0} advanced verification failed"

#, python-brace-format
msgid "{0} can be achieved with a single node or a cluster."
msgstr ""

#, python-brace-format
msgid "{0} cannot be blank."
msgstr "{0} is reuqired."

#, python-brace-format
msgid "{0} characters can be entered at most."
msgstr ""

#, python-brace-format
msgid "{0} characters has exceeded the maximum of {1}."
msgstr ""

#, python-brace-format
msgid "{0} data is checked. Are you sure to delete?"
msgstr "Are you sure to delete the {0} item(s) selected??"

#, python-brace-format
msgid "{0} has been cleared."
msgstr ""

#, python-brace-format
msgid "{0} is disabled"
msgstr ""

#, python-brace-format
msgid "{0} is enabled"
msgstr ""

#, python-brace-format
msgid "{0} list has already exceed limit {1}."
msgstr ""

#, python-brace-format
msgid "{0} list has already reached limit {1}."
msgstr ""

#, python-brace-format
msgid "{0} must be a number between {1} and {2}."
msgstr ""

#, python-brace-format
msgid "{0} must be an integer between {1} and {2}."
msgstr ""

#, python-brace-format
msgid "{0} prefix length supports {1} ~ {2}"
msgstr "{0} prefix length: {1} ~ {2}"

#, python-brace-format
msgid "{0} to {1} characters"
msgstr ""

#, python-brace-format
msgid "{0} web filtering failed"
msgstr ""

#, python-brace-format
msgid "{0} {1} are not supported."
msgstr ""

#, python-brace-format
msgid "{0}: There are duplicate configuration items with row {1} request url."
msgstr "Duplicates are found."

#, python-brace-format
msgid "{Login}"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of Nginx CPU is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of Nginx memory is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of partition '/' is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of partition '/var' is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of partition root is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of partition var is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of system CPU is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid ""
"{current_value}% of system memory is used, exceeding the limit of "
"{target_value}%"
msgstr ""

#, python-brace-format
msgid "{node_ip} restore the factory configuration."
msgstr ""

#, python-brace-format
msgid ""
"{trigger_reason}, and has exceeded th 14-day grace period on {grace_time}. "
"The system is out of service. Please restore the abnormal node or update "
"license ASAP."
msgstr ""

#, python-brace-format
msgid ""
"{trigger_reason}. The 14-day grace period has entered. Please restore the "
"abnormal node or update license before {grace_time}, or protect system will "
"be out of service."
msgstr ""

msgid "{}"
msgstr ""

msgid "{} ({}) format error"
msgstr ""

msgid "{} API APPs"
msgstr " {} Application(s)"

msgid "{} API(s): "
msgstr ""

msgid "{} APIs"
msgstr " {} API(s)"

msgid "{} Application(s): {}"
msgstr ""

msgid "{} Manual Rule"
msgstr ""

msgid "{} Pii Request Detection"
msgstr " detection of PII requests for {}"

msgid "{} Pii Response Block"
msgstr " blocking of responses for PII {}"

msgid "{} Pii Response Detection"
msgstr " detection of PII responses for {}"

msgid "{} Pii Response Mask"
msgstr " response masking for PII {}"

msgid "{} cant not repeat"
msgstr ""

msgid "{} config Error"
msgstr ""

msgid "{} does not exist: {}"
msgstr ""

msgid "{} field is not allowed to empty"
msgstr ""

msgid "{} format error, actual type: {}"
msgstr ""

msgid "{} format error: {}"
msgstr ""

msgid "{} ignored list APIs"
msgstr " {} ignored API(s)"

msgid "{} includes invalid character(s)."
msgstr ""

msgid "{} is out of the range"
msgstr ""

msgid "{} pii types request detection"
msgstr " detection of requests for {} PII types"

msgid "{} pii types response block"
msgstr " blocking of resposes for {} PII types"

msgid "{} pii types response detection"
msgstr " detection of responses for {} PII types"

msgid "{} pii types response mask"
msgstr " response masking for {} PII types"

msgid "{} strategy Error: key({}) = v({}), msg:{}"
msgstr ""

msgid "{} to {}"
msgstr ""

msgid "{} update {} status to {}"
msgstr "{} updated status \"{}\" to \"{}\""

msgid "{} {}"
msgstr ""

msgid "{}'s {} learning results"
msgstr ""

#, python-brace-format
msgid "第{0}行：\\"
msgstr ""

msgctxt "FlowFilter"
msgid "Body regular expression"
msgstr "Body regular expression "

msgctxt "FlowFilter"
msgid "Collection"
msgstr "Capture"

msgctxt "FlowFilter"
msgid "Dest IP"
msgstr "Destination IP"

msgctxt "FlowFilter"
msgid "Dest Port"
msgstr "Destination Port"

msgctxt "FlowFilter"
msgid "Domain"
msgstr "Domain "

msgctxt "FlowFilter"
msgid "Not Collection"
msgstr "Do not Capture"

msgctxt "FlowFilter"
msgid "Path"
msgstr "Path "

msgctxt "FlowFilter"
msgid "Path regular expression"
msgstr "Path regular expression "

msgctxt "FlowFilter"
msgid "Request Body"
msgstr "Request Body "

msgctxt "FlowFilter"
msgid "Request Method"
msgstr "Request Method "

msgctxt "FlowFilter"
msgid "Source IP"
msgstr "Source IP "

msgctxt "FlowFilter"
msgid "Source Port"
msgstr "Source Port "
