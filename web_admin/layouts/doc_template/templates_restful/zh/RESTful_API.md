---
pagetitle: 接口使用说明
---
{# 注释 #}

[TOC]

##### 关于本文档

本文档将对保护系统提供的RESTful API进行详细说明，为客户工程师调用API提供使用指导。

{% if cfg is inLayouts(["riversec","ngwaf","abd","asa"]) %}{# 开始不同layout不同章节介绍的判断 #}
本文档分为10个章节：

1. 第**1**章对RESTful API的调用方法进行说明介绍。

2. 第**2**章介绍如何通过API获取RCM支持。

3. 第**3**章介绍如何通过API配置保护站点。

4. 第**4**章介绍如何通过API配置全局设置项。

5. 第**5**章介绍如何通过API配置{{ cfg | waf_name }}防护策略。

6. 第**6**章介绍如何通过API配置“可编程对抗”高级规则。

7. 第**7**章介绍如何通过API对客户自身的API资产进行管理配置。

8. 第**8**章介绍如何通过API查询系统中的告警信息。

9. 第**9**章介绍如何通过API配置“地理位置纠正”规则。

10. 第**10**章介绍配置过程中可能出现的错误码、错误码描述和含义。
{% else %}
本文档分为8个章节：

1. 第**1**章对RESTful API的调用方法进行说明介绍。

2. 第**2**章介绍如何通过API获取RCM支持。

3. 第**3**章介绍如何通过API配置保护站点。

4. 第**4**章介绍如何通过API配置全局设置项。

5. 第**5**章介绍如何通过API配置威胁检测防护策略。

6. 第**6**章介绍如何通过API查询系统中的告警信息。

7. 第**7**章介绍如何通过API配置“地理位置纠正”规则。

8. 第**8**章介绍配置过程中可能出现的错误码、错误码描述和含义。
{% endif %}{# 结束不同layout不同章节介绍的判断 #}

# RESTful API用法说明

## 使用规范

- 客户端使用GET、POST、PUT和DELETE等方法发起请求。

- POST和PUT方法的请求参数放在body中，使用JSON格式封装；GET和DELETE请求参数放在URL参数中传递。

- 为了保证信息完整不被篡改，请求使用基于令牌的签名认证机制，请求URL参数中必须附加Timestamp、Nonce、ID和Signature四个参数。

   |  参数名   | 说明                                                         |
   | :-------: | ------------------------------------------------------------ |
   | Timestamp | 当前时间（UTC时间戳）                                        |
   |   Nonce   | 随机生成的UUID字符串（可以通过Python的UUID库生成，请参考**【Python示例代码】**中的内容） |
   |    ID     | “系统API接口”页面中显示的TokenID                             |
   | Signature | 为保证请求不被篡改而生成的签名（签名的构造方法请参考下一小节的内容） |

## 构造签名

签名的构造方法如下：
```
Signature = HMAC(Token, CanonicalRequest)

CanonicalRequest =
    Method + '\n' +
    CanonicalURI + '\n' +
    CanonicalQueryString + '\n' +
    Timestamp + '\n' +
    Nonce + '\n' +
    ID + '\n' +
    HexEncode(Hash(RequestPayload))
```

- Token：“系统API接口”界面上显示的TokenValue（令牌值）

- Method：请求方法（POST/GET/PUT/DELETE）

- CanonicalURI：RFC-3986编码的URI字符串

- CanonicalQueryString：RFC-3986编码的请求参数字符串

- Timestamp：UTC时间戳

- Nonce：随机生成的UUID字符串

- ID：“系统API接口”界面上显示的TokenID，分别为api_admin和api_viewer，其中api_admin的权限为**可读写**，支持GET、POST、PUT和DELETE四种方法；api_viewer的权限为**只读**，只支持GET方法

- HexEncode(Hash(RequestPayload))：请求body的MD5哈希值


## 签名示例

假设请求如下：

`POST https://IP:port/api/v1/ip_black_list/switch HTTP/1.1`

请求body中的JSON：{"value": "off"}


1. Method：请求方法（大写）。

> POST

2. CanonicalURI：规范化URI（host:port后面的path部分，不包含`?`后面的请求参数），进行encodeURI操作，编码后如下。

> /api/v1/ip_black_list/switch

3. CanonicalQueryString：URI中`?`后的请求参数，先对参数按照key值进行升序排序，重复key按值进行排序，对每个key和value进行encodeURI编码，使用**&**将排序好的参数拼接起来，示例中没有参数，为空。

> ''

4. Timestamp：请求时间（UTC时间戳）。

> 1567739882

5. Nonce：UUID。

> 93cce320-8180-4a54-b58e-62805060adae

6. ID

> api_admin

7. 哈希值：对body进行hash（我们采用MD5，不包含body计算空字符串，最后转换为小写十六进制字符串）。

> md5('{"value": "off"}').hexdigest() = 4851bc309e682b6efe3804df45cf9749

8. 最终上述每一个字符串一行进行排列（注意顺序），得到如下字符串：

> POST
> /api/v1/ip_black_list/switch
>
> 1567739882
> 93cce320-8180-4a54-b58e-62805060adae
> api_admin
> 4851bc309e682b6efe3804df45cf9749

9. 使用Token作为key，对上述字符串进行HMAC（这里使用的Token值为"0a1de493ccef089479d502d384fd8b1f"），得到最终签名字符串：

> 09c2d61f43acc26e0fb6da2644bd6a9a0c82f74e18f616eba059a18f4f58021b

## 附加参数

将上例中的四个附加参数（Timestamp、Nonce、ID和Signature）添加到request_uri请求参数中去，得到最终的请求：

```
https://***********:20167/api/v1/ip_black_list/switch?
timestamp=1567739882&
nonce=93cce320-8180-4a54-b58e-62805060adae&
tokenid=api_admin&
signature=09c2d61f43acc26e0fb6da2644bd6a9a0c82f74e18f616eba059a18f4f58021b
```

## Python示例代码

将上面的计算签名，构造最终URL，向API网关发起请求的过程使用python实现的代码如下:

💡 采用Python2.x开发环境时，请参考以下代码。

```
#encoding=utf-8

import copy
import hashlib
import hmac
import os
import time
import uuid

import requests
from six.moves.urllib.parse import quote

API_GATEWAY = os.environ.get('API_GATEWAY', 'https://***********:20167')
API_TOKEN_ID = os.environ.get('API_TOKEN_ID', 'api_admin')
API_TOKEN_VALUE = os.environ.get('API_TOKEN_VALUE', '0a1de493ccef089479d502d384fd8b1f')

EMPTY_MD5_HASH = 'd41d8cd98f00b204e9800998ecf8427e'


def get_signed_url(method, path, params, body):
    # 构造标准签名字符串
    # method
    # path
    # query_string
    # timestamp
    # nonce
    # api_token_id
    # payload_hash
    cr = [
        method.upper(),
        quote(path.encode('utf-8'), safe='/-_~'),
    ]

    # encode params
    l = []

    for k, v in sorted(params.items()):
        l.append('%s=%s' % (quote(k, safe=''),
                            quote(v, safe='-_.~,')))
    cr.append('&'.join(l))
    cr.append(str(int(time.time())))
    cr.append(str(uuid.uuid4()))
    cr.append(API_TOKEN_ID)
    if method == 'get':
        cr.append(EMPTY_MD5_HASH)
    else:
        cr.append(hashlib.md5(body).hexdigest())

    sign_str = '\n'.join(cr)

    # 对标准签名字符串进行HMAC签名
    sign = hmac.new(API_TOKEN_VALUE.encode('utf-8'), sign_str.encode('utf-8'), hashlib.sha256).hexdigest()

    new_params = copy.deepcopy(params)
    new_params.update({
        'timestamp': cr[3],
        'nonce': cr[4],
        'tokenid': cr[5],
        'signature': sign,
    })
    # 构造最终请求字符串
    return API_GATEWAY + path + '?' + '&'.join(i + '=' + j for i, j in new_params.items())


if __name__ == '__main__':
    request_body = '''
    {"value": "off"}
    '''
    request_method = 'post'
    api_path = '/api/v1/ip_black_list/switch'
    api_params = dict()
    url = get_signed_url(request_method, api_path, api_params, request_body)

    if request_method == 'get':
        response = requests.request(request_method, url, verify=False, allow_redirects=True)
    else:
        response = requests.request(request_method, url, headers={'Content-Type': 'application/json'}, data=request_body, verify=False, allow_redirects=True)

    print(response.status_code)
    print(response.content)
```

💡 采用Python3.x开发环境时，请参考以下代码。

```
#encoding=utf-8

import copy
import hashlib
import hmac
import os
import time
import uuid
import json
import requests
from urllib.parse import quote


API_GATEWAY = os.environ.get('API_GATEWAY', 'https://***********:20167')
API_TOKEN_ID = os.environ.get('API_TOKEN_ID', 'api_admin')
API_TOKEN_VALUE = os.environ.get('API_TOKEN_VALUE', '0a1de493ccef089479d502d384fd8b1f')

EMPTY_MD5_HASH = 'd41d8cd98f00b204e9800998ecf8427e'


def get_signed_url(method, path, params, body):
    # 构造标准签名字符串
    # method
    # path
    # query_string
    # timestamp
    # nonce
    # api_token_id
    # payload_hash
    cr = [
        method.upper(),
        quote(path.encode('utf-8'), safe='/-_~'),
    ]

    # encode params
    l = []

    for k, v in sorted(params.items()):
        l.append('%s=%s' % (quote(k, safe=''),
                            quote(v, safe='-_.~,')))
    cr.append('&'.join(l))
    cr.append(str(int(time.time())))
    cr.append(str(uuid.uuid4()))
    cr.append(API_TOKEN_ID)

    if method == 'get':
        cr.append(EMPTY_MD5_HASH)
    else:
        cr.append(hashlib.md5(json.dumps(body).encode()).hexdigest())
    sign_str = '\n'.join(cr)

    # 对标准签名字符串进行HMAC签名
    sign = hmac.new(API_TOKEN_VALUE.encode('utf-8'), sign_str.encode('utf-8'), hashlib.sha256).hexdigest()
    new_params = copy.deepcopy(params)
    new_params.update({
        'timestamp': cr[3],
        'nonce': cr[4],
        'tokenid': cr[5],
        'signature': sign,
    })

    # 构造最终请求字符串
    return API_GATEWAY + path + '?' + '&'.join(i + '=' + j for i, j in new_params.items())


if __name__ == '__main__':
    request_body = {"value": "on"}

    request_method = 'post'
    api_path = '/api/v1/ip_black_list/switch'
    api_params = dict()
    url = get_signed_url(request_method, api_path, api_params, request_body)
    print(url)

    if request_method == 'get':
        response = requests.request(request_method, url, verify=False, allow_redirects=True)
    else:
        response = requests.request(request_method, url, headers={'Content-Type': 'application/json'},
                                    json=request_body, verify=False, allow_redirects=True)

    print(response.status_code)
    print(response.content)
```



# 获取RCM支持

## 获取免密登录管理界面的URL

- **URL：**

  `GET https://IP:port/api/v1/rcm/sso_token`

- **请求参数：**

  | 字段名   | 数据类型 | 是否必需 | 描述                          |
  | :--------: | :--------: | :--------: | ----------------------------- |
  | username | string   | 是       | 需要登录的用户名 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :--------: | :--------: | --------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功 |
  | err_msg | string   | 是       | 错误码描述            |
  | url     | string   | 否       | 用来访问管理界面的URL |

  URL的示例如下：
  `https://webconsole_ip:webconsole_port/rcm_login?sso_token=dXNlcm5hbWU9YWRtaW4mdG9rZW5pZD1hcGlfYWRtaW4mdGltZXN0YW1wPTE1NzQyNjE5NDgmbm9uY2U9ZTg2NGE4NDgtMmRiYS00ODY5LWFkY2EtYTkzNTExZGFhOWQ4JnNpZ25hdHVyZT00NDlkYTEwMzc5YzNhZmIyNjg5MWJmZTE4ZjY1MjEzYWFlNTg4NGZjNmM5MTdhMzFjNmE4YTQ3YWU2Y2RkMGMy`

  该URL为一次性，有效期为10秒，复用或者过期不再有效。

## 获取集群和节点的信息

- **URL：**

  `GET https://IP:port/api/v1/rcm/cluster_info`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功 |
  | err_msg | string   | 是       | 错误码描述              |
  | pre_version | string | 否 | 可供回滚的版本号 |
  | product_type | string | 否 | 产品类型 |
  | cluster_name | string | 否 | 集群名 |
  | cluster_id   | string | 是 | 集群ID |
  | nodes | dict | 否 | 集群中各个节点的信息：IP作为key，包括节点状态、版本、节点角色，参见`NodeStatus`字段的说明 |

  `NodeStatus`字段的说明：

  | 字段名  | 数据类型 | 是否必需 | 描述                                        |
  | :-----: | :------: | :------: | ------------------------------------------- |
  | status  |  string  |    是    | online表示此节点在线，offline表示离线       |
  | os_uuid |  string  |    否    | 当前节点唯一ID                              |
  |  role   |  array   |    是    | 节点角色                                    |
  | version |  string  |    否    | 节点当前版本                                |
  |  stats  |   dict   |    否    | 节点当前实时状态，参见`NodeStats`字段的说明 |

  `NodeStats`字段的说明：

  |       字段名        | 数据类型  | 是否必需 | 描述                            |
  |:----------------:|:-----:| :------: |-------------------------------|
  | cpu_time_percent | float |    是    | 当前CPU占用率                      |
  |  net_bits_sent   |  int  |    是    | 发送字节数                         |
  |  net_bits_recv   |  int  |    是    | 接收字节数                         |
  |  process_alive   | dict  |    是    | 进程存活状态                        |
  |   disk_percent   | dict  |    是    | 当前磁盘已挂载分区使用率，key为分区，value为使用率 |
  |   mem_percent    | float |    是    | 当前系统内存使用率                     |
  |    cpu_count     |  int  |    是    | CPU核数                         |
  | mem_total_bytes  |  int  |    是    | 系统内存总大小，单位字节                  |

## 升级集群

- **URL：**

  `POST https://IP:port/api/v1/rcm/upgrade`

- **请求参数：**

  升级包以二进制数据的形式附加在POST请求的请求体中，请求头添加{'Content-Type': 'application/octet-stream'}。

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :--------: | :--------: | --------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功 |
  | err_msg | string   | 是       | 错误码描述            |

## 回滚集群

- **URL：**

  `POST https://IP:port/api/v1/rcm/rollback`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :--------: | :--------: | --------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功 |
  | err_msg | string   | 是       | 错误码描述            |

# 配置保护站点

>**💡 提示：**
>
>POST和PUT请求参数通过请求body传递，使用JSON格式封装。当JSON数据内容存在反斜杠时，需要在字符串前加**r**避免转义，此时JSON数据中正则表达式的一些特殊写法，比如\\.和\\d等，需要写成\\\\.和\\\\d，而JSON支持的\\r和\\n等，则无需额外转义。

## 查询保护站点列表

- **URL：**

  `GET https://IP:port/api/v1/protected_sites`

- **请求参数：**

{% if cfg is has_mirror %}{# 镜像才有 #}
  | 字段名 |  数据类型  |     是否必需     | 描述                                                        |
  |:------------:|:------:|:---------------------------------------:|-----------------------------------------------------------|
  | with_default | string |      否       | 返回的站点列表是否要包括镜像部署下的default.com的信息，默认为否。取值为1或True时，表示返回。取值0或False时，不返回。 |
{% else %}

  无
{% endif %}{# 镜像才有 #}
- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                                         |
  | :-----: | :-------: | :-------: | ----------------------------------------------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功                                        |
  | err_msg | string   | 是       | 错误码描述                                                   |
  | sites   | list     | 是       | 站点列表（该字段存在子项，具体配置方法，参考下文对该字段的说明） |

  <font size=3>**▲ sites 子项**</font>

  | 字段名          | 数据类型 | 是否必需 | 描述                                                         |
  | :-------------: | :-------: | :-------: | ----------------------------------------------------------- |
  | id              | string   | 是       | 站点ID                                                       |
  | protocol        | string   | 是       | 监听的协议，取值为http、https                                |
  | port            | number   | 是       | 监听的端口                                                   |
  | type            | string   | 是       | 站点名称类型，取值范围：domain（域名）、ipv4、ipv6、regex（正则） |
  | site            | string   | 是       | 被保护站点的名称                                             |
  | name            | string   | 是       | 站点自定义名称                                               |
  | enable_site_conf| bool     | 是       | 是否启用当前站点配置                                               |
  | protection_mode | string   | 是       | 保护模式，取值范围：intercept（拦截模式）、monitor（监控模式）、passthrough（透传模式） |
  | waf_strategy    | dict     | 是       | {{ cfg | waf_name }}防护策略</br>**该字段存在子项，请参考下文对子字段的说明** |

  <font size=3>**▲ waf_strategy 子项**</font>

  | 字段名       | 数据类型 | 是否必需 | 描述                                                         |
  | :----------: | :-------: | :-------: | ----------------------------------------------------------- |
  | enable       | bool     | 是       |  是否启用{{ cfg | waf_name }}    |
  | monitor_only | bool     | 是       | {{ cfg | waf_name }}是否为监控模式            |
  | type         | string   | 是       | {{ cfg | waf_name }}站点策略类型，取值范围：basic（基础）、standard（标准）、strict（严格）、 monitor（监控）、monitor II（监控II）、monitor III（监控 III）、balanced（均衡）、bas（入侵与攻击模拟）、api（API防护）、customized_{id}（自定义站点策略ID） |

  > **💡 提示：**
  >
  > 查询保护站点列表返回的是所有站点的概要信息。

  查询保护站点列表的返回值示例如下：

```
{
  "err_no": 0,
  "err_msg": "Success",
  "sites": [
    {
      "protocol": "http",
      "name": "testfire",
      "enable_site_conf": true,
      "site": "www.testfire.net",
      "protection_mode": "intercept",
      "type": "domain",
      "port": 9000,
      "waf_strategy": {
        "enable": true,
        "type": "strict",
        "monitor_only": false
      },
      "id": "www.testfire.net_9000"
    },
    {
      "protocol": "http",
      "name": "",
      "enable_site_conf": false,
      "site": "***********",
      "protection_mode": "intercept",
      "type": "ipv4",
      "port": 80,
      "waf_strategy": {
        "enable": true,
        "type": "strict",
        "monitor_only": true
      },
      "id": "***********_80"
    }
  ]
}
```


## 新增保护站点

- **URL：**

  `POST https://IP:port/api/v1/protected_sites`

- **请求参数：**

  | 字段名                       | 数据类型 | 是否必需 | 描述                                                         |
  | :--------------------------: | :-------: | :-------: | ----------------------------------------------------------- |
  | type                         | string   | 是       | 站点名称类型，取值范围：domain（域名）、ipv4、ipv6、regex（正则） |
  | site                         | string   | 是       | 被保护站点的名称，如`www.testweb.com`                        |
  | protocol                     | string   | 是       | 监听的协议，取值为http、https{{ "（镜像部署下仅支持http）" if cfg is has_mirror }}                            |
  | port                         | number   | 是       | 监听的端口，取值范围：1~65535（不能为系统预留端口201xx）     |
  | enable_business_path         | bool     | 否       | 启用业务路径区分网站                                     |
  | business_path                | string   | 否       | 业务路径的正则表达式，启用业务路径区分网站后为必填            |
  | protection_mode              | string   | 是       | 保护模式，取值范围：intercept（拦截模式）、monitor（监控模式）、passthrough（透传模式） |
  | upstream                     | dict     | 是       | 上游服务器地址{{ "（反向代理部署下为必填，镜像部署下为可选）" if cfg is has_mirror }}</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | certificate                  | base64   | 否       | 当协议为https时，传入的证书                                  |
  | certificate_key              | base64   | 否       | 当协议为https时，传入的证书私钥                              |
  | enable_international_cert    | bool     | 否       | 是否启用国际证书 |
  | terminal_setting             | dict     | 否       | 浏览器访问用的协议和端口</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | name                         | string   | 否       | 客户自定义站点名称，最长不超过26个英文字符或者13个中文字符，不指定该字段时，默认值为空 |
  | enable_site_conf             | bool     | 否       | 是否启用当前站点配置。关闭时，将禁用当前站点配置，站点不再提供服务。 |
  | invalid_action               | string   | 否       | 无效请求响应策略。取值范围：reject（拒绝）、blank（返回空白页）、redirect（重定向）、drop（丢弃），不指定该字段时，默认值为reject |
  | invalid_action_redirect_path | string   | 否       | 无效请求响应重定向路径，当invalid_action为redirect时生效，不指定该字段时，默认值为/ |
  | ip_strategy                  | dict     | 否       | 基于IP的保护，不指定该字段时，默认为保护所有IP</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  {% if cfg is inLayouts(["riversec"]) %}{# 开始判断动态防护的区别 #}  | web_essential_strategy       | dict     | 否       | Web标准保护策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | web_power_strategy           | dict     | 否       | Web高级保护策略（购买了该机制的用户配置才会生效）</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  {% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}  | web_primary_strategy         | dict     | 否       | 动态防护策略（购买了该机制的用户配置才会生效）</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  {% endif %}{# 结束判断动态防护的区别 #}
  | waf_strategy                 | dict     | 否       | {{ cfg | waf_name }}防护策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | ai_waf_strategy              | dict     | 否       | AI-{{ cfg | waf_name }}防护策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | static_resource_list         | string   | 否       | 静态资源后缀名，仅支持字母、数字、下划线（\_）、中横线（-）   |
  | gm_certificate               | dict     | 否       | 国密证书配置</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | src_ip_strategy              | dict     | 否       | 源IP获取策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | allowed_http_methods         | list     | 否       | 配置允许的HTTP请求方法，例如["GET", "POST"]|
  | self_health_check            | dict     | 否       | 配置对系统节点的健康检查，</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | internal_res_path            | string   | 否       | 内部资源路径，默认为/ |
  {% if cfg is inLayouts(["riversec","ngwaf"]) %}{# 开始判断仅限riversec和ngwaf内容 #}  | wechat_strategy             | dict     | 否        | 微信小程序防护策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | alipay_strategy             | dict     | 否        | 支付宝小程序防护策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  | mpaas_mpp_strategy          | dict     | 否        | 移动小程序防护策略</br>**该字段存在子项，具体配置方法，参考下文对该字段的说明** |
  {% endif %}{# 结束判断仅限riversec和ngwaf内容 #}

  > **💡 提示：**
  >
  > • protocol为https时，可选传入证书配置，如果不传则默认使用内置证书。新增站点后，可以在管理界面上更新证书，也可以通过修改站点配置的API更新证书，请参考**【修改单个保护站点的配置】**中的相应内容。
  >
  > {% if cfg is inLayouts(["riversec"]) %}• web_essential_strategy、waf_strategy、ai_waf_strategy中必须至少启用一项，同时，启用web_power_strategy时，web_essential_strategy也必须启用。{% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}• web_primary_strategy、waf_strategy、ai_waf_strategy中必须至少启用一项。{% else %}• waf_strategy、ai_waf_strategy中必须至少启用一项。{% endif %}


<font size=3>**▲ upstream子项**</font>

|    字段名     | 数据类型 | 是否必需 | 描述                                                         |
| :-----------: | :------: | :------: | ------------------------------------------------------------ |
|   protocol    |  string  |    否    | 协议，取值范围：http、https，不指定该字段时，默认值为http    |
| upstream_list |   list   |    是    | 上游服务器IP地址，包含以下几个子字段：</br>• enable，是否启用该上游服务器 </br>• ip（必填），上游服务器ip地址，支持IPv4 </br>• port{{ "（反向代理部署下为必填，镜像部署下为可选）" if cfg is has_mirror else "（必填）"}}，上游服务器端口号 </br>• weight（可选，默认为1），权重 |
| load_balance  |  string  |    否    | 负载均衡策略，取值范围：ip_hash（IP哈希）、round_robin（轮询）、cookie_sticky（Cookie会话保持），不指定该字段时，默认值为ip_hash |
| health_check  |   dict   |    否    | 上游健康检查配置，包含以下子字段：</br>• type，健康检查类型，取值范围：disable/tcp/http，默认值tcp </br>• interval，健康检查时间间隔，单位秒，在type为tcp或者http时有效，默认值5 </br>• retry_times，健康检查重试次数，type为tcp或者http时有效，默认值3 </br>• timeout，健康检查超时时间，单位秒，type为tcp或者http时有效，默认值5 </br>• http_mode，http健康检查模式，type为http时有效，默认值为path_ua，取值范围path_ua/custom_request </br>• path，http健康检查路径，http_mode为path_ua时有效，默认值/ </br>• user_agent，http健康检查时的User Agent，http_mode为path_ua时有效，默认值为Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)</br>• http_custom_request_header，http健康检查用户自定义请求头，http_mode为custom_request时有效，默认值为GET / HTTP/1.0\r\nUser-Agent:Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0) </br>• http_custom_request_body，http健康检查用户自定义请求体，http_mode为custom_request时有效。 |

>**💡 提示：**
>
>• 新增站点时，upstream_list中的上游服务器个数至少为1。
>
>• 当protocol取值为https时，health_check的type字段值不能为http。
>
>• 当upstream_list的ip字段值填写的域名时，不能启用健康检查模式，health_check的type字段值只能为disable。

<font size=3>**▲ terminal_setting 子项**</font>

|  字段名  | 数据类型 | 是否必需 | 描述                                                         |
| :------: | :------: | :------: | ------------------------------------------------------------ |
|  enable  |   bool   |    否    | 是否开启浏览器访问用的协议和端口，不指定该字段时，默认值为false |
| protocol |  string  |    否    | 协议，取值范围：http/https，开启本功能时必填                 |
|   port   |  number  |    否    | 终端端口号，开启本功能时必填                                 |

<font size=3>**▲ ip_strategy子项**</font>

|       字段名       | 数据类型 | 是否必需 | 描述                                                         |
| :----------------: | :------: | :------: | ------------------------------------------------------------ |
|        type        |  string  |    否    | IP保护策略，取值范围: all_ip（保护所有IP）、ip_white_list(不保护的IP名单)、ip_protection_list(仅保护的IP名单)，不指定该字段时，默认值为all_ip |
|   ip_white_list    |   list   |    否    | 不保护的ip名单，type为ip_white_list时必填，且至少要有一条有效数据，默认为空 |
| ip_protection_list |   list   |    否    | 仅保护的ip名单，type为ip_protection_list有效，且至少要有一条有效数据，默认为空 |

ip_white_list和ip_protection_list的字段值支持IP+子网掩码以及IP段两种形式，格式分别为：

-  [ip, mask, comments, "mask"]，例如 ["***********", "***************", "IP掩码", "mask"]；
-  [start_ip, end_ip, comments, "segment"]，例如["***********00", "*************", "IP范围", "segment"]

{% if cfg is inLayouts(["riversec"]) %}{# 开始判断标准/高级保护和基础保护的差异 #}
<font size=3>**▲ web_essential_strategy子项**</font>

|           字段名            | 数据类型 | 是否必需 | 描述                                             |
| :-------------------------: | :------: | :------: | ------------------------------------------------ |
|           enable            |   bool   |    否    | 是否启用标准保护，不指定该字段时，默认值为false  |
| automated_tool_interception |   bool   |    否    | 是否拦截自动化工具，不指定该字段时，默认值为true |
| crack_behavior_interception |   bool   |    否    | 是否拦截破解行为，不指定该字段时，默认值为true   |
|       prevent_scanner       |   bool   |    否    | 是否拦截扫描工具，不指定该字段时，默认值为false  |
|     request_white_list      |   list   |    否    | 请求白名单，不指定该字段时，默认值为空           |
|     response_white_list     |   list   |    否    | 响应白名单，不指定该字段时，默认值为空           |
|   ajax_token_bypass_list    |   list   |    否    | 不添加Ajax/Fetch请求URL令牌的名单，不指定该字段时，默认值为空           |
|    ajax_token_path_list     |   list   |    否    | 需要添加Ajax/Fetch请求URL令牌的名单，不指定该字段时，默认值为空           |
|    ajax_token_path_type     |   number |    否    | 指定ajax_token_bypass_list和ajax_token_path_list哪个生效（0表示ajax_token_bypass_list生效，1表示ajax_token_path_list生效），不指定该字段时，默认值为0|
- request_white_list字段值格式为[url, comments, method, case sensitive, selected type]，其中url填写正则表达式，comments为备注信息，method表示是否仅限GET请求（取值为get或all），case sensitive表示是否忽略大小写（取值为true或false），selected type用于指定url字段的含义（取值范围：0-URL正则表达式、1-路径后缀名、2-路径开头、3-路径结尾、4-路径包含、5-请求头User-Agent包含）。取值示例：["^/abc/$", "不保护abc路径请求", "all", true, 0]；
- response_white_list字段值格式为[url, comments, case sensitive, selected type], 其中url填写正则表达式，comments为备注信息，case sensitive表示是否忽略大小写（取值为true或false），selected type用于指定url字段的含义（取值范围：0-URL正则表达式、1-路径后缀名、2-路径开头、3-路径结尾、4-路径包含）。取值示例：["/test", "不保护test及其子路径上的响应", true, 2]；
- ajax_token_bypass_list字段格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写（取值为true或false）。取值示例：["/ajax_path1", "此路径不添加Ajax令牌", true]；
- ajax_token_path_list字段格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写（取值为true或false）。取值示例：["/ajax_path2", "此路径需要添加Ajax令牌", true]。

<font size=3>**▲ web_power_strategy子项**</font>

|         字段名          | 数据类型 | 是否必需 | 描述                                                                       |
| :---------------------: | :------: | :------: | -------------------------------------------------------------------------- |
|         enable          |   bool   |    否    | 是否启用高级保护，不指定该字段时，默认值为false                            |
|     form_encryption     |   bool   |    否    | 是否启用Form表单内容混淆，不指定该字段时，默认值为false                    |
|    cookie_encryption    |   bool   |    否    | 是否启用客户Cookie内容加密，不指定该字段时，默认值为false                  |
|    enable_ajax_req_body_enc    |   bool   |    否    | 是否启用Ajax请求Body加密，不指定该字段时，默认值为false                  |
|    enable_ajax_resp_body_enc   |   bool   |    否    | 是否启用Ajax响应Body加密，不指定该字段时，默认值为false                  |
|    verification_list    |   list   |    否    | 高级保护验证名单，不指定该字段时，默认值为空，仅对列表中的路径进行高级验证 |
|   encapsulation_list    |   list   |    否    | 高级保护封装名单，不指定该字段时，默认值为空，仅对列表中的路径进行高级封装 |
| ajax_req_body_enc_list  |   list   |    否    | Ajax/Fetch请求内容混淆名单，不指定该字段时，默认值为空 |
|    ajax_referer_list    |   list   |    否    | 发出Ajax/Fetch请求的页面名单，不指定该字段时，默认值为空 |
| ajax_resp_body_enc_list |   list   |    否    | Ajax/Fetch响应内容混淆名单，不指定该字段时，默认值为空 |

- verification_list字段值格式为[url, comments, method, case sensitive]，其中url填写正则表达式，comments为备注信息，method为请求方法，可以为all或者post，case sensitive表示是否忽略大小写，为布尔值。取值示例：["^/cde/$", "对cde路径请求进行高级验证", "all", true]；
- encapsulation_list字段值格式为[url, comments, case sensitive], 其中url填写正则表达式，comments为备注信息，case sensitive表示是否忽略大小写，为布尔值。取值示例：["^/test$", "对test路径响应进行高级封装", true]；
- ajax_req_body_enc_list字段值格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写，为布尔值。取值示例：["^/ajax1$", "对此路径发起的Ajax请求的Body做混淆", true]；
- ajax_resp_body_enc_list字段值格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写，为布尔值。取值示例：["^/ajax1$", "对此路径的Ajax响应的Body做混淆", true]；
- ajax_referer_list 仅在此列表指定路径的页面上发起的Ajax请求，才会对其响应Body做混淆，字段值格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写，为布尔值。取值示例：["^/ajax1$", "对此路径的Ajax响应的Body做混淆", true]。

{% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}
<font size=3>**▲ web_primary_strategy子项**</font>

|           字段名            | 数据类型 | 是否必需 | 描述                                             |
| :-------------------------: | :------: | :------: | ------------------------------------------------ |
|           enable            |   bool   |    否    | 是否启用动态防护，不指定该字段时，默认值为false  |
| automated_tool_interception |   bool   |    否    | 是否拦截自动化工具，不指定该字段时，默认值为true |
| crack_behavior_interception |   bool   |    否    | 是否拦截破解行为，不指定该字段时，默认值为true   |
|     request_white_list      |   list   |    否    | 请求白名单，不指定该字段时，默认值为空           |
|     response_white_list     |   list   |    否    | 响应白名单，不指定该字段时，默认值为空           |
|   ajax_token_bypass_list    |   list   |    否    | 不添加Ajax/Fetch请求URL令牌的名单，不指定该字段时，默认值为空           |
|    ajax_token_path_list     |   list   |    否    | 需要添加Ajax/Fetch请求URL令牌的名单，不指定该字段时，默认值为空           |
|    ajax_token_path_type     |   number |    否    | 指定ajax_token_bypass_list和ajax_token_path_list哪个生效（0表示ajax_token_bypass_list生效，1表示ajax_token_path_list生效），不指定该字段时，默认值为0|

- request_white_list字段值格式为[url, comments, method, case sensitive, selected type]，其中url填写正则表达式，comments为备注信息，method表示是否仅限GET请求（取值为get或all），case sensitive表示是否忽略大小写（取值为true或false），selected type用于指定url字段的含义（取值范围：0-URL正则表达式、1-路径后缀名、2-路径开头、3-路径结尾、4-路径包含、5-请求头User-Agent包含）。取值示例：["^/abc/$", "不保护abc路径请求", "all", true, 0]；
- response_white_list字段值格式为[url, comments, case sensitive, selected type], 其中url填写正则表达式，comments为备注信息，case sensitive表示是否忽略大小写（取值为true或false），selected type用于指定url字段的含义（取值范围：0-URL正则表达式、1-路径后缀名、2-路径开头、3-路径结尾、4-路径包含）。取值示例：["/test", "不保护test及其子路径上的响应", true, 2]；
- ajax_token_bypass_list字段格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写（取值为true或false）。取值示例：["/ajax_path1", "此路径不添加Ajax令牌", true]；
- ajax_token_path_list字段格式为[url, comments, case sensitive]，其中url填写为正则表达式，comments为备注信息，case sensitive表示是否忽略大小写（取值为true或false）。取值示例：["/ajax_path2", "此路径需要添加Ajax令牌", true]。
{% endif %}{# 结束判断标准/高级保护和基础保护的差异 #}

<font size=3>**▲ waf_strategy子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    否    | 是否启用{{ cfg | waf_name }}，不指定该字段时，默认值为false                   |
| monitor_only |   bool   |    否    | {{ cfg | waf_name }}模块是否单独开启监控模式（该功能仅在protection_mode为intercept时才有效）不指定该字段时，默认值false |
|     type     |  string  |    否    | {{ cfg | waf_name }}站点策略类型，取值范围：basic（基础）、standard（标准）、strict（严格）、 monitor（监控）、monitor II（监控II）、monitor III（监控 III）、balanced（均衡）、bas（入侵与攻击模拟）、api（API防护）、customized_{id}（自定义站点策略ID）。默认值为{{ "api" if cfg is inLayouts(["abd","asa"]) else "basic" }}。 |
|  white_list  |   list   |    否    | {{ cfg | waf_name }}白名单                                                    |

* white_list字段值格式为[url, rule id, comments]，其中url为路径，不支持正则表达式，rule id表示{{ cfg | waf_name }}规则ID，为数字字符串，url和rule id至少填写一项，comment为备注，取值示例：["/waf_path", "1011", "waf_path路径1011规则不生效"]。

<font size=3>**▲ ai_waf_strategy子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    否    | 是否启用AI-{{ cfg | waf_name }}，不指定该字段时，默认值为false                |
| monitor_only |   bool   |    否    | AI-{{ cfg | waf_name }}模块是否单独开启监控模式（该功能仅在protection_mode为intercept时才有效）不指定该字段时，默认值false |
|  white_list  |   list   |    否    | AI-{{ cfg | waf_name }}白名单                                                 |

- white_list字段值格式为[url, comments, case sensitive]，其中url为路径，不支持正则表达式，case sensitive表示是否忽略大小写，为布尔值。取值示例：["/ai_waf_path", "不保护ai_waf_path路径", true]。


<font size=3>**▲ gm_certificate子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    是    | 是否启用国密                |
|    type    |   string   |    是    | 配置上传证书或者内置证书，取值为 "upload" 或 "builtin"                |
|    certificate    |   base64   |    否    | 国密证书，当type指定为"upload"时，必须配置此字段 |
|    certificate_key    |   base64   |    否    | 国密私钥，当type指定为"upload"时，必须配置此字段 |
|    enc_certificate    |   base64   |    否    | 加密国密证书，当type指定为"upload"时，必须配置此字段 |
|    enc_certificate_key    |   base64   |    否    | 加密国密私钥，当type指定为"upload"时，必须配置此字段 |


<font size=3>**▲ src_ip_strategy子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    use_global_setting    |   bool   |    是    | 是否从全局设置中继承源IP的获取方式                |
|    src_ip_strategy_list    |   list   |    否    | 当不从全局设置中继承时，必须配置此字段      |

**src_ip_strategy_list** 列表元素为一个对象，包含以下属性:

- `src_ip_from_type` 必须提供，取值如下：
    - "Custom"：从自定义HTTP Header中取源IP
    - "X-Forwarded-For"：从请求头X-Forwarded-For中取源IP
    - "X-Real-IP"：从请求头X-Real-IP 中取源IP
    - "\__SRCIP_TCPIP__"：从IP层取源IP
- `src_ip_customed_name` 当`src_ip_from_type`取值为 "Custom" 时，必须配置此字段，默认为空字符串
- `xff_position` 当``src_ip_from_type`取值为"X-Forwarded-For"时，必须配置此字段，取值范围如下：
    - "first"：取X-Forwarded-For值的IP
    - "penultimate"：取X-Forwarded-For值的取倒数第二个IP
    - "last"：取X-Forwarded-For值的最后一个IP


<font size=3>**▲ self_health_check子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    是    | 是否启用对系统节点的健康检查           |
|    path      |   string | 否      | 健康检查的请求路径，当enable为true时，必须配置此字段    |

{% if cfg is inLayouts(["riversec","ngwaf"]) %}{# 开始判断小程序字段显示的差异 #}
<font size=3>**▲ wechat_strategy子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    是    | 是否启用微信小程序保护策略                |
|    enable_request_body_encrypt      |   bool | 否      | 是否启用请求body加密，默认启用    |
|    enable_response_body_encrypt      |   bool | 否      | 是否启用响应body加密，默认启用    |
|    request_white_list  | list      |否| 请求白名单，格式如：[['/path1', '路径1'], ['/path2', '路径2']] |

<font size=3>**▲ alipay_strategy子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    是    | 是否启用支付宝小程序保护策略                |
|    enable_request_body_encrypt      |   bool | 否      | 是否启用请求body加密，默认启用    |
|    enable_response_body_encrypt      |   bool | 否      | 是否启用响应body加密，默认启用    |
|    request_white_list  | list      | 否 |请求白名单，格式如：[['/path1', '路径1'], ['/path2', '路径2']] |

<font size=3>**▲ mpaas_mpp_strategy子项**</font>

|    字段名    | 数据类型 | 是否必需 | 描述                                                         |
| :----------: | :------: | :------: | ------------------------------------------------------------ |
|    enable    |   bool   |    是    | 是否启用移动小程序保护策略                |
|    enable_request_body_encrypt      |   bool | 否      | 是否启用请求body加密，默认启用    |
|    enable_response_body_encrypt      |   bool | 否      | 是否启用响应body加密，默认启用    |
|    request_white_list  | list      | 否 | 请求白名单，格式如：[['/path1', '路径1'], ['/path2', '路径2']] |
{% endif %}{# 结束判断小程序字段显示的差异 #}

新增站点json body完整示例如下：

{% if cfg is inLayouts(["riversec"]) %}{# 开始判断上述代码在不同layout中显示不同 #}
```
{
    "type": "domain",
    "site": "www.testfire.net",
    "protocol": "https",
    "port": 443,
    "protection_mode": "intercept",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "gm_certificate": {
        "type": "upload",
        "enable": true,
        "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJYekNDQVFTZ0F3SUJBZ0lHQVl4aVllc3FNQW9HQ0NxQkhNOVZBWU4xTUI0eEhEQWFCZ05WQkFNTUUxQnkKYjNSbFkzUnBiMjVUZVhOMFpXMGdRMEV3SGhjTk1qTXhNakV6TURnMU16VXpXaGNOTXpNeE1qRXdNRGcxTXpVegpXakFZTVJZd0ZBWURWUVFEREExM2QzY3VhSFIwY0hNdVkyOXRNRmt3RXdZSEtvWkl6ajBDQVFZSUtvRWN6MVVCCmdpMERRZ0FFQzljdmRjR2tHbHZCSjRMZWJSY0Rlb3REMFByK0JGQ0RFU1ljWTlLMkpocld2L2xuN2QwbHYzOGQKVTVnY1BqRUMvanJxMnBmZDhhcjNBVVoyVmppdS9xTTBNREl3Q1FZRFZSMFRCQUl3QURBTEJnTlZIUThFQkFNQwpCc0F3R0FZRFZSMFJCQkV3RDRJTmQzZDNMbWgwZEhCekxtTnZiVEFLQmdncWdSelBWUUdEZFFOSkFEQkdBaUVBCjdzck5KdHo5TVFsSGpHVlpKakcrMDByVW9wb1ZtaldLZTM2WE5IUHQ0MGNDSVFEOWVoWUh0bFpURDdGSjA0TmkKNll5OHZzNlBTSTZJL2RGeDl5MFI2SUUxTVE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQpNSUlCbXpDQ0FVR2dBd0lCQWdJVWRsdUpRRkJsbU9yNS9IOVIrcklUZkRINXFjZ3dDZ1lJS29FY3oxVUJnM1V3CkhqRWNNQm9HQTFVRUF3d1RVSEp2ZEdWamRHbHZibE41YzNSbGJTQkRRVEFlRncweU16RXlNRFF3TnpNNU5ESmEKRncwek16RXlNREV3TnpNNU5ESmFNQjR4SERBYUJnTlZCQU1NRTFCeWIzUmxZM1JwYjI1VGVYTjBaVzBnUTBFdwpXVEFUQmdjcWhrak9QUUlCQmdncWdSelBWUUdDTFFOQ0FBUk5lZUtmWVdONDdRbTFUVlNjWHQxa2xpRGliSGY5CnhmYUM0MTQvbHY5a2dXd29pTGxWUDlyV3hBOWh2TGRHS096ZDY2TUNYZzJWRFJva3NRNXRxa3BObzEwd1d6QWQKQmdOVkhRNEVGZ1FVOU13K3J4cHNjSnBlTU5vaDJkWWFLUHdWQVd3d0h3WURWUjBqQkJnd0ZvQVU5TXcrcnhwcwpjSnBlTU5vaDJkWWFLUHdWQVd3d0RBWURWUjBUQkFVd0F3RUIvekFMQmdOVkhROEVCQU1DQVFZd0NnWUlLb0VjCnoxVUJnM1VEU0FBd1JRSWhBSzJOWXpvd0JnNmphamhsRW54YWhtcFJFUWFTdUhiOWFXa0NtTThweXk4SUFpQXUKVndXN2ZkVWxsckRBU3ZHSHg5V3JLZTdxVVdMSHJoMVc5ZTdoQUcxMGNnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=",
        "certificate_key": "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JR0hBZ0VBTUJNR0J5cUdTTTQ5QWdFR0NDcUJITTlWQVlJdEJHMHdhd0lCQVFRZ2VHU2drY1VieGNzODIzTmQKZEpZRnRPbkZqNHRKS0pzQ0FWY0hJTmVoUnhXaFJBTkNBQVFMMXk5MXdhUWFXOEVuZ3Q1dEZ3TjZpMFBRK3Y0RQpVSU1SSmh4ajByWW1HdGEvK1dmdDNTVy9meDFUbUJ3K01RTCtPdXJhbDkzeHF2Y0JSblpXT0s3KwotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tCg==",
        "enc_certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJSRENCNnFBREFnRUNBZ1lCakdKaDYyb3dDZ1lJS29FY3oxVUJnM1V3SGpFY01Cb0dBMVVFQXd3VFVISnYKZEdWamRHbHZibE41YzNSbGJTQkRRVEFlRncweU16RXlNVE13T0RVek5UTmFGdzB6TXpFeU1UQXdPRFV6TlROYQpNQmd4RmpBVUJnTlZCQU1NRFhkM2R5NW9kSFJ3Y3k1amIyMHdXVEFUQmdjcWhrak9QUUlCQmdncWdSelBWUUdDCkxRTkNBQVRNdTkrdC9tRkQrWkorbURlaDliYTNJbmk0QTdybnFFcjRhWnlwcThoTndsZlhLUzYxaGRSNGV5TEoKUWhKTVAzZFpPNE5qNlVwdjRMbkF6T2grUEt0Wm94b3dHREFKQmdOVkhSTUVBakFBTUFzR0ExVWREd1FFQXdJRApPREFLQmdncWdSelBWUUdEZFFOSkFEQkdBaUVBMXhTTzdYWWFnWUVoSWp4aXdWanVjdzI4a25CSHJLNGR4dVlCClVSZ1BIUFlDSVFENzU2MFRxVkR3cGZmZHRidTdZeEc2akxGOStxSkxEcXNJMk1LWUFUaFNxUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
        "enc_certificate_key": "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JR0hBZ0VBTUJNR0J5cUdTTTQ5QWdFR0NDcUJITTlWQVlJdEJHMHdhd0lCQVFRZ081SkEwSlBhWTlMcWFkQS8KTXZCekJ4elFoZnpwQ2MzTWVnSENwQ2ZxNDBXaFJBTkNBQVRNdTkrdC9tRkQrWkorbURlaDliYTNJbmk0QTdybgpxRXI0YVp5cHE4aE53bGZYS1M2MWhkUjRleUxKUWhKTVAzZFpPNE5qNlVwdjRMbkF6T2grUEt0WgotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tCg=="
    },
    "upstream": {
        "protocol": "http",
        "upstream_list": [
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            },
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            }
        ],
        "load_balance": "ip_hash",
        "health_check": {
            "type": "disable",
            "interval": 5,
            "retry_times": 3,
            "timeout": 5,
            "path": "/",
            "user_agent": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)"
        }
    },
    "terminal_setting": {
        "enable": false,
        "protocol": "http",
        "port": 80
    },
    "name": "testfire",
    "enable_site_conf": true,
    "invalid_action": "reject",
    "invalid_action_redirect_path": "/",
    "ip_strategy": {
        "type": "all_ip",
        "ip_white_list": [
            ["***********", "***************", "ip mask", "mask"],
            ["***********00", "*************", "ip range", "segment"]
        ],
        "ip_protection_list": [
            ["***********", "***************", "ip mask", "mask"],
            ["***********00", "*************", "ip range", "segment"]
        ]
    },
    "src_ip_strategy": {
        "use_global_setting": false,
        "src_ip_strategy_list": [
            {"src_ip_from_type": "Custom", "src_ip_customed_name": "test"},
            {"src_ip_from_type": "X-Forwarded-For", "xff_position": "last"},
            {"src_ip_from_type": "X-Real-IP"}
        ]
    },
    "self_health_check": {
        "enable": true,
        "path": "health_check"
    },
    "internal_res_path": "/lbpath",
    "allowed_http_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    "web_essential_strategy": {
        "enable": true,
        "automated_tool_interception": true,
        "crack_behavior_interception": true,
        "prevent_scanner": false,
        "request_white_list": [
            ["^/$", "root path", "all", true],
            ["^/abc/$", "abc path", "all", true]
        ],
        "response_white_list": [
            ["^/test$", "test", true]
        ]
    },
    "web_power_strategy": {
        "enable": true,
        "form_encryption": false,
        "cookie_encryption": false,
        "verification_list": [
            ["^/verified/", "verification path", "all", true]
        ],
        "encapsulation_list": [
            ["^/webpage_1/", "encapsulation path", true]
        ]
    },
    "waf_strategy": {
        "enable": false,
        "monitor_only": false,
        "type": "standard",
        "white_list": [
            ["/waf_path", "1011", "comments"]
        ]
    },
    "ai_waf_strategy": {
        "enable": false,
        "monitor_only": false,
        "white_list": [
            ["/ai_waf_path", "comments", true]
        ]
    },
    "static_resource_list": "7z,a,aac,amr,apk,ar,asm,avi,bac,backup,bak,bat,bin,bmp,bz2,c,cab,cache,cbz,ceb,cebx,cert,cfm,cmake,coffee,com,conf,config,cpp,crt,css,csv,dat,db,deb,default,dll,dmg,doc,docx,dot,ehp,eml,env,eot,et,exe,fla,flac,flc,flv,fon,font,fot,gdb,gho,gif,git,gitignore,gz,gzip,hlp,hpp,htaccess,htc,htpasswd,ico,image,inc,include,inf,ini,ins,ipa,iso,jar,java,jpeg,jpg,js,json,key,lib,lock,log,lua,lzma,m4a,manifest,map,md,md5,mdb,mid,mim,mkv,mod,mov,mp3,mp4,mpa,mpeg,mpg,mpp,msi,mysql,nil,numbers,obj,ocx,odp,ods,odt,ogg,olb,old,ole,otf,out,ova,pages,pas,passwd,pcap,pdf,pem,pgm,pgsql,pic,pl,pli,plist,png,pom,ppm,pps,ppt,pptx,properties,psd,pub,pwd,py,pyc,qcow2,qif,qtx,ra,ram,rar,rb,reg,res,rm,rmvb,rpm,rtf,rtmp,sbl,sfx,sh,sha,sha1,so,sql,sqlite,sqlite3,sqlitedb,svg,swa,swf,swp,sys,tar,taz,temp,tgz,tif,tiff,tmp,torrent,tpl,tsv,ttf,txt,vb,vsd,vss,vsw,vxd,war,wav,webm,webp,wim,wma,wmv,woff,woff2,wps,xbm,xls,xlsx,xml,xpm,xsl,xz,yaml,yml,z,zip"
}
```
{% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"])  %}
```
{
    "type": "domain",
    "site": "www.testfire.net",
    "protocol": "https",
    "port": 443,
    "protection_mode": "intercept",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "gm_certificate": {
        "type": "upload",
        "enable": true,
        "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJYekNDQVFTZ0F3SUJBZ0lHQVl4aVllc3FNQW9HQ0NxQkhNOVZBWU4xTUI0eEhEQWFCZ05WQkFNTUUxQnkKYjNSbFkzUnBiMjVUZVhOMFpXMGdRMEV3SGhjTk1qTXhNakV6TURnMU16VXpXaGNOTXpNeE1qRXdNRGcxTXpVegpXakFZTVJZd0ZBWURWUVFEREExM2QzY3VhSFIwY0hNdVkyOXRNRmt3RXdZSEtvWkl6ajBDQVFZSUtvRWN6MVVCCmdpMERRZ0FFQzljdmRjR2tHbHZCSjRMZWJSY0Rlb3REMFByK0JGQ0RFU1ljWTlLMkpocld2L2xuN2QwbHYzOGQKVTVnY1BqRUMvanJxMnBmZDhhcjNBVVoyVmppdS9xTTBNREl3Q1FZRFZSMFRCQUl3QURBTEJnTlZIUThFQkFNQwpCc0F3R0FZRFZSMFJCQkV3RDRJTmQzZDNMbWgwZEhCekxtTnZiVEFLQmdncWdSelBWUUdEZFFOSkFEQkdBaUVBCjdzck5KdHo5TVFsSGpHVlpKakcrMDByVW9wb1ZtaldLZTM2WE5IUHQ0MGNDSVFEOWVoWUh0bFpURDdGSjA0TmkKNll5OHZzNlBTSTZJL2RGeDl5MFI2SUUxTVE9PQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCi0tLS0tQkVHSU4gQ0VSVElGSUNBVEUtLS0tLQpNSUlCbXpDQ0FVR2dBd0lCQWdJVWRsdUpRRkJsbU9yNS9IOVIrcklUZkRINXFjZ3dDZ1lJS29FY3oxVUJnM1V3CkhqRWNNQm9HQTFVRUF3d1RVSEp2ZEdWamRHbHZibE41YzNSbGJTQkRRVEFlRncweU16RXlNRFF3TnpNNU5ESmEKRncwek16RXlNREV3TnpNNU5ESmFNQjR4SERBYUJnTlZCQU1NRTFCeWIzUmxZM1JwYjI1VGVYTjBaVzBnUTBFdwpXVEFUQmdjcWhrak9QUUlCQmdncWdSelBWUUdDTFFOQ0FBUk5lZUtmWVdONDdRbTFUVlNjWHQxa2xpRGliSGY5CnhmYUM0MTQvbHY5a2dXd29pTGxWUDlyV3hBOWh2TGRHS096ZDY2TUNYZzJWRFJva3NRNXRxa3BObzEwd1d6QWQKQmdOVkhRNEVGZ1FVOU13K3J4cHNjSnBlTU5vaDJkWWFLUHdWQVd3d0h3WURWUjBqQkJnd0ZvQVU5TXcrcnhwcwpjSnBlTU5vaDJkWWFLUHdWQVd3d0RBWURWUjBUQkFVd0F3RUIvekFMQmdOVkhROEVCQU1DQVFZd0NnWUlLb0VjCnoxVUJnM1VEU0FBd1JRSWhBSzJOWXpvd0JnNmphamhsRW54YWhtcFJFUWFTdUhiOWFXa0NtTThweXk4SUFpQXUKVndXN2ZkVWxsckRBU3ZHSHg5V3JLZTdxVVdMSHJoMVc5ZTdoQUcxMGNnPT0KLS0tLS1FTkQgQ0VSVElGSUNBVEUtLS0tLQo=",
        "certificate_key": "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JR0hBZ0VBTUJNR0J5cUdTTTQ5QWdFR0NDcUJITTlWQVlJdEJHMHdhd0lCQVFRZ2VHU2drY1VieGNzODIzTmQKZEpZRnRPbkZqNHRKS0pzQ0FWY0hJTmVoUnhXaFJBTkNBQVFMMXk5MXdhUWFXOEVuZ3Q1dEZ3TjZpMFBRK3Y0RQpVSU1SSmh4ajByWW1HdGEvK1dmdDNTVy9meDFUbUJ3K01RTCtPdXJhbDkzeHF2Y0JSblpXT0s3KwotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tCg==",
        "enc_certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUJSRENCNnFBREFnRUNBZ1lCakdKaDYyb3dDZ1lJS29FY3oxVUJnM1V3SGpFY01Cb0dBMVVFQXd3VFVISnYKZEdWamRHbHZibE41YzNSbGJTQkRRVEFlRncweU16RXlNVE13T0RVek5UTmFGdzB6TXpFeU1UQXdPRFV6TlROYQpNQmd4RmpBVUJnTlZCQU1NRFhkM2R5NW9kSFJ3Y3k1amIyMHdXVEFUQmdjcWhrak9QUUlCQmdncWdSelBWUUdDCkxRTkNBQVRNdTkrdC9tRkQrWkorbURlaDliYTNJbmk0QTdybnFFcjRhWnlwcThoTndsZlhLUzYxaGRSNGV5TEoKUWhKTVAzZFpPNE5qNlVwdjRMbkF6T2grUEt0Wm94b3dHREFKQmdOVkhSTUVBakFBTUFzR0ExVWREd1FFQXdJRApPREFLQmdncWdSelBWUUdEZFFOSkFEQkdBaUVBMXhTTzdYWWFnWUVoSWp4aXdWanVjdzI4a25CSHJLNGR4dVlCClVSZ1BIUFlDSVFENzU2MFRxVkR3cGZmZHRidTdZeEc2akxGOStxSkxEcXNJMk1LWUFUaFNxUT09Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
        "enc_certificate_key": "LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0tCk1JR0hBZ0VBTUJNR0J5cUdTTTQ5QWdFR0NDcUJITTlWQVlJdEJHMHdhd0lCQVFRZ081SkEwSlBhWTlMcWFkQS8KTXZCekJ4elFoZnpwQ2MzTWVnSENwQ2ZxNDBXaFJBTkNBQVRNdTkrdC9tRkQrWkorbURlaDliYTNJbmk0QTdybgpxRXI0YVp5cHE4aE53bGZYS1M2MWhkUjRleUxKUWhKTVAzZFpPNE5qNlVwdjRMbkF6T2grUEt0WgotLS0tLUVORCBQUklWQVRFIEtFWS0tLS0tCg=="
    },
    "upstream": {
        "protocol": "http",
        "upstream_list": [
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            },
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            }
        ],
        "load_balance": "ip_hash",
        "health_check": {
            "type": "disable",
            "interval": 5,
            "retry_times": 3,
            "timeout": 5,
            "path": "/",
            "user_agent": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)"
        }
    },
    "terminal_setting": {
        "enable": false,
        "protocol": "http",
        "port": 80
    },
    "name": "testfire",
    "enable_site_conf": true,
    "invalid_action": "reject",
    "invalid_action_redirect_path": "/",
    "ip_strategy": {
        "type": "all_ip",
        "ip_white_list": [
            ["***********", "***************", "ip mask", "mask"],
            ["***********00", "*************", "ip range", "segment"]
        ],
        "ip_protection_list": [
            ["***********", "***************", "ip mask", "mask"],
            ["***********00", "*************", "ip range", "segment"]
        ]
    },
    "src_ip_strategy": {
        "use_global_setting": false,
        "src_ip_strategy_list": [
            {"src_ip_from_type": "Custom", "src_ip_customed_name": "test"},
            {"src_ip_from_type": "X-Forwarded-For", "xff_position": "last"},
            {"src_ip_from_type": "X-Real-IP"}
        ]
    },
    "self_health_check": {
        "enable": true,
        "path": "health_check"
    },
    "internal_res_path": "/lbpath",
    "allowed_http_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS", "HEAD", "PATCH"],
    "web_primary_strategy": {
        "enable": true,
        "automated_tool_interception": true,
        "crack_behavior_interception": true,
        "request_white_list": [
            ["^/$", "root path", "all", true],
            ["^/abc/$", "abc path", "all", true, 0],
            ["exe", "suffix", "all", true, 1],
            ["abc", "url start with", "all", true, 2],
            ["end", "url end with", "all", true, 3],
            ["abc", "url include", "all", true, 4],
            ["firefox", "user agent include", "all", true, 5]
        ],
        "response_white_list": [
            ["^/test$", "test", true],
            ["^/abc/$", "abc path", true, 0],
            ["exe", "suffix", true, 1],
            ["abc", "url start with", true, 2],
            ["end", "url end with", true, 3],
            ["abc", "url include", true, 4]
        ]
    },
    "waf_strategy": {
        "enable": false,
        "monitor_only": false,
        "type": "standard",
        "white_list": [
            ["/waf_path", "1011", "comments"]
        ]
    },
    "ai_waf_strategy": {
        "enable": false,
        "monitor_only": false,
        "white_list": [
            ["/ai_waf_path", "comments", true]
        ]
    },
    "static_resource_list": "7z,a,aac,amr,apk,ar,asm,avi,bac,backup,bak,bat,bin,bmp,bz2,c,cab,cache,cbz,ceb,cebx,cert,cfm,cmake,coffee,com,conf,config,cpp,crt,css,csv,dat,db,deb,default,dll,dmg,doc,docx,dot,ehp,eml,env,eot,et,exe,fla,flac,flc,flv,fon,font,fot,gdb,gho,gif,git,gitignore,gz,gzip,hlp,hpp,htaccess,htc,htpasswd,ico,image,inc,include,inf,ini,ins,ipa,iso,jar,java,jpeg,jpg,js,json,key,lib,lock,log,lua,lzma,m4a,manifest,map,md,md5,mdb,mid,mim,mkv,mod,mov,mp3,mp4,mpa,mpeg,mpg,mpp,msi,mysql,nil,numbers,obj,ocx,odp,ods,odt,ogg,olb,old,ole,otf,out,ova,pages,pas,passwd,pcap,pdf,pem,pgm,pgsql,pic,pl,pli,plist,png,pom,ppm,pps,ppt,pptx,properties,psd,pub,pwd,py,pyc,qcow2,qif,qtx,ra,ram,rar,rb,reg,res,rm,rmvb,rpm,rtf,rtmp,sbl,sfx,sh,sha,sha1,so,sql,sqlite,sqlite3,sqlitedb,svg,swa,swf,swp,sys,tar,taz,temp,tgz,tif,tiff,tmp,torrent,tpl,tsv,ttf,txt,vb,vsd,vss,vsw,vxd,war,wav,webm,webp,wim,wma,wmv,woff,woff2,wps,xbm,xls,xlsx,xml,xpm,xsl,xz,yaml,yml,z,zip"
}
```
{% else %}
```
{
    "type": "domain",
    "site": "www.testfire.net",
    "protocol": "https",
    "port": 443,
    "protection_mode": "monitor",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
        "protocol": "http",
        "upstream_list": [
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            },
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            }
        ],
        "load_balance": "ip_hash",
        "health_check": {
            "type": "disable",
            "interval": 5,
            "retry_times": 3,
            "timeout": 5,
            "path": "/",
            "user_agent": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)"
        }
    },
    "terminal_setting": {
        "enable": false,
        "protocol": "http",
        "port": 80
    },
    "name": "testfire",
    "enable_site_conf": true,
    "invalid_action": "reject",
    "invalid_action_redirect_path": "/",
    "ip_strategy": {
        "type": "all_ip",
        "ip_white_list": [
            ["***********", "***************", "ip mask", "mask"],
            ["***********00", "*************", "ip range", "segment"]
        ],
        "ip_protection_list": [
            ["***********", "***************", "ip mask", "mask"],
            ["***********00", "*************", "ip range", "segment"]
        ]
    },
    "src_ip_strategy": {
        "use_global_setting": false,
        "src_ip_strategy_list": [
            {"src_ip_from_type": "Custom", "src_ip_customed_name": "test"},
            {"src_ip_from_type": "X-Forwarded-For", "xff_position": "last"},
            {"src_ip_from_type": "X-Real-IP"}
        ]
    },
    "waf_strategy": {
        "enable": false,
        "monitor_only": false,
        "type": "standard",
        "white_list": [
            ["/waf_path", "1011", "comments"]
        ]
    },
    "ai_waf_strategy": {
        "enable": false,
        "monitor_only": false,
        "white_list": [
            ["/ai_waf_path", "comments", true]
        ]
    },
    "static_resource_list": "7z,a,aac,amr,apk,ar,asm,avi,bac,backup,bak,bat,bin,bmp,bz2,c,cab,cache,cbz,ceb,cebx,cert,cfm,cmake,coffee,com,conf,config,cpp,crt,css,csv,dat,db,deb,default,dll,dmg,doc,docx,dot,ehp,eml,env,eot,et,exe,fla,flac,flc,flv,fon,font,fot,gdb,gho,gif,git,gitignore,gz,gzip,hlp,hpp,htaccess,htc,htpasswd,ico,image,inc,include,inf,ini,ins,ipa,iso,jar,java,jpeg,jpg,js,json,key,lib,lock,log,lua,lzma,m4a,manifest,map,md,md5,mdb,mid,mim,mkv,mod,mov,mp3,mp4,mpa,mpeg,mpg,mpp,msi,mysql,nil,numbers,obj,ocx,odp,ods,odt,ogg,olb,old,ole,otf,out,ova,pages,pas,passwd,pcap,pdf,pem,pgm,pgsql,pic,pl,pli,plist,png,pom,ppm,pps,ppt,pptx,properties,psd,pub,pwd,py,pyc,qcow2,qif,qtx,ra,ram,rar,rb,reg,res,rm,rmvb,rpm,rtf,rtmp,sbl,sfx,sh,sha,sha1,so,sql,sqlite,sqlite3,sqlitedb,svg,swa,swf,swp,sys,tar,taz,temp,tgz,tif,tiff,tmp,torrent,tpl,tsv,ttf,txt,vb,vsd,vss,vsw,vxd,war,wav,webm,webp,wim,wma,wmv,woff,woff2,wps,xbm,xls,xlsx,xml,xpm,xsl,xz,yaml,yml,z,zip"
}
```
{% endif %}  {# 结束判断上述代码在不同layout中显示不同 #}

- **返回值：**

  |  字段名   |  数据类型   | 是否必需 | 描述                   |
  | :-------: | :---------: | :------: | ---------------------- |
  |  err_no   |   number    |    是    | 错误码，0代表请求成功  |
  |  err_msg  |   string    |    是    | 错误码描述             |
  | err_extra | json string |    否    | 附加错误信息表         |
  |    id     |   string    |    否    | 成功时返回自动生成的id |

  - id：站点ID，创建站点时由保护系统自动生成，不能修改，集群内全局唯一。当站点名称为domain、ipv4、ipv6时，id 格式为【site + 监听端口】（例如：`www.testweb.com_80`）；当站点名称为正则表达式时，id 格式为【正则表达式MD5值 + 监听端口】（例如：a4b5c2def_80）。
  - id将作为查询指定站点、修改站点和删除站点的URL路径的一部分。

## 查询指定保护站点的详细配置

- **URL：**

  `GET https://IP:port/api/v1/protected_sites/{id}`

  > **💡 提示：**
  >
  > URL中的id为站点ID，该ID可通过查询保护站点列表获得。

- **请求参数：**

  无

- **返回值：**

  |            字段名            |  数据类型   | 是否必需 | 描述                                                         |
  | :--------------------------: | :---------: | :------: | ------------------------------------------------------------ |
  |            err_no            |   number    |    是    | 错误码，0代表请求成功                                        |
  |           err_msg            |   string    |    是    | 错误码描述                                                   |
  |          err_extra           | json string |    否    | 附加错误信息表                                               |
  |              id              |   string    |    否    | 站点ID                                                       |
  |             type             |   string    |    否    | 站名类型：domain（域名）、ipv4、ipv6、regex（正则）          |
  |             site             |   string    |    否    | 被保护站点的名称，如"www.testweb.com"                        |
  |           protocol           |   string    |    否    | 监听的协议（http/https）                                     |
  |             port             |   number    |    否    | 监听的端口                                                   |
  |       protection_mode        |   string    |    否    | 保护模式：intercept（拦截模式）、monitor（监控模式）、passthrough（透传模式） |
  |           upstream           |    dict     |    否    | 上游服务器地址                                               |
  |       terminal_setting       |    dict     |    否    | 外部端口偏移配置：{启用，协议，端口}                         |
  |             name             |   string    |    否    | 客户自定义站点名称                                           |
  |        enable_site_conf      |   bool      |    否    | 是否启用当前站点配置                                           |
  |        invalid_action        |   string    |    否    | 无效请求响应策略：reject（拒绝）、blank（返回空白页）、redirect（重定向）、drop（丢弃） |
  | invalid_action_redirect_path |   string    |    否    | 无效请求响应重定向路径                                       |
  |         ip_strategy          |    dict     |    否    | 基于IP的保护                                                 |
  {% if cfg is inLayouts(["riversec"]) %}  | web_essential_strategy       | dict     | 否       | Web标准保护策略 |
  | web_power_strategy           | dict     | 否       | Web高级保护策略 |
  {% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"])  %}  | web_primary_strategy         | dict     | 否       | 动态防护策略|
  {% endif %}
  |         waf_strategy         |    dict     |    否    | {{ cfg | waf_name }}保护策略                                                  |
  |       ai_waf_strategy        |    dict     |    否    | AI-{{ cfg | waf_name }}保护策略                                               |
  |     static_resource_list     |   string    |    否    | 静态资源后缀名       |

  >**💡 提示：**
  >
  >部分字段存在子字段，关于这些字段和字段值的含义，请参考**【新增保护站点】**中的相应内容。


## 修改单个保护站点的配置

- **URL：**

  `PUT https://IP:port/api/v1/protected_sites/{id}`

  > **💡 提示：**
  >
  > URL中的id为站点ID，该ID可通过查询保护站点列表获得。

- **请求参数：**

  |            字段名            | 数据类型 | 是否必需 | 描述                                                         |
  | :--------------------------: | :------: | :------: | ------------------------------------------------------------ |
  |         certificate          |  base64  |    否    | 当协议为https时，传入的证书                                  |
  |       certificate_key        |  base64  |    否    | 当协议为https时，传入的证书私钥                              |
  |       protection_mode        |  string  |    否    | 保护模式，取值范围：intercept（拦截模式）、monitor（监控模式）、passthrough（透传模式），默认值为intercept |
  |           upstream           |   dict   |    否    | 上游服务器地址                                               |
  |       terminal_setting       |   dict   |    否    | 外部端口偏移配置：{启用，协议，端口}                         |
  |             name             |  string  |    否    | 客户自定义站点名称，默认值为空，最长不超过26个英文字符或者13个中文字符 |
  |        enable_site_conf      |  bool    |    否    | 是否启用当前站点配置。关闭时，将禁用当前站点配置，站点不再提供服务 |
  |        invalid_action        |  string  |    否    | 无效请求响应策略。取值范围：reject（拒绝）、blank（返回空白页）、redirect（重定向）、drop（丢弃）" |
  | invalid_action_redirect_path |  string  |    否    | 无效请求响应重定向路径，当invalid_action为redirect时生效，不指定该字段时，默认值为/ |
  |         ip_strategy          |   dict   |    否    | 基于IP的保护，不指定该字段时，默认为保护所有IP               |
  {% if cfg is inLayouts(["riversec"]) %}  | web_essential_strategy       | dict     | 否       | Web标准保护策略 |
  | web_power_strategy           | dict     | 否       | Web高级保护策略（购买了该机制的用户配置才会生效）   |
  {% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}  | web_primary_strategy         | dict     | 否       | 动态防护策略（购买了该机制的用户配置才会生效） |
  {% endif %}
  |         waf_strategy         |   dict   |    否    | {{ cfg | waf_name }}保护策略               |
  |       ai_waf_strategy        |   dict   |    否    | AI-{{ cfg | waf_name }}保护策略            |
  |     static_resource_list     |  string  |    否    | 静态资源后缀名，仅支持字母、数字、下划线（_）、中横线（-）   |

  >**💡 提示：**
  >
  >• 部分字段存在子字段，关于这些字段和字段值的含义，请参考**【新增保护站点】**中的相应内容。
  >
  >• type、site、protocol、port字段在站点创建后不支持修改。

  修改站点json body示例：

{% if cfg is inLayouts(["riversec"]) %}{# 开始判断上述代码在不同layout下显示不同 #}
```
{
    "protection_mode": "passthrough",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
        "upstream_list": [
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            },
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            }
        ]
    },
    "web_essential_strategy": {
        "request_white_list": [
            ["^/$", "root path", "all", true],
            ["^/abc/$", "abc path", "all", true]
        ]
    }
}
```
{% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}
```
{
    "protection_mode": "passthrough",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
        "upstream_list": [
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            },
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            }
        ]
    },
    "web_primary_strategy": {
        "request_white_list": [
            ["^/$", "root path", "all", true],
            ["^/abc/$", "abc path", "all", true]
        ]
    }
}
```
{% else %}
```
{
    "protection_mode": "passthrough",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
        "upstream_list": [
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            },
            {
                "enable": true,
                "ip": "***********",
                "port": 8088
            }
        ]
    }
}
```
{% endif %}{# 结束判断上述代码在不同layout下显示不同 #}

> **💡 提示：**
>
> • dict类型的字段，可以仅修改其中某一个子字段，其他项的值不受影响，比如上例中的upstream为dict类型，仅修改了upstream_list子字段，其他子字段protocol、load_balance、health_check的值不受影响；
>
> • string、number和list类型的字段，已修改的内容将直接覆盖原有配置，比如上例中的upstream_list为list类型，修改后的两条IP配置将覆盖原有上游服务器配置。


- **返回值：**

  |  字段名   |  数据类型   | 是否必需 | 描述                  |
  | :-------: | :---------: | :------: | --------------------- |
  |  err_no   |   number    |    是    | 错误码，0代表请求成功 |
  |  err_msg  |   string    |    是    | 错误码描述            |
  | err_extra | json string |    否    | 附加错误信息表        |

## 删除指定保护站点

- **URL：**

  `DELETE https://IP:port/api/v1/protected_sites/{id}`

  > **💡 提示：**
  >
  > URL中的id为站点ID，该ID可通过查询保护站点列表获得。

- **请求参数：**

  无

- **返回值：**

  |  字段名   |  数据类型   | 是否必需 | 描述                  |
  | :-------: | :---------: | :------: | --------------------- |
  |  err_no   |   number    |    是    | 错误码，0代表请求成功 |
  |  err_msg  |   string    |    是    | 错误码描述            |
  | err_extra | json string |    否    | 附加错误信息表        |


## 批量新增保护站点

- **URL:**

  `POST https://IP:port/api/v1/batch_protected_sites`

- **请求参数：**

  |  字段名   | 数据类型 | 是否必需 | 描述                                                         |
  | :-------: | :------: | :------: | ------------------------------------------------------------ |
  | sites |   list   |    是    | 待新增的站点列表，其中每个元素为单个站点的保护配置，包含的字段与新增单个站点相同，请参考【新增单个保护站点】中的相应内容。|


- **返回值：**

  |  字段名   |  数据类型   | 是否必需 | 描述                  |
  | :-------: | :---------: |:----:| --------------------- |
  |  err_no   |   number    |  是   | 错误码，0代表请求成功 |
  |  err_msg  |   string    |  是   | 错误码描述            |
  | err_extra | json string |  否   | 附加错误信息表        |
  | site_list |   list   |  否   | 新增的站点ID列表 |

  批量新增站点json body示例如下：

{% if cfg is inLayouts(["riversec"]) %}{# 开始判断上述代码在不同layout中显示不同 #}
```
{
  "sites": [
      {
          'protocol': 'http',
          'type': 'ipv4',
          'site': '***********',
          'port': 80,
          'protection_mode': 'intercept',
          'upstream': {
              'upstream_list': [{
                  'ip': '***********',
                  'port': 7000
              }]
          },
          'web_essential_strategy': {
              'enable': True
          }
      },
      {
          'web_essential_strategy': {
              'enable': True
          },
          'protocol': 'https',
          'type': 'domain',
          'site': 'batch2.example.com',
          'port': 443,
          'protection_mode': 'monitor',
          'upstream': {
              'upstream_list': [{
                  'ip': '***********',
                  'port': 8000
              }]
          }
      }
  ]
}
```
{% else %}
```
{
  "sites": [
      {
          'protocol': 'http',
          'type': 'ipv4',
          'site': '***********',
          'port': 80,
          'protection_mode': 'intercept',
          'upstream': {
              'upstream_list': [{
                  'ip': '***********',
                  'port': 7000
              }]
          },
          'waf_strategy': {
              'enable': True,
              'type': 'standard',
              'monitor_only': False
          }
      },
      {
          'waf_strategy': {
              'enable': True,
              'type': 'standard',
              'monitor_only': False
          },
          'protocol': 'https',
          'type': 'domain',
          'site': 'batch2.example.com',
          'port': 443,
          'protection_mode': 'monitor',
          'upstream': {
              'upstream_list': [{
                  'ip': '***********',
                  'port': 8000
              }]
          }
      }
  ]
}
```
{% endif %}

## 批量修改保护站点的配置

- **URL:**

  `PUT https://IP:port/api/v1/batch_protected_sites`

- **请求参数：**

  |  字段名   | 数据类型 | 是否必需 | 描述                                                         |
  | :-------: | :------: | :------: | ------------------------------------------------------------ |
  | site_list |   list   |    是    | 站点ID列表                                                   |
  |  config   |   dict   |    是    | 批量修改的内容，该字段下的子字段与修改单个站点配置的使用方法一致，请参考**【修改单个保护站点的配置】**中的相应内容 |

  修改站点json body示例如下：

{% if cfg is inLayouts(["riversec"]) %}{# 开始判断上述代码在不同layout中显示不同 #}
```
{
  "site_list": [
    "www.testfire.net_9000",
    "***********_80"
  ],
  "config": {
    "protection_mode": "passthrough",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
      "upstream_list": [
        {
          "enable": true,
          "ip": "***********",
          "port": 8088
        },
        {
          "enable": true,
          "ip": "***********",
          "port": 8088
        }
      ]
    },
    "web_essential_strategy": {
      "request_white_list": [
        ["^/$", "root path", "all", true]
      ]
    }
  }
}
```
{% elif cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}
```
{
  "site_list": [
    "www.testfire.net_9000",
    "***********_80"
  ],
  "config": {
    "protection_mode": "passthrough",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
      "upstream_list": [
        {
          "enable": true,
          "ip": "***********",
          "port": 8088
        },
        {
          "enable": true,
          "ip": "***********",
          "port": 8088
        }
      ]
    },
    "web_primary_strategy": {
      "request_white_list": [
        ["^/$", "root path", "all", true]
      ]
    }
  }
}
```
{% else %}
```
{
  "site_list": [
    "www.testfire.net_9000",
    "***********_80"
  ],
  "config": {
    "protection_mode": "passthrough",
    "certificate": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURoVENDQW0yZ0F3SUJBZ0lVS2VKSUo4bDFjT1ZJUHBLT2p3RmtpMUNFUEhzd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1VqRUxNQWtHQTFVRUJoTUNRMDR4Q3pBSkJnTlZCQWdNQWxORE1Rc3dDUVlEVlFRSERBSkRSREVXTUJRRwpBMVVFQ2d3TlVtbDJaWEpUWldOMWNtbDBlVEVSTUE4R0ExVUVBd3dJWkdWdGJ5NWpiMjB3SGhjTk1qRXdOREUwCk1ETXhOVEk0V2hjTk1qSXdOREUwTURNeE5USTRXakJTTVFzd0NRWURWUVFHRXdKRFRqRUxNQWtHQTFVRUNBd0MKVTBNeEN6QUpCZ05WQkFjTUFrTkVNUll3RkFZRFZRUUtEQTFTYVhabGNsTmxZM1Z5YVhSNU1SRXdEd1lEVlFRRApEQWhrWlcxdkxtTnZiVENDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFLZ1pUSUloClNsdkJwWEtRUFBBUzJ3T3IzUE9kZDNpcEsrUk41NnZlMlREbVFxYy9CZGJLeWxXWnlYcGtqRkJybHAwTGFOczEKUGd5MThxb00yd2JpelhwUC8xN2lIeWs5QzQyMXUrVGpwcCsrSEhDMFFsTkxya2ZKeitWaHRqWWZiLy9Ya1c5RwovVWExTFFSNmwyRXZUWEorSHlQdjg3MytaOExsc2NrU0Y4eHRGWkFOQWtldDl3b3c1K2dHZDA1YllGeXBJQjNrCms4aUkzR2ZRQm13eGJxM3dCbHA2N2UreVdVdEk0NWdKK0tLTXdjbjh6RXRDeDF5azQ0dmxnOG9kNXd3R1NvVmgKZWQzQ0FiWXFoaVIyczlUK2F1VWVpTkg5bG5XK3JmZnpFNkZ2YzZ5ei9PeFJVUTVDZ2oxZTY5M0Nvai8xbW9mQQpWQXh6WGZmazNST1kzYU1DQXdFQUFhTlRNRkV3SFFZRFZSME9CQllFRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxCmRIeUFNQjhHQTFVZEl3UVlNQmFBRkcxZkhyb3U2UnBra3UvdTcyUVdRNUgxZEh5QU1BOEdBMVVkRXdFQi93UUYKTUFNQkFmOHdEUVlKS29aSWh2Y05BUUVMQlFBRGdnRUJBSXp2aWdqeFMxZVM5OHF4MnBKUVAxdFVvdGpOemxCeApwYUpXV2UzQkxEVHJ6Tmp1Y3czdW9oUmJ4eXpUM2NIUEp2WGZJUHdwLzdrUVlBd3RRYTlxQWJkOWw5Sm1sS2FKCnFDTGFRczRQS0RVZnFQYXNxVitRL05FZVZGZmJSZGRnNXFaWnFmT3dKWHE2aWw0bkxhVEFwYnRaWXROT2tFTlQKOUR5SEVLbVZvYTdPdzB1MUR3VW5LdGtZaGlaTjNROXVlZWEva091RHRZSGZaMnhuSWI5Nkg5amUrNTYxRkNOWApzOEZjbDExYUoxMFo0RFhCeG9DRHBBRytzUHdSVmU0S1ZrZUdtSkpwaVBESmFxYldiNk94UUZkSEJSVVMrT1UwClNpdnRpcFEvYktrRDRkQVgwYWtRUjMyY3JYZE5HZlhvR1VmL3NRanVwZzlSdlNsK1JDYjNxdFk9Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K",
    "certificate_key": "****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
    "upstream": {
      "upstream_list": [
        {
          "enable": true,
          "ip": "***********",
          "port": 8088
        },
        {
          "enable": true,
          "ip": "***********",
          "port": 8088
        }
      ]
    }
  }
}
```
{% endif %}{# 结束判断上述代码在不同layout中显示不同 #}

- **返回值：**

  |  字段名   |  数据类型   | 是否必需 | 描述                  |
  | :-------: | :---------: | :------: | --------------------- |
  |  err_no   |   number    |    是    | 错误码，0代表请求成功 |
  |  err_msg  |   string    |    是    | 错误码描述            |
  | err_extra | json string |    否    | 附加错误信息表        |


## 一键切换
- **URL:**

  `POST https://IP:port/api/v1/protected_sites/one_click_switch`

- **请求参数：**

  |  字段名   | 数据类型 | 是否必需 | 描述                                                         |
  | :-------: | :------: | :------: | ------------------------------------------------------------ |
  | action |   string   |    是    | 一键操作的动作（block: 拦截， transparent: 透传， monitor: 监控， disable: 禁用）                                                  |
  |  sites   |   list   |    是    | 需要一键操作的站点列表，元素为 serverKey |

  修改站点json body示例如下：
  ```
  {
    "action": "block",
    "sites": ["www.a.com_80", "www.b.com_443"]
  }
  ```
- **返回值：**

  |  字段名   |  数据类型   | 是否必需 | 描述                  |
  | :-------: | :---------: | :------: | --------------------- |
  |  err_no   |   number    |    是    | 错误码，0代表请求成功 |
  |  err_msg  |   string    |    是    | 错误码描述            |
  | err_extra | json string |    否    | 附加错误信息表        |

## 特有错误码

通过API对保护站点进行配置时，可能返回特有的错误码和错误信息，如下表所示。

| 错误码（err_no） | 错误信息err_msg            | 含义 |
| :-----: | ------------------------|---------------- |
|101|Site count exceed the licensed limitation|站点数量已达到授权允许的上限|
|102|Site already exists|站点已经存在|
|103|Site does not exist|指定站点不存在|

> **💡 提示：**
>
> 其他公共错误码和错误信息，请参考**【错误码和错误信息】**章节。

# 配置全局设置项

##  IP黑名单

### 查询IP黑名单是否启用

- **URL：**

  `GET https://IP:port/api/v1/ip_black_list/switch`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                       |
  | :-----: | :------: | :------: | ------------------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功                      |
  | err_msg |  string  |    是    | 错误码描述                                 |
  |  value  |  string  |    否    | err_no为0时，值为on/off，分别代表启用/禁用 |

### 启用和禁用IP黑名单

- **URL：**

  `POST https://IP:port/api/v1/ip_black_list/switch`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  | :----: | :------: | :------: | ----------------------------- |
  | value  |  string  |    是    | 值为on/off，分别代表启用/禁用 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

### 查询当前的IP黑名单

- **URL：**

  `GET https://IP:port/api/v1/ip_black_list`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                           |
  | :-----: | :------: | :------: | ------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功          |
  | err_msg |  string  |    是    | 错误码描述                     |
  |  items  |  array   |    否    | 黑名单条目的数组，包含IP、掩码 |

  items的示例如下：["*************","**********/24","***********,1713507657","**********/24,1713507810"]
  IP地址后面可以指定一个表示有效期的时间戳，指定为0或没有指定时永久有效。

### 覆盖更新IP黑名单

- **URL：**

  `POST https://IP:port/api/v1/ip_black_list`

- **请求参数：**

  |      字段名       | 数据类型 | 是否必需 | 描述                                               |
  | :---------------: | :------: | :------: | -------------------------------------------------- |
  |       items       |  array   |    是    | 黑名单条目的数组，包含IP、掩码、可选的有效期时间戳 |
  |  enable_validity  | boolean  |    否    | 是否为上传的黑名单设置有效期                       |
  | validity_duration |   int    |    否    | 设置有效期时长，范围为[0-63072000]，单位为秒       |

  items的示例如下：["*************/***************","**********/24","**********/24,1713507810"]

  支持采用CIDR记法来填写IPv6和IPv4地址（不支持混合记法的IPv6地址）。当数组中含有不合法的IP条目时，会自动忽略，仅添加合法的条目。

  请求成功后，新增的IP将直接覆盖黑名单中原有IP。目前只支持最大100,000个IP条目，超过限制时，会返回错误码4，表明参数错误。

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

### 增量更新IP黑名单

- **URL：**

  `PUT https://IP:port/api/v1/ip_black_list`

- **请求参数：**

  |      字段名       | 数据类型 | 是否必需 | 描述                                               |
  | :---------------: | :------: | :------: | -------------------------------------------------- |
  |       items       |  array   |    是    | 黑名单条目的数组，包含IP，掩码、可选的有效期时间戳 |
  |  enable_validity  | boolean  |    否    | 是否为上传的黑名单设置有效期                       |
  | validity_duration |   int    |    否    | 设置有效期时长，范围为[0-63072000]，单位为秒       |

  items的示例如下：["*************/***************","**********/24","**********/24,1713507810"]

  支持采用CIDR记法来填写IPv6和IPv4地址（不支持混合记法的IPv6地址）。当数组中含有不合法的IP条目时，会自动忽略，仅添加合法的条目。

  请求成功后，新增的IP将更新在黑名单中，原有IP不会被覆盖。黑名单中IP总条目数量不能超过100,000条，如果新增的IP导致总数超限，则请求将失败，会返回错误码4

- **返回值：**

  |    字段名    | 数据类型 | 是否必需 | 描述                |
  | :----------: | :------: | :------: | ------------------- |
  |    err_no    |  number  |    是    | 错误码，0代表成功。 |
  |   err_msg    |  string  |    是    | 错误码描述          |
  | total_number |  number  |    是    | 黑名单总数量        |
  |  invalid_ip  |  array   |    是    | 无效IP列表          |

### 清空IP黑名单

- **URL：**

  `DELETE https://IP:port/api/v1/ip_black_list`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |


##  IP白名单

### 查询IP白名单是否启用

- **URL：**

  `GET https://IP:port/api/v1/ip_white_list/switch`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                       |
  | :-----: | :------: | :------: | ------------------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功                      |
  | err_msg |  string  |    是    | 错误码描述                                 |
  |  value  |  string  |    否    | err_no为0时，值为on/off，分别代表启用/禁用 |

### 启用和禁用IP白名单

- **URL：**

  `POST https://IP:port/api/v1/ip_white_list/switch`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  | :----: | :------: | :------: | ----------------------------- |
  | value  |  string  |    是    | 值为on/off，分别代表启用/禁用 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

### 查询当前的IP白名单

- **URL：**

  `GET https://IP:port/api/v1/ip_white_list`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                           |
  | :-----: | :------: | :------: | ------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功          |
  | err_msg |  string  |    是    | 错误码描述                     |
  |  items  |  array   |    否    | 白名单条目的数组，包含IP、掩码 |

  items的示例如下：["*************","**********/24","***********,1713507657","**********/24,1713507810"]
  IP地址后面可以指定一个表示有效期的时间戳，指定为0或没有指定时永久有效。

### 覆盖更新IP白名单

- **URL：**

  `POST https://IP:port/api/v1/ip_white_list`

- **请求参数：**

  |      字段名       | 数据类型 | 是否必需 | 描述                                               |
  | :---------------: | :------: | :------: | -------------------------------------------------- |
  |       items       |  array   |    是    | 白名单条目的数组，包含IP、掩码、可选的有效期时间戳 |
  |  enable_validity  | boolean  |    否    | 是否为上传的白名单设置有效期                       |
  | validity_duration |   int    |    否    | 设置有效期时长，范围为[0-63072000]，单位为秒       |

  items的示例如下：["*************/***************","**********/24","**********/24,1713507810"]

  支持采用CIDR记法来填写IPv6和IPv4地址（不支持混合记法的IPv6地址）。当数组中含有不合法的IP条目时，会自动忽略，仅添加合法的条目。

  请求成功后，新增的IP将直接覆盖白名单中原有IP。目前只支持最大10,000个IP条目，超过限制时，会返回错误码4，表明参数错误。

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

### 增量更新IP白名单

- **URL：**

  `PUT https://IP:port/api/v1/ip_white_list`

- **请求参数：**

  |      字段名       | 数据类型 | 是否必需 | 描述                                               |
  | :---------------: | :------: | :------: | -------------------------------------------------- |
  |       items       |  array   |    是    | 白名单条目的数组，包含IP，掩码、可选的有效期时间戳 |
  |  enable_validity  | boolean  |    否    | 是否为上传的白名单设置有效期                       |
  | validity_duration |   int    |    否    | 设置有效期时长，范围为[0-63072000]，单位为秒       |

  items的示例如下：["*************/***************","**********/24","**********/24,1713507810"]

  支持采用CIDR记法来填写IPv6和IPv4地址（不支持混合记法的IPv6地址）。当数组中含有不合法的IP条目时，会自动忽略，仅添加合法的条目。

  请求成功后，新增的IP将更新在白名单中，原有IP不会被覆盖。白名单中IP总条目数量不能超过10,000条，如果新增的IP导致总数超限，则请求将失败，会返回错误码4

- **返回值：**

  |    字段名    | 数据类型 | 是否必需 | 描述                |
  | :----------: | :------: | :------: | ------------------- |
  |    err_no    |  number  |    是    | 错误码，0代表成功。 |
  |   err_msg    |  string  |    是    | 错误码描述          |
  | total_number |  number  |    是    | 白名单总数量        |
  |  invalid_ip  |  array   |    是    | 无效IP列表          |

### 清空IP白名单

- **URL：**

  `DELETE https://IP:port/api/v1/ip_white_list`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

{% if cfg is inLayouts(["riversec","waf","cetcwaf","ngwaf"]) %}{# 开始判断多端策略仅限riversec，waf和ngwaf #}
## 多端应用防护策略配置

{% if cfg is inLayouts(["waf","cetcwaf","ngwaf"]) %}{# 开始判断提示信息仅限waf和ngwaf #}
>**💡 提示：**
>
>该功能仅限购买了动态防护机制的用户使用。

{% endif %}{# 结束判断提示信息仅限waf和ngwaf #}
### 查询当前配置

- **URL：**

  `GET /api/v1/global_settings/multi_endpoints_strategy`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                                         |
  | :-----: | :------: | :------: | ------------------------------------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功                                        |
  | err_msg |  string  |    是    | 成功时返回“Success”，失败时返回错误信息描述                  |
  | result  |  array   |    否    | 对于成功调用，返回配置全量配置列表。关于result字段的详细说明，见下文描述。 |

- **请求示例：**

```
{
    "err_no": 0,
    "err_msg": "Success",
    "result": {
        "transparent_mpp_requests": true,
        "transparent_app_native_requests": true,
        "transparent_app_h5_requests": true,
        "transparent_external_site_requests": true,
        "referers": ["http://www.external.com"],
    }
}
```

### 全量保存配置

- **URL：**

  `POST /api/v1/global_settings/multi_endpoints_strategy`

- **请求参数：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                        |
  | :-----: | :------: | :------: | ------------------------------------------- |
  | transparent_mpp_requests  |  Boolean  |    是    | 是否启用透传小程序请求                    |
  | transparent_mobile_requests  |  Boolean  |    是    | 是否启用透传移动APP请求             |
  | transparent_native_h5_requests  |  Boolean  |    是    | 是否启用透传移动应用离线H5请求              |
  | transparent_external_site_requests  |  Boolean  |    是    | 是否启用透传外站Web请求              |
  | referers  |  Array  |    否    | 当启用透传外站Web请求，此字段用于配置外站的Referer信息            |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                        |
  | :-----: | :------: | :------: | ------------------------------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功                       |
  | err_msg |  string  |    是    | 成功时返回“Success”，失败时返回错误信息描述 |

- **请求示例：**

```
{
    'transparent_mpp_requests': False,
    'transparent_app_native_requests': False,
    'transparent_app_h5_requests': False,
    'transparent_external_site_requests': True,
    'referers': ["http://www.crosssite1.com", "http://www.crosssite2.com"]
}
```
{% endif %}{# 结束判断多端策略仅限riversec，waf和ngwaf #}

#  配置{{ cfg | waf_name }}防护策略

## 查询单个全局自定义规则

- **URL：**

  `GET https://IP:port/api/v1/waf/global_custom_rules/{id}`

  > **💡 提示：**
  >
  > URL中的id为全局自定义规则ID。

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-----: | :------: | :------: | ----------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。 |
  | err_msg |  string  |    是    | 错误码描述              |
  |  data   |   dict   |    是    | 规则详情                |

  **data**包含的子字段说明：

  |            字段名             |  数据类型 | 是否必需 | 描述                                     |
  |:--------------------------:|:--:| :------: |----------------------------------------|
  |             id             | number |    是    | 规则id                                   |
  |            name            | string |    是    | 规则名称                                   |
  |          comment           | string |    是    | 规则描述                                   |
  |        detect_type         | string |    是    | 规则检测类型                                 |
  |      detect_direction      | string |    是    | 检测HTTP交互方向， request：检测请求；response：检测响应 |
  |        detect_rule         | array |    是    | 检测规则                                   |
  |           action           | string |    是    | 命中规则执行的动作，Pass代表放行，Block代表拦截           |
  |        block_value         | number |    是    | 如果动作是拦截，响应码，范围200-600                  |
  |         risk_level         | string |    是    | 规则的风险级别，high：高风险；medium:中风险；low:低风险    |
  |           hosts            | array |    是    | 规则当前应用的保护站点的站点ID列表                     |
  |         decode_enable      | bool |    是    | 规则是否启用解析解码               |
  |       decode_sequence      | array |    是    | 解析解码顺序                    |

## 查询全部全局自定义规则

- **URL：**

  `GET https://IP:port/api/v1/waf/global_custom_rules`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                                                         |
  | :----: | :------: | :------: | ------------------------------------------------------------ |
  | fields |  string  |    否    | 值为id,name,comment,detect_type,detect_direction,detect_rule,action,risk_level,block_value,hosts<br>任意字段名称组合，代表查询返回的规则的字段，默认返回id,name,comment |
  |  name  |  string  |    否    | 值为自定义规则名称，代表查询和名称相同的自定义规则           |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                             |
  | :-----: | :------: | :------: | ------------------------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。                          |
  | err_msg |  string  |    是    | 错误码描述                                       |
  |  data   |  array   |    是    | 规则列表。data字段的详细说明，见上一小节的描述。 |
  |  total  |  number  |    是    | 规则的数量                                       |

## 创建全局自定义规则

- **URL：**

  `POST https://IP:port/api/v1/waf/global_custom_rules`

- **请求参数：**

  |            字段名            | 数据类型 | 是否必需 | 描述                                    |
  |:-------------------------:| :------: |:----:|---------------------------------------|
  |           name            |  string  |  是   | 规则名称，必须唯一                             |
  |          comment          |  string  |  否   | 规则描述                                  |
  |        detect_type        |  string  |  是   | 规则检测类型。字段值见下表说明。                      |
  |     detect_direction      |  string  |  是   | 检测HTTP交互方向，request：检测请求；response：检测响应 |
  |        detect_rule        |  array   |  是   | 检测规则。字段值见下表说明。                        |
  |          action           |  string  |  否   | 命中规则执行的动作，Pass代表放行，Block代表拦截，默认Block  |
  |        block_value        |  number  |  否   | 如果动作是拦截，响应码必填，默认400                   |
  |        risk_level         |  string  |  否   | 规则的风险级别，high：高风险；medium:中风险；low:低风险   |
  |       decode_enable       | bool |  否   | 规则是否启用解析解码                            |
  |      decode_sequence      | array |  否   | 解析解码顺序。字段值见下表说明。     |

  **detect_type**目前支持的类型：

  |            值            | 描述                   |
  | :----------------------: | ---------------------- |
  |   SERVER_INFO_LEAKAGE    | 服务器信息泄露防护     |
  |        XML_ATTACK        | XML注入攻击防护        |
  |      PHP_INJECTION       | PHP注入攻击防护        |
  |    COMMAND_INJECTION     | 命令注入语义攻击防护   |
  |        WEB_SHELL         | WebShell语义攻击防护   |
  | SENSITIVE_FILE_DETECTION | 敏感文件探测防护       |
  |    LOCAL_FILE_INCLUDE    | 本地文件包含攻击防护   |
  |      MALICIOUS_LINK      | 外链挂马防护           |
  |   SERVER_VULNERABILITY   | 服务器漏洞攻击防护     |
  |  SENSITIVE_INFO_FILTER   | 敏感信息过滤           |
  |     PROTOCOL_ATTACK      | HTTP协议攻击防护       |
  |      SQL_INJECTION       | SQLi语义攻击防护       |
  |   REMOTE_FILE_INCLUDE    | 远程文件包含攻击防护   |
  |       FILE_UPLOAD        | 文件非法上传攻击防护   |
  |      SCANNER_DETECT      | 扫描工具与爬虫攻击防护 |
  |   JAVA_DESERIALIZATION   | JAVA OGNL语义攻击防护  |
  |   JAVA_CODE_INJECTION    | JAVA代码注入攻击防护   |
  |     SESSION_FIXATION     | 会话盗用攻击防护       |
  |           XSS            | XSS语义攻击防护        |
  |  ILLEGAL_FILE_DOWNLOAD   | 文件非法下载防护       |
  |    URL_ACCESS_CONTROL    | URL访问控制            |
  |       USER-DEFINE        | 用户自定义             |

  **detect_rule**是对具体检测规则的定义，里面的所有检测规则是并且的意思。 每个规则包含Key--表示检测位置，value表示检测内容，支持正则。

  目前支持的检测位置：

  | 检测位置                   | 描述               |
  | -------------------------- | ------------------ |
  | Request_All                | 请求任意位置       |
  | Request_URL                | 请求URL            |
  | Request_Parameter_Name     | 请求参数名         |
  | Request_Parameter          | 请求参数值         |
  | Request_Header_Name        | 请求头名           |
  | Request_Headers            | 请求头值           |
  | Request_Referer            | 请求头Referer      |
  | Request_UA                 | 请求头UA           |
  | Request_Cookie_Name        | 请求头Cookie名     |
  | Request_Cookie             | 请求头Cookie值     |
  | Request_Body               | 请求体             |
  | Request_Source_IP          | 源地址             |
  | Response_Header_Name       | 响应头名           |
  | Response_Headers           | HTTP响应头信息设置 |
  | Response_Status            | 响应码             |
  | Response_Body              | 响应体             |
  | Request_Multipart_Filename | 上传文件名         |
  | Request_Multipart_Filebody | 上传文件内容       |

  **decode_sequence**的值为解析解码的顺序，支持的值为：

  | 值               | 解码/解析类型 |
  | ---------------- | --------------|
  | url_decode       | URL解码       |
  | html_decode      | HTML实体解码  |
  | slash_decode     | 斜杠转义解码  |
  | base64_decode    | Base64解码    |
  | utf7_decode      | UTF-7解码     |
  | hex_decode       | 十六进制解码  |
  | plus_escape      | 加号解析      |
  | json_parse       | JSON解析      |
  | xml_parse        | XML解析       |
  | php_unserialize  | PHP反序列化   |

  POST的body示例如下：

```
{
    "name": "custom_rule",
    "comment": "dd",
    "detect_type": "USER-DEFINE",
    "detect_direction": "request",
    "detect_rule": [

        {
            "value": "spam",
            "key": "Request_URL"
        }
    ],
    "risk_level": "high",
    "action": "Block",
    "block_value": 400,
    "decode_enable": true,
    "decode_sequence": ["url_decode","html_decode"]
}
```

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                   |
  | :-------: | :------: | :------: | ---------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功  |
  |  err_msg  |  string  |    是    | 错误码描述             |
  | err_extra |  string  |    否    | 附加错误信息表         |
  |    id     |  number  |    否    | 成功时返回自动生成的id |

## 修改全局自定义规则

- **URL：**

  `PUT https://IP:port/api/v1/waf/global_custom_rules/{id}`

  > **💡 提示：**
  >
  > URL中的id为全局自定义规则ID。

- **请求参数：**

  |           字段名            | 数据类型 | 是否必需 | 描述                                                       |
  |:------------------------:| :------: | :------: | ---------------------------------------------------------- |
  |           name           |  string  |    是    | 规则名称，必须唯一                                         |
  |         comment          |  string  |    否    | 规则描述。字段值见上一小节的说明。                         |
  |       detect_type        |  string  |    是    | 规则检测类型                                               |
  |     detect_direction     |  string  |    是    | 检测HTTP交互方向，request：检测请求；response：检测响应    |
  |       detect_rule        |  array   |    是    | 检测规则。字段值见上一小节的说明。                         |
  |          action          |  string  |    否    | 命中规则执行的动作，Pass代表放行，Block代表拦截，默认Block |
  |       block_value        |  number  |    否    | 如果动作是拦截，响应码必填，默认400                        |
  |        risk_level        |  string  |    否    | 规则的风险级别，high：高风险；medium:中风险；low:低风险    |
  |      decode_enable       | bool |  否   | 规则是否启用解析解码            |
  |     decode_sequence      | array |  否   | 解析解码顺序。字段值见上一小节的说明。 |

  > **💡 提示：**
  >
  > detect_direction 不能修改

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :------: | :------: | --------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功 |
  |  err_msg  |  string  |    是    | 错误码描述            |
  | err_extra |  string  |    否    | 附加错误信息表        |

## 修改全局自定义规则的应用站点

- **URL：**

  `PUT https://IP:port/api/v1/waf/global_custom_rules/{id}`

  > **💡 提示：**
  >
  > URL中的id为全局自定义规则ID。

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                 |
  | :----: | :------: | :------: | -------------------- |
  | hosts  |  array   |    是    | 保护站点的站点ID列表 |

   示例：``{"hosts": ["************_80"]}``

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :------: | :------: | --------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功 |
  |  err_msg  |  string  |    是    | 错误码描述            |
  | err_extra |  string  |    否    | 附加错误信息表        |

## 删除全局自定义规则

- **URL：**

  `DELETE https://IP:port/api/v1/waf/global_custom_rules/{id}`

  > **💡 提示：**
  >
  > URL中的id为全局自定义规则ID。

- **请求参数：**

  无

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :------: | :------: | --------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功 |
  |  err_msg  |  string  |    是    | 错误码描述            |
  | err_extra |  string  |    否    | 附加错误信息表        |

## 查询单个自定义模块策略

- **URL：**

  `GET https://IP:port/api/v1/waf/general_protection/{protection_type}/{id}`

  > **💡 提示：**
  >
  > URL中的**protection_type**为模块名称的字符串（例如sql_injection_interception），**id**为自定义规则ID（例如1001）。

  防护系统所有的规则模块和语义分析模块对应的名称如下表所示：

  |            protection_type            | 描述                   |
  | :-----------------------------------: | ---------------------- |
  |   server_info_leakage_interception    | 服务器信息泄露防护     |
  |        xml_attack_interception        | XML注入攻击防护        |
  |      php_injection_interception       | PHP注入攻击防护        |
  |      command_excute_interception      | 命令注入攻击防护       |
  |        web_shell_interception         | WebShell攻击防护       |
  | sensitive_file_detection_interception | 敏感文件探测防护       |
  |    local_file_include_interception    | 本地文件包含攻击防护   |
  |      malicious_link_interception      | 外链挂马防护           |
  |   server_vulnerability_interception   | 服务器漏洞攻击防护     |
  |  sensitive_info_filter_interception   | 敏感信息过滤           |
  |     protocol_attack_interception      | HTTP协议攻击防护       |
  |      sql_injection_interception       | SQL注入攻击防护        |
  |   remote_file_include_interception    | 远程文件包含攻击防护   |
  |       file_upload_interception        | 文件非法上传攻击防护   |
  |      scanner_detect_interception      | 扫描工具与爬虫攻击防护 |
  |   java_deserialization_interception   | JAVA反序列化攻击防护   |
  |        java_code_interception         | JAVA代码注入攻击防护   |
  |     session_fixation_interception     | 会话盗用攻击防护       |
  |      xss_injection_interception       | 跨站脚本攻击防护       |
  |  illegal_file_download_interception   | 文件非法下载防护       |
  |        cmd_syntax_interception        | 命令注入语义攻击防护   |
  |        xss_syntax_interception        | XSS语义攻击防护        |
  |       ognl_syntax_interception        | JAVA OGNL语义攻击防护  |
  |       sqli_syntax_interception        | SQLi语义攻击防护       |
  |   php_webshell_syntax_interception    | WebShell语义攻击防护   |

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-----: | :------: | :------: | ----------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。 |
  | err_msg |  string  |    是    | 错误码描述              |
  |  data   |   dict   |    是    | 策略详情                |

  **data**包含的自定义策略字段：

  |     字段名     | 数据类型 | 是否必需 | 描述                           |
  | :------------: | :------: | :------: | ------------------------------ |
  |       id       |  number  |    是    | 策略id                         |
  |      name      |  string  |    是    | 策略名称                       |
  |    comment     |  string  |    是    | 策略描述                       |
  | special_config |   dict   |    是    | 策略的特殊配置                 |
  |     rules      |   dict   |    是    | 策略的规则配置                 |
  |    template    |  number  |    是    | 语义模块使用的策略模版         |

## 查询所有自定义模块策略

- **URL：**

  `GET https://IP:port/api/v1/waf/general_protection/{protection_type}`

  > **💡 提示：**
  >
  > URL中的**protection_type**为模块名称的字符串（例如sql_injection_interception），具体请参考**【查询单个自定义模块策略】** 小节中关于**protection_type**的表格。

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                                                         |
  | :----: | :------: | :------: | ------------------------------------------------------------ |
  | fields |  string  |    否    | 值为id,name,comment,special_config,rules,template<br>任意字段名称组合，代表查询返回策略的字段，默认返回id,name,comment |
  |  name  |  string  |    否    | 值为自定义策略名称，代表查询和名称相同的自定义策略           |

  URL 请求示例：``https://************:20167/api/v1/waf/general_protection/sql_injection_interception?fields="id,name,comment,rules"``

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                             |
  | :-----: | :------: | :------: | ------------------------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。                          |
  | err_msg |  string  |    是    | 错误码描述                                       |
  |  data   |  array   |    是    | 策略数据。data字段的详细说明，见上一小节的描述。 |
  |  total  |  number  |    是    | 策略数量                                         |


## 创建自定义模块策略

- **URL：**

  `POST https://IP:port/api/v1/waf/general_protection/{protection_type}`

  > **💡 提示：**
  >
  > URL中的**protection_type**为模块名称的字符串（例如sql_injection_interception），具体请参考**【查询单个自定义模块策略】** 小节中关于**protection_type**的表格。


- **请求参数：**

  |     字段名     | 数据类型 | 是否必需 | 描述                                    |
  | :------------: | :------: | :------: | --------------------------------------- |
  |      name      |  string  |    是    | 策略名称                                |
  |    comment     |  string  |    否    | 策略描述                                |
  | special_config |   dict   |    否    | 策略的特殊配置                          |
  |     rules      |   dict   |    否    | 用于修改规则模块中指定规则的配置，例如启用状态、风险级别、执行动作等         |
  |    template    |  number  |    否    | 只有语义模块才需要设置策略模版，默认为1 |

  请求示例代码如下:

```
{
    "comment": "api create",
    "rules": {"941130": {
            "enabled": 1,
            "risk_level": "medium",
            "action": 2,
            "redirect_path": null,
        },
        "913822": {
            "enabled": 1,
            "risk_level": "low",
            "action": 5,
            "redirect_path": "/",
        }},
    "name": "custom_r"
}

```

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  |    id     |  number  |    否    | 创建成功返回的策略id    |
  | err_extra |  string  |    否    | 附加错误信息表          |

## 修改自定义模块策略

- **URL：**

  `PUT https://IP:port/api/v1/waf/general_protection/{protection_type}/{id} `

  > **💡 提示：**
  >
  > URL中的**protection_type**为模块名称的字符串（例如sql_injection_interception），**id**为自定义规则ID（例如1001）。关于**protection_type**的名称，请参考**【查询单个自定义模块策略】**小节中关于该字段的表格。


- **请求参数：**

  |     字段名     | 数据类型 | 是否必需 | 描述                                    |
  | :------------: | :------: | :------: | --------------------------------------- |
  |      name      |  string  |    是    | 策略名称                                |
  |    comment     |  string  |    否    | 策略描述                                |
  | special_config |   dict   |    否    | 策略的特殊配置                          |
  |     rules      |   dict   |    否    | 用于修改规则模块中指定规则的配置，例如启用状态、风险级别、执行动作等         |
  |    template    |  number  |    否    | 只有语义模块才需要设置策略模版，默认为1 |

  语义模块请求示例代码如下:

```
{
    "comment": "api create",
    "special_config":{"enable_block_php": true}
    "name": "custom_r",
    "template": 3
}

```

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |

## 删除自定义模块策略

- **URL：**

  `DELETE https://IP:port/api/v1/waf/general_protection/{protection_type}/{id}`

  > **💡 提示：**
  >
  > URL中的**protection_type**为模块名称的字符串（例如sql_injection_interception），**id**为自定义规则ID（例如1001）。关于**protection_type**的名称，请参考**【查询单个自定义模块策略】**小节中关于该字段的表格。

- **请求参数：**

  无

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |

## 查询单个自定义站点策略

- **URL：**

  `GET https://IP:port/api/v1/waf/general_protection/customized_site_strategies/{id}`

  > **💡 提示：**
  >
  > URL中的id为自定义站点策略ID。

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-----: | :------: | :------: | ----------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。 |
  | err_msg |  string  |    是    | 错误码描述              |
  |  data   |   dict   |    是    | 规则详情                |

  **data**包含的自定义策略字段：

  | 字段名  | 数据类型 | 是否必需 | 描述                                                         |
  | :-----: | :------: | :------: | ------------------------------------------------------------ |
  |   id    |  number  |    是    | 策略id                                                       |
  |  name   |  string  |    是    | 策略名称                                                     |
  | comment |  string  |    是    | 策略描述                                                     |
  | modules |   dict   |    是    | 站点策略包含的防护模块的状态，enabled表示防护模块是否启用，strategy表示防护模块的策略id |
  |  hosts  |  array   |    是    | 站点策略应用到的站点id列表                                   |


## 查询所有自定义站点策略

- **URL：**

  `GET https://IP:port/api/v1/waf/general_protection/customized_site_strategies`


- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                                                         |
  | :----: | :------: | :------: | ------------------------------------------------------------ |
  | fields |  string  |    否    | 值为id,name,comment,modules,hosts<br>任意字段名称组合，代表查询返回策略的字段，默认返回id,name,comment |
  |  name  |  string  |    否    | 值为自定义策略名称，代表查询和名称相同的自定义策略           |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                             |
  | :-----: | :------: | :------: | ------------------------------------------------ |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。                          |
  | err_msg |  string  |    是    | 错误码描述                                       |
  |  data   |  array   |    是    | 策略列表。data字段的详细说明，见上一小节的描述。 |
  |  total  |   dict   |    是    | 策略数量                                         |


## 创建自定义站点策略

- **URL：**

  `POST https://IP:port/api/v1/waf/general_protection/customized_site_strategies`

- **请求参数：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                                         |
  | :-----: | :------: | :------: | ------------------------------------------------------------ |
  |  name   |  string  |    是    | 策略名称                                                     |
  | comment |  string  |    否    | 策略描述                                                     |
  | modules |   dict   |    否    | 站点策略包含的防护模块的状态，<br>enabled表示防护模块是否启用，strategy表示防护模块的策略id |

  示例如下：

```
{
    "comment": "create by api",
    "modules": {
        "java_deserialization_interception": {
            "enabled": true,
            "strategy": 1001
        },
        "sensitive_file_detection_interception": {
            "enabled": false,
            "strategy": 1
        },
        "xml_attack_interception": {
            "enabled": false,
            "strategy": 1
        },
        "malicious_link_interception": {
            "enabled": false,
            "strategy": 1
        },
        "server_vulnerability_interception": {
            "enabled": false,
            "strategy": 1
        },
        "sensitive_info_filter_interception": {
            "enabled": false,
            "strategy": 1
        },
        "sql_injection_interception": {
            "enabled": false,
            "strategy": 1
        },
        "java_code_interception": {
            "enabled": false,
            "strategy": 1
        },
        "scanner_detect_interception": {
            "enabled": true,
            "strategy": 1
        },
        "command_excute_interception": {
            "enabled": false,
            "strategy": 1
        },
        "illegal_file_download_interception": {
            "enabled": false,
            "strategy": 1
        },
        "remote_file_include_interception": {
            "enabled": false,
            "strategy": 1
        },
        "ognl_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "server_info_leakage_interception": {
            "enabled": false,
            "strategy": 1
        },
        "php_injection_interception": {
            "enabled": false,
            "strategy": 1
        },
        "cmd_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "web_shell_interception": {
            "enabled": false,
            "strategy": 1
        },
        "php_webshell_syntax_interception": {
            "enabled": true,
            "strategy": 3
        },
        "local_file_include_interception": {
            "enabled": false,
            "strategy": 1
        },
        "protocol_attack_interception": {
            "enabled": false,
            "strategy": 1
        },
        "session_fixation_interception": {
            "enabled": false,
            "strategy": 1
        },
        "xss_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "sqli_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "file_upload_interception": {
            "enabled": false,
            "strategy": 1
        },
        "xss_injection_interception": {
            "enabled": false,
            "strategy": 1
        }
    },
    "name": "test_create"
}
```


- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                           |
  | :-------: | :------: | :------: | ------------------------------ |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。        |
  |  err_msg  |  string  |    是    | 错误码描述                     |
  | err_extra |  string  |    否    | 附加错误信息表                 |
  |    id     |  number  |    否    | 如果创建成功，返回创建的策略id |

## 修改自定义站点策略

- **URL：**

  `PUT https://IP:port/api/v1/waf/general_protection/customized_site_strategies/{id}`

  > **💡 提示：**
  >
  > URL中的id为自定义站点策略ID。

- **请求参数：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                                         |
  | :-----: | :------: | :------: | ------------------------------------------------------------ |
  |  name   |  string  |    是    | 策略名称                                                     |
  | comment |  string  |    否    | 策略描述                                                     |
  | modules |   dict   |    否    | 站点策略包含的防护模块的状态，<br>enabled表示防护模块是否启用，strategy表示防护模块的策略id |

  示例如下：

```
{
    "comment": "create by api",
    "modules": {
        "java_deserialization_interception": {
            "enabled": true,
            "strategy": 1001
        },
        "sensitive_file_detection_interception": {
            "enabled": false,
            "strategy": 1
        },
        "xml_attack_interception": {
            "enabled": false,
            "strategy": 1
        },
        "malicious_link_interception": {
            "enabled": false,
            "strategy": 1
        },
        "server_vulnerability_interception": {
            "enabled": false,
            "strategy": 1
        },
        "sensitive_info_filter_interception": {
            "enabled": false,
            "strategy": 1
        },
        "sql_injection_interception": {
            "enabled": false,
            "strategy": 1
        },
        "java_code_interception": {
            "enabled": false,
            "strategy": 1
        },
        "scanner_detect_interception": {
            "enabled": true,
            "strategy": 1
        },
        "command_excute_interception": {
            "enabled": false,
            "strategy": 1
        },
        "illegal_file_download_interception": {
            "enabled": false,
            "strategy": 1
        },
        "remote_file_include_interception": {
            "enabled": false,
            "strategy": 1
        },
        "ognl_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "server_info_leakage_interception": {
            "enabled": false,
            "strategy": 1
        },
        "php_injection_interception": {
            "enabled": false,
            "strategy": 1
        },
        "cmd_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "web_shell_interception": {
            "enabled": false,
            "strategy": 1
        },
        "php_webshell_syntax_interception": {
            "enabled": true,
            "strategy": 3
        },
        "local_file_include_interception": {
            "enabled": false,
            "strategy": 1
        },
        "protocol_attack_interception": {
            "enabled": false,
            "strategy": 1
        },
        "session_fixation_interception": {
            "enabled": false,
            "strategy": 1
        },
        "xss_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "sqli_syntax_interception": {
            "enabled": false,
            "strategy": 1
        },
        "file_upload_interception": {
            "enabled": false,
            "strategy": 1
        },
        "xss_injection_interception": {
            "enabled": true,
            "strategy": 2
        }
    },
    "name": "test_modify"
}

```

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |

## 修改站点策略的应用站点

- **URL：**

  `PUT https://IP:port/api/v1/waf/general_protection/customized_site_strategies/{id}`

  > **💡 提示：**
  >
  > URL中的id为自定义站点策略ID。

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述       |
  | :----: | :------: | :------: | ---------- |
  | hosts  |  array   |    是    | 站点id列表 |

   示例如下：` {"hosts": ["************_80"]}`

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |

## 删除自定义站点策略

- **URL：**

  `DELETE https://IP:port/api/v1/waf/general_protection/customized_site_strategies/{id}`

  > **💡 提示：**
  >
  > URL中的id为自定义站点策略ID。

- **请求参数：**

  无

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |


## 获取规则库信息

- **URL：**

  `GET https://IP:port/api/v1/waf/ruleset`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-----: | :------: | :------: | ----------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功。 |
  | err_msg |  string  |    是    | 错误码描述              |
  |  data   |   dict   |    是    | 规则库信息              |


## 升级规则库

- **URL：**

  `POST https://IP:port/api/v1/waf/ruleset`

- **请求参数：**

  升级包以二进制数据的形式附加在POST请求的请求体中，请求头添加{'Content-Type': 'application/octet-stream'}。

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |
  | data | dict   | 是       | 规则库信息              |

## 查询全局{{ cfg | waf_name }}白名单列表

- **URL：**

  `GET https://IP:port/api/v1/waf/global_white_list`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |
  | data | array   | 是       | {{ cfg | waf_name }}白名单列表        |

## 全量更新全局{{ cfg | waf_name }}白名单列表

- **URL：**

  `POST https://IP:port/api/v1/waf/global_white_list`

- **请求参数：**

  请求参数为 `array`，数组中每个元素对应一条全局{{ cfg | waf_name }}白名单配置，每条白名单配置对应的字段如下。除了comment之外，其它字段不能同时都为空，否则接口会返回错误。

  | 字段名 | 数据类型 | 是否必需 | 描述 |
  |:-----:|:------:|:----:|-------|
  | comment    | string |  否   | 备注信息 |
  | url    | string |  否   | 白名单URL的正则表达式 |
  |host| string |  否   | 白名单Host的正则表达式 |
  |id_whitelist|string |  否   | 规则ID的列表，以英文逗号分隔 |
  |ip_whitelist| string |  否   | 要添加白名单的源IP的列表，支持CIRD格式的IPv4和IPv6地址，多个地址用英文逗号隔开 |
  |method| string |  否   | 请求方法的列表，多个方法用英文逗号隔开 |

  示例如下：

```
[
    {
        "comment": "",
        "url": "/qqqq",
        "host": "^10\\.10\\.70\\.125:80$",
        "id_whitelist": "600001,600002",
        "ip_whitelist": "************",
        "method": "GET,POST"
    },
    {
        "comment": "",
        "url": "/qqqq",
        "host": "^10\\.10\\.70\\.125:80$",
        "id_whitelist": "600001",
        "ip_whitelist": "************",
        "method": "AAA"
    },
    {
        "ip_whitelist": "",
        "id_whitelist": "600001",
        "host": "",
        "method": "",
        "url": "",
        "comment": ""
    }
]
```

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
    | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |



## 增量更新全局{{ cfg | waf_name }}白名单列表

- **URL：**

  `PATCH https://IP:port/api/v1/waf/global_white_list`

- **请求参数：**

  |    字段名    | 数据类型 | 是否必需 | 描述                    |
  | :----------: | :------: | :------: | -------------- |
  |   comment    |  string  |    否    | 备注信息                                                     |
  |     url      |  string  |    否    | 白名单URL的正则表达式                                        |
  |     host     |  string  |    否    | 白名单Host的正则表达式                                       |
  | id_whitelist |  string  |    否    | 规则ID的列表，以英文逗号分隔     |
  | ip_whitelist |  string  |    否    | 要添加白名单的源IP的列表，支持CIRD格式的IPv4和IPv6地址，多个地址用英文逗号隔开 |
  |    method    |  string  |    否    | 请求方法的列表，多个方法用英文逗号隔开    |

  示例如下：
```
{
    "comment": "",
    "url": "/qqqq",
    "host": "^10\\.10\\.70\\.125:80$",
    "id_whitelist": "600001",
    "ip_whitelist": "************",
    "method": ""
}
```

- **返回值：**

  |  字段名   | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :------: | :------: | ----------------------- |
  |  err_no   |  number  |    是    | 错误码，0代表请求成功。 |
  |  err_msg  |  string  |    是    | 错误码描述              |
  | err_extra |  string  |    否    | 附加错误信息表          |

{% if cfg is inLayouts(["riversec","ngwaf","abd","asa"]) %}{# 开始判断ubb仅限riversec、ngwaf、abd、asa #}
#  配置"可编程对抗"高级规则
## 查询"可编程对抗"规则编辑器是否启用

- **URL：**

  `GET https://IP:port/api/v1/ubbv2/manual_rule/switch`

- **请求参数：**

  无

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                       |
  | :-------: | :--------: | :--------: | ------------------------------------------ |
  | err_no  | number   | 是       | 错误码，0代表请求成功。                    |
  | err_msg | string   | 是       | 错误码描述                                 |
  | status  | string   | 否       | err_no为0时，值为on/off，分别代表启用/禁用 |

## 启用和禁用"可编程对抗"规则编辑器

- **URL：**

  `POST https://IP:port/api/v1/ubbv2/manual_rule/switch`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | status | string   | 是       | 值为on/off，分别代表启用/禁用 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

## 获取"可编程对抗"Web高级规则

- **URL：**

  `GET https://IP:port/api/v1/ubbv2/manual_rule/web`

- **请求参数：**

   无

- **返回值：**

  |   字段名    | 数据类型 | 是否必需 | 描述                    |
  | :---------: | :------: | :------: | ----------------------- |
  |   err_no    |  number  |    是    | 错误码，0代表请求成功。 |
  |   err_msg   |  string  |    是    | 错误码描述              |
  | manual_rule |  string  |    是    | Web高级规则             |

## 更新"可编程对抗"Web高级规则

- **URL：**

  `POST https://IP:port/api/v1/ubbv2/manual_rule/web`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | manual_rule  | string | 是       | 上传的规则文件 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

{% if cfg is inLayouts(["riversec","ngwaf"])%}{# 开始判断app和小程序相关ubb仅限riversec和ngwaf #}
## 获取"可编程对抗"APP高级规则

- **URL：**

  `GET https://IP:port/api/v1/ubbv2/manual_rule/app`

- **请求参数：**

   无

- **返回值：**

  |   字段名    | 数据类型 | 是否必需 | 描述                    |
  | :---------: | :------: | :------: | ----------------------- |
  |   err_no    |  number  |    是    | 错误码，0代表请求成功。 |
  |   err_msg   |  string  |    是    | 错误码描述              |
  | manual_rule |  string  |    是    | APP高级规则             |


## 更新"可编程对抗"APP高级规则

- **URL：**

  `POST https://IP:port/api/v1/ubbv2/manual_rule/app`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | manual_rule  | string | 是       | 上传的规则文件 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

## 获取"可编程对抗"小程序高级规则

- **URL：**

  `GET https://IP:port/api/v1/ubbv2/manual_rule/mpp`

- **请求参数：**

   无

- **返回值：**

  |   字段名    | 数据类型 | 是否必需 | 描述                    |
  | :---------: | :------: | :------: | ----------------------- |
  |   err_no    |  number  |    是    | 错误码，0代表请求成功。 |
  |   err_msg   |  string  |    是    | 错误码描述              |
  | manual_rule |  string  |    是    | 小程序高级规则             |

## 更新"可编程对抗"小程序保护高级规则

- **URL：**

  `POST https://IP:port/api/v1/ubbv2/manual_rule/mpp`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | manual_rule  | string | 是       | 上传的规则文件 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

{% endif %}{# 结束判断app和小程序相关ubb仅限riversec和ngwaf #}
## 查询"可编程对抗"规则是否启用

- **URL：**

  `GET https://IP:port/api/v1/ubbv2/rule/switch`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述             |
  |:------: | :--------: | :--------: | ---------------- |
  | id     | string   | 是       | 可编程对抗规则ID |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                                       |
  | :-------: | :--------: | :--------: | ------------------------------------------ |
  | err_no  | number   | 是       | 错误码，0代表请求成功。                    |
  | err_msg | string   | 是       | 错误码描述                                 |
  | status  | string   | 否       | err_no为0时，值为on/off，分别代表启用/禁用 |

## 启用和禁用"可编程对抗"规则

- **URL：**

  `POST https://IP:port/api/v1/ubbv2/rule/switch`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | id     | string   | 是       | 可编程对抗规则ID              |
  | status | string   | 是       | 值为on/off，分别代表启用/禁用 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

## 上传"可编程对抗"资源文件

- **URL：**

  `POST https://IP:port/api/v1/ubbv2/resource_file`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | file_name  | string | 是       | 上传的资源文件名称 |
  | type  | string | 是       | 上传的资源文件类型,仅支持list,js,html |
  | file_content  | string | 是       | 上传的资源文件内容 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |
{% endif %}{# 结束判断ubb仅限riversec、ngwaf、abd、asa #}

{% if cfg is inLayouts(["riversec","ngwaf","abd","asa"]) %}{# 开始判断api资产管理仅限riversec、ngwaf、abd、asa #}
# 配置API资产管理
## 获取当前API列表

- **URL：**

  `GET https://IP:port/api/v2/abd/api`

- **请求参数：**

  |   字段名   | 数据类型 | 是否必需 | 描述                                                      |
  | :--------: | :------: | :------: | --------------------------------------------------------- |
  | start_time |  number  |    否    | API列表以指定的创建时间作为时间过滤起点，格式为Unix时间戳 |
  |  end_time  |  number  |    否    | API列表以指定的创建时间作为时间过滤终点，格式为Unix时间戳 |
  |  page_no   |  number  |    否    | 分页参数：起始页。默认值为None，表示不指定              |
  |  page_size |  number  |    否    | 分页参数：每页API条数。默认值为50，支持1~1000的整数     |


- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                   |
  | :-------: | :--------: | :--------: |----------------------|
  | err_no  | number   | 是       | 错误码，0代表请求成功          |
  | err_msg | string   | 是       | 错误码描述                |
  | api_list   | array    | 是      | API列表，该字段包含的子字段见下文描述 |

  <font size=3>**▲ api_list 子项**</font>

  | 字段名          | 数据类型 | 是否必需 | 描述                                                         |
  | :-------------: | :-------: | :-------: | ----------------------------------------------------------- |
  | id | string   | 是       | API ID |
  | access_count | number | 否          | 7日访问数 |
  | active_status | string   | 是       | 活跃状态 |
  | api_match_rule_id | number | 否      | 定义规则ID |
  | api_max_body_args_cnt | number | 否      | body参数最大个数 |
  | api_max_body_size | number | 否      | body最大大小 |
  | api_max_query_args_cnt | number | 否    | query参数最大个数 |
  | api_params_policy | string | 否    | 参数规则 |
  | api_path | string   | 是       | API路径 |
  | api_type | string   | 是       | API类型 |
  | app_ip | string   | 是       | 资产IP |
  | app_name | string   | 是       | 应用 |
  | app_site_id | string   | 是       | 应用ID |
  | attack_count | number | 否      | 7日攻击数 |
  | auth_scheme | string   | 否      | 认证方式 |
  | auto_business_types | array | 否      | 自动业务分类 |
  | auto_industry_types | array | 否      | 自动行业分类 |
  | bot_count | number | 否      | 7日Bot访问数 |
  | content_type | string   | 是       | 响应类型 |
  | created_by | string   | 是       | 资产来源 |
  | created_time | string   | 是       | 发现时间 |
  | department | string   | 否      | 部门 |
  | enable_args | bool | 是       | 是否开启参数检测 |
  | endpoint | string   | 是       | API |
  | forbidden_headers | array | 否      | 禁用头部 |
  | is_custom | bool | 是       | 是否手动添加 |
  | is_included_sub_path | bool | 是       | 是否匹配子路径 |
  | is_online | bool | 是       | 上下线状态 |
  | manual_business_types | array | 否      | 手动业务分类 |
  | manual_industry_types | array | 否      | 手动行业分类 |
  | method | string   | 是       | 方法 |
  | name | string   | 否      | API名称 |
  | network_type | string   | 否      | 内外网类型 |
  | path_prefix | string   | 否     | 应用路径前缀 |
  | pii_infos | string   | 否      | 敏感类型 |
  | request_pii_infos | array | 否      | 请求敏感类型 |
  | required_headers | array | 否      | 必需头部 |
  | response_pii_infos | array | 否      | 响应敏感类型 |
  | responsible | string   | 否      | 责任人 |
  | risk_level | string   | 否      | 风险评级 |
  | risks | array | 否      | 缺陷名称 |
  | servers | string   | 是       | 关联域名 |
  | split_param_enabled | bool | 是       | 是否开启参数拆分 |
  | split_params | string   | 否      | 参数拆分规则 |
  | updated_time | string   | 是       | 修改时间 |
  | visited_time | string   | 是       | 最近访问时间 |



## 添加API

- **URL：**

  `POST https://IP:port/api/v2/abd/api`

- **请求参数：**

  | 字段名                 | 数据类型 | 是否必需 | 描述                                                         |
  | :-----------: | :-------: | :-------: | ----------------------------------------------------------- |
  | api_name               | string   | 否    | API名称                                                      |
  | app_site_name   | string   | 是       | API所属的应用名称，如`www.a.com`                  |
  | method                 | string   | 是       | API的方法，取值为：GET、POST、HEAD、PUT、DELETE、PATCH、OPTIONS、MKCOL、COPY、MOVE、PROPFIND、PROPPATCH、LOCK、UNLOCK、CONNECT、TRACE |
  |        endpoint        |  string  |    是    | API路径。支持大括号扩起的模板参数，例如/abc/{user_id}。支持配置query参数、form和json形式的body参数，格式为路径后输入?，然后指定参数位置（args、form、json）、参数名和参数值，例如/abc/{user_id}?args.token=123 |
  |  is_included_sub_path  |   bool   |    是    | 值为True/False，分别代表匹配子路径/不匹配子路径。值为True时，endpoint的路径部分必须以"/*"结尾 |
  | enable_args            | bool | 否       | 值为True/False，分别代表启用/禁用参数检测功能，默认为禁用    |
  | api_max_body_args_cnt  | number   | 否       | API最大body参数个数，取值范围：-1-100，-1表示不限制          |
  | api_max_query_args_cnt | number   | 否       | API最大query参数个数，取值范围：-1-100，-1表示不限制         |
  | api_max_body_size      | number   | 否       | API最大body字节大小，取值范围：-1-1000000000（1GB），-1表示不限制 |
  | required_headers | array | 否       | API请求必须包含所有配置的头部，多个头部用英文逗号分隔        |
  | forbidden_headers      | array | 否       | API请求不能包含任何一个配置的头部，多个头部用英文逗号分隔    |
  | business_types | array    | 否       | 业务分类标签，取值为：登录、上传、下载等内置标签和用户自定义标签 |


- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-------: | :--------: | :--------: | --------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功 |
  | err_msg | string   | 是       | 错误码描述            |

## 修改API

- **URL：**

  `PUT https://IP:port/api/v2/abd/api`

- **请求参数：**

  |     字段名     | 数据类型 | 是否必需 | 描述                                                         |
  | :------------: | :------: | :------: | ------------------------------------------------------------ |
  |       id       |  string  |    是    | API的ID                                                      |
  |    api_name    |  string  |    否    | API的名称                                                    |
  | industry_types |  array   |    否    | 行业分类标签                                                 |
  | business_types |  array   |    否    | 业务分类标签，取值为：登录、上传、下载等内置标签和用户自定义标签。 |


- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

## 删除API

- **URL：**

  `DELETE https://IP:port/api/v2/abd/api`

- **请求参数：**

  | 字段名          | 数据类型 | 是否必需 | 描述              |
  | :-----------------: | :-------: | :-------: | ------------------------------- |
  | id                     | string   | 是       | API的ID |

  > **💡 提示：**
  >
  > API的ID可以通过GET请求当前API列表的方式获得，具体方法请参考本文档**【获取当前API列表】**小节的内容。


- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                           |
  | :-------: | :--------: | :--------: | ------------------------------ |
  | err_no  | number   | 是       | 错误码，0代表请求成功          |
  | err_msg | string   | 是       | 错误码描述                     |


## 从API列表忽略API

- **URL：**

  `PUT https://IP:port/api/v2/abd/ignore_api`

- **请求参数：**

  | 字段名                       | 数据类型 | 是否必需 | 描述                   |
  | :----------:| :-------: | :-------: | ------------------------------------ |
  | id                     | string   | 是       | API的ID |

  > **💡 提示：**
  >
  > API的ID可以通过GET请求当前API列表的方式获得，具体方法请参考本文档**【获取当前API列表】**小节的内容。


- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                           |
  | :-------: | :--------: | :--------: | ------------------------------ |
  | err_no  | number   | 是       | 错误码，0代表请求成功          |
  | err_msg | string   | 是       | 错误码描述                     |

## 上/下线API

- **URL：**

  `POST https://IP:port/api/v2/abd/api_online`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | id     | string   | 是       | API的ID             |
  | status | string   | 是       | 值为on/off，分别代表上线/下线 |

  > **💡 提示：**
  >
  > API的ID可以通过GET请求当前API列表的方式获得，具体方法请参考本文档**【获取当前API列表】**小节的内容。

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

## 导出API列表

- **URL：**

  `GET https://IP:port/api/v2/abd/api_export`

- **请求参数：**

   | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | file_type | string   | 是       | 指定导出格式，取值为abd_csv或者openapi_3.0_json |

- **正常返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | api.csv  | file   | 是       | API列表文件 |

- **异常返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |

## 批量导入API列表

- **URL：**

  `POST https://IP:port/api/v2/abd/api_import`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | api_list  | string | 是       | 上传的API列表文件，必须为csv文件，且格式必须与导出的API文件一致 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |


## 获取当前应用列表

- **URL：**

  `GET https://IP:port/api/v2/abd/app_site`

- **请求参数：**

  无

- **返回值：**

  | 字段名     | 数据类型 | 是否必需 | 描述                  |
  | ---------- | :--------: | :--------: | --------------------- |
  | err_no     | number   | 是       | 错误码，0代表请求成功 |
  | err_msg    | string   | 是       | 错误码描述            |
  | app_site | array    | 是       | API应用列表       |


## 添加应用

- **URL：**

  `POST https://IP:port/api/v2/abd/app_site`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  | :----: | :-------: | :-------: | ----------------------------- |
  | name   | string   | 是       | API应用名称，如`test`    |
  | related_domain | array | 是 | 关联域名，至少指定一个，例如：[{"host": "www.a.com", "port": 8080}, {"host": "www.b", "port": 80}] |
  | responsible | string   | 否       | API应用责任人，可为空       |
  | username | string   | 否       | API应用指派的系统用户，可以指定多个用户，中间用逗号隔开；可为空 |
  | department | string   | 否       | API应用所属部门，可为空  |
  | app_group_name | string   | 否       | API应用所属分组，可为空  |
  | path_prefix | string | 否 | 按路径拆分API应用的路径前缀参数 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                           |
  | :-------: | :--------: | :--------: | ------------------------------ |
  | err_no  | number   | 是       | 错误码，0代表请求成功          |
  | err_msg | string   | 是       | 错误码描述                     |

## 修改应用

- **URL：**

  `PUT https://IP:port/api/v2/abd/app_site`

- **请求参数：**

  |   字段名    | 数据类型 | 是否必需 | 描述                          |
  | :---------: | :------: | :------: | ----------------------------- |
  |     id      |  string  |    是    | API应用ID                     |
  |    name     |  string  |    否    | API应用名称，如`test`         |
  | responsible |  string  |    否    | API应用责任人，可为空         |
  |  username   |  array  |    否    | API应用指派的系统用户，可以指定多个用户，中间用逗号隔开；可为空 |
  | department  |  string  |    否    | API应用所属部门，可为空       |
  | app_group_name | string   | 否       | API应用所属分组，可为空  |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误码描述            |

## 删除应用
- **URL：**

  `DELETE https://IP:port/api/v2/abd/app_site`

- **请求参数：**

  | 字段名     | 数据类型 | 是否必需 | 描述                                 |
| :--------: | :-------: | :-------: | -------------------------------- |
| name | string   | 是       | API应用名称 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                           |
  | :-------: | :--------: | :--------: | ------------------------------ |
  | err_no  | number   | 是       | 错误码，0代表请求成功          |
  | err_msg | string   | 是       | 错误码描述                     |



## 上/下线API应用

- **URL：**

  `PATCH https://IP:port/api/v2/abd/app_site`

- **请求参数：**

  | 字段名 | 数据类型 | 是否必需 | 描述                          |
  |:------: | :--------: | :--------: | ----------------------------- |
  | name | string   | 是       | API应用名称      |
  | status | string   | 是       | 值为on/off，分别代表上线/下线 |

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                    |
  | :-------: | :--------: | :--------: | ----------------------- |
  | err_no  | number   | 是       | 错误码，0代表请求成功。 |
  | err_msg | string   | 是       | 错误码描述              |



## 获取当前忽略列表

- **URL：**

  `GET https://IP:port/api/v2/abd/ignore_api`

- **请求参数：**

  无

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | err_no      | number   | 是       | 错误码，0代表请求成功 |
  | err_msg     | string   | 是       | 错误码描述            |
  | ignore_list | array    | 是       | API忽略列表           |

## 获取缺陷列表

- **URL：**

  `GET https://IP:port/api/v2/abd/defects`

- **请求参数：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | defect_status |  number  |    是    | 查询的缺陷状态，取值取值可为0、1、2、3，分别对应待确认、待处理、已处理、已忽略的状态 |
  |      page       |  number  |    否    | 分页参数：起始页。默认值为1                                  |
  |    page_size    |  number  |    否    | 分页参数：每页缺陷条数。默认值为10，支持1~200的整数          |
  |   start_time    |  number  |    否    | 开始时间戳，单位秒，默认为1596273491。如果该时间距离当前不超过14天，则取值为时间戳所在的整点；如果超过14天，则取值为时间戳所在的当天0点。 |
  |    end_time     |  number  |    否    | 结束时间戳，单位秒，默认为查询时间。如果该时间距离当前不超过14天，则取值为时间戳所在的整点；如果超过14天，则取值为时间戳所在的当天0点。 |

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功                    |
  | err_msg |  string  |    是    | 错误码描述                               |
  | counts  |  number  |    否    | 满足查询条件的缺陷数量（非分页返回数量） |
  |  data   |  array   |    否    | 该缺陷对应的详细数据                     |

  **data** 字段含义：

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  |      defect_name      |  string  |    是    | 缺陷名称       |
  |        servers        |  array   |    是    | 来源应用站点   |
  |     defect_status     |  number  |    是    | 缺陷状态       |
  |       last_time       |  number  |    是    | 最后发现时间戳 |
  |        method         |  string  |    是    | API对应的方法  |
  |        api_id         |  string  |    是    | API的ID        |
  |      defect_kind      |  string  |    是    | 缺陷类型       |
  |     defect_level      |  string  |    是    | 缺陷等级       |
  |       endpoint        |  string  |    是    | API路径        |
  |     app_site_name     |  string  |    是    | 应用名称       |
  | manual_business_types |  array   |    是    | 业务分类       |

## 修改缺陷状态

- **URL：**

  `POST https://IP:port/api/v2/abd/defects`

- **请求参数：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | new_status |  number  |    是    | 缺陷新状态，修改状态和已有状态值不变时，不做变更 |
  | api_infos  |  array   |    是    | 待修改状态列表，数组最大条数为200。              |

  **api_infos** 格式如下：

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | api_id | string | 是 | API的ID  |
  | defect_name | string | 是 | 缺陷名称 |

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | err_no      | number   | 是       | 错误码，0代表请求成功 |
  | err_msg     | string   | 是       | 错误码描述            |
  | api_infos | array    | 否       | 更新失败的数据          |


## 获取攻击列表

- **URL：**

  `GET https://IP:port/api/v2/abd/attacks`

- **请求参数：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  |    page    |  number  |    否    | 分页参数：起始页。默认值为1                                  |
  | page_size  |  number  |    否    | 分页参数：每页攻击条数，用于分页。默认值为10，支持1~200的整数 |
  | start_time |  number  |    否    | 开始时间戳，单位秒，默认为系统当前时间 - 7 \* 24 \* 3600 |
  |  end_time  |  number  |    否    | 结束时间戳，单位秒，默认为系统当前时间                       |

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | err_no      | number   | 是       | 错误码，0代表请求成功 |
  | err_msg     | string   | 是       | 错误码描述            |
  | counts | number | 否 | 满足查询条件的攻击条数（非分页返回条数） |
  | data | array | 否 | 攻击清单详细数据 |

  **data** 字段含义

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  |    attack_name     |  string  |    是    | 攻击名称       |
  |     event_type     |  string  |    是    | 事件类型       |
  |       detail       |  string  |    是    | 事件详情       |
  |     start_time     |  number  |    是    | 开始时间       |
  |      end_time      |  number  |    是    | 结束时间       |
  |       period       |  string  |    是    | 统计周期       |
  |      rule_id       |  number  |    是    | 规则ID         |
  |       status       |  number  |    否    | HTTP响应状态码 |
  |   attack_source    |  string  |    是    | 攻击来源       |
  | attack_source_type |  string  |    是    | 攻击来源类型   |
  |     risk_level     |  string  |    是    | 攻击等级       |
  |     event_uuid     |  string  |    是    | 事件ID         |
  |   event_category   |  string  |    是    | 攻击类型       |
  |    related_api     |  array   |    是    | 攻击目标       |
  |     related_ip     |  string  |    是    | 关联IP         |
  |  related_account   |  array   |    是    | 关联帐号       |
  |  related_country   |  array   |    是    | 关联国家       |
  |  related_province  |  array   |    是    | 关联省（州）   |
  |    related_city    |  array   |    是    | 关联城市       |


## 获取敏感列表

- **URL：**

  `GET https://IP:port/api/v2/abd/piis`

- **请求参数：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  |    page    |  number  |    否    | 分页参数：起始页。默认值为1                                  |
  | page_size  |  number  |    否    | 分页参数：每页敏感数据条数。默认值为10，支持1~200的整数      |
  | start_time |  number  |    否    | 开始时间戳，单位秒，如果该时间距离当前不超过14天，则取值为时间戳所在的整点；如果超过14天，则取值为时间戳所在的当天0点。默认为系统当前时间 - 7 \* 24 \* 3600 |
  |  end_time  |  number  |    否    | 结束时间戳，单位秒，默认为系统当前时间。如果该时间距离当前不超过14天，则取值为时间戳所在的整点；如果超过14天，则取值为时间戳所在的当天0点 |

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | err_no      | number   | 是       | 错误码，0代表请求成功 |
  | err_msg     | string   | 是       | 错误码描述            |
  | counts | number | 否 | 满足查询条件的敏感数据条数（非分页返回条数） |
  | data | array | 否 | 敏感清单详细数据 |

  **data** 字段含义

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  |      pii_level       |  string  |    是    | 敏感信息的风险等级 |
  |       api_name       |  string  |    是    | API名称            |
  |       servers        |  array   |    是    | 来源应用站点       |
  | recent_detected_time |  number  |    是    | 最后发现时间       |
  | first_detected_time  |  number  |    是    | 第一次发现时间     |
  |   detected_counts    |  number  |    是    | 发现次数           |
  |      app_site_id     |  string  |    是    | API应用ID         |
  |        method        |  string  |    是    | API对应的方法      |
  |        api_id        |  string  |    是    | API的ID            |
  |       endpoint       |  string  |    是    | API路径            |
  |     path_prefix      |  string  |    是    | API资源路径前缀    |
  |    app_site_name     |  string  |    是    | 应用名称           |
  |       pii_type       |  string  |    是    | 敏感信息类型       |
  |    pii_positions     |  array   |    是    | 敏感信息位置       |
  | is_included_template |   bool   |    是    | API是否带模板      |
  |      is_online       |   bool   |    是    | 是否在线           |
  | is_included_sub_path |   bool   |    是    | 是否包含子路径     |
  |   pii_value_sample   |  string  |    是    | 敏感信息样例       |
  |    industry_types    |  array   |    是    | 行业分类           |
  |    industry_levels   |  array   |    是    | 行业分级           |

## 添加敏感信息API白名单

- **URL：**

  `PUT https://IP:port/api/v2/abd/piis/whitelist`

- **请求参数：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | api_id | string | 是 | API的ID |
  | pii_type | string | 是 | 敏感信息类型 |

  > **💡 提示：**
  >
  > 请求成功后，将增量添加白名单，原有白名单不会被覆盖。单个敏感信息类型的白名单总数不能超过100条。如果新增的白名单导致总数超限、重复添加，则请求将失败，会返回错误码4。

  请求示例如下：

```
{
    "api_id": "2h50WCT9zDT8w.OHnek01hd_7H3",
    "pii_type": "中国银联卡"
}
```

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述                  |
  | :-----------: | :--------: | :--------: | --------------------- |
  | err_no      | number   | 是       | 错误码，0代表请求成功 |
  | err_msg     | string   | 是       | 错误码描述            |
  | data | string | 否 | 详细数据 |

{% endif %}{# 结束判断api资产管理仅限riversec、ngwaf、abd、asa #}
# 智能威胁研判

## payload分析

- **URL：**

  `POST https://IP:port/api/v1/ai_threat_analysis`

- **请求参数：**

  |       字段名        | 数据类型 | 是否必需 | 描述                                                                                                                                             |
  |:----------------:| :--------: | :--------: |------------------------------------------------------------------------------------------------------------------------------------------------|
  |     payload      | string | 是 | 待分析的payload                                                                                                                                    |
  | payload_position | string | 是 | payload的位置，取值范围：ALL_REQUEST（完整请求）、 REQUEST_BODY（请求体）、REQUEST_URI（URL）、 REQUEST_USER_AGENT（UA）、REQUEST_REFERER（Referer）、 REQUEST_COOKIE（Cookie） |
  |   content_type   | string | 否 | payloadLocation选择请求体时contentType的类型，取值范围：application/json、text/plain、application/x-www-form-urlencoded、application/xml                                  |

- **返回值：**

  | 字段名      | 数据类型 | 是否必需 | 描述          |
  | :-----------: | :--------: | :--------: |-------------|
  | err_no      | number   | 是       | 错误码，0代表请求成功 |
  | err_msg     | string   | 是       | 错误码描述       |
  | err_extra | json string | 否 | 附加错误信息表     |
  | threat_impact | string | 否 | 威胁影响        |
  | threat_type | string | 否 | 威胁类型        |
  | potential_impact | string | 否 | 可能影响        |
  | attack_position | string | 否 | 攻击位置   |
  | protection_suggestion | string | 否 | 防护建议        |
  | intent_analysis | string | 否 | 意图分析        |
  | request_payload | string | 否 | 请求载荷        |
  | risk_level | string | 否 | 风险等级        |
  | threat_feature | string | 否 | 威胁特征        |
  | payload_encoding_feature | string | 否 | payload编码特征 |

# 告警

## 查询告警信息

- **URL：**

  `GET https://IP:port/api/v1/system/alarms`

- **请求参数：**

  |   字段名   | 数据类型 | 是否必须 | 描述                    |
  | :--------: | :------: | :------: | ---------------------------------------- |
  |    name    |  string  |    否    | 告警的名字，选择多个时用半角逗号分隔  |
  |  severity  |  string  |    否    | 告警的等级，选择多个时用半角逗号分隔  |
  | event_type |  string  |    否    | 告警的状态，选择多个时用半角逗号分隔  |
  | start_time |  string  |    否    | 告警产生的时间范围-起，使用精准到秒的 UNIX 时间戳 |
  |  end_time  |  string  |    否    | 告警产生的时间范围-止，使用精准到秒的 UNIX 时间戳 |

  **name**字段的说明：

  | name                               | 告警名称                     |
  | ---------------------------------- | ---------------------------- |
  | GracePeriod14d                     | 14天缓冲期                   |
  | AiServiceUnavailable               | AI服务未启用                 |
  | AllUpstreamHealth                  | 上游健康状态(全部)           |
  | UpstreamHealth                     | 上游健康状态(单独)           |
  | DynamicGoodBotCacheFull            | 动态善意机器人缓冲区达到上限 |
  | KeepAlivedState                    | 双机热备状态变化             |
  | GoodBotAutoUpdateFailed            | 善意机器人情报库自动升级失败 |
  | ThreatIntelligenceAutoUpdateFailed | 威胁情报库自动更新失败       |
  | NginxCPULimit                      | 安全应用CPU超限              |
  | NginxMemoryLimit                   | 安全应用内存超限             |
  | LogDiskQuotaLimitExceeded          | 容量设置超限                 |
  | PhoenixLicMasterError              | 报表授权服务器错误           |
  | LicenseWarning                     | 授权警告                     |
  | LicenseError                       | 授权错误                     |
  | DiskLimitVar                       | 日志分区磁盘超限             |
  | LogSenderState                     | 日志发送器状态变化           |
  | CoreDump                           | 程序崩溃                     |
  | SystemCPULimit                     | 系统CPU超限                  |
  | SystemMemoryLimit                  | 系统内存超限                 |
  | DiskLimitRoot                      | 系统分区磁盘超限             |
  | EmergencyModeSoftWareBypass        | 紧急模式(业务透传)           |
  | EmergencyModeHardWareBypass        | 紧急模式(流量透传)           |
  | WebPageTamper                      | 网页疑似被篡改               |
  | SwitchBypass                       | 自动切换网卡Bypass           |
  | NodeServiceError                   | 节点服务异常                 |
  | WAFRuleAutoUpdateFailed            | 规则库自动更新失败           |
  | ClusterVersionInconsistent         | 集群版本不一致               |
  | ClusterNodeDisconnected            | 集群节点掉线                 |
  | NodeDisconnected                   | 集群连接失败                 |
  {% if cfg is inLayouts(["riversec","ngwaf","waf","cetcwaf"]) %}{# 不在abd和asa里显示的告警 #}  | QPSWarning                         | QPS配额告警                 |
  | QPSError                           | QPS使用超限                 |
  | AjaxConfigLimit                    | Ajax相关配置的总大小超限      |
  {% endif %}{# 不在abd和asa里显示的告警 #}

  **severity**字段的说明：

  | severity | 告警等级 |
  | :------: | -------- |
  |    20    | 警告     |
  |    40    | 较重     |
  |    50    | 严重     |

  **event_type**字段的说明：

  | event_type | 告警状态 |
  | :--------: | -------- |
  |     0      | 事件     |
  |     1      | 未恢复   |
  |     2      | 自动恢复 |
  |     3      | 手动恢复 |

- **返回值：**

  |   字段名    | 数据类型 | 是否必需 | 描述                       |
  | :---------: | :------: | :------: | -------------------------- |
  |   err_no    |  number  |    是    | 错误码，0代表请求成功      |
  |   err_msg   |  string  |    是    | 错误码描述                 |
  | alarm_data  |  array   |    是    | 系统中的告警信息           |
  | data_intact |   bool   |    是    | 数据是否来自集群中所有节点 |

  **alarm_data** 的示例如下：

```
"alarm_data": [
    {
        "recovered_time": -1,
        "event_type": 1,
        "name": "LicenseWarning",
        "node_ip": "**********",
        "label": "授权警告",
        "content": "当前集群的指纹信息已经改变，可信度降低，请尽快更新授权许可。",
        "alarm_id": "1546720250270060544",
        "created_time": **********,
        "severity": 50
    },
    {
        "recovered_time": -1,
        "event_type": 1,
        "name": "LicenseWarning",
        "node_ip": "**********",
        "label": "授权警告",
        "content": "当前集群的指纹信息已经改变，可信度降低，请尽快更新授权许可。",
        "alarm_id": "1546720250270012333",
        "created_time": **********,
        "severity": 50
    }
]
```


# 地理位置纠正

## 查询规则列表

- **URL：**

  `GET /api/v1/system/custom_geo_ip`

- **请求参数：**

  无

- **返回值：**

  |    字段名     | 数据类型 | 是否必需 | 描述          |
  |:----------:| :--------: |:-----------:|-------------|
  |   err_no   | number   | 是  | 错误码，0代表请求成功 |
  |  err_msg   | string   | 是  |  错误信息描述     |
  | result | array | 是 | 规则列表 |

## 新增规则
- **URL：**

  `POST /api/v1/system/custom_geo_ip`

- **请求参数：**

  请求参数为 **array**，数组中每个元素对应一条新增的规则，每条规则对应的字段如下。

  | 字段名 | 数据类型 | 是否必需 | 描述 |
  |:-----:|:------:|:-----:|-------|
  | ip    | string | 是 | 掩码格式的IP地址 |
  |country| string | 否 | 国家 |
  |province|string |否 | 省份 |
  |city   | string | 否 | 城市 |
  |intranet| boolean | 是 | 是否当做内网 |

  请求示例如下：

```
[
    {
        "ip": "***********/32",
        "country": "xx",
        "province": "yy",
        "city": "zz",
        "intranet": true
    },
    {
        "ip": "***********/24",
        "country": "aa",
        "province": "bb",
        "city": "cc",
        "intranet": false
    }
]
```

- **返回值：**

  |    字段名     | 数据类型 | 是否必需 | 描述          |
  |:----------:| :--------: |:-----------:|-------------|
  |   err_no   | number   | 是  | 错误码，0代表请求成功 |
  |  err_msg   | string   | 是  |  错误信息描述       |


## 编辑规则
- **URL：**

  `PUT /api/v1/system/custom_geo_ip`

- **请求参数：**

  请求参数为 **array**，数组中每个元素对应一条需要修改的规则，每条规则对应的字段如下。

  | 字段名 | 数据类型 | 是否必需 | 描述 |
  |:-----:|:------:|:-----:|-------|
  | old_ip    | string | 是 | 被修改配置的原始IP |
  | ip    | string | 是 | 修改后新的IP |
  |country| string | 否 | 国家 |
  |province|string |否 | 省份 |
  |city   | string | 否 | 城市 |
  |intranet| boolean | 是 | 是否当做内网 |

  如果不改变IP的值，只需要将 **old_ip** 与 **ip** 值设置为相同即可。

  请求示例如下：

```
[
    {
        "old_ip": "***********/32",
        "ip": "***********/32",
        "country": "xx",
        "province": "yy",
        "city": "zz",
        "intranet": true
    }
]
```

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误信息描述          |

## 删除规则
- **URL：**

  `DELETE /api/v1/system/custom_geo_ip`

- **请求参数：**

  请求参数为 **array**，数组中每个元素对应一条需要删除的规则，每条规则对应的字段如下：

  | 字段名 | 数据类型 | 是否必需 | 描述 |
  |:-----:|:------:|:-----:|-------|
  | ip    | string | 是 | 修改后新的IP |
  |country| string | 否 | 国家 |
  |province|string |否 | 省份 |
  |city   | string | 否 | 城市 |
  |intranet| boolean | 是 | 是否当做内网 |

  请求示例如下：
```
[
    {
        "ip": "***********/32",
        "country": "xx",
        "province": "yy",
        "city": "zz",
        "intranet": true
    }
]
```

- **返回值：**

  | 字段名  | 数据类型 | 是否必需 | 描述                  |
  | :-----: | :------: | :------: | --------------------- |
  | err_no  |  number  |    是    | 错误码，0代表请求成功 |
  | err_msg |  string  |    是    | 错误信息描述          |


# 错误码和错误信息

| 错误码（err_no） | 错误信息（err_msg）       | 含义 |
| :--------------: | ------------------------------------------------------------ | ------------------------------------------------------------ |
|0|Success|请求成功|
|1|Method not allowed|请求方法不允许|
|2|Failed to validate token|Token（令牌）校验失败|
|3|No token for tokenid in request|请求中缺少Token（令牌）|
|4|Argument error when calling RESTful API|请求参数错误|
|5|Error when calling RESTful API, please check operation log for more detail|内部错误，详情请查看操作日志|
|6|Webconsole account does not exist|管理界面账户不存在|
|7|Illegal upgrade package|非法升级包|
|8|Upgrading or rolling back|正在升级或回滚|
|9|No rollback version exists|没有可用的回滚版本|
|10|License is invalid|无效的授权文件|
{% if cfg is inLayouts(["riversec","ngwaf","abd","asa"]) %}{# 11-27的错误码不在safeplus里显示 #}
|11| The new submission of UBB rule is the same as the current one|“可编程对抗”规则文件已存在|
|12| Illegal UBB rule|无效的“可编程对抗”规则文件|
|13|The number of resource files has reached the limit of 200.|资源文件数已达上限200个|
|14|The size of resource folder has reached the limit of 200M.|资源文件已达上限200M|
|15|API is configured in the API Sequence Rule|该API在【API调用顺序】规则中使用|
|16|API file only supports UTF-8 encoding.|API文件只支持UTF-8编码|
|17|API file format error.|API文件格式解析错误|
|18|Data requested has exceeded the allowed number of entries.|请求数据超过最大限制条数|
|19|API does not exist|API不存在|
|20|API already exists|API已存在|
|21|API is configured in the API account tracking|该API在【账号跟踪】中使用|
|22|API is configured in the API Attack Policy|该API在【攻击策略】中使用|
|23|App site is configured in the API Attack Policy|该应用在【攻击策略】中使用|
|24|App site is offline, can not bring API online|应用已下线，无法再次下线属于该应用的API|
|25|Invalid request URL args|请求URL参数非法|
|26|Invalid request body args |请求体参数值非法或参数缺失|
|27|App site already exists|应用已存在|
{% endif %}{# 11-27的错误码不在safeplus里显示 #}
|28| Tokenid has no permission of requested method  | 请求中令牌ID不能访问指定的方法  |
|29|Incorrect json body| json数据错误|
{% if cfg is inLayouts(["riversec","ngwaf","abd","asa"]) %}{# 30-31的错误码不在safeplus里显示 #}|30| Invalid parameters contained in API  | 添加的API含有非法参数 |
|31|App site does not exist| 应用不存在 |
{% endif %}{# 30-31的错误码不在safeplus里显示 #}
|32|Timeout| 超时 |
|33|Invalid geolocation correction IP| 地理位置纠正无效IP|
|34|WAF strategy id is being used and cannot be deleted|{{ cfg | waf_name }}策略正在被使用，不能删除 |
|35|Illegal WAF strategy|{{ cfg | waf_name }}策略配置数据非法|
|36|The strategy name is duplicated| {{ cfg | waf_name }}策略名称重复|
|37|The module type does not exist| {{ cfg | waf_name }}防护模块不存在|
|38|Missing required fields|缺少必要的数据字段 |
|39|The number of strategies has reached the limit|{{ cfg | waf_name }}策略数量达到限制 |
|40|WAF strategy id does not exist| {{ cfg | waf_name }}策略ID不存在 |
|41|An unknown error occurred| 未知错误|
|42|Failed to validate waf_ruleset| 验证规则库失败|
|43|Failed to install waf_ruleset| 升级规则库失败|
{% if cfg is inLayouts(["riversec","ngwaf","abd","asa"]) %}{# 44-49的错误码不在safeplus里显示 #}|44|App server already exists| 应用中的关联域名与其他应用的关联域名重复，无法添加应用|
|45|Failed to update API| 更新API失败 |
|46|The app or API is configured in other functions| 应用或API已经被引用 |
|47|Field not allowed| 非法字段 |
|48|API role is not enabled| API安全管控角色不可用 |
|49|Failed to update the data in api_infos, please check for accuracy.| api_infos中的数据更新失败，请检查准确性。 |
{% endif %}{# 44-49的错误码不在safeplus里显示 #}
|50|The total number of items in the list has exceeded the limit| 名单中的条目总数已超限 |
|51|The API is unavailable in mirror deployment.| 镜像部署下该API不可用 |
|52|Sailfish server does not exist.| 大数据分析角色不存在 |
|57|One Click Switch operation falied| 一键切换操作失败 |
