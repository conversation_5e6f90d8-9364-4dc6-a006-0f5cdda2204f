# -*- coding:utf-8 -*-
"""
    Author : <PERSON> : <EMAIL>
    Created on : 2018/8/3 14:34:18
"""
from django.conf.urls import url

from web_admin import views_proxy as stub_proxy_views, views as stub_views
from proxy import views

urlpatterns = [
    url(r'^$', stub_proxy_views.proxys,  name='proxy_config'),
    url(r'^global_settings/$', stub_proxy_views.proxy_global_settings),
    url(r'^global_settings/multi_endpoints_strategy/$', stub_proxy_views.multi_endpoints_strategy),
    url(r'^detail/$', stub_proxy_views.proxy_detail, name='proxy_detail'),
    url(r'^export/$', stub_proxy_views.export_upstream, name='export_upstream'),
    url(r'^exportall/$', stub_views.export_all_config, name='export_all_config'),
    url(r'^scheduled_config_export_records/$', stub_views.scheduled_config_export_records, name='scheduled_config_export_records'),
    url(r'^save_replace/$', stub_proxy_views.save_replace, name='proxy_config'),
    url(r'^save_watermark/$', stub_proxy_views.save_watermark, name='save_watermark'),
    url(r'^err_page_src/$', stub_proxy_views.err_page_src, name='proxy_errpage_show'),
    url(r'^check_file_size/$', stub_proxy_views.check_file_size, name='check_file_size'),
    url(r'^add_encapsulation/$', stub_proxy_views.proxy_add_encapsulation, name='proxy_add_encapsulation'),
    url(r'^add_request_whitelist/$', stub_proxy_views.proxy_add_request_whitelist, name='proxy_add_request_whitelist'),
    url(r'^add_wechat_request_whitelist/$', stub_proxy_views.proxy_add_wechat_request_whitelist, name='proxy_add_wechat_request_whitelist'),
    url(r'^add_alipay_mpp_request_whitelist/$', stub_proxy_views.proxy_add_alipay_mpp_request_whitelist, name='proxy_add_alipay_mpp_request_whitelist'),
    url(r'^add_mpaas_mpp_request_whitelist/$', stub_proxy_views.proxy_add_mpaas_mpp_request_whitelist, name='proxy_add_mpaas_mpp_request_whitelist'),
    url(r'^detail/getplaintext/$', stub_proxy_views.proxy_get_plain_text, name='proxy_get_plain_text'),
    url(r'^ajax/check_white_ip_conflict/$', stub_proxy_views.check_white_ip_conflict, name='check_white_ip_conflict'),
    url(r'^download_ip_black_file/$', stub_proxy_views.download_ip_black_file, name='download_ip_black_file'),
    url(r'^download_ip_white_file/$', stub_proxy_views.download_ip_white_file, name='download_ip_white_file'),
    url(r'^upload_ip_black_file/$', stub_proxy_views.upload_ip_black_file, name='upload_ip_black_file'),
    url(r'^upload_ip_white_file/$', stub_proxy_views.upload_ip_white_file, name='upload_ip_white_file'),
    url(r'^enable_alert_box_for_cookie/$', stub_proxy_views.enable_alert_box_for_cookie, name='enable_alert_box_for_cookie'),
    url(r'^enable_strictly_match_host/$', stub_proxy_views.enable_strictly_match_host, name='enable_strictly_match_host'),
    url(r'^enable_slow_http_attack/$', stub_proxy_views.enable_slow_http_attack, name='enable_slow_http_attack'),
    url(r'^enable_gm_algorithm/$', stub_proxy_views.enable_gm_algorithm, name='enable_gm_algorithm'),
    url(r'^enable_ip_black/$', stub_proxy_views.enable_ip_black, name='enable_ip_black'),
    url(r'^enable_ip_white/$', stub_proxy_views.enable_ip_white, name='enable_ip_white'),
    url(r'^clear_ip_black/$', stub_proxy_views.clear_ip_black, name='clear_ip_black'),
    url(r'^clear_ip_white/$', stub_proxy_views.clear_ip_white, name='clear_ip_white'),
    url(r'^direct_connection_control/$', stub_proxy_views.handle_direct_conn_ctrl, name='handle_direct_conn_ctrl'),
    url(r'^add_waf_whitelist/$', stub_proxy_views.proxy_add_waf_whitelist, name='proxy_add_waf_whitelist'),
    url(r'^good_bot_settings/$', stub_views.good_bot_settings),
    url(r'^good_bot_settings/check_dns/$', stub_views.check_dns),
    url(r'^good_bot_settings/check_upgrade_service/$', stub_views.check_upgrade_service),
    url(r'^upload_good_bot_file/$', stub_views.upload_good_bot_file),
    url(r'^enable_keep_src_ip/$', stub_views.enable_keep_src_ip),
    url(r'^enable_admin_listen/$', stub_views.enable_admin_listen),
    url(r'^add_site_customize_name/$', stub_proxy_views.add_site_customize_name, name='add_site_customize_name'),
    url(r'^enable_site_conf/$', stub_proxy_views.enable_site_conf, name='enable_site_conf'),
    url(r'^enable_token_rename/$', views.TokenRenameView.as_view(), name='enable_token_rename'),
    url(r'^upstream_status/$', views.NginxUpstreamStatusView.as_view(), name='upstream_status'),
    url(r'^full_flow_collection/$', views.FullFlowCollection.as_view(), name='full_flow_collection'),
    url(r'^stream_forward/$', stub_proxy_views.stream_forward),
    url(r'^read_protection_list_file/$', stub_proxy_views.read_protection_list_file, name='read_protection_list_file'),
    url(r'^one_click_switch/$', stub_proxy_views.one_click_switch, name='one_click_switch'),
]
