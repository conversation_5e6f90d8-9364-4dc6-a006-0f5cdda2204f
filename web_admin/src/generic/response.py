# -*- coding:utf-8 -*-
"""
    Author : <PERSON> : <EMAIL>
    Created on : 2018/8/3 15:38:17
"""

from functools import partial

from django.http import JsonResponse
from django.template.response import TemplateResponse
from django.utils.functional import SimpleLazyObject
from django.utils.translation import ugettext as _
from django.core.cache import caches

from asp_conf_ctrl import GetConfDb, ConfDb
from asp_utils.utils import get_layout, get_product_type, get_license_product_category, is_product_category_NGWAF, get_is_safeplus_or_ngwaf_we,get_is_botgate_or_ngwaf_le, is_web_filter_installed, is_primary_protection, get_bidding_ver, RESULT, in_container, in_k8s, get_build_hash_and_layout_hash, current_machine_hardware_name, os_id, not_ubuntu, get_language, get_copyright
from asp_utils.network_protect_util import is_support_current_platform
from asp_utils.bond_util import get_bond_info
from generic.conf.conf_webconsole import is_dev_mode_enable
from generic.conf import NginxConf, WebconsoleConf, NetworkConf
from generic.utils import HealthyStatus, check_nginx_config_status
from generic.constants import asp_version
from service_mgr_rest_api import query_online_phoenix_nodes, query_online_sailfish_nodes, service_mgr_set_asp_config, \
    query_online_external_sf_nodes
from web_admin.site_acl_util import get_sites_4_user, get_users_with_role
from web_admin.decorators import has_permission, get_rbac_conf
from asp_utils.threat_intelligence_util import CtiDbUpgrade
from web_admin.Conf_Logserver import LogserverConf
import os
import copy
import logging
import platform

smart_conf = GetConfDb()


def is_external_storage_node_allOnline():
    online_exernal_sf_nodes = query_online_external_sf_nodes().get("external_node_list", [])
    confdb = ConfDb()
    if set(confdb.get_value('/sailfish/external_node_list', [])) == set(online_exernal_sf_nodes):
        return True
    else:
        return False


class CommonContextMixin(HealthyStatus):
    """
    Common Context Contains:
    * Healthy Status
    * Menu Context
    """

    def merge_common_context(self, request, _context):
        conf = WebconsoleConf()
        confdb = smart_conf.db()
        context = dict(_context) if _context else dict()
        user_name = request.user.username

        context['is_wizard_finished'] = conf.is_wizard_finished()
        context['copyright_year'] = get_copyright()
        context['brand'] = asp_version.brand_str
        context['is_build_debug'] = asp_version.current_is_debug
        context['is_prod_pkg'] = asp_version.current_is_prod_pkg
        context['build_info'] = asp_version.current_build_info
        context['build_commit_no'] = asp_version.current_git_commit_no
        context['build_hash_and_layout_hash'] = get_build_hash_and_layout_hash()
        context['machine_hardware_name'] = current_machine_hardware_name()
        context['os_id'] = os_id()
        context['is_chn_os'] = not_ubuntu()
        context['processor'] = platform.processor()
        context['is_base_render'] = 1
        context['layout'] = get_layout(True)
        context['product_type'] = get_product_type()
        context['license_product_category'] = get_license_product_category()
        context['is_bidding_ver'] = 0 if get_bidding_ver() == 'no' else 1
        context['web_filter_install'] = is_web_filter_installed()
        context['is_cloud_mode'] = conf.is_cloud_mode()
        context['is_dev_mode'] = is_dev_mode_enable()
        context['is_expert_mode'] = conf.is_expert_mode(user_name)
        context['is_web_filter_enable'] = conf.is_web_filter_enable(user_name)
        context['is_threat_intelligence_enable'] = CtiDbUpgrade(ConfDb()).get_threat_intelligence_enabled()
        context['is_support_network_protect'] = is_support_current_platform(confdb)
        context['is_network_ddos_protect_enable'] = confdb.network_ddos_protect_enabled()
        context['is_role_management_enable'] = conf.get_role_management_enable()
        context['is_enable_sensitive_log_filter_switch'] = LogserverConf().get_sensitive_log_filter_switch()
        context['session_idle_all'] = conf.get_session_idle_all()
        license_info = confdb.get_license_info()
        context['has_bta_server'] = confdb.has_bta_server()
        context['has_abd_server'] = confdb.has_abd_server()
        context['abd_server_count'] = confdb.get_abd_server_count()
        context['has_repu_server'] = confdb.has_repu_server()
        context['has_api_gateway'] = confdb.has_api_gateway()
        context['has_flowlearn_server'] = confdb.has_flowlearn_server()
        context['has_llm_server'] = confdb.has_llm_server()
        context['is_llm_server'] = confdb.is_llm_server()
        context['is_bta_app_enabled'] = license_info.is_module_effect(license_info.DM_BOT_THREAT_AWARENESS_APP)
        context['is_bta_biz_enabled'] = license_info.is_module_effect(license_info.DM_BOT_THREAT_AWARENESS_BIZ)
        context['is_bta_enabled'] = license_info.is_module_effect(license_info.DM_BOT_THREAT_AWARENESS)
        context['is_bsd_enabled'] = license_info.is_module_effect(license_info.DM_BIG_SCREEN_DISPLAY)
        context['is_bse_enabled'] = license_info.is_module_effect(license_info.DM_BIG_SCREEN_EDITOR)
        context['is_pd_enabled'] = license_info.is_module_effect(license_info.DM_PROGRAMMABLE_DEFENDING)
        context['is_dc_enabled'] = license_info.is_module_effect(license_info.DM_DYNAMIC_CHANLLENGE)
        context['is_api_enabled'] = license_info.is_module_effect(license_info.DM_API_MONITOR)
        context['is_ai_threat_analyst_enabled'] = license_info.is_module_effect(license_info.DM_AI_THREAT_ANALYST)
        context['protected_level']= license_info.get_license_protected_level()
        context['cluster_name'] = confdb.get_cluster_name()

        context['is_safeplus_or_ngwaf_we'] = get_is_safeplus_or_ngwaf_we(license_info)
        context['is_botgate_or_ngwaf_le'] = get_is_botgate_or_ngwaf_le(license_info)
        context['is_ngwaf'] = is_product_category_NGWAF()


        # _protected_level 仅用于控制动态的显示和功能
        # LE用于控制初级防护的动态
        # NGWAF的SKU是NE，动态就是初级防护，所以这里 _protected_level 共用 LE
        if asp_version.current_is_debug == 1 \
           and is_primary_protection() \
           and license_info.get_license_protected_level() not in ['WE', 'LE']:
                context['protected_level'] = 'LE'

        context['is_mpp_enabled'] = license_info.is_mpp_enabled()
        context['mobile_max_app_count'] = license_info.get_mobile_max_app()
        context['mobile_salt'] = license_info.get_mobile_salt()
        context['is_ai_service'] = confdb.is_ai_service()
        context['is_advanced_waf_enabled'] = license_info.is_module_effect(license_info.DM_ADVANCED_WAF)
        context['is_LLM_Protection_enabled'] = license_info.is_module_effect(license_info.DM_LLM_PROTECTION)
        context['is_show_cmdline_page'] = conf.is_access_cmdline() and request.META.get("HTTP_X_FORWARDED_ASP") == "asp"
        context['is_archive_server'] = confdb.is_archive_log_server()

        context['is_phoenix_server'] = confdb.is_phoenix_server()

        context['is_sailfish_server'] = confdb.is_sailfish_server()
        context['is_proxy'] = confdb.is_proxy()
        context['is_head_master'] = confdb.is_head_master()
        context['head_master_ip'] = confdb.get_head_master_ip()

        phoenix_server_count = len(confdb.get_phoenix_servers())
        context['has_phoenix_server'] = True if phoenix_server_count > 0 else False

        sailfish_server_count = len(confdb.get_sailfish_servers())
        context['has_sailfish_server'] = True if sailfish_server_count > 0 else False

        context['heterogeneous_cluster'] = confdb.is_heterogeneous_cluster()

        context['use_bypass_card'] = conf.use_bypass_card()
        context['bond_mode'] = confdb.get_value('_private/os/network/bond/mode', -1)
        context['transparent_status'] = conf.get_transparent_status()
        context['is_mirror_mode'] = conf.is_mirror_status()
        context['is_route_proxy_mode'] = confdb.is_route_proxy()
        context['is_ha'] = confdb.is_ha()
        context['deploy_mode'] = confdb.get_deploy_mode_name()
        context['is_transparent_mode'] = confdb.get_value('nginx/transparent_mode/enable', False)
        context['has_br1'] = os.path.exists('/sys/class/net/br1/')
        context['is_bonded'] = True if get_bond_info() else False
        context['in_container'] = in_container()
        context['in_k8s'] = in_k8s()
        context['cluster_language'] = get_language()

        is_connected_to_master = True
        if context['is_phoenix_server']:
            try:
                phoenix_online_nodes = query_online_phoenix_nodes()
                if phoenix_online_nodes.get('result') == RESULT.NOT_CONNECTED_TO_ZK:
                    is_connected_to_master = False
            except:
                phoenix_online_nodes = {}
            phoenix_online_list = phoenix_online_nodes.get('node_list', [])
            context['is_phoenix_online'] = True if confdb.get_log_ip() in phoenix_online_list else False

        else:
            try:
                sailfish_online_nodes = query_online_sailfish_nodes()
                if sailfish_online_nodes.get('result') == RESULT.NOT_CONNECTED_TO_ZK:
                    is_connected_to_master = False
            except:
                sailfish_online_nodes = {}
            sailfish_online_list = sailfish_online_nodes.get('node_list', [])
            context['is_sailfish_online'] = True if confdb.get_log_ip() in sailfish_online_list else False

        context['is_connected_to_master'] = is_connected_to_master

        context['is_phoenix_index'] = confdb.is_phoenix_server() and not confdb.is_search_head()

        nginxConf = NginxConf()
        context['is_valid_license'] = license_info.is_protected_on()
        context['webconsole_admin_port'] = nginxConf.get_conf().get_web_port()
        context['show_mobile_app'] = True \
            if hasattr(license_info, 'mobile_max_app') \
               and int(license_info.mobile_max_app) > 0 else False

        if not conf.is_wizard_finished() or request.path.startswith('/wizard/'):
            # in wizard
            return context

        # Variable 'cfg_iface' is from configDB and is used to update the response templates via its values
        networkConf = NetworkConf()
        cfg_iface = networkConf.clone_cfg_iface()
        if conf.is_cloud_mode():
            context['admin_adapter'] = NetworkConf().get_adapter_name('admin')
            context['admin_port'] = NetworkConf().get_admin_port()
            externel_iface = cfg_iface.get(NetworkConf.IF_EXTERNAL, {})
            user_defined_reserved_ports = cfg_iface.get(NetworkConf.USER_DEFINED_RESERVED_PORTS, '')
            cfg_iface = {
                NetworkConf.IF_EXTERNAL: {NetworkConf.DNS_LIST: externel_iface.get('dns_list', '')},
                NetworkConf.USER_DEFINED_RESERVED_PORTS: user_defined_reserved_ports
            }

        context['cfg_iface'] = cfg_iface

        system_status = self.node_status(False)

        alert_overview_color = 'red'
        for alert in system_status.get('nav_alerts', []):
            if alert.get('alert_level', '') == 'error':
                alert_overview_color = 'red'
                break
            else:
                alert_overview_color = 'yellow'

        status, bad_site_list = check_nginx_config_status(nginxConf, user_name)
        system_status.update({'alert_overview_color': alert_overview_color, 'nginx_config_status': status})
        system_status.update({"all_sf_external_storage_node_online": is_external_storage_node_allOnline()})

        context['system_status'] = system_status
        log_node_available = system_status.get('log_node_available')
        context['log_node_available'] = log_node_available
        context['has_phoenix_pkg'] = asp_version.current_has_phoenix_pkg

        user_role = conf.get_user_role(user_name)
        context['is_operator'] = user_role == 'Operator'
        context['role'] = user_role
        report_type = confdb.get_value('report_type', 'sailfish')
        if not context['has_phoenix_server'] and report_type == 'phoenix':
            service_mgr_set_asp_config({'report_type': 'sailfish'}, 'IP', 'User')
            report_type = 'sailfish'
        elif context['has_phoenix_server'] and not context['has_sailfish_server'] and report_type == 'sailfish':
            service_mgr_set_asp_config({'report_type': 'phoenix'}, 'IP', 'User')
            report_type = 'phoenix'
        context['report_type'] = report_type
        context['password_expire'] = conf.get_password_period(user_name)
        context['site_4_user'] = get_sites_4_user(user_name)
        context['user_list'] = get_users_with_role('all')

        mem_cache = caches['mem_cache']
        rbac = mem_cache.get('rbac', dict())
        if not rbac:
            rbac = get_rbac_conf()
            mem_cache.set('rbac', rbac)
        if user_name == 'admin':
            admin_acl = copy.deepcopy(rbac.get(user_role, {}))
            for v in admin_acl.values():
                v['read'] = True
                v['write'] = True
            context['acl'] = admin_acl
        else:
            context['acl'] = rbac.get(user_role, {})
        if not context['acl']:
            logging.error('Failed to get acl for user %s with role %s' % (user_name, user_role))
            logging.info('The rbac content is as following:\n %s' % rbac)
        context['menu'] = self._get_menu(context, user_name)
        context['is_full_flow_collection'] = conf.is_full_flow_collection_enabled()

        def ipv(ipstr):
            if ipstr is None:
                return None
            return 6 if ':' in ipstr else 4

        is_ip46 = len({
            ipv(confdb.get_value('_private/os/network/internal/ip')),
            ipv(confdb.get_value('_private/os/network/internal/dual_ip')),
        } - {None}) == 2
        context['isIPv46'] = is_ip46
        context['node_id'] = confdb.get_value('_private/node_id', '')

        # 高级验证
        context['is_advanced_verify_enable'] = confdb.get_value('nginx/enable_advanced_verify', False)

        return context

    def _get_menu(self, param, user_name):
        report_type = param['report_type']
        is_mirror_mode = param['is_mirror_mode']
        license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
        is_botgate_product_type = param['product_type'] == 'Botgate'
        is_safeplus_layout = param['product_type'] == 'Safeplus'
        is_safeplus_or_ngwaf_we = get_is_safeplus_or_ngwaf_we(license_info)
        is_botgate_or_ngwaf_le = get_is_botgate_or_ngwaf_le(license_info)
        is_abd_or_asa = (param['product_type'] == 'ApiBotDefender' or param['product_type'] == 'ApiSecurityAudit')

        if is_safeplus_layout:
            waf_overview_menu = self._get_waf_overview_menu(user_name)
            menus = [waf_overview_menu] if waf_overview_menu else []
        else:
            # 系统概要
            menus = [self._get_overview_menu()]

        # 保护设置
        if has_permission(user_name, 'Protection_List', 'read'):
            menus.append(self._get_protection_menu())

        # 文件监测
        if (is_botgate_product_type or is_product_category_NGWAF()) and (param['deploy_mode'] != 'plugin'):
            if license_info.is_file_monitor_supported() and (license_info.is_protected_on() or param['is_build_debug']):
                if has_permission(user_name, 'File_Monitoring', 'read'):
                    menus.append(self._get_file_menu())

        # WAF
        if has_permission(user_name, 'WAF_Strategy', 'read'):
            waf_menu = self._get_waf_scenes(param, license_info, user_name, is_safeplus_or_ngwaf_we)
            if waf_menu:
                menus.append(waf_menu)

        # llm
        if has_permission(user_name, 'LLM_Protection', 'read'):
            llm_menu = self._get_llm_scenes(param, license_info)
            if llm_menu:
                menus.append(llm_menu)

        # ai_threat_analyst
        if has_permission(user_name, 'AI_Threat_Analyst', 'read'):
            ai_threat_analyst_menu = self._get_ai_threat_analyst_scenes(param, license_info)
            if ai_threat_analyst_menu:
                menus.append(ai_threat_analyst_menu)

        # 可编程对抗 - abd有, mirror没有
        if has_permission(user_name, 'Programmable_Defending', 'read'):
            ubb_menu = self._get_ubb_scenes(param, license_info)
            if ubb_menu:
                menus.append(ubb_menu)

        # 移动保护 - abd无,waf无 mirror无
        if has_permission(user_name, 'Mobile_Protection', 'read'):
            mobile_menu = self._get_mobile_menu(param, license_info)
            if not is_mirror_mode and (is_botgate_or_ngwaf_le) and mobile_menu:
                menus.append(mobile_menu)

        # 小程序保护 - abd无，waf无 mirror无
        if has_permission(user_name, 'Mpp_Settings', 'read'):
            mpp_menu = self._get_mpp_menu(param, license_info)
            if not is_mirror_mode and is_botgate_or_ngwaf_le and mpp_menu:
                menus.append(mpp_menu)

        # API资产管理，waf无
        if has_permission(user_name, 'API_Means', 'read'):
            api_bot_defender_menu = self._get_api_bot_defender_menu(param, license_info)
            if api_bot_defender_menu and not is_safeplus_or_ngwaf_we:
                menus.append(api_bot_defender_menu)

        # 业务威胁感知，waf无
        if has_permission(user_name, 'Business_Threat_Awareness', 'read'):
            bta_menu = self._get_bta_menu(param, license_info)
            if bta_menu and not is_safeplus_or_ngwaf_we:
                menus.append(bta_menu)

        # 业务数据采集
        if has_permission(user_name, 'Data_Collection', 'read'):
            bta_collection_menu = self._get_bta_collection_menu(param, license_info)
            if bta_collection_menu:
                menus.append(bta_collection_menu)

        # 信誉库，waf无
        if has_permission(user_name, 'Reputation', 'read'):
            reputation_menu = self._get_reputation_menu(param, license_info)
            if reputation_menu and not is_safeplus_or_ngwaf_we:
                menus.append(reputation_menu)

        # 大屏展示 - abd无，waf无
        if has_permission(user_name, 'Big_Screen_Display', 'read'):
            screen_menu = self._get_screen_menu(param, license_info, report_type)
            if is_botgate_or_ngwaf_le and screen_menu:
                menus.append(screen_menu)

        # 静态Bot检测 - abd、asa有
        if has_permission(user_name, 'Static_Bot_Detection', 'read') and is_abd_or_asa:
            menus.append({"menuid": "staticBotDetection", "name": _("staticBotDetection"),
                     "href": "/static_bot_detection/",
                     "icon": "fa fa-search", "rsid": "bt_static_bot_detection"})

        #威胁情报库
        if has_permission(user_name, 'Threat_Intelligence', 'read') and (license_info.is_protected_on() or param['is_build_debug']):
            menus.append({"menuid": "threat_intelligence", "name": _("Threat Intelligence"), "href": "/threat_intelligence/",
                                       "icon": "fa fa-search-minus",
                                       "expertMode": True, "rsid": "bt_threat_intelligence",
                                       "if": "page.expertMode && global_params.isEnableThreatIntelligence"})

        #网络层DDOS防护
        if has_permission(user_name, 'Network_DDOS_Protect', 'read') and (license_info.is_protected_on() or param['is_build_debug']):
            menus.append({"menuid": "network_ddos_protect", "name": _("Network_Ddos_Protect"), "href": "/network_ddos_protect/",
                          "icon": "fa fa-shield",
                          "expertMode": True, "rsid": "bt_network_ddos_protect",
                          "if": "page.expertMode && global_params.isEnableNetworkDDOSProtect && global_params.isSupportNetworkDDOS"})

        if has_permission(user_name, 'Statistics', 'read'):
            # 报表分析 - abd无,WAF在
            if is_botgate_product_type:
                resport_log_menus = self._get_report_menu(param, user_name)
                menus.append(resport_log_menus)


        # 网页过滤 - abd无
        if has_permission(user_name, 'Web_Filter', 'write'):
            web_filter_menu = self._get_filter_menu(param)
            if (is_botgate_or_ngwaf_le or is_safeplus_or_ngwaf_we) and web_filter_menu and not ConfDb().is_plugin():
                menus.append(web_filter_menu)

        # 系统
        if has_permission(user_name, 'System_Navigator', 'read'):
            system_menu = self._get_system_menu(param, user_name)
            if system_menu:
                menus.append(system_menu)

        # 开发者模式
        if has_permission(user_name, 'Developer_Mode', 'write'):
            dev_mode_menu = self._get_dev_mode_menu(param)
            if dev_mode_menu:
                menus.append(dev_mode_menu)

        return menus

    # WAFlayout系统概要
    def _get_waf_overview_menu(self, user_name):
        # 报表信息 - Waf layout独立页面，二级菜单
        report_log_menus = {"menuid": "s_statistics", "name": _("Statistics_Report"),
                            "href": "javascript:void(0)",
                            "if": "global_params.logNodeAvailable",
                            "icon": "ic-statistic-o", "activeIcon": "ic-statistic", "rsid": "bt_s_statistics",
                            "children": []
                            }
        # Waf layout下 概览放在数据报表中
        report_log_menus['children'] += [
            {"menuid": "overview", "name": _('Overview'), "href": "/overview/",
             # "icon": "ic-overview-o",
             # "activeIcon": "ic-overview",
             "rsid": "bt_overview"}
        ]
        # 修改Security_Log权限为只有管理员可读写。
        if has_permission(user_name, 'Security_Log', 'read'):
            report_log_menus['children'] += [
                {"menuid": "security_log", "name": _("Security Log"),
                 "href": "/system/operation_log/security_log/",
                 "rsid": "bt_security_log"
                 }
            ]
        # Waf layout下的报表分析，与原本的报表分析一样
        if has_permission(user_name, 'Statistics', 'read'):
            report_log_menus['children'] += [
                {"menuid": "statistics", "name": _("Statistics"),
                 "href": "/statistics_app/",
                 "if": "global_params.logNodeAvailable", "rsid": "bt_statistics"}
            ]
        if report_log_menus['children']:
            return report_log_menus
        return None

    # 系统概要
    def _get_overview_menu(self):
        return {"menuid": "overview", "name": _('Overview'), "href": "/overview/", "icon": "ic-overview-o",
         "activeIcon": "ic-overview", "rsid": "bt_overview"}

    # 保护设置
    def _get_protection_menu(self):
        return {"menuid": "protection", "name": _('Protection'), "href": "/proxy/", "icon": "ic-briefcase-o",
                  "activeIcon": "ic-briefcase", "rsid": "bt_protection", "match_urls": "^/proxy/"}

    #文件监测
    def _get_file_menu(self):
        return { "menuid": "file", "name": _('File Monitoring'), "href": "/file_monitor/",
                 "icon": "fa fa-file-circle-check","rsid": "bt_file"}

    # 移动保护
    def _get_mobile_menu(self, param, license_info):
        is_debug = param['is_build_debug']
        show_mobile_app = param['show_mobile_app']

        if is_debug or (show_mobile_app and (license_info.is_protected_on() or is_debug)):
            return {"menuid": "mobile_protection", "name": _("Mobile Protection"), "href": "/mobile_authorization/",
                          "icon": "fa fa-mobile-screen-button", "rsid": "bt_mobile"}

        return None

    # 小程序保护
    def _get_mpp_menu(self, param, license_info):
        is_debug = param['is_build_debug']

        if license_info.is_mpp_enabled():
            return {"menuid": "mpp_protection", "name": _("Mini Program Protection"), "href": "/mpp/",
                          "icon": "fa fa-qrcode", "rsid": "bt_weixin"}

        return None

    # API资产管理 - VUE
    def _get_api_bot_defender_menu(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return None

        if (is_debug or (license_info.is_module_effect(license_info.DM_API_MONITOR) \
                            and (license_info.is_protected_on() or is_debug))) and ConfDb().has_abd_server():
            return {"menuid": "api_bot_defender", "name": _("API Means"), "href": "/index#/index",
                "icon": "fa fa-microchip", "rsid": "bt_api_bot_defender", "target": "_blank"}

        return None

    # WAF
    def _get_waf_scenes(self, param, license_info ,user_name, is_waf=False):
        is_debug = param['is_build_debug']
        is_plugin_mode = param['deploy_mode'] == 'plugin'
        is_abd_or_asa = (param['product_type'] == 'ApiBotDefender' or param['product_type'] == 'ApiSecurityAudit')

        if license_info.is_module_effect(license_info.DM_ADVANCED_WAF) and (
                license_info.is_protected_on() or is_debug):
            wafMenu = {"menuid": "waf", "name": _('WAF'), "href": "javascript:void(0)",
                       "icon": "fa fa-shield-cat", "activeIcon": "fa fa-shield-cat", "rsid": "bt_waf", "children": []}
            if has_permission(user_name, 'WAF_General_Protection', 'read'):
                wafMenu['children'] += [
                    {"menuid": "waf_general_protection", "name": _("Waf General Protection"), "href": "/waf_general_protection/",
                     "rsid": "bt_waf_general"},
                ]

            if has_permission(user_name, 'WAF_Power_Protection', 'read'):
                wafMenu['children'] += [
                    {"menuid": "waf_power_protection", "name": _("Waf Power Protection"), "href": "/waf_power_protection/",
                     "rsid": "bt_waf_power"},
                ]

            # if has_permission(user_name, 'LLM_Protection', 'read') and not is_plugin_mode:
            #     wafMenu['children'] += [
            #         {"menuid": "llm", "name": _('LLM Protection'), "href": "/llm_protection/",
            #           "rsid": "bt_llm"},]

            if has_permission(user_name, 'WAF_Compliance_Detection', 'read'):
                wafMenu['children'] += [
                    {"menuid": "waf_compliance_detection", "name": _("Compliance Detection"), "href": "/waf_compliance_detection/",
                     "rsid": "bt_waf_compliance_detection"},
                ]

            if has_permission(user_name, 'WAF_Global_Custom_Rule', 'read'):
                wafMenu['children'] += [
                    {"menuid": "waf_global_custom_protection", "name": _("Global Custom Rule"), "href": "/waf_global_custom_protection/",
                     "rsid": "bt_global_custom"},
                ]
            if has_permission(user_name, 'WAF_Global_Whitelist', 'read'):
                wafMenu['children'] += [
                    {"menuid": "bt_waf_global_whitelist", "name": _("Global WAF Whitelist"), "href": "/waf_global_whitelist/",
                     "rsid": "bt_waf_global_whitelist"},
                ]
            if param.get('has_flowlearn_server', False) and has_permission(user_name, 'WAF_Flow_Learning', 'read') and not is_abd_or_asa:
                wafMenu['children'] += [
                    {"menuid": "waf_flow_learning", "name": _("Flow self-learning"), "href": "/waf_flow_learning/",
                     "rsid": "bt_waf_flow_learning"},
                ]
            if has_permission(user_name, 'WAF_Analysis_And_Process', 'read'):
                wafMenu['children'] += [
                    {"menuid": "bt_waf_misreport_analysis", "name": _("WAF Analysis And Process"), "href": "/data_sheet/waf_misreport_analysis/",
                     "rsid": "bt_waf_misreport_analysis"},
                ]
            if has_permission(user_name, 'Ruleset_Update', 'read'):
                wafMenu['children'] += [
                    {"menuid": "waf_ruleset_upgrade", "name": _("Ruleset Upgrade"), "href": "/waf_ruleset_upgrade/",
                     "rsid": "bt_waf_ruleset_upgrade"},
                ]
            if has_permission(user_name, 'WAF_Config', 'read'):
                wafMenu['children'] += [
                    {"menuid": "waf_settings", "name": _("Settings"), "href": "/waf_settings/",
                     "rsid": "waf_settings"},
                ]
            if wafMenu['children']:
                return wafMenu

        return None

    # 增加llm场景
    def _get_llm_scenes(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']
        is_plugin_mode = param['deploy_mode'] == 'plugin'

        if not is_connected_to_master:
            return None

        if is_plugin_mode:
            return None

        # if license_info.is_module_effect(license_info.DM_ADVANCED_WAF) and (
                # license_info.is_protected_on() or is_debug):
        if license_info.is_module_effect(license_info.DM_LLM_PROTECTION) and license_info.is_protected_on() or is_debug:
            return {"menuid": "llm", "name": _('LLM Protection'), "href": "/llm_protection/",
                    "icon": "ic-proxy-o", "activeIcon": "ic-proxy", "rsid": "bt_llm"}

        return None
    
    # 增加智能威胁研判助手
    def _get_ai_threat_analyst_scenes(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']
        is_plugin_mode = param['deploy_mode'] == 'plugin'

        if not is_connected_to_master:
            return None

        if is_plugin_mode:
            return None

        if license_info.is_module_effect(license_info.DM_AI_THREAT_ANALYST) and license_info.is_protected_on() or is_debug:
            return {"menuid": "ai_threat_analyst", "name": _('AI Threat Analyst'), "href": "/ai_threat_analyst_assistant/",
                    "icon": "fa fa-balance-scale", "activeIcon": "fa fa-balance-scale", "rsid": "bt_ai_threat_analyst"}

        return None

    # 可编程对抗
    def _get_ubb_scenes(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return None

        if license_info.is_module_effect(license_info.DM_PROGRAMMABLE_DEFENDING) and (
                license_info.is_protected_on() or is_debug):
            return {"menuid": "ubb", "name": _('Programmable Defending'), "href": "/ubb_scenes/",
                  "icon": "ic-rta-o", "activeIcon": "ic-rta", "rsid": "bt_ubb"}

        return None

    # 业务威胁感知
    def _get_bta_menu(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return None

        if (license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS_APP) or license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS_BIZ) or license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS)) and ConfDb().has_bta_server() \
                and (license_info.is_protected_on() or is_debug):
            return {"menuid": "bta", "name": _('Business Threat Awareness'), "href": "/bta/",
                          "icon": "ic-hdd-o", "activeIcon": "ic-hdd", "rsid": "bt_bta"}

        return None

    # 业务数据采集
    def _get_bta_collection_menu(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return None

        if is_debug or license_info.is_protected_on():
            return {"menuid": "business_data_collection", "name": _('Business Data Collection'), "href": "/business_data_collection/",
                          "icon": "ic-log-o", "activeIcon": "ic-log", "rsid": "bt_bdc"}

        return None

    # 信誉库
    def _get_reputation_menu(self, param, license_info):
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return None

        if ConfDb().get_reputation_server() and (
                    license_info.is_protected_on() or is_debug):
            return {"menuid": "reputation", "name": _("Reputation"), "href": "/reputation/",
                          "icon": "fa fa-braille", "rsid": "bt_reputation"}

        return None

    # 大屏展示
    def _get_screen_menu(self, param, license_info, report_type='phoenix'):
        is_debug = param['is_build_debug']

        if license_info.is_module_effect(license_info.DM_BIG_SCREEN_DISPLAY) and (
                license_info.is_protected_on() or is_debug):
            return {"menuid": "screen", "name": _("Big Screen Display"), "href": "/sailfish_screen/" if report_type=='sailfish' else "/screen/",
                          "icon": "ic-dashboard-o", "activeIcon": "ic-dashboard", "rsid": "bt_screen"}

        return None


    # 网页过滤
    def _get_filter_menu(self, param):
        if param["web_filter_install"]:
            filterMenu = {"menuid": "web_filter", "name": _("Web Filter"), "href": "javascript:void(0)",
                          "icon": "ic-webfilter-o",
                          "activeIcon": "ic-webfilter", "expertMode": True, "rsid": "bt_web_filter",
                          "if": "page.expertMode && global_params.isEnableWebFilter",
                          "children": []}

            filterMenu['children'].append(
                {"menuid": "settings", "name": _("Settings"), "href": "/web_filter/settings/",
                 "rsid": "bt_settings"})
            filterMenu['children'].append(
                {"menuid": "abnormal_requests", "name": _("Abnormal Requests"),
                    "if": "global_params.logNodeAvailable",
                    "href": "/web_filter/blocked_events/",
                    "rsid": "bt_abnormal_requests"})
            filterMenu['children'].append(
                {"menuid": "blocked_urls", "name": _("Blocked Requests"), "href": "/web_filter/blocked_report/",
                    "if": "global_params.logNodeAvailable",
                    "rsid": "bt_blocked_urls"})
            filterMenu['children'].append(
                {"menuid": "requested_urls", "name": _("Requested URLs"), "href": "/web_filter/report_all_acesses/",
                    "if": "global_params.logNodeAvailable",
                    "rsid": "bt_requested_urls"})

            if filterMenu['children']:
                return filterMenu

        return None

    # 报表分析
    def _get_report_menu(self, param, user_name):
        is_botgate = param['product_type'] == 'Botgate'
        is_debug = param['is_build_debug']
        reportMenu = None
        if is_botgate or is_debug:
            reportMenu = {
                "menuid": "statistics", 
                "name": _("Statistics"),
                "children": [
                    {
                        "menuid": "basic_report", 
                        "name": "通用报表",
                        "href": "/statistics_app/",
                        "rsid": "bt_basic_report"
                    },
                    {
                        "menuid": "risk_report", 
                        "name": "风险报表",
                        "href": "/risk_app/",
                        "rsid": "bt_security_log"
                    }
                ],
                "if": "global_params.logNodeAvailable",
                "icon": "ic-statistic-o", 
                "activeIcon": "ic-statistic", 
                "rsid": "bt_statistics"
            }
        else:
            reportMenu = {
                "menuid": "statistics", 
                "name": _("Statistics"),
                "href": "/statistics_app/",
                "if": "global_params.logNodeAvailable",
                "icon": "ic-statistic-o", 
                "activeIcon": "ic-statistic", 
                "rsid": "bt_statistics"
            }

        return reportMenu
                    

    # 系统
    def _get_system_menu(self, param, user_name):
        is_mirror_mode = param['is_mirror_mode']
        license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
        is_safeplus_or_ngwaf_we = get_is_safeplus_or_ngwaf_we(license_info)
        is_valid_license = param['is_valid_license']

        systemMenu = {"menuid": "system", "name": _("System"), "href": "javascript:void(0)", "icon": "ic-system-o",
                      "activeIcon": "ic-system", "rsid": "bt_system", "children": []}

        if has_permission(user_name, 'System_General', 'read'):
            systemMenu['children'] += [
                {"menuid": "general", "name": _("General"), "href": "/system/general/", "match_urls": "^/factory_reset$",
                 "rsid": "bt_general"},
            ]
        if has_permission(user_name, 'License', 'read'):
            systemMenu['children'] += [
                {"menuid": "license", "name": _("License"), "href": "/system/license/",
                 "rsid": "bt_license"},
            ]
        if has_permission(user_name, 'Network_Configuration', 'read'):
            systemMenu['children'] += [
                {"menuid": "network_config", "name": _("Network Configuration"), "href": "/system/networkconfig/",
                 "rsid": "bt_network_config"},
            ]
        if has_permission(user_name, 'LLM_Model_Configuration', 'read') and (license_info.is_protected_on() or param['is_build_debug']):
            systemMenu['children'] += [
                {"menuid": "llm_config", "name": _("LLM Settings"), "href": "/system/llm_config/",
                 "rsid": "bt_llm_config"},
            ]
        if has_permission(user_name, 'Login_Account', 'read'):
            systemMenu['children'] += [
                {"menuid": "login_account", "name": _("Login/Account"), "href": "/system/account_management/",
                 "rsid": "bt_login_account"},
            ]
        if param.get('has_api_gateway', False) and has_permission(user_name, 'API_Gateway', 'read'):
            systemMenu['children'] += [
                {"menuid": "api_gateway", "name": _("API Gateway"), "href": "/system/api_gateway/",
                 "rsid": "bt_api_gateway"},
            ]

        if has_permission(user_name, 'Operation_Log', 'read'):
            systemMenu['children'] += [
                {"menuid": "logs", "name": _("Logs"), "href": "/system/operation_log/",
                 "rsid": "bt_logs"}
            ]

        if has_permission(user_name, 'Navigation_Alarm', 'read'):
            systemMenu['children'].append(
                {"menuid": "alarm", "name": _("Alarms"), "href": "/system/alarm/",
                 "rsid": "bt_alarm"})

        if has_permission(user_name, 'Emergency_Mode', 'read') and is_valid_license and (not is_mirror_mode or is_safeplus_or_ngwaf_we):
            systemMenu['children'].append(
                {"menuid": "emergency_mode", "name": _("Emergency_Mode"), "expertMode": True, "href": "/system/emergency_mode/",
                 "if": "page.expertMode", "rsid": "bt_emergency_mode"})

        if has_permission(user_name, 'HttpCap_Cfg', 'read') and is_mirror_mode:
            systemMenu['children'] += [
                {"menuid": "traffic_collection", "name": _("HTTP Capture Configuration"), "href": "/system/httpcap_cfg/",
                 "rsid": "bt_traffic_collection"}
            ]

        if has_permission(user_name, 'Labs', 'read'):
            systemMenu['children'].append(
                {"menuid": "labs", "name": _("Labs"), "expertMode": True, "href": "/system/labs/",
                 "if": "page.expertMode", "match_urls": "^/system/labs/",
                 "rsid": "bt_labs"})

        if has_permission(user_name, 'Command_Line', 'read'):
            systemMenu['children'].append(
                {"menuid": "command_line", "name": _("Command Line"), "href": "/system/web_cmdline/",
                 "rsid": "bt_command_line", "if": "global_params.isShowCMDLinePage"})

        if systemMenu['children']:
            return systemMenu
        return None

    # 开发者模式
    def _get_dev_mode_menu(self, param):
        if param["is_dev_mode"]:
            return {"menuid": "developer_mode", "name": _('Developer Mode'), "href": "/developer_mode/",
                          "icon": "ic-diagnosis_o", "activeIcon": "ic-diagnosis", "rsid": "bt_developer_mode"}
        return None


class BaseTemplateResponse(TemplateResponse, CommonContextMixin):
    """
    Inherit from TemplateResponse contain RAS custom context(healthy checkdata)
    In View:
        Use `return BaseTemplateResponse(request, template_name, context_data)`
        instead of `return render()`
    """

    def __init__(self, request, template, context=None, content_type=None,
                 status=None, charset=None, using=None):
        super(BaseTemplateResponse, self).__init__(
            request, template, context, content_type, status, charset=charset, using=using
        )
        self._defer_context = partial(
            BaseTemplateResponse.merge_common_context, self, self._request, context
        )

    def resolve_context(self, context):
        return SimpleLazyObject(self._defer_context)


class BaseJsonResponse(JsonResponse):
    """
    Base Ajax Api Template by json content-type
    """
    pass


class jsonResponse(JsonResponse):
    def __init__(self, status, data=None, error_msg="", **kwargs):
        if data is None:
            data = dict()
        result = {"save_success": status, "data": data, "error_msg": error_msg}
        super(jsonResponse, self).__init__(result, **kwargs)


class JsonResponseOk(jsonResponse):
    def __init__(self, data=None, error_msg="", **kwargs):
        super(JsonResponseOk, self).__init__(True, data, error_msg, **kwargs)


class JsonResponseFailed(jsonResponse):
    def __init__(self, data=None, error_msg="", **kwargs):
        super(JsonResponseFailed, self).__init__(False, data, error_msg, **kwargs)
