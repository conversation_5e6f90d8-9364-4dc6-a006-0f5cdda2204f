# -*- coding:utf-8 -*-
"""
    Author : <PERSON> : <EMAIL>
    Created on : 2018/8/3 15:07:12
"""
import functools
import logging
import time
import json
import os

import requests
from django.contrib.auth import SESSION_KEY
from django.contrib.auth.models import User
from django.contrib.sessions.models import Session
from django.utils import six
from django.utils.translation import ugettext as _, ugettext_noop
from django.utils.html import conditional_escape

from asp_conf_ctrl import ConfDb
import asp_utils.phoenix_util
import asp_utils.sailfish_util
from asp_utils.signature import new_session_with_sign
from asp_utils.utils import RESULT, generate_alert, get_language
from generic.conf import NginxConf
from service_mgr_rest_api import query_log_node_status
from web_admin.operation_log import operation_log
from web_admin.Conf_Webconsole import WebconsoleConf, QUERY_FROM_MGR_TIMEOUT
from web_admin.Conf_Nginx import NginxConf
from web_admin.site_acl_util import get_sites_4_user


logger = logging.getLogger('view')


def check_nginx_config_status(nginx_conf=None, user_name=None):
    """In proxy page we return nginx pass status
    and nginx pass failed site if test_pass failed
    :return: tuple(global_pass_status, pass_failed_sites)
    """

    if not nginx_conf:
        nginx_conf = NginxConf()
    upstreams = nginx_conf.get_all_upstreams(readonly=True)
    roles = nginx_conf.get_conf().get_my_roles()

    status = True  # working well.
    bad_site_list = []

    if 'proxy' in roles:
        server_keys_of_user = {site['key'] for site in get_sites_4_user(user_name, 'read')} if user_name else set()
        for upstream in upstreams:
            server_name = upstream.get('key')
            is_working_well = (upstream.get('status') == 'test_pass')
            if not is_working_well and server_name in server_keys_of_user and upstream.get('enable_site_conf', True) != False:
                bad_site_list.append(server_name)
        if bad_site_list:
            status = False
    return status, bad_site_list


def unescape(s):
    if six.PY2:
        from HTMLParser import HTMLParser
        h = HTMLParser()
        return h.unescape(s)
    elif six.PY3:
        from html import unescape
        return unescape(s)
    return s


class AlarmAPIMixin(object):
    """
    提供告警所需的大部分实用方法
    """

    query_all_api_url = 'http://127.0.0.1:20149/_internal/v1/query_alarm_all'

    @property
    def http_client(self):
        if not hasattr(self, '__http_client'):
            self.__http_client = requests.session()
        return self.__http_client

    def param_validate(self, query_params):
        """
        :param query_params:
        :return:
        """
        def type_check(cast_type, o):
            return isinstance(cast_type(o), cast_type)
        int_check = functools.partial(type_check, int)

        for key, value in query_params.items():
            if not isinstance(value, list):
                value = [value]

            if key in (
                    'level', 'status', 'alarm_id',
                    'gtime_start', 'gtime_end',
                    'page_size', 'query_status',
            ) and False in [int_check(o) for o in value]:
                raise TypeError('Params type error with key: {}'.format(key))

    def query_alarm(
            self, name=None, level=None, status=None, alarm_id=None,
            gtime_start=None, gtime_end=None, page_size=None, lang=get_language(), **kwargs
    ):
        """
        Query alarm data from service_mgr
        :param name: List[str] 告警名称 LicenseError, UpstreamHealth 等
        :param level: List[int] 告警级别 10 - 50
        :param status: List[int] 告警状态 0-事件 1-告警未恢复 2-告警自动恢复 3-告警手动恢复
        :param alarm_id: List[int] 告警ID 当存在此条件将排除其他所有条件进行精确查找
        :param gtime_start: timestamp 告警开始时间戳
        :param gtime_end: timestamp 告警结束时间戳
        :param page_size: int
        :param alarm_lang: str 告警语言
        :param kwargs:
            - query_status: 标识右上角铃铛查询
            - query_names: 进行名称查找 非告警查找
        :return:
        """
        query_params = dict(
            name=name,
            level=level,
            status=status,
            alarm_id=alarm_id,
            gtime_start=gtime_start,
            gtime_end=gtime_end,
            page_size=page_size,
            lang=lang,
            query_status=kwargs.get('query_status'),
            query_names=kwargs.get('query_names'),
        )
        query_params = {key: value for key, value in query_params.items() if value is not None}
        request_error = _("Unknown error.")
        try:
            self.param_validate(query_params)

            response = self.http_client.get(
                self.query_all_api_url,
                params=query_params,
                timeout=QUERY_FROM_MGR_TIMEOUT
            )
            if response.status_code != 200:
                # Server error
                return dict(result='FAILED', message=request_error)

            json_resp = response.json()
            if json_resp.get('result', '') != 'OK':
                # Cannot get alarm data
                return dict(result='FAILED', message=request_error)

            return dict(result='OK', message='OK', alarm_data=json_resp.get('alarm_data', {}))
        except Exception as e:
            logger.error('Query alarm data from service_mgr failed! with {}'
                         .format(e))
            return dict(result='FAILED', message=request_error)

    def query_alarm_names(self, use_dict=False):
        query_dict = {'query_names': 1}
        data = self.query_alarm(**query_dict)
        if data.get('result', '') == 'OK':
            names = data['alarm_data']
            for idx, name_d in enumerate(names):
                names[idx]['label'] = _(name_d['label'])
            return {i['name']: i['label'] for i in names} if use_dict else names
        return {} if use_dict else []

    def manual_recover(self, node_ip, alarm_code, id):
        """
        使用ID进行手动恢复
        :param node_ip:
        :param id: 告警ID
        :return:
        """
        return WebconsoleConf().recover_alarm(node_ip, alarm_code, id, 3)

    def _sort_alarm_data(self, _json_data):
        result = []
        json_data = _json_data.copy()
        for k, value in json_data.items():
            data = value.get('data', [])
            [v.update({"node_ip": k}) for v in data]
            result.extend(data)
        # sorted by create_time DESC
        result.sort(cmp=lambda x, y: y - x, key=lambda d: d["created_time"])
        return result

    def filter_alarm_data(self, user_name, alarm_data):
        if WebconsoleConf().get_user_role(user_name) == 'Operator':
            site_name_array = None
            result_alarm_data = []
            for alarm in alarm_data:
                if alarm['alarm_code'] in (112, 113):
                    if site_name_array is None:
                        site_name_array = []
                        for site in get_sites_4_user(user_name):
                            upstream = NginxConf().get_upstream_by_server_key(site['key'])
                            port = upstream['ListenPort']
                            server_name = upstream['ServerName']
                            if upstream['ServerNameType'] == 'IPv6':
                                s_name = '[%s]:%s' % (server_name, port)
                            else:
                                s_name = '%s:%s' % (server_name, port)
                            if site['alias']:
                                s_name = site['alias'] + '@' + s_name
                            business_path = upstream.get('business_path', '') if upstream.get('enable_business_path', False) else ''
                            if business_path:
                                s_name = s_name + '|' + business_path
                            site_name_array.append(s_name)
                    for site_name in site_name_array:
                        if ' ' + site_name + ' ' in alarm['content']:
                            result_alarm_data.append(alarm)
                            break
                if alarm['alarm_code'] in (132, 133) and user_name + ' ' in alarm['content']:
                    result_alarm_data.append(alarm)
            return result_alarm_data

        return alarm_data


class HealthyStatus(AlarmAPIMixin):
    @property
    def label_map(self):
        conf_json = os.path.dirname(__file__) + '/../../../service_mgr/alarm_service/conf.json'
        alarm_policy = json.load(open(conf_json))['alarm_policy']
        return {i['name']: _(i['label']) for i in alarm_policy} if alarm_policy else {}

    def node_status(self, with_alarm=True, user_name=None):
        available = False
        all_online = False
        is_connected_to_master = True
        try:
            log_node_status = query_log_node_status()
            if log_node_status.get('result') == RESULT.NOT_CONNECTED_TO_ZK:
                is_connected_to_master = False
            if log_node_status.get('available'):
                available = True
            if log_node_status.get('all_online'):
                all_online = True
        except:
            log_node_status = {}

        conf_db = ConfDb()
        if conf_db.get_value('report_type', 'phoenix') == 'phoenix':
            if conf_db.is_phoenix_server() and not conf_db.is_search_head():
                available = False

        if len(log_node_status) == 0:
            # if phoenix server is deleted, clear the global address of phoenix server
            asp_utils.phoenix_util.clean_search_head()
            asp_utils.sailfish_util.clean_search_head()

        alarm_query_dict = {
            'level': [50, 40],
            'status': [1],
            'query_status': 1
        }
        nav_alerts = []
        if with_alarm:
            alarm_resp = self.query_alarm(**alarm_query_dict)
            if alarm_resp['result'].upper() == 'OK':
                alarm_data = self._sort_alarm_data(alarm_resp['alarm_data'])
                if user_name:
                    alarm_data = self.filter_alarm_data(user_name, alarm_data)
                for d in alarm_data:
                    alarm_id = d['alarm_id']
                    severity = d['severity']
                    level = 'error' if severity == 50 else 'warning'
                    content = '{0}<br>{1}'.format(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(d['created_time'])), conditional_escape(d['content']))
                    alarm = generate_alert(
                        '',
                        content,
                        '/system/alarm/?alarm_id={}'.format(alarm_id),
                        _('Details'),
                        level
                    )
                    if d["name"] in ["LicenseError", "LicenseWarning"]:
                        # FIXME 授权错误和授权告警由于有单独的子标题，这里单独处理
                        alarm["label"] = d["title"]
                    else:
                        alarm['label'] = _(self.label_map.get(d['name'], d['name']))
                    nav_alerts.append(alarm)
        disconnected_alert = {}

        return {
            'log_node_available': available,
            'log_node_all_online': all_online,
            'is_connected_to_master': is_connected_to_master,
            'nav_alerts': nav_alerts,
            'disconnected_alert': disconnected_alert,
        }


def _compare_dict(dict1, dict2, root='', reverse=False):
    """
    :type dict1: dict
    :type dict2: dict
    :type root: str
    :type reverse: bool
    """
    change_set = set()
    if not isinstance(dict1, dict) or not isinstance(dict2, dict):
        return change_set
    for k, l in dict1.items():
        path = '{}/{}'.format(root, k)
        r = dict2.get(k)
        if isinstance(r, dict) and isinstance(l, dict):
            change_set |= _compare_dict(l, r, path, reverse)
            continue

        result = (str(l), str(r), ) if not reverse else (str(r), str(l), )

        if isinstance(r, list) and isinstance(l, list):
            change_set |= {(path, result,)} if r.sort() != l.sort() else set()
            continue

        if isinstance(r, int) or isinstance(l, int):
            try:
                change_set |= {(path, result,)} if int(r) != int(l) else set()
                continue
            except:
                pass
        # fallback
        change_set |= {(path, result,)} if r != l else set()

    return change_set


def compare_dict(dict1, dict2):
    """
    递归对比两个字典
    :type dict1: dict
    :type dict2: dict
    ----------
    Examples:
        >>> a1 = {'a', '1', 'b': 'bbb'}
        >>> a2 = {'b': 'ccc', 'a': 1}
        >>> print(compare_dict(a1, a2))
        set([('b', ('bbb', 'ccc',))])
    """
    return _compare_dict(dict1, dict2) | _compare_dict(dict2, dict1, reverse=True)


def kick_user_session(request, retain_session_keys=None, extra_session_keys=None, flush=False, kicked_user=None):
    """
    在当前节点将其他所有已经通过此用户名登录的Session清空
    置空的办法为在session字典中插入 _kicked 值
    当使用query_status的时候，会自动判断跳转
    注意： 此方法并没有清空session，只是置标记位
    @param flush: 直接清空而不是置标志位
    @param extra_session_keys: 额外需要处理的Session Key
    @param retain_session_keys: 额外需要保留的Session Key
    @param request: 此操作的请求
    @param kicked_user: 需要清除的用户名
    @return: 成功返回True 失败返回False
    """
    if extra_session_keys is None:
        extra_session_keys = set()

    if retain_session_keys is None:
        retain_session_keys = set()

    if isinstance(extra_session_keys, (str, unicode, )):
        extra_session_keys = {extra_session_keys}

    if isinstance(retain_session_keys, (str, unicode, )):
        retain_session_keys = {retain_session_keys}

    retain_session_keys.add(request.session.session_key)

    if kicked_user:
        username = kicked_user
    else:
        username = request.user.username
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        logger.error('Kick user session failed because have no user {}'.format(username, ))
        return False
    self_ip = request.session.get('remote_addr', '')
    audit_ips = set()
    for s in Session.objects.all():
        if (s.expire_date - s.expire_date.now()).total_seconds() < 0:
            s.delete()
            continue

        session_dict = s.get_decoded()
        if session_dict.get('_kicked', 0) == 1:
            continue

        if (int(session_dict.get(SESSION_KEY, '-1')) == user.id and s.session_key not in retain_session_keys) \
                or s.session_key in extra_session_keys:
            audit_ips.add(session_dict.get('remote_addr', ''))
            if not flush:
                session_dict['_kicked'] = 1
                Session.objects.save(s.session_key, session_dict, s.expire_date)
            else:
                s.delete()

    audit_ips -= {self_ip, ''}
    if audit_ips:
        if flush:
            if kicked_user == 'admin' and ConfDb().get_value('web_console/is_admin_user_disabled', False):
                msg = ugettext_noop('Due to the disabling of admin account, the following IP users are forced to log out: {ips}.')
            else:
                msg = ugettext_noop('Due to the password change, the following IP users are forced to log out: {ips}.')
        else:
            msg = ugettext_noop('Due to the enabling of no repeat login, the following IP users are forced to log out: {ips}.')
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Logout'), '0', {
            'msg': msg,
            'extra': {
                'ips': ', '.join(audit_ips),
            }
        }, user=username)

    return True


def recover_kicked_user_session(request):
    """
    恢复当前用户其他session标志位
    :param request:
    :return:
    """
    username = request.user.username
    try:
        user = User.objects.get(username=username)
    except User.DoesNotExist:
        logger.error('Recover user session failed because have no user {}'.format(username, ))
        return False
    for s in Session.objects.all():
        session_dict = s.get_decoded()
        if session_dict.get('_kicked', 0) == 0:
            continue
        if int(session_dict.get(SESSION_KEY, '-1')) == user.id and session_dict.get('_kicked', 0) == 1:
            s.delete()

    return True
