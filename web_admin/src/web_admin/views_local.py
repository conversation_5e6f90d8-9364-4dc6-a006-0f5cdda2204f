import _strptime # workaround for https://bugs.python.org/issue7980
import time
import os
import re
import subprocess
import json
from views import clone_shared_config, get_cluster_node_count
from view_lib import json_ok_response, json_fail_response, is_local_request
from django.views.decorators.csrf import csrf_exempt
from django.http import HttpResponse
from django.core.cache import caches
import django.contrib.auth
from django.utils.translation import ugettext_noop
from asp_conf_ctrl import ConfDb
from web_admin.operation_log import operation_log

from Conf_Network import NetworkConf
from Conf_Webconsole import WebconsoleConf
from Conf_Base import BaseConf
from asp_utils.utils import exe, exe_with_output, get_release_file, get_version, is_disk_mounted, current_machine_hardware_name, in_container
from asp_utils.CommonLibs import compress_IP
from asp_utils.threat_intelligence_util import CtiDbUpgrade
from asp_utils.license_info import get_current_is_debug
from asp_utils.rsa_sign import asp_decrypt
from generic.utils import kick_user_session

from service_mgr_rest_api import ensure_task_completed
from views import conduct_scheduled_export

# asp_config supported system commands, in case the
# command is not easy to added to jail user env
SYS_CMD = {
  'df' : None,
  'df -h' : None,
  'df -i' : None,
  'apply spatch' : 'sudo /usr/sbin/limited_exe apply_spatch',
  'remove spatch' : 'sudo /usr/sbin/limited_exe remove_spatch',
  'show spatch' : 'cat /.security_configuration_patches_info 2>/dev/null || echo Security configuration patch has not apply',
  '#halt' : 'sleep 1; sudo halt -fp',
  '#reboot' : 'sleep 1; sudo reboot -f',
  '#reset_support_code' : 'sudo /usr/sbin/reset_support_code',
  '#new-qr': 'cat /home/<USER>/tmp/.qr.input 2>/dev/null | qr > /etc/asp/release/web_admin/static/qr.png',
  '#del-qr': 'rm -f /home/<USER>/tmp/.qr.input /etc/asp/release/web_admin/static/qr.png',
  '#mount_log' : 'test -d /home/<USER>/var/log/asp || sudo mount --bind /var/log /home/<USER>/var/log',
  '#umount_log' : 'if [ -d /home/<USER>/var/log/asp ]; then sudo umount -f /home/<USER>/var/log; fi',
  'lsdisk' : """lsblk | awk '$6=="disk"{print "/dev/"$1}'""",
  'who' : None,
  'last' : None,
  'last reboot' : None,
  'service --status-all' : 'sudo service --status-all'
}


def _dhclient():
    ret = {
        'ip' : None,
        'netmask' : None,
        'gateway' : None
    }
    amdin_adapter = NetworkConf().get_admin_adapter_current_ip()
    try:
        exe('/etc/asp/release/bin/asp_conf_ctrl dhclient {}'.format(amdin_adapter['name']))
        with open('/var/lib/dhclient/dhclient.leases' if os.path.isdir('/var/lib/dhclient') else '/var/lib/dhcp/dhclient.leases') as f:
            for line in f.read().split(';'):
                if 'fixed-address' in line:
                    ret['ip'] = line.split()[-1]
                elif 'option subnet-mask' in line:
                    ret['netmask'] = line.split()[-1]
                elif 'option routers' in line:
                    ret['gateway'] = line.split()[-1]
    except:
        pass

    if ret['ip']:
        if ret['ip'] != amdin_adapter['ip'] \
            or ret['netmask'] != amdin_adapter['netmask'] \
            or ret['gateway'] != amdin_adapter['gateway']:
            ret['changed'] = True
        return HttpResponse(json.dumps(ret), content_type='application/json')
    else:

        return json_fail_response('Failed to obtain IP address from DHCP.')


@csrf_exempt
def cloud_mode(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'GET':
        return json_ok_response('true' if ConfDb().is_cloud_mode() else 'false')

    if request.method == 'POST':
        mode = request.POST.get('mode', '')
        if mode not in ('true', 'false'):
            return json_ok_response('illegal parameter')
        asp_conf_ctrl = get_release_file('bin/asp_conf_ctrl')
        exe('{0} --logfile /var/log/asp/asp_conf_ctrl.log set _private/os/network/cloud_mode={1}'.format(asp_conf_ctrl, mode))
        return json_ok_response('done')


@csrf_exempt
def config_ip(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    ip = compress_IP(request.POST.get('ip'))
    netmask = request.POST.get('netmask')
    gateway = compress_IP(request.POST.get('gateway'))
    conf = NetworkConf()
    conf_db = ConfDb()
    stat = None
    if request.method == 'POST':
        if conf_db.is_cloud_mode():
            return json_fail_response("IP configuration is not supported in cloud mode")

        if ":" in ip:
            # management network cannot be assign to IPV6
            return json_fail_response("Not allowed to change ip to ipv6, use set_ipv6 instead")

        if not WebconsoleConf().is_wizard_finished():
            if ip == 'auto':
                return _dhclient()

            ifcfg = {'admin': {'ip': ip}}
            if netmask:
                ifcfg['admin']['netmask'] = netmask
            if gateway:
                ifcfg['admin']['gateway'] = gateway

            ret = conf.validate_cfg_iface(ifcfg['admin'])
            if ret:
                return json_fail_response('Invalid config: ' + str(ret))
            stat = conf.apply_cfg_iface(ifcfg)
        else:
            cluster_node_count = get_cluster_node_count()
            if cluster_node_count == 0:
                return json_fail_response('Can not connect to ZK, please try again 5 minutes latter.')
            if cluster_node_count > 1:
                return json_fail_response('Can not modify, because cluster node count greater than 1.')

            # Only one node, one interface mode allowed to change IP
            cfg_iface = conf.clone_cfg_iface()
            if cfg_iface['log']['shared_with'] != 'admin' or cfg_iface['admin']['shared_with'] != 'external' or \
                    cfg_iface['internal']['shared_with'] != 'external' or cfg_iface['keepalived']['shared_with'] != 'external' :
                return json_fail_response('Not allowed to change ip when use more than one net interface')

            if ip == 'auto':
                return _dhclient()

            ifcfg = {'external': {'ip': ip}}
            if netmask:
                ifcfg['external']['netmask'] = netmask
            if gateway:
                ifcfg['external']['gateway'] = gateway
            ret = conf.validate_cfg_iface(ifcfg['external'])
            if ret:
                return json_fail_response('Invalid config: ' + str(ret))
            # Don't permit changeip if the interface have some ipv4 routes exclude default route.
            route_list = cfg_iface['external'].get('route_list')
            if route_list:
                for route_value in route_list.values():
                    if ':' not in route_value['ip']:
                        return json_fail_response('Not allowed to change ip when some routes exist on net interface')

            clone_shared_config(conf, ifcfg)
            abs_value = {}
            if conf.get_value('log_archive_server/ip', None, True) != '127.0.0.1':
                abs_value = {'log_archive_server': {'ip': ip}}

            node_id = conf_db.get_value('_private/node_id')

            lic_master = conf_db.get_value('phoenix/lic_master')

            if node_id:
                node_list = {'cluster': {'node_list': {node_id: {}}}}
                for name in ('internal', 'external', 'admin', 'log', 'keepalived'):
                    node_list['cluster']['node_list'][node_id][name] = {'ip': ip}
                    if netmask:
                        node_list['cluster']['node_list'][node_id][name]['netmask'] = netmask
                if gateway:
                    node_list['cluster']['node_list'][node_id]['external']['gateway'] = gateway
                abs_value.update(node_list)

                sailfish = conf_db.get_value('sailfish/servers/' + node_id)
                if sailfish:
                    abs_value.update({'sailfish': {'servers': {node_id: ip}}})
                abs_value.update({'master': {'servers': {node_id: {'admin_ip': ip, 'log_ip': None}}}})

            if lic_master:
                abs_value.update({'phoenix': {'lic_master': ip}})

            if abs_value:
                stat = conf.apply_cfg_iface(ifcfg, abs_values=abs_value)
            else:
                stat = conf.apply_cfg_iface(ifcfg)

        if stat.get('task_id') is None:
            return json_fail_response('{0}'.format(stat['message']))
        else:
            task_id = stat['task_id']
            from task_queue import TaskBase
            _, task_status, _ = ensure_task_completed(task_id)

        if task_status == TaskBase.STATUS_TIMEOUT:
            return json_fail_response('\nCheck Chang IP TimeOut.\n')
        elif task_status != TaskBase.STATUS_FINISHED: #config task fail
            return json_fail_response('\nChange IP failed.\n')
        else:
            #config success
            param = ip + '#' + netmask + '#'
            if gateway:
                param += gateway
            else:
                param += 'None'
            asp_conf_ctrl = get_release_file('bin/asp_conf_ctrl')
            exe('{0} update_manifest_ip {1}'.format(asp_conf_ctrl, param))
            return json_ok_response('\nChange IP success.\nWebConsole address: http://%s:20146\n' % ip)

    if request.method == 'GET':
        admin_adapter = conf.get_admin_adapter_current_ip()
        result = 'Current network setting:\n\nIP: {}\nNetmask: {}'.format(admin_adapter['ip'], admin_adapter['netmask'])
        if admin_adapter['gateway']:
            result += '\nGateway: {}'.format(admin_adapter['gateway'])
        result += '\n\nWebConsole address: http://{}:20146\n'.format(admin_adapter['ip'])
        return json_ok_response(result)

    return json_fail_response('forbidden')

@csrf_exempt
def user_authentication(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')
    if request.method == 'POST':
        try:
            json_data = json.loads(request.body)
            username = json_data.get('username', '')
            password = json_data.get('password', '')
        except:
            username, password = '', ''
        if username != '' and password != '':
            try:
                user = django.contrib.auth.authenticate(username=username, password=password)
                if user is not None:
                    if user.username == 'admin':
                        user = user.username
                    else:
                        pass
                        #user = '_botgate_' + user.username
            except:
                user = None
            if user is not None:
                return HttpResponse(json.dumps({"valid":True, "login_username":user}), content_type='application/json')
    return HttpResponse(json.dumps({"valid":False}), content_type='application/json')


@csrf_exempt
def conduct_config_export(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')
    if request.method == 'POST':
        transmit_mode = ConfDb().get_value("cluster/scheduled_export_configs", {}).get("transmit_mode", [])
        transmit_mode[0]["user_pwd"] = asp_decrypt(transmit_mode[0].get("user_pwd",""))
        op_code, details = conduct_scheduled_export(transmit_mode[0])
        operation_log(request, ugettext_noop('System'), ugettext_noop('Scheduled_Export'), op_code, details, user="admin")

        if op_code == '0':
            file_name = details.get("file_name")
            remote = details.get("recv_addr_port")
            result = {"msg": "{0} has been sent to {1}.".format(file_name, remote), "result": "OK"}
        else:
            err_args = details.get("err_args")
            err_msg = details.get("err_msg")
            if not err_msg == "":
                msg = err_msg
            else:
                msg = err_args
            result = {"msg": msg, "result": "Failed"}

        return HttpResponse(json.dumps(result, encoding='utf-8'), content_type='application/json')

@csrf_exempt
def config_date(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')
    date_format = '%Y%m%d %H:%M:%S'
    loc_date_format = date_format + ' %Z'
    if request.method == 'POST':
        date = request.POST.get('date')
        try:
            t = time.strptime(date, date_format)
        except Exception as e:
            return json_fail_response('parse date string error: %s' % date)
        BaseConf().exc_asp_conf_command(['set_utc_time', str(time.mktime(t))])
        time.sleep(1)
        time_offset = time.strftime(loc_date_format).split(' ')[-1]
        return json_ok_response(date + ' ' + time_offset)
    time.sleep(1)
    return json_ok_response(time.strftime(loc_date_format))


@csrf_exempt
def unlock_user(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST':
        username = request.POST.get('username', '')
        if WebconsoleConf().reset_last_login_time(username) == 0:
            return json_ok_response('Unlocked successfully')

    return json_fail_response('User does not exist')


@csrf_exempt
def disable_admin_user(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST':
        if WebconsoleConf().disable_admin_user():
            kick_user_session(request, flush=True, kicked_user='admin')
            return json_ok_response('Disable admin user successfully')

    return json_fail_response('Failed to disable admin user')


@csrf_exempt
def enable_admin_user(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST':
        if WebconsoleConf().enable_admin_user():
            return json_ok_response('Enable admin user successfully')

    return json_fail_response('Failed to enable admin user')


@csrf_exempt
def unlock_ip(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST':
        ip = request.POST.get('ip', '')
        if WebconsoleConf().unlock_ip(ip):
            return json_ok_response('Unlocked successfully')

    return json_fail_response('IP is not locked')


@csrf_exempt
def clean_2fa(request):
    """
    default action: disable 2fa
    when action=delete_2fa_config: delete 2fa config
    """
    if not is_local_request(request):
        return json_fail_response('forbidden')

    conf = WebconsoleConf()
    conf.set_2fa_enabled(False)

    message = 'Disable TOTP 2fa successfully'
    user_config_dict = {}
    if request.GET.get('action') == 'delete_2fa_config':
        all_usernames = conf.get_all_no_admin_users()
        all_usernames = [i['user_name'] for i in all_usernames]
        all_usernames += ['admin']
        for username in all_usernames:
            user_config = conf.get_value('user/' + username, is_abs=True)
            user_config['config_2fa'] = {}
            user_config_dict['user/' + username] = user_config

        conf.set_asp_conf_values(user_config_dict)
        message = 'Delete TOTP 2fa config successfully'

    return json_ok_response(message)


@csrf_exempt
def allow_all_ip(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST':
        if WebconsoleConf().set_web_console_allow_ip_status(False):
            return json_ok_response('Successfully allow all IP/MAC addresses for logins')

    return json_fail_response('Failed')

@csrf_exempt
def eth1_bond_cmd(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST':
        if ConfDb().get_deploy_mode() != ConfDb.DEPLOY_MODE_MIRROR:
            return HttpResponse(json.dumps({'msg': 'Not Mirroring Deployment Mode', 'ret': -1}), content_type='application/json')

        if in_container():
            return HttpResponse(json.dumps({'msg': 'Not supported in docker', 'ret': -1}), content_type='application/json')

        cmd = request.POST.get('cmd')
        if (cmd in ('show', 'unbond')) or (cmd.startswith('bond ') and re.match(r'^[a-zA-Z0-9_ ]{1,64}$', cmd)):
            ret, out, _ = exe_with_output('sudo /usr/sbin/limited_exe eth1_bond_cmd ' + cmd)
            return HttpResponse(json.dumps({'msg': out, 'ret':ret}), content_type='application/json')
    return HttpResponse(json.dumps({'msg': 'Illegal parameters', 'ret': -1}), content_type='application/json')


@csrf_exempt
def config_disk(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'POST' and request.POST.get('extend') == 'all':
        if current_machine_hardware_name() != 'x86_64':
            exe_with_output('curl -s -XPOST 127.0.0.1:20149/_internal/v1/disk_space_check')
            return json_ok_response("Done")
        else:
            ret, out, _ = exe_with_output('sudo /usr/sbin/extend_disk.sh')
            return json_ok_response(out)

    free_disk_list = []
    if current_machine_hardware_name() != 'x86_64':
        output = subprocess.check_output("sudo /usr/sbin/add_disk.sh", shell=True)
        free_disk_list = output.strip().split()
    else:
        output = subprocess.check_output("lsblk -n | grep -w disk | awk '{print \"/dev/\"$1,$4}'", shell=True)
        output = output.strip().split('\n')
        for line in output:
            disk, _ = tuple(line.split(' '))

            # check if disk is mounted or going to be mounted after reboot(by modifying /etc/fstab)
            if not is_disk_mounted(disk):
                free_disk_list.append(disk)

    if not free_disk_list:
        return json_ok_response('No free disk found')

    post_without_param = False
    if request.method == 'POST':
        disks = request.POST.get('disks')
        if disks:
            disk_arr = disks.split(',')
            disk_arr = [disk.strip() for disk in disk_arr]
            disks = ','.join(disk_arr)
            for disk in disk_arr:
                if disk not in free_disk_list:
                    return json_fail_response('No free disk %s' % disk)

            ret, out, _ = exe_with_output('sudo /usr/sbin/add_disk.sh ' + disks)
            if ret != 0:
                return json_fail_response(out)
            return json_ok_response(out)
        else:
            post_without_param = True

    if request.method == 'GET' or post_without_param:
        return json_ok_response('Disks available:\n\t%s' % '\n\t'.join(free_disk_list))


@csrf_exempt
def exe_syscmd(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    post_without_param = True
    if request.method == 'POST':
        cmd_str = request.POST.get('cmd_str')
        cmd_str = cmd_str.strip()
        if cmd_str in SYS_CMD:
            ret, out, _ = exe_with_output(SYS_CMD[cmd_str] or cmd_str)
            if ret != 0:
                return json_fail_response(out)
            return json_ok_response(out)
        elif re.match(r'service [a-zA-Z0-9._\-]{1,50} status$', cmd_str):
            ret, out, _ = exe_with_output('sudo ' + cmd_str)
            return json_ok_response(out)
        elif re.match(r'service [a-zA-Z0-9._\-]{1,50} (restart|start|stop|status) --yes$', cmd_str):
            ret, out, _ = exe_with_output('sudo ' + cmd_str[:-6])
            return json_ok_response(out)
        elif cmd_str.startswith('service '):
            return json_ok_response('Usage: service <service_name> status')

    if request.method == 'GET' or post_without_param:
        sys_cmd_list = [i for i in SYS_CMD.keys() if i[0] != '#']
        sys_cmd_list.sort()
        return json_ok_response('Command list:\n' + '\n'.join(sys_cmd_list))

@csrf_exempt
def change_jail_user_passwd(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    if request.method == 'GET':
        ret, out, _ = exe_with_output('sudo /usr/sbin/change_jail_user_passwd show-salt')
        if ret != 0:
            return json_fail_response(out)
        return json_ok_response(out)

    if request.method == 'POST':
        action = request.POST.get('action')
        if action != 'update-self' and action != 'update-cluster':
            return json_fail_response('invalid action')

        current_passwd_sha512 = request.POST.get('current_passwd_sha512')
        if re.search('^[a-zA-Z0-9./]+$', current_passwd_sha512) is None:
            return json_fail_response('invalid current password')

        new_passwd_sha512 = request.POST.get('new_passwd_sha512')
        if re.search('^[a-zA-Z0-9./]+$', new_passwd_sha512) is None:
            return json_fail_response('invalid new password')

        ret, out, _ = exe_with_output('sudo /usr/sbin/change_jail_user_passwd {0} {1} {2}'.format(action, current_passwd_sha512, new_passwd_sha512))
        if ret != 0:
            return json_fail_response(out)
        return json_ok_response(out)

@csrf_exempt
def sys_info(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    build_version = get_version()[4]
    return json_ok_response('\nBuild Version:\t%s\n' % build_version)

@csrf_exempt
def update_threat_intelligence(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')
    conf_db = ConfDb()
    updrade = CtiDbUpgrade(conf_db)

    license_info = conf_db.get_license_info()
    enabled = license_info.is_in_effect() or get_current_is_debug()
    if not enabled:
        return json_ok_response(
            "Invalid license, unable to update threat intelligence.")

    if not updrade.get_threat_intelligence_enabled():
        return json_ok_response("Threat intelligence switch is not turned on. Please turn on threat intelligence in the laboratory first.")

    err, update_list = updrade.download_ctidbs()
    if update_list:
        update_list_names=[update_list_item.get('name') for update_list_item in update_list]
        upgrade_name= update_list[0].get('name')
        ctidb_filepath = updrade.pre_install_cti_db(upgrade_name)
        result = BaseConf().sync_file(ctidb_filepath, sync=True)
        if result=="OK":
            return json_ok_response('\ndownload:{}\ninstall:{}\nresult:\t{}\n'.format(update_list_names,upgrade_name,result))
    return json_fail_response('forbidden')


@csrf_exempt
def clear_rbac_from_memory_cache(request):
    if not is_local_request(request):
        return json_fail_response('forbidden')

    caches['mem_cache'].delete('rbac')
    return json_ok_response('clear rbac from django memory cache')
