'''
@author: tom tang
@copyright: (c) 2014 River Security Technology Corporation, Ltd. All rights reserved.
'''
import os.path

from django.conf.urls import include, url
from django.views.i18n import javascript_catalog

from django.contrib import admin
from django.views.generic import RedirectView
import django_views_static as static #Fix CVE-2017-7234 DAP-24200

from asp_utils.utils import get_release_file
from system.alarm import views_api as alarm_api
from web_admin import views, wizard, views_cluster, views_phoenix_web_filter, views_phoenix_report_errors, \
    views_phoenix, views_local, views_ubb, views_bta, views_business_collect, views_reputation, views_mobile, \
    views_sailfish, views_ubb_site_shield, views_web_console_allow_ip, views_permission,views_waf, views_mpp, \
    views_flowlearn, views_threat_intelligence, views_network_protect, views_file_monitoring, views_llm, views_llm_setting, views_ai_threat_analysis
from web_admin.settings import STATICFILES_DIR
from web_admin.apis import api_patterns
admin.autodiscover()


urlpatterns = [
    url(r'^user/', include('account.urls')),

    url(
        r'^favicon.ico$|^static/img/favicon.ico$',
        views.priority_static,
        kwargs={'file_list': ("/etc/asp/release/web_admin/static/sync_file/favicon.ico", STATICFILES_DIR + "/api/oem/favicon.ico", STATICFILES_DIR + "/img/favicon.ico")}
    ),
    url(
        r'^logo.png$|^static/img/logo.png$',
        views.priority_static,
        kwargs={'file_list': ("/etc/asp/release/web_admin/static/sync_file/logo.png", STATICFILES_DIR + "/api/oem/logo.png", STATICFILES_DIR + "/img/logo.png")}
    ),
    url(
        r'^logo_brand.png$|^static/img/logo_brand.png$|^images/logo_brand.png$',
        views.priority_static,
        kwargs={'file_list': ("/etc/asp/release/web_admin/static/sync_file/logo_brand.png", STATICFILES_DIR + "/api/oem/logo_brand.png", STATICFILES_DIR + "/img/logo_brand.png")}
    ),
    url(
        r'^logo_brand_mini.png$|^static/img/logo_brand_mini.png$',
        views.priority_static,
        kwargs={'file_list': ("/etc/asp/release/web_admin/static/sync_file/logo_brand_mini.png", STATICFILES_DIR + "/api/oem/logo_brand_mini.png", STATICFILES_DIR + "/img/logo_brand_mini.png")}
    ),
    url(
        r'^header-bg.png$|^images/header-bg.png$',
        views.priority_static,
        kwargs={'file_list': ("/etc/asp/release/web_admin/static/sync_file/header-bg.png", STATICFILES_DIR + "/api/images/header-bg.png")}
    ),

    url(r'^get_qrcode$', views.get_qrcode,  name='get_qrcode'),
    url(r'^gen_qrcode$', views.gen_qrcode,  name='gen_qrcode'),

    url(r'^rcm_login$', views.rcm_login, name='rcm_login_page'),

    url(r'^overview/$', views_cluster.overview, name='homepage'),


    url(r'^status/ajax/query_task_stat_callback.js$', views.ajax_query_task_stat_callback),
    url(r'^wizard/pre_cluster_join.js$', wizard.ajax_pre_cluster_join),
    url(r'^wizard/ajax_check_adapter_cfg.js$', wizard.ajax_check_adapter_cfg),
    url(r'^internal_api/v1/cluster_join_verify.js$', views_cluster.ajax_cluster_join_verify),
    url(r'^internal_api/v1/pre_cluster_join_verify.js$', views_cluster.webconsole_pre_cluster_join_verify),
    url(r'^internal_api/v1/pre_cfg_verify.js$', views.pre_cfg_verify),

    url(r'^proxy/', include('proxy.urls', 'proxy')),

    url(r'^system/', include('system.urls', 'system')),

    url(r'^wizard/finished.js$', wizard.finished,  name='wizard_done'),
    url(r'^wizard/ajax_join_or_create_cluster$', wizard.ajax_join_or_create_cluster, name='ajax_join_or_create_cluster'),
    url(r'^wizard/system_cert_download', views.system_cert_download),
    url(r'^system_cert_download', views.system_cert_download2),
    url(r'^wizard/wizard_home/$', wizard.wizard_home),
    url(r'^en/wizard/wizard_home/$', wizard.wizard_home),
    url(r'^wizard/blinking_nic_port/$', wizard.blinking_nic_port),

    url(r'^web_filter/settings/$', views_phoenix_web_filter.settings),
    url(r'^web_filter/blocked_events/$', views_phoenix_web_filter.blocked_events),
    url(r'^web_filter/blocked_report/$', views_phoenix_web_filter.blocked_report),
    url(r'^web_filter/report_all_acesses/$', views_phoenix_web_filter.report_all_acesses),
    url(r'^web_filter/download_events$', views_phoenix_web_filter.download_events),

    url(r'^errors/combination/$', views_phoenix_report_errors.phoenix_errors_report_multi),
    url(r'^errors/combination/detail', views_phoenix_report_errors.phoenix_error_report_detail),
    url(r'^errors/combination/export$', views_phoenix_report_errors.export_errors),

    url(r'^errors/waf_analysis/$', views_phoenix_report_errors.waf_analysis),
    url(r'^errors/waf_analysis/filters/$', views_phoenix_report_errors.waf_analysis_filters),
    url(r'^errors/waf_analysis/detail/$', views_phoenix_report_errors.waf_analysis_detail),

    url(r'^download_operation_log/$', views.download_operation_log, name='download_operation_log'),
    url(r'^system_update_status_callback.js$', views.system_update_status),

    url(r'^help/$', views.help_page,  name='contact_us'),

    # Uncomment the next two lines to enable the admin:
    #  (r'^admin/doc/', include('django.contrib.admindocs.urls')),
    #  (r'^admin/', include(admin.site.urls)),

    url(r'^$', views.ua_check,  name='ua_check'),
    url(r'^index$', views.index,  name='root_index'),
    url(r'^rootFolder/$', views.rootFolder,  name='rootFolder'),

    url(r'^supported_browser/$', views.supported_browser, name='browser_warnning'),
    url(r'^download_browser/$', views.download_browser, name='browser_loading'),
    url(r'^down_file/$', views.chrome_file_download,  name='chrome_file_download'),
    url(r"^statistics_app/$", views.statistics_entry),
    url(r"^risk_app/$", views.risk_report_entry),
    url(r"^data_sheet/waf_misreport_analysis/$", views.waf_misreport_analysis),
    url(r"^waf_global_whitelist/$", views.waf_global_whitelist),

    ##########################################################################
    ########################## Claster related ###############################

    url(r'^cluster/v1/(?P<uri>[^/]*)/$', views_cluster.cluster_handler),
    url(r'^local/config_ip/$', views_local.config_ip),
    url(r'^local/cloud_mode/$', views_local.cloud_mode),
    url(r'^local/auth/$', views_local.user_authentication),
    url(r'^local/conduct_config_export/$', views_local.conduct_config_export),
    url(r'^local/config_date/$', views_local.config_date),
    url(r'^local/unlock_user/$', views_local.unlock_user),
    url(r'^local/disable_admin_user/$', views_local.disable_admin_user),
    url(r'^local/enable_admin_user/$', views_local.enable_admin_user),
    url(r'^local/unlock_ip/$', views_local.unlock_ip),
    url(r'^local/clean_2fa/$', views_local.clean_2fa),
    url(r'^local/config_disk/$', views_local.config_disk),
    url(r'^local/exe_syscmd/$', views_local.exe_syscmd),
    url(r'^local/sys_info/$', views_local.sys_info),
    url(r'^local/change_jail_user_passwd/$', views_local.change_jail_user_passwd),
    url(r'^local/allow_all_ip$', views_local.allow_all_ip),
    url(r'^local/eth1_bond_cmd$', views_local.eth1_bond_cmd),
    url(r'^local/update_threat_intelligence', views_local.update_threat_intelligence),
    url(r'^local/clear_rbac_from_memory_cache/$', views_local.clear_rbac_from_memory_cache),

    ##########################################################################
    ########################## Phoenix url ##################################
    url(r'^phoenix_resource_dashboard/', views_phoenix.phoenix_res_dashboard),
    url(r'^base_report/', views_phoenix.base_query,
             name='base_report'),
    url(r'^advanced_report/', views_phoenix.advanced_query,
         name='advanced_report'),
    url(r'^phoenix/', views_phoenix.phoenix_query),
    url(r'^jsi18n/$', javascript_catalog, {'packages': ('web_admin',), }, name='javascript-catalog'),
    url(r'^phoenix_query/$', views.phoenix_query, name='phoenix_query'),
    url(r'^screen_conf/$', views.screen_conf, name='screen_conf'),

    ##########################################################################
    ########################## Sailfish url ##################################
    url(r'^sailfish/', views_sailfish.sailfish_query),
    url(r"^sailfish_jump/$", views_sailfish.sailfish_jump),

    ##########################################################################
    ########################## ScreenEditor url ##################################
    url(r'^screen/editor', views.screen_editor),
    url(r'^screen/monitor', views.screen_monitor),
    url(r'^screen/documentation', views.screen_documentation),
    url(r'^screen/references', views.screen_references),
    url(r'^screen/notSupport', views.screen_notSupport),
    url(r'^screen/list', views.screen_list),
    url(r'^screen/save_sh_ip', views.screen_save_sh_ip),
    url(r'^screen/$', views.screen),
    url(r"^sailfish_screen/$", views.sailfish_screen_entry),
    url(r"^sailfish_screen_redirect/$", views.sailfish_screen_redirect),

    ##########################################################################
    ########################## mobile related ##################################
    url(r'^mobile_authorization/$', views_mobile.mobile_authorization, name='mobile_authorization'),
    url(r'^mobile_authorization/mobile_certificate/$', views_mobile.mobile_certificate, name='mobile_certificate'),
    url(r'^mobile_authorization/get_unknown_apps/$', views_mobile.get_unknown_apps, name='get_unknown_apps'),
    url(r'^mobile_authorization/add_register_apps/$', views_mobile.add_register_apps, name='add_register_apps'),
    url(r'^mobile_authorization/delete_register_apps/$', views_mobile.delete_register_apps, name='delete_register_apps'),
    url(r'^mobile_authorization/switch_verify_app/$', views_mobile.switch_verify_app, name='switch_verify_app'),
    url(r'^mobile_authorization/data_collection_configurations/$', views_mobile.data_collection_configurations, name='data_collection_configurations'),
    url(r'^mobile_authorization/response_token_max_value/$', views_mobile.response_token_max_value, name='response_token_max_value'),
    url(r'^mobile_authorization/risk_library/$', views_mobile.risk_library, name='risk_library'),
    url(r'^mobile_authorization/enable_block_note/$', views_mobile.enable_block_note, name='enable_block_note'),

    ##########################################################################
    ########################## WAF related ##################################
    url(r'^waf_general_protection/$', views_waf.waf_general_protection, name='waf_general_protection'),
    url(r'^waf_power_protection/$', views_waf.waf_power_protection, name='waf_power_protection'),
    url(r'^waf_global_custom_protection/$', views_waf.waf_global_custom_protection, name='waf_global_custom_protection'),
    url(r'^llm_protection/$', views_waf.llm_protection, name='llm_protection'),
    url(r'^waf_compliance_detection/$', views_waf.waf_compliance_detection, name='waf_compliance_detection'),
    url(r'^waf_ruleset_upgrade/$', views_waf.waf_ruleset_upgrade, name='waf_ruleset_upgrade'),
    url(r'^waf_settings/$', views_waf.waf_settings,name='waf_settings'),
    url(r'^waf_settings/adaption_decode$', views_waf.waf_adaption_decode,name='adaption_decode'),
    url(r'^waf_settings/upload_inflate$', views_waf.waf_upload_inflate,name='upload_inflate'),
    url(r'^waf_settings/detection_size/$', views_waf.waf_detection_size,name='detection_size'),
    url(r'^waf_settings/log_threshold/$', views_waf.waf_log_threshold,name='log_threshold'),
    url(r'^waf_settings/remove_black_ip/$', views_waf.waf_setting_remove_black_ip,name='remove_black_ip'),
    url(r'^waf_settings/save_block_config/$', views_waf.waf_setting_save_block_config,name='save_block_config'),
    url(r'^waf_settings/waf_for_api_request_only$', views_waf.waf_for_api_request_only,name='waf_for_api_request_only'),
    url(r'^waf_strategy/get_waf_strategies/$', views_waf.get_waf_strategies, name='get_waf_strategies'),
    url(r'^waf_strategy/get_waf_strategies/global_custom/$', views_waf.get_waf_strategies_for_global_custom, name='get_waf_strategies_for_global_custom'),
    url(r'^waf_strategy/get_waf_strategies/general/$', views_waf.get_waf_strategies_for_general, name='get_waf_strategies_for_general'),
    url(r'^waf_strategy/get_waf_strategies/power/$', views_waf.get_waf_strategies_for_power, name='get_waf_strategies_for_power'),
    url(r'^waf_strategy/get_waf_strategies/compliance/$', views_waf.get_waf_strategies_for_compliance, name='get_all_strategy_for_compliance'),
    url(r'^waf_strategy/get_waf_strategies/log/$', views_waf.get_waf_strategies_for_log, name='get_waf_strategies_for_log'),
    url(r'^waf_strategy/get_waf_strategies/site/$', views_waf.get_waf_strategies_for_site, name='get_waf_strategies_for_site'),
    url(r'^waf_strategy/waf_rules_set/$',views_waf.waf_rules_set,name='waf_rules_set'),
    url(r'^waf_strategy/save_site_strategy/$',views_waf.save_site_strategy,name='save_site_strategy'),
    url(r'^waf_strategy/save_module_strategy/$',views_waf.save_module_strategy,name='save_module_strategy'),
    url(r'^waf_strategy/save_syntax_strategy/$',views_waf.save_syntax_strategy,name='save_syntax_strategy'),
    url(r'^waf_strategy/save_res_leech_strategy/$',views_waf.save_res_leech_strategy,name='save_res_leech_strategy'),
    url(r'^waf_strategy/delete_site_strategy/$',views_waf.delete_site_strategy,name='delete_site_strategy'),
    url(r'^waf_strategy/delete_module_strategy/$',views_waf.delete_module_strategy,name='delete_module_strategy'),
    url(r'^waf_strategy/delete_syntax_strategy/$',views_waf.delete_syntax_strategy,name='delete_syntax_strategy'),
    url(r'^waf_strategy/delete_res_leech_strategy/$',views_waf.delete_res_leech_strategy,name='delete_res_leech_strategy'),
    url(r'^waf_strategy/default_waf_rules_set/$',views_waf.default_waf_rules_set,name='default_waf_rules_set'),
    url(r'^waf_strategy/save_strategy_api/$', views_waf.save_strategy_api, name='save_strategy_api'),
    url(r'^waf_strategy/save_brute_force_strategy/$', views_waf.save_brute_force_strategy, name='save_brute_force_strategy'),
    url(r'^waf_strategy/delete_brute_force_strategy/$', views_waf.delete_brute_force_strategy, name='delete_brute_force_strategy'),
    url(r'^waf_strategy/save_weak_password_check_strategy/$', views_waf.save_weak_password_check_strategy, name='save_weak_password_check_strategy'),
    url(r'^waf_strategy/delete_weak_password_check_strategy/$', views_waf.delete_weak_password_check_strategy, name='delete_weak_password_check_strategy'),
    url(r'^waf_strategy/save_waf_weak_password_list/$', views_waf.save_waf_weak_password_list, name='save_waf_weak_password_list'),
    url(r'^waf_strategy/get_waf_weak_password_list/$', views_waf.get_waf_weak_password_list, name='get_waf_weak_password_list'),
    url(r'^waf_strategy/save_honey_pot_check_strategy/$', views_waf.save_honey_pot_check_strategy, name='save_honey_pot_check_strategy'),
    url(r'^waf_strategy/delete_honey_pot_check_strategy/$', views_waf.delete_honey_pot_check_strategy,name='delete_honey_pot_check_strategy'),
    url(r'^waf_strategy/save_illegal_download_strategy/$', views_waf.save_illegal_download_strategy, name='save_illegal_download_strategy'),
    url(r'^waf_strategy/delete_illegal_download_strategy/$', views_waf.delete_illegal_download_strategy,name='delete_illegal_download_strategy'),
    url(r'^waf_strategy/save_regional_access_strategy/$', views_waf.save_regional_access_strategy,name='save_regional_access_strategy'),
    url(r'^waf_strategy/delete_regional_access_strategy/$', views_waf.delete_regional_access_strategy,name='delete_regional_access_strategy'),
    url(r'^waf_strategy/save_site_apply/$', views_waf.save_site_apply,name='save_site_apply'),
    url(r'^waf_strategy/save_cc_attack_strategy/$', views_waf.save_cc_attack_strategy, name='save_cc_attack_strategy'),
    url(r'^waf_strategy/delete_cc_attack_strategy/$', views_waf.delete_cc_attack_strategy, name='delete_cc_attack_strategy'),
    url(r'^waf_strategy/save_vulnerability_scan_strategy/$', views_waf.save_vulnerability_scan_strategy, name='save_vulnerability_scan_strategy'),
    url(r'^waf_strategy/delete_vulnerability_scan_strategy/$', views_waf.delete_vulnerability_scan_strategy, name='delete_vulnerability_scan_strategy'),
    url(r'^waf_strategy/save_xml_attack_strategy/$', views_waf.save_xml_attack_strategy, name='save_xml_attack_strategy'),
    url(r'^waf_strategy/download_waf_resource_file/$', views_waf.download_waf_resource_file, name='download_waf_resource_file'),
    url(r'^waf_strategy/delete_waf_resource_file/$', views_waf.delete_waf_resource_file, name='delete_waf_resource_file'),
    url(r'^waf_strategy/save_xml_file/$', views_waf.save_xml_file, name='save_xml_file'),
    url(r'^waf_strategy/delete_xml_attack_strategy/$', views_waf.delete_xml_attack_strategy, name='delete_xml_attack_strategy'),
    url(r'^waf_strategy/save_cookie_tamper_strategy/$', views_waf.save_cookie_tamper_strategy, name='save_cookie_tamper_strategy'),
    url(r'^waf_strategy/delete_cookie_tamper_strategy/$', views_waf.delete_cookie_tamper_strategy, name='delete_cookie_tamper_strategy'),
    url(r'^waf_strategy/save_global_custom_rule/$', views_waf.save_global_custom_rule, name='save_global_custom_rule'),
    url(r'^waf_strategy/delete_global_custom_rule/$', views_waf.delete_global_custom_rule, name='delete_global_custom_rule'),
    url(r'^waf_strategy/delete_http_protocol_strategy/$',views_waf.delete_http_protocol_strategy,name='delete_http_protocol_strategy'),
    url(r'^waf_strategy/save_http_protocol_strategy/$',views_waf.save_http_protocol_strategy,name='save_http_protocol_strategy'),
    url(r'^waf_strategy/save_parameter_detection_strategy/$',views_waf.save_parameter_detection_strategy,name='save_parameter_detection_strategy'),
    url(r'^waf_strategy/delete_parameter_detection_strategy/$',views_waf.delete_parameter_detection_strategy,name='delete_parameter_detection_strategy'),
    url(r'^waf_strategy/get_parameter_detection_strategy/$',views_waf.get_parameter_detection_strategy,name='get_parameter_detection_strategy'),
    url(r'^waf_strategy/get_parameter_detection_path/$', views_waf.get_parameter_detection_path, name='get_parameter_detection_path'),
    url(r'^waf_strategy/save_global_csrf_strategy/$', views_waf.save_global_csrf_strategy, name='save_global_csrf_strategy'),
    url(r'^waf_strategy/delete_global_csrf_strategy/$', views_waf.delete_global_csrf_strategy, name='delete_global_csrf_strategy'),


    # LLM Security Strategy APIs
    url(r'^llm_strategy/save_prompt_injection_strategy/$', views_waf.save_prompt_injection_strategy,
        name='save_prompt_injection_strategy'),
    url(r'^llm_strategy/delete_prompt_injection_strategy/$', views_waf.delete_prompt_injection_strategy,
        name='delete_prompt_injection_strategy'),
    url(r'^llm_strategy/save_sensitive_detection_strategy/$', views_waf.save_sensitive_detection_strategy,
        name='save_sensitive_detection_strategy'),
    url(r'^llm_strategy/delete_sensitive_detection_strategy/$', views_waf.delete_sensitive_detection_strategy,
        name='delete_sensitive_detection_strategy'),
    url(r'^llm_strategy/get_llm_security_strategy/$', views_waf.get_llm_security_strategy,
        name='get_llm_security_strategy'),
    url(r'^llm_strategy/get_prompt_injection_strategy/$', views_waf.get_prompt_injection_strategy,
        name='get_prompt_injection_strategy'),
    url(r'^llm_strategy/get_sensitive_detection_strategy/$', views_waf.get_sensitive_detection_strategy,
        name='get_sensitive_detection_strategy'),

    # LLM Security Settings APIs
    url(r'^llm_setting/save_prompt_blacklist/$', views_waf.save_prompt_blacklist,
        name='save_prompt_blacklist'),
    url(r'^llm_setting/save_prompt_whitelist/$', views_waf.save_prompt_whitelist,
        name='save_prompt_whitelist'),
    url(r'^llm_setting/$', views_waf.get_llm_setting,
        name='get_llm_setting'),

    url(r'^llm_setting/verify_llm_corpus_form/$', views_llm.verify_llm_corpus_form, name='verify_llm_corpus_form$'),
    url(r'^llm_setting/upgrade_llm_corpus/$', views_llm.upgrade_llm_corpus, name='upgrade_llm_corpus$'),
    url(r'^llm_setting/rollback_llm_corpus/$', views_llm.rollback_llm_corpus, name='rollback_llm_corpus'),
    url(r'^llm_setting/llm_corpus_upgrade/$', views_llm.llm_corpus_upgrade,
        name='llm_corpus_upgrade'),

    url(r'^add_waf_id_whitelist/$', views_waf.add_waf_id_whitelist, name='add_waf_id_whitelist'),
    url(r'^waf_rule_upgrade/save_update_config/$', views_waf.save_update_config, name='save_update_config'),
    url(r'^waf_rule_upgrade/check_waf_ruleset/$', views_waf.check_waf_ruleset, name='check_waf_ruleset'),
    url(r'^waf_rule_upgrade/upgrade_waf_ruleset/$', views_waf.save_strategy_api, name='save_strategy_api'),
    url(r'^waf_rule_upgrade/rollback_waf_ruleset/$', views_waf.save_strategy_api, name='save_strategy_api'),
    url(r'^waf_rule_upgrade/install_waf_ruleset/$', views_waf.install_waf_ruleset, name='install_waf_ruleset'),
    url(r'^waf_rule_upgrade/save_gray_box/$', views_waf.save_gray_box, name='save_gray_box'),
    url(r'^waf_rule_upgrade/close_gray_box/$', views_waf.close_gray_box, name='close_gray_box'),
    url(r'^waf/clear_tamper_cache/$', views_waf.clear_tamper_cache, name='clear_tamper_cache'),
    url(r'^waf_rules/(?P<filename>.*)', views_waf.waf_rules, name='waf_rules'),
    url(r'^waf_flow_learning/$', views_flowlearn.waf_flow_learning, name='waf_flow_learning'),
    url(r'^waf_flow_learning/get_learning_results/$', views_flowlearn.get_learning_results, name='get_learning_results'),
    url(r'^waf_flow_learning/get_learning_path_result/$', views_flowlearn.get_learning_path_result, name='get_learning_path_result'),
    url(r'^waf_flow_learning/save_flow_learning_task/$', views_flowlearn.save_flow_learning_task, name='save_flow_learning_task'),
    url(r'^waf_flow_learning/flow_learning_task_status/$', views_flowlearn.flow_learning_task_status, name='flow_learning_task_status'),
    url(r'^waf_flow_learning/flow_learning_restart/$', views_flowlearn.flow_learning_restart, name='flow_learning_restart'),
    url(r'^waf_flow_learning/flow_learning_continue/$', views_flowlearn.flow_learning_continue, name='flow_learning_continue'),
    url(r'^waf_flow_learning/statistics_count_sort/$', views_flowlearn.statistics_count_sort, name='flow_learning_continue'),
    url(r'^waf_flow_learning/statistics_progress_sort/$', views_flowlearn.statistics_progress_sort, name='flow_learning_continue'),
    url(r'^waf_flow_learning/check_has_saved/$', views_flowlearn.check_has_saved, name='check_has_saved'),
    url(r'^waf_flow_learning/get_learning_path_list_process/$',views_flowlearn.get_learning_path_list_process,name='get_learning_path_list_process'),
    url(r'^waf_flow_learning/get_parameter_detection_strategy_with_learning_process/$',views_flowlearn.get_parameter_detection_strategy_with_learning_process,name='get_parameter_detection_strategy_with_learning_process'),

    #AI Threat Analyst
    url(r'^ai_threat_analyst_assistant/$', views_ai_threat_analysis.ai_threat_analysis_assistant, name='ai_threat_analysis_assistant'),
    url(r'^ai_threat_analyst/log_analysis$', views_ai_threat_analysis.ai_threat_log_analysis),
    url(r'^ai_threat_analyst/payload_analysis$', views_ai_threat_analysis.ai_threat_payload_analysis, name='ai_threat_payload_analysis'),
    url(r'^ai_threat_analyst/singlefile_analysis$', views_ai_threat_analysis.ai_threat_singlefile_analysis, name='ai_threat_singlefile_analysis'),
    url(r'^ai_threat_analyst/result_list$', views_ai_threat_analysis.get_analysis_result_list, name='get_analysis_result_list'),
    url(r'^ai_threat_analyst/result_detail$', views_ai_threat_analysis.get_analysis_result_detail, name='get_analysis_result_detail'),
    url(r'^ai_threat_analyst/singleline_analysis$', views_ai_threat_analysis.ai_threat_singleline_analysis, name='ai_threat_singleline_analysis'),
    

    ##########################################################################
    ########################## threat intelligence url ##################################
    url(r'^threat_intelligence/$', views_threat_intelligence.threat_intelligence, name='threat_intelligence'),
    url(r'^threat_intelligence/threat_intelligence_mode_switch/$', views_threat_intelligence.threat_intelligence_mode_switch, name='threat_intelligence_mode_switch'),
    url(r'^threat_intelligence/threat_intelligence_whitelist/$', views_threat_intelligence.threat_intelligence_whitelist, name='threat_intelligence_whitelist'),
    url(r'^threat_intelligence/verify_ctidb/$', views_threat_intelligence.verify_ctidb, name='verify_ctidb'),
    url(r'^threat_intelligence/upgrade_ctidb/$', views_threat_intelligence.upgrade_ctidb, name='upgrade_ctidb'),
    url(r'^threat_intelligence/save_update_config/$', views_threat_intelligence.save_update_config, name='save_update_config'),
    url(r'^threat_intelligence/check_ctidb/$', views_threat_intelligence.check_ctidb, name='check_ctidb'),
    url(r'^threat_intelligence/install_ctidb/$', views_threat_intelligence.install_ctidb, name='install_ctidb'),

    ##########################################################################
    ########################## LLM model role url ##################################
    url(r'^llm_config/submit_llm_config/$', views_llm_setting.submit_llm_config, name='llm_submit_llm_config'),

    ##########################################################################
    ########################## file monitor related ##################################
    url(r'^file_monitor/$', views_file_monitoring.file_monitor, name='file_monitor'),
    url(r'^file_monitoring/save_file_monitoring_config/$', views_file_monitoring.save_file_monitoring_config, name='save_file_monitoring_config'),
    url(r'^file_monitoring/get_file_monitoring_config/$', views_file_monitoring.get_file_monitoring_config, name='get_file_monitoring_config'),
    url(r'^file_monitoring/get_file_list/$', views_file_monitoring.get_file_list, name='get_file_list'),
    url(r'^file_monitoring/download_file/$', views_file_monitoring.download_file, name='download_file'),

    ##########################################################################
    url(r'^network_ddos_protect/$', views_network_protect.network_ddos_protect, name='network_ddos_protect'),
    url(r'^network_ddos_protect/mode_switch/$', views_network_protect.network_ddos_protect_mode_switch, name='network_ddos_protect_mode_switch'),
    url(r'^network_ddos_protect/save_config/$', views_network_protect.network_ddos_protect_save_config, name='network_ddos_protect_save_config'),
    ########################## network protect url ##################################

    ##########################################################################
    ########################## developer_mode url ##################################
    url(r'^developer_mode/$', views.developer_mode),
    url(r'^developer_mode/enable$', views.enable_developer_mode),
    url(r'^developer_mode/disable$', views.disable_developer_mode),
    url(r'^developer_mode/txsafe_debug', views.txsafe_debug),
    url(r'^developer_mode/download_log/$', views.log_file_download),
    url(r'^developer_mode/download_raw_log/$', views.raw_log_file_download),
    url(r'^developer_mode/archive_log_info$', views.show_archive_log_info),
    url(r'^developer_mode/import_waf_rules_file$', views.import_waf_rules_file),
    url(r'^developer_mode/save_check_tool', views.save_check_tool),
    url(r'^developer_mode/clean_sailfish_reports', views_sailfish.clean_sailfish_reports),
    url(r'^developer_mode/clean_phoenix_reports', views.clean_phoenix_reports),
    url(r'^developer_mode/phoenix_secret', views.phoenix_secret),
    url(r'^developer_mode/enable_phoenix_secret', views.enable_phoenix_secret),
    url(r'^developer_mode/enable_cmdline_page', views.enable_cmdline_page),
    url(r'^developer_mode/reset_mobile_cert', views_mobile.reset_mobile_cert),

    url(r'^captcha/', include('captcha.urls')),
    url(r'^captcha/enable', views.captcha_enable),
    url(r'^qps_enable', views.qps_enable),
    url(r'^enhance_cluster_security/enable', views.enhance_cluster_security),
    url(r'^hide_hardware_info', views.hide_hardware_info),
    url(r'^web_console_allow_ip$', views_web_console_allow_ip.web_console_allow_ip),

    url(r'^factory_reset$', views.factory_reset),
    url(r'^factory_reset_status_callback.js$', views.factory_reset_status),
    url(r'^power_reboot/$', views.power_reboot),
    url(r'^power_shutdown/$', views.power_shutdown),
    url(r'^test_connection/$', views.test_connection),
    url(r'^enable_expert_mode/$', views.enable_expert_mode),
    url(r'^change_table_view/$', views.change_upstream_tab_view),

    url(r'^TBD/(?P<filename>.*)$', views.download_third_party_files, name='TBD'),

    url(r'^debug_console/',views.debug_console_proxy),
    # Alarm API
    # Reuse internal_api for get alarm data
    url(r'^internal_api/v1/alarm/$', alarm_api.AlarmApiView.as_view(), name='alarm_api'),

    # UBB
    url(r'^ubb_scenes/$', views_ubb.ubb_scenes, name='ubb_scenes'),
    url(r'^ubb_scenes/save_lua_rule_content/$', views_ubb.save_manual_rule, name='save_lua_rule_content'),
    url(r'^ubb_scenes/enable_lua_rule/$', views_ubb.enable_manual_rule, name='enable_lua_rule'),
    url(r'^ubb_scenes/save_black_rule/$', views_ubb.save_black_rule, name='ubb_save_black'),
    url(r'^ubb_scenes/delete_black_rule/$', views_ubb.delete_black_rule, name='ubb_delete_black'),
    url(r'^ubb_scenes/save_black_rules_status/$', views_ubb.save_black_rules_status, name='ubb_save_black_status'),
    url(r'^ubb_scenes/save_all_black_rules_status/$', views_ubb.save_all_black_rules_status, name='save_all_black_status'),
    url(r'^ubb_scenes/save_high_freq_rule/$', views_ubb.save_high_freq_rule, name='ubb_save_high_freq'),
    url(r'^ubb_scenes/delete_high_freq_rule/$', views_ubb.delete_high_freq_rule, name='ubb_delete_high_freq'),
    url(r'^ubb_scenes/save_high_freq_rules_status/$', views_ubb.save_high_freq_rules_status, name='ubb_save_high_freq_status'),
    url(r'^ubb_scenes/save_all_high_freq_status/$', views_ubb.save_all_high_freq_status, name='ubb_save_all_freq_status'),
    url(r'^ubb_scenes/save_reputation_rule/$', views_ubb.save_reputation_rule, name='ubb_save_reputation'),
    url(r'^ubb_scenes/delete_reputation_rule/$', views_ubb.delete_reputation_rule, name='ubb_delete_reputation'),
    url(r'^ubb_scenes/save_reputation_rules_status/$', views_ubb.save_reputation_rules_status, name='ubb_save_reputation_status'),
    url(r'^ubb_scenes/save_all_reputation_status/$', views_ubb.save_all_reputation_status,
        name='ubb_save_all_reputation_status'),
    url(r'^ubb_scenes/save_bad_behavior_rule/$', views_ubb.save_bad_behavior_rule, name='ubb_save_bad_behavior'),
    url(r'^ubb_scenes/save_timer/$', views_ubb.set_timer, name='ubb_save_timer'),
    url(r'^ubb_scenes/delete_bad_behavior_rule/$', views_ubb.delete_bad_behavior_rule, name='ubb_delete_bad_behavio'),
    url(r'^ubb_scenes/save_bad_behavior_status/$', views_ubb.save_bad_behavior_status, name='ubb_save_behavior_status'),
    url(r'^ubb_scenes/save_all_bad_behavior_status/$', views_ubb.save_all_bad_behavior_status,
        name='ubb_save_all_bad_behavior_status'),
    url(r'^ubb_scenes/save_new_comer_rule/$', views_ubb.save_new_comer_rule, name='save_new_comer_rule'),
    url(r'^ubb_scenes/delete_new_comer_rule/$', views_ubb.delete_new_comer_rule, name='delete_new_comer_rule'),
    url(r'^ubb_scenes/save_new_comer_status/$', views_ubb.save_new_comer_status, name='save_new_comer_status'),
    url(r'^ubb_scenes/save_all_new_comer_status/$', views_ubb.save_all_new_comer_status, name='save_all_new_comer_status'),
    url(r'^ubb_scenes/save_sniping_rule/$', views_ubb.save_sniping_rule, name='ubb_save_sniping'),
    url(r'^ubb_scenes/delete_sniping_rule/$', views_ubb.delete_sniping_rule, name='ubb_delete_sniping'),
    url(r'^ubb_scenes/save_sniping_rules_status/$', views_ubb.save_sniping_rules_status, name='ubb_save_sniping_status'),
    url(r'^ubb_scenes/save_all_sniping_rules_status/$', views_ubb.save_all_sniping_rules_status, name='save_all_sniping_status'),
    url(r'^ubb_scenes/save_counter_rule/$', views_ubb.save_counter_rule, name='ubb_save_counter_rule'),
    url(r'^ubb_scenes/delete_counter_rule/$', views_ubb.delete_counter_rule, name='ubb_delete_counter_rule'),
    url(r'^ubb_scenes/save_counter_rules_status/$', views_ubb.save_counter_rules_status, name='ubb_save_counter_rules_status'),
    url(r'^ubb_scenes/save_all_counter_rules_status/$', views_ubb.save_all_counter_rules_status, name='ubb_save_all_counter_rules_status'),
    url(r'^ubb_scenes/get_adv_filter_lua_code/$', views_ubb.get_adv_filter_lua_code, name='ubb_get_advfilter_lua_code'),

    url(r'^ubb_scenes/remove_black_ip/$', views_ubb.remove_black_ip, name='remove_black_ip'),
    url(r'^ubb_scenes/remove_black_fp/$', views_ubb.remove_black_fp, name='remove_black_fp'),
    url(r'^ubb_scenes/remove_black_account/$', views_ubb.remove_black_account, name='remove_black_account'),
    url(r'^ubb_scenes/remove_black_account_hostname/$', views_ubb.remove_black_account_hostname, name='remove_black_account_hostname'),
    url(r'^ubb_scenes/save_dict_share_data/$', views_ubb.save_dict_share_data, name='save_dict_share_data'),
    url(r'^ubb_scenes/delete_dict_share_data/$', views_ubb.delete_dict_share_data, name='delete_dict_share_data'),
    url(r'^ubb_scenes/flush_share_memory/$', views_ubb.flush_share_memory, name='flush_share_memory'),
    url(r'^ubb_scenes/get_ubbv2_shm_free_space/$', views_ubb.get_ubbv2_shm_free_space, name='get_ubbv2_shm_free_space'),
    url(r'^ubb_scenes/ubb_query_log/$', views_ubb.ubb_query_log, name='ubb_query_log'),
    url(r'^ubb_scenes/ubb_clear_log/$', views_ubb.ubb_clear_log, name='ubb_clear_log'),

    #ubb mobile
    url(r'^ubb_scenes/save_app_counter_rule/$', views_ubb.save_app_counter_rule, name='ubb_save_app_counter_rule'),
    url(r'^ubb_scenes/delete_app_counter_rule/$', views_ubb.delete_app_counter_rule, name='ubb_delete_app_counter_rule'),
    url(r'^ubb_scenes/save_app_counter_rules_status/$', views_ubb.save_app_counter_rules_status, name='ubb_save_app_counter_rules_status'),
    url(r'^ubb_scenes/save_all_app_counter_rules_status/$', views_ubb.save_all_app_counter_rules_status, name='ubb_save_all_app_counter_rules_status'),
    url(r'^ubb_scenes/save_app_black_rule/$', views_ubb.save_app_black_rule, name='ubb_save_app_black'),
    url(r'^ubb_scenes/delete_app_black_rule/$', views_ubb.delete_app_black_rule, name='ubb_delete_app_black'),
    url(r'^ubb_scenes/save_app_black_rules_status/$', views_ubb.save_app_black_rules_status, name='ubb_save_app_black_status'),
    url(r'^ubb_scenes/save_all_app_black_rules_status/$', views_ubb.save_all_app_black_rules_status,
        name='save_all_app_black_status'),
    url(r'^ubb_scenes/save_app_high_freq_rule/$', views_ubb.save_app_high_freq_rule, name='ubb_save_app_high_freq'),
    url(r'^ubb_scenes/delete_app_high_freq_rule/$', views_ubb.delete_app_high_freq_rule, name='ubb_delete_app_high_freq'),
    url(r'^ubb_scenes/save_app_high_freq_rules_status/$', views_ubb.save_app_high_freq_rules_status,
        name='ubb_save_app_high_freq_status'),
    url(r'^ubb_scenes/save_all_app_high_freq_status/$', views_ubb.save_all_app_high_freq_status,
        name='ubb_save_all_app_freq_status'),
    url(r'^ubb_scenes/save_app_reputation_rule/$', views_ubb.save_app_reputation_rule, name='ubb_save_app_reputation'),
    url(r'^ubb_scenes/delete_app_reputation_rule/$', views_ubb.delete_app_reputation_rule, name='ubb_delete_app_reputation'),
    url(r'^ubb_scenes/save_app_reputation_rules_status/$', views_ubb.save_app_reputation_rules_status,
        name='ubb_save_app_reputation_status'),
    url(r'^ubb_scenes/save_all_app_reputation_status/$', views_ubb.save_all_app_reputation_status,
        name='ubb_save_all_app_reputation_status'),
    url(r'^ubb_scenes/save_site_shield_rule/$', views_ubb_site_shield.save_site_shield_rule,
        name='save_site_shield_rule'),

    #Api bot defender
    #url(r'^api_bot_defender/', include('api_bot_defender.urls')),

    # BOT check
    url(r'^bot_check/', views.bot_check),

    # resource file uploading
    url(r'^ubb_scenes/upload_resource_file/$', views_ubb.upload_resource_file,
        name='upload_source_file'),
    url(r'^ubb_scenes/delete_resource_file/$', views_ubb.delete_resource_file,
        name='delete_source_file'),
    url(r'^ubb_scenes/download_resource_file/$', views_ubb.download_resource_file,
        name='download_source_file'),

    url(r'^ubb_scenes/export_ubb_config/$', views_ubb.export_ubb_config,
        name='export_ubb_config'),
    url(r'^ubb_scenes/import_ubb_config/$', views_ubb.import_ubb_config,
        name='import_ubb_config'),
    url(r'^ubb_scenes/save_sort/$', views_ubb.save_sort,
        name='save_ubb_sort'),

    url(r'^bta/$', views_bta.threat_settings, name='bta'),
    url(r'^bta/bta_lua/(?P<model>\d{3})/$', views_bta.bta_lua_model, name='bta_lua'),
    url(r'^bta/bta_restore/(?P<model>\d{3})/$', views_bta.bta_restore_model, name='bta_restore'),
    url(r'^bta/set_session_life/$', views_bta.set_session_life, name='set_session_life'),


    url(r'^business_data_collection/$', views_business_collect.business_data_collection, name='business_data_collection'),
    url(r'^business_data_collection/data_collect_rule/$', views_business_collect.data_collect_rule, name='data_collect_rule'),
    url(r'^business_data_collection/enable_all_collect_rule/$', views_business_collect.enable_all_collect_rule, name='enable_all_collect_rule'),
    url(r'^business_data_collection/enable_collect_rule/$', views_business_collect.enable_collect_rule, name='enable_collect_rule'),
    url(r'^business_data_collection/delete_collect_rule/$', views_business_collect.delete_collect_rule, name='delete_collect_rule'),
    url(r'^bta/get_weak_pwd_list/$', views_business_collect.get_weak_pwd_list, name='get_weak_pwd_list'),
    url(r'^business_data_collection/data_priority/$', views_business_collect.data_priority, name='data_priority'),

    url(r'^bta_service/reputation_sendout$', views_reputation.reputation_sendout, name='reputation_sendout'),
    url(r'^bta/set_reputation_decay_rate/$', views_reputation.set_reputation_decay_rate, name='set_reputation_decay_rate'),
    url(r'^reputation/$', views_reputation.reputation, name='reputation'),
    url(r'^bta_service/', views_reputation.reputation_service, name='reputation_service'),

    #url(r'^api_security/$', views_api.api_security, name='api_security'),

    url(r'^mpp/$', views_mpp.mpp),
    url(r'^mpp/(?P<mpp_type>wechat|alipay|mpaas)/$', views_mpp.mpp_detail),
    url(r'^mpp/set_mpplist/$', views_mpp.set_mpplist),
    url(r'^mpp/sdk_upload/$', views_mpp.sdk_upload),
    url(r'^mpp/sdk_download/$', views_mpp.sdk_download),
    url(r'^mpp/plugin_sdk_download/$', views_mpp.plugin_sdk_download),

    # website and role's permission control
    url(r'^permission/assign_site_for_user/$', views_permission.assign_site_for_user, name='assign_site_for_user'),
    url(r'^permission/user_list/$', views_permission.users_list, name='users_list'),
    url(r'^permission/site_list/$', views_permission.sites_list, name='sites_list'),

    # static_bot
    url(r'^static_bot_detection/$', views.static_bot_detection, name='static_bot_detection'),

    # API static resource
    url(r'^css/(?P<path>.*)$', static.serve, {'document_root': STATICFILES_DIR + "/api/css"}),
    url(r'^fonts/(?P<path>.*)$', static.serve, {'document_root': STATICFILES_DIR + "/api/fonts"}),
    url(r'^images/(?P<path>.*)$', static.serve, {'document_root': STATICFILES_DIR + "/api/images"}),
    url(r'^oem/customize.json$', views.priority_static, {'file_list': ("/etc/asp/release/web_admin/static/sync_file/customize.json", STATICFILES_DIR + "/api/oem/customize.json")}),
    url(r'^js/(?P<path>.*)$', static.serve, {'document_root': STATICFILES_DIR + "/api/js"}),

    # api v1
    url(r'^meta.json$', views.meta_json),
    url(r'^api/v1/', include(api_patterns)),
]

# web_console static serves
web_admin_root = get_release_file("web_admin")
urlpatterns += [
    url(r'^static/(?P<path>.*)$', static.serve, dict(document_root=os.path.join(web_admin_root, "static"))),
    url(r'^report/(?P<path>.*)$', static.serve, dict(
        document_root=os.path.join(web_admin_root, "src/web_admin/templates/report"),
    )),
]

handler404 = 'web_admin.views.custom_404_view'