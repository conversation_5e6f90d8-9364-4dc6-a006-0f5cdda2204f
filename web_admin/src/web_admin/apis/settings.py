# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

#### 自动导入结束，不要在前面插入代码

import os
from asp_conf_ctrl import GetConfDb
from service_mgr_rest_api import service_mgr_set_asp_config, query_online_sailfish_nodes
from generic.conf import WebconsoleConf
from generic.constants import asp_version
from generic.conf.conf_webconsole import is_dev_mode_enable
from web_admin.decorators import has_permission, get_rbac_conf
from api_base.menu_id import *
from asp_utils.utils import RESULT, get_product_type, get_language, in_k8s


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Read", "read")
def get_menu_config(request):
    """
    @check_rest_api_permission: API_Read
    获取用户 login 需要配置、验证码等信息
    获取系统设置可以提供的菜单信息
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    smart_conf = GetConfDb()
    confdb = smart_conf.db()
    is_ver_en = get_language() == 'en'

    def _is_connected_to_master():
        is_connected_to_master = True
        try:
            sailfish_online_nodes = query_online_sailfish_nodes()
            if sailfish_online_nodes.get('result') == RESULT.NOT_CONNECTED_TO_ZK:
                is_connected_to_master = False
        except:
            is_connected_to_master = False

        return is_connected_to_master
    
    def _check_Func_permission(user_name, param):
        return has_permission(user_name, 'API_Setting', 'read')
    
    def _check_decorator(f):
        def check_func(func):
            def inner(*args, ** kwargs):
                if f(*args, ** kwargs):
                    return func(*args, ** kwargs)
                return False
            return inner
        return check_func
    
    @_check_decorator(_check_Func_permission)
    def _check_WAF_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']

        if has_permission(user_name, 'WAF_Strategy', 'read'):
            return (license_info.is_module_effect(license_info.DM_ADVANCED_WAF) and
                    (license_info.is_protected_on() or is_debug))
        return False
        
    def _check_Protection_permission(user_name, param):
        # license_info = param['license']
        # is_debug = param['is_build_debug']
        #'Protection_List'  ---> API_Setting
        return has_permission(user_name, 'API_Setting', 'read')
    
    def _check_api_scanner_permission(user_name, param):
        # 不考虑license, 只通过RBA控制扫描器配置目录入口是否显示
        if is_ver_en: # en 隐藏
            return False
        
        return has_permission(user_name, 'API_Scanner', 'read')

    def _check_word_report_permission(user_name, param):
        if is_ver_en or in_k8s(): # en 隐藏
            return False
        
        return has_permission(user_name, 'REPORT', 'read')

    def _check_llm_permission(user_name, param):

        return has_permission(user_name, 'LLM_Model_Configuration', 'read')
    
    @_check_decorator(_check_Func_permission)
    def _check_ATTACK_Menu_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        return license_info.is_in_effect() or is_debug
    
    @_check_decorator(_check_Func_permission)
    def _check_UBB_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return False
        if has_permission(user_name, 'Programmable_Defending', 'read'):
            return license_info.is_module_effect(license_info.DM_PROGRAMMABLE_DEFENDING) and (
                license_info.is_protected_on() or is_debug)
        
        return False
    
    @_check_decorator(_check_Func_permission)
    def _check_BTA_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return False
        
        if has_permission(user_name, 'Business_Threat_Awareness', 'read'):
            return (license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS_APP) or license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS_BIZ) or license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS)) and confdb.has_bta_server() \
                and (license_info.is_protected_on() or is_debug)
        
        return False
    
    @_check_decorator(_check_Func_permission)
    def _check_DATA_COLLECTION_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return False
        
        # Data_Collection  -> API_Setting
        if has_permission(user_name, 'API_Setting', 'read'):
            return True

        return False
    
    @_check_decorator(_check_Func_permission)
    def _check_REPUTATION_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return False
        
        if has_permission(user_name, 'Reputation', 'read'):
            return confdb.get_reputation_server() and (
                    license_info.is_protected_on() or is_debug)
        
        return False
    
    @_check_decorator(_check_Func_permission)
    def _check_API_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return False
        
        return (is_debug or (license_info.is_module_effect(license_info.DM_API_MONITOR) \
                 and (license_info.is_protected_on() or is_debug))) and confdb.has_abd_server()

    def _check_API_PARAMTER_permission(user_name, param):
        permission = _check_API_permission(user_name,param)
        return permission and confdb.has_flowlearn_server()
    
    @_check_decorator(_check_Func_permission)
    def _check_BOT_DETECTION_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_abd = param['is_abd']
        is_connected_to_master = param['is_connected_to_master']

        if not is_connected_to_master:
            return False
        
        return has_permission(user_name, 'Static_Bot_Detection', 'read') and is_abd
    
    def _check_REPORT_permission(user_name, param):
        # license_info = param['license']
        # is_debug = param['is_build_debug']
        # is_connected_to_master = param['is_connected_to_master']

        # if not is_connected_to_master:
        #     return False
        
        # if has_permission(user_name, 'API_Read', 'read'):
        #     return (is_debug or (license_info.is_module_effect(license_info.DM_API_MONITOR) \
        #             and (license_info.is_protected_on() or is_debug))) and confdb.has_abd_server()
        
        # return False
        return has_permission(user_name, 'API_Read', 'read')
    
    def _check_SYS_permission(user_name, param):
        return has_permission(user_name, 'System_Navigator', 'read')

    @_check_decorator(_check_SYS_permission)
    def _check_DEVELOPER_MODE_permission(user_name, param):
        # is_dev_mode = is_dev_mode_enable()
        return has_permission(user_name, 'Developer_Mode', 'write') # and is_dev_mode
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_GENERAL_permission(user_name, param):
        return has_permission(user_name, 'System_General', 'read')  

    @_check_decorator(_check_SYS_permission)
    def _check_SYS_LICENSE_permission(user_name, param):
        return has_permission(user_name, 'License', 'read')   
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_ALARM_permission(user_name, param):
        return has_permission(user_name, 'Navigation_Alarm', 'read')   
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_NETWORK_permission(user_name, param):
        return has_permission(user_name, 'Network_Configuration', 'read')   
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_ACCOUNT_permission(user_name, param):
        return has_permission(user_name, 'Login_Account', 'read')
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_LOG_permission(user_name, param):
        return has_permission(user_name, 'Operation_Log', 'read')
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_FLOW_COLLECTION_permission(user_name, param):
        is_mirror_mode = param['is_mirror_mode']
        return has_permission(user_name, 'HttpCap_Cfg', 'read') and is_mirror_mode
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_LAB_permission(user_name, param):
        is_abd = param['is_abd']
        return has_permission(user_name, 'Labs', 'read') and is_abd
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_CMD_permission(user_name, param):
        return has_permission(user_name, 'Command_Line', 'read')
    
    @_check_decorator(_check_SYS_permission)
    def _check_SYS_API_GATEWAY_permission(user_name, param):
        return has_permission(user_name, 'API_Gateway', 'read') and confdb.has_api_gateway()

    @_check_decorator(_check_SYS_permission)
    def _check_SYS_Cluster_permission(user_name, param):
        return has_permission(user_name, 'Cluster_Node_List', 'read')
    
    @_check_decorator(_check_SYS_permission)
    def _check_THREAT_INTELLIGENCE_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_abd = param['is_abd']
        return has_permission(user_name, 'Threat_Intelligence', 'read') and (license_info.is_protected_on() or is_debug) and is_abd
    
    @_check_decorator(_check_SYS_permission)
    def _check_EMERGENCE_MODE_permission(user_name, param):
        is_mirror_mode = param['is_mirror_mode']
        is_abd = param['is_abd']
        return has_permission(user_name, 'Emergency_Mode', 'read') and (not is_mirror_mode and is_abd)
    
    @_check_decorator(_check_SYS_permission)
    def _check_DDOS_PROTECT_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        is_mirror_mode = param['is_mirror_mode']
        is_abd = param['is_abd']
        return has_permission(user_name, 'Network_DDOS_Protect', 'read') and (license_info.is_protected_on() or is_debug) and (not is_mirror_mode and is_abd)
    
    def _check_llm_protection_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        if has_permission(user_name, 'LLM_Protection', 'read'):
            return (license_info.is_module_effect(license_info.DM_LLM_PROTECTION) and
                    (license_info.is_protected_on() or is_debug))
        return False

    def _check_ai_threat_analysis_permission(user_name, param):
        license_info = param['license']
        is_debug = param['is_build_debug']
        if has_permission(user_name, 'AI_Threat_Analyst', 'read'):
            return (license_info.is_module_effect(license_info.DM_AI_THREAT_ANALYST) and
                    (license_info.is_protected_on() or is_debug))
        return False

    MENU_CONFIG = [ # (menu_id, func))
        (MENU_ID_FUNCTION, _check_Func_permission),         # 功能设置
        (MENU_ID_SYSTEM, _check_SYS_permission),            # 系统设置
        (MENU_ID_ADVANCE_REPORT, _check_REPORT_permission), # 高级报表
        (MENU_ID_FUNCTION_ASSET, _check_API_permission),    # 功能设置->资产设置
        (MENU_ID_FUNCTION_ATTACK, _check_ATTACK_Menu_permission),                    # 功能设置->攻击设置
        (MENU_ID_FUNCTION_ATTACK_THREAT_DETECTION, _check_WAF_permission),          # 功能设置->攻击设置->漏洞攻击->威胁检测
        (MENU_ID_FUNCTION_ATTACK_API_PARAMTER_DETECTION, _check_API_PARAMTER_permission),    # 功能设置->攻击设置->漏洞攻击->参数检测
        (MENU_ID_FUNCTION_ATTACK_API_ATTACK_DETECTION, _check_API_permission),      # 功能设置->攻击设置->业务行为攻击->攻击检测
        (MENU_ID_FUNCTION_ATTACK_UBB,  _check_UBB_permission),                      # 功能设置->攻击设置->业务行为攻击->可编程对抗
        (MENU_ID_FUNCTION_ATTACK_API_SEQUENCE, _check_API_permission),              #功能设置->攻击设置->业务行为攻击->调用顺序
        (MENU_ID_FUNCTION_ATTACK_BUSSINESS_COLLECTION, _check_DATA_COLLECTION_permission), # 功能设置->攻击设置->业务行为攻击->业务数据采集
        (MENU_ID_FUNCTION_ATTACK_BTA, _check_BTA_permission),                       # 功能设置->攻击设置->业务行为攻击->业务威胁感知
        (MENU_ID_FUNCTION_ATTACK_REPUTATION, _check_REPUTATION_permission),         # 功能设置->攻击设置->业务行为攻击->信誉库
        (MENU_ID_FUNCTION_PROTECTION,  _check_Protection_permission),               # 功能设置->保护站点
        (MENU_ID_FUNCTION_BOT_DETECTION, _check_BOT_DETECTION_permission),          # 功能设置->Bot检测
        (MENU_ID_FUNCTION_DEFECT, _check_API_permission),                           # 功能设置->缺陷设置
        (MENU_ID_FUNCTION_PII, _check_API_permission),                              # 功能设置->敏感信息设置
        (MENU_ID_FUNCTION_THREAT_INTELLIGENCE, _check_THREAT_INTELLIGENCE_permission),    # 功能设置->威胁情报
        (MENU_ID_FUNCTION_DDOS_PROTECT, _check_DDOS_PROTECT_permission),            # 功能设置->DDos防护
        (MENU_ID_SYSTEM_GENERAL, _check_SYS_GENERAL_permission),                    # 系统配置->系统->通用
        (MENU_ID_SYSTEM_CLUSTER, _check_SYS_Cluster_permission),                    # 系统配置->系统->集群
        (MENU_ID_SYSTEM_FLOW_COLLECTION, _check_SYS_FLOW_COLLECTION_permission),    # 系统配置->系统->流量采集设置
        (MENU_ID_SYSTEM_LICENSE, _check_SYS_LICENSE_permission),                    # 系统配置->系统->授权许可
        (MENU_ID_SYSTEM_ALARM, _check_SYS_ALARM_permission),                        # 系统配置->系统->告警
        (MENU_ID_SYSTEM_CMD, _check_SYS_CMD_permission),                            # 系统配置->系统->命令行
        (MENU_ID_SYSTEM_NETWORK, _check_SYS_NETWORK_permission),                    # 系统配置->网络->网络配置
        (MENU_ID_SYSTEM_ACCOUNT, _check_SYS_ACCOUNT_permission),                    # 系统配置->账号->登陆与账号管理
        (MENU_ID_SYSTEM_LOG, _check_SYS_LOG_permission),                            # 系统配置->日志
        (MENU_ID_SYSTEM_API_GATEWAY, _check_SYS_API_GATEWAY_permission),            # 系统配置->API网关
        (MENU_ID_SYSTEM_LAB, _check_SYS_LAB_permission),                            # 系统配置->实验室
        (MENU_ID_SYSTEM_EMERGENCE_MODE, _check_EMERGENCE_MODE_permission),          # 系统配置->紧急模式
        (MENU_ID_SYSTEM_DEVELOP_MODE, _check_DEVELOPER_MODE_permission),            # 系统配置->开发者模式
        (MENU_ID_FUNCTION_SCANNER, _check_api_scanner_permission),                  # 功能设置->扫描器
        (MENU_ID_REPORT, _check_word_report_permission),                             # 周期WORD报告列表
        (MENU_ID_SYSTEM_LLM_CONFIG, _check_llm_permission),                         # 系统配置->大模型配置
        (MENU_ID_LLM_PROTECTION, _check_llm_protection_permission),                 # 系统配置->大模型安全防护
        (MENU_ID_AI_THREAT_ANALYSIS, _check_ai_threat_analysis_permission)          # 攻击设置 -> 漏洞攻击 -> 智能威胁研判
    ]

    menus = []

    user_name = request.user.username
    param = dict()
    param['is_build_debug'] = asp_version.current_is_debug
    param['is_connected_to_master'] = _is_connected_to_master()
    param['is_mirror_mode'] = confdb.is_mirror()
    param['license'] = confdb.get_license_info(i18n_support=False, care_cluster=False)
    product_type = get_product_type() 
    param['is_abd'] = product_type == 'ApiBotDefender' or product_type == 'ApiSecurityAudit'

    for (id, func) in MENU_CONFIG:
        if func is None or func(user_name, param):
            menus.append(id)

    return api_ok_response(menus)