# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.post_refresh_summary_request import PostRefreshSummaryRequest
from abd_utils.schemas.summary_filter import SummaryFilter

#### 自动导入结束，不要在前面插入代码

from abd_utils.repositories.overview.summary import SummaryRepository
from abd_utils.repositories.attacks.summary import AttackSummaryRepository
from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.repositories.assets.app_site import AppSiteRepository
from abd_utils.utils.func_database import ChangeRole
from abd_utils.repositories.defects.summary import DefectSummaryRepository
from abd_utils.repositories.defects.scanner import ScannerRepository
from abd_utils.repositories.assets.api_cache import ApiCacheRepository
from api_base.api_const_define import OWASP_API_CLASS_0

from abd_utils.utils.func_base import get_api_access_trend_line_chart_by_column_names, get_bar_chart_data_by_column_names, \
    get_system_risk_level, get_api_risk_level, get_all_api_level_count, get_pre_start

from abd_utils.utils.func_attack_policy import ApiAttackPolicyList

from abd_utils.utils.func_pii import get_pii_type_config_dict, count_pii_level, count_pii_score
from abd_utils.utils.func_risk import get_risk_name_level_dict, get_risk_name_config_dict, count_defect_level, count_defect_score
from datetime import datetime
from abd_utils.utils.func_datetime_convert import get_start_end_time_by_time_filter, merge_echarts_format_result, get_past_7_days_start_end
from abd_utils.utils.func_industry_data_level import convert_pii_type_to_industry_data_tag
from web_admin.utils import send_http_request_to_abd_master_node
from api_base.result_code import RC_INTERNAL_ERROR
from asp_utils.utils import ugettext
from abd_utils.services.asset.asset_service import AssetService

import logging
import time
import json
# import objsize

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_refresh_summary(request):
    """
    @check_rest_api_permission: API_Read
    手动刷新摘要
    """
    try:
        req_body = ClassField("PostRefreshSummaryRequest", PostRefreshSummaryRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    response = send_http_request_to_abd_master_node('POST', '/abd_service/generate_summary')

    try:
        AssetService().refresh_summary()
    except Exception as e:
        logging.exception('Exception in refresh_summary {}'.format(str(e)))
        return api_error_response(RC_INTERNAL_ERROR, ugettext("Summary refresh failed"))
    
    if response:
        response = json.loads(response)
        if not response.get('err_msg'):
            return api_ok_response({})
        else:
            return api_error_response(RC_INTERNAL_ERROR, response.get('err_msg'))
    else:
        return api_error_response(RC_INTERNAL_ERROR, 'failed to connect abd service')

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_overview(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    基于时间统计应用数、API总数、攻击事件数、缺陷API数、涉敏API数包括于上个时间段的变化值
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        web_username = request.user.username
        filter = req_body.to_json()
        time_filter = filter['time_filter']
        app_site_ids = filter['app_site_ids']
        with ChangeRole(web_username):
            pre_time, start_time, end_time = get_start_end_time_by_time_filter(time_filter)
            not_round_pre_start, not_round_start, not_round_end = get_start_end_time_by_time_filter(time_filter, builtin_round=False)
            repo = SummaryRepository()
            now = int(time.time())
            current_site_count = repo.get_app_site_count(app_site_ids, 0, now).total
            pre_site_count = repo.get_app_site_count(app_site_ids, 0, not_round_start - 1).total
            delta_site_count = current_site_count - pre_site_count

            current_api_count = repo.get_api_count(app_site_ids, 0, now).total
            pre_api_count = repo.get_api_count(app_site_ids, 0, not_round_start - 1).total
            delta_api_count = current_api_count - pre_api_count

            pii_api_count = repo.get_pii_api_count(app_site_ids, start_time, end_time).total
            pre_pii_api_count = repo.get_pii_api_count(app_site_ids, pre_time, start_time - 1).total
            pii_api_delta_count = pii_api_count - pre_pii_api_count

            attack_repo = AttackSummaryRepository()
            attack_event_count, attack_event_delta_count = attack_repo.get_attack_event_total(app_site_ids, not_round_pre_start, not_round_start, not_round_end, web_username)

            defect_api_count = repo.get_defect_api_count(app_site_ids, start_time, end_time).total
            pre_defect_api_count = repo.get_defect_api_count(app_site_ids, pre_time, start_time - 1).total
            defect_api_delta_count = defect_api_count - pre_defect_api_count

            level_count, system_risk_score = ApiCacheRepository().get_risk_level_count_and_score(app_site_ids)
            risk_level = get_system_risk_level(system_risk_score)

            data = {}
            data['app_site'] = {'count': current_site_count, 'delta': delta_site_count}
            data['api'] = {'count': current_api_count, 'delta': delta_api_count}
            data['attack'] = {'count': attack_event_count, 'delta': attack_event_delta_count}
            data['defect'] = {'count': defect_api_count, 'delta': defect_api_delta_count}
            data['pii'] = {'count': pii_api_count, 'delta': pii_api_delta_count}
            data['risk'] = {'score': round(system_risk_score), 'delta': 0, 'level': risk_level, 'high': level_count['high'] , 'middle': level_count['middle'], 'low':level_count['low']}
            return api_ok_response(data)

    except Exception as e:
        logging.exception('Exception in get_dashboard_count_overview {}'.format(str(e)))
        return api_error_response(RC_INTERNAL_ERROR, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_attack_event_top_n(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    攻击事件TopN
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        time_filter = json_body['time_filter']
        count = api_validate_int_value(json_body['filter'].get('count', 0), required=False, name="count", def_val=10, min_val=1, max_val=10)
        app_site_ids = json_body['app_site_ids']
        _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

        def get_attack_policy_by_name(policy_list, name):
            for policy in policy_list:
                if policy['name'] == name:
                    return policy
            return None

        web_username = request.user.username
        with ChangeRole(web_username):
            entries = AttackSummaryRepository().get_attack_name(app_site_ids, count, start_time, end_time, web_username)
            records = []
            details = []
            attack_policy_list = ApiAttackPolicyList().get_list()
            related_api_ids = []
            related_apps = []
            for entry in entries:
                records.append([entry['name'], entry['total_count']])
                detail = {}
                detail['category'] = entry['category']
                detail['domestic_count'] = entry['domestic_count']
                detail['oversea_count'] = entry['oversea_count']
                detail['intranet_count'] = entry['intranet_count']

                attack_policy = get_attack_policy_by_name(attack_policy_list, entry['name'])
                detail['desc'] = ''
                if attack_policy:
                    detail['desc'] = attack_policy['description']

                detail['level'] = 'high' if (ugettext('High') in entry['level']) or (ugettext('High') in entry['level']) else ''

                related_api_ids.extend(entry['api_ids'])
                related_apps.extend(entry['site_ids'])

                detail['endpoints'] = entry['api_ids']
                detail['app_site_names'] = entry['site_ids']

                details.append(detail)

            related_api_query, _ = ApiRepository().get_api_info_by_ids(list(set(related_api_ids)))
            api_endpoints = {api['id']:api['endpoint'] for api in related_api_query}
            related_app_query, _ = AppSiteRepository().get_app_site_by_ids(list(set(related_apps)))
            app_site_names = {app['id']:app['name'] for app in related_app_query}

            for d in details:
                endpoints = []
                for api_id in d['endpoints']:
                    endpoint = api_endpoints.get(api_id, None)
                    if endpoint:
                        endpoints.append(endpoint)
                d['endpoints'] = endpoints

                app_names = []
                for site_id in d['app_site_names']:
                    app_site_name = app_site_names.get(site_id, None)
                    if app_site_name:
                        app_names.append(app_site_name)
                d['app_site_names'] = app_names

        ret = {
            'column_names': ['name', 'total'],
            'records': records,
            'detail': details
        }

        return api_ok_response(ret)
    except ValidationException as e:
        return api_error_response(e.code, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_defect_name_top_n(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    缺陷名称TopN
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        time_filter = json_body['time_filter']
        count = api_validate_int_value(json_body['filter'].get('count', 0), required=False, name="count", def_val=10, min_val=1, max_val=10)
        app_site_ids = json_body['app_site_ids']
        _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

        with ChangeRole(request.user.username):
            column_name_defaults = [('name', ''), ('total', 0)]
            repo = SummaryRepository()
            scanner_repo = ScannerRepository()
            defect_repo = DefectSummaryRepository()
            defect_query = repo.get_defect_name_top_n(app_site_ids, count, start_time, end_time)
            records = []
            details = []
            defect_names = []
            defect_name_to_config = get_risk_name_config_dict()
            scanner_defects = scanner_repo.get_scanner_defect_info()
            for defect in defect_query:
                record = []
                for name, default in column_name_defaults:
                    record.append(defect.get(name, default))

                records.append(record)
                defect_name = defect['name']
                defect_names.append(defect_name)
                detail = {}
                detail['name'] = defect_name
                detail['category'] = ugettext('Unknown')
                detail['desc'] = ""
                detail['level'] = 'high' if ('high' in defect['level']) else ''
                detail['tobe_confirmed'] = 0
                detail['tobe_handled'] = 0
                detail['handled'] = 0

                config = defect_name_to_config.get(defect_name, None)
                if config:
                    detail['category'] = config.get('category', ugettext('Unknown'))
                    detail['owasp_class'] = config.get('owasp_class', OWASP_API_CLASS_0)
                    detail['desc'] = config.get('desc', "")
                elif defect_name in scanner_defects:
                        scanner_risk_info = scanner_defects.get(defect_name, {})
                        detail['category'] = scanner_risk_info.get('risk_class', ugettext('Unknown'))
                        detail['owasp_class'] = scanner_risk_info.get('owasp_class', OWASP_API_CLASS_0)
                        detail['desc'] = scanner_risk_info.get('description', '')
                else:
                    detail['category'] = ugettext('Unknown')
                    detail['desc'] = ''
                    detail['owasp_class'] = OWASP_API_CLASS_0

                details.append(detail)
            defect_counts = defect_repo.get_defect_name_status_count_by_app_site_id_or_names(app_site_ids, start_time, end_time, defect_names)
            for dc in defect_counts:
                for d in details:
                    if dc['name'] == d['name']:
                        d['tobe_confirmed'] = dc['data'].get('tobe_confirmed', 0)
                        d['tobe_handled'] = dc['data'].get('tobe_handled', 0)
                        d['handled'] = dc['data'].get('handled', 0)
                        
        column_names = [name for name, default in column_name_defaults]
        ret = {'column_names': column_names,
            'records': records,
            'detail': details}

        return api_ok_response(ret)
    except ValidationException as e:
        return api_error_response(e.code, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_pii_top_n(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    敏感信息TopN
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        time_filter = json_body['time_filter']
        count = api_validate_int_value(json_body['filter'].get('count', 0), required=False, name="count", def_val=10, min_val=1, max_val=10)
        app_site_ids = json_body['app_site_ids']
        _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

        with ChangeRole(request.user.username):
            column_name_defaults = [('name', ''), ('total', 0)]
            piis = SummaryRepository().get_pii_top(app_site_ids, count, start_time, end_time)
            records = []
            details = []
            pii_type_to_config = get_pii_type_config_dict()
            for pii in piis:
                record = []
                detail = {}
                for name, default in column_name_defaults:
                    record.append(pii.get(name, default))

                records.append(record)

                detail['intranet_count'] = pii.get('intranet_count', 0)
                detail['domestic_count'] = pii.get('domestic_count', 0)
                detail['oversea_count'] = pii.get('oversea_count', 0)
                config = pii_type_to_config.get(pii['name'])
                level = 'low'
                if config:
                    level = config['level']
                detail['level'] = level
                industry_tag = convert_pii_type_to_industry_data_tag(pii['name'])
                detail['industries'] = [industry_tag] if industry_tag else []
                details.append(detail)

        column_names = [name for name, default in column_name_defaults]
        ret = {'column_names': column_names,
            'records': records,
            'detail': details}

        return api_ok_response(ret)
    except ValidationException as e:
        return api_error_response(e.code, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_api_access_trend(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    API访问趋势折线图
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    filter = req_body.to_json()
    time_filter = filter['time_filter']
    app_site_ids = filter['app_site_ids']
    _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

    def build_ts_dict(ts_dict, timestamp, type, total):
        if ts_dict.get(timestamp, None) is None:
            ts_dict[timestamp] = {type: total}
        else:
            if ts_dict[timestamp].get(type, None) is None:
                ts_dict[timestamp][type] = total
            else:
                ts_dict[timestamp][type] += total
        return ts_dict

    with ChangeRole(request.user.username):
        access_trends = SummaryRepository().get_access_trend(app_site_ids, start_time, end_time)
        attack_trends = AttackSummaryRepository().get_trend(app_site_ids, start_time, end_time, request.user.username)
        column_names = ["timestamp", "attack_count", "defect_count", "pii_count", "access_count"]
        ts_dict = {}
        for trend in access_trends:
            type = '{}_count'.format(trend['type'])

            if type in column_names:
                total = trend['total']
                build_ts_dict(ts_dict, trend['timestamp'], type, total)

        for trend in attack_trends:
            type = 'attack_count'
            total = trend['total']
            build_ts_dict(ts_dict, trend['time'], type, total)

    records = []
    for timestamp in sorted(ts_dict.keys()):
        record = []
        record.append(timestamp)
        for name in column_names[1:]:
            record.append(ts_dict[timestamp].get(name, 0))

        records.append(record)
    
    display_precision, merged_echarts = merge_echarts_format_result(records, time_filter, start_time, end_time)

    ret = {
            'display_precision': display_precision,
            'column_names': column_names,
            'records': merged_echarts
          }

    return api_ok_response(ret)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_bot_top_n(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    机器人访问TopN
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        time_filter = json_body['time_filter']
        count = api_validate_int_value(json_body['filter'].get('count', 0), required=False, name="count", def_val=10, min_val=1, max_val=10)
        app_site_ids = json_body['app_site_ids']
        with ChangeRole(request.user.username):
            _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

            column_name_defaults = [('bot_name', ugettext('Bot Name'),  ''), ('total', ugettext('Total Requests'), 0)]
            repo = SummaryRepository()
            bots = repo.get_bot_name_top(app_site_ids, count, start_time, end_time)
            ret = get_bar_chart_data_by_column_names(bots, column_name_defaults)
            details = []
            for record in ret['records']:
                detail = {}
                bot_name = record[0]
                dts = repo.get_bot_details(bot_name, app_site_ids, start_time, end_time)
                for dt in dts:
                    detail[dt.get('category', '')] = dt.get('total', 0)
                details.append(detail)

        ret['detail'] = details

        return api_ok_response(ret)
    except ValidationException as e:
        return api_error_response(e.code, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
@RequestLruCache()
def post_dashboard_api_access_top_n(request):
    """
    @check_rest_api_permission: API_Read
    @RequestLruCache
    API访问调用TopN
    """
    try:
        req_body = SummaryFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        time_filter = json_body['time_filter']
        count = api_validate_int_value(json_body['filter'].get('count', 0), required=False, name="count", def_val=10, min_val=1, max_val=10)
        app_site_ids = json_body['app_site_ids']
        _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

        with ChangeRole(request.user.username):
            repo = SummaryRepository()
            access_query = repo.get_access_top_n(app_site_ids, count, start_time, end_time)
            api_ids = [access['api_id'] for access in access_query]
            ret=[]
            pii_query = repo.get_pii_name_by_app_site_ids_and_api_ids(app_site_ids, start_time, end_time, api_ids)
            pii_names = {pii['api_id']:pii['names'] for pii in pii_query}
            defect_query = repo.get_defect_name_by_app_site_ids_and_api_ids(app_site_ids, start_time, end_time, api_ids)
            defect_names = {defect['api_id']: defect['names'] for defect in defect_query }
            for access in access_query:
                level_count = count_pii_level(pii_names.get(access['api_id'], []), {})

                count_defect_level(defect_names.get(access['api_id'], []), level_count)
                risk_level = get_api_risk_level(level_count)
                access['level'] = risk_level

                ret.append(access)

        return api_ok_response(ret)
    except ValidationException as e:
        return api_error_response(e.code, e.message)