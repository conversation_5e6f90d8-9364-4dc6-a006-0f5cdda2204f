# encoding: utf-8
# 此文件由 gen-server.py 自动创建，请不要手动修改此文件.

from django.conf.urls import url
from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_not_supported_method_response

from web_admin.apis import api_scanner
from web_admin.apis import parameter_detection
from web_admin.apis import settings
from web_admin.apis import summary
from web_admin.apis import user_login
from web_admin.apis import user_session
from web_admin.apis import whitelist
from web_admin.apis.assets import apis as assets_apis
from web_admin.apis.assets import apis_detail as assets_apis_detail
from web_admin.apis.assets import app_sites as assets_app_sites
from web_admin.apis.assets import config as assets_config
from web_admin.apis.assets import customized_rule as assets_customized_rule
from web_admin.apis.assets import ignored_apis as assets_ignored_apis
from web_admin.apis.assets import port as assets_port
from web_admin.apis.assets import settings as assets_settings
from web_admin.apis.assets import summary as assets_summary
from web_admin.apis.attacks import api_seq as attacks_api_seq
from web_admin.apis.attacks import attacks as attacks_attacks
from web_admin.apis.attacks import detail as attacks_detail
from web_admin.apis.attacks import settings as attacks_settings
from web_admin.apis.attacks import summary as attacks_summary
from web_admin.apis.common import filters as common_filters
from web_admin.apis.defects import defects as defects_defects
from web_admin.apis.defects import detail as defects_detail
from web_admin.apis.defects import settings as defects_settings
from web_admin.apis.defects import summary as defects_summary
from web_admin.apis.piis import api_file as piis_api_file
from web_admin.apis.piis import config as piis_config
from web_admin.apis.piis import list as piis_list
from web_admin.apis.piis import not_http as piis_not_http
from web_admin.apis.piis import summary as piis_summary
from web_admin.apis.report_optimization import detailed_list as report_optimization_detailed_list
from web_admin.apis.report_optimization import overview as report_optimization_overview
from web_admin.apis.report_optimization import risk as report_optimization_risk

@csrf_exempt
def entry_get_apis(request, *args):
    if request.method.lower() == "post": return assets_apis.post_get_apis(request, *args)
    if request.method.lower() == "delete": return assets_apis.delete_apis(request, *args)
    if request.method.lower() == "patch": return assets_apis.patch_assets_apis(request, *args)
    if request.method.lower() == "get": return assets_apis.get_assets_apis(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_asssets_apis_business(request, *args):
    if request.method.lower() == "post": return assets_config.post_asssets_apis_business(request, *args)
    if request.method.lower() == "put": return assets_config.put_asssets_apis_business(request, *args)
    if request.method.lower() == "delete": return assets_config.delete_asssets_apis_business(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_config_account_track(request, *args):
    if request.method.lower() == "get": return assets_config.get_assets_config_account_track(request, *args)
    if request.method.lower() == "post": return assets_config.post_assets_config_account_track(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_apis_industries(request, *args):
    if request.method.lower() == "post": return assets_config.post_assets_apis_industries(request, *args)
    if request.method.lower() == "put": return assets_config.put_assets_apis_industries(request, *args)
    if request.method.lower() == "delete": return assets_config.delete_assets_apis_industries(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_port_merge_config(request, *args):
    if request.method.lower() == "get": return assets_config.get_port_merge_config(request, *args)
    if request.method.lower() == "post": return assets_config.post_port_merge(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_get_app_sites(request, *args):
    if request.method.lower() == "post": return assets_app_sites.post_get_app_sites(request, *args)
    if request.method.lower() == "delete": return assets_app_sites.delete_app_sites(request, *args)
    if request.method.lower() == "patch": return assets_app_sites.patch_app_sites(request, *args)
    if request.method.lower() == "put": return assets_app_sites.put_assets_app_sites(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_app_sites_confirm(request, *args):
    if request.method.lower() == "post": return assets_app_sites.post_assets_app_sites_confirm(request, *args)
    if request.method.lower() == "put": return assets_app_sites.put_assets_app_sites_confirm(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_api_parameters_verification(request, *args):
    if request.method.lower() == "get": return assets_apis_detail.get_api_parameters_verification(request, *args)
    if request.method.lower() == "put": return assets_apis_detail.put_api_parameters_verification(request, *args)
    if request.method.lower() == "patch": return assets_apis_detail.patch_api_parameters_verification(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_api_detail_defect_trace(request, *args):
    if request.method.lower() == "get": return defects_detail.get_api_detail_defect_trace(request, *args)
    if request.method.lower() == "put": return defects_detail.put_assets_apis_id_detail_defect_trace(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_api_ignore_items(request, *args):
    if request.method.lower() == "delete": return assets_ignored_apis.delete_api_ignore_items(request, *args)
    if request.method.lower() == "post": return assets_ignored_apis.post_assets_ignored_apis(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_defect_settings(request, *args):
    if request.method.lower() == "get": return defects_settings.get_defect_settings(request, *args)
    if request.method.lower() == "patch": return defects_settings.patch_defect_settings(request, *args)
    if request.method.lower() == "put": return defects_settings.put_defects_settings(request, *args)
    if request.method.lower() == "post": return defects_settings.post_defects_settings(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_defects_settings_auto_convert(request, *args):
    if request.method.lower() == "get": return defects_settings.get_defects_settings_auto_convert(request, *args)
    if request.method.lower() == "post": return defects_settings.post_defects_settings_auto_convert(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_defects_settings_customize(request, *args):
    if request.method.lower() == "delete": return defects_settings.delete_defects_settings_customize(request, *args)
    if request.method.lower() == "put": return defects_settings.put_defects_settings_customize(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_get_defects(request, *args):
    if request.method.lower() == "post": return defects_defects.post_get_defects(request, *args)
    if request.method.lower() == "patch": return defects_defects.patch_defects(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_user_login(request, *args):
    if request.method.lower() == "post": return user_login.post_user_login(request, *args)
    if request.method.lower() == "get": return user_login.get_user_login(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_user_password(request, *args):
    if request.method.lower() == "post": return user_login.post_user_password(request, *args)
    if request.method.lower() == "patch": return user_login.patch_user_password(request, *args)
    if request.method.lower() == "put": return user_login.put_user_password(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_customized_rules(request, *args):
    if request.method.lower() == "get": return assets_customized_rule.get_customized_rules(request, *args)
    if request.method.lower() == "post": return assets_customized_rule.post_customized_rule(request, *args)
    if request.method.lower() == "put": return assets_customized_rule.put_customized_rule(request, *args)
    if request.method.lower() == "delete": return assets_customized_rule.delete_customized_rule(request, *args)
    if request.method.lower() == "patch": return assets_customized_rule.patch_customized_rule(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_static_resource(request, *args):
    if request.method.lower() == "post": return assets_customized_rule.post_static_resource(request, *args)
    if request.method.lower() == "get": return assets_customized_rule.get_static_resource(request, *args)
    if request.method.lower() == "put": return assets_customized_rule.put_static_resource(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_settings_business(request, *args):
    if request.method.lower() == "get": return assets_settings.get_assets_settings_business(request, *args)
    if request.method.lower() == "post": return assets_settings.post_assets_settings_business(request, *args)
    if request.method.lower() == "delete": return assets_settings.delete_assets_settings_business(request, *args)
    if request.method.lower() == "put": return assets_settings.put_assets_settings_business(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_settings_business_whitelist(request, *args):
    if request.method.lower() == "get": return assets_settings.get_assets_settings_business_whitelist(request, *args)
    if request.method.lower() == "post": return assets_settings.post_assets_settings_business_whitelist(request, *args)
    if request.method.lower() == "delete": return assets_settings.delete_assets_settings_business_whitelist(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_period_sample_conf(request, *args):
    if request.method.lower() == "get": return assets_settings.get_period_sample_conf(request, *args)
    if request.method.lower() == "post": return assets_settings.post_period_sample_conf(request, *args)
    if request.method.lower() == "put": return assets_settings.put_period_sample_conf(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_settings_exclude_error_response(request, *args):
    if request.method.lower() == "post": return assets_settings.post_assets_settings_exclude_error_response(request, *args)
    if request.method.lower() == "get": return assets_settings.get_assets_settings_exclude_error_response(request, *args)
    if request.method.lower() == "put": return assets_settings.put_assets_settings_exclude_error_response(request, *args)
    if request.method.lower() == "patch": return assets_settings.patch_assets_settings_exclude_error_response(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_full_sample_conf(request, *args):
    if request.method.lower() == "get": return assets_settings.get_full_sample_conf(request, *args)
    if request.method.lower() == "post": return assets_settings.post_full_sample_conf(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_setting_lifecycle(request, *args):
    if request.method.lower() == "get": return assets_settings.get_assets_setting_lifecycle(request, *args)
    if request.method.lower() == "post": return assets_settings.post_assets_setting_lifecycle(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_setting_automated_cleanup(request, *args):
    if request.method.lower() == "get": return assets_settings.get_assets_setting_automated_cleanup(request, *args)
    if request.method.lower() == "post": return assets_settings.post_assets_setting_automated_cleanup(request, *args)
    if request.method.lower() == "put": return assets_settings.put_assets_setting_automated_cleanup(request, *args)
    if request.method.lower() == "delete": return assets_settings.delete_assets_setting_api_automated_cleanup(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_assets_settings_api_merge_whitelist(request, *args):
    if request.method.lower() == "get": return assets_settings.get_assets_settings_api_merge_whitelist(request, *args)
    if request.method.lower() == "post": return assets_settings.post_assets_settings_api_merge_whitelist(request, *args)
    if request.method.lower() == "put": return assets_settings.put_assets_settings_api_merge_whitelist(request, *args)
    if request.method.lower() == "delete": return assets_settings.delete_assets_settings_api_merge_whitelist(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_piis_config_desensitize_store(request, *args):
    if request.method.lower() == "patch": return piis_config.patch_piis_config_desensitize_store(request, *args)
    if request.method.lower() == "get": return piis_config.get_piis_config_desensitize_store(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_piis_config(request, *args):
    if request.method.lower() == "get": return piis_config.get_piis_config(request, *args)
    if request.method.lower() == "post": return piis_config.post_piis_config(request, *args)
    if request.method.lower() == "put": return piis_config.put_piis_config(request, *args)
    if request.method.lower() == "patch": return piis_config.patch_piis_config(request, *args)
    if request.method.lower() == "delete": return piis_config.delete_piis_config(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_pii_settings_industries(request, *args):
    if request.method.lower() == "get": return piis_config.get_pii_settings_industries(request, *args)
    if request.method.lower() == "patch": return piis_config.patch_pii_settings_industries(request, *args)
    if request.method.lower() == "put": return piis_config.put_pii_settings_industries(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_piis_config_file_detect(request, *args):
    if request.method.lower() == "get": return piis_config.get_piis_config_file_detect(request, *args)
    if request.method.lower() == "put": return piis_config.put_piis_config_file_detect(request, *args)
    if request.method.lower() == "patch": return piis_config.patch_piis_config_file_detect(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_piis_desensitive_show(request, *args):
    if request.method.lower() == "post": return piis_config.post_piis_desensitive_show(request, *args)
    if request.method.lower() == "get": return piis_config.get_piis_desensitize_show(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_attack_detect_settings(request, *args):
    if request.method.lower() == "get": return attacks_settings.get_attack_detect_settings(request, *args)
    if request.method.lower() == "post": return attacks_settings.post_attack_detect_settings(request, *args)
    if request.method.lower() == "put": return attacks_settings.put_attack_detect_settings(request, *args)
    if request.method.lower() == "delete": return attacks_settings.delete_attack_detect_settings(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_attack_detect_settings_policies(request, *args):
    if request.method.lower() == "put": return attacks_settings.put_attack_detect_settings_policies(request, *args)
    if request.method.lower() == "patch": return attacks_settings.patch_attack_detect_settings_policies(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_attacks_settings_api_seqs(request, *args):
    if request.method.lower() == "get": return attacks_api_seq.get_attacks_settings_api_seqs(request, *args)
    if request.method.lower() == "post": return attacks_api_seq.post_attacks_settings_api_seqs(request, *args)
    if request.method.lower() == "put": return attacks_api_seq.put_attacks_settings_api_seqs(request, *args)
    if request.method.lower() == "delete": return attacks_api_seq.delete_attacks_settings_api_seqs(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_attacks_settings_api_seqs_status(request, *args):
    if request.method.lower() == "post": return attacks_api_seq.post_attacks_settings_api_seqs_status(request, *args)
    if request.method.lower() == "get": return attacks_api_seq.get_attacks_settings_api_seqs_status(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_parameter_detection_setting(request, *args):
    if request.method.lower() == "put": return parameter_detection.put_parameter_detection_setting(request, *args)
    if request.method.lower() == "patch": return parameter_detection.patch_parameter_detection_setting(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_parameter_detection_outcomes(request, *args):
    if request.method.lower() == "get": return parameter_detection.get_parameter_detection_outcomes(request, *args)
    if request.method.lower() == "post": return parameter_detection.post_parameter_detection_outcomes(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_parameter_detection_pro(request, *args):
    if request.method.lower() == "get": return parameter_detection.get_parameter_detection_pro(request, *args)
    if request.method.lower() == "post": return parameter_detection.post_parameter_detection_progress(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_parameter_detection_monitor(request, *args):
    if request.method.lower() == "get": return parameter_detection.get_parameter_detection_monitor(request, *args)
    if request.method.lower() == "post": return parameter_detection.post_parameter_detection_monitor(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_api_scanner(request, *args):
    if request.method.lower() == "get": return api_scanner.get_api_scanner(request, *args)
    if request.method.lower() == "post": return api_scanner.post_api_scanner(request, *args)
    if request.method.lower() == "put": return api_scanner.put_api_scanner(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_whitelist(request, *args):
    if request.method.lower() == "get": return whitelist.get_whitelist(request, *args)
    if request.method.lower() == "post": return whitelist.post_whitelist(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_risk_setting(request, *args):
    if request.method.lower() == "get": return report_optimization_risk.get_risk_setting(request, *args)
    if request.method.lower() == "post": return report_optimization_risk.post_risk_setting(request, *args)
    if request.method.lower() == "patch": return report_optimization_risk.patch_risk_setting(request, *args)
    return api_not_supported_method_response()

@csrf_exempt
def entry_risk_setting_display_none_risk(request, *args):
    if request.method.lower() == "get": return report_optimization_risk.get_risk_setting_display_none_risk(request, *args)
    if request.method.lower() == "post": return report_optimization_risk.post_risk_setting_display_none_risk(request, *args)
    return api_not_supported_method_response()

api_patterns = [
    url(r'^assets/apis$', entry_get_apis),
    url(r'^assets/apis/([^/]+)/access_statistical$', assets_apis.get_api_access_statistical),
    url(r'^assets/apis/([^/]+)/detail/access_trends$', assets_apis.get_api_access_trends),
    url(r'^assets/apis/([^/]+)/detail/top_src_ips$', assets_apis.get_api_top_src_ips),
    url(r'^assets/apis/([^/]+)/detail/top_accounts$', assets_apis.get_api_top_account),
    url(r'^assets/apis/([^/]+)$', assets_apis.put_update_api),
    url(r'^assets/api_total$', assets_apis.get_apis_total),
    url(r'^assets/api$', assets_apis.post_add_api),
    url(r'^assets/check_api_before_add$', assets_apis.post_assets_api_check_before_add),
    url(r'^assets/check_api_ref$', assets_apis.post_assets_check_api_ref),
    url(r'^assets/get_list_time_interval$', assets_apis.get_assets_get_list_time_interval),
    url(r'^assets/apis/move_to_merge_whitelist/$', assets_apis.post_assets_apis_move_to_merge_whitelist),
    url(r'^assets/bot_kinds$', assets_config.get_api_bot_kinds),
    url(r'^assets/config/business$', entry_asssets_apis_business),
    url(r'^assets/config/enable_account_track$', assets_config.post_assets_config_enable_account_track),
    url(r'^assets/config/account_track$', entry_assets_config_account_track),
    url(r'^assets/config/search_refresh_apis$', assets_config.post_assets_apis_refresh_api),
    url(r'^assets/config/industries$', entry_assets_apis_industries),
    url(r'^assets/config/port_merge$', entry_port_merge_config),
    url(r'^assets/users$', assets_app_sites.get_users),
    url(r'^assets/app_sites$', entry_get_app_sites),
    url(r'^assets/app_groups$', assets_app_sites.get_app_groups),
    url(r'^assets/app_site$', assets_app_sites.post_app_site),
    url(r'^assets/app_site/merge$', assets_app_sites.post_merge_app_sites),
    url(r'^assets/ignore_app_sites$', assets_app_sites.post_ignore_app_sites),
    url(r'^assets/app_sites/([^/]+)$', assets_app_sites.put_api_app_site),
    url(r'^assets/app_site/confirm$', entry_assets_app_sites_confirm),
    url(r'^assets/app_site/search_related_domains$', assets_app_sites.post_assets_app_site_search_related_domains),
    url(r'^assets/apis/([^/]+)/detail/statistical$', assets_apis_detail.get_api_detail_statistical),
    url(r'^assets/apis/([^/]+)/detail/basic_info$', assets_apis_detail.get_api_detail_basic_info),
    url(r'^assets/apis/([^/]+)/samples$', assets_apis_detail.get_api_samples),
    url(r'^assets/apis/([^/]+)/parameters_verification$', entry_api_parameters_verification),
    url(r'^defects/([^/]+)/detail/defect_trace$', entry_api_detail_defect_trace),
    url(r'^common/([^/]+)/filter_options$', common_filters.get_filter_options),
    url(r'^assets/exports$', assets_port.post_apis_exports),
    url(r'^assets/app_sites/exports/$', assets_port.post_export_app_sites),
    url(r'^assets/imports$', assets_port.post_api_imports),
    url(r'^assets/app_sites/imports/$', assets_port.post_app_sites_imports),
    url(r'^assets/ignored_apis$', entry_api_ignore_items),
    url(r'^assets/check_ignored_before_add$', assets_ignored_apis.post_assets_check_ignore_before_add),
    url(r'^assets/add_ignored_api$', assets_ignored_apis.post_assets_ignored_api),
    url(r'^assets/ignore_from_api$', assets_ignored_apis.post_assets_ignore_from_api),
    url(r'^defects/settings$', entry_defect_settings),
    url(r'^defects/settings/auto_convert$', entry_defects_settings_auto_convert),
    url(r'^defects/settings/customize$', entry_defects_settings_customize),
    url(r'^defects$', entry_get_defects),
    url(r'^defects/export$', defects_defects.post_get_defect_export),
    url(r'^defects/names$', defects_defects.post_get_defect_names),
    url(r'^defects/([^/]+)/([^/]+)/logs$', defects_defects.get_defect_names_logs),
    url(r'^defects/class$', defects_defects.get_defects_class),
    url(r'^defects/proof$', defects_defects.post_defects_proof),
    url(r'^attacks/dashboard/overview$', attacks_summary.post_attacks_dashboard_overview),
    url(r'^attacks/dashboard/attack_name_top_n$', attacks_summary.post_attacks_dashboard_attack_name_top_n),
    url(r'^attacks/dashboard/attack_trend$', attacks_summary.post_attacks_dashboard_attack_trend),
    url(r'^attacks/dashboard/attacked_api_top_n$', attacks_summary.post_attacks_dashboard_attacked_api_top_n),
    url(r'^attacks/dashboard/attack_source_top_n$', attacks_summary.post_attacks_dashboard_attack_source_top_n),
    url(r'^defects/dashboard/overview$', defects_summary.post_defects_dashboard_overview),
    url(r'^defects/dashboard/defect_name_top_n$', defects_summary.post_defects_dashboard_defect_name_top_n),
    url(r'^defects/dashboard/defect_api_top_n$', defects_summary.post_defects_dashboard_defect_api_top_n),
    url(r'^defects/dashboard/access_source_top_n$', defects_summary.post_defects_dashboard_access_source_top_n),
    url(r'^defects/dashboard/access_trend$', defects_summary.post_defects_dashboard_access_trend),
    url(r'^piis/dashboard/overview$', piis_summary.post_piis_dashboard_overview),
    url(r'^piis/dashboard/access_trend$', piis_summary.post_piis_dashboard_access_trend),
    url(r'^piis/dashboard/industry_category_top_n$', piis_summary.post_piis_dashboard_industry_category_top_n),
    url(r'^piis/dashboard/access_source_top_n$', piis_summary.post_piis_dashboard_access_source_top_n),
    url(r'^piis/dashboard/pii_name_top_n$', piis_summary.post_piis_dashboard_pii_name_top_n),
    url(r'^piis/dashboard/pii_api_top_n$', piis_summary.post_piis_dashboard_pii_api_top_n),
    url(r'^dashboard/overview$', summary.post_dashboard_overview),
    url(r'^dashboard/attack_event_top_n$', summary.post_dashboard_attack_event_top_n),
    url(r'^dashboard/defect_name_top_n$', summary.post_dashboard_defect_name_top_n),
    url(r'^dashboard/pii_top_n$', summary.post_dashboard_pii_top_n),
    url(r'^dashboard/api_access_trend$', summary.post_dashboard_api_access_trend),
    url(r'^dashboard/bot_top_n$', summary.post_dashboard_bot_top_n),
    url(r'^dashboard/api_access_top_n$', summary.post_dashboard_api_access_top_n),
    url(r'^summary/refresh$', summary.post_refresh_summary),
    url(r'^assets/dashboard/topology$', assets_summary.post_assets_dashboard_topology),
    url(r'^assets/dashboard/access_active_trend$', assets_summary.post_assets_dashboard_access_active_trend),
    url(r'^assets/dashboard/life_monitor$', assets_summary.post_assets_dashboard_life_monitor),
    url(r'^assets/dashboard/statistical$', assets_summary.post_assets_dashboard_statistical),
    url(r'^assets/dashboard/api_access_sources_top_n$', assets_summary.post_assets_dashboard_api_access_sources_top_n),
    url(r'^assets/dashboard/attacked_api_top_n$', assets_summary.post_assets_dashboard_attacked_api_top_n),
    url(r'^assets/dashboard/defect_api_top_n$', assets_summary.post_assets_dashboard_defect_api_top_n),
    url(r'^assets/dashboard/pii_api_top_n$', assets_summary.post_assets_dashboard_pii_api_top_n),
    url(r'^user/login$', entry_user_login),
    url(r'^user/login/captcha$', user_login.post_captcha_validation),
    url(r'^user/login/remote_user$', user_login.post_is_remote_user),
    url(r'^user/password$', entry_user_password),
    url(r'^user/super_token$', user_login.post_super_token),
    url(r'^user/logout$', user_login.get_user_logout),
    url(r'^menu$', settings.get_menu_config),
    url(r'^session/status$', user_session.get_session_status),
    url(r'^session/get_asp_conf$', user_session.get_asp_conf),
    url(r'^assets/config/customized_rule$', entry_customized_rules),
    url(r'^assets/config/reset-customized_rules$', assets_customized_rule.get_reset_customized_rules),
    url(r'^assets/config/static_resource$', entry_static_resource),
    url(r'^assets/settings/business$', entry_assets_settings_business),
    url(r'^assets/settings/business/whitelist$', entry_assets_settings_business_whitelist),
    url(r'^assets/settings/business/references$', assets_settings.get_assets_settings_business_ref),
    url(r'^assets/settings/period_sample$', entry_period_sample_conf),
    url(r'^assets/settings/exclude_error_response$', entry_assets_settings_exclude_error_response),
    url(r'^assets/settings/full_sample$', entry_full_sample_conf),
    url(r'^assets/settings/rules$', assets_settings.get_assets_settings_rules),
    url(r'^assets/setting/lifecycle$', entry_assets_setting_lifecycle),
    url(r'^assets/setting/api_automated_cleanup$', entry_assets_setting_automated_cleanup),
    url(r'^assets/settings/api_merge_whitelist$', entry_assets_settings_api_merge_whitelist),
    url(r'^piis/config/list$', piis_config.get_piis_config_list),
    url(r'^piis/config/desensitize_store$', entry_piis_config_desensitize_store),
    url(r'^piis/config$', entry_piis_config),
    url(r'^piis/config/update_level$', piis_config.post_piis_config_update_level),
    url(r'^piis/config/default$', piis_config.get_piis_config_default),
    url(r'^piis/config/industries$', entry_pii_settings_industries),
    url(r'^piis/config/file_detect$', entry_piis_config_file_detect),
    url(r'^piis/config/industries/custom$', piis_config.put_pii_settings_industries_custom),
    url(r'^piis/desensitize_show$', entry_piis_desensitive_show),
    url(r'^piis/list/apis$', piis_list.post_piis_list_apis),
    url(r'^piis/list/export$', piis_list.post_piis_list_export),
    url(r'^piis/list/set_pii_whitelist$', piis_list.post_piis_list_set_pii_whitelist),
    url(r'^piis/list/count$', piis_list.post_piis_list_count),
    url(r'^attacks/settings/attack_detect$', entry_attack_detect_settings),
    url(r'^attacks/settings/attack_detect/policies$', entry_attack_detect_settings_policies),
    url(r'^attacks/settings/attack_detect/exports$', attacks_settings.get_attack_detect_export),
    url(r'^attacks/settings/attack_detect/imports$', attacks_settings.post_attack_detect_imports),
    url(r'^attacks/settings/attack_detect/imports-confirm$', attacks_settings.post_attack_detect_imports_confirm),
    url(r'^attacks/settings/waf_global_whitelist$', attacks_settings.post_attacks_settings_waf_global_whitelist),
    url(r'^attacks$', attacks_attacks.post_attacks),
    url(r'^attacks/export$', attacks_attacks.post_attacks_export),
    url(r'^attacks/names$', attacks_attacks.post_attacks_names),
    url(r'^attacks/detail/basic_info/([^/]+)$', attacks_detail.get_attacks_detail_basic_info_id),
    url(r'^attacks/detail/access_log/([^/]+)$', attacks_detail.get_attacks_detail_access_log_id),
    url(r'^attacks/detail/waf/([^/]+)$', attacks_detail.get_waf_attacks_detail_event_uuid),
    url(r'^piis/list/not_http$', piis_not_http.post_piis_list_not_http),
    url(r'^piis/list/not_http_export$', piis_not_http.post_piis_list_not_http_export),
    url(r'^attacks/settings/api_seqs$', entry_attacks_settings_api_seqs),
    url(r'^attacks/settings/api_seqs_status$', entry_attacks_settings_api_seqs_status),
    url(r'^attacks/settings/api_seqs_learning_list$', attacks_api_seq.post_get_attacks_settings_api_seqs_learning_list),
    url(r'^attacks/settings/ignore_api_seq$', attacks_api_seq.post_attacks_settings_ignore_api_seqs),
    url(r'^attacks/settings/recover_api_seq$', attacks_api_seq.post_attacks_settings_recover_api_seq),
    url(r'^parameter-detection/setting$', entry_parameter_detection_setting),
    url(r'^parameter-detection/outcomes$', entry_parameter_detection_outcomes),
    url(r'^parameter-detection/all_app$', parameter_detection.get_parameter_detection_all_app),
    url(r'^parameter-detection/app$', parameter_detection.get_parameter_detection_app),
    url(r'^parameter-detection/progress$', entry_parameter_detection_pro),
    url(r'^parameter-detection/monitor$', entry_parameter_detection_monitor),
    url(r'^parameter-detection/exports$', parameter_detection.get_parameter_detection_exports),
    url(r'^parameter-detection/app/outcomes$', parameter_detection.post_parameter_detection_app_outcomes),
    url(r'^parameter-detection/bulk_parameters_verification$', parameter_detection.put_bulk_parameters_verification),
    url(r'^piis/list/files$', piis_api_file.post_get_piis_list_files),
    url(r'^piis/list/files/export$', piis_api_file.post_piis_list_files_export),
    url(r'^piis/list/files/download$', piis_api_file.post_get_piis_list_files_download),
    url(r'^api_scanner$', entry_api_scanner),
    url(r'^api_scanner_login$', api_scanner.post_api_scanner_login),
    url(r'^([^/]+)/settings/whitelist$', entry_whitelist),
    url(r'^([^/]+)/settings/whitelist/rules$', whitelist.get_whitelist_rules),
    url(r'^risk/setting$', entry_risk_setting),
    url(r'^risk/setting/reset$', report_optimization_risk.post_reset_risk_setting),
    url(r'^risk/setting/display_none_risk$', entry_risk_setting_display_none_risk),
    url(r'^risk/overview/src_top_n$', report_optimization_overview.post_src_top_n),
    url(r'^risk/overview/abnormal_req_trend$', report_optimization_overview.post_abnormal_req_trend),
    url(r'^risk/overview/top_bots$', report_optimization_overview.post_top_bots),
    url(r'^risk/overview/latest_risks$', report_optimization_overview.post_overview_latest_risks),
    url(r'^risk/overview/req_ratio_history$', report_optimization_overview.post_req_ratio_history),
    url(r'^risk/overview/risk_ratio_history$', report_optimization_overview.post_risk_ratio_history),
    url(r'^risk/overview/risk_statistics$', report_optimization_overview.post_risk_statistics),
    url(r'^risk/overview/risk_trend$', report_optimization_overview.post_risk_trend),
    url(r'^risk/detailed_list$', report_optimization_detailed_list.post_risk_detailed_list),
    url(r'^risk/detailed_list/details$', report_optimization_detailed_list.post_risk_detailed_list_details),
    url(r'^risk/detailed_list/related_fp$', report_optimization_detailed_list.post_risk_detailed_list_related_fp),
    url(r'^risk/detailed_list/related_ip$', report_optimization_detailed_list.post_risk_detailed_list_related_ip),
]