# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.attack_list_filter import <PERSON><PERSON><PERSON><PERSON><PERSON>er
from abd_utils.schemas.post_attacks_export_request import PostAttacksExportRequest

#### 自动导入结束，不要在前面插入代码

import logging
from django.utils.translation import ugettext
from collections import OrderedDict
from api_base.base_api import api_file_response, api_word_tpl_response
from abd_utils.utils.func_base import LEVEL_OPTION_TO_LABEL, EXPORT_WORD_LIMIT, EXPORT_CSV_LIMIT
from abd_utils.repositories.attacks.attack import AttackRepository
from django.utils.translation import gettext as _
from asp_utils.utils import get_release_file, get_language
from api_base.result_code import RC_MORE_INFO, RC_BAD_PARAMETERS, RC_INTERNAL_ERROR
from abd_utils.utils.func_datetime_convert import get_start_end_time_by_time_filter
from web_admin.view_lib import botgate_user_to_sf_user

ATTACK_LIST_TPL = get_release_file('bin/abd_utils/docx_tpl/{}/attack_list_tpl.docx'.format(get_language()))
ATTACK_LIST_EXPORT_WORD_FILE_NAME = 'attack_list.docx'
ATTACK_LIST_EXPORT_CSV_FILE_NAME = 'attack_list.csv'

def process_start_end_time(time_filter):
    # 时间按窗口对齐， 与报表上时间处理一致。
    _, start, end = get_start_end_time_by_time_filter(time_filter)
    # 攻击概览和清单没有做union，当查询范围超过7天时，概览跳转到清单数据不一致问题无法通过处理查询时间来修正，先注释掉align_start_end_time
    return start, end

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_attacks(request):
    """
    @check_rest_api_permission: API_Read
    获取攻击事件清单
    """
    try:
        req_body = AttackListFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        conditions = req_body.to_json()
        total_count, events = AttackRepository().get_attack_event(conditions, sailfish_user=botgate_user_to_sf_user(request.user.username))
    except Exception as e:
        err_msg = e.message
        if 'Memory limit' in err_msg and 'exceeded' in err_msg:
            err_msg = ugettext('Insufficient memory, please increase memory or reduce query time range.')

        if '''Can'nt connect server''' in err_msg:
            err_msg = ugettext('Unable to connect to big data analysis server, please try again later.')
            
        logging.error(e.message)
        return api_error_response(RC_INTERNAL_ERROR, err_msg)
    
    return api_ok_response(events, total_count)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_attacks_export(request):
    """
    @check_rest_api_permission: API_Read
    导出攻击事件清单
    """
    try:
        req_body = ClassField("PostAttacksExportRequest", PostAttacksExportRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    repo = AttackRepository()
    json_req_body = req_body.to_json()
    filter_conditions = json_req_body['filter_conditions']
    file_type = json_req_body.pop('file_type')
    step = json_req_body.pop('step')
    pending_operation_log(request, _("Export"), _('export {} to {} file.').format(_("attack list"), file_type if file_type != 'abd_csv' else 'csv'))
    if file_type == 'word':
        if step == 1:
            event_count = repo.get_attack_event_count(filter_conditions, sailfish_user=botgate_user_to_sf_user(request.user.username))
            if event_count > EXPORT_WORD_LIMIT:
                return api_error_response(RC_MORE_INFO, _('The number of items in the current list after '
                                                          'filtering is greater than {export_limit} items!, continue to '
                                                          'export {export_limit} items or cancel?').format(
                    export_limit=EXPORT_WORD_LIMIT))

        content = repo.get_export_word_content(filter_conditions, sailfish_user=botgate_user_to_sf_user(request.user.username))
        return api_word_tpl_response(ATTACK_LIST_TPL, content, ATTACK_LIST_EXPORT_WORD_FILE_NAME, False)

    elif file_type == 'abd_csv':
        if step == 1:
            event_count = repo.get_attack_event_count(filter_conditions, sailfish_user=botgate_user_to_sf_user(request.user.username))
            if event_count > EXPORT_CSV_LIMIT:
                return api_error_response(RC_MORE_INFO, _('The number of items in the current list after '
                                                          'filtering is greater than {export_limit} items!, continue to '
                                                          'export {export_limit} items or cancel?').format(
                    export_limit=EXPORT_CSV_LIMIT))
    
        content = repo.get_export_csv_content(filter_conditions, sailfish_user=botgate_user_to_sf_user(request.user.username))
        return api_file_response(content, ATTACK_LIST_EXPORT_CSV_FILE_NAME)

    else:
        return api_error_response(RC_BAD_PARAMETERS, 'Invalid file type: {}'.format(file_type))


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_attacks_names(request):
    """
    @check_rest_api_permission: API_Read
    获取攻击名称
    """
    try:
        req_body = AttackListFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    conditions = req_body.to_json()
    attack_names = AttackRepository().get_attack_name(conditions, sailfish_user=botgate_user_to_sf_user(request.user.username))
    
    return api_ok_response(attack_names)