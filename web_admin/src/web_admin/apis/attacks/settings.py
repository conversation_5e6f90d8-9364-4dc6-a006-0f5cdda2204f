# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import <PERSON>Field, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.delete_attack_detect_settings_request import DeleteAttackDetectSettingsRequest
from abd_utils.schemas.patch_attack_detect_settings_policies_request import PatchAttackDetectSettingsPoliciesRequest
from abd_utils.schemas.post_attack_detect_imports_confirm_request import PostAttackDetectImportsConfirmRequest
from abd_utils.schemas.post_attack_detect_settings_request import PostAttackDetectSettingsRequest
from abd_utils.schemas.post_attacks_settings_waf_global_whitelist_request import PostAttacksSettingsWafGlobalWhitelistRequest
from abd_utils.schemas.put_attack_detect_settings_policies_request import PutAttackDetectSettingsPoliciesRequest
from abd_utils.schemas.put_attack_detect_settings_request import PutAttackDetectSettingsRequest

#### 自动导入结束，不要在前面插入代码

import json
import os.path
import hashlib
import logging
from django.utils.translation import ugettext as _
from abd_utils.utils.func_attack_policy import ApiAttackPolicyList, MAX_DEFAULT_ID, API_CUSTOMIZED_ATTACK_POLICY_KEY_MAP, \
        API_CUSTOMIZED_ATTACK_POLICY_OPERATION_MAP
from abd_utils.utils.func_base import get_abd_zk_lock
from api_base.base_api import api_file_response
from asp_utils.filelock import FileLock
from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.repositories.assets.app_site import AppSiteRepository
from api_base.result_code import RC_INTERNAL_ERROR, RC_BAD_PARAMETERS
from abd_utils.utils.func_database import ChangeRole
from asp_utils.utils import dict_diff
from api_base.base_api import remove_pending_operation_log
from abd_utils.utils.func_whitelist import filter_whitelist_invalid_ref

TMP_IMPORT_ATTACK_POLICY_FILE = '/tmp/attack_policy'

def convert_to_old_policy(policy):
    policy['risk_level'] = {'value': policy.pop('risk_level', '')}
    for rule in policy['rules']:
        if rule['key'] == 'req_header':
            rule['value'] = {rule['value']['header_key']:rule['value']['header_value']} if type(rule['value']) == dict else {} 
    return policy

def convert_to_new_policy(policy):
    policy['risk_level'] = policy['risk_level']['value']
    for rule in policy['rules']:
        if rule['key'] == 'req_header':
            for key,value in rule['value'].items():
                rule['value'] = {'header_key': key, 'header_value': value}
        if 'value' not in rule:
            rule['value'] = ''
    target = policy.get('target', None)
    if type(target) == dict and target['type'] == 'api':
        value_info, _ = ApiRepository().get_ref_api_info_by_ids(target['value'])
        target['value_info'] = value_info
    if type(target) == dict and target['type'] == 'group':
        app_site_infos, _ = AppSiteRepository().get_app_and_group_by_ids(target['value'])
        target['value_info'] = app_site_infos[:]
    return policy

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_attack_detect_settings(request):
    """
    @check_rest_api_permission: API_Setting
    获取攻击检测配置列表
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    with ChangeRole(request.user.username):
        attack_policies = ApiAttackPolicyList().get_list()
        for policy in attack_policies:
            convert_to_new_policy(policy)

    attack_policies = filter_whitelist_invalid_ref(attack_policies)
    return api_ok_response({'attack_policies': attack_policies,
                            'key_map':API_CUSTOMIZED_ATTACK_POLICY_KEY_MAP,
                            'operation_map':API_CUSTOMIZED_ATTACK_POLICY_OPERATION_MAP})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_attack_detect_settings(request):
    """
    @check_rest_api_permission: API_Setting
    添加攻击检测策略配置
    """
    try:
        req_body = ClassField("PostAttackDetectSettingsRequest", PostAttackDetectSettingsRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        policy = req_body.to_json()
        with get_abd_zk_lock():
            api_attack_policy = ApiAttackPolicyList()
            policy = convert_to_old_policy(policy)
            new_policy = api_attack_policy.add(policy, policy.get('copy_from_rule_id', 0))
            
        new_policy = convert_to_new_policy(new_policy)

        op_content = "{}: {}".format(_('API Attack Policy'), new_policy['id'])
        pending_operation_log(request, _("Add"), op_content, info=new_policy)
    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)
    return api_ok_response(new_policy)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def put_attack_detect_settings(request):
    """
    @check_rest_api_permission: API_Setting
    更新攻击检测策略配置
    """
    try:
        req_body = ClassField("PutAttackDetectSettingsRequest", PutAttackDetectSettingsRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        policy = req_body.to_json()
        with get_abd_zk_lock():
            policy_id = req_body.id
            api_attack_policy = ApiAttackPolicyList()
            old_policy = convert_to_new_policy(api_attack_policy.find_policy_by_id(policy_id))
            policy = convert_to_old_policy(policy)
            api_attack_policy.modify(policy)
        new_policy = convert_to_new_policy(api_attack_policy.find_policy_by_id(policy_id))
        diff=dict_diff(new_policy, old_policy)
        op_content = "{}: {}".format(_('API Attack Policy'), policy_id)
        pending_operation_log(request, _("Modify"), op_content, info=diff)
    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)
    return api_ok_response(new_policy)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def delete_attack_detect_settings(request):
    """
    @check_rest_api_permission: API_Setting
    删除攻击检测策略配置
    """
    try:
        req_body = ClassField("DeleteAttackDetectSettingsRequest", DeleteAttackDetectSettingsRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        api_attack_policy = ApiAttackPolicyList()

        if req_body.ids:
            api_attack_policy.delete(req_body.ids)
            
        op_content = "{}: {}".format(_('API Attack Policy'), req_body.ids)
        pending_operation_log(request, _("Delete"), op_content)
    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

    return api_ok_response({})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def put_attack_detect_settings_policies(request):
    """
    @check_rest_api_permission: API_Setting
    修改攻击检测策略级别
    """
    try:
        req_body = ClassField("PutAttackDetectSettingsPoliciesRequest", PutAttackDetectSettingsPoliciesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        policy = req_body.to_json()
        with get_abd_zk_lock():
            api_attack_policy = ApiAttackPolicyList()
            policy['id_list'] = policy.pop('ids', [])
            api_attack_policy.set_policy_level(policy)
        
        op_content = "{}: {}".format(_('API Attack Policy Risk Level'), policy['id_list'])
        pending_operation_log(request, _("Modify"), op_content, info=policy)
    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)
    
    return api_ok_response({'ids':req_body.ids})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def patch_attack_detect_settings_policies(request):
    """
    @check_rest_api_permission: API_Setting
    启用攻击检测策略配置
    """
    try:
        req_body = ClassField("PatchAttackDetectSettingsPoliciesRequest", PatchAttackDetectSettingsPoliciesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        policy = req_body.to_json()
        policy['id_list'] = policy.pop('ids', [])
        with get_abd_zk_lock():
            api_attack_policy = ApiAttackPolicyList()
            api_attack_policy.set_policy_enable(policy)

        op_content = "{}: {}".format(_('API Attack Policy'), policy['id_list'])
        op = _("Enable") if policy['enable'] else _("Disable")
        pending_operation_log(request, op, op_content, info=policy)
    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)
    return api_ok_response({'ids':req_body.ids})

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Read", "read")
def get_attack_detect_export(request):
    """
    @check_rest_api_permission: API_Read
    导出攻击策略
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    with get_abd_zk_lock():
        api_attack_policy = ApiAttackPolicyList()
        custom_policies = filter_whitelist_invalid_ref(api_attack_policy.get_custom_policy_list())
        
    pending_operation_log(request, _("Export"), _('API Attack Policy'))
    return api_file_response(json.dumps({'custom_policies': custom_policies}), ApiAttackPolicyList.EXPORT_FILE_NAME)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Batch_Import", "write")
def post_attack_detect_imports(request):
    """
    @check_rest_api_permission: API_Batch_Import
    导入攻击策略
    """
    try:
        req_body = request.body
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    pending_operation_log(request, _('Import'), _('API Attack Policies'))

    f = request.FILES['rawdata']
    file_size = f.size
    if file_size <= 0:
        return api_error_response(RC_BAD_PARAMETERS, _('API Attack Policies file should not be empty'))

    file_content = f.read()

    try:
        policy_dict = json.loads(file_content, encoding='utf-8')
    except Exception as e:
        return api_error_response(RC_BAD_PARAMETERS,'{}'.format(_('API Attack Policies file format is incorrect.')))

    policy_list = policy_dict.get('custom_policies', None)
    if policy_list is None:
        return api_error_response(RC_BAD_PARAMETERS, _('API Attack Policies file format is incorrect'))

    api_attack_policy = ApiAttackPolicyList()
    res, error_msg, duplicate_count, ignore_count, ignore_msg, new_policy_list = api_attack_policy.validate_importing_custom_policy_list(policy_list)

    if duplicate_count > 0:
        remove_pending_operation_log(request)
        tmp_tmp_file = '{}.tmp'.format(TMP_IMPORT_ATTACK_POLICY_FILE)
        file_lock = FileLock('/tmp/.attack_policy.lock')
        file_content = json.dumps({'custom_policies': new_policy_list, "ignore_count": ignore_count, "ignore_msg":ignore_msg}, encoding='utf-8')
        if file_lock.aquire_no_wait():
            with open(tmp_tmp_file, 'w') as f:
                f.write(file_content)
            file_lock.release()
        else:
            return api_error_response(RC_BAD_PARAMETERS, _('Another User is importing, import again after a minute'))

        os.rename(tmp_tmp_file, TMP_IMPORT_ATTACK_POLICY_FILE)
        md5 = hashlib.md5(file_content).hexdigest()

        return api_ok_response({'action': 'ask',
            'duplicate_count': duplicate_count,
            'md5': md5,
            'save_success': False,
        })

    if not res:
        return api_error_response(RC_INTERNAL_ERROR, error_msg)

    replace_count, append_count = api_attack_policy.import_custom_policy_list(new_policy_list)

    data =  {'save_success': True, 'replace_count': replace_count, 'append_count': append_count, 'ignore_count': ignore_count, 'ignore_msg': ignore_msg[0] if ignore_msg else ""}

    return api_ok_response(data)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_attack_detect_imports_confirm(request):
    """
    @check_rest_api_permission: API_Setting
    导入攻击策略-确认操作
    """
    try:
        req_body = ClassField("PostAttackDetectImportsConfirmRequest", PostAttackDetectImportsConfirmRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    if req_body.operate not in ['append', 'replace']:
        return api_error_response(RC_BAD_PARAMETERS, _('Invalid Request Parameter'))

    if not os.path.exists(TMP_IMPORT_ATTACK_POLICY_FILE):
        return api_error_response(RC_BAD_PARAMETERS, _('API Attack Policies temporary file does not exist'))

    with open(TMP_IMPORT_ATTACK_POLICY_FILE, 'r') as f:
        content = f.read()
        local_md5 = hashlib.md5(content).hexdigest()
        if local_md5 != req_body.md5:
            return api_error_response(RC_BAD_PARAMETERS, _('The imported file is changed, please import again'))

    policy_dict = json.loads(content, encoding='utf-8')

    try:
        custom_policy_list = policy_dict['custom_policies']
        old_ignore_count = policy_dict['ignore_count']
        old_ignore_msg = policy_dict['ignore_msg']
    except:
        return api_error_response(RC_INTERNAL_ERROR, _('API Attack Policies temporary file is destroyed.'))

    api_attack_policy = ApiAttackPolicyList()
    res, error_msg, duplicate_count, ignore_count, ignore_msg, new_policy_list = api_attack_policy.validate_importing_custom_policy_list(
        custom_policy_list)

    if (not res) and (duplicate_count == 0):
        return api_error_response(RC_INTERNAL_ERROR, error_msg)

    replace_count, append_count = api_attack_policy.import_custom_policy_list(new_policy_list, req_body.operate)

    pending_operation_log(request, _('Import'), _('API Attack Policies'))

    return api_ok_response({'save_success': True, 'replace_count': replace_count, 'append_count': append_count, 'ignore_count': old_ignore_count+ignore_count, 'ignore_msg': old_ignore_msg[0] if old_ignore_msg else ""})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_attacks_settings_waf_global_whitelist(request):

    try:
        req_body = ClassField("PostAttacksSettingsWafGlobalWhitelistRequest", PostAttacksSettingsWafGlobalWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    strategy  = req_body.to_json()
    from web_admin.Conf_Waf import GlobalWafWhitelist
    global_whitelist = GlobalWafWhitelist()
    result = global_whitelist.add_whitelist_with_abd(request,strategy)
    if not result.get('save_success'):
       return  api_error_response(RC_INTERNAL_ERROR,result.get('error_msg'))

    return api_ok_response(result)
