# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import <PERSON><PERSON>ield, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.api_info import ApiInfo
from abd_utils.schemas.api_info_filter import ApiInfoFilter
from abd_utils.schemas.delete_apis_request import DeleteApisRequest
from abd_utils.schemas.patch_assets_apis_request import PatchAssetsApisRequest
from abd_utils.schemas.post_add_api_request import PostAddApiRequest
from abd_utils.schemas.post_assets_apis_move_to_merge_whitelist_request import PostAssetsApisMoveToMergeWhitelistRequest
from abd_utils.schemas.post_assets_check_api_ref_request import PostAssetsCheckApiRefRequest
from abd_utils.schemas.put_update_api_request import PutUpdateApiRequest

#### 自动导入结束，不要在前面插入代码

import json
import copy
import logging
import time

from django.utils.translation import ugettext as _
from datetime import datetime
from playhouse.shortcuts import model_to_dict

from api_base.base_api import pending_api_batch_operation_log
from abd_utils.repositories.assets import BATCH_DELETE_MAX_NUM
from abd_utils.repositories.assets.api import ApiRepository, risk_level_to_option
from abd_utils.repositories.assets.api_cache import ApiCacheRepository
from abd_utils.repositories.defects.scanner import ScannerRepository
from abd_utils.utils.func_base import gen_api_hash_id, update_zk_trigger, get_bar_chart_data_by_column_names, ErrMsgException, SCANNER_LEVEL_NUM_TO_OPTION
from abd_utils.utils.func_base import gen_api_endpoint
from abd_utils.utils.func_pii import get_pii_info_by_pos, get_pii_type_config_dict
from abd_utils.utils.func_risk import get_risk_name_to_level_dict_with_scanner
from api_base.result_code import RC_EXISTS_ALREADY, RC_NOT_EXIST, RC_INTERNAL_ERROR, RC_BAD_PARAMETERS, RC_REFERENCED, RC_APP_OFFLINE, RC_EXCEED_MAX_LIMIT, RC_NO_BUSINESS_TYPE
from abd_utils.utils.func_api_relationship import SubpathApiFinder, convert_to_subpath_finder_api_info, parse_check_result, check_api_conflict
from abd_utils.utils.func_api_relationship import CHECK_PASS, DUPLICATE_FOUND, SUBPATH_FOUND, PARAM_CONFLICT_FOUND, get_check_conflict_target_apis
from abd_utils.models.api_info_model import ApiInfoModel
from abd_utils.repositories.utils import gen_filter_condition,get_int,get_str,get_array, sorted_by_level, get_bool

from abd_utils.utils.func_ref import check_used_when_user_op, remove_ref_api
from abd_utils.summary_update import update_summary
from abd_utils.utils import func_api, func_app_site
from abd_utils.utils.func_industry_data_level import convert_pii_type_to_industry_data_tags,get_industry_levels
from abd_utils.utils.func_database import Database, ChangeRole
from abd_utils.repositories.attacks.summary import AttackSummaryRepository
from abd_utils.validation_utils import api_validate_path, validate_split_params

from asp_utils.utils import dict_diff
from asp_utils.utils import ugettext
from web_admin.Conf_Base import BaseConf

from abd_utils.models.api_params_policies_model import ApiParamsPoliciesModel
from abd_utils.utils.func_api_flowlearn_vaild import api_flowlearn_param_validate 
from abd_utils.utils.func_datetime_convert import get_start_end_time_by_time_filter, merge_echarts_format_result
from abd_utils.utils.func_industry_data_level import get_api_industry_data_level_mapping_tags
from abd_utils.data_views.api_list_cache import AssetListSummaryCache7dModel
from abd_utils.services.asset.asset_service import AssetService
from abd_utils.utils.func_api_tag import BusinessTypeSettings
from abd_utils.utils.func_api_merge_whitelist import ApiMergeWhitelistSettings, pop_unused_fileds

from api_base.api_const_define import CREATED_BY_MANUAL

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def delete_apis(request):
    """
    根据 API-ID 删除多个 API
    """
    try:
        req_body = ClassField("DeleteApisRequest", DeleteApisRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        body_json = req_body.to_json()
        filter_json = body_json['filter']
        delete_ids = body_json['ids']
        repo = ApiRepository()
    
        # TODO: search referenced API
        try:

            # do batch delete
            if not delete_ids:
                condition = gen_filter_condition(filter_json)
                assets, count = ApiCacheRepository().get_api_cache_lists_for_7day(condition, 1, BATCH_DELETE_MAX_NUM)
                delete_ids = [api['id'] for api in assets]
            
            ref_apis, ref_apps = check_used_when_user_op(delete_ids)
            if ref_apis:
                return api_ok_response({'code':RC_REFERENCED, 'message':'Found referenced API in delete id list!', 'ref_apis':ref_apis})
    
            repo.delete_by_ids(delete_ids, need_delete_null_app=True)
      
            update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])

            detail = _("Deleted") + _("{} APIs").format(len(delete_ids))
            pending_api_batch_operation_log(request, _("Delete"), detail, delete_ids, "API_ID")
            return api_ok_response({})
    
        except Exception as e:
            return api_error_response(RC_INTERNAL_ERROR, e.message)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_add_api(request):
    """
    添加API二次确认

    返回值定义：
    RC_OK: 表示成功, data 是完整的新 API 值，使用返回值替换前端内容
    RC_EXISTS_ALREADY：同名的 api 已经存在，添加失败
    """
    try:
        req_body = ClassField("PostAddApiRequest", PostAddApiRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        with ChangeRole(request.user.username):
            req_data = req_body.to_json()
            need_merge = req_data['need_merge']
            delete_ids = req_data['ids']
            api_info = req_data['api_info']

            api_repo = ApiRepository()
            app_site = api_repo.get_app_site_by_id(api_info['app_site_id']).dicts().get()

            try:
                api_validate_path(api_info.get('api_path'), app_site['path_prefix'])
            except ErrMsgException as e:
                return api_error_response(RC_BAD_PARAMETERS, e.message)
            except Exception as e:
                return api_error_response(RC_BAD_PARAMETERS, '{}: endpoint'.format(_('Invalid parameter')))
            
            try:
                # 新建API，参数拆分开关关闭，无论前端传什么都不保存
                api_info['split_params'] = [] if not api_info['split_param_enabled'] else api_info['split_params']
                validate_split_params(api_info['split_params'])
            except Exception as e:
                return api_error_response(RC_BAD_PARAMETERS, e.message)
        
            api_info['created_by'] = CREATED_BY_MANUAL
            api_info['endpoint'] = gen_api_endpoint(api_info)
            api_id = gen_api_hash_id(ApiInfo(api_info))
            if api_repo.is_exists(api_id):
                return api_error_response(RC_EXISTS_ALREADY, _("API already exists!"))

            if need_merge:
                ref_apis, ref_apps = check_used_when_user_op(delete_ids)
                if ref_apis:
                    return api_ok_response({'code':RC_REFERENCED, 'message':'Found referenced API in delete id list!', 'ref_apis':ref_apis})

                with Database.session.atomic():
                    api_repo.delete_by_ids(delete_ids)
                    api_info['api_map'] = delete_ids
                    api_info['visited_time'] = int(time.time())
                    new_api_obj = api_repo.create(api_id, ApiInfo(api_info))
                    id_map = {delete_id: api_id for delete_id in delete_ids}
                    update_summary(id_map)
            else:
                new_api_obj = api_repo.create(api_id, ApiInfo(api_info))

            update_zk_trigger(func_api.TRIGGER_PATH)

            operation = ugettext('CreateOnly') if not need_merge else ugettext('CreateAndMerge')
            msg = ugettext('Only create API') if not need_merge else ugettext('Create API and merge subpath API')
            dict_info = { 'merged_api_ids': delete_ids if need_merge else [], 'api_info': api_info }
            pending_operation_log(request, operation, msg, info=json.dumps(dict_info, ensure_ascii=False))

            return api_ok_response(parse_check_result(model_to_dict(new_api_obj), [], CHECK_PASS))

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def patch_assets_apis(request):
    """
    批量上下线
    """
    try:
        req_body = ClassField("PatchAssetsApisRequest", PatchAssetsApisRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    
    with ChangeRole(request.user.username):
        body_json = req_body.to_json()
        is_online = body_json['is_online']
        ids = body_json['ids']
        filter_json = body_json['filter']
        
        try:
            repo = ApiRepository()
            condition = gen_filter_condition(filter_json)
            repo.check_ids_count_limit(ids, condition)
            
            off_site_list, onoff_ids = repo.onoff(ids, condition, is_online)
            if off_site_list:
                return api_error_response(RC_APP_OFFLINE, '{},{}'.format(off_site_list[0], len(off_site_list)))
            
            update_zk_trigger(func_api.TRIGGER_PATH)
            
            real_onoff_ids = ids if ids else onoff_ids
            detail = ugettext('Set_Online') if is_online else ugettext('Set_Offline')
            detail = detail + ugettext('{} APIs').format(len(real_onoff_ids))
            pending_api_batch_operation_log(request, _("Modify"), detail, real_onoff_ids, "API_ID")
            return api_ok_response({})
        
        except OverflowError:
            return api_error_response(RC_EXCEED_MAX_LIMIT, "")
        except Exception as e:
            return api_error_response(RC_INTERNAL_ERROR, e.message)


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_access_statistical(request, id):
    """
    根据 ID 查询统计的 API 访问记录

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        start_time = api_validate_int_value(request.GET.get('start_time'), required=False, name="start_time", def_val=1678940170)
        end_time = api_validate_int_value(request.GET.get('end_time'), required=False, name="end_time", def_val=1678940170)
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    return api_ok_response('data')

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def put_update_api(request, id):
    """
    根据 ID 更新 API 信息

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        req_body = ClassField("PutUpdateApiRequest", PutUpdateApiRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        new_api_info = req_body.to_json()
        api_repo = ApiRepository()

        # update: 编辑后保存，update_only: 二次确认仅编辑，update_and_merge: 二次确认编辑且合并
        if new_api_info['operation'] not in ('update', 'update_only', 'update_and_merge'):
            return api_error_response(RC_BAD_PARAMETERS, _('invalid operation type'))
        
        if not api_repo.is_exists(id):
            return api_error_response(RC_NOT_EXIST, _("API does not exist!"))

        api = api_repo.get_api_by_id(id).dicts().get()
        app_site = api_repo.get_app_site_by_id(api['app_site_id']).dicts().get()

        try:
            api_validate_path(new_api_info['api_path'], app_site['path_prefix'])
        except ErrMsgException as e:
            return api_error_response(RC_BAD_PARAMETERS, e.message)
        except Exception as e:
            return api_error_response(RC_BAD_PARAMETERS, '{}: endpoint'.format(_('Invalid parameter')))
        
        try:
            # 编辑，参数拆分开关关闭，不管前端传什么都不改参数配置
            if not new_api_info['split_param_enabled']:
                new_api_info.pop('split_params')
            validate_split_params(new_api_info.get('split_params', []))
        except Exception as e:
            return api_error_response(RC_BAD_PARAMETERS, e.message)
            
        new_api_info['is_custom'] = True

        if new_api_info['is_online'] and not app_site['is_online']:
            return api_error_response(RC_APP_OFFLINE, app_site['name'])
        
        new_api_info['app_site_id'] = api['app_site_id']

        business_name_list = BusinessTypeSettings().get_business_name_list()
        business_types = []
        for item in new_api_info['business_types']:
            business_type = item.strip()
            if business_type in business_name_list:
                business_types.append(business_type)
            else:
                return api_error_response(RC_NO_BUSINESS_TYPE, _('Business type does not exists!'))

        new_api_info['business_types'] = business_types
        
        operation = new_api_info.pop('operation', 'update')
        merge_ids = new_api_info.pop('merge_ids', [])

        # 查找是否存在参数冲突、路径重复、子路径包含的API
        if operation == 'update':
            check_target_apis = get_check_conflict_target_apis(new_api_info, exclude_api_ids=[id])
            check_result = check_api_conflict(new_api_info, check_target_apis)
            if check_result['status'] in (DUPLICATE_FOUND, SUBPATH_FOUND):
                return api_ok_response(check_result)
            
            if check_result['status'] == PARAM_CONFLICT_FOUND:
                sample_api = check_result['first_50_api_infos'][0]
                err_msg = ugettext('API with parameter position {} and parameter name {} already exists in this path, other parameter positions and names is not allowed.').format(sample_api['split_params'][0]['position'], sample_api['split_params'][0]['name'])
                return api_error_response(RC_EXISTS_ALREADY, err_msg)

        new_api_info['endpoint'] = gen_api_endpoint(new_api_info)
        new_api_id = gen_api_hash_id(ApiInfo(new_api_info))
        if new_api_id != api['id']:
            if api_repo.is_exists(new_api_id):
                return api_error_response(RC_EXISTS_ALREADY, _("API already exists!"))
        
        try:
            api_prarams_policies = api_repo.get_api_base_info().where(ApiInfoModel.id == id).group_by(ApiInfoModel.id,
                                                                                                      ApiParamsPoliciesModel.id).dicts().get()
            param_rules = api_prarams_policies.get('api_params_policy', '[]')
            api_flowlearn_param_validate(param_rules, new_api_info.get('endpoint'))
        except Exception as e:
            return api_error_response(RC_BAD_PARAMETERS, '{}: {}'.format(_("API param detection rules error:{}"), e.message))
    
        try:
            need_merge = True if operation == 'update_and_merge' else False
            change_cnt = api_repo.update_api_base_info_by_id(id, new_api_id, new_api_info, need_merge, merge_ids)
            if change_cnt:
                update_zk_trigger(func_api.TRIGGER_PATH)
    
            new_api = api_repo.get_api_by_id(new_api_id).get()
            new_api = model_to_dict(new_api)

            report = api_repo.get_api_info_by_ids_from_cache([new_api_id]).dicts()[0]

            report_info = {'auto_business_types': [],
                           'auto_industry_types': []}
            if report:
                report_info['auto_business_types'] = [report['auto_business_types']] if report['auto_business_types'] else []
                report_info['auto_industry_types'] = list(set(report['industry_types'])) if report['industry_types'] else []

            diff=dict_diff(new_api, api)
            op_content = "{}: {} {} {}".format(_('API list'), app_site['name'], new_api['endpoint'], new_api['method'])
            pending_operation_log(request, _("Modify"), op_content, info=diff)
            response = parse_check_result(new_api, [], CHECK_PASS)
            response['report_info'] = report_info
            return api_ok_response(response)
        except Exception as e:
            return api_error_response(RC_INTERNAL_ERROR, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_access_trends(request, id):
    """
    API访问趋势图

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        time_filter = api_validate_string_value(request.GET.get('time_filter'), required=True, name="time_filter", def_val="from:-7d,to:now")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    def build_ts_dict(ts_dict, timestamp, type, total):
        if ts_dict.get(timestamp, None) is None:
            ts_dict[timestamp] = {type: total}
        else:
            if ts_dict[timestamp].get(type, None) is None:
                ts_dict[timestamp][type] = total
            else:
                ts_dict[timestamp][type] += total
        return ts_dict

    with ChangeRole(request.user.username):
        _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)
        access_trends = ApiRepository().get_access_trend(id, start_time, end_time)
        attack_trends = AttackSummaryRepository().get_asset_trend(id, start_time, end_time, request.user.username)
        column_names = ["timestamp", "access_count", "fail_count", "attack_count", "pii_count"]
    
        ts_dict = {}
        for trend in access_trends:
    
            type = '{}_count'.format(trend['type'])
    
            if type in column_names:
                total = trend['total']
                build_ts_dict(ts_dict, trend['timestamp'], type, total)
    
        for trend in attack_trends:
            type = 'attack_count'
            total = trend['total']
            build_ts_dict(ts_dict, trend['time'], type, total)
    
        records = []
        for timestamp in sorted(ts_dict.keys()):
            record = []
            record.append(timestamp)
            for name in column_names[1:]:
                record.append(ts_dict[timestamp].get(name, 0))
    
            records.append(record)
    
        display_precision, merged_echarts = merge_echarts_format_result(records, time_filter, start_time, end_time)

        ret = {
                'display_precision': display_precision,
                'column_names': column_names,
                'records': merged_echarts
            }

    return api_ok_response(ret)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_top_src_ips(request, id):
    """
    根据 ID 获取 API 的 Top IP 访问

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        time_filter = api_validate_string_value(request.GET.get('time_filter'), required=True, name="time_filter", def_val="from:-7d,to:now")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

    with ChangeRole(request.user.username):
        ip_datas = ApiRepository().get_api_src_ip(id, start_time, end_time)
        column_name_defaults = [('src_ip', ugettext('msg_id_source_ip'), ''), ('total', ugettext('Total Requests'), 0)]
        ret = get_bar_chart_data_by_column_names(ip_datas, column_name_defaults)

    return api_ok_response(ret)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_top_account(request, id):
    """
    获取 API 的 Top-N 访问的账号

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        time_filter = api_validate_string_value(request.GET.get('time_filter'), required=True, name="time_filter", def_val="from:-7d,to:now")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

    with ChangeRole(request.user.username):
        accounts = ApiRepository().get_api_accounts(id, start_time, end_time)
        column_name_defaults = [('account', ugettext('Account'), ''), ('total', ugettext('Total Requests'), 0)]
        ret = get_bar_chart_data_by_column_names(accounts, column_name_defaults)

    return api_ok_response(ret)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_get_apis(request):
    """
    @check_rest_api_permission: API_Read
    获取API列表,过滤条件，空数组或字段为空表示all 如果数组里面出现英文，any（任意） 或者no（无） 会去掉其他项的过滤条件， rank 是一个对象，包含字段名和排序方式.
    is_online的值为online，或offline，空值为全部
    network_types空值为全部，intranet内网、internet外网
    search搜索，match支持fuzzy，full, prefix, suffix
    fuzzy是模糊匹配(包含字符) 
    full 完整匹配（字符串必须完全相等）
    prefix前缀匹配（以什么开头）
    suffix是后缀匹配（以什么结束）
    {
        'app_sites': ["id"],
        'search': {"keyword":"/foo", "match":"full"},
        'api_type': ["RESTFul"],
        'is_online': "online",
        'method': ["GET"],
        'risk_level': [], 
        'business': [], 
        'industries': [],
        'pii_infos': [],
        'defect_name': [],
        'created_by': [],
        'api_match_rule_ids': [],
        'enable_args': 'on',
        'rank':{'field': '', 'type': 'desc'},
    }
    """
    try:
        req_body = ApiInfoFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        condition = gen_filter_condition(req_body.to_json())
        asset_list, count = AssetService().get_list(condition)
    
    return api_ok_response(asset_list, count)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_assets_api_check_before_add(request):
    """
    添加API检查重复和子路径
    """
    try:
        req_body = ApiInfo(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        new_api = req_body.to_json()
        repo = ApiRepository()
        app_site = repo.get_app_site_by_id(new_api['app_site_id']).dicts().get_or_none()

        if not app_site:
            return api_error_response(RC_NOT_EXIST, _('APP does not exists!'))
    
        try:
            api_validate_path(new_api.get('api_path'), app_site['path_prefix'])
        except ErrMsgException as e:
            return api_error_response(RC_BAD_PARAMETERS, e.message)
        except Exception as e:
            return api_error_response(RC_BAD_PARAMETERS, '{}: api_path'.format(_('Invalid parameter')))
        
        try:
            # 新建API，参数拆分开关关闭，无论前端传什么都不保存
            new_api['split_params'] = [] if not new_api['split_param_enabled'] else new_api['split_params']
            validate_split_params(new_api['split_params'])
        except Exception as e:
            return api_error_response(RC_BAD_PARAMETERS, e.message)
        
        check_target_apis = get_check_conflict_target_apis(new_api)
        check_result = check_api_conflict(new_api, check_target_apis)
        if check_result['status'] in (DUPLICATE_FOUND, SUBPATH_FOUND):
            return api_ok_response(check_result)
            
        if check_result['status'] == PARAM_CONFLICT_FOUND:
            sample_api = check_result['first_50_api_infos'][0]
            err_msg = ugettext('API with parameter position {} and parameter name {} already exists in this path, other parameter positions and names is not allowed.').format(sample_api['split_params'][0]['position'], sample_api['split_params'][0]['name'])
            return api_error_response(RC_EXISTS_ALREADY, err_msg)
        
        # 无重复、无子路径直接保存忽略列表API
        new_api['created_by'] = CREATED_BY_MANUAL
        new_api['endpoint'] = gen_api_endpoint(new_api)
        api_id = gen_api_hash_id(ApiInfo(new_api))
        new_api_obj = repo.create(api_id, ApiInfo(new_api))
        
        app_site_name = app_site['name']

        op_content = "{}: {} {} {}".format(_('API list'), app_site_name, new_api['endpoint'], new_api['method'])
        pending_operation_log(request, _("Add"), op_content)

        update_zk_trigger(func_api.TRIGGER_PATH)
        return api_ok_response(parse_check_result(model_to_dict(new_api_obj), [], CHECK_PASS))

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_assets_check_api_ref(request):
    """
    删除、忽略API前检查引用
    """
    try:
        req_body = ClassField("PostAssetsCheckApiRefRequest", PostAssetsCheckApiRefRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    return api_ok_response({})

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_assets_get_list_time_interval(request):
    """
    获取清单查询起止时间时间戳
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        repo = ApiRepository()
        start, end = repo.get_second_for_n_day(7)

    return api_ok_response({'start_time': start, 'end_time': end})

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_assets_apis(request):
    """
    搜索api和应用host
    """
    try:
        search = api_validate_string_value(request.GET.get('search'), required=False, name="search")
        page = api_validate_int_value(request.GET.get('page'), required=False, name="page", def_val=1, min_val=1)
        page_size = api_validate_int_value(request.GET.get('page_size'), required=False, name="page_size", def_val=50, min_val=1, max_val=10000)
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        data = ApiRepository().search_api_and_app_site(search, page, page_size)
        results = []
        for d in data:
            d.pop('endpoint_length')
            d['auto_business_types'] = [d['auto_business_types']] if d['auto_business_types'] else []
            
            results.append(d)
    return api_ok_response(results)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Read", "read")
def get_apis_total(request):
    """
    @check_rest_api_permission: API_Read
    获取默认7天的api总数和忽略api的总数
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    apis_count,ignore_apis_count = (0, 0)
    with ChangeRole(request.user.username):
        apis_count,ignore_apis_count = ApiCacheRepository().get_api_and_ignore_total()
    return api_ok_response({'apis': apis_count, 'ignore_apis': ignore_apis_count})
    

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_assets_apis_move_to_merge_whitelist(request):
    """
    删除并添加至资产合并白名单
    """
    try:
        req_body = ClassField("PostAssetsApisMoveToMergeWhitelistRequest", PostAssetsApisMoveToMergeWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    with ChangeRole(request.user.username):
        json_body = req_body.to_json()
        repo = ApiRepository()
        # TODO: search referenced API
        try:
            
            delete_ids = [json_body['api_id']]
            ref_apis, ref_apps = check_used_when_user_op(delete_ids)
            if ref_apis:
                return api_ok_response({'code':RC_REFERENCED, 'message':'Found referenced API in delete id list!', 'ref_apis':ref_apis})

            new_whitelist_items = [{'domain': domain, 'path': json_body['endpoint']} for domain in json_body['domain']]
            new_whitelist_items = ApiMergeWhitelistSettings().batch_add(new_whitelist_items, auto_merge=True)
            pop_unused_fileds(new_whitelist_items, ['domain_regex', 'path_regex'])
            repo.delete_by_ids(delete_ids, need_delete_null_app=True)
            update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])

            info = {'delete_api_id': delete_ids, 'new_whitelist_items': new_whitelist_items}
            pending_operation_log(request, ugettext("Delete"), ugettext("Delete api and move to merge whitelist."), info=json.dumps(info))
            return api_ok_response(None)
    
        except Exception as e:
            return api_error_response(RC_INTERNAL_ERROR, e.message)
