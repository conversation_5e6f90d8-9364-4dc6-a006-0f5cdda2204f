# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.app_sites_filter import AppSitesFilter
from abd_utils.schemas.post_apis_exports_request import PostApisExportsRequest

#### 自动导入结束，不要在前面插入代码

import signal
import logging
import os


from api_base.base_api import (
    api_error_response,
    remove_pending_operation_log,
    api_ok_response,
    api_file_response,
    api_file_download_response
)
from api_base.base_validator import ValidationException

from abd_utils.utils.func_base import update_zk_trigger, ErrMsgException, EXPORT_WORD_LIMIT, EXPORT_CSV_LIMIT, EXPORT_EXCEL_LIMIT, EXPORT_SWAGGER_LIMIT
from django.utils.translation import ugettext as _, ugettext_noop, ugettext
from api_base.result_code import RC_BAD_PARAMETERS, RC_MORE_INFO
from abd_utils.utils.func_database import ChangeRole
from abd_utils.services.app_site import AppSiteService
from abd_utils.services.asset.asset_service import AssetService


from django.http import HttpResponse
from multiprocessing import Process, Semaphore, Queue
process_semaphore = Semaphore(1)

WORD_EXPORT_TIMEOUT = 600
WORD_WAIT_TIMEOUT = 30
APP_SITES_EXPORT_FILE_NAME = "app_sites.csv"

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Batch_Import", "write")
def post_api_imports(request):
    """
    @check_rest_api_permission: API_Batch_Import
    导入 API
    """
    try:
        req_body = request.body
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        
        try:
            f = request.FILES['rawdata']
            file_size = f.size
            if file_size <= 0:
                return api_error_response(RC_BAD_PARAMETERS, _('API file should not be empty'))
    
            file_content = f.read()
            f.seek(0)
    
            try:
                # utf_8_sig sig即signature，也就是”带有签名的utf-8”，读取带有BOM的”utf-8文件时”会把BOM单独处理，与文本内容隔离开
                if not f.name.endswith('.zip'):
                    file_content.decode('utf_8_sig')
            except:
                return api_error_response(RC_BAD_PARAMETERS, _('API file only support UTF-8 encoding'))
            if f.content_type == "text/csv" or f.name.endswith('.csv'):
                content_type = AssetService.TYPE_CSV
                #new_api_count, updated_api_count, failed_api_count, no_change_api_count = asset_service.import_content(f, AssetService.TYPE_CSV)
            elif f.content_type == "application/json" or f.name.endswith('.json') or f.name.endswith('.zip'):  
                content_type = AssetService.TYPE_SWAGGER
            else:
                return api_error_response(RC_BAD_PARAMETERS, 'Invalid file type: {}'.format(f.content_type))
            

            # 只实例化一次 AssetService
            asset_service = AssetService()
            new_api_count, updated_api_count, failed_api_count, no_change_api_count = asset_service.import_content(f, content_type)
    
            ret = {
                'import_info':
                    {
                        'new_api_count': new_api_count,
                        'updated_api_count': updated_api_count,
                        'failed_api_count': failed_api_count,
                        'no_change_api_count': no_change_api_count
                    }
            }
            pending_operation_log(request, ugettext('Import'), ugettext('Imported API list'))
    
        except ErrMsgException as e:
            logging.exception(e)
            return api_error_response(RC_BAD_PARAMETERS, e.message)
        except Exception as e:
            logging.exception(e)
            return api_error_response(RC_BAD_PARAMETERS, ugettext('import failed, API file format error'))
    
        return api_ok_response(ret)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Batch_Import", "write")
def post_app_sites_imports(request):
    """
    @check_rest_api_permission: API_Batch_Import
    导入应用
    """
    try:
        req_body = request.body
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        try:
            f = request.FILES['rawdata']
            file_size = f.size
            if file_size <= 0:
                return api_error_response(RC_BAD_PARAMETERS, _('app sites file should not be empty'))
    
            file_content = f.read()
            f.seek(0)
            try:
                file_content.decode('utf_8_sig')
            except Exception as e:
                return api_error_response(RC_BAD_PARAMETERS, _('app sites file only support UTF-8 encoding'))
    
            new_app_site, updated_app_site, not_changed_app_site, failed_app_site = AppSiteService().import_content(f, AppSiteService.TYPE_CSV)
            from abd_utils.utils import func_app_site
            update_zk_trigger(func_app_site.TRIGGER_PATH)
            update_zk_trigger(func_app_site.TRIGGER_PG_USER)

            ret = {
                'import_info':
                {
                    'new_app_site_count': new_app_site,
                    'updated_app_site_count': updated_app_site,
                    'no_change_app_site_count': not_changed_app_site,
                    'failed_app_site_count': failed_app_site
                }
            }

            pending_operation_log(request, ugettext("Import"), ugettext('Imported Application list'))

        except ErrMsgException as e:
            return api_error_response(RC_BAD_PARAMETERS, e.message)
        except Exception as e:
            return api_error_response(RC_BAD_PARAMETERS, ugettext("import failed, app sites file format error"))
    
        return api_ok_response(ret)

def export_word_child_process(word_content, file_name):
    from api_base.result_code import RC_EXPORT_TIMOUT, RC_EXPORT_PROCESSING, RC_EXPORT_EXCEPTION
    
    def child_process(queue, word_content, file_name):
        try:
            response = api_file_download_response(word_content, file_name, 'application/octet-stream')
            logging.info('put response to queue')
            queue.put(response)
        except Exception as e:
            logging.exception(str(e))

    def start_process_with_timeout(timeout=600):
        queue = Queue()
        p = Process(target=child_process, args=(queue, word_content, file_name))
        p.daemon = True
        p.start()

        try:
            response = queue.get(timeout=timeout)
        except Exception as e:
            logging.exception(str(e))

        p.join(60)
        if p.is_alive():
            logging.info('process is alive, kill process')
            os.kill(p.pid, signal.SIGKILL)
            p.join()
            response = api_error_response(RC_EXPORT_TIMOUT, ugettext('Export Timeout'))

        return response

    acquired = process_semaphore.acquire(timeout=WORD_WAIT_TIMEOUT)
    if acquired:
        logging.info('acquired semaphore')
        try:
            response = start_process_with_timeout(WORD_EXPORT_TIMEOUT)
        except Exception as e:
            logging.exception(str(e))
            response = api_error_response(RC_EXPORT_EXCEPTION, ugettext('Export Exception'))
        finally:
            process_semaphore.release()
    else:
        logging.info('cannot acquired semaphore')
        response = api_error_response(RC_EXPORT_PROCESSING, ugettext('Export process is in progress, try again later'))

    return response


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_apis_exports(request):
    """
    @check_rest_api_permission: API_Read
    导出过滤后的API列表,
    过滤条件，空数组或字段为空表示all 如果数组里面出现英文，any（任意） 或者no（无） 会去掉其他项的过滤条件
    is_online的值为online，或offline，空值为全部
    network_types空值为全部，intranet内网、internet外网
    {
        'app_sites': ["id"],
        'search': {"keyword":"/foo", "match":"full"},
        'api_type': ["RESTFul"],
        'is_online': "online",
        'method': ["GET"],
        'risk_level': [], 
        'business': [], 
        'industries': [],
        'pii_infos': [],
        'defect_name': [],
        'enable_args': 'on'
    }
    """
    try:
        req_body = ClassField("PostApisExportsRequest", PostApisExportsRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        # 只实例化一次 AssetService
        asset_service = AssetService()
        
        conditions = req_body.to_json()
        file_type = conditions.pop('file_type')
        step = conditions.pop('step')
        if file_type in ['abd_csv', 'abd_excel']:
            type = file_type.split('abd_')[-1]
        else:
            type = file_type
        
        pending_operation_log(request, _("Export"), _('export {} to {} file.').format(_('API list'), type))
        if file_type == AssetService.TYPE_WORD:
            # check api count
            if step == 1:
                api_count = asset_service.get_api_list_count(conditions)
                if api_count > EXPORT_WORD_LIMIT:
                    remove_pending_operation_log(request)
                    return api_error_response(RC_MORE_INFO, _('The number of items in the current list after '
                                                              'filtering is greater than {export_limit} items!, continue to '
                                                              'export {export_limit} items or cancel?').format(
                        export_limit=EXPORT_WORD_LIMIT))

            word_content = asset_service.get_export_content(conditions, AssetService.TYPE_WORD, EXPORT_WORD_LIMIT)
            return export_word_child_process(word_content, asset_service.export_word_file_name)
        

        elif file_type in [AssetService.TYPE_CSV, AssetService.TYPE_SWAGGER, AssetService.TYPE_EXCEL]:
            if step == 1:
                api_count = asset_service.get_api_list_count(conditions)
                if api_count > EXPORT_CSV_LIMIT:
                    remove_pending_operation_log(request)
                    return api_error_response(RC_MORE_INFO, _('The number of items in the current list after '
                                                              'filtering is greater than {export_limit} items!, continue to '
                                                              'export {export_limit} items or cancel?').format(
                        export_limit=EXPORT_CSV_LIMIT))
            
            if file_type == AssetService.TYPE_CSV:
                content = asset_service.get_export_content(conditions, AssetService.TYPE_CSV, EXPORT_CSV_LIMIT)
                return api_file_response(content, asset_service.export_csv_file_name)
            elif file_type == AssetService.TYPE_EXCEL:
                content = asset_service.get_export_content(conditions, AssetService.TYPE_EXCEL, EXPORT_EXCEL_LIMIT)
                return api_file_response(content, asset_service.export_excel_file_name)
            else:
                content = asset_service.get_export_content(conditions, AssetService.TYPE_SWAGGER, EXPORT_SWAGGER_LIMIT)
                return api_file_response(content, asset_service.export_swagger_file_name)

        else:
            return api_error_response(RC_BAD_PARAMETERS, 'Invalid file type: {}'.format(file_type))


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_export_app_sites(request):
    """
    @check_rest_api_permission: API_Read
    导出过滤后的应用列表
    """
    try:
        req_body = AppSitesFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        
        contents = AppSiteService().get_export_content(req_body.to_json(), AppSiteService.TYPE_CSV)
        pending_operation_log(request, _("Export"), _('export {} to {} file.').format(_('AppSite list'), 'csv'))
        return api_file_response(contents, APP_SITES_EXPORT_FILE_NAME)
