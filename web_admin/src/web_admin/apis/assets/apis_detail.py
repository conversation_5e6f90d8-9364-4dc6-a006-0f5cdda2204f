# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.api_params_detail_info import ApiParamsDetailInfo
from abd_utils.schemas.patch_api_parameters_verification_request import PatchApiParametersVerificationRequest

#### 自动导入结束，不要在前面插入代码

import copy
import time
import logging
from django.utils.translation import ugettext, ugettext_noop

from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.models.api_params_policies_model import ApiParamsPoliciesModel
from abd_utils.repositories.utils import get_array
from abd_utils.utils.func_sample import get_samples, get_first_sample
from api_base.result_code import RC_INTERNAL_ERROR, RC_NOT_EXIST, RC_BAD_PARAMETERS
from abd_utils.repositories.defects.defect import DefectRepository
from api_base.api_const_define import API_EVENT_PROCESSED_STATUS
from abd_utils.repositories.piis.pii import PiiRepository
from abd_utils.repositories.assets.apis_detail import create_or_update_api_params_policy, api_validate_forbidden_or_required_headers
from abd_utils.utils.func_pii import get_pii_type_config_dict, get_pii_whitelist_api_app_ids
from abd_utils.utils.func_base import ErrMsgException
from module_ctrls.ubbv2.ubb_util import ubb_invalid_bool
from abd_utils.utils.func_api_flowlearn_vaild import covert_dict_to_str_value,covert_str_rule_to_dict,api_flowlearn_param_validate
from abd_utils.utils.func_base import update_zk_trigger
from abd_utils.utils import func_api
from abd_utils.utils.func_risk import get_defect_kinds, get_risk_name_level_dict
from abd_utils.models.api_info_model import ApiInfoModel
from abd_utils.utils.func_database import ChangeRole
from abd_utils.utils.func_datetime_convert import get_start_end_time_by_time_filter
from abd_utils.repositories.attacks.summary import AttackSummaryRepository
from asp_utils.utils import ugettext
from web_admin.view_lib import botgate_user_to_sf_user

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_detail_statistical(request, id):
    """
    根据API ID查询请求总数，失败请求数，攻击事件数，敏感信息数

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        time_filter = api_validate_string_value(request.GET.get('time_filter'), required=True, name="time_filter", def_val="from:-7d,to:now")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)

    web_username = request.user.username

    with ChangeRole(request.user.username):
        type_stats = ApiRepository().get_statistical(id, start_time, end_time)
        ret = {'pii_count': 0, 'attack_count': 0, 'fail_count': 0, 'access_count': 0}
        for stat in type_stats:
            type = '{}_count'.format(stat['type'])
            if type in ret.keys():
                ret[type] = stat['total']

        attack_count = AttackSummaryRepository().get_api_attack_event_total(id, 'any', start_time, end_time, web_username)
        ret['attack_count'] = attack_count

    return api_ok_response(ret)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Read", "read")
def get_api_samples(request, id):
    """
    @check_rest_api_permission: API_Read
    获取 API 样例

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        defects = api_validate_array_value(request.GET.getlist('defects'), required=False, name="defects")
        piis = api_validate_array_value(request.GET.getlist('piis'), required=False, name="piis")
        bots = api_validate_array_value(request.GET.getlist('bots'), required=False, name="bots")
        industries = api_validate_array_value(request.GET.getlist('industries'), required=False, name="industries")
        page = api_validate_int_value(request.GET.get('page'), required=False, name="page", def_val=1, min_val=1)
        page_size = api_validate_int_value(request.GET.get('page_size'), required=False, name="page_size", def_val=50, min_val=1, max_val=10000)
        time_filter = api_validate_string_value(request.GET.get('time_filter'), required=False, name="time_filter")
        day = api_validate_int_value(request.GET.get('day'), required=False, name="day", def_val=7, min_val=7, max_val=30)
        is_first = api_validate_bool_value(request.GET.get('is_first'), required=False, name="is_first")
        first_uuid = api_validate_int_value(request.GET.get('first_uuid'), required=False, name="first_uuid")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    api_repo = ApiRepository()
    defect_repo = DefectRepository()
    if not api_repo.is_exists(id):
        return api_error_response(RC_NOT_EXIST, ugettext('API does not exist!'))
    ignore_defects = defect_repo.get_defects_by_condition({'defect_status': 3}).dicts()[:]
    conditions = {
        'id': id, 
        'defects': defects,
        'piis': piis,
        'bots': bots,
        'industries': industries,
        'time_filter': time_filter, 
        'page': page,
        'page_size': page_size, 
        'ignore_defects': ignore_defects,
        'day': day,
        'request_uuid': first_uuid
    }

    if is_first:
        ret, total_count, samples = get_first_sample(botgate_user_to_sf_user(request.user.username), **conditions)
    else:
        ret, total_count, samples = get_samples(botgate_user_to_sf_user(request.user.username), **conditions)
    if ret != 0:
        return api_error_response(RC_INTERNAL_ERROR, 'failed to query sample')

    return api_ok_response(samples, total_count)


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_parameters_verification(request, id):
    """
    获取API参数检测

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        api_repo = ApiRepository()
        if not api_repo.is_exists(id):
            return api_error_response(RC_NOT_EXIST, ugettext("API does not exist!"))

        result = api_repo.get_api_base_info().where(ApiInfoModel.id == id).group_by(ApiInfoModel.id,
                                                                                    ApiParamsPoliciesModel.id)

        data = result.dicts().get()
        policies = []
        for rst in result.dicts():
            r = copy.deepcopy(rst)
            policies_old = r.get('api_params_policy', '[]')
            policies = covert_str_rule_to_dict(policies_old)

        ret = {"api_params_policies": {}}
        ret["api_params_policies"]["enable_args"] = data["enable_args"]
        ret["api_params_policies"]["api_max_query_args_cnt"] = data["api_max_query_args_cnt"]
        ret["api_params_policies"]["api_max_body_args_cnt"] = data["api_max_body_args_cnt"]
        ret["api_params_policies"]["api_max_body_size"] = data["api_max_body_size"]
        ret["api_params_policies"]["forbidden_headers"] = get_array(data["forbidden_headers"])
        ret["api_params_policies"]["required_headers"] = get_array(data["required_headers"])
        ret["api_params_policies"]["api_params_rules"] = policies

    return api_ok_response(ret)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def put_api_parameters_verification(request, id):
    """
    修改 API 参数检测配置
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        req_body = ApiParamsDetailInfo(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        try:

            api_info = ApiRepository().get_ref_api_info_by_id(id)
            if not api_info:
                return api_error_response(RC_NOT_EXIST, ugettext("API does not exist!"))

            req_body_json = req_body.to_json()

            # 参数合规检测配置
            para_policy = req_body_json['api_params_policies']
            api_validate_forbidden_or_required_headers(para_policy['forbidden_headers'], 'forbidden_headers')
            api_validate_forbidden_or_required_headers(para_policy['required_headers'], 'required_headers')
            para_policy.pop('api_id')
            para_policy.pop('id')

            # 参数自学习配置
            rules_list = para_policy['api_params_rules']
            # if len(rules_list) > API_PARAMS_POLICY_NUM_LIMIT:
            #     raise Exception('api_params_policy is more than limit')

            para_policy['api_params_rules'] = covert_dict_to_str_value(rules_list)
            api_flowlearn_param_validate(para_policy['api_params_rules'], api_info.get('endpoint'))
            _, diff = create_or_update_api_params_policy(id, para_policy)
            args = {'updated_time': int(time.time())}
            ApiInfoModel.update(**args).where(ApiInfoModel.id == id).execute()

            op_content = "{}: {} {} {}".format(ugettext('Parameter Detection'),
                                               api_info.get('app_site_name'),
                                               api_info.get('endpoint'),
                                               api_info.get('method'))
            if diff:
                pending_operation_log(request, ugettext("Modify"), op_content, info=diff)
            else:
                pending_operation_log(request, ugettext("Add"), op_content, info=diff)

            update_zk_trigger(func_api.TRIGGER_PATH)
        except ErrMsgException as e:
            return api_error_response(RC_BAD_PARAMETERS, e.message)
        except Exception as e:
            logging.exception("Failed to save parameters verification config,Error:{}".format(e))
            return api_error_response(RC_INTERNAL_ERROR, ugettext("Save failed"))

    return api_ok_response('ok')


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def patch_api_parameters_verification(request, id):
    """
    开启和关闭 API 参数检测配置
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        req_body = ClassField("PatchApiParametersVerificationRequest", PatchApiParametersVerificationRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        try:
            api_repo = ApiRepository()
            if not api_repo.is_exists(id):
                return api_error_response(RC_NOT_EXIST, ugettext("API does not exist!"))
    
            enable = req_body.to_json()['enable']
            ubb_invalid_bool(enable)
    
            param_data = ApiParamsPoliciesModel.get_or_none(api_id=id)
            if param_data:
                ApiParamsPoliciesModel.update(enable_args=enable).where(ApiParamsPoliciesModel.api_id == id).execute()
            else:
                policy = {
                    'enable_args': enable,
                    "required_headers": [],
                    "forbidden_headers": [],
                    "api_max_body_args_cnt": -1,
                    "api_max_body_size": -1,
                    "api_max_query_args_cnt": -1,
                    "api_params_rules": "[]"
                }
                ApiParamsPoliciesModel.create(api_id=id, **policy).save()
            args = {'updated_time': int(time.time())}
            ApiInfoModel.update(**args).where(ApiInfoModel.id == id).execute()
            update_zk_trigger(func_api.TRIGGER_PATH)

            new_api = api_repo.get_api_by_id(id).get()
            op_content = "{}: {} {} {}".format(ugettext('Parameter Detection'),
                                               new_api['name'],
                                               new_api['endpoint'],
                                               new_api['method'])
            if enable:
                pending_operation_log(request, ugettext("Enable"), op_content)
            else:
                pending_operation_log(request, ugettext("Disable"), op_content)

        except Exception as e:
            logging.exception("Failed to save enabled parameters verification,Error:{}".format(e))
            return api_error_response(RC_INTERNAL_ERROR, ugettext("Save failed"))
        return api_ok_response('ok')

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_api_detail_basic_info(request, id):
    """
    根据 ID 查询 缺陷列表， 近7天敏感信息， 近7天攻击事件

    错误代码:
    RC_NOT_EXIST：如果 API-ID 不存在
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        api_query =  ApiInfoModel.select(ApiInfoModel.app_site_id).where(ApiInfoModel.id == id).dicts()[:]
        if len(api_query) == 0:
            return api_error_response(RC_NOT_EXIST, ugettext("API does not exist!"))
        app_site_id = api_query[0]['app_site_id']
        defects = DefectRepository().get_api_pending_defects(id, API_EVENT_PROCESSED_STATUS)
        attacks = ApiRepository().get_attacks(id)
        piis = PiiRepository().get_api_piis(id)
        defect_type_kind = get_defect_kinds()
        for d in defects:
            d['kind'] = defect_type_kind.get(d['name'].encode('utf8'), ugettext('Unknown'))
        
        ret = {'defects': defects, 'attacks': attacks, 'piis': piis}
        return api_ok_response(ret)