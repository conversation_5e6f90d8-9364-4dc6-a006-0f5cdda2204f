# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.business_type import BusinessType
from abd_utils.schemas.delete_assets_settings_business_request import DeleteAssetsSettingsBusinessRequest
from abd_utils.schemas.delete_assets_settings_business_whitelist_request import DeleteAssetsSettingsBusinessWhitelistRequest
from abd_utils.schemas.life_cycle import LifeCycle
from abd_utils.schemas.patch_assets_settings_exclude_error_response_request import PatchAssetsSettingsExcludeErrorResponseRequest
from abd_utils.schemas.post_assets_setting_automated_cleanup_request import PostAssetsSettingAutomatedCleanupRequest
from abd_utils.schemas.post_assets_settings_api_merge_whitelist_request import PostAssetsSettingsApiMergeWhitelistRequest
from abd_utils.schemas.post_assets_settings_business_whitelist_request import PostAssetsSettingsBusinessWhitelistRequest
from abd_utils.schemas.post_assets_settings_exclude_error_response_request import PostAssetsSettingsExcludeErrorResponseRequest
from abd_utils.schemas.post_period_sample_conf_request import PostPeriodSampleConfRequest
from abd_utils.schemas.put_assets_setting_automated_cleanup_request import PutAssetsSettingAutomatedCleanupRequest
from abd_utils.schemas.put_assets_settings_api_merge_whitelist_request import PutAssetsSettingsApiMergeWhitelistRequest
from abd_utils.schemas.put_assets_settings_exclude_error_response_request import PutAssetsSettingsExcludeErrorResponseRequest
from abd_utils.schemas.put_period_sample_conf_request import PutPeriodSampleConfRequest

#### 自动导入结束，不要在前面插入代码

import json
import logging
from collections import OrderedDict

from django.utils.translation import ugettext
from asp_utils.utils import list_diff, dict_diff
from web_admin.Conf_Base import BaseConf
from abd_utils.conf_api import ApiConf
from abd_utils.utils.func_attack_policy import get_api_attack_policy_ref_used_tag
from abd_utils.utils.func_api_tag import get_auto_tag_names, PATH_TO_API_ENABLE_AUTO_TAG, \
    PATH_TO_API_MANUAL_TAG_LIST,match_validate_business_name
from abd_utils.utils.func_api_tag import BusinessTypeSettings, BUSINESS_TYPE_RULE_KEY_MAP, \
    BUSINESS_TYPE_RULE_KEY_FROND_END_LIST
from abd_utils.default_auto_tag import DEFAULT_BUSINESS_TYPES
from abd_utils.repositories.assets.business import BusinessRepository
from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.repositories.utils import get_array
from api_base.result_code import RC_EXISTS_ALREADY, RC_NOT_EXIST, RC_INTERNAL_ERROR, RC_BAD_PARAMETERS
from abd_utils.utils.func_base import ErrMsgException,CONFIG_TYPE_RULE_OPERATION_MAP
from abd_utils.utils.func_sample import save_sample_risk_dict_list, api_validate_sample_collection_risk_list, \
    get_sample_risk_dict_list, PATH_TO_SAMPLE_COLLECTION_PERIOD, DEFAULT_SAMPLE_COLLECTION_PERIOD, \
    update_sample_collect_conf_due_del_manual_business
from abd_utils.utils.func_base import update_zk_trigger
from abd_utils.utils import func_api
from abd_utils.utils.func_database import ChangeRole
from abd_utils.repositories.whitelist_relate import WhitelistRelateApiAppRepository
from abd_utils.utils.func_settings import get_all_auth_scheme_names
from asp_utils.utils import ugettext
from abd_utils.utils.func_api_exclude_error_resp import get_exclude_error_response_settings, get_default_exclude_error_response_settings, \
    set_exclude_error_response_settings, validate_exclude_error_response_settings
from abd_utils.utils.func_api_merge_whitelist import ApiMergeWhitelistSettings, pop_unused_fileds
from abd_utils.schemas.automated_cleanup_settings import AutomatedCleanupSettings
from web_admin.utils import send_http_request_to_abd_master_node

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_settings_business(request):
    """
    @check_rest_api_permission: API_Setting
    获取所有的业务分类
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    business_type_settings = BusinessTypeSettings()
    setting_list = business_type_settings.get_setting_list()
    return api_ok_response(setting_list)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_assets_settings_business(request):
    """
    @check_rest_api_permission: API_Setting
    添加自定义业务分类
    """
    try:
        req_body = BusinessType(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        business_type = req_body.to_json()
        business_type_settings = BusinessTypeSettings()
        business_type_setting = business_type_settings.add(business_type)
        op_content = "{}: {} - {}".format(ugettext('Business Type'), business_type_setting['id'], business_type_setting['name'])
        pending_operation_log(request, ugettext("Add"), op_content)
        return api_ok_response(business_type_setting)

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def put_assets_settings_business(request):
    """
    @check_rest_api_permission: API_Setting
    修改业务分类
    """
    try:
        req_body = BusinessType(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        business_type = req_body.to_json()
        business_type_settings = BusinessTypeSettings()
        index, old_setting = business_type_settings.get_setting_by_id(business_type['id'])
        business_type_setting, is_update_api_info = business_type_settings.modify(business_type)

        if is_update_api_info:
            update_zk_trigger(func_api.TRIGGER_PATH)

        op_content = "{}: {} - {}".format(ugettext('Business Type'), business_type_setting['id'], business_type_setting['name'])
        diff = dict_diff(old_setting, business_type_setting)
        pending_operation_log(request, ugettext("Modify"), op_content, info=diff)
        return api_ok_response(business_type_setting)

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_settings_business_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    获取已设置的业务分类白名单
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    
    with ChangeRole(request.user.username):
        business_white_list = ApiConf().get_api_business_white_list()
        if len(business_white_list) == 0:
            return api_ok_response([])
        
        api_infos, _ = ApiRepository().get_ref_api_info_by_ids(business_white_list)
        results = []
        # use add config rank.
        for api_id in business_white_list:
            for q in api_infos:
                if api_id == q['id']:
                    q['auto_business_types'] = [q['auto_business_types']] if q['auto_business_types'] else []
                    results.append(q)
        return api_ok_response(results)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_assets_settings_business_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    添加业务分类白名单
    """
    try:
        req_body = ClassField("PostAssetsSettingsBusinessWhitelistRequest", PostAssetsSettingsBusinessWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        with ChangeRole(request.user.username):
            from abd_utils.utils.func_api_tag import MAX_AUTO_TAG_WHITE_LIST_COUNT
            if len(req_body.api_ids) > MAX_AUTO_TAG_WHITE_LIST_COUNT:
                return api_error_response(RC_INTERNAL_ERROR, ugettext("auto business white list at most can specified {} apis".format(MAX_AUTO_TAG_WHITE_LIST_COUNT)))

            _,count = BusinessRepository().get_ref_api_info_by_ids(req_body.api_ids)
            if count != len(req_body.api_ids):
                return api_error_response(RC_NOT_EXIST, ugettext('api not exist'))

            ApiConf().set_api_business_white_list(req_body.api_ids)
            WhitelistRelateApiAppRepository().update_business_whitelist(req_body.api_ids)

            pending_operation_log(request, ugettext("Add"), ugettext('Business Type Whitelist'), info="API IDs: {}".format(req_body.api_ids))

            return api_ok_response({})

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def delete_assets_settings_business_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    删除业务分类白名单
    """
    try:
        req_body = ClassField("DeleteAssetsSettingsBusinessWhitelistRequest", DeleteAssetsSettingsBusinessWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        with ChangeRole(request.user.username):
            business_white_list = ApiConf().get_api_business_white_list()
            ApiConf().set_api_business_white_list([api_id for api_id in business_white_list if api_id != req_body.api_id])
            WhitelistRelateApiAppRepository().remove_business_whitelist_by_api_ids(req_body.api_ids)
            pending_operation_log(request, ugettext("Delete"), ugettext('Business Type Whitelist'), info="API IDs: {}".format(req_body.api_ids))

        return api_ok_response({})

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

def get_manual_tag_api_ref(name):
    rt = []
    api_list,_ = BusinessRepository().get_business_by_names([name])

    for api in api_list:
        rt.append(api.id)

    return rt

def remove_manual_business_ref(name):
    return BusinessRepository().bulk_remove_by_name(name)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def delete_assets_settings_business(request):
    """
    @check_rest_api_permission: API_Setting
    删除业务分类
    """
    try:
        req_body = ClassField("DeleteAssetsSettingsBusinessRequest", DeleteAssetsSettingsBusinessRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        param = req_body.to_json()
        id_list = param['ids']

        business_type_settings = BusinessTypeSettings()
        name_list = business_type_settings.get_business_type_name_list(id_list)

        count = 0
        for name in name_list:
            if not business_type_settings.has_duplication_name(name):

                # TODO: attack policy need redo.
                if get_api_attack_policy_ref_used_tag([name]):
                    return api_error_response(RC_EXISTS_ALREADY, 'Delete business failed: {}, ref by attack_policy'.format(name))

                count += remove_manual_business_ref(name)
                update_sample_collect_conf_due_del_manual_business(name)

        if count > 0:
            update_zk_trigger(func_api.TRIGGER_PATH)

        business_type_settings.delete(id_list)
        id_name_list = []
        for index, id in enumerate(id_list):
            id_name_list.append('{} - {}'.format(id, name_list[index]))
        op_content = "{}: {}".format(ugettext('Business Type'), ','.join(id_name_list))
        pending_operation_log(request, ugettext("Delete"), op_content)

        return api_ok_response({})

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_settings_business_ref(request):
    """
    @check_rest_api_permission: API_Setting
    获取业务分类引用关联信息
    """
    try:
        name = api_validate_string_value(request.GET.get('name'), required=True, name="name", min_length=1, max_length=50)
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # TODO: attack policy need redo
    with ChangeRole(request.user.username):

        business_type_settings = BusinessTypeSettings()
        if not business_type_settings.has_duplication_name(name):
            api_policy_ref_list = get_api_attack_policy_ref_used_tag([name])
            if api_policy_ref_list:
                return api_ok_response({'ref_by': 'api_attack_policy', 'data': api_policy_ref_list})

            # api may remove tag even if api refer
            api_list_ref_list = get_manual_tag_api_ref(name)
            if api_list_ref_list:
                return api_ok_response({'ref_by': 'api_list', 'data': api_list_ref_list})

        return api_ok_response({'ref_by': '', 'data': []})


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_period_sample_conf(request):
    """
    @check_rest_api_permission: API_Setting
    获取接口样例定时采集配置
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    conf = ApiConf()
    period_sample_conf = conf.get_value(PATH_TO_SAMPLE_COLLECTION_PERIOD, DEFAULT_SAMPLE_COLLECTION_PERIOD.copy())

    return api_ok_response(period_sample_conf)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_period_sample_conf(request):
    """
    @check_rest_api_permission: API_Setting
    保存接口样例定时采集配置
    """
    try:
        req_body = ClassField("PostPeriodSampleConfRequest", PostPeriodSampleConfRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        api_conf = ApiConf()
        data = req_body.to_json()
        # pending_operation_log(request, ugettext_noop('Modify'), ugettext_noop('API period sample collection'))
        old_data = api_conf.get_value(PATH_TO_SAMPLE_COLLECTION_PERIOD)
        api_conf.set_value(PATH_TO_SAMPLE_COLLECTION_PERIOD, data)

        info = 'org: {}, new: {}'.format(old_data, data)
        pending_operation_log(request, ugettext("Modify"), ugettext('API period sample collection'), info=info)

        return api_ok_response('ok')

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def put_period_sample_conf(request):
    """
    @check_rest_api_permission: API_Setting
    启用/关闭接口样例定时采集
    """
    try:
        req_body = ClassField("PutPeriodSampleConfRequest", PutPeriodSampleConfRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # pending_operation_log(request, ugettext_noop('Modify'), ugettext_noop('API period sample collection'))

    try:
        api_conf = ApiConf()
        period_sample_conf = api_conf.get_value(PATH_TO_SAMPLE_COLLECTION_PERIOD, DEFAULT_SAMPLE_COLLECTION_PERIOD.copy())
        period_sample_conf['enabled'] = req_body.enabled
        api_conf.set_value(PATH_TO_SAMPLE_COLLECTION_PERIOD, period_sample_conf)
        if req_body.enabled:
            pending_operation_log(request, ugettext('Enable'), ugettext('API period sample collection'))
        else:
            pending_operation_log(request, ugettext('Disable'), ugettext('API period sample collection'))

        return api_ok_response('ok')

    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_full_sample_conf(request):
    """
    @check_rest_api_permission: API_Setting
    获取样例全采集配置
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    conf = get_sample_risk_dict_list()
    return api_ok_response(conf)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_full_sample_conf(request):
    """
    @check_rest_api_permission: API_Setting
    保存样例全采集配置
    """
    try:
        req_body = ListField("PostFullSampleConfRequest", StringField(name="PostFullSampleConfRequest_item")).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # pending_operation_log(request, ugettext_noop('Modify'), ugettext_noop('API risk sample collection'))
    try:
        enabled_data_list = [{'type': data, 'enabled': True} for data in req_body]

        enabled_data_list = api_validate_sample_collection_risk_list(enabled_data_list)
        risk_name = [data['name'] for data in enabled_data_list if data['class'] == 'risk']
        pii_name = [data['name'] for data in enabled_data_list if data['class'] == 'pii']
        business_name = [data['name'] for data in enabled_data_list if data['class'] == 'business_types']

        if len(risk_name) > 30 or len(pii_name) > 30 or len(business_name) > 30:
            return api_error_response(RC_BAD_PARAMETERS, 'exceed max count')

        if ugettext('All') in risk_name and len(risk_name) != 1:
            return api_error_response(RC_BAD_PARAMETERS, 'error risk data')

        if ugettext('All') in pii_name and len(pii_name) != 1:
            return api_error_response(RC_BAD_PARAMETERS, 'error pii data')

        if ugettext('All') in business_name and len(business_name) != 1:
            return api_error_response(RC_BAD_PARAMETERS, 'error business_type data')

        all_data = get_sample_risk_dict_list()
        old_enabled_list = []
        for data in all_data:
            if data['enabled']:
                old_enabled_list.append(data['type'])

        enabled_type_list = [data['type']for data in enabled_data_list]
        diff = list_diff(enabled_type_list, old_enabled_list)
        for data in all_data:
            if data['type'] in enabled_type_list:
                data['enabled'] = True
            else:
                data['enabled'] = False

        save_sample_risk_dict_list(all_data)
        pending_operation_log(request, ugettext("Modify"), ugettext('API risk sample collection'), info=diff)

    except ErrMsgException as e:
        return api_error_response(RC_BAD_PARAMETERS, e.message)
    except Exception as e:
        return api_error_response(RC_BAD_PARAMETERS, '')

    return api_ok_response('ok')


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_assets_settings_rules(request):
    """
    获取规则配置
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    business_type_key_map = OrderedDict()
    for k, value in BUSINESS_TYPE_RULE_KEY_MAP.items():
        if k in BUSINESS_TYPE_RULE_KEY_FROND_END_LIST:
            business_type_key_map[k] = value

    result = {'business': {
        'key_map': business_type_key_map,
        'operation_map': CONFIG_TYPE_RULE_OPERATION_MAP
    }}
    return api_ok_response(result)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_assets_settings_exclude_error_response(request):
    """
    @check_rest_api_permission: API_Setting
    设置排除错误响应开关
    """
    try:
        req_body = ClassField("PostAssetsSettingsExcludeErrorResponseRequest", PostAssetsSettingsExcludeErrorResponseRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        set_exclude_error_response_settings(json_body)

        operation = ugettext('Enable') if json_body['enable'] else ugettext('Disable')
        detail = ugettext('Exclude Error Response')
        pending_operation_log(request, operation, detail)
    except Exception as e:
        return api_error_response(RC_INTERNAL_ERROR, e.message)

    return api_ok_response('ok')

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_settings_exclude_error_response(request):
    """
    @check_rest_api_permission: API_Setting
    获取排除错误响应配置
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    settings = get_exclude_error_response_settings()

    return api_ok_response(settings)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_setting_lifecycle(request):
    """
    @check_rest_api_permission: API_Setting
    获取API生命周期定义
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    lifecycle = ApiConf().get_api_lifecycle()
    return api_ok_response(LifeCycle(lifecycle=lifecycle).to_json())

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_assets_setting_lifecycle(request):
    """
    @check_rest_api_permission: API_Setting
    设置API生命周期定义
    """
    try:
        req_body = LifeCycle(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    ApiConf().set_api_lifecycle(req_body.lifecycle)
    pending_operation_log(request, ugettext("Settings"), ugettext('Setting API lifecycle'), info="{} {}".format(req_body.lifecycle, ugettext("days")))
    return api_ok_response(req_body.to_json())

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def put_assets_settings_exclude_error_response(request):
    """
    修改排除错误响应配置
    """
    try:
        req_body = ClassField("PutAssetsSettingsExcludeErrorResponseRequest", PutAssetsSettingsExcludeErrorResponseRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    
    new_settings = req_body.to_json()
    try:
        validate_exclude_error_response_settings(new_settings)
    except Exception as e:
        logging.exception(e)
        return api_error_response(RC_BAD_PARAMETERS, ugettext('Invalid format.'))

    old_settings = get_exclude_error_response_settings()
    new_settings = set_exclude_error_response_settings(new_settings)
    detail = ugettext('Exclude Error Response')
    diff = json.dumps(dict_diff(new_settings, old_settings), ensure_ascii=False)
    pending_operation_log(request, ugettext("Modify"), detail, info=diff)

    return api_ok_response(new_settings)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def patch_assets_settings_exclude_error_response(request):
    """
    排除错误响应恢复默认
    """
    try:
        req_body = ClassField("PatchAssetsSettingsExcludeErrorResponseRequest", PatchAssetsSettingsExcludeErrorResponseRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    default_settings = get_default_exclude_error_response_settings()

    return api_ok_response({'error_keywords': default_settings['error_keywords']})

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_settings_api_merge_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    获取资产合并白名单
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    whitelist = ApiMergeWhitelistSettings().get().get('whitelist', [])
    pop_unused_fileds(whitelist, ['domain_regex', 'path_regex'])
    return api_ok_response(whitelist)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Setting", "read")
def get_assets_setting_automated_cleanup(request):
    """
    @check_rest_api_permission: API_Setting
    获取API无访问自动清理配置
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    automated_cleanup = ApiConf().get_api_inactive_automated_cleanup()
    return api_ok_response(AutomatedCleanupSettings(automated_cleanup).to_json())

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_assets_settings_api_merge_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    添加资产合并白名单
    """
    try:
        req_body = ClassField("PostAssetsSettingsApiMergeWhitelistRequest", PostAssetsSettingsApiMergeWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        new_whitelist_items = ApiMergeWhitelistSettings().batch_add([{'domain':json_body['domain'], 'path':json_body['path']}])
        pop_unused_fileds(new_whitelist_items, ['domain_regex', 'path_regex'])
        detail = ugettext('API Merge Whitelist')
        pending_operation_log(request, ugettext("Add"), detail, info=json.dumps(new_whitelist_items))
        return api_ok_response(new_whitelist_items[0])
    except Exception as e:
        logging.exception(e)
        return api_error_response(RC_BAD_PARAMETERS, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def post_assets_setting_automated_cleanup(request):
    """
    @check_rest_api_permission: API_Setting
    设置API无访问自动清理周期
    """
    try:
        req_body = ClassField("PostAssetsSettingAutomatedCleanupRequest", PostAssetsSettingAutomatedCleanupRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    zk_conf = ApiConf()
    automated_cleanup = zk_conf.get_api_inactive_automated_cleanup()
    if not automated_cleanup['enable']:
        return api_error_response(RC_INTERNAL_ERROR, ugettext("Automated cleanup of inactive APIs is not enabled in settings."))
    if req_body.cleanup_cycle != automated_cleanup['cleanup_cycle']:
        automated_cleanup['cleanup_cycle'] = req_body.cleanup_cycle
        zk_conf.set_api_inactive_automated_cleanup(automated_cleanup)
        pending_operation_log(request, ugettext("Modify"), ugettext('Auto Remove Unused APIs'), info="{} {}".format(req_body.cleanup_cycle, ugettext("days")))
    
    return api_ok_response(AutomatedCleanupSettings(automated_cleanup).to_json())

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def put_assets_settings_api_merge_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    修改资产合并白名单
    """
    try:
        req_body = ClassField("PutAssetsSettingsApiMergeWhitelistRequest", PutAssetsSettingsApiMergeWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        json_body = req_body.to_json()
        old_whitelilst_item, new_whitelist_item = ApiMergeWhitelistSettings().update(json_body['id'], json_body['domain'], json_body['path'])
        pop_unused_fileds(old_whitelilst_item, ['domain_regex', 'path_regex'])
        pop_unused_fileds(new_whitelist_item, ['domain_regex', 'path_regex'])
        diff = dict_diff(old_whitelilst_item, new_whitelist_item)
        detail = ugettext('API Merge Whitelist')
        pending_operation_log(request, ugettext("Modify"), detail, info=diff)
        return api_ok_response(new_whitelist_item)
    except Exception as e:
        logging.exception(e)
        return api_error_response(RC_BAD_PARAMETERS, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def put_assets_setting_automated_cleanup(request):
    """
    @check_rest_api_permission: API_Setting
    开启API无访问自动清理
    """
    try:
        req_body = ClassField("PutAssetsSettingAutomatedCleanupRequest", PutAssetsSettingAutomatedCleanupRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    zk_conf = ApiConf()
    automated_cleanup = zk_conf.get_api_inactive_automated_cleanup()
    enable = automated_cleanup['enable']
    if req_body.enable != enable:
        automated_cleanup['enable'] = not enable
        zk_conf.set_api_inactive_automated_cleanup(automated_cleanup)
        pending_operation_log(request, ugettext("enabled") if enable else ugettext("disabled"), ugettext('Auto Remove Unused APIs'))
    
    return api_ok_response(AutomatedCleanupSettings(automated_cleanup).to_json())

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def delete_assets_settings_api_merge_whitelist(request):
    """
    @check_rest_api_permission: API_Setting
    删除资产合并白名单
    支持批量删除
    """
    try:
        req_body = ListField("DeleteAssetsSettingsApiMergeWhitelistRequest", IntField(name="DeleteAssetsSettingsApiMergeWhitelistRequest_item")).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        delete_ids = req_body
        delete_items = ApiMergeWhitelistSettings().batch_delete(delete_ids)
        pop_unused_fileds(delete_items, ['domain_regex', 'path_regex'])
        detail = ugettext('API Merge Whitelist')
        pending_operation_log(request, ugettext("Delete"), detail, info=json.dumps(delete_items))
        return api_ok_response(None)
    except Exception as e:
        logging.exception(e)
        return api_error_response(RC_BAD_PARAMETERS, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Setting", "write")
def delete_assets_setting_api_automated_cleanup(request):
    """
    @check_rest_api_permission: API_Setting
    手动立即清理无访问APIs
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    response = send_http_request_to_abd_master_node('POST', '/abd_service/automation_cleanup')
    if response:
        response = json.loads(response)
        if not response.get('err_msg'):
            pending_operation_log(request, ugettext("Remove"), ugettext('Successfully removed unused APIs.'))
            return api_ok_response({})
        else:
            pending_operation_log(request, ugettext("Remove"), ugettext('Failed removed unused APIs.'))
            return api_error_response(RC_INTERNAL_ERROR, response.get('err_msg'))
    else:
        return api_error_response(RC_INTERNAL_ERROR, 'failed to connect abd service')
