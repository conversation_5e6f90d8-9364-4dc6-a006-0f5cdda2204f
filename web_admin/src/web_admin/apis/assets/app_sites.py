# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.app_site_edit import AppSiteEdit
from abd_utils.schemas.app_sites_filter import AppSitesFilter
from abd_utils.schemas.delete_app_sites_request import DeleteAppSitesRequest
from abd_utils.schemas.patch_app_sites_request import PatchAppSitesRequest
from abd_utils.schemas.post_assets_app_site_search_related_domains_request import PostAssetsAppSiteSearchRelatedDomainsRequest
from abd_utils.schemas.post_assets_app_sites_confirm_request import PostAssetsAppSitesConfirmRequest
from abd_utils.schemas.post_ignore_app_sites_request import PostIgnoreAppSitesRequest
from abd_utils.schemas.post_merge_app_sites_request import PostMergeAppSitesRequest
from abd_utils.schemas.put_assets_app_sites_confirm_request import PutAssetsAppSitesConfirmRequest
from abd_utils.schemas.put_assets_app_sites_request import PutAssetsAppSitesRequest

#### 自动导入结束，不要在前面插入代码

import copy
import json
from asp_utils.utils import dict_diff
from playhouse.shortcuts import model_to_dict

from django.utils.translation import ugettext as _
from api_base.base_api import pending_api_batch_operation_log
from api_base.result_code import RC_EXISTS_ALREADY, RC_NOT_EXIST, RC_BAD_PARAMETERS, RC_OK, RC_REFERENCED
from abd_utils.repositories.assets.api import ApiRepository
from abd_utils.repositories.assets.app_site import AppSiteRepository
from abd_utils.repositories.assets import BATCH_UPDATE_MAX_NUM, BATCH_DELETE_MAX_NUM
from abd_utils.models.app_site_model import AppSiteModel
from abd_utils.utils.func_app_site import validate_servers,\
    check_app_sites_inclusion_relationship
from abd_utils.utils.func_base import  gen_app_site_hash_id, update_zk_trigger
from abd_utils.utils.func_database import Database, ChangeRole
from abd_utils.utils.func_ref import check_used_when_remove_app
from abd_utils.utils import func_app_site, func_api, func_ignore
from api_base.lru_cache import clear_global_lru_cache
from api_base.api_const_define import CREATED_BY_MANUAL, APP_EDIT_OPERATION_COVER

from abd_utils.services.app_site import AppSiteService

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def put_api_app_site(request, id):
    """
    更新API应用
    """
    try:
        id = api_validate_string_value(id, required=True, name="id")
        req_body = AppSiteEdit(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    new_app_site = req_body.to_json()
    app_service = AppSiteService()
    # 检查应用id是否存在；应用名称是否已存在；用户名是否在系统内存在；关联域名是否已在其他应用中存在
    validate_result = app_service.validate_edit_app_site_request(new_app_site, operation='edit')
    if validate_result.get('err_code'):
        return api_error_response(validate_result['err_code'], validate_result['err_msg'])

    with ChangeRole(request.user.username):
        # 不带路径前缀才检查应用包含
        if new_app_site.get('path_prefix') == '':
            result = check_app_sites_inclusion_relationship(new_app_site)
            if result['err_code'] != RC_OK:
                return api_error_response(result['err_code'], result['err_msg'])
            if result['contained_apps']:
                return api_ok_response(result['contained_apps'])
          
        old_app_id = id
        old_app_site, new_app_site = app_service.update_app_site_by_id_and_new_app_info(old_app_id, new_app_site)

        details = json.dumps(dict_diff(new_app_site, old_app_site), ensure_ascii=False)
        op_content = "{}: {}".format(_('App'), new_app_site['name'])
        pending_operation_log(request, _("Edit"), op_content, info=details) 
    
        if req_body.username != '___NOT__SPECIFIED___':
            update_zk_trigger(func_app_site.TRIGGER_PG_USER)

        if new_app_site['id'] != old_app_site['id']:
            update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])
        else:
            update_zk_trigger(func_app_site.TRIGGER_PATH)

        clear_global_lru_cache()
    
        return api_ok_response(new_app_site)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Means", "read")
def get_users(request):
    """
    获取用户列表， 管理员返回所有用户。
    操作员返回自己。
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    from web_admin.Conf_Webconsole import WebconsoleConf
    user_list = WebconsoleConf().get_api_users_list(request.user.username)

    return api_ok_response(user_list)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_get_app_sites(request):
    """
    @check_rest_api_permission: API_Read
    获取应用列表
    """
    try:
        req_body = AppSitesFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        count, app_sites = AppSiteService().get_list(req_body.to_json())
    return api_ok_response(app_sites, count)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def delete_app_sites(request):
    """
    删除 API 应用信息
    """
    try:
        req_body = ClassField("DeleteAppSitesRequest", DeleteAppSitesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        deleted_app_ids = req_body.deleted_ids
        repo = AppSiteRepository()

        if len(deleted_app_ids) == 0:
            conditions = req_body.filter.to_json()
            conditions['page_size'] = BATCH_DELETE_MAX_NUM
            app_count, filtered_app_sites = AppSiteService().get_list(conditions)
            if app_count == 0:
                return api_ok_response({})
            deleted_app_ids = [app['id'] for app in filtered_app_sites]
        else:
            if len(deleted_app_ids) > BATCH_DELETE_MAX_NUM:
                return api_error_response(RC_BAD_PARAMETERS, 'The number of deleted app site id over {}'.format(BATCH_DELETE_MAX_NUM))
            
            invalid_app_ids = repo.check_app_sites_exist_by_ids(deleted_app_ids)
            if invalid_app_ids:
                return api_error_response(RC_BAD_PARAMETERS, _('APP does not exists!') + ': {}'.format(json.dumps(invalid_app_ids, ensure_ascii=False)))
        
        ref_apis, ref_apps = check_used_when_remove_app(deleted_app_ids)
        if ref_apis or ref_apps:
            return api_ok_response({'code':RC_REFERENCED, 'message':'Found ref API or APP!', 'ref_apis':ref_apis, 'ref_apps': ref_apps})

        deleted_app_sites, app_count = repo.get_app_site_by_ids(deleted_app_ids)
        deleted_app_names = [app['name'] for app in deleted_app_sites]
        
        detail = _('Deleted') + _('{} API APPs').format(len(deleted_app_names))
        pending_api_batch_operation_log(request, _('Delete'), detail, deleted_app_names, _('App') + _('Name'))

        api_repo = ApiRepository()
        return_api_apps = {}
        for delete_app_site in deleted_app_sites:
            if delete_app_site['path_prefix']:
                main_app = api_repo.get_main_app_site(delete_app_site)
                if main_app is not None and main_app.id not in deleted_app_ids:
                    # 删除前缀应用， 其main应用存在，且当前不被删除时， 归还其API
                    return_api_apps[delete_app_site['id']] = delete_app_site

        deleted_app_ids = [app_id for app_id in deleted_app_ids if app_id not in return_api_apps]
        
        with Database.session.atomic():
            repo.delete_by_app_site_ids(deleted_app_ids)

            for return_api_app in return_api_apps.values():
                api_repo.return_api_for_prefix_app(return_api_app)
                AppSiteModel.delete_by_id(return_api_app['id'])

        update_zk_trigger(func_app_site.TRIGGER_PG_USER)

        update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])

        clear_global_lru_cache()
    
    return api_ok_response({})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def patch_app_sites(request):
    """
    批量上下线
    """
    try:
        req_body = ClassField("PatchAppSitesRequest", PatchAppSitesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        assert(len(req_body.ids) == 1)
        app_id = req_body.ids[0]
        repo = AppSiteRepository()
        if not repo.is_exists(app_id):
            return api_error_response(RC_NOT_EXIST, _('APP does not exists!'))
        
        repo.set_online_status(app_id, req_body.is_online)

        app_site = repo.get_app_site_by_id(app_id)
        if req_body.is_online:
            op_content = "{}{}: {}".format(_('Set_Online'), _('App'), app_site['name'])
        else:
            op_content = "{}{}: {}".format(_('Set_Offline'), _('App'), app_site['name'])
        pending_operation_log(request, _("Modify"), op_content)
    
        update_zk_trigger(func_app_site.TRIGGER_PATH)
    
    return api_ok_response({})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Advance_Means", "write")
def post_app_site(request):
    """
    @check_rest_api_permission: API_Advance_Means
    添加 API 应用
    """
    try:
        req_body = AppSiteEdit(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    new_app_site = req_body.to_json()
    
    validate_result = AppSiteService().validate_edit_app_site_request(new_app_site, operation='create')
    if validate_result.get('err_code'):
        return api_error_response(validate_result['err_code'], validate_result['err_msg'])

    with ChangeRole(request.user.username):
        # 不带路径前缀才检查应用包含
        if new_app_site.get('path_prefix') == '':
            result = check_app_sites_inclusion_relationship(new_app_site)
            if result['err_code'] != RC_OK:
                return api_error_response(result['err_code'], result['err_msg'])
            if result['contained_apps']:
                return api_ok_response(result['contained_apps'])
        
        with Database.session.atomic():
            new_app_id = gen_app_site_hash_id(new_app_site['servers'][0], new_app_site['path_prefix'])
            new_app_site['id'] = new_app_id
            
            new_app_site['created_by'] = CREATED_BY_MANUAL
            app_site_repo = AppSiteRepository()
            if not app_site_repo.is_exists(new_app_id):
                obj = app_site_repo.create(new_app_id, new_app_site)
            else:
                return api_error_response(RC_EXISTS_ALREADY, _("App site is already exists."))
            
            if new_app_site['path_prefix']:
                ApiRepository().assign_api_for_prefix_app(obj)

        details = json.dumps(new_app_site, ensure_ascii=False)
        op_content = "{}: {}".format(_('App'), new_app_site['name'])
        pending_operation_log(request, _("Add"), op_content, info=details)

        if new_app_site['username'] != '___NOT__SPECIFIED___':
            update_zk_trigger(func_app_site.TRIGGER_PG_USER)

        if new_app_site['path_prefix']:
            update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])
        else:
            update_zk_trigger(func_app_site.TRIGGER_PATH)
        
        return api_ok_response(model_to_dict(obj))

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_merge_app_sites(request):
    """
    合并API 应用
    """
    try:
        req_body = ClassField("PostMergeAppSitesRequest", PostMergeAppSitesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):

        app_site = req_body.app_site
        servers = [server.to_json() for server in app_site.servers]
        try:
            validate_servers(servers)
        except:
            return api_bad_parameter_response('servers')
        
        repo = AppSiteRepository()
        invalid_app_ids = repo.validate_app_site_ids(req_body.ids)
        if invalid_app_ids:
            return api_error_response(RC_BAD_PARAMETERS, _('App site does not exist, please refresh app site list and try again.'))

        app_site_id = gen_app_site_hash_id(app_site.servers[0], '')
        app_site.id = app_site_id
        app_site.created_by = CREATED_BY_MANUAL
    
        merged_app_ids = req_body.ids
        assert(len(merged_app_ids) >= 2)
        assert(len(merged_app_ids) <= 50)
    
        if repo.is_name_exists(app_site.name, merged_app_ids):
            return api_error_response(RC_EXISTS_ALREADY, _("App site name already exists"))

        assert(app_site.path_prefix == '')
        check_servers_result = repo.is_servers_and_pathprefix_exists(servers, '', merged_app_ids)
        if check_servers_result:
            return api_error_response(RC_EXISTS_ALREADY, "{}: {}".format(_("Following relate domain already exists in other app site"), ', '.join(check_servers_result)))
        
        delete_app_ids = copy.copy(merged_app_ids)
        if app_site_id in delete_app_ids:
            #新的应用与被merge的id一样时， 这个应用不认为是删除，不检查这个应用的引用
            delete_app_ids.remove(app_site_id) 
    
        ref_apis, ref_apps = check_used_when_remove_app(delete_app_ids)
        if ref_apis or ref_apps:
            return api_ok_response({'code':RC_REFERENCED, 'message':'Found ref API or APP!', 'ref_apis':ref_apis, 'ref_apps': ref_apps})
    
        api_repo = ApiRepository()
        with Database.session.atomic():
            app_site_model = api_repo.merge_app_update_api(app_site.to_json(), merged_app_ids)

        info = 'Merged APP IDs: {}'.format(','.join(merged_app_ids))
        op_content = "{}: {}".format(_('App'), app_site.name)
        pending_operation_log(request, _("Merge"), op_content, info=info)
    
        if app_site.username and app_site.username != '___NOT__SPECIFIED___':
            update_zk_trigger(func_app_site.TRIGGER_PG_USER)
    
        update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])
        
        return api_ok_response(model_to_dict(app_site_model))

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Advance_Means", "write")
def post_ignore_app_sites(request):
    """
    @check_rest_api_permission: API_Advance_Means
    忽略 API 应用
    """
    try:
        req_body = ClassField("PostIgnoreAppSitesRequest", PostIgnoreAppSitesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        assert(len(req_body.ids) == 1)
        app_id = req_body.ids[0]
        repo = AppSiteRepository()
        
        try:
            app_site = AppSiteModel.get_by_id(app_id)
        except AppSiteModel.DoesNotExist:
            return api_error_response(RC_NOT_EXIST, _('APP does not exists!'))
        
        ref_apis, ref_apps = check_used_when_remove_app([app_id])
        if ref_apis or ref_apps:
            return api_ok_response({'code':RC_REFERENCED, 'message':'Found ref API or APP!', 'ref_apis':ref_apis, 'ref_apps': ref_apps})
    
        ignore_api = {'servers': app_site.servers,
                      'api_path': app_site.path_prefix if app_site.path_prefix else '/',
                      'method': 'ANY',
                      'is_included_sub_path': True}
        
        repo = ApiRepository()
        app_site_name = app_site.name
        with Database.session.atomic():
            AppSiteRepository().delete_by_id(app_id)
            try:
                with Database.session.atomic(): 
                    repo.create_ignore_api(ignore_api)
            except: # in case ignore list exist.
                pass

        op_content = "{}: {}".format(_('App'), app_site_name)
        pending_operation_log(request, _("Ignore"), op_content)

        update_zk_trigger([func_app_site.TRIGGER_PATH, func_ignore.TRIGGER_PATH])
    
    return api_ok_response({})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_assets_app_sites_confirm(request):

    try:
        req_body = ClassField("PostAssetsAppSitesConfirmRequest", PostAssetsAppSitesConfirmRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    json_body = req_body.to_json()
    operation = json_body['operation']
    new_app_site = json_body['app_site']
    merge_app_ids = json_body['sub_app_ids']
    new_app_id = gen_app_site_hash_id(new_app_site['servers'][0], new_app_site['path_prefix'])
    new_app_site['id'] = new_app_id
    new_app_site['created_by'] = CREATED_BY_MANUAL
    # 检查应用名称是否已存在；关联域名是否已在其他应用中存在；用户名是否在系统内存在；待合并应用是否存在、是否被引用
    validate_result = AppSiteService().validate_edit_app_site_request(json_body, operation)
    if validate_result.get('err_code'):
        return api_error_response(validate_result['err_code'], validate_result['err_msg'])
        
    with ChangeRole(request.user.username):
        # 仅新建
        if operation == 'create_only':
            app_site_repo = AppSiteRepository()
            if not app_site_repo.is_exists(new_app_id):
                obj = app_site_repo.create(new_app_id, new_app_site)
            else:
                return api_error_response(RC_EXISTS_ALREADY, _("App site is already exists."))

            op_content = "{}: {}".format(_('App'), new_app_site['name'])
            pending_operation_log(request, _("CreateOnly"), op_content, info=json.dumps(new_app_site, ensure_ascii=False))

            if new_app_site['username'] != '___NOT__SPECIFIED___':
                update_zk_trigger(func_app_site.TRIGGER_PG_USER)
    
            update_zk_trigger(func_app_site.TRIGGER_PATH)
        
            return api_ok_response(model_to_dict(obj))

        # 新建合并
        elif operation == 'create_and_merge':
            # limit numbers of merged app ?
            merged_app_names = AppSiteRepository().get_app_site_names_by_ids(merge_app_ids)
            
            with Database.session.atomic():
                app_site_model = ApiRepository().merge_app_update_api(new_app_site, merge_app_ids, operation='create_and_merge')

            details = json.dumps({'app_site': new_app_site, 'merged_app_names': merged_app_names}, ensure_ascii=False)
            op_content = "{}: {}".format(_('Added_one_application_while_merging'), new_app_site['name'])
            pending_operation_log(request, _("CreateAndMerge"), op_content, info=details)
    
            if new_app_site['username'] and new_app_site['username'] != '___NOT__SPECIFIED___':
                update_zk_trigger(func_app_site.TRIGGER_PG_USER)
    
            update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])
        
            return api_ok_response(model_to_dict(app_site_model))

    return api_ok_response('data')

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def put_assets_app_sites_confirm(request):

    try:
        req_body = ClassField("PutAssetsAppSitesConfirmRequest", PutAssetsAppSitesConfirmRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    json_body = req_body.to_json()
    new_app_site = json_body['app_site']
    old_app_id = new_app_site['id']
    new_app_id = gen_app_site_hash_id(new_app_site['servers'][0], new_app_site['path_prefix'])
    operation = json_body['operation']

    # 检查被编辑的应用是否存在；是否重名；关联域名是否已在其他应用中存在；用户名是否在系统内存在；待合并应用是否存在、是否被引用
    app_service = AppSiteService()
    validate_result = app_service.validate_edit_app_site_request(json_body, operation)
    if validate_result.get('err_code'):
        return api_error_response(validate_result['err_code'], validate_result['err_msg'])

    with ChangeRole(request.user.username):
        # 仅编辑
        if operation == 'edit_only':

            old_app_site, new_app_site = app_service.update_app_site_by_id_and_new_app_info(old_app_id, new_app_site)

            details = json.dumps(dict_diff(new_app_site, old_app_site), ensure_ascii=False)
            op_content = "{}: {}".format(_('App'), new_app_site['name'])
            pending_operation_log(request, _("EditOnly"), op_content, info=details)
    
            if new_app_site['username'] != '___NOT__SPECIFIED___':
                update_zk_trigger(func_app_site.TRIGGER_PG_USER)

            if new_app_id != old_app_id:
                update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])
            else:
                update_zk_trigger(func_app_site.TRIGGER_PATH)

            clear_global_lru_cache()
    
            return api_ok_response(old_app_site)
        
        # 编辑合并
        elif operation == 'edit_and_merge':
            repo = AppSiteRepository()
            merge_app_ids = json_body['sub_app_ids']
            merged_app_names = repo.get_app_site_names_by_ids(merge_app_ids)
            new_app_site['created_by'] = repo.get_app_site_by_id(old_app_id).get('created_by')
            new_app_site['id'] = new_app_id

            # limit numbers of merged app ?
            old_app_site = repo.get_app_site_by_id(old_app_id)
            with Database.session.atomic():
                merge_app_ids.append(old_app_id)
                app_site_model = ApiRepository().merge_app_update_api(new_app_site, merge_app_ids, operation)

            details = json.dumps({'app_site_diff': dict_diff(new_app_site, old_app_site), 'merged_app_names': merged_app_names}, ensure_ascii=False)
            op_content = "{}: {}".format(_('Edited_one_application_while_merging'), new_app_site['name'])
            pending_operation_log(request, _("EditAndMerge"), op_content, info=details)
    
            if new_app_site['username'] and new_app_site['username'] != '___NOT__SPECIFIED___':
                update_zk_trigger(func_app_site.TRIGGER_PG_USER)
    
            update_zk_trigger([func_api.TRIGGER_PATH, func_app_site.TRIGGER_PATH])
        
            return api_ok_response(model_to_dict(app_site_model))

    return api_ok_response({})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_assets_app_site_search_related_domains(request):
    """
    @check_rest_api_permission: API_Read
    查询关联域名
    """
    try:
        req_body = ClassField("PostAssetsAppSiteSearchRelatedDomainsRequest", PostAssetsAppSiteSearchRelatedDomainsRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    with ChangeRole(request.user.username):
        app_repo = AppSiteRepository()
        related_domains = app_repo.search_related_domains_by_keyword(req_body.search)
        return api_ok_response(related_domains)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("API_Read", "read")
def get_app_groups(request):
    """
    @check_rest_api_permission: API_Read
    获取应用分组列表
    """
    try:
        name = api_validate_string_value(request.GET.get('name'), required=False, name="name")
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    with ChangeRole(request.user.username):
        data, count = AppSiteService().get_app_group_names_by_keyword(keyword=name)
        return api_ok_response(data, count)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Advance_Means", "write")
def put_assets_app_sites(request):
    """
    @check_rest_api_permission: API_Advance_Means
    批量编辑应用
    """
    try:
        req_body = ClassField("PutAssetsAppSitesRequest", PutAssetsAppSitesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    def _build_setting_messages(settings):
        """
        根据 settings 构建用户可读的操作描述列表
        返回: (message_list, triggers)
        """
        SETTING_LABEL_MAP = {
            'username': _('msg_id_username'),
            'responsible': _('Responsible Person'),
            'department': _('Department'),
            'app_group_name': _('Application Group Name')
        }

        messages = {}
    
        for field, config in settings.items():
            value = config.get('value', '').strip()
            operation = config.get('operation', '').upper()

            # 生成字段描述
            label = SETTING_LABEL_MAP[field]
            if operation == APP_EDIT_OPERATION_COVER:
                if not value:
                    continue

                value_display = '["{}"]'.format(value)
                action_desc = _('{} {}').format(label, value_display)
                operation_text = _('Overwrote')
            else:
                action_desc = label
                operation_text = _("Clear ")

            # 添加操作前缀（如 "覆盖"、"清除"）
            messages[field] = "{}{}".format(operation_text, action_desc)

        return messages

    def _build_update_conditions(app_site_ids, conditions):
        """
        根据 app_site_ids 和 filter 构建最终更新条件, app_site_ids为空时，表示全选，并限制最大更新数
        返回: 条件字典，或 None（如果app_site_ids超出限制）
        """

        if app_site_ids:
            if len(app_site_ids) > BATCH_UPDATE_MAX_NUM:
                raise Exception(_("Invalid Request Parameter"))
            conditions['app_sites'] = app_site_ids
        else:
            conditions['page_size'] = BATCH_UPDATE_MAX_NUM

        return conditions


    def _format_operation_message(updated_count, setting_messages):
        """
        格式化操作日志消息
        """
        setting_str = ",".join(setting_messages)
        return _("{} Application(s): {}").format(updated_count, setting_str)

    # 解析请求数据
    json_data = req_body.to_json() if req_body else {}
    settings = json_data.get('settings', {})
    filters = json_data.get('filter', {})
    app_site_ids = json_data.get('app_site_ids', [])

    # 权限上下文
    try:
        with ChangeRole(request.user.username):
            # 处理设置项并生成操作描述
            setting_messages = _build_setting_messages(settings)
            
            # 构建最终过滤条件
            final_conditions = _build_update_conditions(app_site_ids, filters)

            # 执行批量更新
            updated_count = AppSiteService().bluk_update_app_site(final_conditions, settings)

            # 触发配置更新
            trigger_paths = [func_app_site.TRIGGER_PATH]
            if 'username' in setting_messages.keys():
                trigger_paths.append(func_app_site.TRIGGER_PG_USER)
            update_zk_trigger(trigger_paths)

            # 记录操作日志
            operation_msg = _format_operation_message(updated_count, setting_messages.values())

            return api_ok_response({}, updated_count)
    except Exception as e:
        operation_msg = _(e.message)
        return api_error_response(RC_BAD_PARAMETERS, operation_msg)
    finally:
        pending_operation_log(request, _("Modify"), operation_msg, info=json_data)
            