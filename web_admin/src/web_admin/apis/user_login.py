# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import <PERSON><PERSON>ield, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.patch_user_password_request import PatchUserPasswordRequest
from abd_utils.schemas.post_captcha_validation_request import PostCaptchaValidationRequest
from abd_utils.schemas.post_is_remote_user_request import PostIsRemoteUserRequest
from abd_utils.schemas.post_super_token_request import PostSuperTokenRequest
from abd_utils.schemas.post_user_login_request import PostUserLoginRequest
from abd_utils.schemas.post_user_password_request import PostUserPasswordRequest
from abd_utils.schemas.put_user_password_request import PutUserPasswordRequest

#### 自动导入结束，不要在前面插入代码

import datetime
import time
import copy
import logging
import hashlib
import uptime

from django.contrib.auth.hashers import check_password
from web_admin.decorators import get_rbac_conf
from django.contrib.auth.hashers import make_password
from django.contrib.auth import login as auth_login, authenticate, logout as auth_logout, get_user_model, update_session_auth_hash
from web_admin.operation_log import operation_log, get_client_ip
from django.utils.translation import ugettext as _, ugettext_lazy, ugettext_noop
from generic.utils import kick_user_session
from web_admin.Conf_Webconsole import WebconsoleConf
from web_admin.Conf_Base import BaseConf
from web_admin.view_lib import get_current_is_debug, get_current_is_prod_pkg, get_current_git_commit_no
from captcha.models import CaptchaStore
from captcha.helpers import captcha_image_url
from api_base.result_code import RC_OK, RC_WRONG_2_FACTORS, RC_WRONG_CAPTCHAR, RC_PASSWORD_EXPIRED, RC_LOG_IN_FAILED,\
      RC_INVALID_TOKEN, RC_NOT_EXIST_USERS, RC_NO_PERMISSION, RC_BAD_PARAMETERS, RC_LOCKED_ACCOUNT, RC_LOCKED_IP, RC_INVALID_PASWD,\
      RC_DISABLE_ADMIN
from web_admin import settings
from web_admin.views_phoenix import clean_phoenix_cookies
from web_admin.views import check_super_token
from asp_conf_ctrl import ConfDb, GetConfDb
from login_utils import get_login_asp_conf, get_safe_now, captcha_validation, two_factors_validation
from user_session import get_session_info

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_user_login(request):
    """
    @check_rest_api_permission: __guest_function__
    用户登陆请求
    """
    try:
        req_body = ClassField("PostUserLoginRequest", PostUserLoginRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    conf = WebconsoleConf()

    # is_mod_pwd_redirect = request.POST.get(
    #     'modify_pwd_success', request.GET.get('modify_pwd_success', '')
    # ) == '1'

    username = req_body.user_name
    password = req_body.password
    code = RC_OK
    message = ''

    if conf.is_2fa_enabled():
        # 开启双因素认证
        if not two_factors_validation(username, req_body.token_2_factors):
            code = RC_WRONG_2_FACTORS
            message = _('Incorrect authentication code.')
            return api_error_response(code, message)

    if conf.is_captcha_enabled(get_current_is_prod_pkg()):
        # 比较验证码
        if not captcha_validation(req_body.captcha_key, req_body.captcha_text):
            code = RC_WRONG_CAPTCHAR
            message = _('Invalid confirmation code.')
            return api_error_response(code, message)

    client_ip = get_client_ip(request)
    if conf.is_ip_locked(client_ip):
        code = RC_LOCKED_IP
        message = ugettext_noop('IP is locked')
        return api_error_response(code, message)

    # if conf.is_user_locked(username):
    #     code = RC_LOCKED_ACCOUNT
    #     message = ugettext_noop('Account is locked')
    #     operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
    #                     'msg': ugettext_noop('Account is locked')}, user=username)
    #     return api_error_response(code, message)
    
    # 禁用 锁定无法登录
    if conf.is_user_exist(username) and conf.judge_user_state(username=username) != 2:
        code = RC_LOCKED_ACCOUNT
        message = ugettext_noop('Account is locked')
        return api_error_response(code, message)
    
    if username == 'admin' and conf.is_admin_user_disabled():
        code = RC_DISABLE_ADMIN
        message = _('admin user is disabled.')
        return api_error_response(code, message)
    
    user = authenticate(username=username, password=password)
    if user is None:
        if conf.is_user_exist(username):
            if not conf.is_user_locked(username):
                conf.set_fail_time(username)
                if WebconsoleConf().is_user_locked(username):
                    operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
                        'msg': ugettext_noop('Account is locked')}, user=username)
                else:
                    operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
                        'msg': ugettext_noop('Failed to log in to WebConsole')}, user=username)
                        
        if conf.add_ip_failed_info(client_ip, username, password):
            operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
                'msg': ugettext_noop('IP is locked')}, user=username)
            return api_error_response(RC_LOCKED_IP, _('IP is locked'))

        return api_error_response(RC_LOG_IN_FAILED, _('Failed to log in, wrong username or password.'))

    if conf.get_password_period(username) == 0:
        token = hashlib.sha512(conf.get_user_password(username)+str(int(uptime.uptime())/300)).hexdigest()
        return api_error_response(RC_PASSWORD_EXPIRED, token)
    
    conf.remove_ip_failed_info(client_ip)
    # Okay, security check complete. Log the user in.
    auth_login(request, user)

    # Reset Expire
    request.session.set_expiry(None)

    operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '0', {
        'msg': ugettext_noop('Successfully logged in to WebConsole')}, user=request.POST.get('username'))

    # 将其他人踢下线
    if conf.is_kicked_enabled():
        kick_user_session(request)
        request.session['_kicked'] = 0
        request.session.save()

    # conf.clean_fail_time(username)
    conf.reset_last_login_time(username)

    if request.session.get("last_activity") is None:
        request.session['last_activity'] = time.time()
    # role = WebconsoleConf().get_user_role(username)

    asp_conf = get_login_asp_conf(username)
    if not asp_conf['acl']:
        return api_error_response(RC_NO_PERMISSION, _('You have no permission to access.'))
    
    session_info = get_session_info(request)

    return api_ok_response({'code': RC_OK, 'asp_conf':asp_conf, 'session_info': session_info})

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("__guest_function__", "read")
def get_user_login(request):
    """
    @check_rest_api_permission: __guest_function__
    获取用户 login 需要配置、验证码等信息
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    conf = WebconsoleConf()
    is_captcha_on = conf.is_captcha_enabled(get_current_is_prod_pkg())

    data = {}
    if is_captcha_on:
        CaptchaStore.remove_expired()
        key = CaptchaStore.generate_key()
        data = {
            'captcha_key': key,
            'captcha_image_url': captcha_image_url(key)
        }

    if conf.is_2fa_enabled():
        # 开启双因素认证
        data['is_2_factors_token_on'] = True

    license_info = conf.get_conf().get_license_info()
    data['is_abd_license_enable'] = license_info.is_module_effect(license_info.DM_API_MONITOR)

    smart_conf = GetConfDb()
    confdb = smart_conf.db()
    data['has_abd_role'] = confdb.has_abd_server()

    return api_ok_response(data)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_captcha_validation(request):
    """
    @check_rest_api_permission: __guest_function__
    校验 capture 是否通过
    """
    try:
        req_body = ClassField("PostCaptchaValidationRequest", PostCaptchaValidationRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    CaptchaStore.remove_expired()

    key = req_body.captcha_key
    text = req_body.captcha_text.lower()
    try:
        CaptchaStore.objects.get(response=text, hashkey=key, expiration__gt=get_safe_now())
        return api_ok_response(RC_OK, '')
    except CaptchaStore.DoesNotExist:
        return api_ok_response(RC_WRONG_CAPTCHAR, '')

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("__guest_function__", "read")
def get_user_logout(request):
    """
    @check_rest_api_permission: __guest_function__
    用户登出
    """
    #### 自动生成结束，不要手动修改以上的代码 ####
    operation_log(request, ugettext_noop('Access'), ugettext_noop('Logout'),
                      '0', {'msg': ugettext_noop('Successfully logged out from WebConsole')})
    auth_logout(request)

    resp = api_ok_response({'code': RC_OK })

    return clean_phoenix_cookies(resp)

def sha1_str(passwd):
    hexadecimal = hashlib.sha1(passwd).hexdigest()
    sha_password = str()
    for i in range(0, len(hexadecimal)):
        if i % 2 == 0:
            sha_password = sha_password + str(int(hexadecimal[i:i + 2], 16)) + ','
    return str(sha_password)[0:-1]

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def patch_user_password(request):
    """
    @check_rest_api_permission: __guest_function__
    Change password by token without login
    """
    try:
        req_body = ClassField("PatchUserPasswordRequest", PatchUserPasswordRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    token = req_body.token
    conf = ConfDb()
    license_info = conf.get_license_info()
    result = check_super_token(token, license_info, 1)
    if result != 1:
        return api_ok_response({'code': RC_INVALID_TOKEN, "message":ugettext_noop("Your token is invalid.")})

    username = req_body.username
    password = req_body.password
    active_users = get_user_model()._default_manager.filter(
            username__iexact=username, is_active=True)

    err = old_password_check(username, password)
    if err:
        return api_ok_response({'code': RC_INVALID_PASWD, "message": err})
    for user in active_users:
        if WebconsoleConf().get_user_auth_mode(user) == 'remote':
            return api_ok_response({'code': RC_NOT_EXIST_USERS, "message": _("Modifying remote account is not allowed.")})

        if user.has_usable_password():
            user.set_password(password)
            user.save()
            WebconsoleConf().set_user_password(username, make_password(password))
            kick_user_session(request, flush=True)
            return api_ok_response({'code': RC_OK })
        
    return api_ok_response({'code': RC_NOT_EXIST_USERS, "message":_("Username doesn't exist.")})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("Password_Change", "write")
def put_user_password(request):
    """
    @check_rest_api_permission: Password_Change
    Change own password with login
    """
    try:
        req_body = ClassField("PutUserPasswordRequest", PutUserPasswordRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        username = request.user.username
        old_password = req_body.old_password
        new_password = req_body.new_password
        conf = WebconsoleConf()
        old_passwd_hash = conf.get_user_password(username)
        if not check_password(old_password, old_passwd_hash):
            raise ValueError(_(' Wrong password! Please try again.'))

        err = old_password_check(username, new_password)
        if err:
            raise ValueError(err)
        request.user.set_password(new_password)
        request.user.save()
        update_session_auth_hash(request, request.user)
        conf.set_user_password(request.user.username,
                                  make_password(new_password))
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '0',
                      {'msg': ugettext_noop('Change user password: '), 'spliceMsg': request.user.username}, user=request.user.username)
        kick_user_session(request, flush=True)
    except ValueError as e:
        return api_error_response(RC_BAD_PARAMETERS, e.message)

    return api_ok_response({'code': RC_OK })

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_super_token(request):
    """
    @check_rest_api_permission: __guest_function__
    验证是否有权限重置密码
    """
    try:
        req_body = ClassField("PostSuperTokenRequest", PostSuperTokenRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    token = req_body.token
    conf = ConfDb()
    license_info = conf.get_license_info()
    result = check_super_token(token, license_info)

    return api_ok_response({'code': RC_OK if result == 1 else RC_INVALID_TOKEN})


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_user_password(request):
    """
    @check_rest_api_permission: __guest_function__
    Change expired password by token without login
    """
    try:
        req_body = ClassField("PostUserPasswordRequest", PostUserPasswordRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    try:
        username = req_body.username
        new_password = req_body.password
        token = req_body.token
        conf = WebconsoleConf()
        old_passwd_hash = conf.get_user_password(username)
        now = int(uptime.uptime()) / 300
        if token != hashlib.sha512(old_passwd_hash+str(now)).hexdigest() and token != hashlib.sha512(old_passwd_hash+str(now-1)).hexdigest():
            raise ValueError(_('Password token has expired.'))
        err = old_password_check(username, new_password)
        if err:
            raise ValueError(err)
        conf.set_user_password(username, make_password(new_password))
        kick_user_session(request, flush=True)
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '0',
                      {'msg': ugettext_noop('Change user password: '), 'spliceMsg': username}, user=username)

    except ValueError as e:
        return api_error_response(RC_BAD_PARAMETERS, e.message)

    return api_ok_response({'code': RC_OK })

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_is_remote_user(request):
    """
    @check_rest_api_permission: __guest_function__
    校验 是不是远程认证用户通过
    """
    try:
        req_body = ClassField("PostIsRemoteUserRequest", PostIsRemoteUserRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    username = req_body.username

    is_remote_user = False
    login_status = ''
    if WebconsoleConf().get_user_auth_mode(username) == 'remote':
        is_remote_user = True
        conf = BaseConf()
        status = conf.get_value('_private/login/%s/status' % username)
        if status:
            login_status = _(status.encode('utf-8'))

    return api_ok_response({'is_remote_user': is_remote_user, 'login_status': login_status})

def old_password_check(username, newpassword):
    conf = WebconsoleConf()
    cur_passwd = conf.get_user_password(username)
    password_config = conf.get_password_config(True)
    history_check = password_config['history_check']
    if history_check == 0:
        return
    old_passwds = conf.get_user_history_password(username)
    old_passwds.append(cur_passwd)
    for passwd_hash in old_passwds[-history_check:]:
        if check_password(newpassword, passwd_hash):
            return _('New password should be different from the lastest {0} passwords that have been used before').format(history_check)
    return
