# -*- coding:utf-8 -*-
import copy
import hashlib
import json
import logging
import os.path
import random

from django.utils.translation import ugettext as _

from asp_conf_ctrl import ConfDb
from asp_utils.utils import get_language
from date_time_converter import get_start_end_time_by_time_filter
from asp_utils.sailfish_util import sailfish_restapi_query_task_list, QueryStatus, sailfish_restapi_cancel_query


RISK_LEVEL_TRANS_MAP = {
        "high": _("High Risk"),
        "medium": _("Medium Risk"),
        "low": _("Low Risk"),
        "none": _("No Risk"),
}


class RiskConf:
    _default_risks = None
    _sub_risks = None
    _risk_specifics = None

    def __new__(cls, *args, **kwargs):
        if not hasattr(cls, '_instance'):
            cls._instance = super().__new__(cls)
            cls.__init_default_risks()
            cls.__init_sub_risks()
        return cls._instance

    def __init__(self):
        super().__init__(self)

    @classmethod
    def __init_default_risks(cls):
        _name_key = "en_name" if get_language() == "en" else "cn_name"
        path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "../../../default_risk.json")
        path = os.path.abspath(path)
        with open(path, 'r') as f:
            cls._default_risks = json.load(f)
            for risk in cls._default_risks:
                risk["name"] = risk.get(_name_key)
                risk.pop("cn_name")
                risk.pop("en_name")

                child = risk.get("child", [])
                for c in child:
                    c["name"] = c.get(_name_key)
                    c.pop("cn_name")
                    c.pop("en_name")

    @classmethod
    def __init_sub_risks(cls):
        cls._sub_risks = {}
        for risk in cls._default_risks:
            for child in risk.get("child", [{}]):
                cls._sub_risks[child.get("id")] = {
                    "name": child.get("name", ""),
                    "level": child.get("level", ""),
                    "parent_id": risk.get("id", ""),
                    "parent_name": risk.get("name", ""),
                }

        local_risk = cls.get_local_risks()
        if not local_risk:
            # 说明没修改过setting，直接返回
            return cls._sub_risks
        for level, sub_ids in cls.get_local_risks().items():
            [cls._sub_risks[sub_id].update({"level": level}) for sub_id in sub_ids]

    @classmethod
    def __init_risk_specifics(cls):
        _name_key = "en_name" if get_language() == "en" else "cn_name"
        path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "../../../risk_detailed_specifics.json")
        path = os.path.abspath(path)
        with open(path, 'r') as f:
            cls._risk_specifics = json.load(f)
            for risk in cls._risk_specifics:
                risk["name"] = risk.get(_name_key)
                risk.pop("cn_name")
                risk.pop("en_name")

    @classmethod
    def get_sub_risks(cls, update_level=True):
        """

        :param update_level:
        :return: {
            "url_token_stolen": {
                "name": "URL 令牌盗用",
                "level": "high",
                "parent_id": "token_stolen",
                "parent_name": "令牌盗用",
            }
        }
        """
        if cls._sub_risks is None:
            cls.__init_default_risks()
            cls.__init_sub_risks()

        if update_level:
            local_risk = cls.get_local_risks()
            if local_risk:
                for level, sub_ids in cls.get_local_risks().items():
                    [cls._sub_risks[sub_id].update({"level": level}) for sub_id in sub_ids if sub_id in cls._sub_risks]
            # 风险等级可能被重置了，还是需要更新level
            else:
                for risk in cls._default_risks:
                    [cls._sub_risks[child.get("id")].update({"level": child.get("level", "")}) for child in risk.get("child", [{}]) if child.get("id") in cls._sub_risks]
        return cls._sub_risks

    @staticmethod
    def get_local_risks():
        """

        :return: {
            "high": ["token_loss", "token_reused", "cookie_token_tamper", "corejs_crack"],
            "medium": ["token_expired", "spider_with_js"],
            "low": ["spider_without_js", "url_token_stolen", "prevent_scanner"],
            "none": ["xxx", "yyy"],
            }

        """
        setting = ConfDb().get_value("risk_level/global", {})
        return setting

    @classmethod
    def get_default_risk(cls):
        """

        :return: [{
            "id": "token_stolen, "cn_name": "令牌盗用", "en_name": "Token Stolen",
            "child: [{"id": "url_token_stolen, "cn_name": "URL 令牌盗用", "en_name": "URL Token Stolen", "level": "high"}]
            }]
        """
        if cls._default_risks is None:
            cls.__init_default_risks()
            cls.__init_sub_risks()
        return copy.deepcopy(cls._default_risks)

    @classmethod
    def get_risk_specifics(cls):
        """

        :return: [{
            "id": "token_stolen, "name": "令牌盗用",
            "child: [{"name": "无效的base64字符串", "detail": "Cookie令牌被篡改，系统可能正遭受破解攻击", "suggestion": "开启站点拦截模式并根据需求配置拦截规则"}]
            }]
        """
        if cls._risk_specifics is None:
            cls.__init_risk_specifics()
        return cls._risk_specifics


    @classmethod
    def get_risk_settings(cls):
        """

        :return:  [{
            "id": "token_stolen, "name": "令牌盗用",
            "child: [{"id": "url_token_stolen, "name": "URL 令牌盗用", "level": "high"}]
            }]
        """
        try:
            default_risk_json = cls.get_default_risk()
            local_risk_json = cls.get_local_risks()
            if not local_risk_json:
                return default_risk_json
            high_levels = local_risk_json.get("high", [])
            medium_levels = local_risk_json.get("medium", [])
            low_levels = local_risk_json.get("low", [])
            none_levels = local_risk_json.get("none", [])

            for values in default_risk_json:
                child = values.get("child", [])
                for c in child:
                    c_id = c.get("id")
                    if c_id in high_levels:
                        c["level"] = "high"
                    elif c_id in medium_levels:
                        c["level"] = "medium"
                    elif c_id in low_levels:
                        c["level"] = "low"
                    elif c_id in none_levels:
                        c["level"] = "none"

            return default_risk_json
        except Exception as e:
            logging.getLogger(__name__).exception(e)
            return []


def get_risk_subtype_desc_by_id(risk_subtype_id):
    risk_settings = RiskConf.get_sub_risks()
    for k, v in risk_settings.items():
        if k == risk_subtype_id:
            return v
    return {}


def escape_sql_str(s):
    return s.replace("\\", "\\\\").replace("'", "\\'")


def get_query_filter(req_body_json):
    site = escape_sql_str(req_body_json.get('site', '').strip())
    src_ip = escape_sql_str(req_body_json.get('src_ip', '').strip())
    src_fingerprint = escape_sql_str(req_body_json.get('src_fingerprint', '').strip())
    path = escape_sql_str(req_body_json.get('path', '').strip())
    location = escape_sql_str(req_body_json.get('location', '').strip())

    if site == "":
        site_cond = 1
    else:
        site_cond = "`site`='{0}'".format(site)

    if src_ip == "":
        src_ip_cond = 1
    else:
        src_ip_cond = "`src_ip`='{0}'".format(src_ip)

    if path == "":
        path_cond = 1
    else:
        path_cond = "`path`='{0}'".format(path)

    if src_fingerprint == "":
        src_fingerprint_cond = 1
    else:
        src_fingerprint_cond = "(`fingerprint_browser_2`='{0}' OR `mobile_device_fingerprint` = '{0}')".format(src_fingerprint)

    if location == "":
        location_cond = 1
    else:
        if location == "内网":
            location_cond = "`is_local_ip`=1"
        elif location == '未知':
            location_cond = "empty(city) AND empty(province) AND empty(country) AND is_local_ip =0"
        else:
            location_cond = "city='{location}' OR province='{location}' OR country='{location}'".format(location=location)

    return """({site_cond} )
                AND ({src_ip_cond})
                AND ({path_cond})
                AND ({src_fingerprint})
                AND ({location})""".format(site_cond=site_cond, src_ip_cond=src_ip_cond, path_cond=path_cond,
                                           src_fingerprint=src_fingerprint_cond,
                                           location=location_cond)


def get_timestamp_filter(body_json):
    time_range = body_json.get('time_range', '')
    last_start_time, start_time, end_time = get_start_end_time_by_time_filter(time_range)
    timestamp_range = "timestamp >= {} and timestamp <{}".format(start_time, end_time)
    return timestamp_range


def get_risk_filter(req_body_json):
    risk_levels = req_body_json.get('risk_levels', [])
    risk_types = req_body_json.get('risk_types', [])
    risk_subtypes = req_body_json.get('risk_subtypes', [])

    # risk_subtypes = get_risk_subtype_set_by_levels(risk_levels).intersection(set(risk_subtypes))
    if not risk_types:
        risk_type_filter = 1
    else:
        temp = ','.join("'%s'" % element for element in risk_types)
        risk_type_filter = "hasAny(anomaly_category, [{temp}])".format(temp=temp)

    if not risk_subtypes:
        risk_subtype_filter = 1
    else:
        temp_str = ','.join("'%s'" % element for element in risk_subtypes)
        risk_subtype_filter = "hasAny(anomaly_subcategory, [{0}])".format(temp_str)

    risk_filter = "{risk_type_filter} AND {risk_subtype_filter}".format(risk_type_filter=risk_type_filter,
                                                                        risk_subtype_filter=risk_subtype_filter)
    return risk_filter


def get_risk_subtype_list_by_levels(levels=None):
    local_risks = RiskConf.get_local_risks()
    if not local_risks:
        risk_settings = RiskConf.get_sub_risks()
        high_risk_list = []
        medium_risk_list = []
        low_risk_list = []
        none_risk_list = []
        for k, v in risk_settings.items():
            if v.get('level', "") == "high":
                high_risk_list.append(k)
            elif v.get('level', "") == "medium":
                medium_risk_list.append(k)
            elif v.get('level', "") == "low":
                low_risk_list.append(k)
            else:
                none_risk_list.append(k)

        local_risks = {
            "high": high_risk_list,
            "medium": medium_risk_list,
            "low": low_risk_list,
            "none": none_risk_list

        }

    if levels is None:
        return local_risks
    else:
        tmp = {}
        for level in levels:
            tmp.update({level:local_risks.get(level, [])})
        return tmp


def get_risk_subtype_list_by_parent_id(parent_id):
    risk_settings = RiskConf.get_sub_risks()
    return [k for k, v in risk_settings.items() if v.get("parent_id") == parent_id]


def get_client_identifier(request):
    # 用户名
    user_name = request.user.username
    ip_address = request.META.get('REMOTE_ADDR')
    # 如果是通过代理，可能需要检查HTTP_X_FORWARDED_FOR
    forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    # 用户代理(浏览器信息)
    user_agent = request.META.get('HTTP_USER_AGENT')
    # 会话ID
    session_id = request.session.session_key
    # 可以组合这些信息来唯一标识客户端
    client_identifier = "{user_name}_{ip_address}_{forwarded_for}_{user_agent}_{session_id}".format(
        user_name=user_name.encode('utf-8'),
        ip_address=ip_address,
        forwarded_for=forwarded_for,
        user_agent=user_agent,
        session_id=session_id)

    md5_hash = hashlib.md5(client_identifier).hexdigest()
    return md5_hash

def generate_random_digits(length=3):
    return ''.join(str(random.randint(0, 9)) for _ in range(length))


def generate_query_related_id(prefix, request):
    session_business_id = "{prefix}:{client_identifier}".format(prefix=prefix, client_identifier=get_client_identifier(request))
    query_id = "{session_business_id}:{random_digit}".format(session_business_id=session_business_id,
                                                             random_digit=generate_random_digits())
    return session_business_id, query_id

def is_none_risk_display():
    return ConfDb().get_value("risk_setting/display_none_risk", False)


def cancel_query_with_same_business_id(session_business_id, sailfish_user):
    query_id = ''
    query_task_list = sailfish_restapi_query_task_list(session_business_id=session_business_id, sailfish_user=sailfish_user)
    if type(query_task_list) != list:
        return []
    for query_task in query_task_list:
        if (query_task["query_status"] == QueryStatus.PENDING
                or (query_task["query_status"] == QueryStatus.RUNNING)
                or (query_task["query_status"] == QueryStatus.SCHEDULED)):
            query_id = query_task['query_id']
            sailfish_restapi_cancel_query(query_id=query_id, sailfish_user=sailfish_user)