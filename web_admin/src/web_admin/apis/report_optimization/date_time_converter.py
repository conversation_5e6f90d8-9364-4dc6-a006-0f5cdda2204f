# encoding: utf-8
import re
import os
import sys
import pytz
import copy
import logging
import time
import _strptime  # workaround for https://bugs.python.org/issue7980
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from asp_utils.utils import ugettext

# 内置时间筛选类型
TIME_FILTER_ALL = 'from:all,to:now'  # 所有时间
TIME_FILTER_1_HOUR_AGO = 'from:-1h,to:now'  # 1小时前
TIME_FILTER_PAST_24_HOURS = 'from:-24h,to:now'  # 过去24小时
TIME_FILTER_PAST_7_DAYS = 'from:-7d,to:now'  # 过去7天
TIME_FILTER_PAST_30_DAYS = 'from:-30d,to:now'  # 过去30天
TIME_FILTER_TODAY = 'from:/d,to:now'  # 今天
TIME_FILTER_YESTERDAY = 'from:-1d/d,to:/d'  # 昨天
TIME_FILTER_THIS_WEEK = 'from:/w,to:now'  # 本周
TIME_FILTER_LAST_WEEK = 'from:-1w/w,to:/w'  # 上周
TIME_FILTER_THIS_MONTH = 'from:/M,to:now'  # 本月
TIME_FILTER_LAST_MONTH = 'from:-1M/M,to:/M'  # 上月
TIME_FILTER_ALL_BEFORE_30_DAYS = 'from:all,to:-30d'  # 30天之前
TIME_FILTER_ALL_BEFORE_60_DAYS = 'from:all,to:-60d'  # 60天之前

DISPLAY_PRECISION_TYPE_ORIGIN = 'none'
DISPLAY_PRECISION_TYPE_DAY = 'day'
DISPLAY_PRECISION_TYPE_MONTH = 'month'

VALID_TIME_UNIT = 'mhdwMy'

# Greenwich时间1970年1月1号 00:00:00
UTC_TIMESTAMP_START_DATETIME = datetime(1970, 1, 1, 0, 0, 0, 0, tzinfo=pytz.UTC)

TIME_INTERVAL_SECONDS = {
    'minute': 60,
    'hour': 3600,
    'day': 3600 * 24,
    'week': 3600 * 24 * 7
    # 月和年时间不固定，需要根据当前时间计算
}


# datetime类型转整形timestamp，精确到秒
def convert_datetime_to_timestamp(date_time):
    # 时间戳的定义是：格林威治时间1970年1月1号 00:00:00, 时间戳与时区无关
    # Greenwich时间1970年1月1号 00:00:00这一时刻, 北京时间是 1970年1月1号 08:00:00, 纽约时间1969年12月31号 19:00:00 ... 时间戳都是 0
    # 本地时间转时间戳，应该先把本地时间转成Greenwich时间，再计算到Greenwich时间1970年1月1日00:00:00的总秒数

    # datetime实例不含tzinfo，默认使用系统时区，直接用mktime转即可
    if not date_time.tzname():
        return int(time.mktime(date_time.timetuple()))

    # 带tzinfo，时间先转Greenwich时间，再计算到1970秒数
    utc_date_time = date_time.astimezone(pytz.UTC)
    delta = utc_date_time - UTC_TIMESTAMP_START_DATETIME
    return int(delta.total_seconds())


# 整形timestamp 转 datetime类型
# 不传timezone默认获取时间戳对应本地日期
def convert_timestamp_to_datetime(timestamp, tz=None):
    if tz:
        return datetime.fromtimestamp(timestamp, tz)
    return datetime.fromtimestamp(timestamp)


def convert_date_str_to_timestamp(date_str, format='%Y-%m-%d %H:%M:%S'):
    return convert_datetime_to_timestamp(datetime.strptime(date_str, format))


# 时间戳转可读字符串
# 1687155276 -> "2023-06-19 14:14:36"
def convert_timestamp_to_datetime_str(timestamp, tz=None):
    date_time = convert_timestamp_to_datetime(timestamp, tz)
    return date_time.strftime('%Y-%m-%d %H:%M:%S')


# 获取当前时间datetime，不传时区默认使用系统时区
def get_now_datetime(timezone=None):
    if timezone:
        return datetime.now(tz=timezone)
    return datetime.now()


# 获取当前时间戳
def get_now_timestamp():
    utc_now = get_now_datetime(timezone=pytz.timezone('UTC'))
    return convert_datetime_to_timestamp(utc_now)


# 计算相对日期
# dy,dm,dw,dd,dh为相对于src_datetime年数、月数、天数、小时数、分钟数，整数，正：未来，负：过去
def calculate_relative_datetime(src_datetime, dy=0, dM=0, dw=0, dd=0, dh=0, dm=0):
    # 与clickhouse minus(now, tointervalMonth(x)) 计算x月前日期保持一致
    # 当前日期所在月的天数，如果大于目标月份的最大天数，则取目标月份最大天数 （与跨过多少个月份无关）
    # 例如 当前时间为 【3月31号12点】，1个月前是2月，最多28天（闰年为29天），那么1个月前是 【2月28号（闰年为2月29号）12点】
    # 跨多个月的情况，当前时间为 【3月31号12点】，4个月前是去年11月，11月最多有30天， 目标日期为【去年11月30号12点】
    # 其他情况下，都是跳转到上个月的当前天，例如当前日期【3月15号12点】的3个月前，目标日期【12月15号12点】

    # 取x年之前的原理与月相同
    # 实际上只有闰年的2月29号，前x年之前如果不是闰年，2月28天，取28号，其他情况都是取上一年的本月当天
    # https://dateutil.readthedocs.io/en/stable/relativedelta.html
    try:
        # 各种参数越界报错，统一返回时间戳0对应的datetime
        # 'Python int too large to convert to C int', 'date value out of range', 'year is out of range'
        return src_datetime + relativedelta(years=dy, months=dM, weeks=dw, days=dd, hours=dh, minutes=dm)

    except Exception as e:

        # 无时区信息，返回系统时区0时戳对应的datetime，否则返回当地时区0时戳对应的datetime
        if not src_datetime.tzinfo:
            return convert_timestamp_to_datetime(0)
        return convert_timestamp_to_datetime(0, tz=src_datetime.tzinfo)


# 计算相对日期的时间戳
# dy,dm,dw,dd,dh为相对于src_timestamp年数、月数、天数、小时数，整数，正：未来，负：过去
def calculate_relative_timestamp(src_timestamp, dy=0, dM=0, dw=0, dd=0, dh=0, dm=0, tz=None):
    src_datetime = convert_timestamp_to_datetime(src_timestamp, tz)
    dst_datetime = calculate_relative_datetime(src_datetime, dy, dM, dw, dd, dh, dm)
    return convert_datetime_to_timestamp(dst_datetime)


def round_datetime_to_week_start(date_time):
    # weekday(), 周一是0，周日是6
    weekday_delta = - date_time.weekday()
    # 先把日期移动到本周周一，再把时分秒置零取周一0点
    date_time = calculate_relative_datetime(date_time, dd=weekday_delta)
    date_time = date_time.replace(hour=0, minute=0, second=0, microsecond=0)
    return date_time


ROUND_DATETIME_FUNC_MAP = {
    'm': lambda x: x.replace(second=0, microsecond=0),
    'h': lambda x: x.replace(minute=0, second=0, microsecond=0),
    'd': lambda x: x.replace(hour=0, minute=0, second=0, microsecond=0),
    'w': lambda x: round_datetime_to_week_start(x),
    'M': lambda x: x.replace(day=1, hour=0, minute=0, second=0, microsecond=0),
    'y': lambda x: x.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
}


def round_datetime(date_time, round_type):
    return ROUND_DATETIME_FUNC_MAP[round_type](date_time)


# round_type指定取整类型
# 'm': 取分钟第0秒
# 'h': 取小时第0分0秒
# 'd': 取天0时0分0秒
# 'w': 取周一0时0分0秒
# 'M': 取月第一天0时0分0秒
# 'y': 取1月1号0时0分0秒
def round_timestamp(timestamp, round_type):
    date_time = convert_timestamp_to_datetime(timestamp)
    date_time = ROUND_DATETIME_FUNC_MAP[round_type](date_time)
    return convert_datetime_to_timestamp(date_time)


# 获取本周起止时间和上周一0点时间戳
# last_start:上周一 00:00:00, start:本周一 00:00:00, end:当前时间
def get_this_week_start_end_timestamp(builtin_round=True):
    end = get_now_timestamp()
    start = round_timestamp(end, round_type='w')
    last_start = start - TIME_INTERVAL_SECONDS['week']
    # last_end = end - TIME_INTERVAL_SECONDS['week']
    return last_start, start, end


# 获取上周一 00:00:00 ~ 本周一 00:00:00 时间戳
# last_week_start:上周一 00:00:00, last_week_end:本周一 00:00:00
def get_last_week_start_end_timestamp(builtin_round=True):
    last_week_start, last_week_end, _ = get_this_week_start_end_timestamp()
    return last_week_start - TIME_INTERVAL_SECONDS['week'], last_week_start, last_week_end


# 获取本月起止时间和上月1号0点时间戳
# last_start:上个月1号 00:00:00, start:本月1号 00:00:00, end:当前时间
def get_this_month_start_end_timestamp(builtin_round=True):
    end = get_now_timestamp()
    start = round_timestamp(end, round_type='M')
    # 例如 end = 2024.4.10 12:00:00，start = 2024.4.1 00:00:00 ，start - 1 = 2024.3.31 23:59:59，再取整到月开头就是 2024.3.1 00:00:00
    last_start = round_timestamp(start - 1, round_type='M')

    return last_start, start, end


# 获取上个月1号 00:00:00 ~ 本月第一天 00:00:00
# last_month_start:上个月1号 00:00:00, last_month_end:本月1号 00:00:00
def get_last_month_start_end_timestamp(builtin_round=True):
    last_month_start, last_month_end, _ = get_this_month_start_end_timestamp()
    return round_timestamp(last_month_start - 1, round_type='M'), last_month_start, last_month_end


# 获取当天起止时间和昨天0点时间戳
# last_start:昨天 00:00:00, start:今天 00:00:00, end:当前时间
def get_today_start_end_timestamp(builtin_round=True):
    end = get_now_timestamp()
    start = round_timestamp(end, round_type='d')
    last_start = start - TIME_INTERVAL_SECONDS['day']
    # last_end = end - TIME_INTERVAL_SECONDS['day']
    return last_start, start, end


# 获取昨天 00:00:00 ~ 今天 00:00:00
# last_day_start:昨天 00:00:00, last_day_end:今天 00:00:00
def get_yesterday_start_end_timestamp(builtin_round=True):
    last_day_start, last_day_end, _ = get_today_start_end_timestamp()
    last_start = last_day_start - TIME_INTERVAL_SECONDS['day']
    # last_end = end - TIME_INTERVAL_SECONDS['day']
    return last_start, last_day_start, last_day_end


def get_all_time_start_end(builtin_round=True):
    end = get_now_timestamp()
    last_start = start = 0
    return last_start, start, end


def get_past_24_hours_start_end(builtin_round=True):
    end = get_now_timestamp()
    start = end - TIME_INTERVAL_SECONDS['day']
    if builtin_round:
        start = round_timestamp(start, round_type='h')
    last_start = start - TIME_INTERVAL_SECONDS['day']
    return last_start, start, end


def get_1_hour_ago_start_end(builtin_round=True):
    end = get_now_timestamp()
    start = end - TIME_INTERVAL_SECONDS['hour']
    if builtin_round:
        start = round_timestamp(start, round_type='h')
    last_start = start - TIME_INTERVAL_SECONDS['hour']
    return last_start, start, end


def get_past_7_days_start_end(builtin_round=True):
    end = get_now_timestamp()
    start = end - TIME_INTERVAL_SECONDS['week']
    if builtin_round:
        start = round_timestamp(start, round_type='h')
    last_start = start - TIME_INTERVAL_SECONDS['week']
    return last_start, start, end


def get_past_30_days_start_end(builtin_round=True):
    end = get_now_timestamp()
    # 如果不按天取整，环比两端数据会有1天重复
    start = end - TIME_INTERVAL_SECONDS['day'] * 30
    if builtin_round:
        start = round_timestamp(start, round_type='d')
    last_start = start - TIME_INTERVAL_SECONDS['day'] * 30
    return last_start, start, end


def get_before_days_start_end(days=30, builtin_round=True):
    end = get_now_timestamp() - TIME_INTERVAL_SECONDS['day'] * days
    # 如果不按天取整，环比两端数据会有1天重复
    # 默认是 1970-01-01 00:00:00, 这里给一个值大于默认值(0会取值东8区时间)，排除手动添加的API
    start = 0
    if builtin_round:
        end = round_timestamp(end, round_type='d')
    last_start = start
    return last_start, start, end


def get_before_30_days_start_end(builtin_round=True):
    return get_before_days_start_end(30, builtin_round)


def get_before_60_days_start_end(builtin_round=True):
    return get_before_days_start_end(60, builtin_round)


BUILTIN_TIME_FILTER_FUNC = {
    TIME_FILTER_ALL: get_all_time_start_end,
    TIME_FILTER_1_HOUR_AGO: get_1_hour_ago_start_end,
    TIME_FILTER_PAST_24_HOURS: get_past_24_hours_start_end,
    TIME_FILTER_PAST_7_DAYS: get_past_7_days_start_end,
    TIME_FILTER_PAST_30_DAYS: get_past_30_days_start_end,
    TIME_FILTER_TODAY: get_today_start_end_timestamp,
    TIME_FILTER_YESTERDAY: get_yesterday_start_end_timestamp,
    TIME_FILTER_THIS_WEEK: get_this_week_start_end_timestamp,
    TIME_FILTER_LAST_WEEK: get_last_week_start_end_timestamp,
    TIME_FILTER_THIS_MONTH: get_this_month_start_end_timestamp,
    TIME_FILTER_LAST_MONTH: get_last_month_start_end_timestamp,
    TIME_FILTER_ALL_BEFORE_30_DAYS: get_before_30_days_start_end,
    TIME_FILTER_ALL_BEFORE_60_DAYS: get_before_60_days_start_end
}
BUILTIN_TIME_CYCLE_FUNC = {
    TIME_FILTER_ALL: """
                        now() as t0,
                        toDateTime('1970-01-01 08:00:00') as t1, 
                        toDateTime('1970-01-01 08:00:00') as t2,
                        toDateTime('1970-01-01 08:00:00') as t3,
                        toDateTime('1970-01-01 08:00:00') as t4,
                        toDateTime('1970-01-01 08:00:00') as t5,
                        toDateTime('1970-01-01 08:00:00') as t6
                        """,
    TIME_FILTER_1_HOUR_AGO: """
                        now() as t0,
                        t0 - toIntervalHour(1) as  t1,
                        t0 - toIntervalHour(2) as  t2,
                        t0 - toIntervalHour(3) as  t3,
                        t0 - toIntervalHour(4) as  t4,
                        t0 - toIntervalHour(5) as  t5,
                        t0 - toIntervalHour(6) as  t6
                    """,
    TIME_FILTER_PAST_24_HOURS: """
                        now() as t0,
                        t0 - toIntervalHour(24 ) as  t1,
                        t0 - toIntervalHour(24 * 2) as  t2,
                        t0 - toIntervalHour(24 * 3) as  t3,
                        t0 - toIntervalHour(24 * 4) as  t4,
                        t0 - toIntervalHour(24 * 5) as  t5,
                        t0 - toIntervalHour(24 * 6) as  t6
                    """,
    TIME_FILTER_PAST_7_DAYS: """
                        now() as t0,
                        t0 - toIntervalDay(7 ) as  t1,
                        t0 - toIntervalDay(7 * 2) as  t2,
                        t0 - toIntervalDay(7 * 3) as  t3,
                        t0 - toIntervalDay(7 * 4) as  t4,
                        t0 - toIntervalDay(7 * 5) as  t5,
                        t0 - toIntervalDay(7 * 6) as  t6
                    """,
    TIME_FILTER_PAST_30_DAYS: """
                        now() as t0,
                        t0 - toIntervalDay(30 ) as  t1,
                        t0 - toIntervalDay(30 * 2) as  t2,
                        t0 - toIntervalDay(30 * 3) as  t3,
                        t0 - toIntervalDay(30 * 4) as  t4,
                        t0 - toIntervalDay(30 * 5) as  t5,
                        t0 - toIntervalDay(30 * 6) as  t6
                    """,
    TIME_FILTER_TODAY: """
                        toStartOfDay(now()+INTERVAL 1 Day) as t0,
                        toStartOfDay(now()) as  t1,
                        toStartOfDay(now() - INTERVAL 1 Day) as  t2,
                        toStartOfDay(now() - INTERVAL 2 Day) as  t3,
                        toStartOfDay(now() - INTERVAL 3 Day) as  t4,
                        toStartOfDay(now() - INTERVAL 4 Day) as  t5,
                        toStartOfDay(now() - INTERVAL 5 Day) as  t6
                    """,  # (timestamp >= toStartOfDay(now()) and timestamp < toStartOfDay(now()+INTERVAL 1 Day))
    TIME_FILTER_YESTERDAY: """
                        toStartOfDay(now()) as t0,
                        toStartOfDay(now()-INTERVAL 1 Day) as  t1,
                        toStartOfDay(now()-INTERVAL 2 Day) as  t2,
                        toStartOfDay(now()-INTERVAL 3 Day) as  t3,
                        toStartOfDay(now()-INTERVAL 4 Day) as  t4,
                        toStartOfDay(now()-INTERVAL 5 Day) as  t5,
                        toStartOfDay(now()-INTERVAL 6 Day) as  t6
                    """,  # timestamp >= toStartOfDay(now()-INTERVAL 1 Day) and timestamp < toStartOfDay(now())
    TIME_FILTER_THIS_WEEK: """
                        toDateTime(toMonday(now()+ INTERVAL 1 Week)) as t0,
                        toDateTime(toMonday(now())) as t1, 
                        toDateTime(toMonday(now()- INTERVAL 1 Week)) as t2,
                        toDateTime(toMonday(now()- INTERVAL 2 Week)) as t3,
                        toDateTime(toMonday(now()- INTERVAL 3 Week)) as t4,
                        toDateTime(toMonday(now()- INTERVAL 4 Week)) as t5,
                        toDateTime(toMonday(now()- INTERVAL 5 Week)) as t6
                        """,
    TIME_FILTER_LAST_WEEK: """
                         toDateTime(toMonday(now())) as t0,
                         toDateTime(toMonday(now()-INTERVAL 1 Week)) as t1,    
                         toDateTime(toMonday(now()-INTERVAL 2 Week)) as t2, 
                         toDateTime(toMonday(now()-INTERVAL 3 Week)) as t3, 
                         toDateTime(toMonday(now()-INTERVAL 4 Week)) as t4, 
                         toDateTime(toMonday(now()-INTERVAL 5 Week)) as t5, 
                         toDateTime(toMonday(now()-INTERVAL 6 Week)) as t6
                        """,
    TIME_FILTER_THIS_MONTH: """
                        toDateTime(toStartOfMonth(now()+INTERVAL 1 Month)) as t0,
                        toDateTime(toStartOfMonth(now())) as t1, 
                        toDateTime(toStartOfMonth(now())-INTERVAL 1 Month) as t2,
                        toDateTime(toStartOfMonth(now())-INTERVAL 2 Month) as t3,
                        toDateTime(toStartOfMonth(now())-INTERVAL 3 Month) as t4,
                        toDateTime(toStartOfMonth(now())-INTERVAL 4 Month) as t5,
                        toDateTime(toStartOfMonth(now())-INTERVAL 5 Month) as t6
                        """,
    TIME_FILTER_LAST_MONTH: """
                        toDateTime(toStartOfMonth(now())) as t0,
                        toDateTime(toStartOfMonth(now()-INTERVAL 1 Month)) as t1,
                        toDateTime(toStartOfMonth(now()-INTERVAL 2 Month)) as t2,
                        toDateTime(toStartOfMonth(now()-INTERVAL 3 Month)) as t3,
                        toDateTime(toStartOfMonth(now()-INTERVAL 4 Month)) as t4,
                        toDateTime(toStartOfMonth(now()-INTERVAL 5 Month)) as t5,
                        toDateTime(toStartOfMonth(now()-INTERVAL 6 Month)) as t6

                            """,
    TIME_FILTER_ALL_BEFORE_30_DAYS: """
                        now()-INTERVAL 30 Day as t0, 
                        toDateTime('1970-01-01 08:00:00') as t1, 
                        toDateTime('1970-01-01 08:00:00') as t2,
                        toDateTime('1970-01-01 08:00:00') as t3,
                        toDateTime('1970-01-01 08:00:00') as t4,
                        toDateTime('1970-01-01 08:00:00') as t5,
                        toDateTime('1970-01-01 08:00:00') as t6
                        """,
    TIME_FILTER_ALL_BEFORE_60_DAYS: """
                        now()-INTERVAL 60 Day as t0, 
                        toDateTime('1970-01-01 08:00:00') as t1, 
                        toDateTime('1970-01-01 08:00:00') as t2,
                        toDateTime('1970-01-01 08:00:00') as t3,
                        toDateTime('1970-01-01 08:00:00') as t4,
                        toDateTime('1970-01-01 08:00:00') as t5,
                        toDateTime('1970-01-01 08:00:00') as t6
                        """
}

DATE_START_FUNC_MAP = {
    '/m': lambda now: round_timestamp(now, round_type='m'),
    '/h': lambda now: round_timestamp(now, round_type='h'),
    '/d': lambda now: round_timestamp(now, round_type='d'),
    '/w': lambda now: round_timestamp(now, round_type='w'),
    '/M': lambda now: round_timestamp(now, round_type='M'),
    '/y': lambda now: round_timestamp(now, round_type='y')
}


def convert_sailfish_style_time_to_timestamp(time_str, now_datetime, bound):
    # 区间上界、下界
    assert bound in ('upper', 'lower')
    if time_str.isdigit():
        # 前端时间筛选器传只包含阿拉伯数字的字符串，精确时间
        timestamp = int(time_str)

    elif time_str == 'now':
        # now 取当前时间戳
        timestamp = convert_datetime_to_timestamp(now_datetime)

    elif time_str == 'all':
        # 结束时间是all给now，开始时间是all给-28800(可能出现的最小值)
        timestamp = convert_datetime_to_timestamp(now_datetime) if bound == 'upper' else -28800

    elif time_str in DATE_START_FUNC_MAP:
        now_timestamp = convert_datetime_to_timestamp(now_datetime)
        timestamp = DATE_START_FUNC_MAP[time_str](now_timestamp)

    else:
        # 否则是相对时间格式 -7h/h, -7d/d, -7M/M, -7y/y, /h, /d, /w, /M, /y
        # 如果没有数字，就是 /h, /d, /w, /M, /y，delta=0
        date_time = now_datetime

        round_unit = None
        if time_str[-2] == '/':
            assert (time_str[-1] in VALID_TIME_UNIT)
            round_unit = time_str[-1]
            time_str = time_str[:-2]

        assert (time_str[-1] in VALID_TIME_UNIT)
        delta_unit = time_str[-1]

        delta_num = int(time_str[:-1])

        kwargs = {}
        kwargs['d' + delta_unit] = delta_num
        date_time = calculate_relative_datetime(now_datetime, **kwargs)
        # /x 后缀视为按单位x取整
        if round_unit:
            date_time = round_datetime(date_time, round_type=round_unit)
        timestamp = convert_datetime_to_timestamp(date_time)

    return timestamp


def get_start_end_time_by_time_filter(time_filter, builtin_round=False):
    # 内置时间筛选器
    if time_filter in BUILTIN_TIME_FILTER_FUNC:
        return BUILTIN_TIME_FILTER_FUNC[time_filter](builtin_round)

    try:
        # 自定义时间范围解析
        filter_split = time_filter.split(',')
        assert (len(filter_split) == 2)

        start_split = filter_split[0].split(':')
        end_split = filter_split[1].split(':')

        assert (len(start_split) == 2 and len(end_split) == 2)

        assert (start_split[0] == 'from' and end_split[0] == 'to')

    except Exception as e:
        # 时间筛选器格式非法，返回所有时间戳置0，避免测试时接口调用500
        logging.error(e.message)
        return 0, 0, 0

    # 自定义时间隐藏环比，不需要上个周期统计数据，last_start=start
    now_datetime = get_now_datetime()

    try:
        start = convert_sailfish_style_time_to_timestamp(start_split[1], now_datetime, bound='lower')
    except Exception as e:
        logging.error(e.message)
        start = 0

    try:
        end = convert_sailfish_style_time_to_timestamp(end_split[1], now_datetime, bound='upper')
    except Exception as e:
        logging.error(e.message)
        end = 0

    last_start = start - (end - start)

    return last_start, start, end


def get_datetime_cycle(time_filter):
    if time_filter in BUILTIN_TIME_CYCLE_FUNC:
        return BUILTIN_TIME_CYCLE_FUNC[time_filter]
    else:
        _, start_time, end_time = get_start_end_time_by_time_filter(time_filter)
        return """
            toDateTime({end_time}) as t0,
            toDateTime({start_time}) as t1,
            t0 - t1 as delta, 
            toDateTime(t1 - delta * 1) as t2,
            toDateTime(t1 - delta * 2) as t3,
            toDateTime(t1 - delta * 3) as t4,
            toDateTime(t1 - delta * 4) as t5,
            toDateTime(t1 - delta * 5) as t6
        """.format(start_time=start_time, end_time=end_time)



def get_life_monitor_start_end_time():
    return get_past_7_days_start_end()


def get_day_start(timestamp):
    return round_timestamp(timestamp, round_type='d')


def get_day_end(timestamp):
    return get_day_start(timestamp) + TIME_INTERVAL_SECONDS['day']


def get_all_time_range_list():
    time_range_list = []
    _, start, end = get_today_start_end_timestamp()
    now_datetime = datetime.fromtimestamp(end)
    last_year_start_date = now_datetime.replace(year=now_datetime.year - 1, month=1, day=1, hour=0, minute=0, second=0)

    # 取去年1月1号0点 ~ 今年下个月1号0点 作为时间区间
    for month in range(1, 13):
        time_range_list.append(convert_datetime_to_timestamp(last_year_start_date.replace(month=month)))

    this_year_end_date = now_datetime.replace(day=1, hour=0, minute=0, second=0)
    for month in range(0, this_year_end_date.month + 1):
        if month < 12:
            end_of_month = convert_datetime_to_timestamp(this_year_end_date.replace(month=month + 1))
        else:
            year = this_year_end_date.year
            end_of_month = convert_datetime_to_timestamp(this_year_end_date.replace(year=year + 1, month=1))
        time_range_list.append(end_of_month)

    return time_range_list


def get_past_30_days_time_range_list():
    time_range_list = []
    _, start, end = get_past_30_days_start_end()
    start = get_day_start(start)
    end = get_day_end(end)
    min = start
    while (min <= end):
        time_range_list.append(min)
        min += 86400
    return time_range_list


def get_this_month_time_range_list():
    time_range_list = []
    _, start, end = get_this_month_start_end_timestamp()
    min = start
    end = get_day_end(end)
    while (min <= end):
        time_range_list.append(min)
        min += 86400
    return time_range_list


def get_last_month_time_range_list():
    now = datetime.now()
    last_month_end = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_month_start = last_month_end - timedelta(days=31)
    min = convert_datetime_to_timestamp(last_month_start)
    end = convert_datetime_to_timestamp(last_month_end)

    time_range_list = []
    while (min <= end):
        time_range_list.append(min)
        min += 86400
    return time_range_list


def get_custom_range_day_list(start_timestamp, end_timestamp):
    start = round_timestamp(start_timestamp, round_type='d')
    end = round_timestamp(end_timestamp + TIME_INTERVAL_SECONDS['day'], round_type='d')
    end = calculate_relative_timestamp(end, dd=1)
    return [timestamp for timestamp in range(start, end + 1, TIME_INTERVAL_SECONDS['day'])]


def sum_echarts_item(time_range_list, item_nums, echarts_list):
    sum_result = {}
    for time in time_range_list[:-1]:
        sum_result[int(time)] = [time] + ([0] * item_nums)
    for echart_item in echarts_list:
        timestamp = echart_item[0]
        if timestamp < time_range_list[0]:
            continue
        for index, time in enumerate(time_range_list[1:]):
            if timestamp < time:
                merge_list = sum_result[time_range_list[index]]
                for idx in range(1, item_nums + 1):
                    merge_list[idx] += echart_item[idx]
                break
    return sum_result


def get_display_precision_and_time_range_list(time_filter, start_timestamp=None, end_timestamp=None):
    if time_filter in (
    TIME_FILTER_PAST_24_HOURS, TIME_FILTER_PAST_7_DAYS, TIME_FILTER_TODAY, TIME_FILTER_THIS_WEEK, TIME_FILTER_YESTERDAY,
    TIME_FILTER_LAST_WEEK):
        return DISPLAY_PRECISION_TYPE_ORIGIN, []

    # 所有时间，天表出数据，以月为粒度合并
    if time_filter == TIME_FILTER_ALL:
        return DISPLAY_PRECISION_TYPE_MONTH, get_all_time_range_list()

    # 过去30天，以天为粒度合并
    if time_filter == TIME_FILTER_PAST_30_DAYS:
        return DISPLAY_PRECISION_TYPE_DAY, get_past_30_days_time_range_list()

    # 上月，天粒度合并
    if time_filter == TIME_FILTER_LAST_MONTH:
        return DISPLAY_PRECISION_TYPE_DAY, get_last_month_time_range_list()

    # 本月，>7天从天表出数据，<=7天从小时表出数据，以天为粒度合并
    if time_filter == TIME_FILTER_THIS_MONTH:
        return DISPLAY_PRECISION_TYPE_DAY, get_this_month_time_range_list()

    # 自定义时间筛选
    if start_timestamp is None:
        start_timestamp = 0

    if end_timestamp is None:
        end_timestamp = get_now_timestamp()

    time_delta = end_timestamp - start_timestamp

    # 时间范围小于等于7天，不合并
    if time_delta <= 7 * TIME_INTERVAL_SECONDS['day']:
        union_date = round_datetime(calculate_relative_datetime(get_now_datetime(), dd=-14), round_type='d')
        union_point = convert_datetime_to_timestamp(union_date)
        if start_timestamp >= union_point:
            return DISPLAY_PRECISION_TYPE_ORIGIN, []
        else:
            return DISPLAY_PRECISION_TYPE_DAY, get_custom_range_day_list(start_timestamp, end_timestamp)

    # 大于7天，小于等于90天，按天合并
    if time_delta > 7 * TIME_INTERVAL_SECONDS['day'] and time_delta <= 90 * TIME_INTERVAL_SECONDS['day']:
        return DISPLAY_PRECISION_TYPE_DAY, get_custom_range_day_list(start_timestamp, end_timestamp)

    # 大于90天，按月合并
    if time_delta > 90 * TIME_INTERVAL_SECONDS['day']:
        start_datetime = convert_timestamp_to_datetime(start_timestamp)
        start = round_datetime(start_datetime, round_type='M')
        end_datetime = convert_timestamp_to_datetime(end_timestamp)
        end = round_datetime(end_datetime, round_type='M')
        end = calculate_relative_datetime(end, dM=1)

        time_range_list = []
        while (start <= end):
            time_range_list.append(convert_datetime_to_timestamp(start))
            start = calculate_relative_datetime(start, dM=1)

        return DISPLAY_PRECISION_TYPE_MONTH, time_range_list

    return DISPLAY_PRECISION_TYPE_ORIGIN, []


# 总览-访问趋势、资产概览-活跃应用访问趋势、攻击趋势、缺陷级别访问趋势、API详情-调用情况-请求次数趋势图
def merge_echarts_format_result(echarts_list, time_filter, start=None, end=None):
    echarts_list = sorted(echarts_list, key=lambda x: x[0])
    display_precision, time_range_list = get_display_precision_and_time_range_list(time_filter, start, end)

    # 最近24小时、最近7天、今天、本周从小时表出数据，展示粒度小时，不需要合并
    if len(echarts_list) == 0 or not time_range_list:
        return display_precision, echarts_list

    item_nums = len(echarts_list[0]) - 1
    sum_result_dict = sum_echarts_item(time_range_list, item_nums, echarts_list)

    echarts_result_list = []
    for timestamp, sum_result in sum_result_dict.items():
        for item in sum_result[1:]:
            if item > 0:
                echarts_result_list.append(sum_result)
                break

    return display_precision, sorted(echarts_result_list, key=lambda x: x[0])


def is_empty_dict_result(target_dict):
    result = True
    for key, value in target_dict.items():
        if isinstance(value, dict):
            result = (result and is_empty_dict_result(value))
        else:
            if key != 'timestamp' and value > 0:
                return False
    return result


def gen_result_template_dict(sample_dict):
    rt = {}

    for key, value in sample_dict.items():
        if isinstance(value, dict):
            rt[key] = gen_result_template_dict(value)
        else:
            rt[key] = 0

    return rt


def sum_dict_result(dict1, dict2):
    for key, value in dict1.items():
        if isinstance(value, dict):
            sum_dict_result(value, dict2[key])
        else:
            if key == 'timestamp':
                continue
            dict1[key] += dict2[key]


def sum_dict_format_echarts_list(time_range_list, echarts_list):
    if len(echarts_list) == 0:
        return {}

    sum_result = {}
    template = gen_result_template_dict(echarts_list[0])
    for time in time_range_list[:-1]:
        template['timestamp'] = time
        sum_result[time] = copy.deepcopy(template)

    for echart_item in echarts_list:
        if echart_item['timestamp'] < time_range_list[0]:
            continue

        for index, time in enumerate(time_range_list[1:]):
            if echart_item['timestamp'] < time:
                merge_dict = sum_result[time_range_list[index]]
                sum_dict_result(merge_dict, echart_item)
                break

    return sum_result


# 敏感信息级别访问趋势单独处理
def merge_pii_level_trend(echarts_list, time_filter, start=None, end=None):
    # 最近24小时、最近7天、今天、本周从小时表出数据，展示粒度小时，不需要合并
    display_precision, time_range_list = get_display_precision_and_time_range_list(time_filter, start, end)
    if len(echarts_list) == 0 or not time_range_list:
        return display_precision, echarts_list

    sum_result = sum_dict_format_echarts_list(time_range_list, echarts_list)
    merged_echarts = sorted([value for _, value in sum_result.items() if not is_empty_dict_result(value)],
                            key=lambda x: x['timestamp'])

    return display_precision, merged_echarts


if __name__ == "__main__":

    list_format_echarts = [
        [
            1687406400,
            284122,
            84186,
            0
        ],
        [
            1687596400,
            284122,
            84186,
            0
        ],
        [
            1688970606,
            2902,
            1363,
            0
        ]]

    dict_format_echarts = [
        {
            "high": {
                "api": 56,
                "file": 20
            },
            "timestamp": 1687449600,
            "low": {
                "api": 3007,
                "file": 22
            },
            "middle": {
                "api": 719,
                "file": 21
            }
        },
        {
            "high": {
                "api": 56,
                "file": 20
            },
            "timestamp": 1687549600,
            "low": {
                "api": 3007,
                "file": 22
            },
            "middle": {
                "api": 719,
                "file": 21
            }
        },
        {
            "high": {
                "api": 6595,
                "file": 20
            },
            "timestamp": 1688970606,
            "low": {
                "api": 243400,
                "file": 22
            },
            "middle": {
                "api": 62099,
                "file": 21
            }
        }
    ]

    # print builtin filter
    filter_type_name = {
        TIME_FILTER_ALL: ugettext('All Time'),
        TIME_FILTER_1_HOUR_AGO: "最近一小时",
        TIME_FILTER_PAST_24_HOURS: "最近24小时",
        TIME_FILTER_PAST_7_DAYS: "最近7天",
        TIME_FILTER_PAST_30_DAYS: '最近 30 days',
        TIME_FILTER_TODAY: ugettext('Today'),
        TIME_FILTER_YESTERDAY: ugettext('Yesterday'),
        TIME_FILTER_THIS_WEEK: ugettext('This week'),
        TIME_FILTER_LAST_WEEK: ugettext('Last week'),
        TIME_FILTER_THIS_MONTH: ugettext('This month'),
        TIME_FILTER_LAST_MONTH: ugettext('Last month'),
        TIME_FILTER_ALL_BEFORE_30_DAYS: "30天前",
        TIME_FILTER_ALL_BEFORE_60_DAYS: "60天前"
    }

    builtin_time_filter = BUILTIN_TIME_FILTER_FUNC.keys()

    builtin_time_cycle = BUILTIN_TIME_CYCLE_FUNC.keys()
    for time_range in builtin_time_cycle:
        print time_range, BUILTIN_TIME_CYCLE_FUNC.get(time_range)


    for time_filter in builtin_time_filter:
        last_start, start, end = get_start_end_time_by_time_filter(time_filter)
        last_start = convert_timestamp_to_datetime_str(last_start)
        start = convert_timestamp_to_datetime_str(start)
        end = convert_timestamp_to_datetime_str(end)
        print(filter_type_name[time_filter])
        print('last_start: {}'.format(last_start),
              'start: {}'.format(start),
              'end: {}'.format(end))
        print('\n')

    _, start, end = get_start_end_time_by_time_filter('from:-15y/y,to:-7M/M')
    date_time = convert_timestamp_to_datetime(start)
    # 年对齐，取当年1月1号0点
    assert (date_time.month == 1 and date_time.day == 1 and date_time.hour == 0 and
            date_time.minute == 0 and date_time.second == 0)

    date_time = convert_timestamp_to_datetime(end)
    # 月对齐，其当月1号0点
    assert (date_time.day == 1 and date_time.hour == 0 and
            date_time.minute == 0 and date_time.second == 0)

    _, start, end = get_start_end_time_by_time_filter('from:-15d/d,to:-7h/h')
    date_time = convert_timestamp_to_datetime(start)
    # 天对齐，取当天0点
    assert (date_time.hour == 0 and date_time.minute == 0 and date_time.second == 0)

    date_time = convert_timestamp_to_datetime(end)
    # 时对齐，取0分0秒
    assert (date_time.minute == 0 and date_time.second == 0)

    _, start, end = get_start_end_time_by_time_filter('from:-15d/d,to:-7h/h')
    print(convert_timestamp_to_datetime(start), convert_timestamp_to_datetime(end))
    _, start, end = get_start_end_time_by_time_filter('from:-15d/d,to:-7h')
    print(convert_timestamp_to_datetime(start), convert_timestamp_to_datetime(end))
    _, start, end = get_start_end_time_by_time_filter('from:-15d,to:-7h')
    print(convert_timestamp_to_datetime(start), convert_timestamp_to_datetime(end))



