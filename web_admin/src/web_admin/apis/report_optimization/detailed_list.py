# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.post_risk_detailed_list_details_request import PostRiskDetailedListDetailsRequest
from abd_utils.schemas.post_risk_detailed_list_related_fp_request import PostRiskDetailedListRelatedFpRequest
from abd_utils.schemas.post_risk_detailed_list_related_ip_request import PostRiskDetailedListRelatedIpRequest
from abd_utils.schemas.post_risk_detailed_list_request import PostRiskDetailedListRequest

#### 自动导入结束，不要在前面插入代码

import copy
import logging
from collections import defaultdict
from django.core.cache import cache
from asp_utils.utils import get_language
from asp_utils.sailfish_util import query_from_sailfish_sql, sailfish_restapi_request_with_large_result
from web_admin.view_lib import botgate_user_to_sf_user
from common import get_query_filter, get_risk_subtype_desc_by_id, get_risk_filter, get_timestamp_filter, RiskConf, \
    get_client_identifier, cancel_query_with_same_business_id, generate_random_digits

table_name = "merge_access_log"
site_key = "if( notEmpty(server_alias),concat(server_alias, '@') || hostname,hostname) "

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_detailed_list(request):
    """
    @check_rest_api_permission: __guest_function__
        获取风险清单
    """
    try:
        req_body = ClassField("PostRiskDetailedListRequest", PostRiskDetailedListRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_id = "RISKREPORT_DETAILED_LIST:{0}".format(get_client_identifier(request))

    req_body_json = req_body.to_json()
    risk_log_cache = RiskLogCache()
    data = risk_log_cache.get_data(request.user.username, req_body_json, session_business_id)
    return api_ok_response(data)

def add_risk_level_info(records, risk_levels, risk_types, risk_subtypes):
    result = []
    for item in records:
        anomaly_subcategory = item.pop('anomaly_subcategory')
        for i, subtype in enumerate(anomaly_subcategory):
            if subtype not in risk_subtypes and risk_subtypes:
                continue
            desc = get_risk_subtype_desc_by_id(subtype)
            risk_level = desc.get("level", "none")
            if ((len(risk_types) > 0 and (desc.get('parent_id', '') not in risk_types))
                    or (risk_level not in risk_levels)):
                continue
            else:
                tmp = copy.deepcopy(item)
                tmp.update({'risk_level': risk_level,
                            'risk_type': desc.get('parent_name', ""),
                            'risk_subtype': desc.get('name', subtype),
                            'risk_subtype_id': subtype,
                            'risk_type_id': desc.get('parent_id', ''),
                            'subtype_index': i,
                            })
                result.append(tmp)
    return result

def singleton(cls):
    instances = {}

    def get_instance(*args, **kwargs):
        if cls not in instances:
            instances[cls] = cls(*args, **kwargs)
        return instances[cls]

    return get_instance

class RiskLogCache:
    CACHE_TIMEOUT = 60 * 30  # 缓存过期时间设为30分钟

    def __init__(self):
        self.err_msg = ""
        self.ret = 0

    def get_data(self, username, request_body_json, session_business_id):
        page_num = request_body_json.get("page", 1)
        page_size = request_body_json.get("page_size", 50)

        if page_num == 1:
            # 获取总条数,缓存数据
            self.initialize(username, request_body_json, session_business_id)

        cached_data = cache.get(session_business_id)
        if cached_data is None:  # 缓存过期时重新刷新缓存
            self.initialize(username, request_body_json, session_business_id)
            cached_data = cache.get(session_business_id)
        risk_total = len(cached_data)
        if risk_total == 0:
            data = {"result": self.ret,
                    "err_msg": self.err_msg,
                    "total_count": 0,
                    "records": []
                    }
            return data

        rank_filed = request_body_json.get("rank", {}).get("field", "")
        order_type = request_body_json.get('rank', {}).get('order_type', 'DESC')
        if rank_filed == "risk_level":
            sorted_cache = self.sort_by_risk_level(cached_data, order_type)
        else:
            sorted_cache = cached_data

        total_page = risk_total / page_size + (1 if risk_total % page_size > 0 else 0)
        if page_num < total_page:
            records = sorted_cache[((page_num - 1) * page_size):(page_num * page_size)]
        else:
            records = sorted_cache[((page_num - 1) * page_size):]
        data = {"result": self.ret,
                "err_msg": self.err_msg,
                "total_count": risk_total,
                "records": records
                }
        return data

    def initialize(self, username, request_body_json, session_business_id):
        # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

        required_risk_types = request_body_json.get("risk_types", [])
        required_risk_subtypes = request_body_json.get("risk_subtypes", [])
        required_risk_levels = request_body_json.get("risk_levels", [])

        rank = request_body_json.get('rank', {})
        order_type = rank .get("order_type", "DESC") if rank.get("field") == "datetime" else "DESC"

        risk_filter = get_risk_filter(request_body_json)
        query_filter_str = get_query_filter(request_body_json)
        timestamp_filter = get_timestamp_filter(request_body_json)

        sql = """
                WITH ({site_key}) AS `site`
                SELECT 
                    timestamp_ms,
                    concat(formatDateTime(toDateTime(timestamp_ms /1000), '%Y-%m-%d %H:%i:%S'),'.',toString(timestamp_ms % 1000)) AS datetime,
                    request_uuid,
                    anomaly_subcategory,
                    site, 
                    src_ip, 
                    if(fingerprint_browser_2 = '', mobile_device_fingerprint, fingerprint_browser_2) as fp,
                    path,
                    concat(status, ' ', invalid_request_action) AS action,
                    multiIf(
                                is_local_ip = 1,
                                dictGetString('default.bgword', 'desc', tuple('内网')),
                                notEmpty(city),
                                city,
                                notEmpty(province),
                                province,
                                notEmpty(country),
                                country,
                                dictGetString('default.bgword', 'desc', tuple('未知'))
                            ) AS location
                FROM {table_name}
                WHERE is_whitelist != 1
                    AND (anomaly_category IS NOT NULL)
                    AND (length(anomaly_category) != 0)
                    AND ({risk_filter})
                    AND ({query_filter_str})
                    AND ({timestamp_range})
                ORDER BY timestamp_ms {order_type}
                LIMIT {limit} OFFSET {offset}
            """.format(table_name=table_name,
                       risk_filter=risk_filter,
                       query_filter_str=query_filter_str,
                       timestamp_range=timestamp_filter,
                       order_type=order_type,
                       site_key=site_key,
                       limit=5000,
                       offset=0)
        query_id = "{session_business_id}:{random_digit}".format(session_business_id=session_business_id,
                                                                 random_digit=generate_random_digits())
        ret, err_msg, records = query_from_sailfish_sql(sql, sailfish_user=botgate_user_to_sf_user(username),
                                                        session_business_id=session_business_id,
                                                        query_id=query_id)
        self.ret = ret
        self.err_msg = err_msg
        if not ret:
            # 填充风险等级
            result = add_risk_level_info(records=records,
                                         risk_types=required_risk_types,
                                         risk_subtypes=required_risk_subtypes,
                                         risk_levels=required_risk_levels)

            cache.set(session_business_id, result[:5000], self.CACHE_TIMEOUT)
        else:
            cache.set(session_business_id, [], self.CACHE_TIMEOUT)

    def sort_by_risk_level(self, result, order_type):
        tmp = copy.deepcopy(result)
        order_type = order_type.upper()
        risk_level_order = {"high": 3, "medium": 2, "low": 1}
        tmp = sorted(
            tmp,
            key=lambda x: (risk_level_order[x["risk_level"]] if order_type == 'DESC' else (-risk_level_order[x["risk_level"]])),
            reverse=True,
        )
        return tmp

def deal_with_suggestion_detail(record):
    language = get_language()
    detail_key = "detail_en" if language == "en" else "detail_cn"
    suggestion_key = "suggestion_en" if language == "en" else "suggestion_cn"
    manual_rule_id = record.pop("manual_rule_id", "")
    ubb_description = record.pop("ubb_description", "")
    suggestion = ""
    detail = ""
    risk_specifics = RiskConf.get_risk_specifics()
    for specific in risk_specifics:
        if specific.get("id") == record.get("risk_subtype_id"):
            for child in specific.get("child", []):
                if child.get("name") == record["anomaly_specific"]:
                    if record.get("risk_subtype_id") == "ubb_scene_captcha":  # 可编程对抗-Web动态挑战特殊处理
                        manual_rule_id = {
                            "1000": "挑战成功", "1001": "挑战失败", "1002": "挑战超时", "1003": "强制挑战（前一次不成功）",
                        }.get(str(manual_rule_id), "")
                    suggestion = child.get(suggestion_key, "").format(
                        src_ip=record.get("src_ip", ""),
                        user_agent=record.get("user_agent", ""),
                        anomaly_subcategory_name=specific.get("name", ""),
                        anomaly_specific=record["anomaly_specific"],
                    )
                    detail = child.get(detail_key, "").format(
                        src_ip=record.get("src_ip", ""),
                        user_agent=record.get("user_agent", ""),
                        anomaly_subcategory_name=specific.get("name", ""),
                        anomaly_specific=record["anomaly_specific"],
                        manual_rule_id=manual_rule_id,
                        ubb_description=ubb_description,
                    )
                    break
            else:
                child = specific.get("child")
                if child and len(child) > 0:
                    suggestion = child[0].get(suggestion_key, "").format(
                        src_ip=record.get("src_ip", ""),
                        user_agent=record.get("user_agent", ""),
                        anomaly_subcategory_name=specific.get("name", ""),
                        anomaly_specific=record["anomaly_specific"],
                    )
                    detail = child[0].get(detail_key, "").format(
                        src_ip=record.get("src_ip", ""),
                        user_agent=record.get("user_agent", ""),
                        anomaly_subcategory_name=specific.get("name", ""),
                        anomaly_specific=record["anomaly_specific"],
                        manual_rule_id=manual_rule_id,
                        ubb_description=ubb_description,
                    )
                break

    # ddos不要三级，三级放到说明中
    if record.get("risk_type_id") == "ddos":
        detail = record.get("anomaly_specific", "")
        record["anomaly_specific"] = ""
    return detail, suggestion


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_detailed_list_details(request):
    """
    @check_rest_api_permission: __guest_function__
        获取风险清单-详情
    """
    try:
        req_body = ClassField("PostRiskDetailedListDetailsRequest", PostRiskDetailedListDetailsRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_id = "RISKREPORT_DETAILED_LIST_DETAILS:{0}".format(get_client_identifier(request))
    req_body_json = req_body.to_json()
    request_uuid = req_body_json.get("request_uuid")
    timestamp_ms = req_body_json.get("timestamp_ms")
    anomaly_subcategory = req_body_json.get("risk_subtype")
    subtype_index = req_body_json.get("subtype_index")

    sql = """SELECT
                concat(formatDateTime(toDateTime(timestamp_ms /1000), '%Y-%m-%d %H:%i:%S'),'.',toString(timestamp_ms % 1000)) AS datetime,
                anomaly_subcategory,
                anomaly_specific,
                concat(status, ' ', invalid_request_action) AS status,
                isp,
                /* 来源 */
                src_ip,
                multiIf(
                    is_local_ip = 1,
                    dictGetString('default.bgword', 'desc', tuple('内网')),
                    notEmpty(city),
                    city,
                    notEmpty(province),
                    province,
                    notEmpty(country),
                    country,
                    dictGetString('default.bgword', 'desc', tuple('未知'))
                ) AS location,
                src_port,
                user_agent,
                bot_type,
                if(fingerprint_browser_2 = '', mobile_device_fingerprint, fingerprint_browser_2) as fp,
                business_username,
                /* 目标 */
                dst_ip,
                dst_port,
                hostname,
                path,
                action,
                manual_rule_id,
                ubb_description,
            FROM
                {table_name}
            WHERE 
                request_uuid = '{request_uuid}'
            AND
                timestamp_ms = '{timestamp_ms}'
            AND
                has(anomaly_subcategory, '{anomaly_subcategory}')
        ;""".format(table_name=table_name, request_uuid=request_uuid,
                    timestamp_ms=timestamp_ms, anomaly_subcategory=anomaly_subcategory)

    ret, err_msg, records = query_from_sailfish_sql(sql,
                                                    sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                    session_business_id=session_business_id)
    record = {}
    if records:
        if len(records) > 1:
            for r in records:
                sub_types = r.get("anomaly_subcategory", [])
                if len(sub_types) >= subtype_index and sub_types[sub_types] == anomaly_subcategory:
                    record = r
                    break
        elif len(records) == 1:
            record = records[0]

    if record:
        # 填充风险等级
        desc = get_risk_subtype_desc_by_id(anomaly_subcategory)
        record.update({
            'risk_level': desc.get("level", "none"),
            'risk_type': desc.get('parent_name', ""),
            'risk_subtype': desc.get('name', anomaly_subcategory),
            'risk_subtype_id': anomaly_subcategory,
            'risk_type_id': desc.get('parent_id', "")
        })
        # 根据index取对应三级分类
        try:
            record["anomaly_specific"] = record.get("anomaly_specific", [""])[subtype_index]
        except Exception as e:
            logging.getLogger(__name__).error("anomaly_specific: {} not found in {}, source_specific: {}, offset: {}".format(
                anomaly_subcategory, record.get("anomaly_specific"), record.get("anomaly_subcategory"), subtype_index
            ))
            record["anomaly_specific"] = record.get("anomaly_specific", "")
            logging.getLogger(__name__).exception(e)
        # 处理详情&建议
        detail, suggestion = deal_with_suggestion_detail(record)
        record.update({
            "suggestion": suggestion,
            "detail": detail,
            "request_header": "",
            "request_body": "",
            "response_header": "",
            "response_body": "",

        })
    return api_ok_response(record)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_detailed_list_related_fp(request):
    """
    @check_rest_api_permission: __guest_function__
        获取风险清单-详情-关联指纹
    """
    try:
        req_body = ClassField("PostRiskDetailedListRelatedFpRequest", PostRiskDetailedListRelatedFpRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_fp_id = "RISKREPORT_DETAILED_LIST_RELATED_FP_FP:{0}".format(get_client_identifier(request))
    session_business_bu_id = "RISKREPORT_DETAILED_LIST_RELATED_FP_BU:{0}".format(get_client_identifier(request))
    req_body_json = req_body.to_json()
    fp = req_body_json.get("fp")
    timestamp_filter = get_timestamp_filter(req_body_json)

    fp_sql = """SELECT DISTINCT
                    src_ip,
                    if(fingerprint_browser_2 = '', mobile_device_fingerprint, fingerprint_browser_2) as fp
                FROM
                    {table_name}
                WHERE
                    fingerprint_browser_2 != '{fp}' AND mobile_device_fingerprint != '{fp}'
                AND (
                        (fingerprint_browser_2 is not null AND fingerprint_browser_2 != '')
                    OR
                        (mobile_device_fingerprint is not null AND mobile_device_fingerprint != '')
                )
                AND
                    src_ip GLOBAL IN (
                        SELECT DISTINCT src_ip FROM {table_name} WHERE (fingerprint_browser_2 = '{fp}' OR mobile_device_fingerprint = '{fp}')
                        AND src_ip is not null AND src_ip != '' AND ({timestamp_range}) LIMIT 1000
                    )
                AND ({timestamp_range})
                LIMIT 1000
        ;""".format(table_name=table_name, fp=fp, timestamp_range=timestamp_filter)
    bu_sql = """SELECT DISTINCT
                    if(fingerprint_browser_2 = '', mobile_device_fingerprint, fingerprint_browser_2) as fp,
                    business_username
                FROM
                    {table_name}
                WHERE
                    fingerprint_browser_2 != '{fp}' AND mobile_device_fingerprint != '{fp}'
                AND (
                        (fingerprint_browser_2 is not null AND fingerprint_browser_2 != '')
                    OR
                        (mobile_device_fingerprint is not null AND mobile_device_fingerprint != '')
                )
                AND
                    business_username GLOBAL IN (
                        SELECT DISTINCT business_username FROM {table_name} WHERE (fingerprint_browser_2 = '{fp}' OR mobile_device_fingerprint = '{fp}')
                        AND business_username is not null AND business_username != '' AND ({timestamp_range}) LIMIT 1000
                    )
                AND ({timestamp_range})
                LIMIT 1000
        ;""".format(table_name=table_name, fp=fp, timestamp_range=timestamp_filter)
    records = []
    ret, err_msg, fp_records = query_from_sailfish_sql(
        fp_sql, sailfish_user=botgate_user_to_sf_user(request.user.username), session_business_id=session_business_fp_id)
    if fp_records is not None:
        fp_dict = defaultdict(list)
        for item in fp_records:
            fp_dict[item["src_ip"]].append(item["fp"])
        records = [{"src_ip": k, "fp": v, "business_username": ""} for k, v in fp_dict.items()]

    ret, err_msg, bu_records = query_from_sailfish_sql(
        bu_sql, sailfish_user=botgate_user_to_sf_user(request.user.username), session_business_id=session_business_bu_id)
    if bu_records is not None:
        bs_dict = defaultdict(list)
        for item in bu_records:
            bs_dict[item["business_username"]].append(item["fp"])
        [records.append({"business_username": k, "fp": v, "src_ip": ""}) for k, v in bs_dict.items()]
    return api_ok_response(records)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_detailed_list_related_ip(request):
    """
    @check_rest_api_permission: __guest_function__
        获取风险清单-详情-关联IP
    """
    try:
        req_body = ClassField("PostRiskDetailedListRelatedIpRequest", PostRiskDetailedListRelatedIpRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_fp_id = "RISKREPORT_DETAILED_LIST_RELATED_IP_FP:{0}".format(get_client_identifier(request))
    session_business_bu_id = "RISKREPORT_DETAILED_LIST_RELATED_IP_BU:{0}".format(get_client_identifier(request))
    req_body_json = req_body.to_json()
    src_ip = req_body_json.get("src_ip")
    timestamp_filter = get_timestamp_filter(req_body_json)

    fp_sql = """SELECT DISTINCT
                    src_ip,
                    if(fingerprint_browser_2 = '', mobile_device_fingerprint, fingerprint_browser_2) as fp
                FROM
                    {table_name}
                WHERE
                    src_ip != '{src_ip}'
                AND
                    fingerprint_browser_2 GLOBAL IN (
                        SELECT DISTINCT fingerprint_browser_2 FROM {table_name} WHERE src_ip = '{src_ip}' AND fingerprint_browser_2 is not null AND fingerprint_browser_2 != '' AND ({timestamp_range}) LIMIT 1000
                    )
                OR
                    mobile_device_fingerprint GLOBAL IN (
                        SELECT DISTINCT mobile_device_fingerprint FROM {table_name} WHERE src_ip = '{src_ip}' AND mobile_device_fingerprint is not null AND mobile_device_fingerprint != '' AND ({timestamp_range}) LIMIT 1000
                    )
                AND ({timestamp_range})
                LIMIT 1000
        ;""".format(table_name=table_name, src_ip=src_ip, timestamp_range=timestamp_filter)
    bu_sql = """SELECT DISTINCT
                    src_ip,
                    business_username
                FROM
                    {table_name}
                WHERE
                    src_ip != '{src_ip}'
                AND
                    business_username GLOBAL IN (
                        SELECT DISTINCT business_username FROM {table_name} WHERE src_ip = '{src_ip}' AND business_username is not null AND business_username != '' AND ({timestamp_range}) LIMIT 1000
                    )
                AND ({timestamp_range})
                LIMIT 1000
        ;""".format(table_name=table_name, src_ip=src_ip, timestamp_range=timestamp_filter)

    records = []
    ret, err_msg, fp_records = query_from_sailfish_sql(
        fp_sql, sailfish_user=botgate_user_to_sf_user(request.user.username), session_business_id=session_business_fp_id)
    if fp_records is not None:
        fp_dict = defaultdict(list)
        for item in fp_records:
            fp_dict[item["fp"]].append(item["src_ip"])
        records = [{"fp": k, "src_ip": v, "business_username": ""} for k, v in fp_dict.items()]
    ret, err_msg, bu_records = query_from_sailfish_sql(
        bu_sql, sailfish_user=botgate_user_to_sf_user(request.user.username), session_business_id=session_business_bu_id)
    if bu_records is not None:
        bs_dict = defaultdict(list)
        for item in bu_records:
            bs_dict[item["business_username"]].append(item["src_ip"])
        [records.append({"business_username": k, "src_ip": v, "fp": ""}) for k, v in bs_dict.items()]
    return api_ok_response(records)
