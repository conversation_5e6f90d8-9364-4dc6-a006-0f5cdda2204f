# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.post_src_top_nrequest import PostSrcTopNRequest
from abd_utils.schemas.risk_report_filter import RiskReportFilter

#### 自动导入结束，不要在前面插入代码

import logging
from abd_utils.utils.func_datetime_convert import merge_echarts_format_result
from date_time_converter import get_start_end_time_by_time_filter, get_datetime_cycle
from asp_utils.sailfish_util import query_from_sailfish_sql, sailfish_restapi_request_with_large_result, \
    sailfish_restapi_query_task_list, QueryStatus, sailfish_restapi_cancel_query
from web_admin.view_lib import botgate_user_to_sf_user
from common import get_query_filter, get_risk_subtype_desc_by_id, get_risk_filter, get_timestamp_filter, \
    get_risk_subtype_list_by_parent_id, get_client_identifier, generate_query_related_id
from common import get_risk_subtype_list_by_levels, get_timestamp_filter, cancel_query_with_same_business_id, \
    is_none_risk_display
from detailed_list import add_risk_level_info
from date_time_converter import TIME_FILTER_ALL, TIME_FILTER_ALL_BEFORE_30_DAYS, TIME_FILTER_ALL_BEFORE_60_DAYS

table_name = "merge_access_log"
# 需要计算环比的内置时间筛选类型
TIME_FILTER_TODAY = 'from:/d,to:now'  # 今天
TIME_FILTER_YESTERDAY = 'from:-1d/d,to:/d'  # 昨天
TIME_FILTER_THIS_WEEK = 'from:/w,to:now'  # 本周
TIME_FILTER_LAST_WEEK = 'from:-1w/w,to:/w'  # 上周
TIME_FILTER_THIS_MONTH = 'from:/M,to:now'  # 本月
TIME_FILTER_LAST_MONTH = 'from:-1M/M,to:/M'  # 上月
TIME_FILTER_ALL = 'from:all,to:now'     # 所有时间
TIME_FILTER_ALL_BEFORE_30_DAYS = 'from:all,to:-30d'  # 30天之前
TIME_FILTER_ALL_BEFORE_60_DAYS = 'from:all,to:-60d'  # 60天之前

BUILTIN_TIME_FILTER = [TIME_FILTER_TODAY,
                       TIME_FILTER_YESTERDAY,
                       TIME_FILTER_THIS_WEEK,
                       TIME_FILTER_LAST_WEEK,
                       TIME_FILTER_THIS_MONTH,
                       TIME_FILTER_LAST_MONTH
                       ]

#
site_key = "if( notEmpty(server_alias),concat(server_alias, '@') || hostname,hostname) "

logger = logging.getLogger(__name__)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_abnormal_req_trend(request):
    """
    @check_rest_api_permission: __guest_function__
        获取异常请求个数趋势
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # session_business_id, query_id = generate_query_related_id("RiskReport_Req_Trend", request)
    session_business_id, query_id = generate_query_related_id(prefix='RiskReport_Req_Trend', request=request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    is_none_risk_displayed = is_none_risk_display()
    if is_none_risk_displayed:
        column_names = ["timestamp", "normal_req_count", "abnormal_req_count", "whitelist_req_count"]
    else:
        column_names = ["timestamp", "normal_req_count", "abnormal_req_count"]
    req_body_json = req_body.to_json()
    query_filter = get_query_filter(req_body_json)
    risk_filter = get_risk_filter(req_body_json)
    time_range = req_body_json.get('time_range', '')
    _, start_time, end_time = get_start_end_time_by_time_filter(time_range)
    timestamp_range = "timestamp >= {} and timestamp <{}".format(start_time, end_time)
    if is_none_risk_displayed:
        sql = """
            WITH ({site_key}) AS `site`
            SELECT 
                toUnixTimestamp(timestamp) as timestamp,
                SUM(CASE WHEN is_whitelist != 1 AND length(anomaly_category) = 0 THEN 1 ELSE 0 END) AS normal_req_count,
                SUM(CASE WHEN is_whitelist != 1 AND length(anomaly_category) != 0 THEN 1 ELSE 0 END) AS abnormal_req_count,
                SUM(CASE WHEN is_whitelist = 1 THEN 1 ELSE 0 END) AS whitelist_req_count
            FROM {table_name} 
            WHERE ({query_filter})   AND ({time_range}) AND ({risk_filter})
            GROUP BY timestamp
            ORDER BY timestamp 
        """.format(site_key=site_key, table_name=table_name, query_filter=query_filter,  risk_filter=risk_filter, time_range=timestamp_range)
    else:
        sql = """
                    WITH ({site_key}) AS `site`
                    SELECT 
                        toUnixTimestamp(timestamp) as timestamp,
                        SUM(CASE WHEN length(anomaly_category) = 0 THEN 1 ELSE 0 END) AS normal_req_count,
                        SUM(CASE WHEN is_whitelist != 1 AND length(anomaly_category) != 0 THEN 1 ELSE 0 END) AS abnormal_req_count
                    FROM {table_name} 
                    WHERE ({query_filter})   AND ({time_range}) AND ({risk_filter})
                    GROUP BY timestamp
                    ORDER BY timestamp 
                """.format(site_key=site_key, table_name=table_name, query_filter=query_filter, risk_filter=risk_filter,
                           time_range=timestamp_range)

    result, total_count = sailfish_restapi_request_with_large_result(sql,
                                                                     sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                                     session_business_id=session_business_id,
                                                                     query_id=query_id)
    if total_count > 0:
        records = result.get('records', None)
        display_precision, merged_echarts = merge_echarts_format_result(records, time_range, start_time, end_time)

        result = {
            "column_names": column_names,
            "display_precision": display_precision,
            "records": merged_echarts
        }

        return api_ok_response(result)
    else:
        result = {
            "column_names": column_names,
            "display_precision": None,
            "records": []
        }
        return api_ok_response(result)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_top_bots(request):
    """
    @check_rest_api_permission: __guest_function__
        获取TOP异常工具
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_id, query_id = generate_query_related_id("RiskReport_Top_Bots", request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    req_body_json = req_body.to_json()
    risk_filter = get_risk_filter(req_body_json)
    query_filter = get_query_filter(req_body_json)
    timestamp_filter = get_timestamp_filter(req_body_json)

    required_risk_levels = req_body_json.get("risk_levels", [])

    required_risk_subtype = req_body_json.get("risk_subtypes", [])
    temp = ','.join("'%s'" % element for element in required_risk_subtype)
    risk_subtype_filter = "has( [{temp}],risk_subtype)".format(temp=temp) if temp else "1"

    sql_top_bots = """
            WITH ({site_key}) AS `site`
            SELECT
              bot_type,
              risk_subtype,
              count(*) AS count
            FROM {table_name}
            ARRAY JOIN anomaly_subcategory as risk_subtype
            WHERE ({query_filter}) 
                AND ({risk_filter})
                AND ({risk_subtype_filter})
                AND (is_whitelist != 1)
                AND (length(anomaly_category) != 0)
                AND ({timestamp_filter})
                AND bot_type != ''
                AND has(`attack_type`, 'Bot')
            GROUP BY bot_type, risk_subtype
            ORDER BY count DESC
    """.format(table_name=table_name,
               query_filter=query_filter,
               risk_filter=risk_filter,
               risk_subtype_filter=risk_subtype_filter,
               timestamp_filter=timestamp_filter,
               site_key=site_key)

    result, total_count = sailfish_restapi_request_with_large_result(sql_top_bots,
                                                                     sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                                     session_business_id=query_id,
                                                                     query_id=query_id)
    temp = {}
    risk_level_dict = get_risk_subtype_list_by_levels()
    if total_count >= 0:
        records = result.get('records', [])
        for r in records:
            risk_subtype = r[1]
            bot_type = r[0]
            count = r[2]
            level = 'none'
            for key, value in risk_level_dict.items():
                if risk_subtype in value:
                    level = key
                    break
            if level in required_risk_levels:
                temp[bot_type] = temp.get(bot_type, 0) + count
        sorted_record = sorted(
            [[k, v] for k, v in temp.items()],
            key=lambda x: x[1],
            reverse=True
        )

        result = {
            "result": 0,
            "err_msg": "",
            "records": sorted_record
        }
    else:
        result = {
            "result": -1,
            "err_msg": result,
            "records": []
        }
    return api_ok_response(result)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_overview_latest_risks(request):
    """
    @check_rest_api_permission: __guest_function__
        获取最新风险列表
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_id, query_id = generate_query_related_id("RiskReport_latest_risks", request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    req_body_json = req_body.to_json()
    risk_types = req_body_json.get("risk_types", [])
    risk_subtypes = req_body_json.get("risk_subtypes", [])
    risk_levels = req_body_json.get("risk_levels", [])
    risk_filter = get_risk_filter(req_body_json)
    query_filter = get_query_filter(req_body_json)
    timestamp_filter = get_timestamp_filter(req_body_json)
    sql = """
            WITH {site_key} AS `site`
            SELECT 
                concat(formatDateTime(toDateTime(timestamp_ms /1000), '%Y-%m-%d %H:%i:%S'),'.',toString(timestamp_ms % 1000)) as datetime,
                anomaly_subcategory,
                site, 
                src_ip
            FROM {table_name}
            WHERE is_whitelist != 1
                AND (anomaly_category IS NOT NULL)
                AND (length(anomaly_category) != 0)
                AND ({risk_filter})
                AND ({query_filter})
                AND ({timestamp_filter})
            ORDER BY datetime DESC
            LIMIT {limit} OFFSET {offset}
        """.format(table_name=table_name,
                   risk_filter=risk_filter,
                   query_filter=query_filter,
                   timestamp_filter=timestamp_filter,
                   site_key=site_key,
                   limit=10,
                   offset=0)
    ret, err_msg, records = query_from_sailfish_sql(sql,
                                                    sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                    session_business_id=session_business_id,
                                                    query_id=query_id)
    if not ret:
        # 填充风险等级
        result = add_risk_level_info(records=records,
                                     risk_types=risk_types,
                                     risk_subtypes=risk_subtypes,
                                     risk_levels=risk_levels)
    else:
        result = []

    data = {"result": ret,
            "err_msg": err_msg,
            "records": result
            }

    return api_ok_response(data)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_src_top_n(request):
    """
    @check_rest_api_permission: __guest_function__
        获取TOP风险来源列表
    """
    try:
        req_body = ClassField("PostSrcTopNRequest", PostSrcTopNRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    request_body_json = req_body.to_json()
    src_type = request_body_json.get("src_type", "ip")
    count = request_body_json.get("count", 5)
    risk_filter = get_risk_filter(request_body_json)
    query_filter = get_query_filter(request_body_json)
    timestamp_range = get_timestamp_filter(request_body_json)

    session_business_id, query_id = generate_query_related_id("RiskReport_src_top_n_{src_type}".format(src_type=src_type), request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    if src_type == "ip":
        key_name = "src_ip"
    elif src_type == "fp":
        key_name = "if(fingerprint_browser_2 = '', mobile_device_fingerprint, fingerprint_browser_2)"
    elif src_type == "location":
        key_name = """
            multiIf(
                is_local_ip = 1,
                dictGetString('default.bgword', 'desc', tuple('内网')),
                notEmpty(city),
                city,
                notEmpty(province),
                province,
                notEmpty(country),
                country,
                dictGetString('default.bgword', 'desc', tuple('未知'))
          )
        """
    elif src_type == "path":
        key_name = "path"
    elif src_type == "site":
        key_name = """
            CASE 
                WHEN empty(server_alias) AND empty(hostname) THEN ''
                WHEN empty(server_alias) THEN concat('', hostname)
                WHEN empty(hostname) THEN concat(server_alias, '@(empty_host)')
                ELSE concat(server_alias, '@', hostname)
            END
    """
    elif src_type == "site_path":
        key_name = "if( notEmpty(path),concat(site, '') || path,site) "

    required_risk_subtype = request_body_json.get("risk_subtypes", [])
    temp = ','.join("'%s'" % element for element in required_risk_subtype)
    risk_subtype_filter = "has( [{temp}],risk_subtype)".format(temp=temp) if temp else "1"

    required_risk_levels = request_body_json.get("risk_levels", [])

    sql = """
        WITH 
            ({site_key}) AS `site`,
            ({key_name}) AS src
        SELECT src, risk_subtype,count() AS count
        FROM 
            merge_access_log
            ARRAY JOIN anomaly_subcategory as risk_subtype
        WHERE ({query_filter})
                AND ({risk_filter})
                AND ({risk_subtype_filter})
                AND ({timestamp_range}) 
                AND (is_whitelist != 1) 
                AND (length(anomaly_category) != 0)
        GROUP BY src,risk_subtype
        ORDER BY count DESC
        
    """.format(key_name=key_name, count=count, site_key=site_key, risk_subtype_filter=risk_subtype_filter,
               risk_filter=risk_filter, query_filter=query_filter, timestamp_range=timestamp_range)

    ret, err_msg, records = query_from_sailfish_sql(sql,
                                                    sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                    session_business_id=session_business_id,
                                                    query_id=query_id)
    temp = {}
    risk_dict = get_risk_subtype_list_by_levels()
    if not ret:
        for r in records:
            risk_subtype = r.get('risk_subtype', '')
            src = r.get("src", '')
            count = r.get("count", '')
            level = 'none'
            if src_type == "fp" and src == "":
                continue
            if src_type == "ip" and src == "":
                src = '未知'
            for risk_level, risk_list in risk_dict.items():
                if risk_subtype in risk_list:
                    level = risk_level
                    break
            r.update({"level": level})
            if level in required_risk_levels:
                temp[src] = temp.get(src, 0) + count
        result = sorted(
            [{"src": k, "count": v} for k, v in temp.items()],
            key=lambda x: x["count"],
            reverse=True
        )
        data = {
            "result": ret,
            "err_msg": err_msg,
            "records": result[:5]
        }
    else:
        data = {
            "result": ret,
            "err_msg": err_msg,
            "records": []
        }

    return api_ok_response(data)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_req_ratio_history(request):
    """
    @check_rest_api_permission: __guest_function__
        获取异常率历史同期对比
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # 取消前序请求
    session_business_id, query_id = generate_query_related_id(prefix='RiskReport_Req_Ration_History', request=request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    # 初始化返回数据结构
    data = {
        "result": 0,
        "err_msg": "",
        "records": []
    }
    # 获取请求参数
    request_body_json = req_body.to_json()
    query_filter = get_query_filter(request_body_json)
    risk_filter = get_risk_filter(request_body_json)
    datetime_cycle = get_datetime_cycle(request_body_json.get("time_range", ""))

    # 时间范围的起始时间为1970-01-01 08：00时， 无历史同期对比
    time_range = request_body_json.get("time_range", "")
    if time_range.startswith("from:all") or time_range.startswith("from:0"):
        return api_ok_response(data)

    # 构建SQL查询
    sql = """
        WITH 
            {site_key} AS `site`, 
            {datetime_cycle} 
        SELECT 
            t0, t1, t2, t3, t4, t5, t6,
            countIf(timestamp >= t1 AND timestamp < t0) AS total_t1_t0,
            countIf(notEmpty(anomaly_subcategory) AND timestamp >= t1 AND timestamp < t0) AS abnormal_t1_t0,

            countIf(timestamp >= t2 AND timestamp < t1) AS total_t2_t1,
            countIf(notEmpty(anomaly_subcategory) AND timestamp >= t2 AND timestamp < t1) AS abnormal_t2_t1,

            countIf(timestamp >= t3 AND timestamp < t2) AS total_t3_t2,
            countIf(notEmpty(anomaly_subcategory) AND timestamp >= t3 AND timestamp < t2) AS abnormal_t3_t2,

            countIf(timestamp >= t4 AND timestamp < t3) AS total_t4_t3,
            countIf(notEmpty(anomaly_subcategory) AND timestamp >= t4 AND timestamp < t3) AS abnormal_t4_t3,

            countIf(timestamp >= t5 AND timestamp < t4) AS total_t5_t4,
            countIf(notEmpty(anomaly_subcategory) AND timestamp >= t5 AND timestamp < t4) AS abnormal_t5_t4,

            countIf(timestamp >= t6 AND timestamp < t5) AS total_t6_t5,
            countIf(notEmpty(anomaly_subcategory) AND timestamp >= t6 AND timestamp < t5) AS abnormal_t6_t5
        FROM 
            merge_access_log
        WHERE ({query_filter}) AND (timestamp >= t6) AND ({risk_filter})
    """.format(
        query_filter=query_filter,
        risk_filter=risk_filter,
        site_key=site_key,
        datetime_cycle=datetime_cycle
    )

    # 执行查询
    ret, err_msg, records = query_from_sailfish_sql(
        sql,
        sailfish_user=botgate_user_to_sf_user(request.user.username),
        session_business_id=session_business_id,
        query_id=query_id
    )

    data["result"] = ret
    data["err_msg"] = err_msg

    if not ret and records:
        record = records[0] if records else {}
        # 定义时间段映射
        time_periods = [
            ("t6", "t6", "abnormal_t6_t5", "total_t6_t5"),
            ("t5", "t5", "abnormal_t5_t4", "total_t5_t4"),
            ("t4", "t4", "abnormal_t4_t3", "total_t4_t3"),
            ("t3", "t3", "abnormal_t3_t2", "total_t3_t2"),
            ("t2", "t2", "abnormal_t2_t1", "total_t2_t1"),
            ("t1", "t1", "abnormal_t1_t0", "total_t1_t0")
        ]
        result = []
        for time_key, time_point, abnormal_key, total_key in time_periods:
            timestamp = record.get(time_point)
            abnormal_count = record.get(abnormal_key, 0)
            total_count = record.get(total_key, 0)

            # 计算异常率
            ratio = 0.0
            if total_count > 0:
                ratio = round(abnormal_count * 100.0 / total_count, 1)

            result.append([timestamp, ratio])
        data["records"] = result

    return api_ok_response(data)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_statistics(request):
    """
    @check_rest_api_permission: __guest_function__
        获取风险统计数据
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    session_business_id, query_id = generate_query_related_id(prefix='RiskReport_Risk_Statistics', request=request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    req_body_json = req_body.to_json()
    required_time_range = req_body_json.get('time_range', '')
    required_risk_levels = sorted(req_body_json.get('risk_levels', []))
    required_risk_types = req_body_json.get('risk_types', [])
    required_risk_subtypes = req_body_json.get('risk_subtypes', [])
    query_filter = get_query_filter(req_body_json)
    risk_filter = get_risk_filter(req_body_json)
    timestamp_filter = get_timestamp_filter(req_body_json)

    sql = """
            WITH {site_key} AS `site`
            SELECT 
                is_whitelist, anomaly_category, anomaly_subcategory, count() AS access_count
            FROM merge_access_log
            WHERE 
                {risk_filter} AND
                {query_filter} AND 
                {timestamp_filter}
            GROUP BY is_whitelist, anomaly_category,anomaly_subcategory
            ORDER BY access_count DESC
        """.format(query_filter=query_filter, risk_filter=risk_filter,
                   timestamp_filter=timestamp_filter, site_key=site_key)

    ret, err_msg, records = query_from_sailfish_sql(sql,
                                                    sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                    session_business_id=session_business_id,
                                                    query_id=query_id)
    if ret:
        result = {
            "result": ret,
            "err_msg": err_msg,
            "records": [],
            "grade": 0,
            "high_risk_req_count": 0,
            "medium_risk_req_count": 0,
            "low_risk_req_count": 0,
            "no_risk_req_count": 0,
            "normal_req_count": 0,
            "abnormal_req_count": 0,
            "whitelist_req_count": 0,
            "total_req_count": 0
        }
    else:
        level_order = {'high': 4, 'medium': 3, 'low': 2, "none": 1, "unknown": 0}
        normal_req_count = 0
        whitelist_req_count = 0
        abnormal_req_count = 0
        high_risk_count = 0
        medium_risk_count = 0
        low_risk_count = 0
        none_risk_count = 0
        no_risk_count = 0
        risk_ranking_list = {}
        for rcd in records:
            # log_risk_grade = []
            is_whitelist = rcd.get("is_whitelist", 0)
            anomaly_subcategory = rcd.get("anomaly_subcategory", [])
            access_count = rcd.get("access_count", 0)
            if not is_whitelist and (not anomaly_subcategory):
                normal_req_count += access_count
            elif is_whitelist and (not anomaly_subcategory):
                whitelist_req_count += access_count
            elif not is_whitelist and anomaly_subcategory:
                abnormal_req_count += access_count
                for risk_sub in anomaly_subcategory:
                    desc = get_risk_subtype_desc_by_id(risk_sub)
                    level = desc.get("level", "unknown")
                    parent_id = desc.get("parent_id", "")
                    parent_name = desc.get("parent_name", "")
                    # log_risk_grade.append(level)
                    # 统计各类风险的个数
                    if required_risk_types and (parent_id not in required_risk_types):
                        continue
                    if required_risk_subtypes and risk_sub not in required_risk_subtypes:
                        continue
                    if level == "none":
                        none_risk_count += access_count
                    elif level == "low":
                        low_risk_count += access_count
                    elif level == "medium":
                        medium_risk_count += access_count
                    elif level == "high":
                        high_risk_count += access_count
                    # 生成风险列表
                    if level in required_risk_levels:
                        risk_subtype = [desc.get("name", ""), risk_sub]
                        risk_type = [parent_name, parent_id]
                        risk_count = risk_ranking_list.get(risk_sub, {}).get("access_count", 0) + access_count
                        risk_ranking_list.update({risk_sub: {
                            "level": level,
                            "risk_type": risk_type,
                            "risk_subtype": risk_subtype,
                            "access_count": risk_count
                        }})
                # 按access log日志个数来计算高中低风险个数
                # log_risk_grade = sorted(log_risk_grade, key=lambda x: level_order[x], reverse=True)
                # if log_risk_grade[0] in required_risk_levels:
                #     abnormal_req_count += access_count
                # if log_risk_grade[0] == "unknown":
                #     abnormal_req_count += access_count

        high_risk_count = high_risk_count if "high" in required_risk_levels else 0
        medium_risk_count = medium_risk_count if "medium" in required_risk_levels else 0
        low_risk_count = low_risk_count if "low" in required_risk_levels else 0
        no_risk_count = (whitelist_req_count + normal_req_count + none_risk_count) if ["high", "low", "medium",
                                                                                       ] == required_risk_levels else 0

        # 风险评级
        total_risk_count = (no_risk_count + high_risk_count + medium_risk_count + low_risk_count)
        risk_percent = float(high_risk_count + medium_risk_count + low_risk_count) / total_risk_count * 100 if total_risk_count > 0 else 0.00
        if risk_percent < 30:
            grade = "low"
        else:
            a = {"high": high_risk_count * 3, "medium": medium_risk_count * 2, "low": low_risk_count * 1}
            if high_risk_count == 0 and medium_risk_count == 0 and low_risk_count == 0:
                grade = "low"
            else:
                grade = max(a, key=lambda k: a[k])

        # 生成风险排序列表 按级别和个数降序排列
        sorted_list = sorted(risk_ranking_list.values(), key=lambda x: (-level_order[x['level']], -x['access_count']))
        # 准备返回值
        total_req_count = normal_req_count + whitelist_req_count + abnormal_req_count
        result = {
            "result": ret,
            "err_msg": err_msg,
            "records": sorted_list,

            "grade": grade,
            "high_risk_req_count": high_risk_count,
            "medium_risk_req_count": medium_risk_count,
            "low_risk_req_count": low_risk_count,
            "none_risk_count": none_risk_count,
            "no_risk_req_count": no_risk_count,
        }
        is_none_risk_displayed = is_none_risk_display()
        if is_none_risk_displayed:
            req_count_statistics = {
                "normal_req_count":  normal_req_count,
                "abnormal_req_count": abnormal_req_count,
                "whitelist_req_count":  whitelist_req_count,
                "total_req_count":  total_req_count
            }
        else:
            req_count_statistics = {
                "normal_req_count": (normal_req_count + whitelist_req_count),
                "abnormal_req_count": abnormal_req_count,
                "total_req_count": total_req_count
            }
        result.update(req_count_statistics)

        # 计算环比
        if (not required_time_range.startswith("from:all")) and (not required_time_range.startswith("from:0")):
            abnormal_req_percentage = abnormal_req_count / float(total_req_count) * 100 if total_req_count > 0 else 0
            delta = get_delta(req_body_json, abnormal_req_percentage, request)
            result.update({"delta": delta})
    # 返回结果
    return api_ok_response(result)

def get_delta(req_body_json, current_risk_percent, request):
    session_business_id, query_id = generate_query_related_id(prefix='RiskReport_Get_Delta', request=request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    time_range = req_body_json.get('time_range', '')
    last_start_time, start_time, end_time = get_start_end_time_by_time_filter(time_range)
    time_range_of_last_period = "timestamp >= {} and timestamp <{}".format(last_start_time, start_time)
    query_filter = get_query_filter(req_body_json)
    risk_filter = get_risk_filter(req_body_json)

    sql = """   
                WITH {site_key} AS `site`
                SELECT 
                    is_whitelist, anomaly_subcategory, count() AS access_count
                FROM merge_access_log
                WHERE 
                    {risk_filter}
                    AND {query_filter}
                    AND {timestamp_filter}
                GROUP BY is_whitelist, anomaly_subcategory
                ORDER BY access_count DESC
            """.format(query_filter=query_filter, risk_filter=risk_filter,
                       timestamp_filter=time_range_of_last_period, site_key=site_key)

    ret, err_msg, records = query_from_sailfish_sql(sql,
                                                    sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                    session_business_id=session_business_id,
                                                    query_id=query_id)

    if not ret:
        normal_req_count = 0
        whitelist_req_count = 0
        abnormal_req_count = 0
        for rcd in records:
            is_whitelist = rcd.get("is_whitelist", 0)
            anomaly_subcategory = rcd.get("anomaly_subcategory", [])
            access_count = rcd.get("access_count", 0)
            if not is_whitelist and (not anomaly_subcategory):
                normal_req_count += access_count
            elif is_whitelist and (not anomaly_subcategory):
                whitelist_req_count += access_count
            elif not is_whitelist and anomaly_subcategory:
                abnormal_req_count += access_count

        # 计算异常请求百分率
        total_req_count = normal_req_count + whitelist_req_count + abnormal_req_count
        last_risk_percent = float(abnormal_req_count) / total_req_count * 100 if total_req_count > 0 else 0.00
        delta = round(current_risk_percent - last_risk_percent, 2)
        return delta
    else:
        return 0.00

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_trend(request):
    """
    @check_rest_api_permission: __guest_function__
        获取风险个数趋势
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # 初始化常量和数据结构
    COLUMN_NAMES = ["timestamp", "high_risk_count", "medium_risk_count", "low_risk_count", "none_risk_count"]
    # 取消前序查询
    session_business_id, query_id = generate_query_related_id(prefix='RiskReport_Risk_Trend', request=request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    req_body_json = req_body.to_json()
    query_filter = get_query_filter(req_body_json)
    risk_filter = get_risk_filter(req_body_json)
    time_range = req_body_json.get('time_range', '')

    required_risk_levels = sorted(req_body_json.get('risk_levels', []))
    required_risk_subtypes = req_body_json.get('risk_subtypes', [])
    required_risk_types = req_body_json.get('risk_types', [])

    # 获取时间范围
    _, start_time, end_time = get_start_end_time_by_time_filter(time_range)
    timestamp_range = "timestamp >= {} AND timestamp < {}".format(start_time, end_time)

    # 构建SQL查询
    sql = """
        WITH ({site_key}) AS `site`
        SELECT 
            toUnixTimestamp(timestamp) as timestamp,
            is_whitelist,
            anomaly_subcategory, 
            count() as count
        FROM {table_name} 
        WHERE ({query_filter}) AND ({time_range}) AND ({risk_filter})
        GROUP BY timestamp, is_whitelist, anomaly_subcategory
        ORDER BY timestamp 
    """.format(
        site_key=site_key,
        table_name=table_name,
        query_filter=query_filter,
        risk_filter=risk_filter,
        time_range=timestamp_range
    )

    # 执行查询
    result, total_count = sailfish_restapi_request_with_large_result(
        sql,
        sailfish_user=botgate_user_to_sf_user(request.user.username),
        session_business_id=session_business_id,
        query_id=query_id
    )

    # 准备返回数据结构
    response_data = {
        "column_names": COLUMN_NAMES,
        "display_precision": None,
        "records": []
    }

    if total_count <= 0:
        return api_ok_response(response_data)

    # 预处理风险等级映射
    risk_level_map = {}
    records = result.get('records', [])

    # 聚合相同时间戳的数据
    timestamp_data = {}
    for r in records:
        timestamp = r[0]
        if timestamp not in timestamp_data:
            timestamp_data[timestamp] = [0, 0, 0, 0]  # high, medium, low, none

        is_whitelist = r[1]
        anomaly_subcategory = r[2]
        count = r[3]

        # 处理无风险情况
        if not anomaly_subcategory and (["high", "low", "medium"] == required_risk_levels):
            timestamp_data[timestamp][3] += count
            continue

        # 处理有风险情况
        if not is_whitelist:
            for risk_id in anomaly_subcategory:
                # 获取风险等级（使用缓存避免重复查询）
                if risk_id not in risk_level_map:
                    risk_desc = get_risk_subtype_desc_by_id(risk_id)
                    if required_risk_types and (risk_desc.get("parent_id", "") not in required_risk_types):
                        continue
                    if required_risk_subtypes and (risk_id not in required_risk_subtypes):
                        continue
                    risk_level_map[risk_id] = risk_desc.get("level", "none") if risk_desc else "none"

                level = risk_level_map[risk_id]

                # 累加到对应风险等级
                if (level == "high") and (level in required_risk_levels):
                    timestamp_data[timestamp][0] += count
                elif (level == "medium") and (level in required_risk_levels):
                    timestamp_data[timestamp][1] += count
                elif (level == "low") and (level in required_risk_levels):
                    timestamp_data[timestamp][2] += count
                elif level == "none" and (["high", "low", "medium"] == required_risk_levels):  # none
                    timestamp_data[timestamp][3] += count

    # 转换为列表格式
    temp = []
    for timestamp, counts in sorted(timestamp_data.items()):
        temp.append([timestamp] + counts)

    # 格式化为echarts所需格式
    display_precision, merged_echarts = merge_echarts_format_result(temp, time_range, start_time, end_time)

    # 更新响应数据
    response_data.update({
        "display_precision": display_precision,
        "records": merged_echarts
    })

    return api_ok_response(response_data)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("__guest_function__", "write")
def post_risk_ratio_history(request):
    """
    @check_rest_api_permission: __guest_function__
       风险个数百分比历史同期对比
    """
    try:
        req_body = RiskReportFilter(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    # 取消前序未完成的查询
    session_business_id, query_id = generate_query_related_id(prefix='RiskReport_Risk_Ratio_History', request=request)
    # cancel_query_with_same_business_id(session_business_id=session_business_id, sailfish_user='admin')

    request_body_json = req_body.to_json()
    query_filter = get_query_filter(request_body_json)
    risk_filter = get_risk_filter(request_body_json)

    # 时间范围的起始时间为1970-01-01 08：00时， 无历史同期对比
    time_range = request_body_json.get("time_range", "")
    if time_range.startswith("from:all") or time_range.startswith("from:0"):
        data = {
            "result": 0,
            "err_msg": "",
            "records": []
        }

        return api_ok_response(data)

    datetime_cycle = get_datetime_cycle(request_body_json.get("time_range", ""))
    required_risk_levels = sorted(request_body_json.get('risk_levels', []))
    required_risk_types = request_body_json.get('risk_types', [])
    required_risk_subtypes = request_body_json.get('risk_subtypes', [])

    sql = """
            WITH 
                {site_key} AS `site`, 
                {datetime_cycle} 
            SELECT 
                t0, t1, t2, t3, t4, t5, t6,
                risk_subtype,
                countIf(timestamp >= t1 AND timestamp < t0) AS count_t1_t0,
                countIf(timestamp >= t2 AND timestamp < t1) AS count_t2_t1,
                countIf(timestamp >= t3 AND timestamp < t2) AS count_t3_t2,
                countIf(timestamp >= t4 AND timestamp < t3) AS count_t4_t3,
                countIf(timestamp >= t5 AND timestamp < t4) AS count_t5_t4,
                countIf(timestamp >= t6 AND timestamp < t5) AS count_t6_t5
            FROM merge_access_log
            LEFT ARRAY JOIN anomaly_subcategory AS risk_subtype
            WHERE ({query_filter}) AND (timestamp >= t6) 
            GROUP BY risk_subtype 
        """.format(
        query_filter=query_filter,
        risk_filter=risk_filter,
        site_key=site_key,
        datetime_cycle=datetime_cycle
    )

    ret, err_msg, records = query_from_sailfish_sql(sql,
                                                    sailfish_user=botgate_user_to_sf_user(request.user.username),
                                                    session_business_id=session_business_id,
                                                    query_id=query_id)

    if not ret and records:
        # 提取时间点
        time_points = {
            't6': records[0]['t6'],
            't5': records[0]['t5'],
            't4': records[0]['t4'],
            't3': records[0]['t3'],
            't2': records[0]['t2'],
            't1': records[0]['t1']
        }

        # 初始化风险计数结构
        risk_levels = ["high", "medium", "low", "none"]
        time_periods = ["t1_t0", "t2_t1", "t3_t2", "t4_t3", "t5_t4", "t6_t5"]

        # 使用字典存储计数 {risk_level: [count_t1_t0, count_t2_t1, ...]}
        risk_counts = {level: [0] * len(time_periods) for level in risk_levels}

        # 获取风险分类映射
        risk_set_by_levels = get_risk_subtype_list_by_levels(required_risk_levels)
        tmp = []
        if required_risk_types:
            for risk_type in required_risk_types:
                tmp.extend(get_risk_subtype_list_by_parent_id(risk_type))
            for key, value in risk_set_by_levels.iteritems():
                risk_set_by_levels[key] = list(set(value).intersection(tmp))

        if required_risk_subtypes:
            for key, value in risk_set_by_levels.iteritems():
                risk_set_by_levels[key] = list(set(risk_set_by_levels[key]).intersection(required_risk_subtypes))

        # 处理每条记录
        for r in records:
            anomaly_subcategory = r.pop('risk_subtype', "")
            # 获取当前记录的计数列表
            counts = [
                r.pop('count_{period}'.format(period=period), 0)
                for period in time_periods
            ]

            # 确定风险等级
            risk_level = "none"
            for level in ["high", "medium", "low", "none"]:
                if anomaly_subcategory in risk_set_by_levels.get(level, set()):
                    risk_level = level
                    break

            # 累加到对应风险等级
            if risk_level in risk_counts:
                for i in range(len(time_periods)):
                    risk_counts[risk_level][i] += counts[i]

        # 计算各时间段总风险计数
        total_risk_counts = [0] * len(time_periods)
        for level in ["high", "medium", "low"]:
            for i, count in enumerate(risk_counts[level]):
                total_risk_counts[i] += count

        # 计算各时间段总计数（风险 + none）
        total_counts = [
            total_risk + risk_counts["none"][i]
            for i, total_risk in enumerate(total_risk_counts)
        ]

        # 计算风险比例并构建结果
        result = []
        time_keys = ["t6", "t5", "t4", "t3", "t2", "t1"]

        # 注意：time_periods 是从新到旧，而我们需要从旧到新
        for i in range(len(time_periods) - 1, -1, -1):
            period_index = len(time_periods) - 1 - i
            total = total_counts[period_index]
            risk_total = total_risk_counts[period_index]

            ratio = 0.0
            if total > 0:
                ratio = round(risk_total * 100.0 / total, 2)

            result.append([time_points[time_keys[i]], ratio])

    else:
        result = []

    data = {
        "result": ret,
        "err_msg": err_msg,
        "records": sorted(result, key=lambda x: x[0])
    }
    return api_ok_response(data)

