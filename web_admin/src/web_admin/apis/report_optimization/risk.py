# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.patch_risk_setting_request import PatchRiskSettingRequest
from abd_utils.schemas.post_reset_risk_setting_request import PostResetRiskSettingRequest
from abd_utils.schemas.post_risk_setting_display_none_risk_request import PostRiskSettingDisplayNoneRiskRequest
from abd_utils.schemas.post_risk_setting_request import PostRiskSettingRequest

#### 自动导入结束，不要在前面插入代码

import copy
from common import RiskConf, RISK_LEVEL_TRANS_MAP, is_none_risk_display
from django.utils.translation import ugettext_noop, ugettext as _
from service_mgr_rest_api import service_mgr_set_asp_config
from abd_utils.utils.func_base import ErrMsgException
from api_base.result_code import RC_BAD_PARAMETERS, RC_INTERNAL_ERROR, RC_OK
from web_admin.operation_log import operation_log


#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("Risk_Report_Setting", "read")
def get_risk_setting(request):
    """
    @check_rest_api_permission: Risk_Report_Setting
        获取风险设置
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    risk_setting = RiskConf.get_risk_settings()
    return api_ok_response(risk_setting)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("Risk_Report_Setting", "write")
def post_risk_setting(request):
    """
    @check_rest_api_permission: Risk_Report_Setting
        修改风险设置
    """
    try:
        req_body = ClassField("PostRiskSettingRequest", PostRiskSettingRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    except AttributeError:
        return api_error_response(RC_BAD_PARAMETERS, _("Invalid parameter"))

    req_body = req_body.to_json()
    try:
        new_high_ids = copy.deepcopy(req_body.get("high", []))
        new_medium_ids = copy.deepcopy(req_body.get("medium", []))
        new_low_ids = copy.deepcopy(req_body.get("low", []))
        new_none_ids = copy.deepcopy(req_body.get("none", []))
        if not new_high_ids and not new_medium_ids and not new_low_ids and not new_none_ids:
            return api_error_response(RC_BAD_PARAMETERS, "parameter required")

        diff_list = []
        old_risk_json = RiskConf.get_risk_settings()
        for k in old_risk_json:
            child = k.get("child", [])
            for c in child:
                c_id = c.get("id")
                c_level = c.get("level")
                if c_id in new_high_ids:
                    if c_level != "high":
                        diff_list.append("{}: {} -> {}".format(c.get("name"), RISK_LEVEL_TRANS_MAP[c.get("level")], RISK_LEVEL_TRANS_MAP["high"]))
                    new_high_ids.remove(c_id)
                elif c_id in new_medium_ids:
                    if c_level != "medium":
                        diff_list.append("{}: {} -> {}".format(c.get("name"), RISK_LEVEL_TRANS_MAP[c.get("level")], RISK_LEVEL_TRANS_MAP["medium"]))
                    new_medium_ids.remove(c_id)
                elif c_id in new_low_ids:
                    if c_level != "low":
                        diff_list.append("{}: {} -> {}".format(c.get("name"), RISK_LEVEL_TRANS_MAP[c.get("level")], RISK_LEVEL_TRANS_MAP["low"]))
                    new_low_ids.remove(c_id)
                elif c_id in new_none_ids:
                    if c_level != "none":
                        diff_list.append("{}: {} -> {}".format(c.get("name"), RISK_LEVEL_TRANS_MAP[c.get("level")], RISK_LEVEL_TRANS_MAP["none"]))
                    new_none_ids.remove(c_id)
        # 含有非法id
        if new_high_ids or new_medium_ids or new_low_ids or new_none_ids:
            return api_error_response(RC_BAD_PARAMETERS, _("Invalid parameter") + ": {}".format(",".join(new_high_ids+new_medium_ids + new_low_ids+new_none_ids)))

        if diff_list:
            ret_code, status, stat = service_mgr_set_asp_config({"risk_level/global": req_body}, 'IP', 'User')
            if ret_code != 0:
                operation_log(request, ugettext_noop('Report'), ugettext_noop('Modify'),
                              '1', {'msg': ugettext_noop('Failed to edit risk setting'), 'info': "\n".join(diff_list)})
                return api_error_response(RC_INTERNAL_ERROR, stat)
        operation_log(request, ugettext_noop('Report'), ugettext_noop('Modify'),
                      '0', {'msg': ugettext_noop('Successfully edit risk setting'), 'info': "\n".join(diff_list)})
        return api_ok_response(RC_OK)
    except ErrMsgException as e:
        return api_error_response(RC_BAD_PARAMETERS, e.message)


#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("Risk_Report_Setting", "write")
def post_reset_risk_setting(request):
    """
    @check_rest_api_permission: Risk_Report_Setting
        重置风险设置
    """
    try:
        req_body = ClassField("PostResetRiskSettingRequest", PostResetRiskSettingRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    try:
        service_mgr_set_asp_config({"risk_level/global": None}, 'IP', 'User')
        risk_settings = RiskConf.get_risk_settings()
        operation_log(request, ugettext_noop('Report'), ugettext_noop('Modify'),
                      '0', {'msg': ugettext_noop('Successfully reset risk setting to default')})
        return api_ok_response(risk_settings)
    except ErrMsgException as e:
        return api_error_response(RC_BAD_PARAMETERS, e.message)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("Risk_Report_Setting", "write")
def patch_risk_setting(request):
    """
    @check_rest_api_permission: Risk_Report_Setting
        修改风险设置
    """
    try:
        req_body = ClassField("PatchRiskSettingRequest", PatchRiskSettingRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    except AttributeError:
        return api_error_response(RC_BAD_PARAMETERS, _("Invalid parameter"))

    req_body = req_body.to_json()
    sub_id = req_body.get("id", "")
    new_level = req_body.get("level", "")
    if sub_id not in RiskConf.get_sub_risks(update_level=False):
        return api_error_response(RC_BAD_PARAMETERS, _("Invalid parameter"))
    if new_level not in RISK_LEVEL_TRANS_MAP.keys():
        return api_error_response(RC_BAD_PARAMETERS, _("Invalid parameter"))

    local_risks = RiskConf.get_local_risks()
    log_desc = "{name}: {old_level}->{new_level}"

    if not local_risks:
        local_risks = {"high": [], "medium": [], "low": [], "none": []}
        for k, v in RiskConf.get_sub_risks().items():
            old_level = v.get("level")
            if k == sub_id:
                local_risks[new_level].append(k)
                log_desc.format(name=v.get("name", sub_id), old_level=old_level, new_level=new_level)
                continue
            if old_level not in RISK_LEVEL_TRANS_MAP:
                local_risks["none"].append(k)
                continue
            local_risks[old_level].append(k)
    else:
        name = RiskConf.get_sub_risks(update_level=False).get(sub_id, {}).get("name", sub_id)
        for sub_level, sub_ids in local_risks.items():
            if sub_id in sub_ids:
                old_level = sub_level
                sub_ids.remove(sub_id)
                log_desc.format(name=name, old_level=old_level, new_level=new_level)
                break
        local_risks[new_level].append(sub_id)

    ret_code, status, stat = service_mgr_set_asp_config({"risk_level/global": local_risks}, 'IP', 'User')
    if ret_code != 0:
        operation_log(request, ugettext_noop('Report'), ugettext_noop('Modify'),'1',
                      {'msg': ugettext_noop('Failed to edit risk setting'), 'info': log_desc})
        return api_error_response(RC_INTERNAL_ERROR, stat)
    operation_log(request, ugettext_noop('Report'), ugettext_noop('Modify'),'0',
                  {'msg': ugettext_noop('Successfully edit risk setting'), 'info': log_desc})
    return api_ok_response(RC_OK)

#### 自动生成开始，不要手动修改以下的代码 ####
@check_rest_api_permission("Risk_Report_Setting", "read")
def get_risk_setting_display_none_risk(request):
    """
    @check_rest_api_permission: Risk_Report_Setting
        获取风险设置-是否展示无风险数据
    """
    #### 自动生成结束，不要手动修改以上的代码 ####

    return api_ok_response({"display_none_risk": is_none_risk_display()})

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("Risk_Report_Setting", "write")
def post_risk_setting_display_none_risk(request):
    """
    @check_rest_api_permission: Risk_Report_Setting
        修改风险设置-是否展示无风险数据
    """
    try:
        req_body = ClassField("PostRiskSettingDisplayNoneRiskRequest", PostRiskSettingDisplayNoneRiskRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    req_body = req_body.to_json()
    display_none_risk = req_body.get("display_none_risk")
    try:
        service_mgr_set_asp_config({"risk_setting/display_none_risk": display_none_risk}, 'IP', 'User')
        if display_none_risk:
            msg = ugettext_noop('Enable none risk display setting successfully')
        else:
            msg = ugettext_noop('Disable none risk display setting successfully')
        operation_log(request, ugettext_noop('Report'), ugettext_noop('Modify'),
                      '0', {'msg': msg})
        return api_ok_response(RC_OK)
    except ErrMsgException as e:
        return api_error_response(RC_BAD_PARAMETERS, e.message)
