# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.post_get_piis_list_files_download_request import PostGetPiisListFilesDownloadRequest
from abd_utils.schemas.post_get_piis_list_files_request import PostGetPiisListFilesRequest
from abd_utils.schemas.post_piis_list_files_export_request import PostPiisListFilesExportRequest

#### 自动导入结束，不要在前面插入代码

import urllib
import json
import logging
from collections import OrderedDict
import requests
from django.http import FileResponse
from django.utils.translation import ugettext_noop, ugettext

from asp_utils.aes_enc import gen_fix_access_token
from abd_utils.utils.func_base import is_sailfish_report, generate_csv_content
from abd_utils.utils.func_api_file import get_abd_download_file_url, make_file_key, FileProtocol
from abd_utils.utils.func_database import ChangeRole
from api_base.result_code import RC_INTERNAL_ERROR, RC_BAD_PARAMETERS, RC_NOT_EXIST
from api_base.base_api import api_file_response
from abd_utils.repositories.piis.api_file import ApiFileRepository
from abd_utils.utils.func_pii import get_pii_file_detect
from django.utils.translation import gettext as _
from web_admin.operation_log import operation_log
from asp_utils.utils import ugettext

EXPORT_FILE_LIST_FILE_NAME = 'file_list.csv'
MAX_EXPORT_COUNT = 100000

def export_file_list(query_result):
    export_fields = OrderedDict([
        ('filename',        ugettext('File Name')),
        ('file_type',       ugettext('File Format')),
        ('file_size',       ugettext('File Size')),
        ('endpoint',        'API'),
        ('app_site_name',   ugettext('Application')),
        ('upload_number',   ugettext('Uploads Count')),
        ('download_number', ugettext('Downloads Count')),
        ('pii_count',       ugettext('PIIs Unique Count')),
        ('pii_value',       ugettext('Sensitive information')),
        ('recent_time',     ugettext('Recently Discovered')),
    ])

    contents = generate_csv_content(query_result, export_fields.keys(),
                                       write_header=True, header_labels=export_fields)

    return contents

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_get_piis_list_files(request):
    """
    @check_rest_api_permission: API_Read
    获取文件敏感信息清单页面
    """
    try:
        req_body = ClassField("PostGetPiisListFilesRequest", PostGetPiisListFilesRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    if not is_sailfish_report():
        return api_error_response(RC_INTERNAL_ERROR, "report type error")
    try:
        req_body_json = req_body.to_json()
        with ChangeRole(request.user.username):
            data = ApiFileRepository().get_list(request.user.username, req_body_json)
            all_config = get_pii_file_detect()
            if all_config.get('max_size') != 0:
                data["allow_download"] = True
            else:
                data["allow_download"] = False
    except Exception as e:
        logging.error("get file list error: {}".format(e))
        return api_error_response(RC_INTERNAL_ERROR, "fetch sailfish data error")
    return api_ok_response(data)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_piis_list_files_export(request):
    """
    @check_rest_api_permission: API_Read
    导出文件敏感信息清单
    """
    try:
        req_body = ClassField("PostPiisListFilesExportRequest", PostPiisListFilesExportRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    if not is_sailfish_report():
        return api_error_response(RC_INTERNAL_ERROR, "report type error")
    try:
        req_body_json = req_body.to_json()
        with ChangeRole(request.user.username):
            req_body_json['page'] = 1
            req_body_json['page_size'] = MAX_EXPORT_COUNT
            file_list = ApiFileRepository().get_export_list(request.user.username, req_body_json)
        for item in file_list:
            item["pii_value"] = [json.dumps({"type": pii["pii_type"], "value": pii["pii_value"]},
                                            ensure_ascii=False) for pii in item["pii_list"]]
        contents = export_file_list(file_list)

    except Exception as e:
        logging.error("get export file list error: {}".format(e))
        return api_error_response(RC_INTERNAL_ERROR, "fetch sailfish export data error")
    pending_operation_log(request, _("Export"), _('export {} to {} file.').format(_("pii file"), 'csv'))
    return api_file_response(contents, EXPORT_FILE_LIST_FILE_NAME)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_get_piis_list_files_download(request):
    """
    @check_rest_api_permission: API_Read
    文件敏感信息清单下载文件
    """
    try:
        req_body = ClassField("PostGetPiisListFilesDownloadRequest", PostGetPiisListFilesDownloadRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    all_config = get_pii_file_detect()
    if all_config.get('max_size') == 0:
        return api_error_response(RC_NOT_EXIST, ugettext("File Already Expired"))

    if not is_sailfish_report():
        return api_error_response(RC_INTERNAL_ERROR, "report type error")
    url = get_abd_download_file_url()
    if url is None:
        return api_error_response(RC_INTERNAL_ERROR, "abd server died")
    req_body_json = req_body.to_json()
    file_name = req_body_json["filename"].encode('utf-8')
    file_type = req_body_json["file_type"]
    file_hash = req_body_json["file_hash"]
    file_path = req_body_json["file_path"].encode('utf-8')
    if not file_name or not file_type or not file_hash:
        return api_error_response(RC_BAD_PARAMETERS, ugettext("Invalid Request Parameter"))

    file_key = urllib.quote(make_file_key(file_name, file_type, file_hash))
    resp = requests.get(
        url + "?file_key={file_key}&file_protocol={file_protocol}".format(
            file_key=file_key, file_protocol=FileProtocol.HTTP),
        headers={'Authorization': gen_fix_access_token('abd')},
        verify=False, timeout=60, stream=True)
    if resp.status_code != 200:
        operation_log(request,
                      ugettext('API'),
                      ugettext("Download"),  # 操作
                      '1',  # 操作结果
                      {
                          'msg': ugettext_noop('Download {file_name} form {file_path}'),
                          'extra': {
                              'file_name': file_name,
                              'file_path': file_path,
                          }
                      })
        return api_error_response(RC_NOT_EXIST, ugettext("File Already Expired"))

    response = FileResponse(resp.raw)
    download_filename = urllib.quote(file_name)
    if "." not in download_filename:
        # not suffix
        download_filename = download_filename + "." + file_type
    response['Content-Type'] = "application/octet-stream"
    response['Content-Disposition'] = 'attachment; filename=' + download_filename
    operation_log(request,
                  ugettext('API'),
                  ugettext("Download"),  # 操作
                  '0',  # 操作结果
                  {
                      'msg': ugettext_noop('Download {file_name} form {file_path}'),
                      'extra': {
                          'file_name': file_name,
                          'file_path': file_path,
                      }
                  })
    return response
