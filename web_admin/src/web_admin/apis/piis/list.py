# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.post_piis_list_apis_request import PostPiisListApisRequest
from abd_utils.schemas.post_piis_list_count_request import PostPiisListCountRequest
from abd_utils.schemas.post_piis_list_export_request import PostPiisListExportRequest
from abd_utils.schemas.post_piis_list_set_pii_whitelist_request import PostPiisListSetPiiWhitelistRequest

#### 自动导入结束，不要在前面插入代码

import json
import logging

from abd_utils.repositories.piis.pii import PiiRepository
from api_base.base_api import api_file_response, api_word_tpl_response
from abd_utils.conf_api import ApiConf
from api_base.result_code import RC_INTERNAL_ERROR, RC_BAD_PARAMETERS, RC_MORE_INFO
from abd_utils.utils.func_database import ChangeRole
from asp_utils.utils import ugettext, get_language
from abd_utils.utils.func_whitelist import append_pii_whitelist_by_api
from abd_utils.utils.func_base import EXPORT_WORD_LIMIT, EXPORT_CSV_LIMIT
from asp_utils.utils import get_release_file
from django.utils.translation import gettext as _
from abd_utils.utils.func_base import is_sailfish_report
from abd_utils.repositories.piis.api_file import ApiFileRepository
from not_http import get_none_http_access_log_info
from asp_conf_ctrl import ConfDb
from asp_utils.utils import ugettext

PII_LIST_TPL = get_release_file('bin/abd_utils/docx_tpl/{}/pii_list_tpl.docx'.format(get_language()))
PII_LIST_EXPORT_WORD_FILE_NAME = 'pii_list.docx'
PII_LIST_EXPORT_CSV_FILE_NAME = 'pii_list.csv'

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_piis_list_apis(request):
    """
    @check_rest_api_permission: API_Read
    获取敏感信息清单页面
    search搜索，match支持fuzzy，full, prefix, suffix
    fuzzy是模糊匹配(包含字符) 
    full 完整匹配（字符串必须完全相等）
    prefix前缀匹配（以什么开头）
    suffix是后缀匹配（以什么结束）
    """
    try:
        req_body = ClassField("PostPiisListApisRequest", PostPiisListApisRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    
    with ChangeRole(request.user.username):
        repo = PiiRepository()
        condition = req_body.to_json()
        result = repo.get_piis_list(condition)

        return api_ok_response(result)

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_piis_list_export(request):
    """
    @check_rest_api_permission: API_Read
    导出敏感信息清单列表
    search搜索，match支持fuzzy，full, prefix, suffix
    fuzzy是模糊匹配(包含字符) 
    full 完整匹配（字符串必须完全相等）
    prefix前缀匹配（以什么开头）
    suffix是后缀匹配（以什么结束）
    """
    try:
        req_body = ClassField("PostPiisListExportRequest", PostPiisListExportRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        repo = PiiRepository()
        conditions = req_body.to_json()
        file_type = conditions['file_type']
        step = conditions['step']
        pending_operation_log(request, _("Export"), _('export {} to {} file.').format(_("pii list"), file_type if file_type != 'abd_csv' else 'csv'))
        if file_type == 'abd_csv':
            if step == 1:
                pii_count = repo.get_all_pii_count(conditions)
                if pii_count > EXPORT_CSV_LIMIT:
                    return api_error_response(RC_MORE_INFO, _('The number of items in the current list after '
                                                              'filtering is greater than {export_limit} items!, continue to '
                                                              'export {export_limit} items or cancel?').format(
                        export_limit=EXPORT_CSV_LIMIT))
        
            contents = repo.export_csv_piis_list(conditions)
            return api_file_response(contents, PII_LIST_EXPORT_CSV_FILE_NAME)
        
        elif file_type == 'word':
            if step == 1:
                pii_count = repo.get_all_pii_count(conditions)
                if pii_count > EXPORT_WORD_LIMIT:
                    return api_error_response(RC_MORE_INFO, _('The number of items in the current list after '
                                                              'filtering is greater than {export_limit} items!, continue to '
                                                              'export {export_limit} items or cancel?').format(
                        export_limit=EXPORT_WORD_LIMIT))

            context = repo.export_word_piis_list(conditions)
            return api_word_tpl_response(PII_LIST_TPL, context, PII_LIST_EXPORT_WORD_FILE_NAME)
        
        else:
            return api_error_response(RC_BAD_PARAMETERS, 'Invalid file type: {}'.format(file_type))
        

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Means", "write")
def post_piis_list_set_pii_whitelist(request):
    """
    敏感信息清单上设置检测白名单
    """
    try:
        req_body = ClassField("PostPiisListSetPiiWhitelistRequest", PostPiisListSetPiiWhitelistRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    with ChangeRole(request.user.username):
        json_body = req_body.to_json()
        api_id, pii_type = json_body['api_id'], json_body['pii_type'].encode('utf8')

        resp = append_pii_whitelist_by_api(api_id, pii_type)
        if resp['message']:
            return api_ok_response(resp)

        result = resp['api_infos']
        endpoint = result['endpoint']
        match_subpath = result['is_included_sub_path']

        # operation log
        if match_subpath:
            if not endpoint.endswith('/'):
                endpoint += '/'
            endpoint += '*'

        info_dict = {
            'API': result['app_site_name'] + endpoint,
            ugettext('PII Type'): pii_type
        }
        pending_operation_log(request, 'Add', ugettext('Add sensitive information white list'), info=json.dumps(info_dict, ensure_ascii=False))
        
        return api_ok_response('ok')

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_piis_list_count(request):
    """
    @check_rest_api_permission: API_Read
    获取敏感清单API、文件、其他协议数量
    """
    try:
        req_body = ClassField("PostPiisListCountRequest", PostPiisListCountRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####
    json_body = req_body.to_json()

    with ChangeRole(request.user.username):
        try:
            api_count = PiiRepository().get_api_piis_count(json_body['api_condition'])

            if is_sailfish_report() : #and ConfDb().get_deploy_mode() == ConfDb.DEPLOY_MODE_MIRROR
                query_data, not_http_count = get_none_http_access_log_info(req_body.not_http_condition)
                file_count = ApiFileRepository().get_total_count(request.user.username, json_body['file_condition'])
            else:
                file_count = 0
                not_http_count = 0
            
            result = {
                'api': api_count,
                'file': file_count,
                'not_http': not_http_count
            }

        except Exception as e:
            logging.error("get piis count error: {}".format(e))
            return api_error_response(RC_INTERNAL_ERROR, "Failed to get piis count.")
        
    return api_ok_response(result)

