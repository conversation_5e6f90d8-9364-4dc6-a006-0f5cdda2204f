# encoding: utf-8
# 此文件由 gen-server.py 创建，请根据当前 API 定义的接口更新此文件.
# 手动编写的代码，一定不要放在 自动创建的代码范围内

from django.views.decorators.csrf import csrf_exempt
from api_base.base_api import api_ok_response, api_bad_parameter_response, api_miss_parameter_response
from api_base.base_api import api_error_response, pending_operation_log
from api_base.base_validator import ValidationException, api_validate_int_value, api_validate_float_value, api_validate_bool_value,      api_validate_string_value, api_validate_array_value
from api_base.base_schema import ClassField, ListField, IntField, FloatField, BoolField, StringField
from web_admin.decorators import check_rest_api_permission
from abd_utils import enums
from web_admin.apis.request_lru_cache import RequestLruCache

from abd_utils.schemas.post_piis_list_not_http_export_request import PostPiisListNotHttpExportRequest
from abd_utils.schemas.post_piis_list_not_http_request import PostPiisListNotHttpRequest

#### 自动导入结束，不要在前面插入代码

import json
from asp_conf_ctrl import ConfDb
from asp_utils.sailfish_util import sailfish_restapi_request,sailfish_restapi_request_with_large_result
from collections import OrderedDict
from api_base.base_api import api_file_response
from abd_utils.utils.func_base import generate_csv_content, LEVEL_ALL_LABEL_TO_OPTION, escape_sql_str
from abd_utils.utils.func_datetime_convert import get_start_end_time_by_time_filter
from django.utils.translation import gettext as _
from asp_utils.utils import ugettext

EXPORT_NOT_HTTP_LIST_FILE_NAME = 'not_http_list.csv'

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_piis_list_not_http(request):
    """
    @check_rest_api_permission: API_Read
    """
    try:
        req_body = ClassField("PostPiisListNotHttpRequest", PostPiisListNotHttpRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    pii_type_filter = req_body.pii_type_filter
    result, total_count = get_none_http_access_log_info(req_body)
    req_body.page = 1
    req_body.page_size = 1000
    req_body.pii_type_filter = ""
    result_all, _ = get_none_http_access_log_info(req_body)

    not_http_list = combine_pii_data(result) #拼接数据

    data = {
        "pii_type_count": get_pii_type_count(result_all, pii_type_filter, total_count),
        "not_http_list": not_http_list,
        "total_count": total_count
    }

    return api_ok_response(data)

def combine_pii_data(result):
    combined_data = []
    for entry in result:
        combined_entry = {
            "protocol":entry["protocol"],
            "path":entry["path"],
            "src_ip":entry["src_ip"],
            "src_port":entry["src_port"],
            "dst_ip":entry["dst_ip"],
            "dst_port":entry["dst_port"],
            "pii_count":entry["pii_count"],
            "timestamp":entry["timestamp"],
            "pii_value": [json.dumps({"type":ptype,"value": pvalue}, ensure_ascii=False) for ptype, pvalue in zip(entry["pii_type"], entry["pii_value"])]
        }
        combined_data.append(combined_entry)
    return combined_data

def get_none_http_access_log_info(req_body, is_large=False):
    confdb = ConfDb()
    if confdb.get_value('report_type') == 'sailfish':
        _, start_time, end_time = get_start_end_time_by_time_filter(req_body.time_filter)
        select_clause = '''
            select
                protocol,
                path,
                src_ip,
                src_port,
                dst_ip,
                dst_port,
                pii_type,
                pii_value,
                pii_level,
                pii_count,
                timestamp
            '''
        if req_body.pii_type_filter:
            escaped_filter = escape_sql_str(req_body.pii_type_filter)
        else:
            escaped_filter = ""
        from_clause = '''
            from
                non_http_access_log
            where
                timestamp between {0} and {1}
                {2} -- Additional condition for pii_type filtering
                and pii_count != 0
            '''.format(start_time, end_time, "AND arrayExists(x -> x LIKE '%{}%', pii_type)".format(escaped_filter))

        filter = "= '{}'"
        if 'fuzzy' == req_body.search.match:
            filter = "ILIKE '%{}%'"
        elif 'prefix' == req_body.search.match:
            filter = "ILIKE '{}%'"
        elif 'suffix' == req_body.search.match:
            filter = "ILIKE '%{}'"
        elif 'not_contain' == req_body.search.match:
            filter = "NOT ILIKE '%{}%'"

        filter = filter.format(req_body.search.keyword)
        
        if  not req_body.search.keyword:
            search_conditions = ''
        elif req_body.search.match in ['not_contain']:
            search_conditions = '''AND (protocol {0} AND path {0} AND src_ip {0} 
                AND src_port::TEXT {0} AND dst_ip {0} AND dst_port::TEXT {0})'''.format(filter)
        else:
            search_conditions = '''AND (protocol {0} OR path {0} OR src_ip {0} 
            OR src_port::TEXT {0} OR dst_ip {0} OR dst_port::TEXT {0})'''.format(filter)

        order_limit_offset_clause = '''
            order by
                {0} {1}, timestamp_ms desc
            limit 1000
            offset 
                {2}
        '''.format(req_body.rank.field, req_body.rank.type, (req_body.page-1) * req_body.page_size)

        sql = select_clause + from_clause + search_conditions + order_limit_offset_clause
        query_count_sql = 'select count() ' + from_clause + search_conditions

        if is_large:
            result, _= sailfish_restapi_request_with_large_result(
                sql)
        else:
            result, _= sailfish_restapi_request(
                sql, count=req_body.page_size) 
        total_count = sailfish_restapi_request(query_count_sql)[0]["records"][0][0]
        if total_count <= 0:
            return [], total_count
        query_result = []
        for record in result['records']:
            query_result.append(dict(zip(result['column_names'], record)))
        return query_result, total_count
    else:
        return [], 0

def get_pii_type_count(result_all, pii_type_filter, pii_type_filter_count):
    result = {'high': {}, 'middle': {}, 'low': {}}

    # {'high': {'email': {'pii_type': 'email', 'count': 1}, 'phone': {'pii_type': 'phone', 'count': 1}}, 'middle': {}, 'low': {}}
    for item in result_all:
        pii_types = item['pii_type']
        pii_levels = item['pii_level']

        pii_type_set = set()
        for pii_type, pii_level in zip(pii_types, pii_levels):
            if pii_type in pii_type_set:
                continue
            
            level = LEVEL_ALL_LABEL_TO_OPTION[pii_level]

            pii_type_set.add(pii_type)
            pii_type_item = result.get(level, {})
            pii_itme = pii_type_item.get(pii_type, None)
            
            if not pii_itme:
                pii_type_item[pii_type] = {
                    "pii_type": pii_type,
                    "count": 1
                } 
            else:
                pii_itme['count'] += 1
            
            if pii_type_filter == pii_type:
                pii_type_item[pii_type]['count'] = pii_type_filter_count

    for k, v in result.items():
        result[k] = list(v.values())

    return result

def export_not_http_list(query_result):
    export_fields = OrderedDict([
        ('protocol',     ugettext('Protocol')),
        ('path',         ugettext('Resource Locator')),
        ('src_ip',       ugettext('msg_id_source_ip')),
        ('src_port',     ugettext('msg_id_source_port')),
        ('dst_ip',       ugettext('Destination IP')),
        ('dst_port',     ugettext('Destination Port')),
        ('pii_count',    ugettext('PIIs Unique Count')),
        ('pii_value',    ugettext('Sensitive information')),
        ('timestamp',    ugettext('Call Time')),
    ])

    contents = generate_csv_content(query_result, export_fields.keys(),
                                       write_header=True, header_labels=export_fields)

    return contents

#### 自动生成开始，不要手动修改以下的代码 ####
@csrf_exempt
@check_rest_api_permission("API_Read", "write")
def post_piis_list_not_http_export(request):
    """
    @check_rest_api_permission: API_Read
    """
    try:
        req_body = ClassField("PostPiisListNotHttpExportRequest", PostPiisListNotHttpExportRequest).validate(request.body) if request.body else None
    except ValidationException as e:
        return api_error_response(e.code, e.message)
    #### 自动生成结束，不要手动修改以上的代码 ####

    req_body.page = 1
    result, total_count = get_none_http_access_log_info(req_body,True)
    if total_count <= 0:
        result = []

    result_tmp = combine_pii_data(result)  #拼接数据
    contents = export_not_http_list(result_tmp)
    pending_operation_log(request, _("Export"), _('export {} to {} file.').format(_("not http"), "csv"))
    return api_file_response(contents, EXPORT_NOT_HTTP_LIST_FILE_NAME)