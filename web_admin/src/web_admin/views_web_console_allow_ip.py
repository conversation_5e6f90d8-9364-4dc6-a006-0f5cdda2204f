# encoding=utf-8

import json
import logging
import re

from django.core.validators import validate_ipv4_address, validate_ipv6_address
from django.http import HttpResponse
from django.utils.translation import ugettext_noop

from asp_utils.CommonLibs import compress_IP
from web_admin.Conf_Webconsole import WebconsoleConf
from web_admin.operation_log import operation_log
from web_admin.view_lib import login_required

from web_admin.decorators import check_permission

import _strptime # workaround for https://bugs.python.org/issue7980
from datetime import datetime

CONTENT_TYPE_JSON = 'application/json'


@login_required
@check_permission('Account_Default_Configurations', 'write')
def web_console_allow_ip(request):
    """update web_console_allow_ip list"""
    data = {'save_success': False, 'error_msg': ''}
    wc_conf = WebconsoleConf()
    op_code = '1'
    old_ips = wc_conf.get_web_console_allow_ip() or []
    old_wcai_status = wc_conf.get_web_console_allow_ip_status()  # wcai -> web console allow ip
    new_wcai_status = None
    allow_ip = []
    try:
        if request.method == "POST":
            def validate_time(begin, end, format="%Y-%m-%d %H:%M:%S"):
                if begin and end:
                    try:
                        time_begin, time_end = datetime.strptime(begin, format), datetime.strptime(end, format)
                        time_min = datetime.strptime("1970-01-01 00:00:00", format)
                        time_max = datetime.strptime("2038-01-01 00:00:00", format)
                        if time_begin >= time_end:
                            data['error_msg'] += "time_begin >= time_end !"
                            return False, False
                        if time_begin < time_min:
                            data['error_msg'] += "time_begin < min 1970 !"
                            return False, False
                        if time_end > time_max:
                            data['error_msg'] += "time_max > max 2038 !"
                            return False, False
                        return begin.replace(' ', 'T'), end.replace(' ', 'T')
                    except ValueError:
                        logging.error("Date format error")
                        data['error_msg'] += "Date format error!"
                        return False, False
                elif begin and not end:
                    data['error_msg'] += "time_end not found!"
                    return False, False
                elif end and not begin:
                    data['error_msg'] += "time_begin not found!"
                    return False, False
                else:
                    return False, False
            post_data = json.loads(request.body)
            for i in post_data['allowIP']:
                time_begin, time_end = validate_time(i.get('time_begin'), i.get('time_end'))
                if i['type'] == 'ip':
                    if ':' in i['ip']:
                        validate_ipv6_address(i['ip'])
                        if not (0 <= int(i['mask']) <= 128):
                            continue
                        i['ip'] = compress_IP(i['ip'])
                    else:
                        validate_ipv4_address(i['ip'])
                        validate_ipv4_address(i['mask'])
                    ip_details = {'type': i['type'], 'ip': i['ip'], 'mask': i['mask'], 'comment': i['comment']}
                    if time_begin:
                        ip_details['time_begin'] = time_begin
                    if time_end:
                        ip_details['time_end'] = time_end
                    allow_ip.append(ip_details)
                else:
                    if not re.match(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$', i['mac']):
                        raise ValueError('Invalid MAC address format')
                    i['mac'] = i['mac'].replace('-', ':').lower()
                    mac_details = {'type': i['type'], 'mac': i['mac'], 'comment': i['comment']}
                    if time_begin:
                        mac_details['time_begin'] = time_begin
                    if time_end:
                        mac_details['time_end'] = time_end
                    allow_ip.append(mac_details)
            new_wcai_status = post_data['enabled']
            WebconsoleConf().set_web_console_allow_ip_status(new_wcai_status)
            if WebconsoleConf().set_web_console_allow_ip(allow_ip) == 0:
                op_code = '0'
                data['save_success'] = True
    except:     # noqa
        pass

    if new_wcai_status != old_wcai_status:
        if new_wcai_status:
            operation_log_msg = ugettext_noop('Enabled login source address control')
            op_action = ugettext_noop('Enable')
        else:
            operation_log_msg = ugettext_noop('Disabled login source address control')
            op_action = ugettext_noop('Disable')
        operation_log(request, ugettext_noop('Access'), op_action, op_code, {
            'msg': ugettext_noop(operation_log_msg)
        })

    if allow_ip != old_ips:
        def process_flat_ips(ips):
            if ips['type'] == 'mac':
                op_str = '--MAC  {} {} {} {}'.format(
                    ips['mac'], ips.get('time_begin', ''), ips.get('time_end', ''), ips['comment'])
            else:
                op_str = '--IP  {}/{} {} {} {}'.format(
                    ips['ip'], ips['mask'], ips.get('time_begin', ''), ips.get('time_end', ''), ips['comment'])
            return op_str

        old_data = '\n'.join([process_flat_ips(i) for i in old_ips])
        new_data = '\n'.join([process_flat_ips(i) for i in allow_ip])
        detail_info = 'OLD_DATA:\n{}\nNEW_DATA:\n{}'.format(old_data, new_data)
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), op_code, {
            'msg': ugettext_noop('Modified login source address control'),
            'info': detail_info,     # doubleclick detail
        })
    return HttpResponse(json.dumps(data), content_type=CONTENT_TYPE_JSON)

