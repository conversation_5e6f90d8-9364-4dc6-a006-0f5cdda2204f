# -*- coding:utf-8 -*-

# This module is related to waf strategy
#@author: weihe.chen

import hashlib
import json
import logging
import os
import re
import shutil
import zipfile
import copy
import datetime
import random
import socket


from asp_conf_ctrl import AspConfCtrl, ConfDb
from asp_utils.license_info import LicenseInfo
import django_views_static as static #Fix CVE-2017-7234 DAP-24200

from django.views.decorators.http import require_http_methods
from django.http import HttpResponse, HttpResponseNotFound, JsonResponse
from django.utils.translation import ugettext as _, ugettext_noop

from web_admin.decorators import has_dm_permission, adv_operation_code_required, check_permission, check_permission_list
from web_admin.operation_log import operation_log
from web_admin.view_lib import login_required
from web_admin.views import base_render_to_response, CONTENT_TYPE_JSON, CONTENT_TYPE_PLAIN

import asp_utils.waf_util as waf_util
from asp_utils.waf_util import get_waf_conf_path
from asp_utils.utils import get_version, get_release_file, get_product_type
from web_admin.Conf_Waf import WafConf, DuplicateNameError, RegionalAccessControl, ResLeech, BruteForce, \
    WeakPasswordCheck, WafStrategyCtrl, get_max_strategy_id, validate_rule, record_operation_log, CCAttackProtection, \
    GlobalCustomRule, is_valid_update_config, get_license_status, HttpProtocol, VulnerabilityScanProtection, \
    XMLAttackProtection, CookieTamperProtection, HoneyPotCheck, WafError, covert_server_key_to_host, filter_id_for_update_list, \
    IllegalDownloadProtection,ParameterDetection,GlobalWafWhitelist,WafConfigCtrl, GlobalCsrfProtection
from web_admin.Conf_Waf import LLMPromptListConfig,LLMPromptInjectionProtection,LLMSensitiveDetection
from web_admin.Conf_Nginx import NginxConf
from service_mgr_rest_api import service_mgr_set_command

from module_ctrls.waf_rule_ctrl import WafStrategyConf, WafRuleSetUpgrade, WafTamperProtection, \
    DefaultAdaptionDecodeConfig, DefaultWafBlockConfig, DefaultUploadInflateConfig, DefaultWafForApiRequestOnly, \
    WAFGrayBox
from module_ctrls.upgrade.cipher_wafruleset import get_waf_rule_info
from asp_utils.CommonLibs import valid_IP_or_CIDR, valid_IPv6, valid_IPv4
from weak_pwd_list import WEAK_PWD_LIST
from web_admin.site_acl_util import get_sites_4_user

import resource_file
from asp_utils.utils import RESULT ,get_sync_file_path
from web_admin.Conf_Webconsole import WebconsoleConf
from Conf_Base import BaseConf

logger = logging.getLogger('view_waf')

# =============================== waf ===================================================


def get_max_site_strategy_id():
    confDb = ConfDb()
    conf_site_strategies = confDb.get_value('nginx/waf/site_strategies', [])
    site_strategy_ids = [item.get('id') for item in conf_site_strategies]
    max_id = waf_util.CUSTOM_STRATEGY_MIN_ID
    if len(site_strategy_ids) != 0:
        max_id = max(site_strategy_ids)

    return max_id

def get_max_module_strategy_id(module_type):
    confDb = ConfDb()
    strategies = confDb.get_value('nginx/waf/module_strategies', {}).get(module_type, {}).get('strategies', [])
    strategy_ids = [item.get('id') for item in strategies]
    max_id = waf_util.CUSTOM_STRATEGY_MIN_ID
    if len(strategy_ids) != 0:
        max_id = max(strategy_ids)

    return max_id


def check_site_name_exist(name,id):
    confDb = ConfDb()
    site_strategies = confDb.get_value('nginx/waf/site_strategies', [])
    for strategy in site_strategies:
        if strategy.get('name')==name and strategy.get('id')!=id:
            return True
    return False

def check_module_name_exist(waf_module,name,id):
    confDb = ConfDb()
    if 'syntax_interception' in waf_module:
        waf_module_strategies = confDb.get_value('nginx/waf/syntax_strategies', {}).get(waf_module, {}).get(
            "strategies", [])
    else:
        waf_module_strategies = confDb.get_value('nginx/waf/module_strategies', {}).get(waf_module,{}).get("strategies",[])

    for strategy in waf_module_strategies:
        if strategy.get('name')==name and strategy.get('id')!=id:
            return True
    return False

def is_valid_site_strategy(strategy):
    if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
        raise Exception('Invalid site strategy :{}'.format(strategy))
    if check_site_name_exist(strategy.get('name'),strategy.get('id')):
        raise DuplicateNameError(_('Strategy name is existed.'))

    check_func_dict = {
        "id": (lambda x: waf_util.valid_number_range(x, max_v=1999, min_v=-1)),
        "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
        "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
        "template": (lambda x: waf_util.valid_string_length(x, length=2, min_v=1)),
        "module_strategies": (lambda x: waf_util.valid_dict(x, 20, 12))
    }

    validate_rule(strategy, check_func_dict, _('site_strategy'))


def is_valid_module_strategy(strategy):
    if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
        raise Exception('Invalid module strategy :{}'.format(strategy))
    if check_module_name_exist(strategy.get("waf_module"),strategy.get('name'),strategy.get('id')):
        raise DuplicateNameError('Strategy name is existed.')

    check_func_dict = {
        "id": (lambda x: waf_util.valid_number_range(x, max_v=1999, min_v=-1)),
        "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
        "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                        verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
        "template": (lambda x: waf_util.valid_number_range(int(x), max_v=99, min_v=1)),
        "waf_module": (lambda x: waf_util.valid_string_length(x, length=50, min_v=20)),
        "rules": (lambda x: waf_util.valid_rules(x)),
        "special_config": (lambda x: waf_util.valid_module_special_config(strategy.get("waf_module"), x))
    }

    validate_rule(strategy, check_func_dict, _('module_strategy'))


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Global_Whitelist', 'read')
def add_waf_id_whitelist(request):
    log_from = ugettext_noop('WAF')
    try:
        result = {
            'save_success': False,
            'error_msg': _(
                'POST Only')
        }

        if request.method != 'POST':
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        global_whitelist = GlobalWafWhitelist()
        result = global_whitelist.modify_global_whitelist(request)
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    except Exception as e:
        logger.exception(e)
        result = {
            'save_success': False,
            'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
        }
        operation_log(request, log_from, ugettext_noop('Modify'), '1',
                      {'msg': ugettext_noop('Global WAF whitelist saved failed')})

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

def update_hosts(site_strategies,request=None):
    confDb = ConfDb()
    site_strategy_used_list = confDb.get_value('nginx/waf/site_strategy_used_list', {})
    allow_sites_key = []

    current_role = WebconsoleConf().get_user_role(request.user.username)
    is_display_all = current_role != 'Operator'
    if not is_display_all and request is not None:
        allow_sites_key = [item.get("key") for item in get_sites_4_user(request.user.username)]
    for item in site_strategies:
        server_keys = site_strategy_used_list.get("{}".format(item.get('id')), [])
        if not is_display_all and request is not None:
            server_keys = [item2 for item2 in server_keys if item2 in allow_sites_key]
        item['hosts'] = covert_server_key_to_host(server_keys)

def get_syntax_strategy(result):
    default_syntax_strategies = waf_util.get_waf_default_syntax_strategies()
    confDb = ConfDb()

    all_syntax_strategy_used_list = confDb.get_value('nginx/waf/syntax_strategy_used_list', {})
    for module, info in default_syntax_strategies.items():
        syntax_strategy_used_list = all_syntax_strategy_used_list.get(module, {})
        del info['include_file']
        strategies = info.get('strategies')
        for strategy in strategies:
            strategy['site_strategies'].extend(syntax_strategy_used_list.get('{}'.format(strategy.get('id')), []))

    custom_site_strategies = result.get('custom_site_strategies',[])
    for site_strategy in custom_site_strategies:
        syntax_strategies = site_strategy.get('syntax_strategies', {})
        for module in default_syntax_strategies.keys():
            if module not in syntax_strategies.keys():
                syntax_strategies[module] = {'enabled': False, 'strategy': '1'}
        site_strategy['syntax_strategies'] = syntax_strategies

    custom_syntax_strategies  = confDb.get_value('nginx/waf/syntax_strategies', {})
    for module, info in custom_syntax_strategies.items():
        used_list = all_syntax_strategy_used_list.get(module, {})
        syntax_strategies = info.get('strategies')
        for syntax_strategy in syntax_strategies:
            syntax_strategy['site_strategies'] = used_list.get('{}'.format(syntax_strategy.get('id')), [])

    result['default_syntax_strategies'] = default_syntax_strategies
    result['custom_syntax_strategies'] = custom_syntax_strategies
    return result

def get_strategy_id_for_default_com(conf_db):
    waf_strategy_conf = WafStrategyConf(conf_db)
    site_strategy_used_list = waf_strategy_conf.get_all_site_strategy_used_list()

    strategy_id = 1
    if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':
        strategy_id = 9

    for id, hosts in site_strategy_used_list.items():
        if "default.com:*" in hosts:
            strategy_id = id
            break
    return strategy_id

def update_general_strategy_special(conf_db,result):
    # 镜像部署下，有默认站点的特殊处理逻辑
    #add default.com:* when in mirror_mode
    is_full_flow_collection_enabled = conf_db.get_value('nginx/httpcap_all_ports', False)
    if is_full_flow_collection_enabled:
        strategy_id = get_strategy_id_for_default_com(conf_db)

        # 界面应用站点展示使用，使用字段为default_site_strategies/custom_site_strategies中对应策略的hosts字段
        default_site_strategies=result['default_site_strategies']
        for item in default_site_strategies:
            if int(item.get('id',0))==int(strategy_id):
                item["hosts"].append('http://default.com:*')
                break
        custom_site_strategies=result['custom_site_strategies']
        for item in custom_site_strategies:
            if int(item.get('id',0))==int(strategy_id):
                item["hosts"].append('http://default.com:*')
                break
        result['default_site_strategies'] = default_site_strategies
        result['custom_site_strategies'] = custom_site_strategies

def is_need_get_default_site(username):
    current_role = WebconsoleConf().get_user_role(username)
    # 只有管理员和审计员以及自定义角色可以看到default站点的信息。操作员不可看。
    if current_role in ['Operator', 'StatisticViewer']:
        return False
    return True


def get_general_strategy(result, confDb,request=None):
    default_module_strategies = waf_util.get_waf_default_module_strategies()
    default_site_strategies = waf_util.get_waf_default_site_strategies()

    update_hosts(default_site_strategies,request)

    all_module_strategy_used_list = confDb.get_value('nginx/waf/module_strategy_used_list', {})

    for module, info in default_module_strategies.items():
        module_strategy_used_list = all_module_strategy_used_list.get(module, {})
        del info['include_file']
        module_strategies = info.get('strategies')
        for module_strategy in module_strategies:
            module_strategy['site_strategies'].extend(module_strategy_used_list.get(
                '{}'.format(module_strategy.get('id')), []))

    custom_site_strategies = confDb.get_value('nginx/waf/site_strategies', [])
    # if upgrade from 21.07 which doesn't contain new module in 21.09, need add new module to custom_site_strategies
    for custom_site_strategy in custom_site_strategies:
        for module in default_module_strategies.keys():
            module_strategies = custom_site_strategy.get('module_strategies')
            if module not in module_strategies.keys():
                module_strategies[module] = {'enabled': False, 'strategy': custom_site_strategy.get('template')}

    custom_module_strategies = confDb.get_value('nginx/waf/module_strategies', {})
    update_hosts(custom_site_strategies,request)

    for module, info in custom_module_strategies.items():
        module_strategy_used_list = all_module_strategy_used_list.get(module, {})
        module_strategies = info.get('strategies')
        for module_strategy in module_strategies:
            module_strategy['site_strategies'] = module_strategy_used_list.get(
                '{}'.format(module_strategy.get('id')), [])

    result['default_site_strategies'] = default_site_strategies
    result['default_module_strategies'] = default_module_strategies
    result['custom_site_strategies'] = custom_site_strategies
    result['custom_module_strategies'] = custom_module_strategies

    if request is not None:
        if not is_need_get_default_site(request.user.username):
            return
    update_general_strategy_special(confDb,result)




def update_site_info_special(result,strategies_list,request=None):
    conf_db = ConfDb()
    if strategies_list == WafConfigCtrl.power_strategies\
            or strategies_list == WafConfigCtrl.protocol_strategies:
        return

    if request is not None:
        if not is_need_get_default_site(request.user.username):
            return
    # 镜像部署下，有默认站点的特殊处理逻辑
    #add default.com:* when in mirror_mode
    is_full_flow_collection_enabled = conf_db.get_value('nginx/httpcap_all_ports', False)
    if is_full_flow_collection_enabled:
        # 更新界面使用的site_info字段。
        default_com_site_name = _("default site of full flow collection")
        default_com_site = {"name": 'http://default.com:*', "site_name": default_com_site_name,
                            "value": 'default.com:*'}

        if strategies_list == WafConfigCtrl.llm_strategies:
            default_com_site["is_llm_open"] = True
            for item in strategies_list:
                strategy_used_list = item().get_strategy_used_list()
                for item_id, hosts in strategy_used_list.items():
                    if int(item_id) == -1:
                        default_com_site[item.STRATEGY_UPSTREAMS_ID] = item_id
                        continue

                    if "default.com:*" in hosts:
                        default_com_site[item.STRATEGY_UPSTREAMS_ID] = item_id
                        break
        else:
            strategy_id = get_strategy_id_for_default_com(conf_db)
            default_com_site["waf_strategy"] = strategy_id
            default_com_site["is_waf_open"] = True

        result["site_info"].append(
            default_com_site
        )


# 获取site_info，站点信息。通用防护/高级防护/协议合规/全局白名单需要。
def get_site_info(result,strategies_list,request = None):
    result["site_info"] = get_site_apply_info(strategies_list,request)
    update_site_info_special(result,strategies_list,request)


def get_strategy_for_module(result,confDb,strategies_list,is_need_general=True, request=None):
    if is_need_general:
        # 通用防护策略
        get_general_strategy(result,confDb,request)
        # 通用防护中的语义
        get_syntax_strategy(result)
    # 其他配置策略
    for strategy in strategies_list:
        if request is not None:
            result[strategy.STRATEGY_UPSTREAMS_INFO] = strategy().get_strategy_used_hosts(request.user.username)
        else:
            result[strategy.STRATEGY_UPSTREAMS_INFO] = strategy().get_strategy_used_hosts()

def get_apply_info_for_module(result,strategies_list,is_need_general=True):
    # 应用站点的信息，所有应用站点的位置需要。
    result["apply_info"]=get_apply_stragey_info(result,strategies_list,is_need_general)


def get_resource_file_info(result):
    # 获取资源文件信息，高级防护页面需要
    all_waf_resource_file = resource_file.get_resource_file_in_zk()
    result["soap_file_name_list"] = []
    result["schema_file_name_list"] = []
    result["html_file_name_list"] = []
    for item in all_waf_resource_file:
        file_name=item.get("file_name")
        if file_name is None:
            continue
        type=item.get("type","")
        if type in ["wsdl"]:
            result["soap_file_name_list"].append({"name": file_name, "key": file_name, "value": file_name})
        if type in ["xsd"]:
            result["schema_file_name_list"].append({"name": file_name, "key": file_name, "value": file_name})
        if type in ["html"]:
            result["html_file_name_list"].append({"name": file_name, "key": file_name, "value": file_name})

def is_visible_by(current_user, strategy_user):
    if strategy_user is None:
        return True
    strategy_role = WebconsoleConf().get_user_role(strategy_user)
    return strategy_role != 'Operator' or current_user == strategy_user

def is_user_host(current_user, hosts):
    if hosts is None:
        return False

    sites = get_sites_4_user(current_user)
    site_keys = {site.get("key").replace("_", ":") for site in sites}
    return any(site_key in host for host in hosts for site_key in site_keys)

def filter_strategies_by_current_user(result, current_user):
    filtered_data = {}
    for key, value in result.iteritems():
        if isinstance(value, dict) and key in ['custom_module_strategies', 'custom_syntax_strategies']:
            for module, module_data in value.items():
                strategies = module_data['strategies']
                filtered_strategies = [strategy for strategy in strategies if
                                       is_visible_by(current_user, strategy.get('create_user'))]
                module_data['strategies'] = filtered_strategies
            filtered_data[key] = value
            continue

        if isinstance(value, list) and key not in ['default_site_strategies', 'site_info']:
            new_value = []
            for item in value:
                if is_visible_by(current_user, item.get('create_user')) or is_user_host(current_user, item.get('hosts')):
                    new_value.append(item)
            filtered_data[key] = new_value
        else:
            filtered_data[key] = value
    return filtered_data

def get_all_strategy_for_global_custom(request):
    # 需要通用配置信息用于类型列表
    return get_all_strategy(
        is_need_general=True,is_need_site_apply_info=True,is_need_resource_info=False,
        power_strategy_list=WafConfigCtrl.global_strategies, request=request)

def get_all_strategy_for_compliance(request):
    return get_all_strategy(
        is_need_general=False,is_need_site_apply_info=True,is_need_resource_info=False,
        power_strategy_list=WafConfigCtrl.protocol_strategies,request=request)

def get_all_strategy_for_llm_security(request,power_strategy_list=WafConfigCtrl.llm_strategies):
    return get_all_strategy(
        is_need_general=False,is_need_site_apply_info=True,is_need_resource_info=False,
        power_strategy_list=WafConfigCtrl.llm_strategies,request=request)

def get_all_strategy_for_power(request):
    return get_all_strategy(is_need_general=False, is_need_site_apply_info=True, is_need_resource_info=True,
                     power_strategy_list=WafConfigCtrl.power_strategies, request=request)

def get_all_strategy_for_general(request):
    return get_all_strategy(is_need_general=True,is_need_site_apply_info=True,is_need_resource_info=False,power_strategy_list=[], request=request)

def get_all_strategy_for_log(request):
    return get_all_strategy(is_need_general=True,is_need_site_apply_info=False,is_need_resource_info=False,power_strategy_list=[], request=request)

def get_all_strategy_for_site(request):
    return get_all_strategy(is_need_general=True,is_need_site_apply_info=False,is_need_resource_info=False,power_strategy_list=WafStrategyCtrl.all_power_strategies, request=request)


def get_all_strategy(is_need_general=True,is_need_site_apply_info=True,is_need_resource_info=True,power_strategy_list=None, request=None):
    # 原本用于回显的接口，当前根据配置不同进行不同处理
    result = {}
    confDb = ConfDb()
    if power_strategy_list is None:
        power_strategy_list = WafStrategyCtrl.all_power_strategies
    get_strategy_for_module(result,confDb,power_strategy_list,is_need_general,request)
    if is_need_site_apply_info:
        get_apply_info_for_module(result,power_strategy_list,is_need_general)
        get_site_info(result,power_strategy_list,request)

# [{ name: "xss_injection_interception", key: "XSS", value:"123"},
# //     { name: "xss_injection_interception2", key: "XSS2", value:"1233"}]
    if is_need_resource_info:
        get_resource_file_info(result)
    result.update({"is_success":True})

    current_role = WebconsoleConf().get_user_role(request.user.username)
    if current_role == 'Operator':
        result = filter_strategies_by_current_user(result, request.user.username)

    return result

def get_apply_stragey_info(strategies,strategies_list, is_need_general=True):
    apply_info={}
    apply_site_list = ['custom_site_strategies', 'default_site_strategies']
    if not is_need_general:
        apply_site_list = []
    for strategy in strategies_list:
        apply_site_list.append(strategy.STRATEGY_UPSTREAMS_INFO)
    for key in apply_site_list:
        items=strategies[key]
        for item in items:
            apply_info.update({
                item["id"]:{"name":item.get('name'),"comment":item.get("comment")}
    })
    return apply_info

def get_site_apply_info(strategies_list,request=None):
    nginxConf = NginxConf()
    upstreams =  nginxConf.get_all_upstreams_for_show()
    site_infos=[]
    upstreams.sort(lambda x,y:1 if x.get('name')>y.get('name') else -1)

    allow_sites_key = None
    if request is not None:
        allow_sites_key = [item.get("key") for item in get_sites_4_user(request.user.username)]
    for upstream in upstreams:
        if allow_sites_key is not None and upstream.get('key') not in allow_sites_key:
            continue
        http_type = 'https://' if upstream.get('IsHttps') else 'http://'
        server_name = upstream.get('name', '')
        site_customize_name = upstream.get('site_customize_name', '')
        site_info = {"name": http_type + server_name, "value": upstream.get('key', ''),
                     "site_name": site_customize_name,
                     "waf_strategy": waf_util.get_waf_strategy_id(upstream),
                     "is_waf_open": upstream.get('protected_list', {}).get('injection_attack_interception', False),
                     "is_llm_open": upstream.get('protected_list', {}).get('llm_protection', False)}

        strategy_upstream_ids = {}
        for strategy in strategies_list:
            strategy_upstream_ids[strategy.STRATEGY_UPSTREAMS_ID] = upstream.get(strategy.STRATEGY_UPSTREAMS_ID)
        site_info.update(strategy_upstream_ids)
        site_infos.append(site_info)

    return site_infos


def get_upstream_with_waf():
    nginxConf = NginxConf()
    upstreams =  nginxConf.get_all_upstreams_for_show()
    result = {}
    for upstream in upstreams:
        http_type = 'https://' if upstream.get('IsHttps') else 'http://'
        server_name = upstream.get('name','')
        result[http_type+server_name] = upstream.get('protected_list',{}).get('injection_attack_interception')
    return result

def get_upstream_with_llm():
    nginxConf = NginxConf()
    upstreams =  nginxConf.get_all_upstreams_for_show()
    result = {}
    for upstream in upstreams:
        http_type = 'https://' if upstream.get('IsHttps') else 'http://'
        server_name = upstream.get('name','')
        result[http_type+server_name] = upstream.get('protected_list',{}).get('llm_protection')
    return result

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'read')
def waf_general_protection(request):

    result ={}
    try:
        strategies = get_all_strategy_for_general(request)
        upstreams = get_upstream_with_waf()
        result["waf_strategy"] = strategies
        result["upstream_waf"] = upstreams
    except Exception as e:
        logger.exception(e)

    return base_render_to_response(request, 'v2/waf/general_protection.html',result)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'read')
def waf_power_protection(request):
    result ={}
    try:
        strategies = get_all_strategy_for_power(request)
        upstreams = get_upstream_with_waf()
        result["waf_strategy"] = strategies
        result["honey_pot_target_list"]  = resource_file.get_resource_file_in_zk('html')
        result["upstream_waf"] = upstreams
    except Exception as e:
        logger.exception(e)

    return base_render_to_response(request, 'v2/waf/power_protection.html',result)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Compliance_Detection', 'read')
def waf_compliance_detection(request):

    result ={}
    try:
        strategies = get_all_strategy_for_compliance(request)
        upstreams = get_upstream_with_waf()
        result["waf_strategy"] = strategies
        result["upstream_waf"] = upstreams
    except Exception as e:
        logger.exception(e)

    return base_render_to_response(request, 'v2/waf/compliance_detection.html',result)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Global_Custom_Rule', 'read')
def waf_global_custom_protection(request):

    result ={}
    try:
        strategies = get_all_strategy_for_global_custom(request)
        upstreams = get_upstream_with_waf()
        result["waf_strategy"] = strategies
        result["upstream_waf"] = upstreams
    except Exception as e:
        logger.exception(e)

    return base_render_to_response(request, 'v2/waf/global_custom_rule.html',result)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('Ruleset_Update', 'read')
def waf_ruleset_upgrade(request):

    result ={}
    try:
        conf = WafConf()
        update_config = conf.get_waf_update_config()
        update_list = conf.get_waf_ruleset_update_list()
        rule_info = get_waf_rule_info()
        license_status = get_license_status()
        result["waf_update_list"] = update_list
        result["update_config"] = update_config
        result["rule_info"] = rule_info
        result["license_status"] = license_status
        result["waf_gray_box"] = conf.get_waf_gray_box()
    except Exception as e:
        logger.exception(e)

    return base_render_to_response(request, 'v2/waf/ruleset_upgrade.html',result)

@login_required
@check_permission('WAF_Global_Custom_Rule', 'read')
def get_waf_strategies_for_global_custom(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_global_custom(request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('WAF_General_Protection', 'read')
def get_waf_strategies_for_general(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_general(request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('WAF_Power_Protection', 'read')
def get_waf_strategies_for_power(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_power(request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('WAF_Compliance_Detection', 'read')
def get_waf_strategies_for_compliance(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_compliance(request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('WAF_Strategy', 'read')
def get_waf_strategies_for_log(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_log(request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission_list(['Add_Website','Edit_Website_Config','Show_Website'], 'read')
def get_waf_strategies_for_site(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_site(request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('WAF_Strategy', 'read')
def get_waf_strategies(request):
    if request.method == "GET":
        try:
            result = get_all_strategy(True, True, True, None, request)
        except Exception as e:
            query_status = False
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission_list(['WAF_General_Protection', 'WAF_Config','Add_Website','Edit_Website_Config','Show_Website'], 'read')
def waf_rules_set(request):
    confDb = ConfDb()
    if request.method == "GET":
        query_status = False
        try:
            data = []
            waf_module = request.GET.get('waf_module')
            id = request.GET.get('id','0')
            id = int(id)
            waf_strategy_conf = WafStrategyConf(confDb)

            if id > 0 and id < waf_util.CUSTOM_STRATEGY_MIN_ID :
                data = waf_strategy_conf.get_waf_module_rules(waf_module, id)
            elif id > waf_util.CUSTOM_STRATEGY_MIN_ID:
                data = waf_strategy_conf.get_waf_custom_module_change_rules(waf_module, id)

            result = {
                'is_success': True if data is not None else False,
                'data': data,
            }

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Strategy', 'read')
def default_waf_rules_set(request):

    if request.method == "GET":
        query_status = False
        try:
            waf_module = request.GET.get('waf_module')
            data = waf_util.get_waf_default_module_rules(waf_module)
            result = {
                'is_success': True if data else False,
                'data': data,
            }
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': query_status,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

def update_module_strategy(strategy, request=None):
    try:

        waf_module = strategy.get('waf_module','')
        conf = WafConf()
        title = conf.get_waf_module_title(waf_module)
        strategy_id = strategy.get('id', -1)
        if strategy_id == -1:
            action = 'add'
            log_action = 'Add'
            strategy['create_user'] = request.user.username
        else:
            action = 'modify'
            log_action = 'Modify'

        is_valid_module_strategy(strategy)

        module_strategies = conf.get_waf_strategy_module_strategies()

        conf_strategies = module_strategies.get('{}'.format(waf_module), {}).get(
            'strategies', [])


        if strategy_id == -1:
            strategy['id'] = get_max_module_strategy_id(waf_module) + 1
        else:
            is_allow_update,conf_strategies, old_strategy = filter_id_for_update_list(request,conf_strategies,strategy_id)
            if not is_allow_update:
                result = {
                    'is_success': False,
                    'error_msg': _('You cannot modify policies created by other users.')
                }
                record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                                     ugettext_noop('waf {0} module strategy'), strategy['id'], [title])
                return result
            strategy['create_user'] = request.user.username if old_strategy is None else old_strategy.get('create_user', 'admin')

        if len(conf_strategies) < waf_util.MAX_STRATEGY_NUM:
            conf_strategies.append(strategy)
            module_strategies[waf_module] = {'strategies': conf_strategies}

            strategy_changed = {'strategy_type': 'module_strategy', 'strategy_id': strategy['id'], 'action': action,
                                'module_type': waf_module, 'random': datetime.datetime.now().microsecond}

            setting_values = {}
            setting_values["nginx/waf/module_strategies"] = module_strategies

            setting_values["nginx/waf/strategy_changed"] = strategy_changed

            conf.set_asp_conf_values(setting_values)

            result = {
                'is_success': True,
                'id': strategy['id'],
            }
        else:
            result = {
                'is_success': False,
                'error_msg': _('The number of module strategies exceeds {0}. You cannot create a new strategy.').format(waf_util.MAX_STRATEGY_NUM)
            }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf {0} module strategy'), strategy['id'], [title])

    except DuplicateNameError as e:
        logger.exception(e)
        result = {
            'is_success': False,
            'error_msg': _(e.message)
        }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf {0} module strategy'), '', [title])

    except Exception as e:
        logger.exception(e)
        result = {
            'is_success': False,
            'error_msg': _("Strategy creation failed.")
        }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf {0} module strategy'), '', [title])

    result['name'] = title
    return result

def update_syntax_strategy(strategy, request=None):
    try:

        waf_module = strategy.get('waf_module','')
        conf = WafConf()
        title = conf.get_waf_syntax_title(waf_module)
        strategy_id = strategy.get('id', -1)
        if strategy_id == -1:
            action = 'add'
            log_action = 'Add'
            strategy['create_user'] = request.user.username
        else:
            action = 'modify'
            log_action = 'Modify'
        # cmd_syntax has no special_config.
        strategy['special_config'] = strategy.get('special_config', {})

        is_valid_module_strategy(strategy)

        syntax_strategies = conf.get_waf_syntax_strategies()

        conf_strategies = syntax_strategies.get(waf_module, {}).get('strategies', [])

        strategy_id = strategy.get('id', -1)
        if strategy_id == -1:
            strategy['id'] = get_max_strategy_id(conf_strategies) + 1
        else:
            is_allow_update, conf_strategies, old_strategy = filter_id_for_update_list(request,conf_strategies,strategy_id)
            if not is_allow_update:
                result = {
                    'is_success': False,
                    'error_msg': _('You cannot modify policies created by other users.')
                }
                record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                                     ugettext_noop('waf {0} syntax strategy'), strategy['id'], [title])
                return result
            strategy['create_user'] = request.user.username if old_strategy is None else old_strategy.get('create_user', 'admin')

        if len(conf_strategies) < waf_util.MAX_STRATEGY_NUM:
            conf_strategies.append(strategy)
            syntax_strategies[waf_module] = {'strategies': conf_strategies}



            strategy_changed = {'strategy_type': 'syntax_strategy', 'strategy_id': strategy['id'], 'action': action,
                                'module_type': waf_module, 'random': datetime.datetime.now().microsecond}

            setting_values = {}
            setting_values["nginx/waf/syntax_strategies"] = syntax_strategies

            setting_values["nginx/waf/strategy_changed"] = strategy_changed

            conf.set_asp_conf_values(setting_values)

            result = {
                'is_success': True,
                'id': strategy['id'],
            }
        else:
            result = {
                'is_success': False,
                'error_msg': _('The number of module strategies exceeds {0}. You cannot create a new strategy.').format(waf_util.MAX_STRATEGY_NUM)
            }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf {0} syntax strategy'), strategy['id'], [title])

    except DuplicateNameError as e:
        logger.exception(e)
        result = {
            'is_success': False,
            'error_msg': _(e.message)
        }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf {0} syntax strategy'), '', [title])

    except Exception as e:
        logger.exception(e)
        result = {
            'is_success': False,
            'error_msg': _("Strategy creation failed.")
        }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf {0} syntax strategy'), '', [title])
    result['name'] = title
    return result


def update_site_strategy(strategy, request=None):
    try:

        strategy_id = strategy.get('id', -1)
        if strategy_id == -1:
            action = 'add'
            log_action = 'Add'
            strategy['create_user'] = request.user.username
        else:
            action = 'modify'
            log_action = 'Modify'

        is_valid_site_strategy(strategy)
        conf = WafConf()
        conf_site_strategies = conf.get_waf_site_strategies()

        if strategy_id == -1:
            strategy['id'] = get_max_site_strategy_id() + 1
        else:
            is_allow_update, conf_site_strategies, old_strategy = filter_id_for_update_list(request, conf_site_strategies,
                                                                              strategy_id)
            if not is_allow_update:
                result = {
                    'is_success': False,
                    'error_msg': _('You cannot modify policies created by other users.')
                }
                record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                                     ugettext_noop('waf site strategy'), strategy.get('id', -1))
                return result
            strategy['create_user'] = request.user.username if old_strategy is None else old_strategy.get('create_user', 'admin')

        if len(conf_site_strategies) < waf_util.MAX_STRATEGY_NUM:
            module_strategies = strategy.get('module_strategies')

            all_module_exit = True
            for module,info in module_strategies.items():
                err = conf.valid_waf_module_strategy_id_exist(module,info.get('strategy'))
                if err:
                    all_module_exit = False
                    result = {
                        'is_success': False,
                        'error_msg': err
                    }
                    break

            syntax_strategies = strategy.get('syntax_strategies')
            if all_module_exit:
                for module,info in syntax_strategies.items():
                    err = conf.valid_waf_syntax_strategy_id_exist(module,info.get('strategy'))
                    if err:
                        all_module_exit = False
                        result = {
                            'is_success': False,
                            'error_msg': err
                        }
                        break

            if all_module_exit:
                conf_site_strategies.append(strategy)

                setting_values = {}

                strategy_changed = {'strategy_type': 'site_strategy', 'strategy_id': strategy['id'], 'action': action,
                                    'random': datetime.datetime.now().microsecond}
                setting_values["nginx/waf/site_strategies"] = conf_site_strategies
                setting_values["nginx/waf/strategy_changed"] = strategy_changed
                setting_values["nginx/waf/module_strategy_used_list"] = conf.update_module_strategy_used_list(strategy, action)
                setting_values["nginx/waf/syntax_strategy_used_list"] = conf.update_syntax_strategy_used_list(strategy, action)
                conf.set_asp_conf_values(setting_values)

                result = {
                    'is_success': True,
                    'id': strategy['id'],
                }
        else:
            result = {
                'is_success': False,
                'error_msg': _('The number of site strategies exceeds {0}. You cannot create a new strategy.').format(waf_util.MAX_STRATEGY_NUM)
            }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf site strategy'), strategy['id'])

    except DuplicateNameError as e:
        logger.exception(e)
        result = {
            'is_success': False,
            'error_msg': _(e.message)
        }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf site strategy'), strategy['id'])

    except Exception as e:
        logger.exception(e)
        result = {
            'is_success': False,
            'error_msg': _("Strategy creation failed.")
        }
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(log_action)),
                             ugettext_noop('waf site strategy'), strategy.get('id',-1))


    result['name'] = _("site_strategy")
    return result


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'write')
def save_site_strategy(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            result = update_site_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            # record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
            #                      ugettext_noop('waf site strategy'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'write')
def save_syntax_strategy(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            result = update_syntax_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_res_leech_strategy(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)

            res_leech_ctrl=ResLeech()
            result = res_leech_ctrl.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'write')
def save_module_strategy(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            result = update_module_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'write')
def delete_site_strategy(request):
    if request.method == "POST":
        try:
            action = 'delete'
            strategy_id = json.loads(request.body).get('id', 0)
            if isinstance(strategy_id, int) and strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:
                conDb = ConfDb()
                site_strategy_used_list_all = conDb.get_value('nginx/waf/site_strategy_used_list', {})
                site_strategy_used_list = site_strategy_used_list_all.get("{}".format(strategy_id), [])

                strategy_used_hosts = covert_server_key_to_host(site_strategy_used_list)
                # 全流量采集关闭时允许删除，开启时需要校验default站点是否存在。
                is_full_flow_collection_enabled = conDb.get_value('nginx/httpcap_all_ports', False)
                if is_full_flow_collection_enabled:
                    if "default.com:*" in site_strategy_used_list:
                        strategy_used_hosts.append("http://default.com:*")

                if len(strategy_used_hosts) == 0:
                    confDb = ConfDb()
                    all_conf_site_strategies = confDb.get_value('nginx/waf/site_strategies', [])
                    conf_site_strategies = [item for item in all_conf_site_strategies if item.get("id") == strategy_id]
                    if len(conf_site_strategies) > 0:
                        strategy = conf_site_strategies[0]
                        all_conf_site_strategies.remove(strategy)
                        conf = WafConf()
                        setting_values = {}
                        #全流量采集关闭，并且要删除的站点中有默认站点，需要将原本应用站点设置到默认。
                        if not is_full_flow_collection_enabled and "default.com:*" in site_strategy_used_list:
                            #默认策略策略为1
                            default_site_strategy = "1"
                            if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':
                                default_site_strategy = "9"

                            default_site_strategy_used_list=site_strategy_used_list_all.get("{}".format(default_site_strategy), [])
                            default_site_strategy_used_list.append("default.com:*")
                            site_strategy_used_list_all[default_site_strategy]=default_site_strategy_used_list
                            if "default.com:*" in site_strategy_used_list_all[str(strategy_id)]:
                                site_strategy_used_list_all[str(strategy_id)].remove("default.com:*")
                            setting_values['nginx/waf/site_strategy_used_list'] = site_strategy_used_list_all

                        strategy_changed = {'strategy_type': 'site_strategy', 'strategy_id': strategy_id,
                                            'action': action}

                        setting_values["nginx/waf/site_strategies"] = all_conf_site_strategies
                        setting_values["nginx/waf/strategy_changed"] = strategy_changed
                        setting_values["nginx/waf/module_strategy_used_list"] = conf.update_module_strategy_used_list(strategy,
                                                                                                            action)
                        setting_values["nginx/waf/syntax_strategy_used_list"] = conf.update_syntax_strategy_used_list(strategy,
                                                                                                            action)
                        conf.set_asp_conf_values(setting_values)

                        result = {
                            'is_success': True,
                        }
                    else:
                        result = {
                            'is_success': False,
                            'error_msg': _('strategy_id:{} does not exist').format(strategy_id)
                        }

                else:
                    result = {
                        'is_success': False,
                        'error_msg': '',
                        'extra_data': strategy_used_hosts
                    }
            else:
                result = {
                    'is_success': False,
                    'error_msg': _('strategy_id:{} does not exist').format(strategy_id)
                }

            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf site strategy'), strategy_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf site strategy'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'write')
def delete_module_strategy(request):
    if request.method == "POST":
        try:
            action = 'delete'
            data = json.loads(request.body)
            waf_module = data.get('waf_module', None)
            strategy_id = data.get('id', 0)
            conf = WafConf()
            title = conf.get_waf_module_title(waf_module)
            if waf_module and isinstance(strategy_id, int) and strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:
                confDb = ConfDb()
                module_strategy_used_list = confDb.get_value('nginx/waf/module_strategy_used_list', {}).get(waf_module,
                                                                                                            {})
                module_strategy_used_list = module_strategy_used_list.get('{}'.format(strategy_id), [])
                if len(module_strategy_used_list) == 0:

                    module_strategies = confDb.get_value('nginx/waf/module_strategies', {})
                    conf_strategies = module_strategies.get('{}'.format(waf_module), {}).get(
                        'strategies', [])

                    conf_strategies = [item for item in conf_strategies if item.get("id") != strategy_id]
                    module_strategies[waf_module] = {'strategies': conf_strategies}

                    strategy_changed = {'strategy_type': 'module_strategy', 'strategy_id': strategy_id,
                                        'action': action, 'module_type': waf_module}

                    setting_values = {}
                    setting_values["nginx/waf/module_strategies"] = module_strategies

                    setting_values["nginx/waf/strategy_changed"] = strategy_changed

                    conf.set_asp_conf_values(setting_values)

                    result = {
                        'is_success': True,
                    }
                else:
                    result = {
                        'is_success': False,
                        'extra_data': module_strategy_used_list
                    }
            else:
                result = {
                    'is_success': False,
                    'error_msg': _('strategy_id:{} does not exist').format(strategy_id)
                }

            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0} module strategy'), strategy_id, [title])

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0} module strategy'), '', [title])

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_General_Protection', 'write')
def delete_syntax_strategy(request):
    if request.method == "POST":
        try:
            action = 'delete'
            data = json.loads(request.body)
            waf_module = data.get('waf_module', None)
            strategy_id = data.get('id', 0)
            conf = WafConf()
            title = conf.get_waf_syntax_title(waf_module)
            if waf_module and isinstance(strategy_id, int) and strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:
                confDb = ConfDb()
                syntax_strategy_used_list = confDb.get_value('nginx/waf/syntax_strategy_used_list', {}).get(waf_module,{})
                syntax_strategy_used_list = syntax_strategy_used_list.get("{}".format(strategy_id), [])
                if len(syntax_strategy_used_list) == 0:

                    syntax_strategies = conf.get_waf_syntax_strategies()
                    conf_strategies = syntax_strategies.get(waf_module, {}).get('strategies', [])

                    conf_strategies = [item for item in conf_strategies if item.get("id") != strategy_id]
                    syntax_strategies[waf_module] = {'strategies': conf_strategies}

                    strategy_changed = {'strategy_type': 'syntax_strategy', 'strategy_id': strategy_id,
                                        'action': action, 'module_type': waf_module}

                    setting_values = {}
                    setting_values["nginx/waf/syntax_strategies"] = syntax_strategies

                    setting_values["nginx/waf/strategy_changed"] = strategy_changed

                    conf.set_asp_conf_values(setting_values)

                    result = {
                        'is_success': True,
                    }
                else:
                    result = {
                        'is_success': False,
                        'extra_data': syntax_strategy_used_list
                    }
            else:
                result = {
                    'is_success': False,
                    'error_msg': _('strategy_id:{} does not exist').format(strategy_id)
                }

            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0} syntax strategy'), strategy_id, [title])

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0} syntax strategy'), '', [title])

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

def save_syntax_strategy_api(site_strategy, strategies, request):

    for module, info in strategies.items():
        default_strategy_id = info.get('default_strategy_id')
        if default_strategy_id:
            syntax_strategy_id = default_strategy_id
            result = {'is_success':True}
        else:
            syntax_strategy = {}
            syntax_strategy['waf_module'] = module
            syntax_strategy['id'] = -1
            syntax_strategy['name'] = info.get('name')
            syntax_strategy['template'] = str(info.get('template'))
            syntax_strategy['comment'] = info.get('comment')
            syntax_strategy['rules'] = info.get('waf_change_rules')
            syntax_strategy['special_config'] = info.get('special_config')
            result = update_syntax_strategy(syntax_strategy, request)

            if result.get('is_success'):
                syntax_strategy_id = result.get('id')
            else:
                return result

        syntax_info = {}
        syntax_info['enabled'] = info.get('enabled')
        syntax_info['strategy'] = str(syntax_strategy_id)
        site_strategy['syntax_strategies'][module] = syntax_info

    return result



def save_site_strategies_api(module_strategies,syntax_strategies,request):
    conf = WafConf()
    conf_site_strategies = conf.get_waf_site_strategies()
    if len(conf_site_strategies) < waf_util.MAX_STRATEGY_NUM:
        site_strategy = {}
        site_strategy['id'] = -1
        site_strategy['name'] = _('custom strategy_{}').format(random.randint(1000, 9999))
        site_strategy['comment'] = _('Auto Added')
        site_strategy['module_strategies'] = {}
        site_strategy['syntax_strategies'] = {}

        if module_strategies:
            for module, info in module_strategies.items():
                default_strategy_id = info.get('default_strategy_id')
                if default_strategy_id:
                    module_strategy_id = default_strategy_id
                    site_template_id = default_strategy_id
                else:
                    module_strategy = {}
                    module_strategy['waf_module'] = module
                    module_strategy['id'] = -1
                    module_strategy['name'] = info.get('name')
                    module_strategy['template'] = str(info.get('template'))
                    module_strategy['comment'] = info.get('comment')
                    module_strategy['rules'] = info.get('waf_change_rules')
                    module_strategy['special_config'] = info.get('special_config')
                    site_strategy['name'] = module_strategy['name']
                    site_template_id = info.get('template')
                    result = update_module_strategy(module_strategy, request)

                    if result.get('is_success',False):
                        module_strategy_id = result.get('id')
                    else:
                        return result

                module_info = {}
                module_info['enabled'] = info.get('enabled')
                module_info['strategy'] = str(module_strategy_id)
                site_strategy['module_strategies'][module] = module_info

        if syntax_strategies:
            result = save_syntax_strategy_api(site_strategy, syntax_strategies, request)
            if not result.get('is_success',False):
                return result

        site_strategy['template'] = str(site_template_id)
        result = update_site_strategy(site_strategy, request)

    else:
        result = {
            'is_success': False,
            'name':_("site_strategy"),
            'error_msg': _(
                'The number of site strategies exceeds {0}. You cannot create a new strategy.').format(
                waf_util.MAX_STRATEGY_NUM)
        }

    return result


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission_list(['WAF_Power_Protection','WAF_Global_Custom_Rule','WAF_General_Protection','WAF_Compliance_Detection','LLM_Protection'], 'write')
def save_strategy_api(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            result = {}

            save_result=WafStrategyCtrl().save_strategy_api(strategy,request)
            result.update(save_result)
            if not save_result.get('is_success'):
                return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


            module_strategies = strategy.get("module_strategies", {})
            syntax_strategies = strategy.get("syntax_strategies", {})
            if module_strategies and syntax_strategies:
                save_result = save_site_strategies_api(module_strategies,syntax_strategies,request)
                result.update(save_result)
                if not save_result.get('is_success'):
                    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_res_leech_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        res_leech_ctrl = ResLeech()
        result = res_leech_ctrl.delete_strategy(request)


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('Ruleset_Update', 'write')
def save_update_config(request):
    if request.method == "POST":
        try:
            conf = WafConf()
            update_config = json.loads(request.body)
            ## valid config
            is_valid_update_config(update_config)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error': {"err_no": "101", 'err_msg': _("The filled data format is incorrect")}
            }

            record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                                 ugettext_noop('waf update config'), '')

            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        try:

            ## valid network
            waf_ruleset_updrade = WafRuleSetUpgrade(ConfDb())
            if int(update_config.get('update_type')) != WafRuleSetUpgrade.DISABLED_AUTO:
                err = waf_ruleset_updrade.test_request_waf_update(update_config)
                if err:
                    result = {
                        'is_success': False,
                        'error': err
                    }
                    record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                                         ugettext_noop('waf update config'), '')

                    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
            else:
                waf_ruleset_updrade.delelet_alarm_file()

            # add update time
            update_config["setting_time"] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')
            conf.set_waf_update_config(update_config)

            result = {
                'is_success': True,
                'error': ''
            }
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error': {"err_no": "99", 'err_msg': _(e.message)}
            }

        record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                             ugettext_noop('waf update config'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('Ruleset_Update', 'write')
def check_waf_ruleset(request):
    if request.method == "GET":
        try:
            waf_ruleset_updrade = WafRuleSetUpgrade(ConfDb())
            err, update_list = waf_ruleset_updrade.download_waf_ruleset_update()

            if not err:
                conf = WafConf()
                conf.sync_file(WafRuleSetUpgrade.ZIP_FILE_PATH)
                result = {
                    'is_success': True,
                    'data': update_list
                }
            else:
                result = {
                    'is_success': False,
                    'error': err
                }

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error': {"err_no": "99", 'err_msg': "unkonw error {}".format(e.message)}
            }

        record_operation_log(request, result['is_success'], ugettext_noop('Update'),
                             ugettext_noop('Manually update the WAF rule set'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('Ruleset_Update', 'write')
def install_waf_ruleset(request):
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            upgrade_name = data.get("ruleset_version")
            waf_ruleset_updrade = WafRuleSetUpgrade(ConfDb())
            update_list = waf_ruleset_updrade.get_waf_update_list()
            # valid exist
            if upgrade_name not in [item.get("name") for item in update_list]:
                result = {
                    'is_success': False,
                    'error': {"err_no": "101", 'err_msg': _("waf ruleset does not exist")}
                }
                operation_log(request, ugettext_noop('WAF'), ugettext_noop('Install'), '1',
                              {'msg': ugettext_noop('waf ruleset failed to install')})
                return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

            # install ruleset
            wafruleset_filepath, pre_wafruleset_filepath = waf_ruleset_updrade.rotate_pre_waf_ruleset(upgrade_name)

            conf = WafConf()
            conf.sync_file(wafruleset_filepath)
            conf.sync_file(pre_wafruleset_filepath)
            task_finished = conf.set_upgrade_waf_ruleset('upgrade')

            ruleset_version = re.findall('\d+.\d+.\d{8}_\w+', upgrade_name)[0]
            if (task_finished and task_finished[0] == 0):
                WAFGrayBox(conf).update()

                operation_log(request, ugettext_noop('WAF'), ugettext_noop('Install'), '0', {
                    'msg': ugettext_noop('Upgrade WAF Ruleset to {new}.'),
                    'extra': {'new': ruleset_version, },
                })
                result = {
                    'is_success': True,
                }
            else:
                operation_log(request, ugettext_noop('WAF'), ugettext_noop('Install'), '1',
                              {'msg': ugettext_noop('waf ruleset failed to install')})
                result = {
                    'is_success': False,
                    'error': {"err_no": "99", 'err_msg': _(task_finished)}
                }

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error': {"err_no": "99", 'err_msg': _(e.message)}
            }

            operation_log(request, ugettext_noop('WAF'), ugettext_noop('Install'), '1',
                      {'msg': ugettext_noop('waf ruleset failed to install')})

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('Ruleset_Update', 'write')
def handle_waf_request(request, handler, msg):
    if request.method == "POST":
        module_msg = ugettext_noop('WAF')
        action_msg = msg['action']

        try:
            request_body = json.loads(request.body)
            code, data, handler_msg = handler(request_body)
            if code is not None and code != 0:
                operation_log(request, module_msg, action_msg, '1', {'msg': msg['failure'] })
                return JsonResponse({'is_success': False})

            if handler_msg and handler_msg['action']:
                action_msg = handler_msg['action']

            operation_log(request, module_msg, action_msg, '0', {'msg': msg['success']})
            return JsonResponse({'is_success': True, 'data': data })

        except Exception as e:
            logger.exception(e)
            operation_log(request, module_msg, action_msg, '1', {'msg': msg['failure'] })
            return JsonResponse({ 'is_success': False, 'error_msg': msg['error'] })

    return JsonResponse({'is_success': True })

def save_gray_box(request):
    operation_info = {
        'action': ugettext_noop("Modify"),
        'failure': ugettext_noop("Failed to save Monitor-Only New Rules"),
        'success': ugettext_noop("Monitor-Only New Rules"),
        'error': ugettext_noop("Save failed."),
    }

    def handler(request_body):
        new_rules_enabled = request_body.get('new_rules_enabled', False)
        data = {
            "new_rules_enabled": new_rules_enabled,
        }

        code, _, _ = NginxConf().set_asp_conf_values({WAFGrayBox.CONFIG_PATH: data})

        gray_box_data = None
        if new_rules_enabled:
            gray_box_data = WafConf().get_waf_gray_box()

        msg = {}
        if new_rules_enabled:
            msg['action'] = ugettext_noop("Enable")
        else:
            msg['action'] = ugettext_noop("Close")

        return code, gray_box_data, msg

    return handle_waf_request(request, handler, operation_info)

def close_gray_box(request):
    operation_info = {
        'action': ugettext_noop("Modify"),
        'failure': ugettext_noop("Failed to save Default Action"),
        'success': ugettext_noop("Default Action"),
        'error': ugettext_noop("Save failed."),
    }

    def handler(request_body):
        action = request_body.get("new_rules_action", "1")
        WAFGrayBox(BaseConf()).update_new_rules_action(action)
        return None, None, None

    return handle_waf_request(request, handler, operation_info)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('Edit_Website_Config', 'write')
def clear_tamper_cache(request):
    ret = {
        "is_success": False,
        "error": "",
    }

    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            server_key = data.get('serverKey','')
            confDb = ConfDb()
            upstream_info = confDb.get_values("nginx/upstreams/" + server_key)
            if not upstream_info:
                ret["error"] = _("no site_name specified")
                operation_log(request, ugettext_noop('Proxy'), ugettext_noop('delete'), "1", {
                    'msg': ugettext_noop('delete site cache'), 'spliceMsg': ': ' + server_key})
                return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

            server_name = upstream_info.get('ServerName')
            listen_port = upstream_info.get('ListenPort')
            site_key = server_name + ':' + listen_port

            successed = WafTamperProtection.clear_tamper_cache(server_key)
            if not successed:
                ret["error"] = _("delete site cache files failed")
                operation_log(request, ugettext_noop('Proxy'), ugettext_noop('delete'), "1", {
                    'msg': ugettext_noop('delete site cache'), 'spliceMsg': ': ' + site_key})
            else:
                ret["is_success"] = True
                operation_log(request, ugettext_noop('Proxy'), ugettext_noop('delete'), "0", {
                    'msg': ugettext_noop('delete site cache'), 'spliceMsg': ': ' + site_key})

        except Exception as ex:
            logger.exception(ex)
            ret["error"] = _(ex.message)
            operation_log(request, ugettext_noop('Proxy'), ugettext_noop('delete'), "1", {
                'msg': ugettext_noop('delete site cache')})

    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('WAF_Rules', 'read')
def waf_rules(request, filename):
    """
    :param request:
    :type filename: str
    :return:
    """
    filename = os.path.basename(filename)
    if not filename.endswith(".json"):
        return HttpResponseNotFound()
    try:
        local_conf_path = get_waf_conf_path()
        waf_rule_path = "web_admin/static/waf_rules/"
        waf_rule_path = os.path.join(waf_rule_path, local_conf_path)
        return static.serve(request, filename, document_root=get_release_file(waf_rule_path))
    except IOError:
        return HttpResponseNotFound()


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_brute_force_strategy(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            random_str = ''.join(random.sample([chr(i) for i in range(97, 122)], 6))
            strategy['random_str'] = random_str
            brute_force_ctrl=BruteForce()
            result = brute_force_ctrl.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_brute_force_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        brute_force_ctrl = BruteForce()
        result = brute_force_ctrl.delete_strategy(request)


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_weak_password_check_strategy(request):
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            random_str = ''.join(random.sample([chr(i) for i in range(97, 122)], 6))
            strategy['random_str'] = random_str
            weak_password_check_ctrl=WeakPasswordCheck()
            result = weak_password_check_ctrl.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_weak_password_check_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        weak_password_check_ctrl = WeakPasswordCheck()
        result = weak_password_check_ctrl.delete_strategy(request)


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_waf_weak_password_list(request):
    if request.method == "POST":
        try:
            conf = WafConf()
            content = json.loads(request.body)
            weak_password_list = content.get('weak_password_list')
            if len(''.join(weak_password_list)) > conf.MAX_WEAK_PASSWORD_CHARS:
                error = _('The number of weak password characters has exceeded the maximum of {0}.').format(conf.MAX_WEAK_PASSWORD_CHARS)
                result = {"is_success": False,'error_msg': error}
                record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                                     ugettext_noop('Weak password dictionary update'), '')
            else:
                result = {"is_success": True}
                old_weak_password_list = conf.get_weak_password_list() or WEAK_PWD_LIST
                if old_weak_password_list != weak_password_list:
                    conf.set_weak_password_list(weak_password_list)
                    record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                                         ugettext_noop('Weak password dictionary update'), '')

        except Exception as e:

            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Save failed.")
            }
            record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                                 ugettext_noop('Weak password dictionary update'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'read')
def get_waf_weak_password_list(request):
    if request.method == 'GET':
        conf = WafConf()
        weak_password_list = conf.get_weak_password_list()
        if weak_password_list is None:
            weak_password_list = WEAK_PWD_LIST
        return HttpResponse(json.dumps({'weak_password_list': weak_password_list}), content_type='application/json')

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_honey_pot_check_strategy(request):
    if request.method == 'POST':
        try:
            strategy = json.loads(request.body)
            honey_pot_check = HoneyPotCheck()
            result = honey_pot_check.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_honey_pot_check_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        honey_pot_check = HoneyPotCheck()
        result = honey_pot_check.delete_strategy(request)
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_illegal_download_strategy(request):
    if request.method == 'POST':
        try:
            strategy = json.loads(request.body)
            illegal_download = IllegalDownloadProtection()
            result = illegal_download.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_illegal_download_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        illegal_download = IllegalDownloadProtection()
        result = illegal_download.delete_strategy(request)
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_regional_access_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            region_access_control = RegionalAccessControl()
            result = region_access_control.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_regional_access_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        region_access_control = RegionalAccessControl()
        result = region_access_control.delete_strategy(request)


    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# @has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission_list(WafConfigCtrl.site_apply_permission, 'write')
def save_site_apply(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        strategy = json.loads(request.body)
        strategy_id = strategy.get('id', -1)
        # if (642000 > strategy_id or strategy_id > 643000) and strategy_id!=-1:
        #     license_info = NginxConf().get_license_info()
        #     enabled = license_info.is_module_effect(LicenseInfo.DM_ADVANCED_WAF)
        #     if not enabled:
        #         return HttpResponse(status=403)
        apply_site_type = strategy.get('apply_site_type',None)
        server_names = strategy.get('server_names',None)
        strategy_ctrl=globals().get(apply_site_type,None)
        if strategy_ctrl:
            result = strategy_ctrl().update_strategy_used_lists(strategy_id,server_names,request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_cc_attack_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            cc_attack = CCAttackProtection()
            random_str = ''.join(random.sample([chr(i) for i in range(97,122)],6))
            strategy['random_str'] = random_str
            result = cc_attack.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_cc_attack_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        cc_attack = CCAttackProtection()
        result = cc_attack.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_vulnerability_scan_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            vulnerability_scan = VulnerabilityScanProtection()
            random_str = ''.join(random.sample([chr(i) for i in range(97,122)],6))
            strategy['random_str'] = random_str
            result = vulnerability_scan.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_vulnerability_scan_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        vulnerability_scan = VulnerabilityScanProtection()
        result = vulnerability_scan.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission_list(['WAF_Power_Protection','WAF_Config'], 'write')
def save_xml_file(request):
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":

        try:
            if 'soap_file' in request.FILES:
                m_file = request.FILES['soap_file']
            elif 'schema_file' in request.FILES:
                m_file = request.FILES['schema_file']
            elif 'html_file' in request.FILES:
                m_file = request.FILES['html_file']
            else:
                return result
            xml_attack = XMLAttackProtection()

            file_size = m_file.size
            file_name = m_file.name
            file_type = file_name.split(".")[-1]

            if file_size > resource_file.MAX_RESOURCE_FILE_SIZE:
                raise WafError(_("Resource waf file size has exceeded the maximum"))

            file_content = m_file.read()

            file_info, file_info_list = resource_file.prepare_resource_file(file_name, file_type, file_size, file_content)

            # write file content and zip resource waf file
            full_sync_path = resource_file.zip_resource_file_folder(file_info['file_name'], file_info['type'], file_content)
            rt = xml_attack.sync_file(full_sync_path)
            if rt == RESULT.OK:
                rt1 = xml_attack.set_asp_conf_values(
                    [(resource_file.ZK_SHARED_CONF_PATH+'/'+file_info['type'], json.dumps(file_info_list))])
                if not rt1:
                    logging.error('Update resource waf file {}:{} from zk failed.'.format(file_info['type'],
                                                                                      file_info['file_name']))
                    raise WafError(_('Sync resource waf file {}:{} from zk failed.').format(file_info['type'],
                                                                                         file_info[
                                                                                             'file_name']))

                result['is_success'] = True
                result['info'] = file_info
                record_operation_log(request, result['is_success'], ugettext_noop('Upload'),
                                     ugettext_noop(file_info['type']),
                                     file_info['file_name'])
            else:
                logging.error(
                    'Update resource waf file {}:{} failed.'.format(file_info.get('type'),
                                                                file_info.get('file_name')))
                raise WafError(_('Update resource waf file {}:{} failed.').format(file_info['type'],
                                                                               file_info['file_name']))

        except WafError as e:
            logger.exception(e)
            record_operation_log(request, result['is_success'], ugettext_noop('Upload'),
                                 ugettext_noop(file_type),
                                 file_name)
            result = {
                'is_success': False,
                'error_msg': _(e.message),
            }

        except Exception as e:
            logger.exception(e)
            record_operation_log(request, result['is_success'], ugettext_noop('Upload'),
                                 ugettext_noop(file_type),
                                 file_name)
            result = {
                'is_success': False,
                'error_msg': _('Update resource waf file failed.')
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Config', 'write')
def delete_waf_resource_file(request):
    result = {
        'is_success': False,
        'error_msg': ''
    }
    if request.method == "POST":
        data = json.loads(request.body)
        file_name = data.get('file_name', None)
        file_type = data.get('file_type', None)

        try:
            if not file_name or not file_type:
                raise Exception(_('Cant find the resource waf file'))
            # check and refresh file info-list on zk
            file_info_list = resource_file.delete_resource_file_info(file_name, file_type)

            if os.path.isfile(resource_file.TMP_RESOURCE_DIR):
                os.remove(resource_file.TMP_RESOURCE_DIR)
            elif os.path.isdir(resource_file.TMP_RESOURCE_DIR):
                shutil.rmtree(resource_file.TMP_RESOURCE_DIR)

            if os.path.isdir(resource_file.RESOURCE_FILE_FOLDER):
                shutil.copytree(resource_file.RESOURCE_FILE_FOLDER, resource_file.TMP_RESOURCE_DIR)

            file_type_path = resource_file.TMP_RESOURCE_DIR + file_type
            file_path = file_type_path + '/' + file_name

            # delete file
            if os.path.isfile(file_path):
                os.remove(file_path)

            # delete file
            if file_type=='wsdl':
                wsdl_file_path= resource_file.TMP_RESOURCE_DIR + resource_file.DEAL_AFTER_FOLDER + '/' + file_name
                if os.path.isfile(wsdl_file_path):
                    os.remove(wsdl_file_path)

            if len(resource_file.get_resource_file_in_zk()) > 0:
                # build zip file
                full_sync_path = get_sync_file_path(resource_file.ZIP_FILE_NAME)
                if os.path.isfile(full_sync_path):
                    os.remove(full_sync_path)

                with zipfile.ZipFile(full_sync_path, 'a') as zf:
                    for dirpath, dirnames, filenames in os.walk(resource_file.TMP_RESOURCE_DIR):
                        fpath = dirpath.replace(resource_file.TMP_RESOURCE_DIR, '')
                        for filename in filenames:
                            zf.write(os.path.join(dirpath, filename), os.path.join(fpath, filename))
            else:
                full_sync_path = get_sync_file_path('waf_resource_nothing_left')
                if os.path.isfile(full_sync_path):
                    os.remove(full_sync_path)
                fd = open(full_sync_path, 'w')
                fd.close()
            m_conf = WafConf()
            rt = m_conf.sync_file(full_sync_path)

            if rt == RESULT.OK:
                rt1 = m_conf.set_asp_conf_values(
                    [(resource_file.ZK_SHARED_CONF_PATH+'/'+file_type, json.dumps(file_info_list))])
                if not rt1:
                    logging.error('Delete resource waf file {}:{} from zk failed.'.format(file_type, file_name))
                    raise Exception(
                        _('Delete resource waf file {}:{} from zk failed.').format(file_type, file_name))

                result['is_success'] = True
                result['info'] = file_info_list
                record_operation_log(request, result['is_success'], ugettext_noop('Delete'), ugettext_noop(file_type),
                                     file_name)
            else:
                raise Exception(_('Fail to sync file in the cluster'))

        except Exception as e:
            result['error_msg'] = e.message or _('Delete resource waf file failed')
            logger.error('Doelete resource waf file failed with {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Delete'), ugettext_noop(file_type),
                                 file_name)

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_cookie_tamper_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            cookie_tamper = CookieTamperProtection()
            result = cookie_tamper.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_cookie_tamper_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        cookie_tamper = CookieTamperProtection()
        result = cookie_tamper.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_global_csrf_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            csrf_protection = GlobalCsrfProtection()
            result = csrf_protection.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_global_csrf_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        global_csrf = GlobalCsrfProtection()
        result = global_csrf.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Config', 'write')
def download_waf_resource_file(request):
    from django.http import FileResponse
    result = {
        'is_success': False,
        'error_msg': ''
    }
    if request.method == "GET":
        file_name = request.GET.get('file_name')
        file_type = request.GET.get('file_type')
        if not file_name or not file_type:
            raise Exception(_('Cant find the resource waf file'))

        try:
            file_info_list = resource_file.get_resource_file_in_zk((file_type,))
            file_name_exists = False
            for file_exists in file_info_list:
                if file_exists['file_name'] == file_name:
                    file_name_exists = True
                    break

            if not file_name_exists:
                logging.error('Download resource waf file {} : {} not in zk.'.format(file_type, file_name))
                raise Exception(_('Cant find the {} name resource waf file').format(file_name))

            file_type_path = resource_file.RESOURCE_FILE_FOLDER + '/' + file_type
            file_path = file_type_path + '/' + file_name

            if not os.path.isfile(file_path):
                logging.error('Download resource waf file {} : {} not in dir.'.format(file_type, file_name))
                raise Exception(_('Cant find the {} resource waf file: {}').format(file_type, file_name))

            response = FileResponse(open(file_path, 'rb'))
            response['content-type'] = 'application/octet-stream'
            response['Content-Disposition'] = 'attachment; filename="{}"'.format(file_name)
            result['is_success'] = True
            record_operation_log(request, result['is_success'], ugettext_noop('Download'), ugettext_noop(file_type),
                                 file_name)
            return response
        except Exception as e:
            result['error_msg'] = e.message or _('Fail to download resource waf file')
            logger.error('Download resource waf file failed with {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Download'), ugettext_noop(file_type),
                                 file_name)
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)



@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def save_xml_attack_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            xml_attack = XMLAttackProtection()
            result = xml_attack.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Power_Protection', 'write')
def delete_xml_attack_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        xml_attack = XMLAttackProtection()
        result = xml_attack.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Global_Custom_Rule', 'write')
def save_global_custom_rule(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            global_custom_rule = GlobalCustomRule()
            result = global_custom_rule.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Global_Custom_Rule', 'write')
def delete_global_custom_rule(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        global_custom_rule = GlobalCustomRule()
        result = global_custom_rule.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Compliance_Detection', 'write')
def save_http_protocol_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)

            http_protocol=HttpProtocol()
            result = http_protocol.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Compliance_Detection', 'write')
def delete_http_protocol_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        http_protocol = HttpProtocol()
        result = http_protocol.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission_list(['WAF_Flow_Learning','WAF_Compliance_Detection'], 'write')
def save_parameter_detection_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)

            parameter_detection_strategy = ParameterDetection()
            result = parameter_detection_strategy.update_strategy(strategy, request)

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Compliance_Detection', 'write')
def delete_parameter_detection_strategy(request):
    result = {
        "is_success": False,
        "error": "",
    }
    if request.method == "POST":
        parameter_detection_strategy = ParameterDetection()
        result =  parameter_detection_strategy.delete_strategy(request)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Compliance_Detection', 'read')
def get_parameter_detection_strategy(request):
    result = {}
    try:
        if request.method == "GET":
            strategy_id = int(request.GET.get('id', ''))
            parameter_detection_strategy = ParameterDetection()
            result =  parameter_detection_strategy.get_parameter_detection_strategy(strategy_id)
    except Exception as e:
        result = None

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission_list(['WAF_Flow_Learning','WAF_Compliance_Detection'], 'read')
def get_parameter_detection_path(request):
    result = {}
    try:
        if request.method == "GET":
            url = request.GET.get('url', '')
            method = request.GET.get('method', '')
            domain = request.GET.get('domain', '')
            parameter_detection_strategy = ParameterDetection()
            result =  parameter_detection_strategy.get_parameter_detection_path(domain,url,method)
    except Exception as e:
        result = None

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@require_http_methods(['GET', 'POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'read')
def waf_settings(request):
    nginxConf = NginxConf()
    collection_mode_r = nginxConf.get_conf().get_value('nginx/waf/waf_setting/waf_log_setting/collection_mode',"time")
    time_per_second = nginxConf.get_conf().get_value('nginx/waf/waf_setting/waf_log_setting/time_per_second',10)
    col_response_body = nginxConf.get_conf().get_value('nginx/waf/waf_setting/waf_log_setting/col_response_body',False)
    detection_size = nginxConf.get_conf().get_value(WafConf.WAF_DETECTION_SIZE_PATH, WafConf.DEFAULT_DETECTION_SIZE)
    if request.method == 'POST':
        try:

            request_body = json.loads(request.body)

            values = {}
            collection_mode_w = request_body.get('collection_mode')
            col_response_body_w = request_body.get('col_response_body')
            if collection_mode_w not in ['all','time','not_col']:
                raise Exception('fail')
            if collection_mode_w == 'not_col':
                time_per_second_w = time_per_second
                col_response_body_w = col_response_body
            elif collection_mode_w == 'all':
                time_per_second_w = time_per_second
            else:
                time_per_second_w = request_body.get('time_per_second')
                waf_util.valid_number_range(time_per_second_w, max_v=1000000, min_v=1)
                time_per_second_w=int(time_per_second_w)

            waf_util.valid_bool(col_response_body_w)

            if collection_mode_w != collection_mode_r:
                values.update({'nginx/waf/waf_setting/waf_log_setting/collection_mode': collection_mode_w})

            if time_per_second_w != time_per_second:
                values.update({'nginx/waf/waf_setting/waf_log_setting/time_per_second': time_per_second_w})

            if col_response_body_w != col_response_body:
                values.update({'nginx/waf/waf_setting/waf_log_setting/col_response_body': col_response_body_w})

            if values:
                code, output, cmd_line = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Settings Log Collect')})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "0", {'msg': ugettext_noop('Set WAF Settings Log Collect')})
            return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Settings Log Collect')})
            return JsonResponse({'save_success': False})

    waf_source_list = resource_file.get_resource_file_in_zk()
    if collection_mode_r is None:
        collection_mode_r = 'time'
    if time_per_second is None:
        time_per_second = 10
    if col_response_body is None:
        col_response_body = False

    waf_adaption_decode = nginxConf.get_conf().get_value('nginx/waf/waf_setting/adaption_decode', DefaultAdaptionDecodeConfig)
    upload_file_inflate = nginxConf.get_conf().get_value(nginxConf.PATH_WafUploadInflate, DefaultUploadInflateConfig)
    waf_adaption_decode.setdefault('plus_escape', True)
    waf_adaption_decode.setdefault('multipart_parse', True)
    block_config = nginxConf.get_conf().get_value('nginx/waf/waf_setting/block_config', DefaultWafBlockConfig)
    result = {'collection_mode': collection_mode_r,'col_response_body':col_response_body,
              'time_per_second':time_per_second,
              'adaption_decode': waf_adaption_decode,
              'upload_file_inflate': upload_file_inflate,
              'waf_source_list':waf_source_list,
              'block_config':block_config,
              'detection_size': detection_size
    }

    if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':
        result['waf_for_api_request_only'] = nginxConf.get_conf().get_value('nginx/waf/waf_setting/waf_for_api_request_only', DefaultWafForApiRequestOnly)

    log_threshold_setting = waf_log_threshold_setting()
    result.update(log_threshold_setting)
    return base_render_to_response(request, 'v2/waf/waf_settings.html', result)

def waf_log_threshold_setting():
    from utils import GetWafSecurityLogType
    nginxConf = NginxConf()
    log_threshold_config_path = "nginx/waf/waf_setting/log_threshold"
    inject_attack_types = GetWafSecurityLogType().get_attack_type_info()
    inject_attack_types = [item  for item in inject_attack_types if 'all' not in item.get('key') ]

    result = {}
    result['inject_attack_types'] = inject_attack_types
    result['log_threshold_setting'] = nginxConf.get_conf().get_value(log_threshold_config_path, [])
    result['enabled_full_traffic_filter_log'] =  nginxConf.get_conf().get_value('logserver/full_traffic_filter/enabled', False)
    return result

@login_required
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'read')
def waf_log_threshold(request):
    nginxConf = NginxConf()
    log_threshold_config_path = "nginx/waf/waf_setting/log_threshold"

    def valid_setting(setting):
        if  not isinstance(setting, list):
            logging.error('log threshold setting contains error setting {}'.format(setting))
            return False

        for item in setting:
            if len(item.keys()) > 3:
                logging.error('log threshold setting contains error setting {}'.format(setting))
                return False

            if not re.findall('^[\w-]{1,50}$',item.get('inject_attack_type')):
                logging.error('log threshold setting contains error inject_attack_type {}'.format(item.get('inject_attack_type')))
                return  False

            if not re.findall('^(high|all|no)$',item.get('logging_threshold')):
                logging.error('log threshold setting contains error logging_threshold {}'.format(item.get('logging_threshold')))
                return  False

            if len(item.get('comment','')) > 200:
                logging.error('log threshold setting contains error comment {}'.format(item.get('comment')))
                return False

            keys = set([item.get('inject_attack_type') for item in setting])
            if len(keys) < len(setting):
                logging.error('log threshold setting contains dupicate inject_attack_type {}'.format(setting))
                return False
        return True

    if request.method == 'POST':
        try:
            waf_log_threshold_r = nginxConf.get_conf().get_value(log_threshold_config_path,[])
            waf_log_threshold_w = json.loads(request.body)
            values = {}
            if (waf_log_threshold_r == waf_log_threshold_w):
                pass
            else:

                if not valid_setting(waf_log_threshold_w):
                    raise Exception('setting error')
                values.update({log_threshold_config_path: waf_log_threshold_w})

            if values:
                code, _, _ = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF attack log threshold')})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "0", {'msg': ugettext_noop('Set WAF attack log threshold')})
            return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF attack log threshold')})
            return JsonResponse({'save_success': False})

@login_required
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'write')
def waf_detection_size(request):
    nginxConf = NginxConf()
    def vailid_detection_size(detection_size):
        if len(detection_size.keys()) > 3:
            logging.error('detection_size setting contains error setting {}'.format(detection_size))
            return False
        body_size_limit = detection_size.get('body_size_limit')
        file_size_limit = detection_size.get('file_size_limit')
        resp_body_size_limit = detection_size.get('resp_body_size_limit')
        if not isinstance(body_size_limit, int) or body_size_limit < 0 or body_size_limit > 1024:
            logging.error('detection_size setting contains error setting {}'.format(body_size_limit))
            return False
        if not isinstance(file_size_limit, int) or file_size_limit < 0 or file_size_limit > 102400:
            logging.error('detection_size setting contains error setting {}'.format(file_size_limit))
            return False
        if not isinstance(resp_body_size_limit, int) or resp_body_size_limit < 0 or resp_body_size_limit > 1024:
            logging.error('detection_size setting contains error setting {}'.format(resp_body_size_limit))
            return False

        return True

    if request.method == 'POST':
        try:
            detection_size_old = nginxConf.get_conf().get_value(WafConf.WAF_DETECTION_SIZE_PATH, {})
            detection_size = json.loads(request.body)
            values = {}
            if (detection_size_old == detection_size):
                return JsonResponse({'save_success': True})
            else :
                if not vailid_detection_size(detection_size):
                    raise Exception('setting error')
                values.update({WafConf.WAF_DETECTION_SIZE_PATH: detection_size})
            if values:
                code, _, _ = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Detection Size')})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "0",
                                  {'msg': ugettext_noop('Set WAF Detection Size')})
                    return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Detection Size')})
            return JsonResponse({'save_success': False})

@login_required
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'read')
def waf_adaption_decode(request):
    nginxConf = NginxConf()
    if request.method == 'POST':
        try:
            waf_adaption_decode_r = nginxConf.get_conf().get_value('nginx/waf/waf_setting/adaption_decode', {})
            waf_adaption_decode_w = json.loads(request.body)
            values = {}
            if (waf_adaption_decode_r == waf_adaption_decode_w):        
                pass
            else :
                values.update({'nginx/waf/waf_setting/adaption_decode': waf_adaption_decode_w})

            if values:
                code, _, _ = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Auto Adaption Decoder')})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "0", {'msg': ugettext_noop('Set WAF Auto Adaption Decoder')})
            return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Auto Adaption Decoder')})
            return JsonResponse({'save_success': False})

@login_required
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'write')
def waf_upload_inflate(request):
    nginxConf = NginxConf()
    if request.method == 'POST':
        try:
            waf_upload_inflate_r = nginxConf.get_conf().get_value(nginxConf.PATH_WafUploadInflate, DefaultUploadInflateConfig)
            waf_upload_inflate_w = json.loads(request.body)
            values = {}
            if (waf_upload_inflate_r == waf_upload_inflate_w):        
                pass
            else :
                values.update({nginxConf.PATH_WafUploadInflate: waf_upload_inflate_w})

            if values:
                code, _, _ = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Upload File Inflate')})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "0", {'msg': ugettext_noop('Set WAF Upload File Inflate')})
            return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF Upload File Inflate')})
            return JsonResponse({'save_success': False})

def is_ipv6(ip):
    try:
        socket.inet_pton(socket.AF_INET6, ip)
    except socket.error:  # not a valid ip
        return False
    return True

# =============================== about auto list and black ip ===================================================
def clean_waf_shm(data):
    result = {
        'save_success': True,
        'error_msg': ''
    }

    try:
        result_code, _, error_msg = service_mgr_set_command('clean_waf_shm', data)
        if result_code != 0:
            result['save_success'] = False
            result['error_msg'] = _("No Response")
            logging.error("remove_auto_list: notify nginx failed.response : {}".format(error_msg))

    except Exception as e:
        logging.exception('remove_auto_list: failed to request {} exception:{}'.format(data, str(e)))
        result['error_msg'] = str(e)
        result['save_success'] = False

    return result

@login_required
@require_http_methods(['GET', 'POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'read')
def waf_setting_remove_black_ip(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            ip_black = data.get('black_ip', None)
            ip = re.compile(r'^\d+\.\d+\.\d+\.\d+$')
            ipv6 = is_ipv6(ip_black)

            result = {
                'save_success': False,
                'error_msg': _('Invalid IP address')
            }

            if ip_black:
                invalid_ip = False
                if ip_black == '0.0.0.0':
                    target_type = 'auto_ip_all'
                elif ip.findall(ip_black) or ipv6:
                    target_type = 'auto_ip'
                else:
                    invalid_ip = True

                if not invalid_ip:
                    send_data = {
                        'type': target_type,
                        'value': ip_black,
                    }
                    rt = clean_waf_shm(send_data)
                    if rt.get('save_success', False):
                        result = rt
                        record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                             ugettext_noop('Auto_black_ip'), ip_black)

                    else:
                        record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                             ugettext_noop('Auto_black_ip'), ip_black)

                        logger.error('remove_black_ip error: {0}'.format(rt))
                        result['error_msg'] = _("Failed to remove auto black IP")
                else:
                    record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                         ugettext_noop('Auto_black_ip'), ip_black)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== about block_config ===================================================
@login_required
@require_http_methods(['GET', 'POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'write')
def waf_setting_save_block_config(request):
    nginxConf = NginxConf()
    def valid_setting(setting):
        if not isinstance(setting, dict):
            logger.error('waf block_config contains error setting {}'.format(setting))
            return False

        block_code = int(setting.get("block_code",0))
        if block_code in [407, 408, 444, 499]:
            logger.error('waf block_config block_code error {}'.format(block_code))
            return False
        if block_code:
            if 200 <= int(block_code) <= 600:
                return True
        logger.error('waf block_config block_code error {}'.format(block_code))
        return False

    if request.method == 'POST':
        try:
            block_config_r = nginxConf.get_conf().get_value('nginx/waf/waf_setting/block_config', copy.deepcopy(DefaultWafBlockConfig))
            block_config_w = json.loads(request.body)
            block_code_r = block_config_r.get("block_code")
            enable_block_content_r =  block_config_r.get("enable_block_content")


            block_code_w = block_config_w.get("block_code")
            enable_block_content_w = block_config_w.get("enable_block_content")

            values = {}
            if enable_block_content_w is None or enable_block_content_r == enable_block_content_w:
                pass
            else:
                if type(enable_block_content_w) is not bool:
                    raise Exception('setting error')
                block_config_r['enable_block_content'] = enable_block_content_w
                values.update({'nginx/waf/waf_setting/block_config': block_config_r})

                if enable_block_content_w:
                    op_msg = ugettext_noop('Enable Response for Abnormal Requests')
                    op_action = ugettext_noop("Open")
                else:
                    op_action = ugettext_noop("Close")
                    op_msg = ugettext_noop('Disable Response for Abnormal Requests')


            if block_code_w is None or (block_code_w == block_code_r):
                pass
            else :
                if not valid_setting(block_config_w):
                    raise Exception('setting error')
                block_config_r['block_code'] = block_code_w
                values.update({'nginx/waf/waf_setting/block_config': block_config_r})
                op_msg = ugettext_noop('Set WAF Block Config')
                op_action = ugettext_noop("Modify")

            if values:
                code, _, _ = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), op_action, "1", {'msg': op_msg})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), op_action, "0", {'msg':op_msg})
            return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), op_action, "1", {'msg': op_msg})
            return JsonResponse({'save_success': False})


    return JsonResponse({'save_success': False})


# =============================== waf_for_api_request_only ===================================================
@login_required
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@check_permission('WAF_Config', 'read')
def waf_for_api_request_only(request):
    nginxConf = NginxConf()
    if request.method == 'POST':
        try:
            waf_for_api_request_only_r = nginxConf.get_conf().get_value('nginx/waf/waf_setting/waf_for_api_request_only', DefaultWafForApiRequestOnly)
            waf_for_api_request_only_w = json.loads(request.body)
            values = {}
            if (waf_for_api_request_only_r == waf_for_api_request_only_w):
                pass
            else :
                values.update({'nginx/waf/waf_setting/waf_for_api_request_only': waf_for_api_request_only_w})

            if values:
                code, _, _ = nginxConf.set_asp_conf_values(values)
                if code != 0:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF for api request only')})
                    return JsonResponse({'save_success': False})
                else:
                    operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "0", {'msg': ugettext_noop('Set WAF for api request only')})
            return JsonResponse({'save_success': True})
        except Exception as e:
            operation_log(request, ugettext_noop('WAF'), ugettext_noop("Modify"), "1", {'msg': ugettext_noop('Set WAF for api request only')})
            return JsonResponse({'save_success': False})

# =============================== LLM_Protection===================================================


@login_required
@check_permission('LLM_Protection', 'write')
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def save_prompt_injection_strategy(request):
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            llm_prompt = LLMPromptInjectionProtection()
            result = llm_prompt.update_strategy(strategy, request)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_Protection', 'write')
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def delete_prompt_injection_strategy(request):
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":
        llm_prompt = LLMPromptInjectionProtection()
        result = llm_prompt.delete_strategy(request)
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_Protection', 'read')
@require_http_methods(['GET'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def get_prompt_injection_strategy(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_llm_security(request)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_Protection', 'write')
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def save_sensitive_detection_strategy(request):
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":
        try:
            strategy = json.loads(request.body)
            llm_sensitive = LLMSensitiveDetection()
            result = llm_sensitive.update_strategy(strategy, request)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('LLM_Protection', 'write')
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def delete_sensitive_detection_strategy(request):
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":
        llm_sensitive = LLMSensitiveDetection()
        result = llm_sensitive.delete_strategy(request)
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_Protection', 'read')
@require_http_methods(['GET'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def get_sensitive_detection_strategy(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_llm_security(request)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_SETTING', 'read')
@require_http_methods(['GET'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def get_llm_setting(request):
    """获取提示词黑白名单配置"""
    if request.method == "GET":
        try:
            llm_prompt_config = LLMPromptListConfig()
            config = llm_prompt_config.get_prompt_list()
            result = {
                'is_success': True,
                'data': {
                    'blacklist': config.get('blacklist', []),
                    'whitelist': config.get('whitelist', []),
                }
            }
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('LLM_SETTING', 'write')
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def save_prompt_blacklist(request):
    """保存提示词黑名单"""
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":
        try:
            prompt_list = json.loads(request.body)
            
            llm_prompt_config = LLMPromptListConfig()
            current_config = llm_prompt_config.get_prompt_list()
            
            # 验证黑名单列表
            llm_prompt_config.is_valid_prompt_list(prompt_list)
            
            # 更新黑名单
            current_config['blacklist'] = prompt_list
            llm_prompt_config.save_prompt_list(current_config)
            
            result['is_success'] = True
            operation_log(request, llm_prompt_config.MODULE, ugettext_noop("Modify"), '0',
                        {'msg': ugettext_noop('Update LLM prompt blacklist')})
            
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            operation_log(request, llm_prompt_config.MODULE, ugettext_noop("Modify"),'1',
                          {'msg': ugettext_noop('Update LLM prompt blacklist')})
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_SETTING', 'write')
@require_http_methods(['POST'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def save_prompt_whitelist(request):
    """保存提示词白名单"""
    result = {
        "is_success": False,
        "error_msg": "",
    }
    if request.method == "POST":
        try:
            prompt_list = json.loads(request.body)
            
            llm_prompt_config = LLMPromptListConfig()
            current_config = llm_prompt_config.get_prompt_list()
            
            # 验证白名单列表
            llm_prompt_config.is_valid_prompt_list(prompt_list)
            
            # 更新白名单
            current_config['whitelist'] = prompt_list
            llm_prompt_config.save_prompt_list(current_config)
            
            result['is_success'] = True
            operation_log(request, llm_prompt_config.MODULE, ugettext_noop("Modify"), '0',
                        {'msg': ugettext_noop('Update LLM prompt whitelist')})
            
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            operation_log(request, llm_prompt_config.MODULE, ugettext_noop("Modify"), '1',
                          {'msg': ugettext_noop('Update LLM prompt whitelist')})
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('LLM_Protection', 'read')
@require_http_methods(['GET'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def llm_protection(request):
    result = {}
    try:
        strategies = get_all_strategy_for_llm_security(request)
        upstreams = get_upstream_with_llm()
        result["waf_strategy"] = strategies
        result["upstream_waf"] = upstreams
        result["info_module"] = "llm"
    except Exception as e:
        logger.exception(e)

    return base_render_to_response(request, 'v2/llm_security_protection.html', result)

@login_required
@require_http_methods(['GET'])
@check_permission_list(['Add_Website','Edit_Website_Config','Show_Website'], 'read')
def get_llm_security_strategy(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_llm_security(request)
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
# @has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)

@login_required
@check_permission('LLM_Protection', 'read')
@require_http_methods(['GET'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def get_sensitive_detection_strategy(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_llm_security(request,power_strategy_list=[LLMSensitiveDetection])
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
# @has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)


@login_required
@check_permission('LLM_Protection', 'read')
@require_http_methods(['GET'])
@has_dm_permission(LicenseInfo.DM_LLM_PROTECTION)
def get_prompt_injection_strategy(request):
    if request.method == "GET":
        try:
            result = get_all_strategy_for_llm_security(request,power_strategy_list=[LLMPromptInjectionProtection])
        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
# @has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
