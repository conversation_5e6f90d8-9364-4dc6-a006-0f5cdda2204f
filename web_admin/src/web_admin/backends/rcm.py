import logging
import base64
import urlparse
import hmac
import hashlib
import time

from django.contrib.auth.models import User, Group
from django.core.exceptions import PermissionDenied

from web_admin.Conf_Webconsole import WebconsoleConf

logger = logging.getLogger('rcm')


def encoded(s):
    return s if isinstance(s, str) else s.encode('utf-8')


class RCMBackend(object):

    def authenticate(self, username=None, password=None):
        # RCMBackend only handle the login request from the url /rcm_login
        if username == 'RCM':
            params = urlparse.parse_qs(base64.urlsafe_b64decode(encoded(password)))
            account = params['username'][0]
            tokenid = params['tokenid'][0]
            timestamp = params['timestamp'][0]
            nonce = params['nonce'][0]
            signature = params['signature'][0]

            webconsole_conf = WebconsoleConf()

            if account == 'admin' and webconsole_conf.is_admin_user_disabled():
                raise PermissionDenied

            cluster_fingerprint = webconsole_conf.get_value('cluster/fingerprint', is_abs=True)
            api_token_value = webconsole_conf.get_value('api_gateway/token/%s' % tokenid, is_abs=True)['secret']

            str_to_sign = account + '\n' + tokenid + '\n' + timestamp + '\n' + nonce + '\n' + cluster_fingerprint

            if signature.upper() == hmac.new(encoded(api_token_value), encoded(str_to_sign), hashlib.sha256).hexdigest().upper():
                if not webconsole_conf.is_user_exist(account):
                    logger.debug('the account %s does not exist.' % account)
                    return None

                if int(time.time()) - int(timestamp) > 10:
                    logger.debug('the token is not valid as timeout')
                    return None

                try:
                    user = User.objects.get(username=account)
                except User.DoesNotExist:
                    # create the user in Django DB if login for the 1st time
                    # this may happen in cluster environment
                    user = User(username=account)
                    pwd = webconsole_conf.get_user_password(account)
                    user.set_password(pwd)
                    user.save()
                    role = webconsole_conf.get_user_role(account)
                    group = Group.objects.get(name=role)
                    user.groups.add(group)
                return user
            else:
                return None
        else:
            return None

    def get_user(self, user_id):
        try:
            return User.objects.get(pk=user_id)
        except User.DoesNotExist:
            return None
