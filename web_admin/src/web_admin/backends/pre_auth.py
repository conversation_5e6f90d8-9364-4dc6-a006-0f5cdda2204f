from django.contrib.auth.hashers import check_password
from django.core.exceptions import PermissionDenied
from web_admin.Conf_Webconsole import WebconsoleConf

class PreAuthBackend(object):

    def authenticate(self, username=None, password=None):
        conf = WebconsoleConf()
        if (not conf.is_user_exist(username)) or conf.is_user_locked(username):
            raise PermissionDenied
        if username == 'admin' and conf.is_admin_user_disabled():
            raise PermissionDenied

        return None

    def get_user(self, user_id):
        return None

