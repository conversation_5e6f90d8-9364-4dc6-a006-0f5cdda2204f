#-*- coding:utf-8 -*-

import random
import string
import time
import json
from Conf_Base import BaseConf
from Conf_Mpp import WechatConf, AlipayConf, MPaasConf
from asp_utils.utils import get_release_file, get_sync_file_path, RESULT, generate_token_rename, remove_file, remove_dir
from asp_conf_ctrl import ConfDb, AspConfCtrl
from asp_utils.CommonLibs import valid_IPv4,valid_IPv6,valid_Domain,valid_Regex
from access_log_analyser_lite.corelib_consts import BUSINESS_TYPE_MAPPING, MOBILE_RESPONSE_TOKEN_MAX_LEN_2700, MOBILE_RESPONSE_TOKEN_MAX_LEN_2400
from service_mgr_rest_api import service_mgr_syncfile_remove
from django.utils.translation import ugettext as _, ugettext_noop

MOBILE_CONFIG_VERSION_V3_BIT = 3  # DAP-31732

def oscmd_inject_delimiter(candidates):
    # result = [ '\\' +  candidate for candidate in candidates]
    result = candidates
    return "[" + "".join(result) + "]"


def oscmd_inject_command(candidates):
    candidates.sort()
    prev = ''
    result = '\\b('
    count = 0
    for item in candidates:
        cur = item
        if prev == '':
            prev = cur
            count = 1
            continue
        if cur[:1] != prev[:1]:
            if count > 1:
                result = result + '|' + prev[1:] + ')|'
            else:
                result = result + prev + '|'
            count = 1
        else:
            if count > 1:
                result = result + '|' + prev[1:]
            else:
                result = result + prev[:1] + '(' + prev[1:]
            count = count + 1

        prev = cur

    if count > 1:
        result = result + '|' + prev[1:] + ')\\b'
    else:
        result = result + prev + ')\\b'
    return result


def oscmd_inject_linux():
    delimiter = ['\|', '\`', '\;']
    cmds = ['chgrp', 'chmod', 'chown', 'chsh', 'cmd', 'cpp', 'passwd', 'python', 'perl', 'ping', 'ps',
            'nasm', 'nmap', 'nc', 'finger', 'ftp', 'kill', 'mail', 'xterm', 'rm', 'ls', 'lsof', 'telnet',
            'uname', 'echo', 'id', 'gcc', 'cd', 'tftp', 'traceroute', 'tclsh8?', 'cc', 'wget', 'curl',
            'cat', 'halt', 'reboot', 'shutdown',
            ]

    result = oscmd_inject_delimiter(delimiter) + \
        '\W*?' + \
        oscmd_inject_command(cmds)
    return result


def oscmd_inject_window():
    return '\\b((n(et(\\b\W+?\\blocalgroup|\.exe)|(map|c)\.exe)|t(racert|elnet\.exe)|(w(guest|sh)|rcmd|ftp)\.exe|echo\\b\W*?\\by+)\\b|c(md((\.exe|32)\\b|\\b\W*?\/c)))'


def oscmd_inject_pattern():
    result = '(' + oscmd_inject_window() + '|' + oscmd_inject_linux() + ')'
    print "os command injection pattern:" + result
    return result

import base64
import hashlib
import os
import re
from asp_utils.utils import exe
from Crypto.Cipher import AES
class MobileConf():
    def __init__(self, logger):
        self.logger = logger
        self.filePrefix = ''.join(random.sample(string.ascii_letters + string.digits, 16))
        self.files = []
        self.fnPubkey = str(self.filePrefix) + "_pubkey"
        self.fnPrikey = str(self.filePrefix) + "_prikey"
        self.fnSm2Pubkey = str(self.filePrefix) + "_sm2Pubkey"
        self.fnSm2Prikey = str(self.filePrefix) + "_sm2Prikey"
        self.fnSalt = str(self.filePrefix) + "_salt"
        self.fnPasswd = str(self.filePrefix) + "_passwd"
        self.fnSig = str(self.filePrefix) + "_sig"
        self.fnData = str(self.filePrefix) + "_data"
        self.fnRawCert = str(self.filePrefix) + "_rawCert"
        self.fnCert = str(self.filePrefix) + "_Cert"
        self.fnCert_v1 = "mobile_Cert_v1"

    def _data2file(self, filename, value):
        fd = open(filename, "w")
        fd.write(value)
        fd.close()
        self.files.append(filename)

    def _file2data(self, filename):
        fd = open(filename)
        self.files.append(filename)
        return fd.read()

    def _fn(self, name):
        return self.filePrefix+name

    def _get_salt(self,licenseInfo,nginxConf):
        confDb = nginxConf.get_conf()
        existFp = confDb.get_value('cluster/mobile/certFp', '')
        if existFp:
            existFpList = existFp.split('|')
            salt = existFpList[-1]
        else:
            salt = licenseInfo.get_dict().get("mobile_salt")

        return salt

    def config_v1(self, domains, passwd, licenseInfo, allowOffline, nginxConf):
        salt = licenseInfo.get_dict().get("mobile_salt")
        if salt is None or salt == '' or len(salt) < 12:
            self.logger.error("no mobile salt found!")
            return None
        try:
            # generate private/public key via salt and passwd
            binPath = get_release_file("bin/CertificateTools_v1")

            hashs = ""
            for domain in re.split(' |\r|\n|\t', domains):
                if len(domain) == 0:
                    continue
                lenIndicator = ""
                if domain.startswith("*"):
                    lenIndicator = domain.count(".")
                    lenIndicator = "*" + str(lenIndicator)
                hashs += ("#" if hashs != "" else "") + base64.b64encode(hashlib.sha256(domain).digest()) + lenIndicator
            self._data2file(self.fnData, hashs)

            pwd_flag = nginxConf.get_conf().get_value('cluster/mobile/pwd_flag')

            publicKey = nginxConf.get_conf().get_value('cluster/mobile/privateKey')
            privateKey = nginxConf.get_conf().get_value('cluster/mobile/publicKey')


            self._data2file(self.fnSalt, salt)
            self._data2file(self.fnPasswd, passwd)

            exe("{4} -b {0} -i {1} -p {2} -s {3} ecdsaGenFix".format(self.fnPubkey, self.fnPrikey, self.fnPasswd,
                                                                     self.fnSalt, binPath))
            publicKey_new = self._file2data(self.fnPubkey)
            privateKey_new = self._file2data(self.fnPrikey)

            # 1. first gennerate certificate
            # 2. the password and the salt are same with last time
            # 3. have pwd2 and update it
            if (publicKey is None or privateKey is None) \
                or (publicKey == publicKey_new and privateKey == privateKey_new) \
                or (pwd_flag is not None and pwd_flag == 1):
                publicKey = publicKey_new
                privateKey = privateKey_new
            else:
                self.files.remove(self.fnPubkey)
                self.files.remove(self.fnPrikey)
                self._data2file(self.fnPubkey, publicKey)
                self._data2file(self.fnPrikey, privateKey)

            #domains safe on mobile V2
            nginxConf.set_asp_conf_values({'cluster/mobile/privateKey': privateKey,
                                         'cluster/mobile/publicKey': publicKey})

            # construct raw certificate file
            exe("{4} -b {0} -i {1} -g {2} -c {3} ecdsaSign".format(self.fnPubkey, self.fnPrikey, self.fnSig,
                                                                   self.fnData, binPath))

            sig = self._file2data(self.fnSig)
            result = ""
            result += hashs + "\n"
            result += publicKey + "\n"
            result += sig + "\n"
            if allowOffline == True:
                result += "1"
            else:
                result += "0"
            self._data2file(self.fnRawCert, result)

            # encrypt certificate file
            exe("{2} -c {0} -t {1} aesEncrypt".format(self.fnRawCert, self.fnCert_v1, binPath))

            result = self._file2data(self.fnCert_v1)
            return result
        except Exception as e:
            self.logger.error("set mobile v1 certificate exception: %s" % str(e))
            return None
        finally:
            for fn in self.files:
                if fn != "mobile_Cert_v1":
                    exe("rm {0}".format(fn))
            self.files = []

    # there is no certID in lower version
    def _checkCertID(self, confDb):
        updateConfig = {}
        certID = confDb.get_value('cluster/mobile/cert_id')
        if certID is None:
            certID = ''.join(random.sample(string.ascii_letters + string.digits, 16))
            updateConfig['cluster/mobile/cert_id'] = certID
        return certID, updateConfig

    def _saveCertFp(self, salt, confDb):
        existFp = confDb.get_value('cluster/mobile/certFp', '')

        existFpList = existFp.split('|')
        if salt == existFpList[-1]:
            return {}

        if salt in existFpList:
            existFpList.remove(salt)

        existFpList.append(salt)
        newSaltList = '|'.join(existFpList)

        updateConfig = {}
        updateConfig['cluster/mobile/certFp'] = newSaltList

        return updateConfig

    def _getMd5Key(self, salt):
        md5Obj = hashlib.md5()
        md5Obj.update(salt)
        return base64.b64encode(md5Obj.digest(), 'Az')

    def _getHeadKey(self, salt):
        headerKey = self._getMd5Key(salt)
        return headerKey[0:8].lower()

    def _getCollectionPath(self, salt):
        collectionPath = self._getMd5Key(salt[6:] + salt[0:6])
        return collectionPath[0:12] + '/'

    def _getCrashPath(self, salt):
        crashPath = self._getMd5Key(salt[-6:] + salt[:-6])
        return crashPath[0:12]

    def _getUrlConfusionPath(self, salt):
        urlConfusionPath = self._getMd5Key(salt[3:] + salt[:-3])
        return urlConfusionPath[0:12]

    def _save_certificate_pwd(self, passwd, key, nginxConf):
        try:
            cryptor = AES.new(key[0:16], AES.MODE_CBC, key[16:32])
            length = 16
            count = len(passwd)
            if(count % length != 0) :
                add = length - (count % length)
            else:
                add = 0
            passwd = passwd + ('\0' * add)
            encrypt_pwd = cryptor.encrypt(str(passwd))
            base_encrypt_pwd = base64.b64encode(encrypt_pwd)

            confDb = nginxConf.get_conf()

            pwd2_old = confDb.get_value('cluster/mobile/pwd2')
            if pwd2_old is None or pwd2_old == '':
                pwd2_new = base_encrypt_pwd
            else:
                pwd2_list = pwd2_old.split('\n')

                if base_encrypt_pwd not in pwd2_list:
                    if len(pwd2_list) >= 10:
                        pwd2_list.pop(-1)
                else:
                    pwd2_list.remove(base_encrypt_pwd)
                pwd2_new = base_encrypt_pwd + '\n' + '\n'.join(pwd2_list)
            updateConfig = {}
            updateConfig['cluster/mobile/pwd2'] = pwd2_new
            return updateConfig
        except Exception as e:
            self.logger.error("Mobile certificate: save mobile pwd exception: %s" % str(e))
            return None

    def _checkAndUpdateKey(self, salt, passwd, enableChinaSecurity, updateKeyPair, nginxConf):
        confDb = nginxConf.get_conf()
        privateKey = confDb.get_value('cluster/mobile/privateKey_v2')
        privateKey_sm2 = confDb.get_value('cluster/mobile/privateKey_sm2')

        #First gen cert
        if not ((enableChinaSecurity and privateKey_sm2 is None) or privateKey is None or updateKeyPair):
            return {}, None

        try:
            confDb = nginxConf.get_conf()
            pwd_flag = confDb.get_value('cluster/mobile/pwd_flag')
            publicKey = confDb.get_value('cluster/mobile/publicKey_v2')
            publicKey_sm2 = confDb.get_value('cluster/mobile/publicKey_sm2')

            #generate private/public key via salt and passwd
            binPath = get_release_file("bin/CertificateTools")

            self._data2file(self.fnSalt, salt)
            self._data2file(self.fnPasswd, passwd)
            exe("{4} -b {0} -i {1} -p {2} -s {3} genRsaKey".format(self.fnPubkey, self.fnPrikey, self.fnPasswd,
                                                                    self.fnSalt, binPath))
            publicKey_new = self._file2data(self.fnPubkey)
            privateKey_new = self._file2data(self.fnPrikey)

            exe("{4} -b {0} -i {1} -p {2} -s {3} genSm2Key".format(self.fnSm2Pubkey, self.fnSm2Prikey, self.fnPasswd,
                                                                    self.fnSalt, binPath))

            publicKey_sm2_new = self._file2data(self.fnSm2Pubkey)
            privateKey_sm2_new = self._file2data(self.fnSm2Prikey)
            updateConfig = {}
            if self.is_update_keypair(publicKey, publicKey_new, privateKey, privateKey_new, \
                        publicKey_sm2, publicKey_sm2_new, privateKey_sm2, privateKey_sm2_new, \
                        enableChinaSecurity, pwd_flag):
                pwdConfig = self._save_certificate_pwd(passwd, salt, nginxConf)
                if pwdConfig is None:
                    return updateConfig, 'Save the mobile password failed!'
                updateConfig.update(pwdConfig)
                updateConfig['cluster/mobile/pwd_flag'] = 1

                publicKey = publicKey_new
                privateKey = privateKey_new
                publicKey_sm2 = publicKey_sm2_new
                privateKey_sm2 = privateKey_sm2_new
                headerKey = self._getHeadKey(salt)
                collectionPath = self._getCollectionPath(salt)
                crashPath = self._getCrashPath(salt)
                urlConfusionPath = self._getUrlConfusionPath(salt)

                updateConfig['cluster/mobile/privateKey_v2'] = privateKey
                updateConfig['cluster/mobile/publicKey_v2'] = publicKey
                updateConfig['cluster/mobile/header_key_2.0'] = headerKey
                updateConfig['cluster/mobile/collection_path'] = collectionPath
                updateConfig['cluster/mobile/crash_path'] = crashPath
                updateConfig['cluster/mobile/url_confusion_path'] = urlConfusionPath
                updateConfig['cluster/mobile/privateKey_sm2'] = privateKey_sm2
                updateConfig['cluster/mobile/publicKey_sm2'] = publicKey_sm2

            else:
                # handle the scene that error password or salt + low version that hasn't save 'collection-path'\'crash-path'\'url-confusion-path'
                if confDb.get_value('cluster/mobile/header_key_2.0', '') == '':
                    updateConfig['cluster/mobile/header_key_2.0'] = self._getHeadKey(salt)

                if confDb.get_value('cluster/mobile/collection_path', '') == '':
                    updateConfig['cluster/mobile/collection_path'] = self._getCollectionPath(salt)

                if confDb.get_value('cluster/mobile/crash_path', '') == '':
                    updateConfig['cluster/mobile/crash_path'] = self._getCrashPath(salt)

                if confDb.get_value('cluster/mobile/url_confusion_path', '') == '':
                    updateConfig['cluster/mobile/url_confusion_path'] = self._getUrlConfusionPath(salt)

            return updateConfig, None
        except Exception as e:
            self.logger.error("Mobile certificate: _checkAndUpdateKey %s" % str(e))

            return None, 'Generate mobile certificate key pair failed!'
        finally:
            for fn in self.files:
                exe("rm {0}".format(fn))
            self.files = []

    def _updateWithoutKeyPairConfig(self, domain_note_str, allowDataCollection, enableOfflineWithToken,
                enableHandshakeAuthentication, updateCfgVersion, enableChinaSecurity, confDb):
        updateConfig = {}
        updateConfig['cluster/mobile/domains'] = domain_note_str
        updateConfig['cluster/mobile/allow_data_collection'] = allowDataCollection
        updateConfig['cluster/mobile/enable_offline_with_token'] = enableOfflineWithToken
        updateConfig['cluster/mobile/enable_handshake_authentication'] = enableHandshakeAuthentication
        updateConfig['cluster/mobile/enable_china_security'] = enableChinaSecurity
        if updateCfgVersion:
            updateConfig['cluster/mobile/global_config_version'] = int(time.time()) | MOBILE_CONFIG_VERSION_V3_BIT

        return updateConfig

    def config(self, domains, domain_note_str, passwd, updateKeyPair, licenseInfo,
                allowOffline, allowDataCollection, enableOfflineWithToken,
                enableHandshakeAuthentication, updateCfgVersion, enableChinaSecurity, nginxConf):

        salt = self._get_salt(licenseInfo,nginxConf)
        if salt is None or salt == '' or len(salt) < 12:
            self.logger.error("no mobile salt found!")
            return None, _('No license found')
        try:
            confDb = nginxConf.get_conf()

            updateAllConfig = {}
            certID, certIDConfig = self._checkCertID(confDb)
            updateAllConfig.update(certIDConfig)

            certFpConfig = self._saveCertFp(salt, confDb)
            updateAllConfig.update(certFpConfig)

            keypairConfig,err_msg = self._checkAndUpdateKey(salt, passwd, enableChinaSecurity, updateKeyPair, nginxConf)
            if err_msg is not None:
                return None, err_msg
            updateAllConfig.update(keypairConfig)

            updateAllConfig.update(self._updateWithoutKeyPairConfig(domain_note_str, allowDataCollection, enableOfflineWithToken,
                        enableHandshakeAuthentication, updateCfgVersion, enableChinaSecurity, confDb))

            content, err_msg = self._generate_cert_value(confDb, domains, updateAllConfig, allowDataCollection,
                                    enableOfflineWithToken, enableHandshakeAuthentication, enableChinaSecurity, certID)
            if err_msg is not None:
                return None, err_msg
            nginxConf.set_asp_conf_values(updateAllConfig, None, False)
            return content, err_msg
        except Exception as e:
            self.logger.error("Mobile certificate: config exception: %s" % str(e))
            return None, _('Generate certificate failed!')

    def update_certificate(self, salt, enc_pwd):
        if salt is None or salt == '' or len(salt) < 44:
            self.logger.error("The salt is malformed!")
            return False, _("The salt is malformed!")
        try:
            nginxConf = NginxConf()
            confDb = nginxConf.get_conf()

            updateConfig = {}
            certFpConfig = self._saveCertFp(salt, confDb)
            updateConfig.update(certFpConfig)

            pwd_plain = ''
            if enc_pwd is not None:
                pwd_plain = self.decrypt_certificate_pwd(salt, enc_pwd, True)

            if pwd_plain == None:
                self.logger.error("The ecrypt password don't match with the salt!")
                return False, _("The ecrypt password don't match with the salt!")

            binPath = get_release_file("bin/CertificateTools")
            self._data2file(self.fnSalt, salt)
            self._data2file(self.fnPasswd, pwd_plain)

            exe("{4} -b {0} -i {1} -p {2} -s {3} genRsaKey".format(self.fnPubkey, self.fnPrikey, self.fnPasswd,
                                                                   self.fnSalt, binPath))
            publicKey = self._file2data(self.fnPubkey)
            privateKey = self._file2data(self.fnPrikey)

            exe("{4} -b {0} -i {1} -p {2} -s {3} genSm2Key".format(self.fnSm2Pubkey, self.fnSm2Prikey, self.fnPasswd,
                                                                   self.fnSalt, binPath))

            publicKey_sm2 = self._file2data(self.fnSm2Pubkey)
            privateKey_sm2 = self._file2data(self.fnSm2Prikey)


            headerKey = self._getHeadKey(salt)
            collectionPath = self._getCollectionPath(salt)
            crashPath = self._getCrashPath(salt)
            urlConfusionPath = self._getUrlConfusionPath(salt)


            updateConfig['cluster/mobile/privateKey_v2'] = privateKey
            updateConfig['cluster/mobile/publicKey_v2'] = publicKey
            updateConfig['cluster/mobile/header_key_2.0'] = headerKey
            updateConfig['cluster/mobile/collection_path'] = collectionPath
            updateConfig['cluster/mobile/crash_path'] = crashPath
            updateConfig['cluster/mobile/url_confusion_path'] = urlConfusionPath
            updateConfig['cluster/mobile/privateKey_sm2'] = privateKey_sm2
            updateConfig['cluster/mobile/publicKey_sm2'] = publicKey_sm2
            updateConfig['cluster/mobile/pwd_flag'] = 1
            updateConfig['cluster/mobile/pwd2'] = enc_pwd

            nginxConf.set_asp_conf_values(updateConfig)

            return True, None
        except Exception as e:
            self.logger.error("Mobile certificate: exception %s" % str(e))
            return False, _('reset failed!')
        finally:
            for fn in self.files:
                exe("rm {0}".format(fn))

    def is_update_keypair(self, publicKey, publicKey_new, privateKey, privateKey_new, \
                        publicKey_sm2, publicKey_sm2_new, privateKey_sm2, privateKey_sm2_new, \
                        enableChinaSecurity, pwd_flag):
        # 1. first gennerate certificate
        if publicKey is None or privateKey is None:
            return True

        # 2. Precondition: the password and the salt are same with last time
        # 2.1 rsa key pair is the same
        # 2.2 hvae not generated sm2 key pair
        # 2.3 sm2 key pair is the same
        if publicKey == publicKey_new and privateKey == privateKey_new and \
            (not enableChinaSecurity or publicKey_sm2 is None or privateKey_sm2 is None or \
                (publicKey_sm2 == publicKey_sm2_new and privateKey_sm2 == privateKey_sm2_new)):
            return True

        # update password, not including salt, because get_certificate_pwd_and_set_flag has set pwd_flag=1 if salt has no changed.
        if pwd_flag is not None and pwd_flag == 1:
            return True

        return False

    def _generate_cert_value(self, confDb, domains, updateAllConfig, allowDataCollection,
                enableOfflineWithToken, enableHandshakeAuthentication, enableChinaSecurity, certID):
        try:
            binPath = get_release_file("bin/CertificateTools")

            domainList = ""
            for domain in re.split(' |\r|\n|\t', domains):
                if len(domain) == 0:
                    continue
                domainList += ("#" if domainList!="" else "") + domain

            def getConf(path):
                return updateAllConfig.get(path, None) or confDb.get_value(path, "")

            publicKey = getConf('cluster/mobile/publicKey_v2')
            privateKey = getConf('cluster/mobile/privateKey_v2')
            publicKey_sm2 = getConf('cluster/mobile/publicKey_sm2')
            headerKey = getConf('cluster/mobile/header_key_2.0')
            collectionPath = getConf('cluster/mobile/collection_path')
            crashPath = getConf('cluster/mobile/crash_path')
            urlConfusionPath = getConf('cluster/mobile/url_confusion_path')
            pwd2_enc = getConf('cluster/mobile/pwd2')
            pwd2_list = pwd2_enc.split('\n')
            if len(pwd2_list) > 0:
                pwd2_enc = pwd2_list[0]
            else:
                pwd2_enc = ''

            data_collection_selection = confDb.get_value('cluster/mobile/data_collection_selection', "")

            result = ""
            result += domainList + "\n"
            result += publicKey + "\n"
            result += "1\n" # if (allowOffline == True) else "0\n"  ## at 1711, always allow offline
            result += "1\n" if (allowDataCollection == True) else "0\n"
            result += headerKey + "\n"
            result += collectionPath + "\n"
            result += crashPath

            self._data2file(self.fnData, result)
            self._data2file(self.fnPubkey, publicKey)
            self._data2file(self.fnPrikey, privateKey)
            #construct raw certificate file
            exe("{4} -b {0} -i {1} -g {2} -c {3} signData".format(self.fnPubkey, self.fnPrikey, self.fnSig, self.fnData, binPath))

            sig = self._file2data(self.fnSig)
            result += "\n" + sig
            result += "\n" + pwd2_enc
            result += "\n" + certID
            result += "\n" + ("1" if (enableOfflineWithToken == True) else "0")
            result += "\n" + ("1" if (enableHandshakeAuthentication == True) else "0")
            result += "\n" + str(data_collection_selection)
            result += "\n" + ("1" if (enableChinaSecurity == True) else "0")
            result += "\n" + (publicKey_sm2 if (publicKey_sm2 is not None) else "")
            result += "\n" + urlConfusionPath

            self._data2file(self.fnRawCert, result)

            #encrypt certificate file
            exe("{2} -c {0} -t {1} aesEncrypt".format(self.fnRawCert, self.fnCert, binPath))

            result = self._file2data(self.fnCert)

            return result, None
        except Exception as e:
            self.logger.error("mobile certificate exception: _generate_cert_value: %s" % str(e))
            return None, _('Generate certificate file failed!')
        finally:
            for fn in self.files:
                exe("rm {0}".format(fn))
            self.files = []

    def decrypt_certificate_pwd(self, key, base_encrypt_pwd, isSalt):
        try:
            if isSalt:
                cryptor = AES.new(key[0:16], AES.MODE_CBC, key[16:32])
            else:
                cluster_key_byte = key.decode('hex')
                cryptor = AES.new(cluster_key_byte, AES.MODE_CBC, cluster_key_byte[0:16])
            encrypt_pwd = base64.b64decode(base_encrypt_pwd)
            pwd = cryptor.decrypt(encrypt_pwd).rstrip('\0')

            if pwd == repr(pwd)[1:-1]:
                return pwd
            else:
                return None
        except Exception as e:
            self.logger.error("decrypt mobile pwd exception: %s" % str(e))
            return None

    def verify_key_pair(self, salt, pwd_plain, enableChinaSecurity, confDb):
        try:
            self._data2file(self.fnSalt, salt)
            self._data2file(self.fnPasswd, pwd_plain)

            binPath = get_release_file("bin/CertificateTools")

            if enableChinaSecurity:
                exe("{4} -b {0} -i {1} -p {2} -s {3} genSm2Key".format(self.fnSm2Pubkey, self.fnSm2Prikey, self.fnPasswd, self.fnSalt,
                                                                   binPath))

                publicKeySm2 = self._file2data(self.fnSm2Pubkey)
                privateKeySm2 = self._file2data(self.fnSm2Prikey)
                pubKeySm2Use = confDb.get_value('cluster/mobile/publicKey_sm2')
                priKeySm2Use = confDb.get_value('cluster/mobile/privateKey_sm2')

                if pubKeySm2Use is not None and priKeySm2Use is not None and \
                    (pubKeySm2Use != publicKeySm2 or priKeySm2Use != privateKeySm2):
                    return False

            exe("{4} -b {0} -i {1} -p {2} -s {3} genRsaKey".format(self.fnPubkey, self.fnPrikey, self.fnPasswd, self.fnSalt,
                                                                binPath))
            pubKeyUse = confDb.get_value('cluster/mobile/publicKey_v2')
            priKeyUse = confDb.get_value('cluster/mobile/privateKey_v2')

            publicKey = self._file2data(self.fnPubkey)
            privateKey = self._file2data(self.fnPrikey)

            if pubKeyUse is None or priKeyUse is None or pubKeyUse != publicKey or priKeyUse != privateKey:
                return False
            else:
                return True
        except Exception as e:
            self.logger.error("verify mobile key pair exception: %s" % str(e))
            return False
        finally:
            for fn in self.files:
                exe("rm {0}".format(fn))

    def get_certificate_pwd_and_set_flag(self):
        nginxConf = NginxConf()
        confDb = ConfDb()
        licenseInfo = confDb.get_license_info()

        if licenseInfo is None:
            return ''

        try:
            salt = self._get_salt(licenseInfo,nginxConf)
            pwd2 = confDb.get_value('cluster/mobile/pwd2')
            enableChinaSecurity = nginxConf.get_mobile_enable_china_security()
            pwd_flag = ConfDb().get_value('cluster/mobile/pwd_flag')

            if pwd2 is not None:
                pwd_plain = self.decrypt_certificate_pwd(salt, pwd2.split('\n')[0], True)
                if pwd_plain is None:
                    nginxConf.set_asp_conf_values({'cluster/mobile/pwd_flag': 0})
                    return ''

                if pwd_flag is not None:
                    return pwd_plain

                if self.verify_key_pair(salt, pwd_plain, enableChinaSecurity, confDb):
                    nginxConf.set_asp_conf_values({'cluster/mobile/pwd_flag': 1})
                    return pwd_plain
                else:
                    nginxConf.set_asp_conf_values({'cluster/mobile/pwd_flag': 0})
                    return ''
            else:
                pwd = confDb.get_value('cluster/mobile/pwd')
                cluster_key = confDb.get_value('cluster/key')
                if pwd is None or cluster_key is None:
                    return ''

                pwd_plain = self.decrypt_certificate_pwd(cluster_key, pwd, False)

                if pwd_plain is None:
                    nginxConf.set_asp_conf_values({'cluster/mobile/pwd_flag': 0})
                    return ''

                if pwd_flag is not None:
                    return pwd_plain

                if not self.verify_key_pair(salt, pwd_plain, enableChinaSecurity, confDb):
                    nginxConf.set_asp_conf_values({'cluster/mobile/pwd_flag': 0})
                    return ''

                # tran to pwd2
                if self._save_certificate_pwd(pwd_plain, salt, nginxConf) != '-1':
                    nginxConf.set_asp_conf_values({'cluster/mobile/pwd_flag': 1})

                return pwd_plain
        except Exception as e:
            self.logger.error("get_certificate_pwd_and_set_flag exception: %s" % str(e))
            return False

    # call when export system config
    def tran_certificate_pwd(self):
        pwd_flag = ConfDb().get_value('cluster/mobile/pwd_flag')
        if pwd_flag is None:
            self.get_certificate_pwd_and_set_flag()


def add_prefix_to_dict(expect_val_dict, exist_val_dict, prefix):
    """
    merge expect_val_dict and exist_val_dict with the prefix to the keys
    1. if key is only in expect_val_dict, keep the k, v
    2. if key is only in exist_val_dict, set the key = ''
    3. if key is both in expect_val_dict and exist_val_dict, keep the k,v in expect_val_dict
    """
    result = expect_val_dict.copy()
    result.update((k,  expect_val_dict.get(k))  for k in exist_val_dict)
    return {prefix + str(key): value for key, value in result.iteritems()}

class NginxConf(BaseConf):
    UK_Enabled = 'enabled'
    UK_ListenPort = 'ListenPort'
    UK_ServerList = 'UpstreamList'
    UK_Https = 'IsHttps'
    UK_ServerNameType = 'ServerNameType'
    UK_ServerName = 'ServerName'
    UK_TerminalEnabled = 'TerminalEnabled'
    UK_TerminalPort = 'TerminalPort'
    UK_LoadBalancingStrategy = 'load_balancing_strategy'
    UK_IsTerminalHttps = 'IsTerminalHttps'
    UK_UpstreamHttps = 'IsUpstreamHttps'
    UK_Enable_Mobile_List = 'enable_mobile_list'
    UK_Mobile_Enable_Online_Perceptron = 'mobile_enable_online_perceptron'
    UK_Enable_Mobile_Block_WhiteList = 'enable_mobile_block_whitelist'
    UK_Enable_Mobile_Special_Local_html_List = 'enable_special_local_html_list'
    UK_Enable_Mobile_UI_Widget_touch_config_List = 'enable_interested_widget_list'
    UK_Mobile_Enable_Ignore_Jsonp = 'mobile_enable_ignore_jsonp'
    UK_CERTIF = 'Certification'
    UK_CERTIF_KEY = 'CertificationKey'
    UK_ACTION = 'action'
    UK_SiteEntry = 'EntryPath'
    UK_MobileWhiteList = 'MobileWhiteList'
    UK_FullWhiteList = 'FullWhiteList'
    UK_FullWhiteListOut = "FullWhiteListOut"
    UK_VerificatioList = 'VerificationList'
    UK_EncapsulationListOut = 'EncapsulationListOut'
    UK_IpWhiteList = "IpWhiteList"
    UK_IpBlackList = "IpBlackList"
    UK_IpListSwitch = "IpListSwitch"
    UK_Inject_Patternlist = 'Inject_Patternlist'
    UK_RULE_SET = "rule_set"
    UK_WAF_STRATEGY = "waf_strategy"
    UK_Inject_Whitelist = "Inject_Whitelist"
    UK_WAF_CSRF_ENABLE = "waf_anti_csrf_enable"
    UK_WAF_CSRF_LIST = "waf_anti_csrf_list"
    UK_WAF_CSRF_SITE_WHITELIST = "waf_anti_csrf_site_whitelist"
    UK_WAF_RES_LEECH_STRATEGY = "waf_res_leech_strategy"
    UK_STATUS = '_RunningStatus'
    UK_UseBuiltInCert = "useBuiltInCert"
    UK_UseInternationalCert = "useInternationalCert"
    UK_UseChinaSecurityCert = "useChinaSecurityCert"
    UK_UseBuiltInChinaSecurityCert = "useBuiltInChinaSecurityCert"
    UK_GM_SIGN_CERTification = 'gm_sign_certification'
    UK_GM_SIGN_CERTificationKey = 'gm_sign_certificationKey'
    UK_GM_ENC_CERTification = 'gm_enc_certification'
    UK_GM_ENC_CERTificationKey = 'gm_enc_certificationKey'
    UK_X_Frame_Options = 'X_Frame_Options'
    UK_X_Frame_Opt_Allow_Uri = "x_frame_option_allow_uri"
    UK_Enable_X_Frame_Opt = "enable_x_frame_option"
    UK_X_Content_Type_Options = "X_Content_Type_Options"
    UK_Enable_X_Content_Type_Opt = "enable_X_Content_Type_Options"
    UK_X_XSS_Protection = "X_XSS_Protection"
    UK_X_XSS_Protection_report_uri = "X_XSS_Protection_report_uri"
    UK_Enable_X_XSS_Protection = "enable_X_XSS_Protection"
    UK_WL_Proxy_Client_IP = 'WL_Proxy_Client_IP'
    UK_Enable_WL_Proxy_Client_IP = 'enable_WL_Proxy_Client_IP'
    UK_Accept_Encoding = 'Accept_Encoding'
    UK_Enable_Accept_Encoding = 'enable_Accept_Encoding'
    UK_Host = 'Host'
    UK_Enable_host = 'enable_host'
    UK_X_Real_IP = 'X_Real_IP'
    UK_Enable_X_Real_IP = 'enable_X_Real_IP'
    UK_X_Forwarded_For = 'X_Forwarded_For'
    UK_Enable_X_Forwarded_For = 'enable_X_Forwarded_For'
    UK_SecurityLevelMin = 'security_level_min'
    UK_SecurityLevelMax = 'security_level_max'
    UK_Enable_Mobile_Protection = 'enable_mobile_protection'
    UK_No_Script = 'no_script'
    UK_No_Content_Type = 'no_content_type'
    UK_Content_Type_Overwrite = 'content_type_overwrite'
    UK_IE_RENDERING_MODE = 'ie_rendering_mode'
    UK_Learning_Mode = 'learning_mode'
    UK_SSL_Ciphers = 'ssl_ciphers'
    UK_Proxy_SSL_Ciphers = 'proxy_ssl_ciphers'
    UK_SSL_PROTOCOLS = 'ssl_protocols'
    UK_PROXY_SSL_PROTOCOLS = 'proxy_ssl_protocols'
    UK_Enable_Http2Https = 'enable_http2https'
    UK_Http2https_Org_Port = 'http2https_org_port'
    UK_Http2https_New_Port = 'http2https_new_port'
    UK_Limit_Except = 'limit_except'
    UK_Enable_Compatible_Mode = "enable_compatible_mode"
    UK_CHECK_CONSOLE_OPEN = "check_console_open_v2"
    UK_Enable_Client_Protection = "enable_client_protection"
    UK_PROTECTED_LIST = "protected_list"
    UK_MOBILE_ONLY = "mobile_only"
    UK_MOBILE_V1_PROCESS_MODE = "mobile_v1_process_mode"
    UK_MOBILE_PASSTHROUGH_COLLECTION = "mobile_passthrough_collection"
    UK_HEALTH_CHECK = 'health_check'
    UK_P3P_CP = 'P3P_CP'
    UK_ENABLE_P3P_OPTIONS = 'enable_P3P_Options'
    UK_ENABLE_ATTACK_SENSATION = "Enable_Attack_Sensation"
    UK_ENABLE_COOKIE_COLLECTION = "Enable_Cookie_Collection"
    UK_ENABLE_POST_DATA_COLLECTION = "Enable_Post_Data_Collection"
    UK_ENABLE_EXTRA_SENSATION = "Extra_Session_in_Cookie"
    UK_EXTRA_BUSINESS_DATA = "Extra_Business_Data"
    UK_ContentTypeOverwriteEntry = "ContentTypeOverwriteEntry"
    UK_ENABLE_CHARSET = "enable_charset"
    UK_CHARSET = "charset"
    UK_Enable_IP_Black = "enable_ip_black"
    UK_Enable_Good_Bot = "enable_good_bot"
    UK_Mobile_Protect_Rules = "mobile_protect_rules"
    UK_Request_Custom_Head_Key = 'Request_Custom_Head_Key'
    UK_Request_Custom_Head_Value = 'Request_Custom_Head_Value'
    UK_enable_Request_Custom_Head = 'enable_Request_Custom_Head'
    UK_Response_Custom_Head_Key = 'Response_Custom_Head_Key'
    UK_Response_Custom_Head_Value = 'Response_Custom_Head_Value'
    UK_Response_Custom_Head_Mode  = 'Response_Custom_Head_Mode'
    UK_enable_Response_Custom_Head = 'enable_Response_Custom_Head'
    UK_MobileBodyVerificationList = 'MobileBodyVerificationList'
    UK_MobileBodyWhiteList = 'MobileBodyWhiteList'
    UK_MobileBlockWhiteList = 'MobileBlockWhiteList'
    UK_MobileInterestedWidgetList = 'MobileInterestedWidgetList'
    UK_MobileSpecialLocalHtmlList = 'MobileSpecialLocalHtmlList'
    UK_MobileConfigVersion = 'mobile_config_version'
    UK_WAF_CUSTOM_SET_TEMPLATE = 'waf_custom_set_template'
    UK_WAF_ADD_DISABLED_SET = 'waf_add_disabled_set'
    UK_WAF_DEL_DISABLED_SET = 'waf_del_disabled_set'
    UK_Enable_HTTP_2 = 'enableHttp2'
    UK_Mobile_Risk_Interception = 'mobile_risk_interception'
    UK_Mobile_Enable_Url_Confusion = 'mobile_enable_url_confusion'
    UK_Disable_Upstream_Keepalive = 'disable_upstream_keepalive'
    UK_Keep_Http_Version = 'keep_http_version'

    DEFAULT_SECURITY_FEATURES = ['cookie_token', '', '', '', 'inspect', 'bot', '']
    PATH_WafUploadInflate = 'nginx/waf/waf_setting/upload_file_inflate'

    _upstreams_cache = None
    _conf_cache_mtime_and_inode = None

    def __init__(self):
        BaseConf.__init__(self, 'nginx/', 'conf_nginx')

    def get_license_info(self):
        return self.get_conf().get_license_info()

    def set_license(self, data):
        self.set_asp_conf_values({'cluster/license': data})

    def set_ssl_cert(self, cert, key):
        self.set_asp_conf_values({'cluster/ssl/root_ca_crt': cert, 'cluster/ssl/root_ca_key': key})

    def get_mobile_allow_offline(self):
        allowOffline = self.get_conf().get_value('cluster/mobile/allow_offline')
        return False if allowOffline is None else allowOffline

    def get_mobile_allow_data_collection(self):
        allowDataCollection = self.get_conf().get_value('cluster/mobile/allow_data_collection')
        return False if allowDataCollection is None else allowDataCollection

    def get_mobile_enable_offline_with_token(self):
        enableOfflineWithToken = self.get_conf().get_value('cluster/mobile/enable_offline_with_token')
        return True if enableOfflineWithToken is None else enableOfflineWithToken

    def get_mobile_enable_handshake_authentication(self):
        enableHandshakeAuthentication = self.get_conf().get_value('cluster/mobile/enable_handshake_authentication')
        return False if enableHandshakeAuthentication is None else enableHandshakeAuthentication

    def get_mobile_enable_china_security(self):
        enableChinaSecurity = self.get_conf().get_value('cluster/mobile/enable_china_security')
        return False if enableChinaSecurity is None else enableChinaSecurity

    def get_show_sdk_v1_support(self):
        showSdkV1Support = self.get_conf().get_value('nginx/show_sdk_v1_support')
        return False if showSdkV1Support is None else showSdkV1Support

    def set_mobile_allow_offline(self, allowOffline):
        self.set_asp_conf_values({'cluster/mobile/allow_offline': allowOffline})

    def get_data_collection_configurations(self):
        selectionItems = self.get_conf().get_value('cluster/mobile/data_collection_selection')
        if selectionItems is None:
            selectionItems = 0x1FDF
            self.set_asp_conf_values({'cluster/mobile/data_collection_selection': selectionItems})

        collectionInterval = self.get_conf().get_value('cluster/mobile/data_collection_interval')
        if collectionInterval is None:
            collectionInterval = 3600
            self.set_asp_conf_values({'cluster/mobile/data_collection_interval': collectionInterval})

        return selectionItems, collectionInterval

    def set_data_collection_configurations(self, selectionItems, collectionInterval):
        bUpdateConfigVersion = False
        oldSelectionItems = self.get_conf().get_value('cluster/mobile/data_collection_selection')
        if selectionItems != oldSelectionItems:
            self.set_asp_conf_values({'cluster/mobile/data_collection_selection': selectionItems})
            bUpdateConfigVersion = True

        oldCollectionInterval = self.get_conf().get_value('cluster/mobile/data_collection_interval')
        if oldCollectionInterval != collectionInterval:
            self.set_asp_conf_values({'cluster/mobile/data_collection_interval': collectionInterval})
            bUpdateConfigVersion = True

        if bUpdateConfigVersion:
            self.set_asp_conf_values({'cluster/mobile/global_config_version': int(time.time())})

    def get_response_token_max_value(self):
        maxValue = self.get_conf().get_value('cluster/mobile/response_token_max_value')
        if maxValue is None:
            maxValue = '4k'
            self.set_asp_conf_values({'cluster/mobile/response_token_max_value': maxValue})

        return maxValue

    def get_response_token_maximum_size(self):
        maxValue = self.get_response_token_max_value()
        if maxValue == '8k':
            return MOBILE_RESPONSE_TOKEN_MAX_LEN_2700;
        else:
            return MOBILE_RESPONSE_TOKEN_MAX_LEN_2400;

    def set_response_token_max_value(self, maxValue):
        self.set_asp_conf_values({'cluster/mobile/response_token_max_value': maxValue})

    def set_mobile_cfg(self, domains, notes, passwd, allowDataCollection, enableOfflineWithToken, enableHandshakeAuthentication, enableChinaSecurity, updateKeyPair=True):

        domains_len = 0
        for item in domains:
            domains_len += len(item) + 1

        maximum_size = self.get_response_token_maximum_size()
        if domains_len > maximum_size:
            self.logger.error("The total length of the URL protection list of the mobile certificate is {0}, cannot exceed {1} characters.".format(domains_len, maximum_size))
            return None, _('The total length of the URL protection list of the mobile certificate cannot exceed {} characters.').format(maximum_size)

        mobileDomainNoteList = self.get_mobile_cfg()
        old_domain_list = [mobileDomainNoteList[i][0] for i in range(len(mobileDomainNoteList))]
        updateCfgVersion = False
        if set(old_domain_list) != set(domains) \
                or self.get_mobile_allow_data_collection() != allowDataCollection \
                or self.get_mobile_enable_offline_with_token() != enableOfflineWithToken \
                or self.get_mobile_enable_handshake_authentication() != enableHandshakeAuthentication \
                or self.get_mobile_enable_china_security() != enableChinaSecurity:
            updateCfgVersion = True

        lic = self.get_license_info()
        mobile = MobileConf(self.logger)
        allowOffline = self.get_mobile_allow_offline()

        domains_str = ''
        domains_notes_str = ''
        for index in range(0, len(domains)):
            domains_str += domains[index] + '\n'
            domains_notes_str += domains[index] + '|' + notes[index] + '\n'

        if len(domains_notes_str) > 1:
            domains_notes_str = domains_notes_str[:-1]

        #mobile.config_v1(domains_str, passwd, lic, allowOffline, self)

        certContent = mobile.config(domains_str, domains_notes_str, passwd, updateKeyPair, lic,
                        allowOffline, allowDataCollection, enableOfflineWithToken,
                        enableHandshakeAuthentication, updateCfgVersion, enableChinaSecurity, self)

        return certContent

    def reset_mobile_cert(self,salt, enc_pwd):
        mobile = MobileConf(self.logger)
        return mobile.update_certificate(salt, enc_pwd)

    def get_mobile_cfg(self):
        domains_notes = self.get_conf().get_value('cluster/mobile/domains')
        if domains_notes is None:
            return []
        else:
            domains_note_list = []
            for domain_note in domains_notes.split('\n'):
                if '|' not in domain_note:
                    domains_note_list.append((domain_note, ''))
                else:
                    domains_note_list.append(domain_note.split('|', 1))
            return domains_note_list

    def get_mobile_filename(self):
        key = self.get_conf().get_value('cluster/fixed_key')
        key = hashlib.sha256(key).hexdigest()
        return key[:8]

    def get_certificate_pwd_and_set_flag(self):
        mobile = MobileConf(self.logger)
        return mobile.get_certificate_pwd_and_set_flag()

    def get_mobile_token_path_name(self):
        confDb = self.get_conf()
        token_name = confDb.get_value('cluster/mobile/header_key', '')
        token_name = confDb.get_value('cluster/mobile/header_key_2.0', token_name)
        collection_path = confDb.get_value('cluster/mobile/collection_path', '')
        crash_path = confDb.get_value('cluster/mobile/crash_path', '')
        urlConfusionPath = confDb.get_value('cluster/mobile/url_confusion_path', '')

        return token_name, collection_path, crash_path, urlConfusionPath

    def set_rules(self, data):
        self.set_asp_conf_values([['cluster/adrules', data]])

    def set_waf_rules(self, data, filename, node_id, c_time):
        ret = {'md5': data, 'filename': filename, 'node': node_id, 'time': c_time}
        self.set_asp_conf_values([('nginx/wafrules', ret)])

    def set_upgrade_waf_ruleset(self, action):
        ret = {'action': action, 'time': time.time()}
        return self.set_asp_conf_values([('nginx/upgrade_waf_ruleset', ret)])

    def set_upgrade_llm_corpus(self, action):
        ret = {'action': action, 'time': time.time()}
        return self.set_asp_conf_values([('nginx/upgrade_llm_corpus', ret)])

    def get_all_upstream_site_names(self):
        '''
        Return all upstream site names.
        '''
        upstreams = self.get_all_upstreams(readonly=True)
        return [upstream['key'] for upstream in upstreams]

    def get_all_existed_server_names(self):
        server_names = []
        upstreams = self.get_all_upstreams()
        for site in upstreams:
            deleted = site.get('_deleted')
            if not deleted:
                name = site.get('ServerName')
                if not name:
                    name = site.get('name')
                port = site.get('ListenPort')
                serverNameType = site.get('ServerNameType')
                name = serverNameType + '_' + name + '_' + port
                if site.get('enable_business_path', False):
                    name += '_' + site.get('business_path', '')
                server_names.append(name)

        return server_names

    def get_all_upstreams(self, readonly=False):
        '''
        Return all upstream site names.
        '''
        mtime_and_inode = None
        if readonly and os.path.exists(self.get_conf().fn):
            fn_stat = os.stat(self.get_conf().fn)
            mtime_and_inode = fn_stat.st_ino, fn_stat.st_mtime
            if mtime_and_inode == self.__class__._conf_cache_mtime_and_inode and self.__class__._upstreams_cache:
                return self.__class__._upstreams_cache

        upstreams = self.get_conf().get_all('nginx/upstreams/')
        values = []
        for k, v in upstreams.items():
            self._prepare_upstream_conf(v, k)
            values.append(v)

        #cache
        if readonly:
            self.__class__._upstreams_cache = values
            self.__class__._conf_cache_mtime_and_inode = mtime_and_inode

        return values

    def get_nginx_listen_ports_on_eth_external(self, exclude_site_name=None):
        all_upstreams = self.get_all_upstreams()
        http_ports = set([])
        https_ports = set([])
        for site_conf in all_upstreams:
            if site_conf.get('key') == exclude_site_name:
                continue
            if site_conf.get('IsHttps'):
                https_ports.add(site_conf.get('ListenPort'))
                if site_conf.get('enable_http2https'):
                    http_ports.add(site_conf.get('http2https_org_port'))
            else:
                http_ports.add(site_conf.get('ListenPort'))

        # external port should not conflict with admin port
        http_ports |= {'20146'}
        https_ports |= {self.get_conf().get_value('nginx/web_console/port')}

        http_ports -= {None}
        https_ports -= {None}
        return http_ports, https_ports

    def get_business_type_mapping(self):
        business_type_mapping = self.get_conf().get_value('bta_service/web/business_type_mapping/')
        if not business_type_mapping:
            business_type_mapping = BUSINESS_TYPE_MAPPING
        return business_type_mapping

    def get_threat_type_settings(self):
        threat_type_settings = self.get_conf().get_value('bta_service/web/threat_config/')
        if not threat_type_settings:
            threat_type_settings = {
                '1': [0, [17, 0, 0]],     # 信用卡撞库
                '2': [0, [16, 0, 0]],     # 优惠券破解
                '3': [0, [14, 15, 0]],    # 广告欺诈
                '4': [0, [0, 0, 0]],      # 应用指纹探测
                '5': [0, [4, 0, 0]],      # 薅羊毛
                '6': [0, [0, 0, 0]],
                '7': [0, [1, 0, 0]],      # 暴力破解
                '8': [0, [1, 0, 0]],      # 撞库
                '9': [0, [12, 13, 0]],    # 验证码绕过
                '10': [0, [17, 0, 0]],    # 信用卡爆破
                '11': [0, [3, 0, 0]],     # 爬虫
                '12': [0, [0, 0, 0]],
                '13': [0, [10, 9, 0]],    # 秒杀
                '14': [0, [0, 0, 0]],     # 漏洞扫描
                '15': [0, [0, 0, 0]],     # 拒绝服务攻击
                '16': [0, [7, 0, 0]],     # 刷票
                '17': [0, [18, 0, 0]],    # 垃圾评论
                '18': [0, [0, 0, 0]],     # 应用架构探测
                '19': [0, [2, 0, 0]],     # 虚假注册
                '20': [0, [0, 0, 0]],
                '21': [0, [5, 6, 0]],     # 霸占库存
                '33': [0, [8, 0, 0]],     # 短信轰炸
                '34': [0, [11, 0, 0]],    # 接口滥用
                '48': [0, [0, 0, 0]],     # 异常手机终端
                '49': [0, [0, 0, 0]],     # SDK群控
                '50': [0, [0, 0, 0]]      # 未知威胁
            }
        else:
            if not threat_type_settings.has_key('1'):
                threat_type_settings['1'] = [0, [17, 0, 0]]
            if not threat_type_settings.has_key('2'):
                threat_type_settings['2'] = [0, [16, 0, 0]]
            if not threat_type_settings.has_key('3'):
                threat_type_settings['3'] = [0, [14, 15, 0]]
            if not threat_type_settings.has_key('7'):
                threat_type_settings['7'] = [0, [1, 0, 0]]
            if not threat_type_settings.has_key('9'):
                threat_type_settings['9'] = [0, [12, 13, 0]]
            if not threat_type_settings.has_key('10'):
                threat_type_settings['10'] = [0, [17, 0, 0]]
            if not threat_type_settings.has_key('13'):
                threat_type_settings['13'] = [0, [10, 9, 0]]
            if not threat_type_settings.has_key('17'):
                threat_type_settings['17'] = [0, [18, 0, 0]]
            if not threat_type_settings.has_key('19'):
                threat_type_settings['19'] = [0, [2, 0, 0]]
            if not threat_type_settings.has_key('21'):
                threat_type_settings['21'] = [0, [5, 6, 0]]
            if not threat_type_settings.has_key('33'):
                threat_type_settings['33'] = [0, [8, 0, 0]]
            if not threat_type_settings.has_key('34'):
                threat_type_settings['34'] = [0, [11, 0, 0]]
        return threat_type_settings

    def get_enable_mobile_bta(self):
        enabled = self.get_conf().get_value('bta_service/mobile/enable_bta/', False)
        return enabled

    def set_enable_mobile_bta(self, enabled):
        return self.set_asp_conf_values([['bta_service/mobile/enable_bta/', enabled]])

    def get_enable_bta(self):
        enabled_all = self.get_conf().get_value('nginx/enable_bta/', False)
        enabled_analysis = self.get_conf().get_value('nginx/enable_bta_analysis/', None)
        if enabled_all and enabled_analysis is None:
            enabled_analysis = True
        return enabled_all, enabled_analysis

    def set_threat_type_settings(self, data):
        code, output, cmdline = self.set_asp_conf_values([['bta_service/web/threat_config/', data]])
        if type(data) != dict:
            return code, output, cmdline
        bta_enable = False
        for key, val in data.iteritems():
            if val[0]:
                bta_enable = True
                break
        return self.set_asp_conf_values([['bta_service/web/enable_bta/', bta_enable]])

    def get_threat_threshold(self, model):
        model = int(model)
        if model < 1 or model > 64:
            self.logger.error('Invalid threat model id {}!'.format(model))
            return -1

        threat_threshold = self.get_conf().get_value('bta_service/web/threat_threshold/', None)
        if not threat_threshold:
            threshold = 50
        else:
            threshold = threat_threshold[model - 1]

        return threshold

    def restore_threat_threshold(self, model):
        thresholds = self.get_conf().get_value('bta_service/web/threat_threshold/', None)
        if thresholds is not None:
            thresholds[int(model)-1] = 50

            code, output, cmd_line = self.set_asp_conf_values([['bta_service/web/threat_threshold/', thresholds]])
            if code != 0:
                self.logger.error('Restore threat threshold failed!')
                return -1

        return 1

    def restore_threat_model(self, model):

        ret = self.global_zk_values('/bta_service/lua/web/threat_model/{}'.format(model), action='delete')
        if ret.get('result') != RESULT.OK:
            self.logger.error('Restore threat model failed!')
            return -1

        now = str(int(time.time()))
        self.set_asp_conf_values([['bta_service/web/model_version/', 'threat_model_{}_{}'.format(model, now)]])
        return 1

    def set_session_count(self, count):

        session_count = self.get_conf().get_value('bta_service/web/session_count/', 100)
        if session_count == int(count):
            self.logger.error('Bta session count {} is not changed!'.format(session_count))
            return True

        code, output, cmd_line = self.set_asp_conf_values([['bta_service/web/session_count/', int(count)]])
        if code != 0:
            return False

        return True

    def get_session_count(self):

        session_count = self.get_conf().get_value('bta_service/web/session_count/', 100)
        return session_count

    def set_session_timeout(self, timeout):

        session_timeout = self.get_conf().get_value('bta_service/web/session_timeout/', 5)
        if session_timeout == int(timeout):
            self.logger.error('Bta session timeout {} is not changed!'.format(session_timeout))
            return True

        code, output, cmd_line = self.set_asp_conf_values([['bta_service/web/session_timeout/', int(timeout)]])
        if code != 0:
            return False

        return True

    def get_session_timeout(self):

        session_timeout = self.get_conf().get_value('bta_service/web/session_timeout/', 5)
        return session_timeout

    def set_session_max_len(self, max_len):

        session_max_len = self.get_conf().get_value('bta_service/web/session_max_len/', 60)
        if session_max_len == int(max_len):
            self.logger.error('Bta session max len {} is not changed!'.format(session_max_len))
            return True

        code, output, cmd_line = self.set_asp_conf_values([['bta_service/web/session_max_len/', int(max_len)]])
        if code != 0:
            return False

        return True

    def get_session_max_len(self):

        session_max_len = self.get_conf().get_value('bta_service/web/session_max_len/', 60)
        return session_max_len

    def set_threat_threshold(self, model, data):
        model = int(model)
        if model < 1 or model > 64:
            self.logger.error('Invalid threat model id {}!'.format(model))
            return False

        threat_threshold = self.get_conf().get_value('bta_service/web/threat_threshold/', None)
        if not threat_threshold:
            threat_threshold = [50] * 64

        threshold = threat_threshold[model - 1]
        if threshold == int(data):
            self.logger.error('Bta threat {} threshold is not changed!'.format(model))
            return True

        threat_threshold[model - 1] = int(data)
        self.set_asp_conf_values([['bta_service/web/threat_threshold/', threat_threshold]])

        return True

    def set_threat_model(self, model, data):
        # get lua
        model_dict = AspConfCtrl().get_bta_model_dict(model)
        bta_lua = model_dict['lua_body']

        if data == bta_lua:
            self.logger.error('Bta threat model is not changed!')
            return 1

        # check lua
        from views_bta import verify_lua_syntax
        if not verify_lua_syntax(data):
            self.logger.error('Bta threat model lua syntax error!')
            return 0

        # set lua
        ret = self.global_zk_values({'/bta_service/lua/web/threat_model/{}'.format(model): data}, action='set')
        if ret.get('result') != RESULT.OK:
            self.logger.error('Set threat model failed!')
            return -1

        now = str(int(time.time()))
        self.set_asp_conf_values([['bta_service/web/model_version/', 'threat_model_{}_{}'.format(model, now)]])
        return 1

    def get_weak_password_list(self):
        weak_password_list = self.get_conf().get_value('nginx/weak_pwd_list/', None)
        return weak_password_list

    def get_upstream_by_server_key(self, key):
        upstream = self.get_conf().get_all('nginx/upstreams/').get(key)
        if upstream:
            self._prepare_upstream_conf(upstream, key)
            return upstream
        return None

    def get_all_upstreams_with_key_server(self):
        '''
        Return all upstream keys and server names.
        '''
        upstreams = self.get_all_upstreams(readonly=True)
        values = []
        for v in upstreams:
            port = v['ListenPort']
            server_name = v['ServerName']
            server_name_type = v['ServerNameType'],
            server_alias = v.get('site_customize_name', '')
            enable_business_path = v.get('enable_business_path',False)
            business_path = v.get('business_path', '')
            is_https = v['IsHttps']
            if "IPv6" in server_name_type:
                server_name = '[' + server_name + ']'
            obj = {
                    'server_alias': server_alias,
                    'key':v['key'],
                    'server_name': server_name,
                    'port': port,
                    'server_name_type': server_name_type,
                    'is_https': is_https,
                    'enable_business_path': enable_business_path,
                    'business_path': business_path
                }
            values.append(obj)

        return values

    def get_all_upstreams_for_show(self):
        '''
        Return all upstream site names.
        '''
        upstreams = self.get_conf().get_all('nginx/upstreams/')
        values = []
        for k, v in upstreams.items():
            self._prepare_upstream_conf(v, k)
            port = v['ListenPort']
            server_name = v['ServerName']
            server_name_type = v['ServerNameType']
            if server_name_type == "IPv6":
                server_name = '[' + server_name + ']'
            v['name'] = server_name + ':' + port
            values.append(v)

        return values

    def get_cookie_post_enabled(self):
        upstream_conf_list = self.get_all_upstreams_for_show()
        enable_cookie_collection = enable_post_data_collection = False
        for upstream in upstream_conf_list:
            enable_cookie_collection |= upstream.get('Enable_Cookie_Collection', False)
            enable_post_data_collection |= upstream.get('Enable_Post_Data_Collection', False)

        return enable_cookie_collection, enable_post_data_collection

    def get_upstream_conf(self, serverKey):
        d = self.get_conf().get_all('nginx/upstreams/' + serverKey)
        self._prepare_upstream_conf(d, serverKey)
        #handshake key
        d['mobile_customer_id'] = NginxConf().get_license_info().get_dict().get('mobile_handshakeKey', '')
        return d

    def show_upstream_conf(self, conf):
        #means lower config, including update and load config
        if conf.get(NginxConf.UK_Https) and conf.get(NginxConf.UK_UseInternationalCert, None) is None:
            conf[NginxConf.UK_UseInternationalCert] = True

        saved_conf = self.get_upstream_conf(conf.get('key', ''))
        crt = NginxConf.UK_CERTIF
        key = NginxConf.UK_CERTIF_KEY
        conf['cert_saved'] = False
        if crt in conf or crt in saved_conf:
            conf[crt] = ''
            conf['cert_saved'] = True
        if key in conf or key in saved_conf:
            conf[key] = ''
            conf['cert_saved'] = True

        gm_sign_cert = NginxConf.UK_GM_SIGN_CERTification
        gm_sign_key = NginxConf.UK_GM_SIGN_CERTificationKey
        gm_enc_cert = NginxConf.UK_GM_ENC_CERTification
        gm_enc_key = NginxConf.UK_GM_ENC_CERTificationKey
        if gm_sign_cert in conf or gm_sign_cert in saved_conf:
            conf[gm_sign_cert] = ''
            conf['cert_saved'] = True
        if gm_sign_key in conf or gm_sign_key in saved_conf:
            conf[gm_sign_key] = 'gm_sign_key'
            conf['cert_saved'] = True
        if gm_enc_cert in conf or gm_enc_cert in saved_conf:
            conf[gm_enc_cert] = ''
            conf['cert_saved'] = True
        if gm_enc_key in conf or gm_enc_key in saved_conf:
            conf[gm_enc_key] = ''
            conf['cert_saved'] = True

        if 'check_console_open_v2' not in conf:
            # 如果升级上来之后，没做任何配置变更，此时配置中没有check_console_open_v2配置项，继承老的开关配置值
            conf['check_console_open_v2'] = conf.get('check_console_open', True)

    def _prepare_upstream_conf(self, upstream, serverKey):
        upstream['key'] = serverKey
        ary = serverKey.split('_')
        upstream['name'] = ary[0]
        upstream['status'] = self.get_conf().get_value('_private/upstreams/{0}/status'.format(serverKey))
        test_output = self.get_conf().get_value('_private/upstreams/{0}/test_output'.format(serverKey))
        # 脱敏处理，避免暴露服务器路径
        strip_sensitive_msg = lambda s: s.replace('/etc/asp/release/nginx/', '')
        test_output = strip_sensitive_msg(test_output) if test_output else ''
        upstream['test_output'] = test_output
        if 'request_protection_list' not in upstream:
            upstream['request_protection_list'] = {
                "history_list": {},
                "current_index": '',
                'enable': False,
                'recommended': {}
            }

        if upstream.get(self.UK_ServerNameType) is None:
            upstream[self.UK_ServerNameType] = 'Domain'
        if upstream.get(self.UK_ServerName) is None:
            upstream[self.UK_ServerName] = ary[0]
            if valid_IPv6(ary[0]):
                upstream[self.UK_ServerNameType] = 'IPv6'
            elif valid_IPv4(ary[0]):
                upstream[self.UK_ServerNameType] = 'IPv4'
        if upstream.get(self.UK_SecurityLevelMin) is None:
            upstream[self.UK_SecurityLevelMin] = NginxConf.DEFAULT_SECURITY_FEATURES
        if upstream.get(self.UK_SecurityLevelMax) is None:
            upstream[self.UK_SecurityLevelMax] = NginxConf.DEFAULT_SECURITY_FEATURES
        if upstream.get(self.UK_IE_RENDERING_MODE) is None:
            upstream[self.UK_IE_RENDERING_MODE] = False

    def replace_special_character(self, reg_list):
        cur_reg_list = reg_list
        temp_list = cur_reg_list.split(';')
        for i in range(len(temp_list) - 1):
            if not temp_list[i].endswith('\\'):
                temp_list[i] = temp_list[i] + '\\'

        cur_reg_list = ';'.join(temp_list)
        return cur_reg_list

    def save_upstream(self, server_key, conf, extra_values=None):
        """
        Save upstream
        :param server_key: upstream_key
        :param conf: upstream_values
        :param extra_values: other values
        :type extra_values: dict
        :return:
        """

        ignored_properties = ['key', 'status']
        path = 'nginx/upstreams/' + server_key + '/'

        values = {}
        if extra_values is not None:
            values.update(**extra_values)

        for k, v in conf.items():
            if ((k == 'Certification' and not v and conf.get('cert_file_name'))
                    or (k == 'CertificationKey' and not v and conf.get('key_file_name'))
                    or (k == 'gm_sign_certification' and not v and conf.get('gm_sign_cert_file_name'))
                    or (k == 'gm_sign_certificationKey' and not v and conf.get('gm_sign_key_file_name'))
                    or (k == 'gm_enc_certification' and not v and conf.get('gm_enc_cert_file_name'))
                    or (k == 'gm_enc_certificationKey' and not v and conf.get('gm_enc_key_file_name'))):
                continue
            if k not in ignored_properties:
                values.update({path + k: v})

        # 同步更新小程序配置
        app_list = WechatConf().update_mpp_by_proxy(server_key, conf)
        if app_list:
            values.update({'nginx/wechat_app/app_list/': app_list})

        mpp_list = AlipayConf().update_mpp_by_proxy(server_key, conf)
        if mpp_list:
            values.update({'nginx/alipay_mpp/app_list/': mpp_list})

        mpp_list = MPaasConf().update_mpp_by_proxy(server_key, conf)
        if mpp_list:
            values.update({'nginx/mpaas_mpp/app_list/': mpp_list})

        ret = code, output, cmd_line = self.set_asp_conf_values(values, direct=True)
        self.logger.info('@@@@ {}'.format(values))
        if code == 0:
            self.logger.info('Success to save upstream configuration.')
        else:
            self.logger.error('Fail to save upstream configuration. {0} {1} {2}'.format(code, output, cmd_line))
        return ret

    def remove_upstream(self, server_key, extra_values=None):
        kv = {"nginx/upstreams/" + server_key: None}

        # 同步更新小程序配置
        app_list = WechatConf().update_mpp_by_proxy(server_key, None)
        if app_list:
            kv.update({'nginx/wechat_app/app_list/': app_list})

        mpp_list = AlipayConf().update_mpp_by_proxy(server_key, None)
        if mpp_list:
            kv.update({'nginx/alipay_mpp/app_list/': mpp_list})

        mpp_list = MPaasConf().update_mpp_by_proxy(server_key, None)
        if mpp_list:
            kv.update({'nginx/mpaas_mpp/app_list/': mpp_list})

        remove_dir(get_release_file('nginx/protection_list/{}'.format(server_key)))
        service_mgr_syncfile_remove('protection_list_{}.tgz'.format(server_key))

        if extra_values is not None:
            kv.update(**extra_values)

        return self.set_asp_conf_values(kv, direct=True)

    def set_enable_site_conf_switch(self, serverKey, status):
        kv = {'nginx/upstreams/{}/enable_site_conf'.format(serverKey): status}
        # 同步更新小程序配置
        app_list = WechatConf().update_mpp_config_version_by_force(serverKey)
        if app_list:
            kv.update({'nginx/wechat_app/app_list/': app_list})

        mpp_list = AlipayConf().update_mpp_config_version_by_force(serverKey)
        if mpp_list:
            kv.update({'nginx/alipay_mpp/app_list/': mpp_list})

        mpp_list = MPaasConf().update_mpp_config_version_by_force(serverKey)
        if mpp_list:
            kv.update({'nginx/mpaas_mpp/app_list/': mpp_list})
        return self.set_asp_conf_values(kv)

    def get_password_verify_warning(self):
        return (self.get_conf().get_value('attack_detection/analyser/password_verify_warning_count', 0),
                self.get_conf().get_value('attack_detection/analyser/password_verify_warning_on', False))

    def get_txsafe_debug_enabled(self):
        value = self.get_conf().get_value('nginx/develop_mode/txsafe_debug')
        if value is None:
            return False, 0
        if value == -1:
            return True, 0

        now = int(time.time())
        if value <= now < value + 7200:
            return True, value + 7200 - now
        else:
            return False, 0

    def set_txsafe_debug_enabled(self, enabled):
        if self.get_conf().get_value('_private/is_debug'):
            return self.set_asp_conf_values([('nginx/develop_mode/txsafe_debug', -1 if enabled else None)])
        else:
            return self.set_asp_conf_values([('nginx/develop_mode/txsafe_debug', int(time.time()) if enabled else None)])

    def set_subfilter_string(self, strings):
        return self.set_asp_conf_values([('nginx/subfilter/strings', strings)])

    def get_subfilter_string(self):
        return self.get_conf().get_value('nginx/subfilter/strings')

    def set_watermark(self, conf):
        encode_conf = []
        length = len(conf)

        if length > 0:
            for index in range(0, length):
                sub_cfg = conf[index]
                url = sub_cfg['url']
                sub_cfg.pop('url')
                sub_cfg_str = json.dumps(sub_cfg)
                encode_sub_cfg_no_url = base64.b64encode(sub_cfg_str)
                encode_conf.append({"url": url, "base64_cfg": encode_sub_cfg_no_url})
        return self.set_asp_conf_values([('nginx/watermark', encode_conf)])

    def get_watermark(self):
        encode_conf = self.get_conf().get_value('nginx/watermark', [])
        decode_conf = []
        length = len(encode_conf)

        if length > 0:
            for index in range(0, length):
                sub_cfg = encode_conf[index]
                decode_cfg_no_url = base64.b64decode(sub_cfg['base64_cfg'])
                conf = json.loads(decode_cfg_no_url)
                conf['url'] = sub_cfg['url']
                decode_conf.append(conf)
        return decode_conf

    def get_src_ip_strategy_list(self):
        src_ip_strategy_list = self.get_conf().get_value('nginx/src_ip_strategy_list')
        if src_ip_strategy_list is None:
            src_ip_from = self.get_conf().get_value('nginx/src_ip_from', '')
            xff_position = self.get_conf().get_value('nginx/xff_position', 'last')
            if src_ip_from == '':
                src_ip_from = '__SRCIP_TCPIP__'
            src_ip_from_type, src_ip_customed_name = (src_ip_from, '') if src_ip_from in ('__SRCIP_TCPIP__', 'X-Forwarded-For', 'X-Real-IP') else ('Custom', src_ip_from)
            src_ip_strategy_list = [{'src_ip_from_type': src_ip_from_type, 'src_ip_customed_name': src_ip_customed_name, 'xff_position': xff_position}]
        return src_ip_strategy_list[:10] if isinstance(src_ip_strategy_list, list) else src_ip_strategy_list

    def set_src_ip_strategy_list(self, src_ip_strategy_list, auto_disable=True):
        value = [('nginx/src_ip_strategy_list', src_ip_strategy_list)]
        if auto_disable:
            auto_disable = False
            for src_ip_strategy in src_ip_strategy_list:
                if src_ip_strategy.get("src_ip_from_type") in ('X-Forwarded-For', 'X-Real-IP'):
                    auto_disable = True
                    break

            if auto_disable:
                for upstream in self.get_all_upstreams():
                    server_name = upstream.get('key')
                    if server_name and upstream.get('src_ip_use_global_setting', True):
                        value.append(('nginx/upstreams/' + server_name + '/enable_X_Real_IP', False))
                        value.append(('nginx/upstreams/' + server_name + '/enable_X_Forwarded_For', False))

        return self.set_asp_conf_values(value)

    def set_site_names(self, values):
        return self.set_asp_conf_values([('nginx/upstreams/{}/site_customize_name'.format(k), v) for k,v in values], direct=True)

    def set_errpage_status(self, value):
        return self.set_asp_conf_values([('nginx/customized_error_pages', value)])

    def get_errpage_status(self):
        return self.get_conf().get_value('nginx/customized_error_pages')

    def get_upload_max_size(self):
        return self.get_conf().get_value('nginx/client_max_body_size', ConfDb.DEFAULT_CLIENT_MAX_BODY_SIZE)

    def set_upload_max_size(self, val):
        return self.set_asp_conf_values([('nginx/client_max_body_size', val)])

    def set_ip_black_switch(self, val):
        return self.set_asp_conf_values([('nginx/enable_ip_black', val)])

    def set_ip_white_switch(self, val):
        return self.set_asp_conf_values([('nginx/enable_ip_white', val)])

    def set_direct_conn_ctrl(self, enable, ip_list):
        return self.set_asp_conf_values([('nginx/direct_connection_control/enable', enable),
                                         ('nginx/direct_connection_control/ip_list', ip_list)])

    def get_direct_conn_ctrl_switch(self):
        return self.get_conf().get_value('nginx/direct_connection_control/enable')

    def get_direct_conn_ctrl_ip_list(self):
        return self.get_conf().get_value('nginx/direct_connection_control/ip_list', '[]')

    def set_keep_src_ip_switch(self, val):
        return self.set_asp_conf_values([('nginx/keep_src_ip', val)])

    def get_ip_black_switch(self):
        return self.get_conf().get_value('nginx/enable_ip_black')

    def get_ip_white_switch(self):
        return self.get_conf().get_value('nginx/enable_ip_white')

    def get_keep_src_ip_switch(self):
        value = self.get_conf().get_value('nginx/keep_src_ip')
        return value if value is not None else False

    def get_admin_listen_switch(self):
        value = self.get_conf().get_value('nginx/enable_admin_listen', False)
        return value if value is not None else True

    def set_admin_listen_switch(self, val):
        return self.set_asp_conf_values([('nginx/enable_admin_listen', val)])

    def get_update_error_page_switch(self):
        value = self.get_conf().get_value('nginx/update_error_page')
        return value if value is not None else True

    def set_alert_box_for_cookie_switch(self, val):
        return self.set_asp_conf_values([('nginx/alert_box_for_cookie/enable', val)])

    def get_alert_box_for_cookie_switch(self):
        value = self.get_conf().get_value('nginx/alert_box_for_cookie/enable')
        return False if value is None else value

    def set_strictly_match_host_switch(self, val):
        return self.set_asp_conf_values([('nginx/strictly_match_host/enable', val)])

    def get_strictly_match_host_switch(self):
        value = self.get_conf().get_value('nginx/strictly_match_host/enable')
        return False if value is None else value

    def set_slow_http_attack(self, val):
        return self.set_asp_conf_values([('nginx/slow_http_protection/enable', val)])

    def get_slow_http_attack(self):
        value = self.get_conf().get_value('nginx/slow_http_protection/enable')
        return False if value is None else value

    def set_gm_algorithm_switch(self, val):
        return self.set_asp_conf_values([('nginx/gm_algorithm/enable', val)])

    def get_gm_algorithm_switch(self):
        value = self.get_conf().get_value('nginx/gm_algorithm/enable')
        return False if value is None else value

    def set_bot_check(self, val):
        return self.set_asp_conf_values([('nginx/bot_check/', val)])

    def get_bot_check(self):
        value = self.get_conf().get_value('nginx/bot_check/')
        return False if value is None else value

    def get_sites_with_xrealip_xforwarded4_enabled(self):
        value = []
        upstreams = self.get_all_upstreams()
        for upstream in upstreams:
            name = upstream.get('ServerName')
            type = upstream.get('ServerNameType')
            if type == 'IPv6':
                name = '[' + name + ']'
            port = upstream.get('ListenPort')
            server_name = name + ':' + port
            if server_name and upstream.get('src_ip_use_global_setting', True):
                if upstream.get('enable_X_Real_IP') or upstream.get('enable_X_Forwarded_For'):
                    value.append(server_name)
        return value

    def get_ubb_sites_list(self):
        value = []
        upstreams = self.get_all_upstreams()
        for upstream in upstreams:
            if upstream.get('_deleted'):
                continue
            obj = {'ServerName': upstream.get('ServerName'), 'ServerNameType': upstream.get('ServerNameType'),
                   'ListenPort': upstream.get('ListenPort')}
            value.append(obj)
        return value

    def get_api_sites_list(self):
        value = []
        upstreams = self.get_all_upstreams()
        for upstream in upstreams:
            if upstream.get('_deleted'):
                continue
            obj = {'ServerName': upstream.get('ServerName'), 'ServerNameType': upstream.get('ServerNameType'),
                   'ListenPort': upstream.get('ListenPort')}
            value.append(obj)
        return value

    def get_lua_global_switch(self):
        return self.get_conf().get_value('nginx/lua_env/lua_global_switch', True)

    def set_lua_global_switch(self, enabled):
        return self.set_asp_conf_values([('nginx/lua_env/lua_global_switch', enabled)])

    def get_lua_manual_ubb_switch(self):
        # to be compatible with old versions, 'enabled' is used as lua_manual_ubb_switch
        # if 'enabled' in previous version exist, then its value remains
        # if 'enabled' in previous version does not exist, then it's False by default
        return self.get_conf().get_value('nginx/lua_env/enabled', False)

    def set_lua_manual_ubb_switch(self, enabled):
        return self.set_asp_conf_values([('nginx/lua_env/enabled', enabled)])

    def get_lua_share_memory_size(self):
        return self.get_conf().get_value('nginx/lua_env/share_dict', {})

    def set_lua_share_memory_size(self, val):
        if not val:
            val = {}
        return self.set_asp_conf_values([('nginx/lua_env/share_dict', val)])

    def get_lua_share_memory_info(self):
        return self.get_conf().get_value('nginx/lua_env/share_memory_info', {})

    def set_lua_share_memory_info(self, val):
        if not val:
            val = {}
        return self.set_asp_conf_values([('nginx/lua_env/share_memory_info', val)])

    def get_lua_env(self):
        return self.get_conf().get_all('nginx/lua_env')

    def set_lua_env(self, val, exist_val):
        prefix_path = 'nginx/lua_env/'
        value = add_prefix_to_dict(val, exist_val, prefix_path)
        if value:
            return self.set_asp_conf_values(value.items())

        return True

    def get_captcha_conf(self):
        return self.get_conf().get_all('nginx/captcha')

    def set_captcha_conf(self, val, exist_val):
        prefix_path = 'nginx/captcha/'
        value = add_prefix_to_dict(val, exist_val, prefix_path)
        if value:
            return self.set_asp_conf_values(value.items())

        return True

    def set_lua_ubb_rule_timestamp(self, path, timestamp):
        return self.set_asp_conf_values([(path, timestamp)])

    def get_captcha_enable_switch(self):
        return self.get_conf().get_value('nginx/captcha/enabled', False)

    def set_captcha_enable_switch(self, enabled):
        return self.set_asp_conf_values([('nginx/captcha/enabled', enabled)])

    def get_captcha_policy(self):
        result = self.get_conf().get_value('nginx/captcha/policy', None)
        if result is None:
            result = {
                    'threshold': 70,
                    'mandatory': False,
                    'percent': 100,
                    'delay': 0,
                    'action': "block_code",
                    'action_value': 403
                    }
            return result

        # convert list to str
        action_value = result.get('action_value')
        if type(action_value) is list:
            action_value = ','.join([str(i) for i in action_value])
            result['action_value'] = action_value

        if result.get('action') == "captcha":
            result['action'] = "challenge"

        return result
    def set_captcha_policy(self, val):
        if not val:
            return 0, "", ""

        try:
            action_value = val.get('action_value')
            if val.get('action') == "block_code":
                val['action_value'] = int(action_value)
            elif val.get('action') == "challenge":
                val['action'] = "captcha"
                if type(action_value) is str or type(action_value) is unicode:
                    action_value = [i.strip() for i in action_value.split(',')]
                    action_value = [int(i) for i in action_value if len(i)>0]

                    # check validation of template list
                    templatePath = get_release_file("nginx/lua/captcha/templates")
                    templateList = [d for d in os.listdir(templatePath) if os.path.isdir(os.path.join(templatePath, d))]
                    templateList = [int(d) for d in templateList if d.isdigit()]
                    templateList = [t for t in action_value if t not in templateList]
                    if len(templateList) > 0:
                        raise Exception("Non exist template")

                    val['action_value'] = action_value
            elif val.get('action') == "pass":
                val['action_value'] = True
            elif val.get('action') == "transpare":
                val['action_value'] = True
        except Exception as e:
            self.logger.error(e)
            return -1, "", "Invalid Input"

        return self.set_asp_conf_values([('nginx/captcha/policy', val)])
    def set_captcha_css(self, content):
        old = self.get_captcha_css()
        if old == content:
            self.logger.info("css not changed!")
            return 0, "", ""
        return self.set_asp_conf_values([('nginx/captcha/css', content)])
    def get_captcha_css(self):
        data = self.get_conf().get_value('nginx/captcha/css', '')
        if not data:
            # read from custom/default css file
            fn = get_release_file("nginx/lua/captcha/custom.css")
            if not os.path.isfile(fn):
                fn = get_release_file("nginx/lua/captcha/templates/base.css")

            try:
                with open(fn, "r") as f:
                    data = f.read()
            except Exception as e:
                self.logger.error(e)

        return data

    def set_captcha_template(self, path):
        return self.set_asp_conf_values([('nginx/captcha/template', path)])

    def enable_token_rename(self, enabled, seed='cluster_key'):

        token_rename = generate_token_rename(enabled, seed)
        value = [('nginx/token_rename', token_rename)]
        code, status, msg = self.set_asp_conf_values(value)
        if code == 0:
            return self._filter_token_rename_fields(token_rename['fields'])
        return {}

    def get_token_rename(self):
        default_token_fields = self.get_conf().get_default_token_fields()
        default_fields = {k: v['default'] for k, v in default_token_fields.items()}
        value = self.get_conf().get_value('nginx/token_rename', default={
            'enable': False,
            'fields': default_fields,
            'seed': 'none',
            'used_cookie_token': [],
        })

        value['fields'] = self._filter_token_rename_fields(value['fields'])
        return value

    def _filter_token_rename_fields(self, fields):
        if self.get_conf().get_value('_private/web_console/developer_mode/enable', -1) != -1:
            return fields
        # release only show cookie_token fields
        avaliable_field_names = {'cookie_token'}
        return {k: v for k, v in fields.items() if k in avaliable_field_names}
