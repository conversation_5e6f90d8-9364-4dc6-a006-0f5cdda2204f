# encoding=utf-8
import random
import string
import time
import logging
import re
import os
import json
import copy
import _strptime # workaround for https://bugs.python.org/issue7980
from datetime import datetime
from Conf_Base import BaseConf
from module_ctrls.waf_rule_ctrl import WafStrategyConf,WafRuleSetUpgrade,WafTamperProtection, WAFGrayBox
import asp_utils.waf_util as waf_util
from django.utils.translation import ugettext as _,ugettext_noop
from asp_utils.utils import exe_with_output,get_version,get_product_type
from asp_conf_ctrl import ConfDb
from web_admin.operation_log import operation_log
from asp_utils.license_info import LicenseInfo,get_current_is_debug
from asp_utils.CommonLibs import valid_IP_or_CIDR, valid_IPv6, valid_IPv4
from web_admin.Conf_Webconsole import WebconsoleConf
from web_admin.site_acl_util import get_sites_4_user
import gettext

try:
    from pcre import compile as pcre_compile
except:
    from re import compile as pcre_compile

def check_pcre_regex_valid(regular):
    try:
        pcre_compile(regular)
        return True
    except:
        return False
class WafConf(BaseConf):

    MAX_LIST_LENGTH = 100
    DEFAULT_ENABLE_TAMPER = False
    DEFAULT_TAMPER_DISKSPACE = '60'
    DEFAULT_TAMPER_AUTO_UPDATE = False
    DEFAULT_TAMPER_WEB_SIMILARITY= '70' 
    DEFAULT_TAMPER_EXPIRE_TIME = '60' 
    DEFAULT_TAMPER_CACHE_URLS = []
    DEFAULT_GLOBLE_CACHE_SPACE = 1024 # 1G = 1024M

    BRUTE_FORCE_STRATEGY_CONFIG_PATH = 'nginx/waf/brute_force_strategies'
    BRUTE_FORCE_STRATEGY_USED_LIST_CONFIG_PATH = 'nginx/waf/brute_force_strategy_used_list'
    BRUTE_FORCE_STRATEGY_MIN_ID =  620000
    MAX_WEAK_PASSWORD_CHARS = 20480
    WAF_DETECTION_SIZE_PATH = 'nginx/waf/waf_setting/detection_size'
    DEFAULT_DETECTION_SIZE = {
        "body_size_limit": 4,
        "file_size_limit": 10,
        "resp_body_size_limit": 512,
    }


    def __init__(self):
        BaseConf.__init__(self, 'nginx/waf', 'conf_waf')
        self._waf_strategy_conf = WafStrategyConf(self.get_conf())
    # 下方代码未使用

    def set_waf_site_strategy(self, val):
        return self.set_asp_conf_values([('nginx/waf/site_strategies', val)])

    def set_waf_module_strategy(self, val):
        return self.set_asp_conf_values([('nginx/waf/module_strategies', val)])

    def set_waf_syntax_strategy(self, val):
        return self.set_asp_conf_values([('nginx/waf/syntax_strategies', val)])

    def set_waf_res_leech_strategy(self,val):
        return self.set_asp_conf_values([('nginx/waf/res_leech_strategies',val)])

    def set_waf_brute_force_strategy(self,val):
        return self.set_asp_conf_values([(self.BRUTE_FORCE_STRATEGY_CONFIG_PATH,val)])

    def set_waf_brute_force_strategy_used_list(self, val):
        return self.set_asp_conf_values([(self.BRUTE_FORCE_STRATEGY_USED_LIST_CONFIG_PATH, val)])

    def set_waf_module_strategy_used_list(self, val):
        return self.set_asp_conf_values([('nginx/waf/module_strategy_used_list', val)])

    def set_waf_syntax_strategy_used_list(self, val):
        return self.set_asp_conf_values([('nginx/waf/syntax_strategy_used_list', val)])

    def set_waf_res_leech_strategy_used_list(self, val):
        return self.set_asp_conf_values([('nginx/waf/res_leech_strategy_used_list', val)])

    def set_waf_site_strategy_used_list(self, val):
        return self.set_asp_conf_values([('nginx/waf/site_strategy_used_list', val)])

    def set_waf_strategy_changed(self, val):
        return self.set_asp_conf_values([('nginx/waf/strategy_changed', val)])

    # 上方代码未使用
    def set_waf_id_whitelist(self, val):
        return self.set_asp_conf_values([('nginx/waf/waf_id_whitelist', val)])

    def set_weak_password_list(self,val):
        return self.set_asp_conf_values([('nginx/waf/weak_password_list', val)])

    def get_waf_strategy_module_strategies(self):
        return self.get_conf().get_value('nginx/waf/module_strategies', {})

    def get_waf_syntax_strategies(self):
        return self.get_conf().get_value('nginx/waf/syntax_strategies', {})

    def get_waf_res_leech_strategies(self):
        return self.get_conf().get_value('nginx/waf/res_leech_strategies', [])

    def get_waf_brute_force_strategies(self):
        return self.get_conf().get_value(self.BRUTE_FORCE_STRATEGY_CONFIG_PATH,[])

    def get_waf_site_strategies(self):
        return self.get_conf().get_value('nginx/waf/site_strategies', [])

    def get_waf_module_strategy_used_list(self):
        return self.get_conf().get_value('nginx/waf/module_strategy_used_list', {})

    def get_waf_syntax_strategy_used_list(self):
        return self.get_conf().get_value('nginx/waf/syntax_strategy_used_list', {})

    def get_waf_res_leech_strategy_used_list(self):
        return self.get_conf().get_value('nginx/waf/res_leech_strategy_used_list', {})

    def get_waf_brute_force_strategy_used_list(self):
        return self.get_conf().get_value(self.BRUTE_FORCE_STRATEGY_USED_LIST_CONFIG_PATH, {})

    def get_waf_site_strategy_used_list(self):
        return self.get_conf().get_value('nginx/waf/site_strategy_used_list', {})

    def get_waf_id_whitelist(self):
        whitelist = self.get_conf().get_value('nginx/waf/waf_id_whitelist', [])

        # upgrade from 23.03 to 23.05
        if whitelist and not isinstance(whitelist, list):
            whitelist = [{"id_whitelist": whitelist, "ip_whitelist": "", "comment": ""}]
        elif len(whitelist) == 0:
            whitelist = []

        # upgrade from 24.01 to 24.03
        for item in whitelist:
            item['host'] = item.get('host', '')
            item['url'] = item.get('url', '')
            # upgrade from 24.09 to 24.11
            item['method'] = item.get('method','')

        return whitelist

    def get_weak_password_list(self):
        weak_password_list = self.get_conf().get_value('nginx/waf/weak_password_list', None)
        return weak_password_list

    def update_waf_site_strategy(self, site_strategy_id, server_name, action='save'):
        try:
            site_strategy_used_list = self.get_conf().get_value('nginx/waf/site_strategy_used_list', {})

            # When the configuration is saved without any modification, exit directly
            if action == "save":
                hosts = site_strategy_used_list.get(str(site_strategy_id), [])
                if server_name in hosts:
                    return

            for _, hosts in site_strategy_used_list.items():
                if server_name in hosts:
                    hosts.remove(server_name)
                    break

            if action == "save":
                hosts = site_strategy_used_list.get(str(site_strategy_id), [])
                hosts.append(server_name)
                site_strategy_used_list[str(site_strategy_id)] = hosts

            return {
                "nginx/waf/site_strategy_used_list": site_strategy_used_list
            }
        except Exception as ex:
            self.logger.error('update_waf_site_strategy:{}'.format(ex))

    def update_syntax_strategy_used_list(self, site_strategy, action='save'):
        strategy_id = site_strategy.get("id")
        all_syntax_strategy_used_list = self.get_waf_syntax_strategy_used_list()
        # all_syntax_strategy_used_list = confDb.get_value('nginx/waf/syntax_strategy_used_list', {})
        syntax_strategies = site_strategy.get('syntax_strategies', {})
        is_update = False
        for module, info in syntax_strategies.items():
            module_strategy_id = str(info.get('strategy'))
            syntax_strategy_used_list = all_syntax_strategy_used_list.get(module, {}).get(module_strategy_id, [])
            if action == 'delete':
                if strategy_id in syntax_strategy_used_list:
                    is_update = True
                    syntax_strategy_used_list.remove(strategy_id)
                    all_syntax_strategy_used_list[module][module_strategy_id] = syntax_strategy_used_list

            else:
                if strategy_id not in syntax_strategy_used_list:
                    for id, used_list in all_syntax_strategy_used_list.get(module,{}).items():  # delete strategy_id from othe all_module_strategy
                        if strategy_id in used_list:
                            used_list.remove(strategy_id)
                            all_syntax_strategy_used_list[module][id] = used_list
                            break

                    syntax_strategy_used_list.append(strategy_id)  # add strategy_id
                    if module not in all_syntax_strategy_used_list.keys():
                        all_syntax_strategy_used_list[module] = {}
                    all_syntax_strategy_used_list[module][module_strategy_id] = syntax_strategy_used_list
                    is_update = True

        return all_syntax_strategy_used_list


    def update_module_strategy_used_list(self, site_strategy, action='save'):
        strategy_id = site_strategy.get("id")
        all_module_strategy_used_list = self.get_waf_module_strategy_used_list()
        # all_module_strategy_used_list = confDb.get_value('nginx/waf/module_strategy_used_list', {})
        module_strategies = site_strategy.get('module_strategies', {})

        is_update = False
        for module, info in module_strategies.items():
            module_strategy_id = str(info.get('strategy'))
            module_strategy_used_list = all_module_strategy_used_list.get(module, {}).get(module_strategy_id, [])
            if action == 'delete':
                if strategy_id in module_strategy_used_list:
                    is_update = True
                    module_strategy_used_list.remove(strategy_id)
                    all_module_strategy_used_list[module][module_strategy_id] = module_strategy_used_list

            else:
                if strategy_id not in module_strategy_used_list:
                    for id, used_list in all_module_strategy_used_list.get(module,
                                                                           {}).items():  # delete strategy_id from othe all_module_strategy
                        if strategy_id in used_list:
                            used_list.remove(strategy_id)
                            all_module_strategy_used_list[module][id] = used_list
                            break

                    module_strategy_used_list.append(strategy_id)  # add strategy_id
                    if module not in all_module_strategy_used_list.keys():
                        all_module_strategy_used_list[module] = {}
                    all_module_strategy_used_list[module][module_strategy_id] = module_strategy_used_list
                    is_update = True


        return all_module_strategy_used_list

    def update_res_leech_strategy_used_list(self, res_leech_strategy_id, server_name, action='save'):
            # 此函数未使用
            try:
                res_leech_strategy_used_list = self.get_conf().get_value('nginx/waf/res_leech_strategy_used_list', {})

                #When the configuration is saved without any modification, exit directly
                if action == "save":
                    hosts = res_leech_strategy_used_list.get(str(res_leech_strategy_id), [])
                    if server_name in hosts:
                        return

                for _, hosts in res_leech_strategy_used_list.items():
                    if server_name in hosts:
                        hosts.remove(server_name)
                        break

                if action == "save":
                    hosts = res_leech_strategy_used_list.get(str(res_leech_strategy_id), [])
                    hosts.append(server_name)
                    res_leech_strategy_used_list[str(res_leech_strategy_id)] = hosts

                self.set_waf_res_leech_strategy_used_list(res_leech_strategy_used_list)
            except Exception as ex:
                self.logger.error('update_waf_site_strategy:{}'.format(ex))

    def update_brute_force_strategy_used_list(self, strategy_id, server_name, action='save'):
            # 此函数未使用
            try:
                strategy_used_list = self.get_waf_brute_force_strategy_used_list()

                # When the configuration is saved without any modification, exit directly
                if action == "save":
                    hosts = strategy_used_list.get(str(strategy_id), [])
                    if server_name in hosts:
                        return

                for _, hosts in strategy_used_list.items():
                    if server_name in hosts:
                        hosts.remove(server_name)
                        break

                if action == "save":
                    hosts = strategy_used_list.get(str(strategy_id), [])
                    hosts.append(server_name)
                    strategy_used_list[str(strategy_id)] = hosts

                self.set_waf_brute_force_strategy_used_list(strategy_used_list)
            except Exception as ex:
                self.logger.error('update_brute_force_strategy_used_list:{}'.format(ex))


    def check_server_names_exist(self,server_names):
        existed_server_names = self.get_conf().get_all('nginx/upstreams/').keys()
        for server_name in server_names:
            if server_name == "default.com:*":
                continue
            if server_name not in existed_server_names:
                return False
        return True

    def update_strategy_used_lists(self, strategy_id, server_names,request):
        try:
            result = {
                'is_success': True,
                'id': strategy_id,
            }
            action = 'modify'
            if not self.check_server_names_exist(server_names):
                raise ServerNameUnExistsError(_('There are websites that do not exist in the configuration. Please refresh and try again.'))

            if self.valid_waf_site_strategy_id_exist(str(strategy_id)):
                raise StrategyIDUnExistsError(_('There are strategy that do not exist in the configuration. Please refresh and try again.'))

            strategy_used_list = self.get_waf_site_strategy_used_list()

            default_strategy="1"
            if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':
                default_strategy = "9"

            hosts = strategy_used_list.get(str(strategy_id), [])
            new_server_names=[x for x in server_names if x not in hosts]

            to_remove_names_tmp = []
            other_user_sites = []
            for x in hosts:
                if x not in server_names:
                    current_user = request.user.username
                    current_role = WebconsoleConf().get_user_role(current_user)
                    site_user = WebconsoleConf().get_user_of_site(x)
                    if current_user in site_user or current_role != 'Operator':
                        to_remove_names_tmp.append(x)
                    else:
                        other_user_sites.append(x)

            existed_server_names = self.get_conf().get_all('nginx/upstreams/').keys()
            to_remove_names=[x for x in to_remove_names_tmp if x in existed_server_names]

            #特殊逻辑，处理镜像下全流量采集时默认站点策略
            is_full_flow_collection_enabled = self.get_conf().get_value('nginx/httpcap_all_ports', False)
            if is_full_flow_collection_enabled:
                if "default.com:*" in to_remove_names_tmp:
                    to_remove_names.append("default.com:*")
            else:
                if "default.com:*" in to_remove_names_tmp:
                    server_names.append("default.com:*")


            if not new_server_names and not to_remove_names:
                # no change
                return result

            # update site conf
            final_apply_values={}
            for new_server_name in new_server_names:
                if new_server_name != "default.com:*":
                    path = 'nginx/upstreams/' + new_server_name + '/'+'waf_strategy'
                    final_apply_values[path]=str(strategy_id)
                for key, hosts in strategy_used_list.items():
                    if new_server_name in hosts:
                        hosts.remove(new_server_name)

            for remove_name in to_remove_names:
                if remove_name == "default.com:*":
                    continue
                path = 'nginx/upstreams/' + remove_name + '/'+'waf_strategy'
                final_apply_values[path]=str(default_strategy)

            #update used_list
            if default_strategy in strategy_used_list:
                strategy_used_list[default_strategy].extend(to_remove_names)
            else:
                strategy_used_list[default_strategy]=to_remove_names

            if str(strategy_id) == default_strategy:
                server_names.extend(to_remove_names)

            strategy_used_list[str(strategy_id)] = server_names + other_user_sites
            final_apply_values.update({'nginx/waf/site_strategy_used_list':strategy_used_list})

            self.set_asp_conf_values(final_apply_values)
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                             ugettext_noop('waf {0}{1} strategy Application Site'), '', [_("Site Strategy"),strategy_id])
        except (StrategyIDUnExistsError,ServerNameUnExistsError) as e:
            self.logger.error('update_waf_site_strategy_used_list:{}'.format(e))
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0}{1} strategy Application Site'), '', [_("Site Strategy"),strategy_id])
            return result
        except Exception as ex:
            self.logger.error('update_waf_site_strategy_used_list:{}'.format(ex))
            result = {
                'is_success': False,
                'error_msg': _("Strategy apply failed.")
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0}{1} strategy Application Site'), '', [_("Site Strategy"),strategy_id])
        return result


    def valid_res_leech_strategy_id_exist(self,strategy_id):
        '''

        @param strategy_id: type:string
        @return:
        '''
        err = None
        if strategy_id=='-1':
            return None

        elif type(strategy_id) != str and type(strategy_id) != unicode or not re.match('^\d+$', strategy_id):
            err = _('res_leech_strategy: {} is not digit').format(strategy_id)
        else:
            strategy_id = int(strategy_id)
            res_leech_strategies = self.get_waf_res_leech_strategies()
            for strategy in res_leech_strategies:
                if strategy.get('id','-1')==strategy_id:
                    return None
            err = _('res_leech_strategy: {} is not exist in waf res_leech strategy').format(int(strategy_id))

        return err

    def valid_brute_force_strategy_id_exist(self,strategy_id):
        '''

        @param strategy_id: type:string
        @return:
        '''
        err = None
        if strategy_id=='-1':
            return None

        elif type(strategy_id) != str and type(strategy_id) != unicode or not re.match('^\d+$', strategy_id):
            err = _('brute_force_strategy id: {} is not digit').format(strategy_id)
        else:
            strategy_id = int(strategy_id)
            strategies = self.get_waf_brute_force_strategies()
            for strategy in strategies:
                if strategy.get('id','-1')==strategy_id:
                    return None
            err = _('brute_force_strategy id: {} is not exist in waf brute_force_strategies').format(int(strategy_id))

        return err

    def valid_waf_site_strategy_id_exist(self,strategy_id):
        '''

        @param strategy_id: type:string
        @return:
        '''
        err = None
        if not strategy_id:
            err = _('waf_strategy is null')

        elif type(strategy_id) != str and type(strategy_id) != unicode or not re.match('^\d+$', strategy_id):
            err = _('waf_strategy: {} is not digit').format(strategy_id)
        else:
            strategy_id = int(strategy_id)
            site_strategy = self._waf_strategy_conf.get_waf_site_strategy(strategy_id)
            if not site_strategy:
                err = _('waf_strategy: {} is not exist in waf site strategy').format(int(strategy_id))

        return err

    def get_waf_module_title(self, waf_module):
        default_module_strategies = waf_util.get_waf_default_module_strategies()
        module_strategy = default_module_strategies.get(waf_module, {})
        return module_strategy.get("title","")

    def get_waf_syntax_title(self, waf_module):
        default_syntax_strategies = waf_util.get_waf_default_syntax_strategies()
        module_strategy = default_syntax_strategies.get(waf_module, {})
        return module_strategy.get("title","")

    def valid_waf_module_strategy_id_exist(self,module_type,strategy_id):
        '''

        @param strategy_id: type:string
        @return:
        '''
        err = None
        if not strategy_id:
            err = _('waf_strategy is null')

        elif type(strategy_id) != str and type(strategy_id) != unicode or not re.match('^\d+$', strategy_id):
            err = _('waf_strategy: {} is not digit').format(strategy_id)
        else:
            strategy_id = int(strategy_id)

            site_strategy = self._waf_strategy_conf.get_waf_module_strategy(module_type,strategy_id)
            if not site_strategy:
                err = _('select {} strategy does not exist,please reselect.').format(self.get_waf_module_title(module_type))

        return err

    def valid_waf_syntax_strategy_id_exist(self,module_type,strategy_id):
        '''

        @param strategy_id: type:string
        @return:
        '''
        err = None
        if not strategy_id:
            err = _('waf_strategy is null')

        elif type(strategy_id) != str and type(strategy_id) != unicode or not re.match('^\d+$', strategy_id):
            err = _('waf_strategy: {} is not digit').format(strategy_id)
        else:
            strategy_id = int(strategy_id)

            site_strategy = self._waf_strategy_conf.get_waf_syntax_strategy(module_type,strategy_id)
            if not site_strategy:
                err = _('select {} strategy does not exist,please reselect.').format(self.get_waf_syntax_title(module_type))

        return err

    def export_waf_site_strategy_info(self, site_strategy_id):
        waf_site_strategy_info = {}
        site_strategy = self._waf_strategy_conf.get_waf_site_strategy(int(site_strategy_id))
        if not site_strategy: # site_strategy_id is not exist in custom site strategy
            return waf_site_strategy_info

        module_strategies = site_strategy.get('module_strategies')
        for module, info in module_strategies.items():
            strategy_id = int(info.get('strategy'))
            if strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:  # custom module strategy
                strategy = self._waf_strategy_conf.get_waf_module_strategy(module, strategy_id)
                template_id = strategy.get('template')
                waf_change_rules = strategy.get('rules')
                name = strategy.get('name')
                comment = strategy.get('comment')
                special_config = strategy.get('special_config',{})
                waf_site_strategy_info[module] = {'enabled': info.get('enabled'), 'template': template_id,
                                                  'waf_change_rules': waf_change_rules,
                                                  'comment': comment,
                                                  'special_config': special_config}
            else:  # default module stragey
                waf_site_strategy_info[module] = {'enabled': info.get('enabled'),
                                                  'default_strategy_id': str(strategy_id)}

        return waf_site_strategy_info

    def export_waf_site_syntax_info(self, site_strategy_id):
        waf_syntax_info = {}
        site_strategy = self._waf_strategy_conf.get_waf_site_strategy(int(site_strategy_id))
        if not site_strategy: # site_strategy_id is not exist in custom site strategy
            return waf_syntax_info

        syntax_strategies = site_strategy.get('syntax_strategies', {})
        for module, info in syntax_strategies.items():
            strategy_id = int(info.get('strategy'))
            if strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:  # custom module strategy
                strategy = self._waf_strategy_conf.get_waf_syntax_strategy(module, strategy_id)
                template_id = strategy.get('template')
                waf_change_rules = strategy.get('rules')
                name = strategy.get('name')
                comment = strategy.get('comment')
                special_config = strategy.get('special_config',{})
                waf_syntax_info[module] = {'enabled': info.get('enabled'), 'template': template_id,
                                                  'waf_change_rules': waf_change_rules,
                                                  'comment': comment,
                                                  'special_config': special_config}
            else:  # default module stragey
                waf_syntax_info[module] = {'enabled': info.get('enabled'),
                                                  'default_strategy_id': str(strategy_id)}

        return waf_syntax_info

    def export_waf_res_leech_info(self, waf_res_leech_id):
        waf_res_leech_id = int(waf_res_leech_id)
        if waf_res_leech_id <=waf_util.CUSTOM_STRATEGY_MIN_ID:
            return {}
        site_res_leech_strategies=self.get_waf_res_leech_strategies()
        for res_leech_strategy in site_res_leech_strategies:
            strategy_id = int(res_leech_strategy.get('id'))
            if strategy_id == waf_res_leech_id:
                del res_leech_strategy['id']
                return res_leech_strategy
        return {}

    def compare_res_leech_strategy_info(self, waf_res_leech_strategy, waf_res_leech_info):
        '''
        get waf strategy info from confdb according to the waf_strategy_id,then compare waf strategy info of protected site.
        :param waf_res_leech_strategy: waf res_leech strategy id of protected site
        :param waf_res_leech_info: waf res_leech strategy info of protected site
        :return: return True if equal  else return False
        '''
        exist_waf_res_leech_strategy_info = self.export_waf_res_leech_info(waf_res_leech_strategy)
        if not exist_waf_res_leech_strategy_info:
            return False

        is_equal = True
        #"comment" and "name" changed will not create new strategy
        com_keys = ["res_suffix", "site_whitelist", "url_whitelist"]

        for com_key in com_keys:
            if waf_res_leech_info.get(com_key) != exist_waf_res_leech_strategy_info.get(com_key):
                is_equal = False
                self.logger.info("Conf waf res_leech compare false: key:{} info {}:{}".
                                 format(com_key, waf_res_leech_info.get(com_key), exist_waf_res_leech_strategy_info.get(com_key)))
                break

        return is_equal

    def export_waf_brute_force_info(self, strategy_id):
        strategy_id = int(strategy_id)
        strategies=self.get_waf_brute_force_strategies()
        for strategy in strategies:
            if int(strategy.get('id')) == strategy_id:
                del strategy['id']
                return strategy
        return {}

    def compare_waf_brute_force_info(self, waf_brute_force_strategy, waf_brute_force_info):
        '''
        get waf strategy info from confdb according to the waf_strategy_id,then compare waf strategy info of protected site.
        :param waf_brute_force_strategy: waf brute_force strategy id of protected site
        :param waf_brute_force_info: waf brute_force strategy info of protected site
        :return: return True if equal  else return False
        '''
        exist_waf_brute_force_strategy_info = self.export_waf_brute_force_info(waf_brute_force_strategy)
        if not exist_waf_brute_force_strategy_info:
            return False

        is_equal = True
        #"comment" and "name" changed will not create new strategy
        com_keys = ["path_limit", "action", "block_value","delay","auto_black_ip","redirect_value"]

        for com_key in com_keys:
            if waf_brute_force_info.get(com_key) != exist_waf_brute_force_strategy_info.get(com_key):
                is_equal = False
                self.logger.info("Conf waf_brute_force compare false: key:{} info {}:{}".
                                 format(com_key, waf_brute_force_info.get(com_key), exist_waf_brute_force_strategy_info.get(com_key)))
                break

        return is_equal

    def compare_site_strategy_info(self, waf_strategy_id, waf_strategy_info):
        '''
        get waf strategy info from confdb according to the waf_strategy_id,then compare waf strategy info of protected site.
        :param waf_strategy_id: waf strategy id of protected site
        :param waf_strategy_info: waf strategy info of protected site
        :return: return True if equal  else return False
        '''
        exist_waf_site_strategy_info = self.export_waf_site_strategy_info(waf_strategy_id)

        if not exist_waf_site_strategy_info:
            return False

        is_equal = True
        com_keys = ["enabled", "template", "waf_change_rules", "special_config", "default_strategy_id"]

        for module, module_info in waf_strategy_info.items():
            exist_module_info = exist_waf_site_strategy_info.get(module)

            if not exist_module_info:
                return False

            for com_key in com_keys:
                if module_info.get(com_key) != exist_module_info.get(com_key):
                    is_equal = False
                    self.logger.info("Conf waf compare false: module:{} key:{} info {}:{}".
                                     format(module,com_key, module_info.get(com_key), exist_module_info.get(com_key)))
                    break

        return is_equal

    def compare_site_syntax_info(self, waf_strategy_id, waf_syntax_info):
        '''
        get waf strategy info from confdb according to the waf_strategy_id,then compare waf strategy info of protected site.
        :param waf_strategy_id: waf strategy id of protected site
        :param waf_syntax_info: waf strategy info of protected site
        :return: return True if equal  else return False
        '''
        exist_waf_site_strategy_info = self.export_waf_site_syntax_info(waf_strategy_id)

        if not exist_waf_site_strategy_info:
            return False

        is_equal = True
        com_keys = ["enabled", "template", "waf_change_rules", "special_config", "default_strategy_id"]

        for module, module_info in waf_syntax_info.items():
            exist_module_info = exist_waf_site_strategy_info.get(module)
            if not exist_module_info:
                return False

            for com_key in com_keys:
                if module_info.get(com_key) != exist_module_info.get(com_key):
                    is_equal = False
                    self.logger.info("Conf waf compare false: module:{} key:{} info {}:{}".
                                     format(module,com_key, module_info.get(com_key), exist_module_info.get(com_key)))
                    break

        return is_equal

    def set_waf_update_config(self, val):
        return self.set_asp_conf_values([('nginx/waf/update_config', val)])

    def get_waf_default_update_config(self):
        '''

        @return: default update config
        '''
        update_config = {}
        update_config["update_interval"] = "1"
        update_config["update_time"] = "00:00"
        update_config["update_type"] = "0"
        update_config["proxy_protocol"] = "HTTP"
        update_config["proxy_ip"] = ""
        update_config["proxy_port"] = ""
        update_config["proxy_enabled"] = False
        return update_config

    def get_waf_update_config(self):
        update_config = self.get_conf().get_value('nginx/waf/update_config', {})
        if not update_config:
            update_config = self.get_waf_default_update_config()
        UPDATE_SERVER = WafRuleSetUpgrade.UPDATE_SERVER.split('//')[1] if '//' in WafRuleSetUpgrade.UPDATE_SERVER\
            else WafRuleSetUpgrade.UPDATE_SERVER
        update_config['update_server'] = UPDATE_SERVER
        return update_config

    def get_waf_default_gray_box(self):
        gray_box = {}
        gray_box["new_rules_enabled"] = False
        return gray_box

    def get_waf_gray_box(self):
        gray_box = self.get_conf().get_value(WAFGrayBox.CONFIG_PATH, {})
        if not gray_box:
            gray_box = self.get_waf_default_gray_box()

        data = WAFGrayBox(BaseConf()).get_data()
        if not data:
            return gray_box

        gray_box["new_rules_action"] = data.get('new_rules_action') or "1"
        gray_box["new_rules"] = [key for key in data.get('new_rules')]
        return gray_box

    def get_waf_ruleset_update_list(self):
        update_list = []
        update_list_file = WafRuleSetUpgrade.WAF_UPDATE_LIST_PATH
        if os.path.exists(update_list_file):
            try:
                with open(update_list_file) as f:
                    update_list = json.load(f)
            except Exception as  ex:
                logging.error("failed to read update_list.json err: {}".format(ex.message))
        return update_list

    def set_upgrade_waf_ruleset(self, action):
        ret = {'action': action, 'time': time.time()}
        return self.set_asp_conf_values([('nginx/upgrade_waf_ruleset', ret)])

    @staticmethod
    def get_tamper_urls_update_time(upstreamConf):
        """Contruct tamper protection's cached url status list.

        use grep -rn with url to find matched cache file and use stat
        to get out the last modification time

        Args:
            upstreamConf (dict): website conf dictionary

        """
        url_list = upstreamConf.get('tamper_cache_urls', [])
        if not url_list:
            return

        serverKey = upstreamConf.get('key')
        tamper_host_name = upstreamConf.get("ServerName")
        ServerNameType = upstreamConf.get("ServerNameType")
        if ServerNameType == "IPv6":
            tamper_host_name = "\[{}\]".format(tamper_host_name)

        for url_settings in url_list:
            url = url_settings[0]
            is_rex = url_settings[1]

            try:

                cache_dir = WafTamperProtection.CACHE_DIR + serverKey
                if is_rex:
                    url = url.replace('^', '')
                    if url.startswith('(?i)'):
                       case_sensitive = 'i'
                       url = url[4:]
                    else:
                        case_sensitive = ''
                    match_string = "KEY:.*{url}".format(url=url)
                    cmd = "egrep -{case_sensitive}rn \'{match_string}\' {cache_dir}".format(case_sensitive=case_sensitive,match_string=match_string, cache_dir=cache_dir)
                else:
                    match_string = "KEY:.*{url}".format(url=url)
                    cmd = "grep -rn \'^{match_string}$\' {cache_dir}".format(match_string=match_string,
                                                                             cache_dir=cache_dir)
                code, out, _ = exe_with_output(cmd)
                if code != 0 or out is None or out.find('matches') == -1:
                    logging.info("Failed grep key in cache files, code:{}, out:{}".format(code, out))
                    continue

                lines = out.splitlines()
                last_update_time = datetime.strptime("2000-01-01 00:00:00","%Y-%m-%d %H:%M:%S")
                for line in lines:
                    filename = line.split()[2]
                    code, out, _ = exe_with_output("stat -c %y {filename}".format(filename=filename))
                    if code != 0 or out is None:
                        logging.error("Failed stat cache file, code:{}, out:{}".format(code, out))
                        continue

                    file_time = out.split(".")[0]
                    file_time= datetime.strptime(file_time,"%Y-%m-%d %H:%M:%S")
                    if file_time > last_update_time:
                        last_update_time = file_time

                if last_update_time != datetime.strptime("2000-01-01 00:00:00","%Y-%m-%d %H:%M:%S"):
                    url_settings.append(last_update_time.strftime("%Y-%m-%d %H:%M:%S"))

            except Exception as ex:
                logging.error('Fail get cache status. {host_name} {url}. error:{error}'.format(host_name=serverKey, url=url,error=ex.message))

    def get_total_diskspace(self, server_key=None):
        sites_diskspace = self.get_all_site_diskspace()
        total_used, now_use = 0, 0
        for key, value in sites_diskspace.items():
            if key != server_key:
                total_used += value
            else:
                now_use = value
        return total_used, now_use

    def get_all_site_diskspace(self):
        nodes = self.get_conf().get_all('nginx/upstreams/')
        ret_dict= {}
        for key,node in nodes.items():
            if node.get("enable_tamper"):
                ret_dict.update({key:int(node.get("tamper_diskspace",'0'))})
        return ret_dict

class DuplicateNameError(Exception):
    pass


class ServerNameUnExistsError(Exception):
    pass

class StrategyIDUnExistsError(Exception):
    pass

class VerificationError(Exception):
    pass

class WafError(Exception):
    pass

def covert_server_key_to_host(server_keys):
    confDb = ConfDb()
    hosts = []
    for serverKey in server_keys:
        try:
            if not serverKey or serverKey == "default.com:*":
                continue
            ary = serverKey.split('_')
            upstream_info = confDb.get_values("nginx/upstreams/" + serverKey)
            if upstream_info == {}:
                continue
            ServerNameType = upstream_info.get("ServerNameType")
            if ServerNameType is None:
                ServerNameType = 'Domain'
            ServerName = upstream_info.get("ServerName")
            if ServerName is None:
                ServerName = ary[0]
                if valid_IPv6(ary[0]):
                    ServerNameType = 'IPv6'
                elif valid_IPv4(ary[0]):
                    ServerNameType = 'IPv4'
            port = upstream_info.get('ListenPort')
            is_https = upstream_info.get('IsHttps')
            site_name=upstream_info.get("site_customize_name", "")
            if site_name != "":
                site_name=site_name + "@"

            if ServerName and port and ServerNameType:
                hosts.append(
                    '{}http{}://{}:{}'.format(site_name, 's' if is_https else '',
                                            '[{}]'.format(ServerName) if 'IPv6' in ServerNameType else ServerName,
                                            port))
            else:
                logging.error(
                    'serverKey: {} is abnorml: ServerName : {} ServerNameType:{} port:{} is_https:{}'.format(
                        serverKey,ServerName,ServerNameType, port,is_https))
        except Exception as ex:
            logging.error(ex)

    return hosts
    
def get_max_strategy_id(strategies,STRATEGY_MIN_ID=waf_util.CUSTOM_STRATEGY_MIN_ID):
    strategy_ids = [item.get('id') for item in strategies]
    max_id = STRATEGY_MIN_ID
    if len(strategy_ids) != 0 and max(strategy_ids) > max_id:
        max_id = max(strategy_ids)

    return max_id


def get_min_useable_strategy_id(strategies, strategy_min_id=waf_util.CUSTOM_STRATEGY_MIN_ID):
    strategy_ids = [item.get('id') for item in strategies]
    strategy_ids = list(set(strategy_ids))
    if not strategy_ids:
        return strategy_min_id
    # 排序
    strategy_ids.sort()

    i = strategy_min_id
    for num in strategy_ids:
        if num == i:
            i += 1
        else:
            break
    return i

def validate_rule(strategy, check_func_dict, strategy_name):
    for k, v in strategy.items():
        if k in check_func_dict:
            try:
                check_func_dict[k](v)
            except Exception as e:
                logging.error(e.message)
                raise Exception(_('{} strategy Error: key({}) = v({}), msg:{}').format(strategy_name, k, v, e.message))

# operation must avoid ' ' and it should be a single English word
def record_operation_log(request, operation_result, operation, message, sub_message, extra_message=None, module = None):
    is_success = '0' if operation_result else '1'
    splice_msg = '' if sub_message == '' else ':{}'.format(sub_message)
    module = ugettext_noop('WAF') if module is None else module
    operation_log(request, module, operation, is_success,
                  {'msg': message, 'spliceMsg': splice_msg, 'extra': extra_message if extra_message else ''})

def filter_id_for_update_list(request,conf_strategies_list,update_strategy_id):
    """
    获取策略列表中除了待更新策略以外的所有策略，并校验是否允许更新
    :param request: 请求
    :param conf_strategies: 全部的策略列表
    :param update_strategy_id: 要修改的策略ID。
    :return: 是否允许更新，其他策略的列表
    """
    conf_strategies_new = []
    old_strategy = None

    current_user = request.user.username
    current_role = WebconsoleConf().get_user_role(current_user)
    for item in conf_strategies_list:
        if item.get("id") != update_strategy_id:
            conf_strategies_new.append(item)
        elif current_role == 'Operator' and 'create_user' in item and item.get('create_user') != request.user.username:
            return False, []
        else:
            old_strategy = item

    return True, conf_strategies_new, old_strategy


class WafStrategyBase(BaseConf):
    MODULE = None
    def __init__(self, max_strategy_num, strategy_asp_config_path, strategy_used_list_asp_config_path, title, strategy_min_id,
                 strategy_api_id_name, compare_strategy_keys, strategy_upstreams_id):
        self.max_strategy_num = max_strategy_num
        self.strategy_asp_config_path = strategy_asp_config_path
        self.strategy_used_list_asp_config_path = strategy_used_list_asp_config_path
        self.title = title
        self.strategy_min_id = strategy_min_id
        self.strategy_api_id_name = strategy_api_id_name
        self.compare_strategy_keys = compare_strategy_keys
        self.strategy_upstreams_id = strategy_upstreams_id

        BaseConf.__init__(self, 'nginx/waf', 'conf_waf')

    def is_full_flow_collection(self):
        """
        检查是否处于全流量采集状态
        :return: True 如果处于全流量采集模式，否则 False
        """
        return self.get_conf().get_value('nginx/httpcap_all_ports', False)

    def set_strategy(self, val):
        return self.set_asp_conf_values([(self.strategy_asp_config_path, val)])

    def set_strategy_used_list(self, val):
        return self.set_asp_conf_values([(self.strategy_used_list_asp_config_path, val)])

    def get_strategies(self):
        return self.get_conf().get_value(self.strategy_asp_config_path, [])

    def get_strategy_used_list(self):
        return self.get_conf().get_value(self.strategy_used_list_asp_config_path, {})

    def check_strategy_name_exist(self,name, id):
        conf_strategies = self.get_strategies()
        for strategy in conf_strategies:
            if strategy.get('name') == name and strategy.get('id') != id:
                return True
        return False

    def check_server_names_exist(self,server_names):
        existed_server_names = self.get_all_upstreams_server_names()
        for server_name in server_names:
            if server_name == "default.com:*":
                continue
            if server_name not in existed_server_names:
                return False
        return True


    def get_all_upstreams_server_names(self):
        '''
        Return all upstream site names.
        '''
        upstreams = self.get_conf().get_all('nginx/upstreams/')
        return upstreams.keys()


    def get_strategy_used_hosts(self, current_user=None):
        strategies = self.get_strategies()
        strategy_used_list = self.get_strategy_used_list()

        allow_sites_key = []
        current_role = WebconsoleConf().get_user_role(current_user)
        is_display_all = current_role != 'Operator'
        if not is_display_all and current_user is not None:
            allow_sites_key = [item.get("key") for item in get_sites_4_user(current_user)]
        for item in strategies:
            site_strategy_used_list = strategy_used_list.get("{}".format(item.get('id')), [])
            if not is_display_all and current_user is not None:
                server_keys = [item2 for item2 in site_strategy_used_list if item2 in allow_sites_key]
            else:
                server_keys = site_strategy_used_list
            item['hosts'] = covert_server_key_to_host(server_keys)

            # 全流量采集关闭时允许删除，开启时需要校验default站点是否存在。
            is_full_flow_collection_enabled = self.is_full_flow_collection()
            if is_full_flow_collection_enabled:
                if "default.com:*" in site_strategy_used_list:
                    item['hosts'].append("http://default.com:*")

        return strategies

    def migrate_old_data(self, strategy):
        return strategy

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

    def record_op_log(self,request,result,action,id):
        module = self.MODULE if self.MODULE else None
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                             ugettext_noop('waf {0} strategy'), id, [self.title], module)

    def update_strategy(self, strategy, request=None,save_asp_config=True):
        try:
            conf_strategies = self.get_strategies()
            strategy_id = strategy.get('id', -1)
            if strategy_id == -1:
                strategy['create_user'] = request.user.username
                strategy['id'] = get_max_strategy_id(conf_strategies, self.strategy_min_id) + 1
                action = 'Add'
            else:
                action = 'Modify'
                is_allow_update, conf_strategies, old_strategy = filter_id_for_update_list(request, conf_strategies, strategy_id)
                if not is_allow_update:
                    result = {
                        'is_success': False,
                        'error_msg': _('You cannot modify policies created by other users.')
                    }
                    self.record_op_log(request, result, action, '')
                    return result
                strategy['create_user'] = request.user.username if old_strategy is None else old_strategy.get('create_user', 'admin')

            self.is_valid_strategy(strategy)

            if len(conf_strategies) < self.max_strategy_num:
                conf_strategies.append(strategy)
                if save_asp_config:
                    self.set_strategy(conf_strategies)

                    result = {
                        'is_success': True,
                        'id': strategy['id'],
                    }
                else:
                    result = {
                        'is_success': True,
                        'id': strategy['id'],
                        'asp_config':{self.strategy_asp_config_path:conf_strategies}
                    }
            else:
                result = {
                    'is_success': False,
                    'error_msg': _(
                        'The number of module strategies exceeds {0}. You cannot create a new strategy.').format(
                        self.max_strategy_num)
                }

            self.record_op_log(request,result,action,strategy['id'])
        except DuplicateNameError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }

            self.record_op_log(request, result, action, '')

        except VerificationError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }

            self.record_op_log(request, result, action, '')

        except Exception as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

            self.record_op_log(request, result, action, '')

        result['name'] = self.title
        return result

    def get_api_strategy_info(self,strategy, request):
        conf_strategies = self.get_strategies()

        if len(conf_strategies) < self.max_strategy_num:
            new_strategy = {}
            new_strategy['id'] = -1
            new_strategy['name'] = _('custom strategy_{}').format(random.randint(1000, 9999))

            if self.check_strategy_name_exist(new_strategy['name'], -1):
                new_strategy['name'] = _('custom strategy_{}').format(random.randint(1000, 9999))

            new_strategy['comment'] = _('Auto Added')
            formatted_strategy = self.migrate_old_data(strategy)
            for key in self.compare_strategy_keys:
                new_strategy[key] = formatted_strategy.get(key, '')
            result = self.update_strategy(new_strategy, request,False)

            result['is_success'] = result.get('is_success', False)
            if result['is_success']:
                result[self.strategy_api_id_name] = result['id']
                del result['id']
            else:
                result['error_msg'] = result['error_msg']
        else:
            result = {
                'is_success': False,
                'error_msg': _(
                    'The number of site strategies exceeds {0}. You cannot create a new strategy.').format(
                    self.max_strategy_num)
            }
        result['name'] = self.title
        return result

    def update_strategy_used_list(self, strategy_id, server_name, action='save'):
        try:
            strategy_used_list = self.get_strategy_used_list()

            # When the configuration is saved without any modification, exit directly
            if action == "save":
                hosts = strategy_used_list.get(str(strategy_id), [])
                if server_name in hosts:
                    return

            for _, hosts in strategy_used_list.items():
                if server_name in hosts:
                    hosts.remove(server_name)

            if action == "save":
                hosts = strategy_used_list.get(str(strategy_id), [])
                hosts.append(server_name)
                strategy_used_list[str(strategy_id)] = hosts
            return {
                self.strategy_used_list_asp_config_path: strategy_used_list
            }
        except Exception as ex:
            self.logger.error('update_strategy_used_list:{}'.format(ex))

    def valid_strategy_id_exist(self, strategy_id):
        '''

        @param strategy_id: type:string
        @return:
        '''
        err = None
        if strategy_id == '-1':
            return None

        elif type(strategy_id) != str and type(strategy_id) != unicode or not re.match('^\d+$', strategy_id):
            err = self.title + _('strategy id: {} is not digit').format(strategy_id)
        else:
            strategy_id = int(strategy_id)
            strategies = self.get_strategies()
            for strategy in strategies:
                if strategy.get('id', '-1') == strategy_id:
                    return None
            err = self.title + _('strategy id: {} is not exist,please re_select the strategy.').format(int(strategy_id))

        return err

    def export_strategy_info(self, strategy_id):
        '''
        export strategy detail info  from asp_config with strategy id
        :param strategy_id: strategy id
        :return: strategy detail info
        '''
        strategy_id = int(strategy_id)
        strategies = self.get_strategies()
        for strategy in strategies:
            if int(strategy.get('id')) == strategy_id:
                del strategy['id']
                return strategy
        return {}

    def compare_strategy_info(self, strategy_id, strategy_info):
        '''
        get waf strategy info from confdb according to the waf_strategy_id,then compare waf strategy info of protected site.
        :param strategy_id: strategy id of protected site
        :param strategy_info:  strategy info of protected site
        :return: return True if equal  else return False
        '''
        exist_strategy_info = self.export_strategy_info(strategy_id)
        if not exist_strategy_info:
            return False

        is_equal = True

        for com_key in self.compare_strategy_keys:
            if strategy_info.get(com_key) != exist_strategy_info.get(com_key):
                is_equal = False
                self.logger.info("Conf waf_regional_access compare false: key:{} info {}:{}".
                                 format(com_key, strategy_info.get(com_key),
                                        exist_strategy_info.get(com_key)))
                break

        return is_equal

    def delete_strategy(self,request):
        try:
            action = 'delete'
            data = json.loads(request.body)
            strategy_id = data.get('id', 0)

            conf_strategies = self.get_strategies()
            if isinstance(strategy_id, int) and len(
                    [item for item in conf_strategies if item.get("id") == strategy_id]) == 1:

                all_strategy_used_list = self.get_strategy_used_list()
                site_strategy_used_list = all_strategy_used_list.get("{}".format(strategy_id), [])
                used_hosts = covert_server_key_to_host(site_strategy_used_list)

                # 全流量采集关闭时允许删除
                # ，开启时需要校验default站点是否存在。
                is_full_flow_collection_enabled = self.is_full_flow_collection()
                if is_full_flow_collection_enabled:
                    if "default.com:*" in site_strategy_used_list:
                        used_hosts.append("http://default.com:*")
                setting_values = {}
                # 在镜像部署模式下，如果只有default.com:*在使用，也允许删除
                if len(used_hosts) == 0:
                    conf_strategies = [item for item in conf_strategies if item.get("id") != strategy_id]
                    #self.set_strategy(conf_strategies)
                    setting_values[self.strategy_asp_config_path] = conf_strategies

                    if not is_full_flow_collection_enabled and "default.com:*" in site_strategy_used_list:
                        # 默认策略策略为1
                        site_strategy_used_list.remove("default.com:*")
                        all_strategy_used_list[strategy_id] = site_strategy_used_list
                        setting_values[self.strategy_used_list_asp_config_path] = all_strategy_used_list

                    self.set_asp_conf_values(setting_values)

                    result = {
                        'is_success': True,
                    }
                else:
                    result = {
                        'is_success': False,
                        'extra_data': used_hosts
                    }
            else:
                result = {
                    'is_success': False,
                    'error_msg': _('strategy_id:{} does not exist').format(strategy_id)
                }


            self.record_op_log(request, result, action, strategy_id)

        except Exception as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            self.record_op_log(request, result, action, '')
        return result

    def update_strategy_used_lists(self,strategy_id,server_names,request=None,allow_multiple_ids=False):
        try:
            result = {
                'is_success': True,
                'id': strategy_id,
            }
            action = 'modify'
            if not self.check_server_names_exist(server_names):
                raise ServerNameUnExistsError(_('There are websites that do not exist in the configuration. Please refresh and try again.'))

            if self.valid_strategy_id_exist(str(strategy_id)):
                raise StrategyIDUnExistsError(_('There are strategy that do not exist in the configuration. Please refresh and try again.'))

            strategy_used_list = self.get_strategy_used_list()

            hosts = strategy_used_list.get(str(strategy_id), [])
            existed_server_names = self.get_conf().get_all('nginx/upstreams/').keys()
            waf_update_dict = {}

            new_server_names = [x for x in server_names if x not in hosts]
            to_remove_names_tmp = [x for x in hosts if x not in server_names]

            to_remove_names = [x for x in to_remove_names_tmp if x in existed_server_names]


            # 特殊逻辑，处理镜像下全流量采集时默认站点策略
            is_full_flow_collection_enabled = self.is_full_flow_collection()
            if is_full_flow_collection_enabled:
                if "default.com:*" in to_remove_names_tmp:
                    to_remove_names.append("default.com:*")
            else:
                if "default.com:*" in to_remove_names_tmp:
                    server_names.append("default.com:*")

            if not new_server_names and not to_remove_names:
                # no change
                return result

            if not allow_multiple_ids:
                # update site change
                for new_server_name in new_server_names:
                    if new_server_name != "default.com:*":
                        path = "nginx/upstreams/"+new_server_name+"/"+self.strategy_upstreams_id
                        waf_update_dict[path]=str(strategy_id)
                    for key, hosts in strategy_used_list.items():
                        if new_server_name in hosts:
                            hosts.remove(new_server_name)
                for remove_name in to_remove_names:
                    if remove_name == "default.com:*":
                        continue
                    path = "nginx/upstreams/"+remove_name+"/"+self.strategy_upstreams_id
                    waf_update_dict[path]=str(self.default_strategy)

                # update used_list conf
                if self.default_strategy in strategy_used_list:
                    strategy_used_list[self.default_strategy].extend(to_remove_names)
                else:
                    strategy_used_list[self.default_strategy] = to_remove_names
            else:
                # update site change
                for new_server_name in new_server_names:
                    if new_server_name != "default.com:*":
                        path = "nginx/upstreams/" + new_server_name + "/" + self.strategy_upstreams_id
                        strategy_ids = self.get_conf().get_value(path, [])
                    if str(strategy_id) not in strategy_ids:
                        strategy_ids.append(str(strategy_id))
                        strategy_ids = sorted(strategy_ids)
                        waf_update_dict[path] = strategy_ids

                for remove_name in to_remove_names:
                    if remove_name == "default.com:*":
                        continue
                    path = "nginx/upstreams/" + remove_name + "/" + self.strategy_upstreams_id
                    strategy_ids = self.get_conf().get_value(path, [])
                    if str(strategy_id) in strategy_ids:
                        strategy_ids.remove(str(strategy_id))
                        strategy_ids = sorted(strategy_ids)
                        waf_update_dict[path] = strategy_ids

            strategy_used_list[str(strategy_id)] = server_names
            waf_update_dict.update({self.strategy_used_list_asp_config_path:strategy_used_list})

            #apply
            self.set_asp_conf_values(waf_update_dict)
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0}{1} strategy Application Site'), '', [self.title,strategy_id],module=self.MODULE)

        except (ServerNameUnExistsError,StrategyIDUnExistsError) as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0}{1} strategy Application Site'), '', [self.title,strategy_id],module=self.MODULE)
        except Exception as ex:
            self.logger.error('update_strategy_used_list_failed:{},strategy_id:{}'.format(ex,strategy_id))
            result = {
                'is_success': False,
                'error_msg': _("Strategy apply failed.")
            }
            record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                                 ugettext_noop('waf {0}{1} strategy Application Site'), '', [self.title,strategy_id],module=self.MODULE)
        return result

class HoneyPotCheck(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/honey_pot_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/honey_pot_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_honey_pot_strategy'#'nginx/upstreams/{site_name}/waf_honey_pot_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_honey_pot_info'
    TITLE = _("Honey Pot Check")
    HONEY_POT_STRATEGY_MIN_ID = 629000
    STRATEGY_API_ID_NAME = 'waf_honey_pot_id'
    COMPARE_STRATEGY_KEYS = ["path_list", "action", "redirect_value"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.HONEY_POT_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"

    def is_valid_strategy(self, strategy):
        WafStrategyBase.is_valid_strategy(self,strategy)
        for item in strategy['path_list']:
            waf_util.valid_string_length(item)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=(self.HONEY_POT_STRATEGY_MIN_ID + 1000), min_v=self.HONEY_POT_STRATEGY_MIN_ID)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "action": (lambda x: waf_util.valid_string_length(x, length=16, verify_re_str=r"[a-z]+")),
            "redirect_value": (lambda x: waf_util.valid_string_length(x, length=64, verify_re_str=r"[a-z]+")),
        }
        validate_rule(strategy, check_func_dict, self.title)

class RegionalAccessControl(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/regional_access_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/regional_access_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_regional_access_strategy'#'nginx/upstreams/{site_name}/waf_regional_access_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_regional_access_info'
    TITLE = _("Regional Access Control")
    REGIONAL_ACCESS_STRATEGY_MIN_ID = 622000
    STRATEGY_API_ID_NAME = 'waf_regional_access_id'
    COMPARE_STRATEGY_KEYS = ["timer_type", "timer_start", "timer_end", "timer_repeat_days", "timer_repeat_start",
                             "timer_repeat_end", "method", "location"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.REGIONAL_ACCESS_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"


    def is_valid_strategy(self, strategy):
        WafStrategyBase.is_valid_strategy(self,strategy)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=623000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "timer_type": (lambda x: waf_util.valid_string_length(x, length=15, verify_re_str=r"[a-z]+")),
            "timer_start": (lambda x: waf_util.valid_number_range(x, max_v=6850000000000, min_v=500000000)),
            "timer_end": (lambda x: waf_util.valid_number_range(x, max_v=6850000000000, min_v=500000000)),
            "timer_repeat_days": (lambda x: waf_util.valid_timer_repeat_days(x, 7)),
            "timer_repeat_start": (lambda x: waf_util.valid_string_length(x, length=9, verify_re_str=r"[:\d]+")),
            "timer_repeat_end": (lambda x: waf_util.valid_string_length(x, length=9, verify_re_str=r"[:\d]+")),
            "method": (lambda x: waf_util.valid_string_length(x, length=15, verify_re_str=r"[a-z]+")),
            "location":(lambda x: waf_util.valid_location(x))

        }

        validate_rule(strategy, check_func_dict, self.title)

class ResLeech(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/res_leech_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/res_leech_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_res_leech_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_res_leech_info'
    TITLE = _("Anti Res_leech")
    RES_LEECH_STRATEGY_MIN_ID = 621000
    STRATEGY_API_ID_NAME = 'res_leech_id'
    COMPARE_STRATEGY_KEYS = ['res_suffix','site_whitelist','url_whitelist']

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.RES_LEECH_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"


    def is_valid_strategy(self, strategy):
        WafStrategyBase.is_valid_strategy(self,strategy)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=622000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "site_whitelist": (lambda x: waf_util.valid_site_whitelist(x)),
            "res_suffix": (lambda x: waf_util.valid_string_length(x, length=1024, min_v=1, verify_re_str=u'^([a-zA-Z0-9]{1,10})(,[a-zA-Z0-9]{1,10})*$')),
            "url_whitelist": (lambda x: waf_util.valid_url_whitelist(x))
        }

        validate_rule(strategy, check_func_dict, _('res_leech_strategy'))

class BruteForce(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/brute_force_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/brute_force_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_brute_force_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_brute_force_info'
    TITLE = _("Brute Force Protection")
    RES_BRUTE_FORCE_MIN_ID = 620000
    STRATEGY_API_ID_NAME = 'waf_brute_force_id'
    COMPARE_STRATEGY_KEYS = ["path_limit", "action", "block_value", "delay", "auto_black_ip", "redirect_value"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.RES_BRUTE_FORCE_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"

    def get_strategies(self):
        '''
        The function upgrade requires special treatment of the old configuration, which is compatible
        upgrade from 22.09 to 22.11,adding user_param and pwd_param

        :return:
        '''
        strategies = WafStrategyBase.get_strategies(self)
        for strategy in strategies:
            for limit in strategy.get('path_limit', []):
                if len(limit) == 3:
                    limit['user_param'] = ''
                    limit['pwd_param'] = ''
        return strategies

    def is_valid_strategy(self, strategy):
        WafStrategyBase.is_valid_strategy(self,strategy)

        for key in ['delay','auto_black_ip']:
            if re.match("[0-9]+",str(strategy.get(key,""))):
                strategy[key] = int(strategy.get(key))

        for limit_access in strategy.get('path_limit',{}):
            for key in ['limit_minutes', 'limit_times']:
                if re.match("[0-9]+",str(limit_access.get(key,""))):
                    limit_access[key] = int(limit_access.get(key))

        if strategy.get("action") == "Block" and re.match("[0-9]+",str(strategy.get('block_value',""))):
            strategy['block_value'] = int(strategy.get('block_value'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=621000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "path_limit": (lambda x: waf_util.valid_brute_force_path_limit(x)),
            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$')),
            "delay": (lambda x: waf_util.valid_number_range(x, max_v=60, min_v=0)),
            "auto_black_ip": (lambda x: waf_util.valid_number_range(x, max_v=36000, min_v=0))
        }

        if strategy.get("action") == "Block":
            check_func_dict.update({"block_value": (lambda x: waf_util.valid_number_range(x, max_v=600, min_v=200,nullable=True))})
        elif strategy.get("action") == "Redirect":
            check_func_dict.update({ "redirect_value":(lambda x: waf_util.valid_string_length(x, length=1024, min_v=0,verify_re_str='^[\x20-\x7e]*$'))})

        validate_rule(strategy, check_func_dict, _('Brute Force Protection'))

class WeakPasswordCheck(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/weak_password_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/weak_password_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_weak_password_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_weak_password_info'
    TITLE = _("Weak Password Check")
    WEAK_PASSWORD_CHECK_MIN_ID = 626000
    STRATEGY_API_ID_NAME = 'waf_weak_password_id'
    COMPARE_STRATEGY_KEYS = ["path_limit", "action", "block_value", "delay", "redirect_value"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.WEAK_PASSWORD_CHECK_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"

    def migrate_old_data(self, old_data):
        """
        兼容旧版本数据的方法，将旧数据转换为当前格式。

        :param old_data: 旧数据
        :return: 兼容的新数据格式
        """
        new_data = {
            "comment": "",
            "delay": 0,
            "path_limit": [],
            "name": "",
            "block_value": "",
            "redirect_value": "",
            "action": ""
        }

        if isinstance(old_data, dict):
            new_data["comment"] = old_data.get("comment", new_data["comment"])
            new_data["delay"] = old_data.get("delay", new_data["delay"])
            new_data["name"] = old_data.get("name", new_data["name"])
            new_data["block_value"] = old_data.get("block_value", new_data["block_value"])
            new_data["redirect_value"] = old_data.get("redirect_value", new_data["redirect_value"])
            new_data["action"] = old_data.get("action", new_data["action"])

            if "path_limit" in old_data:
                if isinstance(old_data["path_limit"], list):
                    for item in old_data["path_limit"]:
                        if isinstance(item, dict):
                            new_item = {
                                "path": item.get("path", ""),
                                "pwd_param": item.get("pwd_param", ""),
                                "user_param": item.get("user_param", ""),
                            }
                            new_data["path_limit"].append(new_item)

        else:
            raise Exception('Unsupported data format:{}'.format(old_data))
        return new_data

    def is_valid_strategy(self, strategy):
        WafStrategyBase.is_valid_strategy(self,strategy)

        for key in ['delay']:
            if re.match("[0-9]+",str(strategy.get(key,""))):
                strategy[key] = int(strategy.get(key))

        if strategy.get("action") == "Block" and re.match("[0-9]+",str(strategy.get('block_value',""))):
            strategy['block_value'] = int(strategy.get('block_value'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=627000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "path_limit": (lambda x: waf_util.valid_weak_password_path_limit(x)),
            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$')),
            "delay": (lambda x: waf_util.valid_number_range(x, max_v=60, min_v=0))
        }

        if strategy.get("action") == "Block":
            check_func_dict.update({"block_value": (lambda x: waf_util.valid_number_range(x, max_v=600, min_v=200,nullable=True))})
        elif strategy.get("action") == "Redirect":
            check_func_dict.update({ "redirect_value":(lambda x: waf_util.valid_string_length(x, length=1024, min_v=0,verify_re_str='^[\x20-\x7e]*$'))})

        validate_rule(strategy, check_func_dict, _('Weak Password Check'))


class CCAttackProtection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/cc_attack_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/cc_attack_strategy_used_list'
    TITLE = _("CC Attack Protection")
    STRATEGY_UPSTREAMS_ID = 'waf_cc_attack_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_cc_attack_info'
    REGIONAL_ACCESS_STRATEGY_MIN_ID = 623000
    STRATEGY_API_ID_NAME = 'waf_cc_attack_id'
    COMPARE_STRATEGY_KEYS = ["is_each_path","paths","limit_seconds","limit_times","action","block_value","delay","auto_black_ip","redirect_value","dimension_key"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.REGIONAL_ACCESS_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        for key in ['delay','auto_black_ip','limit_seconds','limit_times']:
            if str(strategy.get(key,"")).isdigit():
                strategy[key] = int(strategy.get(key))

        if strategy.get("action") == "Block" and str(strategy.get('block_value',"")).isdigit():
            strategy['block_value'] = int(strategy.get('block_value'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=624000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "is_each_path": (lambda x: waf_util.valid_string_length(x,length=5,min_v=1,verify_re_str='\w+')),
            "dimension_key": (lambda x: waf_util.valid_string_length(x, length=20, min_v=1, verify_re_str='[-\w]+')),
            "paths": (lambda x: waf_util.valid_string_length(x, length=103000, min_v=1, verify_re_str='^[\x20-\x7e\r\n]*$')),
            "limit_seconds": (lambda x: waf_util.valid_number_range(int(x), max_v=1000, min_v=1)),
            "limit_times": (lambda x: waf_util.valid_number_range(int(x), max_v=100000, min_v=1)),
            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$')),
            "delay": (lambda x: waf_util.valid_number_range(int(x), max_v=60, min_v=0)),
            "auto_black_ip": (lambda x: waf_util.valid_number_range(int(x), max_v=36000, min_v=0))
        }

        if strategy.get("action") == "Block":
            check_func_dict.update(
                {"block_value": (lambda x: waf_util.valid_number_range(int(x), max_v=600, min_v=200, nullable=True))})
        elif strategy.get("action") == "Redirect":
            check_func_dict.update({"redirect_value": (
                lambda x: waf_util.valid_string_length(x, length=1024, min_v=0, verify_re_str='^[\x20-\x7e]*$'))})

        validate_rule(strategy, check_func_dict, self.title)

class VulnerabilityScanProtection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/vulnerability_scan_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/vulnerability_scan_strategy_used_list'
    TITLE = _("Vulnerability Scan Protection")
    STRATEGY_UPSTREAMS_ID = 'waf_vulnerability_scan_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_vulnerability_scan_info'
    REGIONAL_ACCESS_STRATEGY_MIN_ID = 625000
    STRATEGY_API_ID_NAME = 'waf_vulnerability_scan_id'
    COMPARE_STRATEGY_KEYS = ["request_type","min_sample_count","request_dispersion","max_request_count","limit_seconds","limit_times","action","block_value","delay","auto_black_ip","redirect_value","dimension_key"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.REGIONAL_ACCESS_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        for key in ['delay','auto_black_ip','limit_seconds','limit_times']:
            if str(strategy.get(key,"")).isdigit():
                strategy[key] = int(strategy.get(key))

        if strategy.get("action") == "Block" and str(strategy.get('block_value',"")).isdigit():
            strategy['block_value'] = int(strategy.get('block_value'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=626000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "request_type": (lambda x: waf_util.valid_string_length(x, length=40, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "dimension_key": (lambda x: waf_util.valid_string_length(x, length=20, min_v=1, verify_re_str='[-\w]+')),


            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$')),
            "delay": (lambda x: waf_util.valid_number_range(int(x), max_v=60, min_v=0)),
            "auto_black_ip": (lambda x: waf_util.valid_number_range(int(x), max_v=36000, min_v=0))
        }

        if strategy.get("action") == "Block":
            check_func_dict.update(
                {"block_value": (lambda x: waf_util.valid_number_range(int(x), max_v=600, min_v=200, nullable=True))})
        elif strategy.get("action") == "Redirect":
            check_func_dict.update({"redirect_value": (
                lambda x: waf_util.valid_string_length(x, length=1024, min_v=0, verify_re_str='^[\x20-\x7e]*$'))})

        if strategy.get("request_type") == "all_request":
            check_dict = {"min_sample_count": (lambda x: waf_util.valid_number_range(int(x), max_v=20, min_v=1)),
            "request_dispersion": (lambda x: waf_util.valid_number_range(float(x), max_v=1, min_v=0)),
            "max_request_count": (lambda x: waf_util.valid_number_range(int(x), max_v=100000, min_v=1))}
            check_func_dict.update(check_dict)
        else:
            check_dict = {"limit_seconds": (lambda x: waf_util.valid_number_range(int(x), max_v=1000, min_v=1)),
            "limit_times": (lambda x: waf_util.valid_number_range(int(x), max_v=100000, min_v=1)),}
            check_func_dict.update(check_dict)

        validate_rule(strategy, check_func_dict, self.title)


class XMLAttackProtection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    max_file_list_num=5
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/xml_attack_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/xml_attack_strategy_used_list'
    TITLE = _("XML Attack Protection")
    STRATEGY_UPSTREAMS_ID = 'waf_xml_attack_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_xml_attack_info'
    REGIONAL_ACCESS_STRATEGY_MIN_ID = 627000
    STRATEGY_API_ID_NAME = 'waf_xml_attack_id'
    COMPARE_STRATEGY_KEYS = ["is_basexml_enabled","max_xml_depth", "max_elem_name_length", "max_elem_count", "max_child_node_count",
                             "max_attr_count", "max_attr_name_length", "max_attr_value_length", "max_cdata_length",
                             "max_xml_size", "min_xml_size", "ban_proc_instructions", "ban_dtds",
                             "ban_external_entities", "max_xml_depth_enabled", "max_elem_name_length_enabled",
                             "max_elem_count_enabled", "max_child_node_count_enabled", "max_attr_count_enabled",
                             "max_attr_name_length_enabled", "max_attr_value_length_enabled", "max_cdata_length_enabled",
                             "max_xml_size_enabled", "min_xml_size_enabled", "is_schema_enabled","xml_schema_list",
                             "is_soap_enabled","xml_soap_list"
                             ]

    path_dir = "/etc/asp/release/web_admin/static/sync_file/xml_schema/tmp"

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.REGIONAL_ACCESS_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        if len(strategy.get('xml_schema_list',[]))>self.max_file_list_num:
            raise Exception('xml_schema_list length limit')
        if len(strategy.get('xml_soap_list',[]))>self.max_file_list_num:
            raise Exception('xml_soap_list length limit')

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=628000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "max_xml_depth": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_elem_name_length": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_elem_count": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_child_node_count": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_attr_count": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_attr_name_length": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_attr_value_length": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_cdata_length": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "max_xml_size": (lambda x: waf_util.valid_number_range(x, max_v=100000, min_v=1)),
            "min_xml_size": (lambda x: waf_util.valid_number_range(x, max_v=10000, min_v=1)),
            "ban_proc_instructions": (lambda x: waf_util.valid_bool(x)),
            "ban_dtds": (lambda x: waf_util.valid_bool(x)),
            "ban_external_entities": (lambda x: waf_util.valid_bool(x)),
            "max_xml_depth_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_elem_name_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_elem_count_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_child_node_count_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_attr_count_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_attr_name_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_attr_value_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_cdata_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "max_xml_size_enabled": (lambda x: waf_util.valid_bool(x)),
            "min_xml_size_enabled": (lambda x: waf_util.valid_bool(x)),
            "is_soap_enabled": (lambda x: waf_util.valid_bool(x)),
            "is_schema_enabled": (lambda x: waf_util.valid_bool(x))
        }
        validate_rule(strategy, check_func_dict, self.title)


class GlobalCsrfProtection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/csrf_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/csrf_strategy_used_list'
    TITLE = _("CSRF")
    STRATEGY_UPSTREAMS_ID = 'waf_csrf_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_csrf_info'
    COOKIE_TAMPER_STRATEGY_MIN_ID = 641000
    STRATEGY_API_ID_NAME = 'waf_csrf_id'
    COMPARE_STRATEGY_KEYS = ["whitelist", "path_limit"]

    def __init__(self):
        WafStrategyBase.__init__(self, self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH,
                                 self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.COOKIE_TAMPER_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME,
                                 self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=641999, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "whitelist": (lambda x: waf_util.valid_site_whitelist(x)),
            "path_limit": (lambda x: waf_util.valid_csrf_path_limit(x)),
        }

        validate_rule(strategy, check_func_dict, self.title)


class CookieTamperProtection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/cookie_tamper_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/cookie_tamper_strategy_used_list'
    TITLE = _("Cookie Tamper Protection")
    STRATEGY_UPSTREAMS_ID = 'waf_cookie_tamper_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_cookie_tamper_info'
    COOKIE_TAMPER_STRATEGY_MIN_ID = 628000
    STRATEGY_API_ID_NAME = 'waf_cookie_tamper_id'
    COMPARE_STRATEGY_KEYS = ["protection_method","compatibility_time","enable_http_only","enable_secure","enable_ip_check","cookie_names","action","block_value","delay","redirect_value"]

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.COOKIE_TAMPER_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        for key in ['delay','auto_black_ip','limit_seconds','limit_times']:
            if str(strategy.get(key,"")).isdigit():
                strategy[key] = int(strategy.get(key))

        if strategy.get("action") == "Block" and str(strategy.get('block_value',"")).isdigit():
            strategy['block_value'] = int(strategy.get('block_value'))

        if strategy.get("protection_method") not in ["encryption","sign"]:
            raise Exception('unknown protection_method')

        error = self._vaild_cookie_name(strategy.get('cookie_names',''))
        if error:
            raise VerificationError(error)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=629000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "protection_method": (lambda x: waf_util.valid_string_length(x, length=40, min_v=1,
                                                                    verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "compatibility_time": (lambda x: waf_util.valid_number_range(x, max_v=6850000000000, min_v=500000000)),
            "enable_http_only": (lambda x: waf_util.valid_bool(x)),
            "enable_secure": (lambda x: waf_util.valid_bool(x)),
            "enable_ip_check": (lambda x: waf_util.valid_bool(x)),
            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$')),
            "delay": (lambda x: waf_util.valid_number_range(int(x), max_v=60, min_v=0))
        }


        if strategy.get("action") == "Block":
            check_func_dict.update(
                {"block_value": (lambda x: waf_util.valid_number_range(int(x), max_v=600, min_v=200, nullable=True))})
        elif strategy.get("action") == "Redirect":
            check_func_dict.update({"redirect_value": (
                lambda x: waf_util.valid_string_length(x, length=1024, min_v=0, verify_re_str='^[\x20-\x7e]*$'))})

        validate_rule(strategy, check_func_dict, self.title)

    def _vaild_cookie_name(self,cookie_name_str):
        if cookie_name_str=="":
            return _("Cookie name cannot be empty.")
        cookie_names=cookie_name_str.split("\n")
        verify_re_str=u"^[!#$%&'*+\-.^_`|～A-Za-z0-9]+$"
        for cookie_name in cookie_names:
            if cookie_name=="":
                return _("Cookie name cannot be empty.")

            if not re.findall(verify_re_str,cookie_name):
                return _("The cookie name contains illegal characters, please re-enter.")

class IllegalDownloadProtection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/illegal_download_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/illegal_download_strategy_used_list'
    TITLE = _("Illegal Download Protection")
    STRATEGY_UPSTREAMS_ID = 'waf_illegal_download_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_illegal_download_info'
    ILLEGAL_DOWNLOAD_STRATEGY_MIN_ID = 640000
    STRATEGY_API_ID_NAME = 'waf_illegal_download_id'
    COMPARE_STRATEGY_KEYS = ["max_length",
                             "enable_max_length",
                             "file_extension",
                             "enable_file_extension",
                             "mime_types",
                             "enable_mime_types",
                             "action", "block_value"]

    def __init__(self):
        WafStrategyBase.__init__(self, self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH,
                                 self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.ILLEGAL_DOWNLOAD_STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME,
                                 self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict or 'name' not in strategy.keys():
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        if strategy.get("action") == "Block" and str(strategy.get('block_value', "")).isdigit():
            strategy['block_value'] = int(strategy.get('block_value'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=(self.ILLEGAL_DOWNLOAD_STRATEGY_MIN_ID+1000), min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),

            "max_length": (lambda x: waf_util.valid_number_range(x, max_v=9007199254740992, min_v=0)),
            "enable_max_length": (lambda x: waf_util.valid_bool(x)),
            "file_extension": (lambda x: waf_util.valid_string_length(x, length=1024, min_v=1, verify_re_str=u'^([a-zA-Z0-9]{1,10})(,[a-zA-Z0-9]{1,10})*$')),
            "enable_file_extension": (lambda x: waf_util.valid_bool(x)),
            "enable_mime_types": (lambda x: waf_util.valid_bool(x)),

            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$'))
        }

        if strategy.get("enable_max_length",False)==False and strategy.get("enable_file_extension",False)==False and strategy.get("enable_mime_types",False)==False:
            raise Exception(_('Please enable at least one from File size limit (byte), File extension detection and MIME type detection.'))

        if strategy.get("action") == "Block":
            check_func_dict.update(
                {"block_value": (lambda x: waf_util.valid_number_range(int(x), max_v=600, min_v=200, nullable=True))})

        validate_rule(strategy, check_func_dict, self.title)


class GlobalCustomRule(WafStrategyBase):
    MAX_STRATEGY_NUM = 1000
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/global_custom_rules'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/global_custom_rules_used_list'
    TITLE = _("Global Custom Rule")
    STRATEGY_UPSTREAMS_ID = 'waf_global_custom_rules'
    STRATEGY_UPSTREAMS_INFO = 'waf_global_custom_rule_info'
    STRATEGY_MIN_ID = 624000
    STRATEGY_API_ID_NAME = 'waf_global_custom_rule_id'
    COMPARE_STRATEGY_KEYS = ["sqlinjection_detect_direction","sqlinjection_detect_type","sqlinjection_item_v","sqlinjection_item"]
    ALLOW_MULTIPLE_IDS = True

    WAF_CUSTOM_LOCATION_TRANSFORM ={
        "Request_All": _("All Location"),
        "Request_URL": _("Request URL"),
        "Request_Method": _("Request Method"),
        "Request_Protocol": _("HTTP Protocol Version"),
        "Request_Parameter_Name": _("Request Parameter Name"),
        "Request_Parameter": _("Request Parameter Value"),
        "Request_Header_Name": _("Request Header Name"),
        "Request_Headers": _("Request Header Value"),
        "Request_Referer": _("Request Header Referer"),
        "Request_UA": _("Request Header UA"),
        "Request_Cookie_Name": _("Request Cookie Name"),
        "Request_Cookie": _("Request Cookie Value"),
        "Request_Body": _("Request Body"),
        "Request_Source_IP": _("Source IP"),
        "Request_Multipart_Filename": _("Request File Name"),
        "Request_Multipart_Filebody": _("Request File Multipart"),
        "Response_Header_Name": _("Response Header Name"),
        "Response_Headers": _("Response Headers"),
        "Response_Status": _("Response Status"),
        "Response_Body": _("Response Body"),
    }

    DECODE_LIST = [
        "url_decode","html_decode","slash_decode","base64_decode","utf7_decode","hex_decode","plus_escape","json_parse","xml_parse","php_unserialize",
    ]

    MAX_DECODE_LIST_NUM = 10


    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = []

    def get_strategies(self):
        '''
        The function upgrade requires special treatment of the old configuration, which is compatible
        upgrade from 23.09 to 23.11,adding user_param and pwd_param

        :return:
        '''
        i = 1
        strategies = WafStrategyBase.get_strategies(self)
        for strategy in strategies:
            strategy['action'] = strategy.get('action','Block')
            strategy['block_value'] = strategy.get('block_value', 400)
            strategy['risk_level'] = strategy.get('risk_level', 'high')
            strategy['decode_enable'] = strategy.get('decode_enable', False)
            strategy['decode_sequence'] = strategy.get('decode_sequence', ["url_decode"])
            if not strategy.get('name'):
                strategy['name'] = _('AUTOGEN-NAME-{}').format(i)
                i = i+1

        return strategies

    def is_valid_strategy(self, strategy):
        if not strategy or type(strategy) != dict :
            raise Exception('Invalid module strategy :{}'.format(strategy))
        if self.check_strategy_name_exist(strategy.get('name'), strategy.get('id')):
            raise DuplicateNameError(_('Strategy name is existed.'))

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=625000, min_v=-1)),
            "sqlinjection_comment": (lambda x: waf_util.valid_string_length(x, length=1024)),
            "sqlinjection_detect_direction": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str='^(request|response)$')),
            "sqlinjection_detect_type": (lambda x: waf_util.valid_detect_type_for_custom_rule(x)),

            #"sqlinjection_item": (lambda x: waf_util.valid_number_range(int(x), max_v=1000, min_v=1)),
            "sqlinjection_item_v": (lambda x: waf_util.valid_string_length(x, length=100000, min_v=1,
                                                            verify_re_str='[\w\-=]+')),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                            verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "action": (lambda x: waf_util.valid_string_length(x, length=10, min_v=1, verify_re_str=u'^(Block|Monitor)$')),
            #"sqlinjection_id": (lambda x: waf_util.valid_string_length(x, length=10, min_v=4, verify_re_str=u'(?i)^[a-z]+$')),
            "risk_level": (lambda x: waf_util.valid_string_length(x, length=10, min_v=1,
                                                              verify_re_str=u'^(high|medium|low)$')),
            "decode_enable": (lambda x: waf_util.valid_bool(x))
        }

        if strategy.get("action") == "Block":
            check_func_dict.update({"block_value": (lambda x: waf_util.valid_number_range(x, max_v=600, min_v=200,nullable=True))})

        validate_rule(strategy, check_func_dict, self.title)

        error = self._vaild_sqlinjection_item(strategy.get('sqlinjection_item', '{}'),
                                              strategy.get('sqlinjection_detect_direction'))
        if error:
            raise VerificationError(error)

        error = self._vaild_decode_sequence(strategy.get('decode_sequence', ["url_decode"]))
        if error:
            raise VerificationError(error)

    def valid_strategy_id_exist(self, strategy_id_or_ids):
        '''
        @param strategy_id: type:string
        @return:
        '''
        return None

    def record_op_log(self,request,result,action,id):
        record_operation_log(request, result['is_success'], ugettext_noop('{}'.format(action)),
                             ugettext_noop('waf {0}'), id, [self.title])

    def update_strategy(self, strategy, request=None):
        try:
            conf_strategies = self.get_strategies()
            strategy_id = strategy.get('id', -1)
            if strategy_id == -1:
                action = 'Add'
                strategy['id'] = get_min_useable_strategy_id(conf_strategies, self.strategy_min_id+1)
                strategy['create_user'] = request.user.username

            else:
                action = 'Modify'
                is_allow_update, conf_strategies, old_strategy = filter_id_for_update_list(request, conf_strategies, strategy_id)
                if not is_allow_update:
                    result = {
                        'is_success': False,
                        'error_msg': _('You cannot modify policies created by other users.')
                    }
                    self.record_op_log(request, result, action, '')
                    return result
                strategy['create_user'] = request.user.username if old_strategy is None else old_strategy.get('create_user', 'admin')

            self.is_valid_strategy(strategy)

            if strategy_id != -1:
                old_strategies = [item for item in self.get_strategies() if item.get("id") == strategy_id]
                if len(old_strategies) == 1 and old_strategies[0].get('sqlinjection_detect_direction') != strategy.get(
                        'sqlinjection_detect_direction'):
                    raise WafError(_('The value of detect direction cannot be modified'))

            if len(conf_strategies) < self.max_strategy_num:
                conf_strategies.append(strategy)
                self.set_strategy(conf_strategies)

                result = {
                    'is_success': True,
                    'id': strategy['id'],
                }
            else:
                result = {
                    'is_success': False,
                    'error_msg': _(
                        'The number of strategies exceeds {0}. You cannot create a new strategy.').format(
                        self.max_strategy_num)
                }

            self.record_op_log(request,result,action,strategy['id'])
        except DuplicateNameError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }

            self.record_op_log(request, result, action, '')

        except VerificationError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }

            self.record_op_log(request, result, action, '')

        except WafError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }

            self.record_op_log(request, result, action, '')

        except Exception as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

            self.record_op_log(request, result, action, '')

        result['name'] = self.title
        return result

    def update_strategy_used_lists(self, strategy_id, server_names, request=None, allow_multiple_ids=True):
        return WafStrategyBase.update_strategy_used_lists(self,strategy_id, server_names, request, allow_multiple_ids)

    def update_strategy_used_list(self, strategy_id, server_name, action='save'):
        try:
            strategy_ids = strategy_id # multiple_ids
            strategy_used_list = self.get_strategy_used_list()

            # When the configuration is saved without any modification, exit directly
            #when save site ,the used_list is not changed.
            if action == "save":
                for strategy_id in strategy_ids:
                    hosts = strategy_used_list.get(str(strategy_id), [])
                    if server_name in hosts:
                        return
            #when delete site, delete used_list
            for _, hosts in strategy_used_list.items():
                if server_name in hosts:
                    hosts.remove(server_name)

            #when import site ,update used_list
            if action == "save":
                for strategy_id in strategy_ids:
                    hosts = strategy_used_list.get(str(strategy_id), [])
                    hosts.append(server_name)
                    strategy_used_list[str(strategy_id)] = hosts

            return {
                self.strategy_used_list_asp_config_path: strategy_used_list
            }
        except Exception as ex:
            self.logger.error('update_strategy_used_list:{}'.format(ex))

    def _vaild_sqlinjection_item(self,regs_str,detect_direction):
        # regs_str data sample: "[{key:检测位置1,value:检测表达式1},{key:检测位置2,value:检测表达式2}]"
        regs_list = json.loads(regs_str)
        for item in regs_list:
            place = item.get("key", "")
            reg = item.get("value", "")
            if len(place) == 0 or len(reg) == 0:
                return _('Detection position and detection value cannot be empty.{}').format(item)

            if not check_pcre_regex_valid(reg):
                if ':' in place:
                    place = self.WAF_CUSTOM_LOCATION_TRANSFORM.get(place.split(':')[0],place.split(':')[0]) +_("named")+place.split(':')[1]
                else:
                    place = self.WAF_CUSTOM_LOCATION_TRANSFORM.get(place,place)
                return _('Invalid regular expression of user-defined rules. Place: {}').format(place)

            if place == 'Request_Source_IP' and reg:
                for _tmp in reg.split(','):
                    if not valid_IP_or_CIDR(_tmp):
                        return _("Invalid IP found in Custom rules.")

            if not place.startswith(detect_direction.capitalize()):
                return _('The detection direction({}) and detection({}) position of the custom rule are inconsistent').format(detect_direction,place)

        return None
    
    def _vaild_decode_sequence(self, decode_sequence_list):
        if type(decode_sequence_list) != list:
            return _("Invalid decode sequence")
        if len(decode_sequence_list) > self.MAX_DECODE_LIST_NUM:
            return _("Invalid decode sequence")
        if len(decode_sequence_list) <= 0:
            return _("Invalid decode sequence")
        for d in decode_sequence_list:
            if d not in self.DECODE_LIST:
                return _("Invalid decode sequence")
        return None

class HttpProtocol(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/http_protocol_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/http_protocol_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_http_protocol_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_http_protocol_info'
    TITLE = _("HTTP Protocol Detections")
    HTTPPROTOCOL_MIN_ID = 630000
    STRATEGY_API_ID_NAME = 'http_protocol_id'
    COMPARE_STRATEGY_KEYS = ['url_length', 'user_agent_length', 'referer_length', 'accept_charset_length',
                             'content_length', 'cookie_length', 'cookie_num_limit', 'accept_length', 'range_number',
                             'range_length', 'request_header_num', 'request_header_name_length',
                             'request_header_value_length', 'args_length', 'url_args_num', 'post_body_args_num',
                             'repeat_http_header', 'url_length_enabled', 'user_agent_length_enabled',
                             'referer_length_enabled', 'accept_charset_length_enabled', 'content_length_enabled',
                             'cookie_length_enabled', 'cookie_num_limit_enabled', 'accept_length_enabled',
                             'range_number_enabled', 'range_length_enabled', 'request_header_num_enabled',
                             'request_header_name_length_enabled', 'request_header_value_length_enabled',
                             'args_length_enabled', 'url_args_num_enabled', 'post_body_args_num_enabled']

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.HTTPPROTOCOL_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"


    def is_valid_strategy(self, strategy):
        WafStrategyBase.is_valid_strategy(self,strategy)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=631000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "url_length": (lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "user_agent_length":(lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "referer_length": (lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "accept_charset_length": (lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "content_length": (lambda x: waf_util.valid_number_range(x, max_v=2147436480, min_v=1)),
            "cookie_length": (lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "cookie_num_limit": (lambda x: waf_util.valid_number_range(x, max_v=2048, min_v=1)),
            "accept_length": (lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "range_number": (lambda x: waf_util.valid_number_range(x, max_v=32, min_v=1)),
            "range_length": (lambda x: waf_util.valid_number_range(x, max_v=67108864, min_v=1)),
            "request_header_num": (lambda x: waf_util.valid_number_range(x, max_v=2048, min_v=1)),
            "request_header_name_length": (lambda x: waf_util.valid_number_range(x, max_v=256, min_v=1)),
            "request_header_value_length": (lambda x: waf_util.valid_number_range(x, max_v=10240, min_v=1)),
            "args_length": (lambda x: waf_util.valid_number_range(x, max_v=131072, min_v=1)),
            "url_args_num": (lambda x: waf_util.valid_number_range(x, max_v=2048, min_v=1)),
            "post_body_args_num": (lambda x: waf_util.valid_number_range(x, max_v=2048, min_v=1)),
            "repeat_http_header": (lambda x: waf_util.valid_bool(x)),
            "url_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "user_agent_length_enabled":(lambda x: waf_util.valid_bool(x)),
            "referer_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "accept_charset_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "content_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "cookie_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "cookie_num_limit_enabled": (lambda x: waf_util.valid_bool(x)),
            "accept_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "range_number_enabled": (lambda x: waf_util.valid_bool(x)),
            "range_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "request_header_num_enabled": (lambda x: waf_util.valid_bool(x)),
            "request_header_name_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "request_header_value_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "args_length_enabled": (lambda x: waf_util.valid_bool(x)),
            "url_args_num_enabled": (lambda x: waf_util.valid_bool(x)),
            "post_body_args_num_enabled": (lambda x: waf_util.valid_bool(x))
        }

        validate_rule(strategy, check_func_dict, _('http_protocol_strategy'))

class ParameterDetection(WafStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/parameter_detection_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/waf/parameter_detection_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'waf_parameter_detection_strategy'
    STRATEGY_UPSTREAMS_INFO = 'waf_parameter_detection_info'
    TITLE = _("Parameter Detection")
    STRATEGY_MIN_ID = 631000
    STRATEGY_API_ID_NAME = 'parameter_detection_id'
    COMPARE_STRATEGY_KEYS = []
    MAX_ALL_URL_STRATEGY_NUM = 500
    MAX_URL_STRATEGY_NUM = 50

    def __init__(self):
        WafStrategyBase.__init__(self,self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH, self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                 self.TITLE, self.STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME, self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy="-1"

    def __get_all_url_strategy_num(self):
        conf_strategies = self.get_strategies()
        total = sum(len(item.get("parameter_detections",[])) for item in conf_strategies)
        return total

    def __get_strategy(self,strategy_id=0,domain=''):
        if not (strategy_id or domain):
            return None
        conf_strategies = self.get_strategies()
        conf_strategies = [item for item in conf_strategies if item.get("domain") == domain or item.get("id") == strategy_id]
        result = conf_strategies[0] if conf_strategies else None
        return result

    def __get_parameter_detection(self, url, method, strategy_id='', domain=''):
        strategy = self.__get_strategy(strategy_id=strategy_id,domain=domain)
        if not strategy:
            raise WafError(_('strategy_id:{} does not exist').format(strategy_id))
        url_strategies = strategy.get("parameter_detections")
        url_strategies = [ item for item in url_strategies if item.get("url") == url and item.get("method") == method]
        result = url_strategies[0] if url_strategies else None
        return result

    def export_strategy_info(self, strategy_id):
        '''
        export strategy detail info  from asp_config with strategy id
        :param strategy_id: strategy id
        :return: strategy detail info
        '''
        return {}

    def delete_strategy(self, request):
        try:

            data = json.loads(request.body)
            if "id" in data.keys() and  len(data) ==1:
               result = WafStrategyBase.delete_strategy(self,request)
            elif all(key in data for key in ('id', 'url', 'method')):
                action = 'Modify'
                strategy_id = data.get('id')
                url = data.get('url')
                method = data.get('method')

                conf_strategies = self.get_strategies()
                new_conf_strategies = [item for item in conf_strategies if item.get("id") != strategy_id]
                conf_strategy = self.__get_strategy(strategy_id=strategy_id)
                if conf_strategy:
                    parameter_detection =conf_strategy.get('parameter_detections')

                    new_parameter_detection = [ item for item in parameter_detection if item.get('url')!= url or item.get('method')!=method]
                    if len(parameter_detection) == len(new_parameter_detection):
                        logging.error('Failed to delete rule:strategy_id:{} url:{} method:{} does not exist').format(strategy_id,url,method)
                        result = {
                            'is_success': False,
                            'error_msg': _('Failed to delete rule.')
                        }
                    else:
                        conf_strategy['parameter_detections'] = new_parameter_detection
                        new_conf_strategies.append(conf_strategy)
                        self.set_strategy(new_conf_strategies)
                        result = {
                            'is_success': True,
                        }

                else:
                    logging.error('Failed to delete strategy:strategy_id:{}.').format(strategy_id)
                    result = {
                        'is_success': False,
                        'error_msg': _('Failed to delete strategy.')
                    }
                self.record_op_log(request, result, action, strategy_id)
            else:
                logging.error('Failed to get rule:{}.').format(data)
                result = {
                    'is_success': False,
                    'error_msg': _('The request parameter information is incorrect.')
                }


        except Exception as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _('Failed to delete strategy.')
            }
            self.record_op_log(request, result, action, '')
        return result

    def update_strategy(self, strategy, request=None):
        try:
            strategy_id = strategy.get('id', -1)
            domain = strategy.get('domain','')

            if strategy_id == -1:
                conf_strategy = self.__get_strategy(domain=domain)
                if not conf_strategy:
                    action = 'Add'
                else:
                    action = 'Modify'
                    strategy_id = conf_strategy.get('id')

            else:
                action = 'Modify'

            self.is_valid_strategy(strategy)
            conf_strategies = self.get_strategies()
            if action == 'Add':
                strategy['create_user'] = request.user.username
                strategy['id'] = get_max_strategy_id(conf_strategies, self.strategy_min_id) + 1
                strategy['parameter_detections'] = [strategy.get('parameter_detection')]
                del strategy['parameter_detection']
                if len(conf_strategies) >= self.max_strategy_num:
                    raise WafError(_(
                        'The number of strategies exceeds {0}. You cannot create a new strategy.').format(
                        self.max_strategy_num))

                if self.__get_all_url_strategy_num() >= self.MAX_ALL_URL_STRATEGY_NUM:
                    raise WafError(_(
                        'Failed to create. {0} rules are allowed in total at most.').format(
                        self.MAX_ALL_URL_STRATEGY_NUM))

                conf_strategies.append(strategy)
                self.set_strategy(conf_strategies)

                result = {
                    'is_success': True,
                    'id': strategy['id'],
                }

            elif action == 'Modify':
                is_allow_update, new_conf_strategies, old_strategy = filter_id_for_update_list(request, conf_strategies,strategy_id)
                if not is_allow_update:
                    result = {
                        'is_success': False,
                        'error_msg': _('You cannot modify policies created by other users.')
                    }
                    self.record_op_log(request, result, action, '')
                    return result
                strategy['create_user'] = request.user.username if old_strategy is None else old_strategy.get('create_user', 'admin')

                url = strategy.get('parameter_detection').get('url')
                method = strategy.get('parameter_detection').get('method')
                url_strategy = self.__get_parameter_detection(url, method, strategy_id=strategy_id)
                conf_strategy = self.__get_strategy(strategy_id=strategy_id)
                if not url_strategy:
                    if len(conf_strategy['parameter_detections'])>= self.MAX_URL_STRATEGY_NUM:
                        raise WafError(_(
                            'Failed to create. A strategy can have {0} rules at most.').format(
                            self.MAX_URL_STRATEGY_NUM))

                    if self.__get_all_url_strategy_num() >= self.MAX_ALL_URL_STRATEGY_NUM:
                        raise WafError(_(
                            'Failed to create. {0} rules are allowed in total at most.').format(
                            self.MAX_ALL_URL_STRATEGY_NUM))
                    conf_strategy['parameter_detections'].append(strategy.get('parameter_detection'))

                else:
                    new_parameter_detections = [item for item in conf_strategy['parameter_detections'] if item.get("url") != url or item.get("method") != method]
                    new_parameter_detections.append(strategy.get('parameter_detection'))
                    conf_strategy['parameter_detections'] = new_parameter_detections

                conf_strategy['create_user'] = request.user.username
                new_conf_strategies.append(conf_strategy)

                self.set_strategy(new_conf_strategies)
                result = {
                    'is_success': True,
                    'id': conf_strategy['id'],
                }

            self.record_op_log(request, result, action, result['id'])


        except VerificationError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _('Strategy creation failed.')
            }

            self.record_op_log(request, result, action, '')

        except WafError as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _(e.message)
            }

            self.record_op_log(request, result, action, '')

        except Exception as e:
            self.logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _("Strategy creation failed.")
            }

            self.record_op_log(request, result, action, '')

        result['name'] = self.title
        return result

    def get_parameter_detection_path(self,domain,url,method):
        if url and domain and method:
            return self.__get_parameter_detection(url, method, domain=domain)

        raise Exception(("Get rule,There must be three parameters: url, method, and domain."))

    def get_parameter_detection_strategy(self,strategy_id):
        waf_util.valid_number_range(strategy_id, max_v=self.STRATEGY_MIN_ID + 1000, min_v=self.STRATEGY_MIN_ID)

        result =  self.__get_strategy(strategy_id=strategy_id)
        if result:
            return result
        raise Exception(("Failed to get strategy from {}").format(strategy_id))


    def get_strategy_used_hosts(self, current_user=None):
        strategies = self.get_strategies()
        strategy_used_list = self.get_strategy_used_list()

        allow_sites_key = []
        current_role = WebconsoleConf().get_user_role(current_user)
        is_display_all = current_role != 'Operator'
        if not is_display_all and current_user is not None:
            allow_sites_key = [item.get("key") for item in get_sites_4_user(current_user)]
        for item in strategies:
            server_keys = strategy_used_list.get("{}".format(item.get('id')), [])
            if not is_display_all and current_user is not None:
                server_keys = [item2 for item2 in server_keys if item2 in allow_sites_key]
            item['hosts'] = covert_server_key_to_host(server_keys)
            del item['parameter_detections'] #数据量太大，不一次性返回

        return strategies

    def is_valid_strategy(self, strategy):

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=self.STRATEGY_MIN_ID+1000, min_v=-1)),
            "domain":(lambda x: waf_util.valid_string_length(x, length=300, min_v=1,verify_re_str=u'^[\[\]:\-\*\._A-Za-z0-9\u4e00-\u9fa5]+$')),
            "parameter_detection":(lambda x: waf_util.valid_parameter_detection(x)),
        }

        validate_rule(strategy, check_func_dict, _('Parameter Detection'))


class LLMStrategyBase(WafStrategyBase):
    """
    LLM策略基类，继承自WafStrategyBase，为LLM相关策略提供统一的基类
    """
    MODULE = ugettext_noop('LLM_Protection')
    
    def __init__(self, max_strategy_num, strategy_asp_config_path, strategy_used_list_asp_config_path, title, strategy_min_id,
                 strategy_api_id_name, compare_strategy_keys, strategy_upstreams_id):
        """
        初始化LLM策略基类
        """
        WafStrategyBase.__init__(self, max_strategy_num, strategy_asp_config_path, strategy_used_list_asp_config_path,
                                 title, strategy_min_id, strategy_api_id_name, compare_strategy_keys, strategy_upstreams_id)


class LLMPromptInjectionProtection(LLMStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/llm/prompt_injection_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/llm/prompt_injection_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'llm_prompt_injection_strategy'
    STRATEGY_UPSTREAMS_INFO = 'llm_prompt_injection_info'
    TITLE = _("Prompt Injection Protection")
    MODULE = ugettext_noop('LLM_Protection')
    STRATEGY_MIN_ID = 101000
    STRATEGY_API_ID_NAME = 'llm_prompt_injection_id'
    COMPARE_STRATEGY_KEYS = ["prompt_injection"]

    def __init__(self):
        LLMStrategyBase.__init__(self, self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH,
                                self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                self.TITLE, self.STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME,
                                self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        """
        验证策略是否有效。
        :param strategy: 待验证的策略
        :return: 如果策略有效则返回 True，否则返回 False
        """
        WafStrategyBase.is_valid_strategy(self, strategy)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=102000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                          verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "prompt_injection": (lambda x: waf_util.valid_prompt_injection_config(x)),
        }

        validate_rule(strategy, check_func_dict, _('Prompt Injection Protection'))

class LLMSensitiveDetection(LLMStrategyBase):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/llm/sensitive_detection_strategies'
    STRATEGY_USED_LIST_ASP_CONFIG_PATH = 'nginx/llm/sensitive_detection_strategy_used_list'
    STRATEGY_UPSTREAMS_ID = 'llm_sensitive_detection_strategy'
    STRATEGY_UPSTREAMS_INFO = 'llm_sensitive_detection_info'
    TITLE = _("PII Detection")
    MODULE = ugettext_noop('LLM_Protection')
    STRATEGY_MIN_ID = 102000
    STRATEGY_API_ID_NAME = 'llm_sensitive_detection_id'
    COMPARE_STRATEGY_KEYS = ["sensitive_detection"]

    def __init__(self):
        LLMStrategyBase.__init__(self, self.MAX_STRATEGY_NUM, self.STRATEGY_ASP_CONFIG_PATH,
                                self.STRATEGY_USED_LIST_ASP_CONFIG_PATH,
                                self.TITLE, self.STRATEGY_MIN_ID, self.STRATEGY_API_ID_NAME,
                                self.COMPARE_STRATEGY_KEYS, self.STRATEGY_UPSTREAMS_ID)
        self.default_strategy = "-1"

    def is_valid_strategy(self, strategy):
        """
        验证策略是否有效。
        :param strategy: 待验证的策略
        :return: 如果策略有效则返回 True，否则返回 False
        """
        WafStrategyBase.is_valid_strategy(self, strategy)

        check_func_dict = {
            "id": (lambda x: waf_util.valid_number_range(x, max_v=103000, min_v=-1)),
            "comment": (lambda x: waf_util.valid_string_length(x, length=200)),
            "name": (lambda x: waf_util.valid_string_length(x, length=26, min_v=1,
                                                          verify_re_str=u'^[()\+\-\*\._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$')),
            "sensitive_detection": (lambda x: waf_util.valid_sensitive_detection_config(x)),
        }

        validate_rule(strategy, check_func_dict, _('LLM Sensitive Information Protection'))

class WafConfigCtrl:
    # Waf相关配置信息
    def __init__(self):
        pass
    # 界面相关的配置
    power_strategies = [ResLeech, RegionalAccessControl, BruteForce,
                        CCAttackProtection, XMLAttackProtection, WeakPasswordCheck, VulnerabilityScanProtection,
                        HoneyPotCheck, CookieTamperProtection, IllegalDownloadProtection, GlobalCsrfProtection]
    global_strategies = [GlobalCustomRule]
    protocol_strategies = [HttpProtocol, ParameterDetection]
    llm_strategies = [LLMPromptInjectionProtection, LLMSensitiveDetection]
    # 界面菜单权限绑定配置
    site_apply_permission = ["WAF_Power_Protection","WAF_Global_Custom_Rule","WAF_General_Protection","WAF_Compliance_Detection","LLM_Protection","LLMPromptInjectionProtection","LLMSensitiveDetection"]
    # ==============================

class WafStrategyCtrl:
    all_power_strategies=[
        ResLeech, RegionalAccessControl, BruteForce, CCAttackProtection,GlobalCustomRule,HttpProtocol,VulnerabilityScanProtection,
        WeakPasswordCheck, XMLAttackProtection, CookieTamperProtection, HoneyPotCheck, ParameterDetection, IllegalDownloadProtection,
        GlobalCsrfProtection,LLMPromptInjectionProtection,LLMSensitiveDetection
    ]
    def __init__(self):
        pass

    def export(self,upstream_info):
        for strategy in self.all_power_strategies:
            id = upstream_info.get(strategy.STRATEGY_UPSTREAMS_ID)
            if isinstance(id,basestring) or isinstance(id,int):
                strategy_info = strategy().export_strategy_info(int(id))
                if strategy_info:
                    upstream_info[strategy.STRATEGY_UPSTREAMS_INFO] = strategy_info
            elif isinstance(id,list):
                strategy_infos = []
                for item in id:
                    strategy_info = strategy().export_strategy_info(int(item))
                    if strategy_info:
                        strategy_infos.append(strategy_info)
                upstream_info[strategy.STRATEGY_UPSTREAMS_INFO] = strategy_infos


    def compare_strategy_info(self,upstream_conf):
        special_moudels = [LLMPromptInjectionProtection, LLMSensitiveDetection]
        for strategy in self.all_power_strategies:
            strategy_info_or_infos = upstream_conf.get(strategy.STRATEGY_UPSTREAMS_INFO)
            strategy_id_or_ids = upstream_conf.get(strategy.STRATEGY_UPSTREAMS_ID)

            if strategy_id_or_ids and not strategy_info_or_infos:
                upstream_conf[strategy.STRATEGY_UPSTREAMS_ID] = "-1"
                continue

            if not strategy_id_or_ids or not strategy_info_or_infos:
                continue
            strategy_ctrl = strategy()

            if isinstance(strategy_info_or_infos, list) and isinstance(strategy_id_or_ids, list) and len(strategy_info_or_infos) == len(strategy_id_or_ids):
                num = len(strategy_id_or_ids)
                new_strategy_ids = copy.deepcopy(strategy_id_or_ids)
                for i in range(num):
                    if not strategy_ctrl.compare_strategy_info(strategy_id_or_ids[i], strategy_info_or_infos[i]):
                        new_strategy_ids.remove(strategy_id_or_ids[i])
                upstream_conf[strategy.STRATEGY_UPSTREAMS_ID] = new_strategy_ids

            else:
                if strategy_ctrl.compare_strategy_info(strategy_id_or_ids, strategy_info_or_infos):
                    del upstream_conf[strategy.STRATEGY_UPSTREAMS_INFO]
                else:
                    if strategy in special_moudels:
                        del upstream_conf[strategy.STRATEGY_UPSTREAMS_INFO]
                        upstream_conf[strategy.STRATEGY_UPSTREAMS_ID] = "-1"

    def update_strategy_used_list(self,server_key, action='save', model=None):
        advanced_used_list = {}
        for strategy in self.all_power_strategies:
            if action == 'delete':
                strategy_id = None
            else:
                # if action is save, model must have value.
                strategy_id = getattr(model,strategy.STRATEGY_UPSTREAMS_ID)
            values = strategy().update_strategy_used_list(strategy_id=strategy_id,server_name=server_key,action=action)
            if values:
                advanced_used_list.update(**values)

        return advanced_used_list


    def save_strategy_api(self,input_strategy_info,request):
        result = {'is_success':True}
        asp_configs = {}
        conf = WafConf()
        for strategy in self.all_power_strategies:
            strategy_info = input_strategy_info.get(strategy.STRATEGY_UPSTREAMS_INFO)

            if strategy_info:
                save_result = strategy().get_api_strategy_info(strategy_info, request)
                if save_result.get('is_success'):
                    asp_configs.update(save_result.get('asp_config',{}))
                    if 'asp_config' in save_result.keys():
                        del save_result['asp_config']
                    result.update(save_result)
                else:
                    conf.set_asp_conf_values(asp_configs)
                    return result

        conf.set_asp_conf_values(asp_configs)
        return result

    def get_all_strategy_info(self,result):
        for strategy in self.all_power_strategies:
            result[strategy.STRATEGY_UPSTREAMS_INFO] = strategy().get_strategy_used_hosts()

    def save_site_strategy_valid(self,model,request):
        # 1.update model info.
        # 2.valid model info.
        waf_strategy_error=None
        for strategy in self.all_power_strategies:
            strategy_name_conf=strategy.STRATEGY_UPSTREAMS_ID
            if getattr(strategy,"ALLOW_MULTIPLE_IDS",False):
                strategy_name_value = request.POST.getlist(strategy_name_conf, [])
            else:
                strategy_name_value=request.POST.get(strategy_name_conf, "-1")
            setattr(model,strategy_name_conf,strategy_name_value)
            waf_strategy_error = strategy().valid_strategy_id_exist(strategy_name_value)
            if waf_strategy_error:
                return waf_strategy_error
        return waf_strategy_error

'''
    proxy_models.py should add 
    STRATEGY_UPSTREAMS_ID = ConfArgs('STRATEGY_UPSTREAMS_ID','default_strategy')
    eg:
        waf_res_leech_strategy = ConfArgs('waf_res_leech_strategy','-1')
'''


class GlobalWafWhitelist(BaseConf):
    MAX_STRATEGY_NUM = 100
    STRATEGY_ASP_CONFIG_PATH = 'nginx/waf/waf_id_whitelist'

    def _is_strategy_equal(self,strategy1, strategy2):

        rule1  = copy.deepcopy(strategy1)
        rule2  = copy.deepcopy(strategy2)
        if "comment" in rule1.keys():
            del rule1['comment']

        if "comment" in rule2.keys():
            del rule2['comment']

        return rule1 == rule2

    def is_valid_strategy(self, strategy):
        trans_func = _
        return waf_util.valid_global_whitelist(strategy,trans_func,check_pcre_regex_valid)

    def add_whitelist_with_abd(self, request,strategy):
        conf = WafConf()
        global_whitelist = conf.get_waf_id_whitelist()
        convert_fields = {"rule_id":"id_whitelist","ip":"ip_whitelist"}
        for key,new_key in convert_fields.items():
            strategy[new_key] = strategy.get(key)
            del strategy[key]

        strategy['method']=strategy.get('method','').upper()

        action = ugettext_noop('Add')
        log_from = ugettext_noop('WAF')

        result = self.is_valid_strategy(strategy)
        if not result['save_success']:
            operation_log(request, log_from, action, '1',
                          {'msg': ugettext_noop('Global WAF whitelist added failed')})
            return result

        if len(global_whitelist) >= self.MAX_STRATEGY_NUM:
            result = {
                'save_success': False,
                'error_msg': _(
                    'WAF whitelist only can add up to {0} rules.').format(self.MAX_STRATEGY_NUM)
            }
            operation_log(request, log_from, action, '1',
                          {'msg': ugettext_noop('Global WAF whitelist added failed')})
            return result

        duplicate_strategy = [item  for item in global_whitelist if self._is_strategy_equal(item,strategy)]
        if len(duplicate_strategy) >0:
            result = {
                'save_success': False,
                'error_msg': _(
                    'There are duplicate configuration.')
            }
            operation_log(request, log_from, action, '1',
                          {'msg': ugettext_noop('Global WAF whitelist added failed')})
            return result


        global_whitelist.append(strategy)
        conf.set_waf_id_whitelist(global_whitelist)
        result = {
            'save_success': True,
            'error_msg': None
        }
        operation_log(request, log_from, action, '0',
                      {'msg': ugettext_noop('Global WAF whitelist added successfully')})

        return result

    def modify_global_whitelist(self,request):
        conf = WafConf()

        log_from = ugettext_noop('WAF')

        whitelist = json.loads(request.body)
        if len(whitelist) > self.MAX_STRATEGY_NUM:
            result = {
                'save_success': False,
                'error_msg': _(
                    'WAF whitelist only can add up to {0} rules.').format(self.MAX_STRATEGY_NUM)
            }
            operation_log(request, log_from, ugettext_noop('Modify'), '1',
                          {'msg': ugettext_noop('Global WAF whitelist saved failed')})
            return result

        for i in range(len(whitelist)):
            for j in range(len(whitelist)):
                if i !=j and self._is_strategy_equal(whitelist[i],whitelist[j]):
                    result = {
                        'save_success': False,
                        'error_msg': _(
                            'There are duplicate configuration.')
                    }
                    operation_log(request, log_from, ugettext_noop('Modify'), '1',
                                  {'msg': ugettext_noop('Global WAF whitelist saved failed')})
                    return result


        for item in whitelist:
            result = self.is_valid_strategy(item)
            if not result['save_success']:
                operation_log(request, log_from, ugettext_noop('Modify'), '1',
                              {'msg': ugettext_noop('Global WAF whitelist saved failed')})
                return result

        conf.set_waf_id_whitelist(whitelist)
        result = {
            'save_success': True,
            'error_msg': None
        }
        operation_log(request, log_from, ugettext_noop('Modify'), '0',
                      {'msg': ugettext_noop('Global WAF whitelist saved successfully')})
        return result



def is_valid_update_config(update_config):
    if not update_config or type(update_config) != dict:
        raise Exception('Invalid update_config :{}'.format(update_config))

    check_func_dict = {
        "update_interval": (lambda x: waf_util.valid_string_length(x, length=2, min_v=1,verify_re_str=u'^([1-9]|[12][0-9]|30)$')),
        "update_time": (lambda x: waf_util.valid_string_length(x, length=5, min_v=5,verify_re_str=u'^([01][0-9]|2[0-3]):[0-5][0-9]$')),
        "update_type": (lambda x: waf_util.valid_string_length(x, length=1, min_v=1,verify_re_str=u'^[012]$')),
        "proxy_enabled":(lambda x: waf_util.valid_bool(x))
    }

    proxy_dict = {
        "proxy_protocol": (
            lambda x: waf_util.valid_string_length(x, length=5, min_v=4, verify_re_str=u'(?i)^http(s)?$')),
        "proxy_ip": (lambda x: waf_util.valid_string_length(x, length=15, min_v=0,
                                                            verify_re_str=u'^(25[0-5]|2[0-4]\d|[0-1]?\d?\d)(\.(25[0-5]|2[0-4]\d|[0-1]?\d?\d)){3}$')),
        "proxy_port": (lambda x: waf_util.valid_string_length(x, length=5, min_v=0,
                                                              verify_re_str=u'^([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$'))
    }

    if update_config.get("proxy_enabled"):
        check_func_dict.update(proxy_dict)

    validate_rule(update_config, check_func_dict, _('update_config'))


def get_license_status():
    Conf = ConfDb()
    license_info = Conf.get_license_info()
    canot_upgrade_tip = ''
    can_upgrade = True
    current_is_debug = get_current_is_debug()
    if not license_info.is_in_effect():
        if current_is_debug == 0:
            can_upgrade = False
        canot_upgrade = _(' Cannot upgrade. ')
        if license_info.license_phase == LicenseInfo.LP_No_License:
            canot_upgrade_tip = canot_upgrade + _('The system has not been activated.')
        elif license_info.license_phase == LicenseInfo.LP_Not_In_Effect or license_info.license_phase == LicenseInfo.LP_Expired:
            canot_upgrade_tip = license_info.error_short + canot_upgrade


    return {"can_upgrade":can_upgrade,"canot_upgrade_tip":canot_upgrade_tip}

class LLMPromptListConfig(BaseConf):
    PROMPT_LIST_CONFIG_PATH = 'nginx/llm/setting/prompt_list'
    MODULE = ugettext_noop('LLM_Protection')
    MAX_LIST_LENGTH = 50  # 限制每个列表最多50条
    MAX_CONTENT_LENGTH = 300  # 限制content最大长度
    MAX_COMMENT_LENGTH = 200  # 限制comment最大长度

    def __init__(self):
        BaseConf.__init__(self, 'nginx/llm', 'conf_llm')
        self.default_config = {
            "blacklist": [],
            "whitelist": []
        }

    def get_prompt_list(self):
        """获取提示词黑白名单配置"""
        return self.get_conf().get_value(self.PROMPT_LIST_CONFIG_PATH,self.default_config)

    def save_prompt_list(self, config):
        """保存提示词黑白名单配置"""
        self.set_asp_conf_values([(self.PROMPT_LIST_CONFIG_PATH, config)])

    def is_valid_prompt_list(self, prompt_list):
        """验证提示词列表是否有效"""
        if not isinstance(prompt_list, list):
            raise VerificationError(_("Invalid prompt list format"))
        
        if len(prompt_list) > self.MAX_LIST_LENGTH:
            raise VerificationError(_("Prompt list exceeds maximum length of {}").format(self.MAX_LIST_LENGTH))
        
        for prompt in prompt_list:
            if not isinstance(prompt, dict):
                raise VerificationError(_("Each prompt must be a dictionary"))
            
            # 检查必需的字段
            if 'content' not in prompt:
                raise VerificationError(_("Each prompt must contain 'content' fields"))

            waf_util.valid_string_length(prompt['content'], length=self.MAX_CONTENT_LENGTH, min_v=1)
            waf_util.valid_string_length(prompt.get('comment',''), length=self.MAX_COMMENT_LENGTH, min_v=0)

        # 对 prompt['content'] 进行重复验证
        contents = [item['content'].lower() for item in prompt_list]
        if len(contents) != len(set(contents)):
                raise VerificationError(_("Duplicate content found"))


