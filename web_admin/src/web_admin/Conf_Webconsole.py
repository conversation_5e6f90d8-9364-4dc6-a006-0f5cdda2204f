# -*- coding:utf-8 -*-

import logging
import random
import requests

from Crypto.Cipher import PKCS1_v1_5
from Crypto.PublicKey import RSA
from django.contrib.auth.hashers import make_password, check_password
from django.utils.translation import ugettext_noop, ugettext
from django.core.cache import cache

from Conf_Base import BaseConf
from web_admin.Conf_Nginx import NginxConf
from generic.constants import asp_version
from asp_utils.utils import get_release_file, in_container, get_product_type
from asp_utils.filelock import FileLock
from asp_utils.signature import new_session_with_sign
from radius_client import RADIUSClient

from Crypto.Cipher import AES
import base64
from web_admin import settings
import time
import datetime
import os


# We use the secret key in settings instead of cluster key
# As the cluster key and server password are stored in the same file
key = settings.SECRET_KEY[0:32]

QUERY_FROM_MGR_TIMEOUT = 12


def aes_encrypt(data):
    BS = AES.block_size
    pad = lambda s: s + (BS - len(s) % BS) * chr(BS - len(s) % BS)

    cipher = AES.new(key)
    encrypted = cipher.encrypt(pad(data))

    result = base64.b64encode(encrypted)

    return result


def aes_decrypt(data):
    unpad = lambda s : s[0:-ord(s[-1])]

    result = base64.b64decode(data)

    cipher = AES.new(key)
    decrypted = unpad(cipher.decrypt(result))

    return decrypted


def gen_random_passwd(passwd_size=6):
    char_set = (
        '23456789',  # exclude 0 1
        'abcdefghijkmnpqrstuvwxyz',  # exclude l o
        'ABCDEFGHJKLMNPQRSTUVWXYZ',  # exclude I O
    )

    used_char = []
    unused_char = []
    for cs in char_set:
        t = [i for i in cs]
        char = random.choice(t)
        t.remove(char)

        used_char.append(char)
        unused_char.extend(t)

    random.shuffle(unused_char)
    for i in range(4):
        used_char.append(unused_char.pop())

    random.shuffle(used_char)
    used_char.extend(unused_char)

    index_table = range(len(used_char))
    random.shuffle(index_table)

    passwd_set = ''
    for i in index_table:
        passwd_set += used_char[i]

    offset = []
    for i in range(passwd_size):
        offset.append(index_table.index(i))

    passwd = ''
    for i in range(passwd_size):
        passwd += passwd_set[offset[i]]

    return passwd, passwd_set, offset


def rsa_encrypt(value):
    pub_key_path = get_release_file('conf_shared/pubkey.pem')
    try:
        with open(pub_key_path) as f:
            k = RSA.importKey(f.read())
        cipher = PKCS1_v1_5.new(k)
        c = cipher.encrypt(value)
        return base64.urlsafe_b64encode(c).replace('=', '')
    except Exception as e:
        return None


class WebconsoleConf(BaseConf):

    def __init__(self):
        BaseConf.__init__(self, '_private/web_console/', 'conf_wizard')

    def is_cloud_mode(self):
        return self.get_conf().is_cloud_mode()

    def is_expert_mode(self, username):
        product_type = get_product_type()
        if product_type == 'ApiBotDefender' or product_type == 'ApiSecurityAudit':
            return True
        config = self.get_value('user/%s' % username, is_abs=True)
        if isinstance(config, dict):
            ret = config.get('expert_mode')
            if ret is not None:
                return ret
        return self.get_value('user_profile/%s/expert_mode' % username, def_val=False, is_abs=True)

    def is_web_filter_enable(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if isinstance(config, dict):
            ret = config.get('web_filter_enable')
            if ret is not None:
                return ret
        return False

    def set_enhance_cluster_security(self, enable, ip_list):
        return self.set_asp_conf_values([
            ('cluster/enhance_cluster_security', enable), ('cluster/ecs_ip_white_list', ip_list)
        ])[0] == 0

    def set_sheduled_export(self, enable, frequency, transmit_mode):
        return self.set_asp_conf_values(
            [
                ('cluster/scheduled_export', enable),
                ('cluster/scheduled_export_configs', {"frequency": frequency, "transmit_mode": transmit_mode})
            ]
        )[0] == 0
    def get_web_console_allow_ip_status(self):
        enabled = self.get_value('web_console/allow_ip_enabled', is_abs=True)
        list_not_empty = len(self.get_web_console_allow_ip() or []) != 0
        if enabled is None:
            return list_not_empty
        else:
            return enabled

    def set_web_console_allow_ip_status(self, enabled):
        code = self.set_value('web_console/allow_ip_enabled', enabled, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to change allow_ip_enabled_status . code: {0}'.format(code))
        return code == 0

    def set_web_console_allow_ip(self, allow_ip):
        return self.set_value('web_console_allow_ip', allow_ip, is_abs=True)

    def get_web_console_allow_ip(self):
        return self.get_value('web_console_allow_ip', is_abs=True)

    def set_session_idle_timeout(self, min):
        return self.set_value('web_console/session_idle_timeout', min or '', is_abs=True)

    def get_session_idle_timeout(self):
        return self.get_value('web_console/session_idle_timeout', is_abs=True, def_val='')

    def get_session_idle_timeout_seconds(self):
        return self.get_conf().get_session_idle_timeout_seconds()

    def use_bypass_card(self):
        return os.path.exists('/run/.use_bypass')

    def is_bypass_on(self):
        with os.popen('sudo /usr/sbin/limited_exe bypass_show') as f:
            return ' on\n' in f.read()

    def get_transparent_status(self):
        if self.get_value('nginx/transparent_mode/enable', def_val=False, is_abs=True):
            if self.use_bypass_card() and self.is_bypass_on():
                return 'bypass'
            elif self.get_value('_private/os/network/bridge_transparent', def_val=False, is_abs=True):
                return 'bridge'
            else:
                return 'inline'
        else:
            return 'not_transparent'

    def is_mirror_status(self):
        return self.get_conf().is_mirror()

    def set_cmdline_enable(self, value):
        return self.set_value('web_console/cmdline_enable', value, is_abs=True)

    def get_cmdline_enable(self):
        return self.get_value('web_console/cmdline_enable', is_abs=True, def_val=0 if in_container() else 1)

    def set_ubb_query_log_index(self, value):
        return self.set_value('_private/web_console/ubb_query_log_index', value or 0, is_abs=True)

    def get_ubb_query_log_index(self):
        return self.get_value('_private/web_console/ubb_query_log_index', is_abs=True, def_val=0)

    def is_access_cmdline(self):
        return self.get_cmdline_enable() == 1

    def is_wizard_finished(self):
        return self.get_value('is_wizard_finished')
    
    def set_lock_user_setting(self, lock_limit_enable, lock_limit_days):
        return self.set_asp_conf_values([
            ('web_console/lock_user_setting/lock_limit_enable',  lock_limit_enable),
            ('web_console/lock_user_setting/lock_limit_days',  int(lock_limit_days) if lock_limit_days else '')
        ])[0] == 0
    
    def get_lock_user_setting(self):
        return ({ 'lock_limit_enable': self.get_value('web_console/lock_user_setting/lock_limit_enable', def_val=False, is_abs=True),
                 'lock_limit_days': self.get_value('web_console/lock_user_setting/lock_limit_days', def_val='', is_abs=True)}
                )
    

    
    def judge_user_state(self, username):
        # 0->disabled  1->locked  2-> effective
        user = self.get_value('user/%s' % username, is_abs=True)
        if isinstance(user, dict):
            lock_user_setting = self.get_lock_user_setting()
            manual_disable = user.get('manual_disable', False)
            validity_period = user.get('validity_period', 0)
            last_login_success = user.get('last_login_success') or user.get('mtime') or 0

            if (manual_disable or (validity_period != 0 and datetime.datetime.fromtimestamp(validity_period).date() < datetime.date.today())) and user['role'] != 'Administrator':
                return 0
            if self.is_user_locked(username):
                return 1
            if lock_user_setting['lock_limit_enable'] and last_login_success != 0 and user['role'] != 'Administrator':
                last_login_date = datetime.datetime.fromtimestamp(last_login_success)
                # lock_limit_days -> '' -> 90 todo
                limit_time_date = last_login_date + datetime.timedelta(days=int(lock_user_setting['lock_limit_days'] or 90))
                current_date = datetime.date.today()
                if (limit_time_date.date() < current_date):
                    return 1
            
        return 2

    def set_user(self, username, role=None, auth_mode=None, validity_period=None, manual_disable=None, update_last_login_success=None, password=None, expert_mode=None, table_view=None, set_fail_time=None, create_user=False, config_2fa=None, web_filter_enable=None):
        config = self.get_value('user/%s' % username, is_abs=True)
        if config is None and create_user is False:
            self.logger.error('User %s does not exist' % username)
            return 1

        login_failed_period, login_failed_counts, lock_period = self.get_login_failure_lock(use_default=True)

        if not isinstance(config, dict):
            config = {
                'password': config or ''
            }

        if config and config.get('role', '') == 'Operator' and role is not None and role != 'Operator':
            self.delete_qps_config(username)

        current_time = int(time.time())
        config.setdefault('role', 'Administrator' if username == 'admin' else 'Viewer')
        config.setdefault('auth_mode', 'local')
        config.setdefault('mtime', current_time)
        config.setdefault('last_login_success', 0)
        config.setdefault('password', '')
        config.setdefault('history', [])
        config.setdefault('fail_time', [])
        config.setdefault('expert_mode', False if create_user else self.get_value('user_profile/%s/expert_mode' % username, def_val=False, is_abs=True))
        config.setdefault('table_view', 'card' if create_user else self.get_value('user_profile/%s/table_view' % username, def_val='card', is_abs=True))
        config.setdefault('web_filter_enable', False)

        if role is not None:
            config['role'] = role

        if auth_mode is not None:
            config['auth_mode'] = auth_mode

        if validity_period is not None:
            config['validity_period'] = validity_period

        if manual_disable is not None:
            config['manual_disable'] = manual_disable

        if update_last_login_success is True:
            config['last_login_success'] = current_time
            config['fail_time'] = []

        if expert_mode is not None:
            config['expert_mode'] = expert_mode

        if web_filter_enable is not None:
            config['web_filter_enable'] = web_filter_enable

        if table_view is not None:
            config['table_view'] = table_view

        if config_2fa is not None:
            config['config_2fa'] = config_2fa

        if create_user:
            config['fail_time'] = []
            config['create_time'] = current_time

        if config['auth_mode'] == 'remote':
            config['password'] = ''
            config['history'] = []
            config['mtime'] = None
        else:
            if password is not None:
                if config['password']:
                    config['history'].append(config['password'])
                config['history'] = config['history'][-9:]
                if 'last_login_success' not in config:
                    config['last_login_success'] = config['mtime']
                config['password'] = password
                config['mtime'] = current_time

        if set_fail_time is True:
            now = current_time
            fail_time_list = config['fail_time']
            fail_time_list.append(now)
            config['fail_time'] = filter(lambda x : now - (login_failed_period + lock_period) * 60 <= x <= now, fail_time_list)
        elif set_fail_time is False:
            config['fail_time'] = []
        else:
            # if set_fail_time is None, do nothing
            pass

        code = self.set_value('user/%s' % username, config, is_abs=True)
        if code != 0:
            self.logger.error('Failed to set user. code: {0}'.format(code))
        return code

    def set_user_password(self, username, password):
        self.set_user(username=username, password=password)

    def get_password_period(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        period = self.get_value('web_console/password_period', is_abs=True)
        if not period:
            period = 90
        if isinstance(config, dict) and config.get('auth_mode', '') == 'local':
            mtime = config.get('mtime') or config.get('create_time')
            if mtime:
                seconds = mtime + (period * 24 * 3600) - int(time.time())
                if seconds <= 0:
                    return 0
                else:
                    return 1 + (seconds / 24 / 3600)
        return 99999


    def set_password_period(self, period):
        return self.set_value('web_console/password_period', period or '', is_abs=True)

    def get_password_expiration_time(self):
        return self.get_value('web_console/password_period', is_abs=True, def_val='')


    def set_password_config(self, config):
        err = ''
        try:
            password_length = config['password_length']
            if password_length:
                assert 8 <= int(password_length) <= 32
                config['password_length'] = int(password_length)

            complexity = config['complexity']
            if complexity and not any(complexity.values()):
                assert False

            include_user_name = config['include_user_name']
            if include_user_name is not None:
                assert isinstance(include_user_name, bool)

            history_check = config['history_check']
            if history_check:
                assert 0 <= int(history_check) <= 10
                config['history_check'] = int(history_check)

            period = config['period']
            if period:
                assert 1 <= int(period) <= 365
                config['period'] = int(period)

        except Exception as e:
            logging.info("set password fail:{}".format(e))
            return e

        complexity = config['complexity']
        default_val = [
            ('web_console/password_config/password_length', config['password_length']),
            ('web_console/password_config/include_user_name', config['include_user_name']),
            ('web_console/password_config/history_check', config['history_check']),
            ('web_console/password_config/lower', complexity['lower']),
            ('web_console/password_config/upper', complexity['upper']),
            ('web_console/password_config/num', complexity['num']),
            ('web_console/password_config/notation', complexity['notation']),
            ('web_console/password_period', config['period'])
        ]
        code, output, cmdline = self.set_asp_conf_values(default_val, sync=True)
        return code

    def get_password_config(self, use_default=False):
        default_val = {
            'password_length': '',
            'complexity': {
                'lower': '',
                'upper': '',
                'num': '',
                'notation': ''
            },
            'include_user_name': '',
            'history_check': '',
        }
        default_val['password_length'] = self.get_value('web_console/password_config/password_length', is_abs=True, def_val='')
        default_val['complexity']['lower'] = self.get_value('web_console/password_config/lower', is_abs=True, def_val=True)
        default_val['complexity']['upper'] = self.get_value('web_console/password_config/upper', is_abs=True, def_val=True)
        default_val['complexity']['num'] = self.get_value('web_console/password_config/num', is_abs=True, def_val=True)
        default_val['complexity']['notation'] = self.get_value('web_console/password_config/notation', is_abs=True, def_val=False)
        default_val['include_user_name'] = self.get_value('web_console/password_config/include_user_name', is_abs=True, def_val=False)
        default_val['history_check'] = self.get_value('web_console/password_config/history_check', is_abs=True, def_val='')
        if use_default:
            if not default_val['password_length']:
                default_val['password_length'] = 8
            if not default_val['history_check'] and default_val['history_check'] != 0:
                default_val['history_check'] = 5

        return default_val

    def delete_user(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if config is None:
            self.logger.error('User %s does not exist' % username)
            return
        code = self.set_value('user/%s' % username, None, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to delete user role. code: {0}'.format(code))
        if self.get_user_role(username) == 'Operator':
            self.delete_qps_config(username)

    def is_user_exist(self, username):
        if self.get_value('user/%s' % username, is_abs=True) is not None:
            return True
        else:
            return False

    def get_user_role(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if config is not None:
            if isinstance(config, dict):
                # for new format user config
                return config['role'].encode('utf-8')
            elif username == 'admin':
                # for admin user
                return 'Administrator'
            else:
                # for old format user config besides admin
                return 'Viewer'
        else:
            return ''

    def get_user_auth_mode(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if config is not None:
            if isinstance(config, dict):
                # for new format user config
                return config['auth_mode'].encode('utf-8')
            else:
                # for old format user config
                return 'local'
        else:
            return ''

    # this function will return '' for both remote user and non-exist user
    def get_user_password(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if config is not None:
            if isinstance(config, dict):
                # for new format user config
                return config.get('password', '')
            else:
                # for old format user config
                return config
        else:
            return ''

    def get_user_history_password(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if config is not None:
            if isinstance(config, dict):
                return config.get('history', [])
        return []

    def set_token(self, token):
        code = self.set_value('token', token, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to set token. code: {0}'.format(code))

    def set_expert_mode(self, username, enable):
        return self.set_user(username=username, expert_mode=enable)

    def set_web_filter_enable(self, username, enable):
        return self.set_user(username=username, web_filter_enable=enable)

    def set_login_failure_lock(self, login_failed_period, login_failed_counts, lock_period):
        code, output, cmdline = self.set_asp_conf_values([
            ('web_console/login_failure_lock/login_failed_period',  login_failed_period),
            ('web_console/login_failure_lock/login_failed_counts',  login_failed_counts),
            ('web_console/login_failure_lock/lock_period',  lock_period)
        ])
        if code != 0:
            self.logger.error(
                'Failed to set values for login failure lock. code: {0}, cmd: {1}, Output: >>>>>>\n{2}<<<<<<'.format(code, cmdline, output))
            return False

        return True

    def get_login_failure_lock(self, use_default=False):
        if use_default:
            default_value = self.get_login_failure_lock_default()
            return (self.get_value('web_console/login_failure_lock/login_failed_period', is_abs=True, def_val='') or default_value[0],
                    self.get_value('web_console/login_failure_lock/login_failed_counts', is_abs=True, def_val='') or default_value[1],
                    self.get_value('web_console/login_failure_lock/lock_period', is_abs=True, def_val='') or default_value[2]
                    )
        else:
            return (self.get_value('web_console/login_failure_lock/login_failed_period', is_abs=True, def_val=''),
                    self.get_value('web_console/login_failure_lock/login_failed_counts', is_abs=True, def_val=''),
                    self.get_value('web_console/login_failure_lock/lock_period', is_abs=True, def_val='')
                    )

    def get_login_failure_lock_default(self):
        # return default value for login_failed_period, login_failed_counts and lock_period
        return 30, 3, 30

    def is_user_locked(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        login_failed_period, login_failed_counts, lock_period = self.get_login_failure_lock(use_default=True)
        if isinstance(config, dict):
            fail_time = config.get('fail_time', [])

            now = int(time.time())
            last_half_hour_failure = filter(lambda x: now - lock_period * 60 <= x <= now, fail_time)
            if len(last_half_hour_failure) == 0:
                return False

            last_failure = max(last_half_hour_failure)
            half_hour_interval_failure = filter(lambda x: last_failure - login_failed_period * 60 <= x <= last_failure, fail_time)
            if len(half_hour_interval_failure) >= login_failed_counts:
                return True

        return False

    def set_fail_time(self, username):
        return self.set_user(username=username, set_fail_time=True)

    def reset_last_login_time(self, username):
        return self.set_user(username=username, update_last_login_success=True)

    def is_ip_locked(self, ip):
        locked_ips = self.get_value('locked_ips', {}, True)
        login_failed_period, login_failed_counts, lock_period = self.get_login_failure_lock(use_default=True)
        if not locked_ips.get(ip):
            return False
        if time.time() - locked_ips.get(ip) <= lock_period * 60:
            return True
        locked_ips.pop(ip)
        self.set_value('locked_ips', locked_ips, True)
        return False

    def is_admin_user_disabled(self):
        return self.get_value('web_console/is_admin_user_disabled', False, is_abs=True)

    def disable_admin_user(self):
        return True if self.set_value('web_console/is_admin_user_disabled', True, is_abs=True) == 0 else False

    def enable_admin_user(self):
        return True if self.set_value('web_console/is_admin_user_disabled', False, is_abs=True) == 0 else False

    def lock_ip(self, locked_ip):
        locked_ips = self.get_value('locked_ips', {}, True)
        if len(locked_ips) == 20:
            earliest_lock_ip = None
            earliest_lock_time = None
            for ip, lock_time in locked_ips.items():
                if earliest_lock_time is None or lock_time < earliest_lock_time:
                    earliest_lock_ip = ip
                    earliest_lock_time = lock_time
            locked_ips.pop(earliest_lock_ip)
        locked_ips[locked_ip] = time.time()
        self.set_value('locked_ips', locked_ips, True)

    def unlock_ip(self, ip):
        locked_ips = self.get_value('locked_ips', {}, True)
        if locked_ips.get(ip):
            locked_ips.pop(ip)
            self.set_value('locked_ips', locked_ips, True)
            return True
        return False

    def get_session_idle_all(self):
        return self.get_value('web_console/session_idle_all', is_abs=True, def_val=False)

    def remove_ip_failed_info(self, ip):
        with FileLock('/tmp/.django_cache.lock'):
            try:
                ip_failed_info = cache.get('ip_failed_info', dict())
            except:
                cache.clear()
                ip_failed_info = cache.get('ip_failed_info', dict())
            if ip_failed_info.get(ip):
                ip_failed_info.pop(ip)
            cache.set('ip_failed_info', ip_failed_info)
            return ip_failed_info

    def add_ip_failed_info(self, fail_ip, fail_user, fail_password):
        # will NOT lock the loopback ip address
        if fail_ip == '127.0.0.1':
            return False
        is_ip_locked = False
        with FileLock('/tmp/.django_cache.lock'):
            try:
                ip_failed_info = cache.get('ip_failed_info', dict())
            except:
                cache.clear()
                ip_failed_info = cache.get('ip_failed_info', dict())
            login_failed_period, login_failed_counts, lock_period = self.get_login_failure_lock(use_default=True)
            now = time.time()
            if ip_failed_info.get(fail_ip):
                fail_item = ip_failed_info[fail_ip]
                previous_fail_password = fail_item['fail_password']
                previous_fail_user = fail_item['fail_user']
                fail_time = fail_item['fail_time']
                fail_item['fail_time'] = list()
                if previous_fail_password == fail_password:
                    for t in fail_time:
                        if now - t < login_failed_period * 60:
                            fail_item['fail_time'].append(t)
                else:
                    fail_item['fail_password'] = fail_password
                if previous_fail_user != fail_user:
                    fail_item['fail_time'].append(now)
                    fail_item['fail_user'] = fail_user
                    if len(fail_item['fail_time']) >= login_failed_counts:
                        ip_failed_info.pop(fail_ip)
                        self.lock_ip(fail_ip)
                        is_ip_locked = True
                else:
                    if fail_item['fail_time']:
                        fail_item['fail_time'][-1] = now
                    else:
                        fail_item['fail_time'].append(now)
            else:
                if len(ip_failed_info) >= 100:
                    # remove the item which failed earliest
                    earliest_ip = None
                    earliest_last_fail_time = None
                    for ip, value in ip_failed_info.items():
                        last_fail_time = max(value['fail_time'])
                        if earliest_last_fail_time is None or last_fail_time < earliest_last_fail_time:
                            earliest_ip = ip
                            earliest_last_fail_time = last_fail_time
                    ip_failed_info.pop(earliest_ip)
                ip_failed_info[fail_ip] = {
                    'fail_password': fail_password,
                    'fail_user': fail_user,
                    'fail_time': [time.time()]
                }
            logging.debug('======ip_failed_info is {}'.format(ip_failed_info))
            cache.set('ip_failed_info', ip_failed_info)
        return is_ip_locked

    def get_token(self):
        return self.get_value('token', def_val='', is_abs=True)

    def get_all_no_admin_users(self):
        users = self.get_conf().get_all('user', None)

        # return all users except admin in array
        users_array = []
        if users is not None:
            if 'admin' in users:
                users.pop('admin')
            for name in users.keys():
                user = users[name]

                # for user config in old format, the role is 'Viewer', the auth_mode is 'local'
                role = 'Viewer'
                auth_mode = 'local'
                validity_period = 0
                mtime = 0
                create_time = 0
                last_login_success = 0
                manual_disable = False

                # for the user config in new format, read the role and auth_mode from config
                if isinstance(user, dict):
                    role = user['role']
                    auth_mode = user['auth_mode']
                    validity_period = user.get('validity_period', 0)
                    manual_disable = user.get('manual_disable', False)
                    create_time = user.get('create_time', 0)
                    mtime = user.get('mtime', 0)
                    last_login_success = user.get('last_login_success', 0)

                users_array.append({'user_name': name.encode('utf-8'),
                                    'role': role.encode('utf-8'),
                                    'create_time': create_time,
                                    'mtime': mtime,
                                    'last_login_success': last_login_success,
                                    'auth_mode': auth_mode.encode('utf-8'),
                                    'validity_period': validity_period,
                                    'manual_disable': manual_disable
                                    })

        return users_array

    def get_count_no_admin_users(self):
        return len(self.get_all_no_admin_users())

    def is_captcha_enabled(self, is_prod):
        return self.get_value('captcha', def_val=False, is_abs=True)

    def set_captcha_enabled(self, enabled):
        code = self.set_value('captcha', enabled, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to set user. code: {0}'.format(code))

    def is_qps_enabled(self):
        return self.get_conf().is_qps_enabled()

    def set_qps_enabled(self, enabled):
        code = self.set_value('qps_enable', enabled, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to set user. code: {0}'.format(code))

    def is_2fa_enabled(self):
        return self.get_value('totp_2fa', def_val=False, is_abs=True)

    def is_kicked_enabled(self):
        return self.get_value('web_console/kicked_enable', def_val=False, is_abs=True)

    def set_kicked_enabled(self, enabled):
        code = self.set_value('web_console/kicked_enable', enabled, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to set kick user enabled. code: {0}'.format(code))
        return code == 0

    def set_2fa_enabled(self, enabled):
        code = self.set_value('totp_2fa', enabled, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to set user. code: {0}'.format(code))
        return code == 0

    def get_2fa_config(self, username):
        user_config = self.get_value('user/%s' % username, is_abs=True, def_val={})
        if isinstance(user_config, dict):
            return user_config.get('config_2fa', {})
        else:
            return {}

    def get_tab_view(self, username):
        config = self.get_value('user/%s' % username, is_abs=True)
        if isinstance(config, dict):
            ret = config.get('table_view')
            if ret:
                return ret
        return self.get_value('user_profile/%s/table_view' % username, def_val='card', is_abs=True)

    def set_tab_view(self, username, tab_view):
        if tab_view not in ('list', 'card'):
            self.logger.error(
                'TabView Must Be (list, card)'
            )
            return 1

        return self.set_user(username=username, table_view=tab_view)

    def set_aaa_config(self, aaa_config):
        enabled = aaa_config['enabled']
        # when enable is 0, do not change other config for later usage
        if enabled == '0':
            old_config = self.get_value('aaa_server', is_abs=True)
            if old_config is not None:
                old_config['enabled'] = '0'
                aaa_config = old_config
            code = self.set_value('aaa_server', aaa_config, is_abs=True)
            if code != 0:
                err_msg = 'Failed to set aaa config. code: {0}'.format(code)
                self.logger.error(err_msg)
                return False, ugettext_noop('Failed to set aaa configuration! Check logs for details.')
            else:
                return True, ''

        protocol = aaa_config['protocol']
        if protocol == 'radius':
            ip_1 = aaa_config['1']['ip']
            port_1 = aaa_config['1']['port']
            password = aaa_config['password'] or self.get_aaa_password()
            password = password.encode('utf-8')
            if ip_1 != '':
                # as the feedback from PM, secondary server is optional and no need to verify
                ping_result, err_msg = RADIUSClient().ping_radius_server((ip_1, int(port_1), password))
                if ping_result:
                    # store encrypted password
                    encrypt_pass = aes_encrypt(password)
                    aaa_config['password'] = encrypt_pass
                    # Now the timeout and retry_times could not configured from WebUI and used default value here
                    # However, you could change it directly in config file if needed
                    aaa_config['timeout'] = '5'
                    aaa_config['retry_times'] = '3'

                    code = self.set_value('aaa_server', aaa_config, is_abs=True)
                    if code != 0:
                        err_msg = 'Failed to set aaa config. code: {0}'.format(code)
                        self.logger.error(err_msg)
                        return False, ugettext_noop('Failed to set aaa configuration! Check logs for details.')
                    else:
                        return True, ''
                else:
                    return False, err_msg
            else:
                err_msg = ugettext_noop('IP of primary authentication server is blank.')
                self.logger.error(err_msg)
                return False, err_msg
        else:
            err_msg = ugettext_noop('Server protocol should be RADIUS.')
            self.logger.error(err_msg)
            return False, err_msg

    def is_aaa_enabled(self):
        # When there is no aaa_server, get_value will return None
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            return False

        enabled = config['enabled'].encode('utf-8')
        if enabled == '0':
            return False
        else:
            return True

    def get_aaa_protocol(self):
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            return ''

        return config['protocol'].encode('utf-8')

    def get_aaa_password(self):
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            return ''

        password = config['password'].encode('utf-8')
        return aes_decrypt(password)

    def get_aaa_server(self, name):
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            return '', ''

        ip = config[name]['ip'].encode('utf-8')
        port = config[name]['port'].encode('utf-8')
        return ip, port

    def get_aaa_timeout(self):
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            return ''

        return config['timeout'].encode('utf-8')

    def get_aaa_retry_times(self):
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            return ''

        return config['retry_times'].encode('utf-8')

    def get_aaa_config(self):
        # This function is only called from view function
        # We will not return the timeout and retry_times now
        # Besides, password will not be returned
        config = self.get_value('aaa_server', is_abs=True)
        if config is None:
            config = {
                'enabled': '0',
                'protocol': '',
                '1': {
                    'ip': '',
                    'port': '',
                },
                '2': {
                    'ip': '',
                    'port': '',
                },
            }
            return config

        enabled = config['enabled'].encode('utf-8')
        protocol = config['protocol'].encode('utf-8')
        ip1 = config['1']['ip'].encode('utf-8')
        port1 = config['1']['port'].encode('utf-8')
        ip2 = config['2']['ip'].encode('utf-8')
        port2 = config['2']['port'].encode('utf-8')
        config = {
                'enabled': enabled,
                'protocol': protocol,
                '1': {
                    'ip': ip1,
                    'port': port1,
                },
                '2': {
                    'ip': ip2,
                    'port': port2,
                },
            }
        return config

    def check_adv_operation_code(self, code):
        deadline_seconds = 60 * 60 * 24
        config_path = 'adv_verification'
        config = self.get_value(config_path, is_abs=True, def_val={})
        hashed_pwd = config.get('password', '')
        out_of_date = not (0 < (config.get('expired', 0) - time.time()) < deadline_seconds)
        if out_of_date:
            return False
        return check_password(code, hashed_pwd)

    def get_or_create_adv_verification_url(self):
        """
        :return: verification_url, expired, is_created
        """

        base_url = 'https://lm-test.dev-rs.com/sc/v1/' \
            if not asp_version.current_is_prod_pkg \
            else 'https://lm.dev-rs.com/sc/v1/'

        config_path = 'adv_verification'
        deadline_seconds = 60 * 60 * 24

        config = self.get_value(config_path, is_abs=True, def_val={})
        expired = config.get('expired', 0)
        out_of_date = not (0 < (expired - time.time()) < deadline_seconds)
        url = config.get('url', '')
        created = False
        if out_of_date or not url:
            # do create when create time expired
            pwd, pwd_set, offset = gen_random_passwd()
            encrypted_code = rsa_encrypt(pwd_set)
            if not encrypted_code:
                return ''

            url = '{0}#{1}'.format(
                encrypted_code, ','.join([str(i) for i in offset])
            )
            expired = int(time.time()) + deadline_seconds

            # Use django hasher to encrypt password
            hashed_pwd = make_password(pwd)

            code = self.set_value(config_path, {
                'password': hashed_pwd,
                'expired': expired,
                'url': url,
            }, is_abs=True)

            if code != 0:
                return ''
            created = True

        return base_url + url, expired, created

    def get_site_acl_config(self):
        """
        template_data = {
            "www.test.com_80": {
                "user": {
                    "_value": {
                        "Lucas": {
                            "read": True,
                            "write": True
                        }
                    }
                }
            },
            "www.test.com_80": {...},
            "www.test.com_80": {...}
        }
        """
        site_acl_data = self.get_conf().get_all('site_acl', {})
        upstreams = NginxConf().get_all_upstream_site_names()
        return {k: item for k, item in site_acl_data.items() if k in upstreams}

    def set_site_acl_config(self, site_name, username, no_apply=False):
        """
        write acl config. path:
        :param site_name: site_name.com_80......
        :param username: target username
        :param no_apply: no update to zookeeper
        :return:
        """

        path = 'site_acl/{}/user/'.format(site_name)

        if isinstance(username, (str, unicode)):
            username = [username]
        elif isinstance(username, dict):
            username = [j for i in username.values() for j in i]

        conf = dict()
        for user in username:
            if not user:
                continue

            role = WebconsoleConf().get_user_role(user)
            if role == 'StatisticViewer':
                conf.update({user: {'read': True, 'write': False}})
            else:
                conf.update({user: {'read': True, 'write': True}})
        if no_apply:
            return {path: conf}
        return self.set_asp_conf_values({path: conf})

    def get_user_of_site(self, site_name):
        path = 'site_acl/{}/user/'.format(site_name)
        result = self.get_conf().get_value(path, {})
        user = []
        if result:
            user = result.keys()

        return user

    def get_userinfo_of_site(self, site_name):
        path = 'site_acl/{}/user/'.format(site_name)
        result = self.get_conf().get_value(path, {})
        if result:
            return result

    def clean_site_acl_config(self, site_name):
        '''
        clean acl config
        :param site_name:
        :return:
        '''
        path = 'site_acl/{}/'.format(site_name)
        self.set_asp_conf_values({path: None})

    def get_api_users_list(self, username=None):
        '''
        input: 
            username
        return:
            all user list
        '''

        users = self.get_conf().get_all('user', None)
        return users.keys()


    def set_full_flow_collection(self, status):
        '''
        enable/disable full flow collection
        :param status: True/False
        :return:
        '''
        assert isinstance(status, bool)
        path = 'nginx/httpcap_all_ports'
        self.set_asp_conf_values({path: status})

    def is_full_flow_collection_enabled(self):
        '''check full flow collection status'''
        path = 'nginx/httpcap_all_ports'
        switch_is_enabled = self.get_value(path, def_val=False, is_abs=True)
        deploy_mode_is_mirror = self.is_mirror_status()
        return switch_is_enabled and deploy_mode_is_mirror

    def get_all_alarm_plugin_config(self):
        path = 'system_alarm/plugins'
        return self.get_value(path, def_val=[], is_abs=True)

    def set_alarm_plugin_config(self, config):
        path = 'system_alarm/plugins'
        self.set_asp_conf_values({path: config})

    def is_role_exist(self, role_name):
        if self.get_value('roles/%s' % role_name, is_abs=True) is not None:
            return True
        else:
            return False

    def get_count_of_roles(self):
        return len(self.get_all_roles())

    def get_all_roles_config(self):
        roles_config = self.get_conf().get_all('roles', None)
        return roles_config

    def get_all_roles(self):
        return self.get_all_roles_config().keys()

    def get_all_used_roles_set(self):
        all_users = self.get_all_no_admin_users()
        all_used_roles_set = set()
        for user in all_users:
            if user.get('role'):
                all_used_roles_set.add(user.get('role'))
        return all_used_roles_set

    def set_role(self, role_name, comments, menu_access_right):
        config = {
            'comments': comments,
            'menus': menu_access_right
        }
        code = self.set_value('roles/%s' % role_name, config, is_abs=True)
        if code != 0:
            self.logger.error('Failed to set role. code: {0}'.format(code))
        return code

    def delete_role(self, role_name):
        config = self.get_value('roles/%s' % role_name, is_abs=True)
        if config is None:
            self.logger.error('Role %s does not exist' % role_name)
            return -1
        code = self.set_value('roles/%s' % role_name, None, is_abs=True)
        if code != 0:
            self.logger.error(
                'Failed to delete role. code: {0}'.format(code))
        return code

    def set_role_management_enable(self, value):
        return self.set_value('web_console/role_management_enable', value, is_abs=True)

    def get_role_management_enable(self):
        return self.get_value('web_console/role_management_enable', is_abs=True, def_val=False)

    def get_all_qps_config(self):
        return self.get_conf().get_all('qps', {})

    def get_qps_config(self, user_name):
        return self.get_all_qps_config().get(user_name, {
            'quota': 0,
            'is_limited': False,
            'date_array': [],
            'alarm_id': ''
            })

    def set_qps_config(self, user_name, quota, is_limited):
        old_config = self.get_all_qps_config().get(user_name)
        new_config = dict()
        if old_config:
            if old_config['quota'] != quota:
                new_config['qps/%s/quota' % user_name] = quota
            if old_config['is_limited'] != is_limited:
                new_config['qps/%s/is_limited' % user_name] = is_limited
                new_config['qps/%s/date_array' % user_name] = []
                if not is_limited:
                    alarm_id = old_config.get('alarm_id')
                    if alarm_id:
                        self.recover_alarm(self.get_conf().get_admin_ip(), 133, int(alarm_id), 2)
                        new_config['qps/%s/alarm_id' % user_name] = ''
        else:
            new_config['qps/%s/quota' % user_name] = quota
            new_config['qps/%s/is_limited' % user_name] = is_limited
            new_config['qps/%s/date_array' % user_name] = []
            new_config['qps/%s/alarm_id' % user_name] = ''
        code, output, cmdline = self.set_asp_conf_values(new_config)
        return code

    def delete_qps_config(self, user_name):
        qps_config = self.get_all_qps_config().get(user_name)
        if qps_config is not None:
            alarm_id = qps_config.get('alarm_id')
            if alarm_id:
                self.recover_alarm(self.get_conf().get_admin_ip(), 133, int(alarm_id), 2)
            return self.set_value('qps/%s' % user_name, None, is_abs=True)
        return 0

    def recover_alarm(self, node_ip, alarm_code, id, event_type):
        """
        使用ID恢复告警
        :param node_ip: 节点IP
        :param alarm_code: 告警编号
        :param id: 告警ID
        :param event_type: 2(自动恢复), 3(手动恢复)
        :return:
        """
        # 首先检查node_ip是否集群节点 否则拒绝服务
        all_servers = self.get_conf().get_all('cluster/node_list', {})
        all_server_ips = [
            item['admin']['ip'] for node_id, item in all_servers.items()
            if item.get('admin', {}).get('ip', '')
        ]
        if node_ip not in all_server_ips:
            return dict(result='FAILED', message=ugettext('Have no this node: {}.').format(node_ip))

        if event_type not in (2, 3):
            return dict(result='FAILED', message=ugettext('alarm_code should be 2 or 3.'))

        # 检查cluster_key
        cluster_key = self.get_conf().get_value('cluster/key')
        if not cluster_key:
            return dict(result='FAILED', message=ugettext('Unknown error.'))

        data = dict(
            event_type=event_type,
            alarm_code=alarm_code,  # 告警编号
            alarm_id=id,    # 告警ID
        )
        # 开始向20146发起请求 此请求需要带API-TOKEN
        base_url = 'http://{node_ip}:20146/internal_api/v1/alarm/'.format(node_ip=node_ip)
        session = new_session_with_sign('alarm_api', cluster_key)
        try:
            response = session.post(
                base_url,
                json=data,
                headers={'Content-Type': 'application/json'},
                timeout=12
            )
        except requests.Timeout as e:
            # 处理超时
            if event_type == 2:
                self.logger.error('Send auto recover alarm timeout with {}'.format(e))
                return dict(result='FAILED', message=ugettext('Auto recover timeout.'))
            else:
                self.logger.error('Send manual recover alarm timeout with {}'.format(e))
                return dict(result='FAILED', message=ugettext('Manual recover timeout.'))
        except Exception as e:
            if event_type == 2:
                self.logger.error('Send auto recover alarm error with {}'.format(e))
                return dict(result='FAILED', message=ugettext('Auto recover failed.'))
            else:
                self.logger.error('Send manual recover alarm error with {}'.format(e))
                return dict(result='FAILED', message=ugettext('Manual recover failed.'))

        if response.status_code != 200 or response.json().get('result').upper() != 'OK':
            if event_type == 2:
                self.logger.error('query manual alarm failed with {}'.format(response.content))
                return dict(result='FAILED', message=ugettext('Auto recover failed.'))
            else:
                self.logger.error('query auto alarm failed with {}'.format(response.content))
                return dict(result='FAILED', message=ugettext('Manual recover failed.'))

        if event_type == 2:
            return dict(result='OK', message=ugettext('Auto recovered.'))
        else:
            return dict(result='OK', message=ugettext('Manual recovered.'))
