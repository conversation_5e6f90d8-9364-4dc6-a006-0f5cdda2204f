{% extends "v2/base_login.html" %}

{% load i18n %}

{% block title %}{% trans 'Login' %}{% endblock %}

{% block script %}

    <script language="JavaScript" type="text/javascript">


        if (window.self !== window.top
                && window.self.frameElement && window.self.frameElement.id != '__SESSION_CHECK_FRAME__') {
            window.top.location.reload(true);
        }

        var errorType = {{error_type}};
        var mLogin = {
            username: '',
            password: '',
            captcha_1: '',
            errorMsg: '',
            loginInfo: ''
        }

        var loginForm = null;
        var usernameInput = null;
        var passwordInput = null;
        var captchaElem = null;
        var submitBtn = null;


        function initPage() {
            sessionStorage.setItem('apiPiiDesensitized', '');
            loginForm = rs('rs\\:form');
            usernameInput = loginForm.child('[name=username]');
            passwordInput = loginForm.child('[name=password]');
            captchaElem = loginForm.child('#id_captcha_1');
            submitBtn = rs('button[rsid=bt_login]');
            if (errorType!=0) initMarkError();
            usernameInput.focus();

            rs('body').bind('keyup input', function(evt) {
                if (evt.keyCode > 40) {
                    mLogin.errorMsg = '';
                    errorType = 0;
                    rs.updateProperty();
                }

                if (evt.keyCode == 13) {
                    var focusElem = document.querySelector('*:focus');
                    if (focusElem) focusElem.blur();
                    submitBtn.click();
                }

            });

            if (rs("img.captcha")) rs("img.captcha").bind('click', handleClickCaptcha);
            bindTextEvent();


            utils.oemReplace(function(result) {
                // var data = rs.killHTML(data || '');
                if (result.mode === 'clean') {
                    return false;
                } else if (result.mode === 'custom') {
                    rs('.customProductName').html(rs.killHTML(result.productName || ''));
                    rs('.customProductNameFooter').html('&copy; {{copyright_year}} ' + rs.killHTML(result.companyName || ''));
                } else {
                    rs('.customProductName').html('<span>{% trans "ForceShield Dynamic Application Protection" %}</span>');
                    rs('.customProductNameFooter').html('&copy; {{copyright_year}} ' + '{% trans "ForceShield" %}');
                }
            });
        }

        function onSubmit(evt){
            var isPass = validateSubmitData();
            if (!isPass) return;

            if (mLogin.username=='admin') {
                localUserLogin();

            } else {
                service.isRemoteUser(
                    {'user_name':mLogin.username},
                    function(res) {
                        if (res.result == 'yes') {
                            mLogin.loginInfo = '{% trans "Communicating with primary authentication server" %}';
                            var t1;
                            insetDot(t1);
                            toggleControlEditable(false);
                            saveUserNameInSession();
                            loginForm.submit();

                            var t = setTimeout(function(){
                                clearInterval(t1);
                                clearTimeout(t);
                                getRemoteUserLoginStatus();
                            },2000);
                        } else {
                            localUserLogin();
                        }
                    });
            }

        }

        function insetDot(t) {
            var str = '';
            t = setInterval(function() {
                if (str.length==5) {
                    clearInterval(t);
                    insetDot();
                }
                str+='.';
                rs('#dot').html(str);
            },1000);
        }

        function validateSubmitData() {
            if(Validation.STRING(mLogin.username)) {
                markErrorElem("{% trans 'Please enter a username.' %}", usernameInput);
                return false;
            }

            if (Validation.STRING(mLogin.password)) {
                markErrorElem("{% trans 'Please enter a password.' %}", passwordInput);
                return false;
            }


            if (captchaElem && Validation.STRING(mLogin.captcha_1)) {
                markErrorElem("{% trans 'Please enter the verification code.' %}", captchaElem);
                return false;
            }

            return true;
        }

        function localUserLogin() {
            saveUserNameInSession();
            mLogin.password = _sha1_hash(mLogin.password);
            loginForm.submit();
            toggleControlEditable(false);
        }

        function getRemoteUserLoginStatus() {
            service.getRemoteUserLoginStatus(
                { 'user_name':mLogin.username },
                function(res) {
                    if (res.result=='ok') {
                        mLogin.loginInfo = res.login_status;
                        mLogin.errorMsg = '';
                        var t2;
                        insetDot(t2);

                        var t = setTimeout(function() {
                            clearInterval(t2);
                            clearTimeout(t);
                            getRemoteUserLoginStatus();
                        }, 5000);

                    } else {
                        mLogin.loginInfo = '';
                        mLogin.errorMsg = res.login_status;
                        toggleControlEditable(true);
                    }
                });

        }

        function initMarkError() {
            getUserNameInSession();
            switch(errorType) {
                case 1:
                    markErrorElem('{% trans "Incorrect username or password, or account/IP is locked." %}', passwordInput);
                    break;
                case 2:
                    markErrorElem('{% trans "Invalid confirmation code." %}', captchaElem);
                    break;
                case 3:
                    markErrorElem('{% trans "Request timeout" %}');
                    break;
                case 4:
                    markErrorElem('{% trans "Remote authentication server is not enabled" %}');
                    break;
                // case 5: deleted
                case 6:
                    markErrorElem('{% trans "The session has expired. Continue editing after login." %}');
                    break;
                case 7:
                    markErrorElem('{% trans "Incorrect authentication code." %}', loginForm.child('[name=totp_token]'));
                    break;
                case 8:
                    markErrorElem('{% trans "Same account repeat login restricted. Please login again to continue editing" %}');
                    break;
                case 9:
                    markErrorElem('{% trans "admin user is disabled." %}');
                    break;
                case 99:
                    markErrorElem('{% trans "Login Success" %}');
                    rs('body').child('[name=errorMsg]').addClass('login-success');
                    break;
                default:
                    break;
            }
        }

        function markErrorElem(msg, elem) {
            mLogin.errorMsg = msg;
            markError(elem);
        }

        function toggleControlEditable(isEdit) {
            var nameElem = loginForm.named('username');
            var passwordElem = loginForm.named('password');
            var caElem = loginForm.named('forgetPassword').child('a');

            var statusVal = isEdit?null:'';
            var linkVal = isEdit?'{% url 'password_reset_token' %}':'javascript:void(0);';

            nameElem.attr('readonly', statusVal);
            passwordElem.attr('readonly', statusVal);
            submitBtn.attr('disabled', statusVal);
            caElem.attr('disabled', statusVal);
            caElem.href = linkVal;
        }

        function handleClickCaptcha(evt) {
            var img = evt.currentTarget;
            service.refreshCaptcha(function(res) {
                if (res && res.image_url && res.key) {
                    img.src = res.image_url;
                    img.parent('div').child('#id_captcha_0').value = res.key;
                }
            }, function() {
                alert("{% trans 'Request exception'%}");
            });
        }

        function saveUserNameInSession() {
            rs.session.set('loginUsername', mLogin.username);
        }

        function getUserNameInSession() {
            mLogin.username = rs.session.get('loginUsername') || '';
        }

    </script>

{% endblock %}

{% block content %}
    <style>
        input, button {
            font-size: 14px;
        }

        #id_captcha_1 {
            display: inline-block;
            width: 65%;
            float: left;
        }

        img[class=captcha] {
            vertical-align: bottom;
            display: inline-block;
            float: right;
            margin-top: 5px;
            box-sizing: border-box;
        }

        input {
            border: solid 1px #d6d7d9;
        }

        input:focus, input:hover {
            border: solid 1px #adafb3;
        }

        p[name=forgetPassword] {
            margin-top:20px;
            font-size: 14px;
        }

        button[type=submit] {
            border-radius: 5px;
        }

        .infoMessage { font-size:14px }
        .infoMessage div[name=loginInfo] { display: inline-block; }
        .infoMessage #dot { display:inline-block;width:50px;text-align:left; }

        img.captcha { cursor:pointer; }

        .errorMessage {
            background-color: #ffeeee;
            border-left: 4px solid #d93939;
            padding: 2px 4px;
        }

        .errorMessage > div[name=errorMsg].no-allow-ip {
            background-color: #ffeeee;
            color: #ce5333;
            line-height: 150px !important;
            text-align: center !important;
            font-size: 18px !important;
        }

        .errorMessage > div[name=errorMsg].no-allow-ip:before {
            content: '\e934' !important;
            font-family: "rs-icon" !important;
            border:0px !important;
            background-color: transparent !important;
            display: inline-block;
            height: auto !important;
            width: auto !important;
            margin-right: 12px;
            vertical-align: bottom !important;
            font-size: 24px !important;
        }


        .errorMessage > div[name=errorMsg].login-success {
            background-color: #e7f3ff;
            color: #319af3;
            border-radius: 5px;
            text-align: center !important;
            line-height: 50px !important;
            font-size: 16px !important;
        }

        .errorMessage > div[name=errorMsg].login-success:before {
            background-color: #eaf5ff;
        }

    </style>


    <div class="login-content">
        <section>
            <div class="logo"></div>
            <h1 class="customProductName">
                <!-- <span>
                    {% trans "ForceShield Dynamic Application Protection" %}
                </span> -->
            </h1>
        </section>

        <section class="loginForm">
            <rs:form model="#mLogin" method="post">
                {% csrf_token %}
                <section class="errorMessage" rs:visible="mLogin.errorMsg">
                    <div name="errorMsg"></div>
                </section>

                <section class="infoMessage" rs:visible="mLogin.loginInfo">
                    <div name="loginInfo"></div><span id="dot"></span>
                </section>

                {% if error_type != 5 %}
                <div class="group">
                    <input type="text" name="username" placeholder="{% trans 'Username ' %}"/>
                    <input type="password" name="password" placeholder="{% trans 'Password ' %}" autocomplete="off" />
                    <input type="hidden" name="next" value="{{ next|escape }}" />
                </div>
                {% if totp_enabled %}
                    <div class="totpPane">
                        <input type="text" name="totp_token" placeholder="{% trans 'TOTP Token' %}"/>
                    </div>
                {% endif %}
                {% if captcha %}<div name="captcha_pane">{{ form.captcha }}</div>{% endif %}
                <button type="button" onclick="onSubmit(event)" important id="__LOGIN_BTN__" rsid="bt_login">{% trans 'Log in' %}</button>
                <p name="forgetPassword"><a href="{% url 'password_reset_token' %}">{% trans 'Forgot your password?' %}</a></p>
                {% endif %}
            </rs:form>
        </section>
    </div>

{% endblock %}
