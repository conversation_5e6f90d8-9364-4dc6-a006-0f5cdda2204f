{% load nav_tags %}
{% load i18n %}
{% block content %}

<div id="reportWrapper" class="panel">
    <div id="reportDiv" v:if="global_params.logNodeAvailable">
        <section class="errorTips">
            <i class="ic-alert"></i>
            <span name="errorMsg">{% trans 'Some nodes in the cluster are offline, and the query results will not be from complete data.' %}</span>
        </section>

        <div id="ifrWrapper">
            <div class="s-operation" v:if="!(pageInstance.isOverviewPage || pageInstance.isNodePage)">
                <rs:FullScreenButton id="fullScreenBtn" v:if="!embedMode && pageInstance.isShowFullScreenBtn"></rs:FullScreenButton>
            </div>
            <rs:iframe id="ifr"
                scrolling="auto"
                v:autoSelfHeight="g_const.vertical_layout"
                onContentReady="apiHack(event)">
                <css disable="#!embedMode">/static/css/api_hack_sailfish.css</css>
            </rs:iframe>
        </div>
    </div>
</div>
    <style>
    rs\:NavigationBar {
       left: 80px;
    }
    </style>

    <rs:style enable="#embedMode">
        rs\:NavigationBar {
            display: none;
        }

        .mainContent {
            padding-top: 0px !important;
            margin-top: 0px;
            overflow: auto;
        }

        .contentWrapper {
            padding: 0px !important;
        }
    </rs:style>
{% endblock %}


{% block script %}
<script>

    var _win = embedMode ? self : top;

    function apiHack(doc) {
        if (!embedMode) return null;
        
        var contentHeight = 0;
        var isObseveDialog = false;
        var checkCounter = 0;

        function modifySailfishContent() {
            var app = doc.querySelector('#app'); // Sailfish only
            if (app) {
                var appHeight = app.clientHeight;
                var ifr = rs('#ifr');
                if (ifr) {
                    ifr.style.height = appHeight + 'px';
                    rs('.contentWrapper').style.height = appHeight + 'px';
                    if (checkCounter < 30) {
                        checkCounter++;
                        setTimeout(modifySailfishContent, 1000);
                    }
                    contentHeight = appHeight;
                }

                if (!isObseveDialog) {
                    listenVizDetailDialog(app);
                    isObseveDialog = true;
                }
            }
        }

        function listenVizDetailDialog(app) {
            var observer = new MutationObserver(function(mutationsList, observer) {
                for (var i in mutationsList) {
                    var mutation = mutationsList[i];
                    if (mutation.type === 'attributes') {
                        var classValue = mutation.target.getAttribute('class');
                        if (/openDialog/i.test(classValue)) {
                            resetView();
                        }
                    }
                }
            });
            var detailDialog = app.querySelector('.vizDetailDialog');
            observer.observe(detailDialog, { attributes: true });
        }

        modifySailfishContent();
    }

    rs.plugin({
        ToggleButton: '~/ui.ToggleButton.js',
        FullScreenButton: FullScreenButton
    });

    {% autoescape off %}
    var pageInstance = {{ report_type|to_json }} == 'sailfish' ? new SailfishManager() : new PhoenixManager();
    var session_idle_all = {{ session_idle_all|to_json }};
    {% endautoescape %}

    rs.bind('ready', function() {
        if (!global_params.logNodeAvailable) return;
        if (Sys.ie && parseFloat(Sys.ie) < 10) {
            rs('#reportDiv').html('{% trans "A Chrome browser of the latest version is required to display Statistics." %}');
            return;
        }

        pageInstance.loadPage();
        pageInstance.setTitle();
    });

    /**
     * 报表类型行为基类 - Phoenix 和 Sailfish
     * dataMap: {
     *   page_path : { title: '浏览器页签标题', navigator: '页面标题', url: '报表src', hasFullScreenButton: true/false
     * }
    */
    function StatisticsBasicManager(dataMap) {
        var _this = this;
        var pathName = _win.location.pathname,
            search = _win.location.search;

        var _cssPathMapInIframe = { 'default': [], '/overview/': [] };

        Object.defineProperties(this, {
            isOverviewPage: {
                get: function() {
                    return pathName == '/overview/' && search.indexOf('node_ip') < 0;
                }
            },
            isNodePage: {
                get: function() {
                    return pathName == '/overview/' && search.indexOf('node_ip') > -1;
                }
            },
            isShowFullScreenBtn: {
                get: function() {
                    return _this.conf.hasFullScreenButton;
                }
            },
            cssPathMapInIframe: {
                get: function() {
                    return _cssPathMapInIframe;
                },
                set: function(map) {
                    _cssPathMapInIframe = map;
                }
            }
        });

        this.dataMap = dataMap;
        this.conf = _getConf(dataMap);
        if (!this.conf) return;

        this.baseUrl = this.conf.url;
        this.unableConnectionMsg = '{% trans "Unable to connect to big data analysis server, please try again later." %}';

        this.setTitle = function() {
            _win.document.title = page.setPageTitle(_this.conf.title);
            rs('#navigationBar').setHeader(_this.conf.navigator);
        };

        this.getUrl = function() {
            return _this.baseUrl;
        };

        this.initIframe = function() {
            var rsIframe = rs('#ifr');
            var iframe = rsIframe.iframe;

            setTimeout(function() {
                iframe.src = _this.getUrl();
                insertCssIntoIframe();
            }, 300);

            // /*
            // * 以下补丁用来解决 DAP-20468 Firefox初次加载无法正确隐藏顶部菜单栏的问题
            // * */
            var isFirefox = rs.browser.type === 'Firefox';
            if (_this.isOverviewPage && isFirefox) {
                rsIframe.style.opacity = 0;
                iframe.onload = function() {
                    setTimeout(function() {
                        iframe.contentWindow.location.reload();
                        iframe.onload = function() {
                            rsIframe.style.opacity = 1;
                            delete iframe.onload;
                        }
                    }, 500);
                }
            }
        }

        this.insertCssInPage = function() {
            if (_this.isOverviewPage) return;

            var cssPathInPage = {
                'default': ['/static/css/statistics/statistics.css'],
                '/overview/': ['/static/css/statistics/overview.css']
            };
            if (g_const.vertical_layout) cssPathInPage['default'].push('/static/css/statistics/vertical_layout/vertical.css');

            var key = _this.isOverviewPage ? pathName : 'default';
            var pathArray = cssPathInPage[key];
            if (!pathArray) return;
            appendCssLinkInPage(pathArray);
        }

        this.setIframeHeightInOverview = function(scaleHeightCallback, errorMsg) {
            if (_this.isOverviewPage) {
                var ifr = _win.document.querySelector('#ifr').iframe;
                ifr.bind('load', function() {
                    var hasError = checkAndShowErrorMsg(ifr, errorMsg);
                    if (hasError) return;
                    setIframeHeight(ifr, scaleHeightCallback);
                });
            }
        }

        function setIframeHeight(ifr, scaleHeightFun) {
            var ifrDoc = ifr.contentWindow.document;
            listenerDOMChange(ifrDoc, function() {
                scaleHeightFun(ifr);
            });
        }

        function checkAndShowErrorMsg(ifr, errorMsg) {
            var ifrBody = ifr.contentWindow.document.body;
            var nodes = ifrBody.children;
            if (!nodes) return false;
            var len = nodes.length;
            if (len == 1) {
                var node = nodes[0];
                if (node && node.nodeType==1) {
                    var _text = node.innerHTML;
                    if (!_text) return false;
                    if (_text == errorMsg) return true;
                    try {
                        var data = JSON.parse(_text);
                        var content = data['err_msg'];
                        if (content) {
                            if (global_params.inContainer) {
                                setTimeout(function() {
                                    ifr.contentWindow.location.reload();
                                }, 10000);
                            }
                            node.innerHTML = errorMsg;
                            return true;
                        }
                    } catch(e) {}
                }
            }

            return false;
        }

        function listenerDOMChange(rootNode, someFun) {
            var config = { attributes: true, childList: true, subtree: true, characterData: true };
            var callback = function(mutationsList, observer) {
                someFun();
            }

            var observer = new MutationObserver(callback);
            observer.observe(rootNode, config);
        }

        function appendCssLinkInPage(pathArray) {
            pathArray.forEach(function(path) {
                var headTag = document.getElementsByTagName('head')[0];
                if (!headTag) return false;
                var styleLink = document.createElement('link');
                styleLink.setAttribute('rel', 'stylesheet');
                styleLink.setAttribute('href', path);
                headTag.appendChild(styleLink);
            });
        }

        function insertCssIntoIframe() {
            if (rs('#ifr')) {
                var key = _this.isOverviewPage ? pathName : 'default';
                var pathList = getCssPathListInIframe(key);
                rs('#ifr').insertCSSByPath(pathList);
            }
        }

        function getCssPathListInIframe(key) {
            var resultList = [];
            var confList = _this.cssPathMapInIframe[key];
            if (!confList) return resultList;
            confList.forEach(function(conf) {
                if (conf && conf.condition) resultList.push(conf.path + '?ghrdm={{ build_hash_and_layout_hash }}');
            });

            return resultList;
        }

        function _getConf(dataMap) {
            var key = _this.isNodePage ? 'overview_node_ip' : pathName;
            return dataMap[key];
        }

    }

    /**
     * Sailfish
    */
    function SailfishManager() {
        var sailfish_lang = 'zh-Hans';
        if(global_params.clusterLanguage == 'en') {
            sailfish_lang = 'en-US';
        }

        var dataMap = {
            '/statistics_app/': {
                title: '{% trans "General Analytics" %}',
                navigator:'{% trans "General Analytics" %}',
                url: '/sailfish/?lang=' + sailfish_lang  + '&hideNav=true#/app/botgate/default',
                hasFullScreenButton: true,
                needQueryString: true
            },

            '/api_security/': {
                title: '{% trans "API Security" %}',
                navigator:'{% trans "API Security" %}',
                url: '/sailfish/?lang=' + sailfish_lang  + '&hideNav=true#/app/api/default',
                hasFullScreenButton: true,
                needQueryString: true
            },

            '/overview/': {
                title: '{% trans "Overview" %}',
                navigator: '{% trans "Overview" %}',
                url: '/sailfish/?lang=' + sailfish_lang  + '&hideNav=true#/dashboard/botgate/__overview?full_screen=on'
            },

            'overview_node_ip': {
                title: '{% trans "Overview" %}',
                navigator: '{% trans "Overview" %}|{% trans "Node Status" %}',
                url: '{{ node_info_dashboard_url }}'.replace('&amp;', '&') + '&title=' + '{% trans "Node Status" %}'
            }
        };

        if(global_params.productType == 'Safeplus') {
            if (global_params.protectedLevel=='WE'){
                dataMap['/statistics_app/']['url'] = '/sailfish/?lang='+ sailfish_lang +'#/app/safeplus_waf/default';
            }
            dataMap['/overview/']['url'] = '/sailfish/?lang=' + sailfish_lang + '&hideChartTools=true#/dashboard/safeplus_waf/__overview?g=time%3Dfrom%253A-24h%252Cto%253Anow';
        }

        var _this = this;
        StatisticsBasicManager.call(this, dataMap);
        this.cssPathMapInIframe = {
            '/overview/': [
                { path: '/static/css/statistics/sailfish/overview.css', condition: true },
            ],
        };

        if(global_params.productType == 'Safeplus') {
            this.cssPathMapInIframe = {
                '/overview/': [
                    { path: '/static/css/statistics/sailfish/overview_safeplus.css', condition: true }
                ]
            };
        }

        /** sailfish服务down时，显示提示信息 */
        var ErrorManager = (function() {
            // var _isShowError = false;
            return {
                init: function(isShowError) {
                    showOrHideNodeErrorTips(isShowError);
                    rs('body').bind('logNodeError', handleShowOrHideError); // TODO rs.Event('logNodeError')放在事件发布文件中
                }
            }

            function handleShowOrHideError(evt) {
                {#showOrHideNodeErrorTips(evt.isShowError);#}
                /** 当有角色节点发生变化（增/删）需要刷新带有报表页面（sailfish）的界面 : DAP-17124 */
                location.reload();
            }

            function showOrHideNodeErrorTips(isShowError) {
                rs('#ifrWrapper').style.top = isShowError ? '52px': '0px';
                rs('.errorTips').attr({ 'error': isShowError ? '' : null });

            }
        })();

        /** 缓存URL：刷新页面时，选中页签不变 */
        var CacheUrl = (function() {
            var cacheName = 'sailfish_url' + location.port,
                hashFlag = 'MMWAZXX';

            return {
                listenUrl: listenUrl,
                getCache: function() {
                    if (_win.location.hash.substring(1) == hashFlag) {
                        return _win.sessionStorage.getItem(cacheName);
                    }

                    _win.sessionStorage.removeItem(cacheName);
                    return '';
                },
            };

            function listenUrl() {
                /**
                 * 以下代码涉及场景为【切换报表页签到X页面后】：
                 * 1、通过浏览器地址栏或刷新按钮刷新页面后，报表页签仍应显示为X
                 * 2、切换到非报表菜单 -> 再切回报表菜单，报表应该显示第一个页签内容
                 *
                 *  Warning!!!
                 *  sessionName='sailfish_url'与sailfish代码中App.vue内容强关联
                 *  修改此名字,需要同步修改App.vue下对应的'sailfish_url'
                 *
                 *  与sailfish产生强关联原因见DAP-12534, 待CORE解决后可解除sailfish中的关联性
                 */
                var ifr = _win.document.querySelector('#ifr').iframe;
                ifr.bind('load', function() {
                    // hashchange由sailfish创建抛出
                    ifr.contentWindow.addEventListener('hashchange', function() {
                        _win.sessionStorage.setItem(cacheName, ifr.contentWindow.document.location.href);
                        _win.location.hash = hashFlag;
                    });
                });

                _this.setIframeHeightInOverview(function() {
                    var ifrDoc = ifr.contentWindow.document;
                    var dashboard = ifrDoc.querySelector('.dashboardWrapper');
                    if (dashboard) {
                        var result = dashboard.clientHeight + 20 + 'px';
                        if(global_params.productType == 'Safeplus') {
                            result = dashboard.clientHeight + 35 + 'px';
                        }
                        if (ifr.style.height != result) {
                            ifr.style.height = result;
                        }
                    }
                }, _this.unableConnectionMsg);
            }
        })();

        /** 刷新页面时，选中页签不变 */
        var UrlManager = (function() {
            return {
                getUrl: getUrl
            };

            function getUrl() {
                if (!_this.conf.needQueryString) return _this.baseUrl;

                var url = _this.baseUrl;
                var hideQueryString = getHideQueryString();
                if (hideQueryString) {
                    url += '?' + hideQueryString;
                }

                return CacheUrl.getCache() || url;
            }

            function getHideQueryString() {
                var hideTabList = [
                    { hideTab: 'h=mobile', hideCondition:matchDeployMode('mirror')|| !{{ show_mobile_app|to_json }} },
                    { hideTab: 'h=pd', hideCondition: !global_params.isPDEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=bta', hideCondition: !global_params.hasBTAServer ||!(global_params.isBTAEnabled || global_params.isBTAAppEnabled || global_params.isBTABizEnabled) || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=repu', hideCondition: !global_params.hasRepuServer|| isNGWAFWE() || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=dc', hideCondition: !global_params.isDCEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=adv_waf', hideCondition: !global_params.isAdvancedWafEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=adv_rql', hideCondition: global_params.productType == 'Safeplus' || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=anomaly_analysis', hideCondition: global_params.productType == 'Safeplus' || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=mpp', hideCondition:matchDeployMode('mirror')|| !global_params.isMppEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=static_bot', hideCondition: !isApiProduct(global_params.productType) },
                    { hideTab: 'h=capture_agent_status', hideCondition: !isApiProduct(global_params.productType) || !matchDeployMode('mirror') },
                    { hideTab: 'h=non_http_traffic', hideCondition: !(isApiProduct(global_params.productType) && matchDeployMode('mirror')) },
                    { hideTab: 'h=pii_file', hideCondition: !matchDeployMode('mirror') },
					{ hideTab: 'h=account_sec', hideCondition: !(global_params.isAPIEnabled && global_params.hasAbdServer) || (!global_params.isValidLicense && !global_params.isDebug) },
                    { hideTab: 'h=report_backend', hideCondition: global_params.inK8s }
                ];

                var hideQueryList = [];
                hideTabList.forEach(function(item) {
                    if (item.hideCondition) hideQueryList.push(item.hideTab);
                });

                return hideQueryList.join('&');
            }
        })();
        this.loadPage = function() {
            CacheUrl.listenUrl();
            ErrorManager.init(page.isShowSailfishWarning());
            if ( session_idle_all === false ) {
                keepLoginSession();
            }
            _this.insertCssInPage();
            _this.initIframe();
        }

        this.getUrl = UrlManager.getUrl;

        function keepLoginSession() {
            setInterval(function() {
                service.keepSession2Sailfish();
            }, 210000);
        }
    }

    /**
     * Phoenix
    */
    function PhoenixManager() {
        var dataMap = {
            '/statistics_app/': { title: '{% trans "Statistics" %}', navigator:'{% trans "Statistics" %}',
                url: '/base_report/phoenix/zh-CN/', hasFullScreenButton: true },

            '/api_security/': { title: '{% trans "API Security" %}', navigator:'{% trans "API Security" %}',
                url: '/base_report/phoenix/zh-CN/app/api_phoenix/api_discover_overview', hasFullScreenButton: true },

            '/overview/': { title: '{% trans "Overview" %}', navigator: '{% trans "Overview" %}',
                url: '/base_report/phoenix/zh-CN/app/phoenix/webconsole?hideTopMenu=true&hideEdit=true&hideTitle=true' },

            'overview_node_ip': { title: '{% trans "Overview" %}', navigator: '{% trans "Overview" %}|{% trans "Node Status" %}',
                url: '{{ node_info_dashboard_url }}' }
        };


        var _this = this;
        StatisticsBasicManager.call(this, dataMap);
        this.cssPathMapInIframe = {
            'default': [
                { path: '/static/css/statistics/phoenix/statistics.css', condition: true },
                { path: '/static/css/statistics/phoenix/hideMenuMobileSDK.css', condition: !{{ show_mobile_app|to_json }} },
                { path: '/static/css/statistics/phoenix/hideMenuUBBV2.css', condition: !global_params.isPDEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                { path: '/static/css/statistics/phoenix/hideMenuBta.css', condition: !global_params.hasBTAServer ||!(global_params.isBTAEnabled || global_params.isBTAAppEnabled || global_params.isBTABizEnabled) || (!global_params.isValidLicense && !global_params.isDebug) },
                { path: '/static/css/statistics/phoenix/hideMenuRepu.css', condition: !global_params.hasRepuServer || (!global_params.isValidLicense && !global_params.isDebug) },
                { path: '/static/css/statistics/phoenix/hideMenuCaptcha.css', condition: !global_params.isDCEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                { path: '/static/css/statistics/phoenix/hideWAF.css', condition: !global_params.isAdvancedWafEnabled || (!global_params.isValidLicense && !global_params.isDebug) },
                { path: '/static/css/statistics/phoenix/hideWechat.css', condition: !global_params.isMppEnabled || (!global_params.isValidLicense && !global_params.isDebug) }
            ],

            '/overview/': [
                { path: '/static/css/statistics/phoenix/overview.css', condition: true }
            ]
        };

        this.loadPage = function() {
            _this.insertCssInPage();
            _this.initIframe();

            _this.setIframeHeightInOverview(function(ifr) {
                var dashboardBody = ifr.contentWindow.document.querySelector('.dashboard-body');
                if (dashboardBody) {
                    var result = ifr.contentWindow.document.body.clientHeight + 'px';
                    if (ifr.style.height != result) {
                        ifr.style.height = result;
                    }
                }
            }, _this.unableConnectionMsg);
        };
    }



    /**
     * 全屏按钮
    */
    function FullScreenButton() {
        var _this = this.as('ToggleButton', { state: false })
            .bind('toggle', handleOnToggleFullScreen);

        function handleOnToggleFullScreen(event){
            var bToggle = event.target.state;
            event.target.attr({
                'fullscreen': bToggle ? 'true' : null,
                'title': bToggle ? "{% trans 'Exit Full Screen' %}" : "{% trans 'Full-Screen Display' %}"
            });

            fullScreen(bToggle);
        }

        function fullScreen(b){
            var container = document.body.child('.panel');
            var className = 's-fullscreen';
            b ? container.addClass(className) : container.removeClass(className);
        }
    }
</script>
{% endblock %}
