<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
        "http://www.w3.org/TR/html4/loose.dtd">
<html xmlns:rs="layout">

<head>
    {% load i18n %}
    {% load nav_tags %}
    {% get_current_language as LANGUAGE_CODE %}
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>{% trans 'Wizard' %}</title>
    <link rel="icon" href="/static/img/favicon.ico?ghrdm={{ build_hash_and_layout_hash }}" mce_href="/static/img/favicon.ico" type="image/x-icon"/>
    <link rel="stylesheet" href="/static/css/FontAwesome6/css/all.css?ghrdm={{ build_hash_and_layout_hash }}"/>
    <link rel="stylesheet" href="/static/css/FontAwesome6/css/v5-font-face.css?ghrdm={{ build_hash_and_layout_hash }}">
    <link rel="stylesheet" href="/static/css/RSFontAwesome/style.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link rel="stylesheet" href="/static/css/theme.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link rel="stylesheet" href="/static/css/layout_theme.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link href="/static/css/rs/rs.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
    <link href="/static/css/common.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
    <link href="/static/css/layout.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
    <link href="/static/css/wizard.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
    <link href="/static/css/layout_wizard.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
    <script type="text/javascript" language="JavaScript" src="/static/js/utils.js"></script>
    <script language="JavaScript" src="/static/js/const.js"></script>
    <script language="JavaScript" src="/static/js/browserAdapter.js"></script>
    <script type="text/javascript" src="{% url 'javascript-catalog' %}?language={{lang_from_url_args}}"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/rs.js" rsTag="true" ready="initPage()"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/Validation.js"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/sha1.js"></script>
    <script type="text/javascript" language="JavaScript">
        'use strict';
        rs.plugin({
            Wizard:'/static/js/rs/ui.Wizard.js',
            Switch: '/static/js/rs/ui.Switch.js',
            ToolTip: '/static/js/rs/ui.ToolTip.js',
            StepProgress: '/static/js/rs/ui.StepProgress.js'
        });
    </script>
</head>

<body>

    <rs:Data id="__preJoinCluster__"
             src="/wizard/pre_cluster_join.js"
             type="json"
             onAlways=""
             onError="handleOnError(event)"></rs:Data>

    <rs:Data id="__finalSubmit__"
             src="/wizard/ajax_join_or_create_cluster"
             type="json"
             onAlways=""
             onError="handleOnError(event)"></rs:Data>

    <rs:Data id="__finished__"
             src="/wizard/finished.js"
             type="json"
             onAlways=""
             onError="handleOnError(event)"></rs:Data>

    <rs:Data id="__validateIPAndMask__"
             src="/wizard/ajax_check_adapter_cfg.js"
             type="json"
             onAlways=""
             onError="handleOnError(event)"></rs:Data>

    <rs:Data id="__checkNicPort__"
             src="/wizard/blinking_nic_port/"
             type="json"
             method="GET"
             onError="BondNicManager.checkNicError(event)"></rs:Data>


    <rs:Template id="__defaultNavigation__">
        <div class="bottomPanel">
            <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
            <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
        </div>
    </rs:Template>

    <rs:Template id="__passwordIcon__">
        <i target="tipIcon" class="ic-info"
           onmouseover="PageEvt.tooltip.show(event, rs('#__passwordTooltip__'))" onmouseout="PageEvt.tooltip.hide(event, rs('#__passwordTooltip__'))"></i>
    </rs:Template>

    <rs:Template id="__TABLE_EXTERNALE_IPV6__">
        <table name="table-ipv6" rs:visible="mForm.external_ipVersion!='ipv4'">
            <tr>
                <td><label>{% trans 'IPv6 Address' %}</label></td>
                <td><textarea name="external_ipv6"></textarea></td>
            </tr>
            <tr>
                <td><label>{% trans 'Prefix Length' %}</label></td>
                <td><input type="text" name="external_prefix" /></td>
            </tr>
            <tr v:if="!RouteSettingManager.canSettingRoute()">
                <td><label>{% trans 'Gateway' %}</label></td>
                <td><textarea name="external_ipv6_gateway"></textarea></td>
            </tr>
        </table>
    </rs:Template>

    <rs:Template id="__TABLE_INTERNAL_IPV6__">
        <table name="table-ipv6" rs:visible="mForm.internal_ipVersion!='ipv4'">
            <tr>
                <td>
                    <label>{% trans 'IPv6 Address' %}</label>
                </td>
                <td><textarea type="text" name="internal_ipv6"></textarea></td>
            </tr>
            <tr>
                <td><label>{% trans 'Prefix Length' %}</label></td>
                <td><input type="text" name="internal_prefix" /></td>
            </tr>
        </table>
    </rs:Template>

    <rs:Template id="__TABLE_HOSTSTANDBY__">
        <div rs:visible="pageInstance.isHA() && mForm.hotStandbyMode=='hostStandby' && mForm.cluster_choice=='cluster_create'">
            <table name="table-virtual-vrid" innersection>
                <tr>
                    <td><label>{% trans 'VRID' %}</label></td>
                    <td><input type="text" rsid="hostStandbyRouterId" name="external_virtual_router_id"/></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td class="explain">{% trans "The VRID must be unique." %}</td>
                </tr>
            </table>

            <table name="table-virtual-vip" rs:visible=" mForm.external_ipVersion !='ipv6'">
                <tr>
                    <td><label>{% trans 'Virtual IP Address' %}</label></td>
                    <td><input type="text" rsid="hostStandbyVirtualIP" name="external_virtual_ip" /></td>
                </tr>
                <tr>
                    <td><label class="ipv4-control">{% trans 'Virtual Netmask' %}</label></td>
                    <td><input type="text" rsid="hostStandbyVirtualNetmask" name="external_virtual_netmask" /></td>
                </tr>
            </table>

            <table name="table-virtual-vip6" rs:visible=" mForm.external_ipVersion !='ipv4'">
                <tr>
                    <td><label> {% trans 'Virtual IPv6 Address' %}</label></td>
                    <td><textarea type="text" rsid="hostStandbyVirtualIPv6" name="external_virtual_ipv6"></textarea></td>
                </tr>
                <tr>
                    <td><label> {% trans 'Virtual IP prefix length' %}</label></td>
                    <td><input type="text" rsid="hostStandbyVirtualIPv6Prefix" name="external_virtual_ipv6_prefix" /></td>
                </tr>
            </table>
        </div>
    </rs:Template>

    <rs:Template id="__TABLE_MUTUALSTANDBY__">
        <div rs:visible="pageInstance.isHAInReverse() && mForm.hotStandbyMode=='mutualStandby' && mForm.cluster_choice=='cluster_create'">
            <table name="table-virtual-vrid" innersection>
                <tr>
                    <td><label> {% trans 'VRID' %}1</label></td>
                    <td><input type="text" rsid="mutualStandbyRouterId" name="external_virtual_router_id"/></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td class="explain">{% trans "The VRID must be unique." %}</td>
                </tr>
            </table>

            <table name="table-virtual-vip" rs:visible="mForm.external_ipVersion !='ipv6'">
                <tr>
                    <td><label> {% trans 'Virtual IP Address' %}1</label></td>
                    <td><input type="text" rsid="mutualStandbyVirtualIP" name="external_virtual_ip" /></td>
                </tr>
                <tr>
                    <td> <label class="ipv4-control"> {% trans 'Virtual Netmask' %}1</label></td>
                    <td><input type="text" rsid="mutualStandbyVirtualNetmask" name="external_virtual_netmask" /></td>
                </tr>
            </table>

            <table name="table-virtual-vip6" rs:visible="mForm.external_ipVersion !='ipv4'">
                <tr>
                    <td><label> {% trans 'Virtual IPv6 Address' %}1</label></td>
                    <td><textarea type="text" rsid="mutualStandbyVirtualIPv6" name="external_virtual_ipv6"></textarea></td>
                </tr>
                <tr>
                    <td><label> {% trans 'Virtual IP prefix length' %}1</label></td>
                    <td><input type="text" rsid="mutualStandbyVirtualIPv6Prefix" name="external_virtual_ipv6_prefix" /></td>
                </tr>
            </table>
        </div>

        <div rs:visible="pageInstance.isHAInReverse() && mForm.hotStandbyMode=='mutualStandby' && mForm.cluster_choice=='cluster_create'">

            <table name="table-virtual2-vrid" innersection>
                <tr>
                    <td><label> {% trans 'VRID' %}2</label></td>
                    <td><input type="text" name="external_virtual2_router_id"/></td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td class="explain">{% trans "The VRID must be unique." %}</td>
                </tr>
            </table>
            <table name="table-virtual2-vip" rs:visible="mForm.external_ipVersion !='ipv6'">
                <tr>
                    <td><label>{% trans 'Virtual IP Address' %}2</label></td>
                    <td><input type="text" name="external_virtual2_ip" /></td>
                </tr>
                <tr>
                    <td><label>{% trans 'Virtual Netmask' %}2</label></td>
                    <td><input type="text" name="external_virtual2_netmask" /></td>
                </tr>
            </table>

            <table name="table-virtual2-vip6" rs:visible="mForm.external_ipVersion !='ipv4'">
                <tr>
                    <td><label>{% trans 'Virtual IPv6 Address' %}2</label></td>
                    <td><textarea name="external_virtual2_ipv6"></textarea></td>
                </tr>
                <tr>
                    <td><label>{% trans 'Virtual IP prefix length' %}2</label></td>
                    <td><input type="text" name="external_virtual2_ipv6_prefix" /></td>
                </tr>
            </table>
        </div>
    </rs:Template>

    <rs:Template id="__PORT_BOND_TEMP__">
        <table name="nicBondTable" innersection
            v:show="BondNicManager.canSettingBondNic()">
            <tr>
                <td><label>{% trans 'Port Binding' %}</label></td>
                <td>
                    <div id="nicBondLabel" class="inlineBlock">
                        <rs:Checkbox name="is_nic_bond" id="nicBondCheckbox" rs-lazy
                            onmouseover="BondNicManager.showNicBondSwitchTooltip(event, BondNicManager.TOOLTIP_MSG.NO_NIC)"
                            onmouseout="BondNicManager.hideTooltip()"></rs:Checkbox>
                        </div>
                </td>
            </tr>
            <tr v:show="mForm.is_nic_bond">
                <td><label>{% trans 'NIC' %}1</label></td>
                <td>
                    <rs:Select name="bond_nic_1" id="bond_nic_1" rs-lazy></rs:Select>
                    <i for="bond_nic_1" class="nicLight far"
                        onclick="BondNicManager.checkNic(event, rs('#bond_nic_1'))"
                        onmouseover="BondNicManager.showLightTooltip(event)"
                        onmouseout="BondNicManager.hideTooltip()"></i>
                </td>
            </tr>
            <tr v:show="mForm.is_nic_bond">
                <td><label>{% trans 'NIC' %}2</label></td>
                <td>
                    <rs:Select name="bond_nic_2" id="bond_nic_2" rs-lazy></rs:Select>
                    <i for="bond_nic_2" class="nicLight far"
                        onclick="BondNicManager.checkNic(event, rs('#bond_nic_2'))"
                        onmouseover="BondNicManager.showLightTooltip(event)"
                        onmouseout="BondNicManager.hideTooltip()"></i>
                </td>
            </tr>
            <tr v:show="mForm.is_nic_bond">
                <td><label>{% trans "Binding Type" %}</label></td>
                <td>
                    <rs:Select name="nic_bond_mode" rs-lazy>
                        <div rs-option value="0">{% trans 'balance-rr' %}</div>
                        <div rs-option value="1">{% trans 'active-backup' %}</div>
                        <div rs-option value="4">{% trans '802.3ad' %}</div>
                    </rs:Select>
                    <i class="ic-info" onmouseover="rs('#__nicBondModeTip__').show(event.pageX, event.pageY)" onmouseout="rs('#__nicBondModeTip__').hide()"></i>
                </td>
            </tr>
            <tr>
                <td>&nbsp;</td>
                <td><div class="errorMsg" rs-for="bond_nic_1, bond_nic_2"></div></div></td>
            </tr>
        </table>
    </rs:Template>

    <div id="wizardWapper" start="false">
        <div id="__WIZARD_CONTENT_WRAPPER__">
            <div class="head">
                <div id="logo">
                    <div rs:visible="useNonStandardStyleInPage">
                        {% if is_prod_pkg == 0 %}<i class="ic-product-flag ic-dev"></i>{% endif %}
                        {% if is_build_debug == 1 %}<i class="ic-product-flag ic-debug"></i>{% endif %}
                    </div>
                </div>

                <div class="titleInfo">
                    <p name="title">{% trans 'ForceShield Dynamic Application Protection' %}{{plt_info}}{% if not is_prod_pkg %} (dev){% endif %}{% if is_build_debug %} (debug){% endif %}</p>
                    <p name="compilerNumber">{% trans 'Build:' %} {{ build_info }}</p>
                </div>
            </div>

            <div class="wrapper">
                <div class="wizardHead" rs:visible="wizard.router.currentAction!='start' && useNonStandardStyleInPage">
                    {% trans 'Wizard' %}
                    <div id="__NUMBER_STEP__" class="floatRight"></div>
                </div>

                <rs:StepProgress id="stepProgress"></rs:StepProgress>

                <rs:Form id="__FORM__" modelName="wizardModel"
                         onValueError="PageEvt.handleOnErrorMessage(event)"
                         onInput="PageEvt.clearErrorMsg(event)"
                         onModelRestore="PageEvt.handleOnModelRestore(event)">
                <!-- start conf -->
                    <rs:Wizard id="__WIZARD__" steps="#steps"
                               onViewChange="PageEvt.handleOnViewChange(event)"
                               onErrorMessage="PageEvt.handleOnErrorMessage(event)"
                               onValidate="PageEvt.handleOnValidateActiveViewFields(event)">

                        <div name="start">
                            <div v:if="isSupportChooseLanguage">
                                <div class="title">
                                    请选择系统使用的语言<br/>
                                    Select a Language
                                </div>
                                <div name="content">
                                    <div class="chooseLanguagePane">
                                        <rs:Label class="languageItem zh" v:active="mForm.language === 'zh'" v:changing="PageEvt.isLangChanging">
                                            <rs:Radio name="language" value="zh" onclick="PageEvt.handleChangeLanguage(event)"></rs:Radio>中文
                                        </rs:Label>
                                        <rs:Label class="languageItem en" v:active="mForm.language === 'en'" v:changing="PageEvt.isLangChanging">
                                            <rs:Radio name="language" value="en" onclick="PageEvt.handleChangeLanguage(event)"></rs:Radio>English
                                        </rs:Label>
                                    </div>
                                </div>
                                <div class="bottomPanel">
                                    <button type="button" name="startBtn" class="rect-button-48 bottomCenterBtn" onclick="PageEvt.handleOnNext(event)">{% trans "Enter Wizard" %}</button>
                                </div>
                            </div>
                            <div v:if="!isSupportChooseLanguage">
                                <div name="content">
                                    {% trans 'Please go through a wizard first before implementing protections for websites.' %}
                                    <br/>
                                    <button type="button" name="startBtn" class="rect-button-48" onclick="PageEvt.handleOnNext(event)" style="margin-top: 120px;">{% trans 'Enter Wizard' %}</button>
                                </div>
                            </div>
                        </div>

                        <rs:Template id="standardSelectCategory"></rs:Template>
                        <div name="selectCategory">
                            <div v:if="!useNonStandardStyleInPage">
                                <div class="title">{% trans 'Deployment Model' %}</div>
                                <div class="contentPanel">
									<rs:List id="deployCategory" v:data="deployModelConfigList">
										<li
											v:name="rowData.name"
											v:selected="rowData.name === mForm.deployCategory"
											v:if="!rowData.invisible"
											onclick="PageEvt.handleSelectDeployCategory(event)"
										>
											<i v:class="rowData.icon"></i>
											<span v:text="rowData.label"></span>
											<i class="fa fa-caret-up"></i>
										</li>
									</rs:List>

									<div id="deployModeItemsPane">
										<rs:List id="deployModeItems" v:data="PageEvt.getDeployModeItemsConfig()">
											<div
												class="modeItem"
												v:value="rowData.value"
												v:selected="rowData.value === mForm.modelCategory"
												v:if="!rowData.invisible"
                                                v:xerror="rowData.markError ? '' : null"
												onclick="PageEvt.handleClickModelCategoryItem(event)"
											>
												<div class="content">
													<div class="title">
														<rs:Label>
															<rs:Radio
																name="modelCategory"
																v:value="rowData.value"
																v:checked="mForm.modelCategory === rowData.value"
															>
																<i class="ic-check"></i>
															</rs:Radio>
															<span v:text="rowData.label"></span>
														</rs:Label>
													</div>
													<div class="desc" v:text="rowData.description()"></div>
												</div>
											</div>
										</rs:List>
									</div>
                                </div>
                            </div>

                            <div v:if="useNonStandardStyleInPage">
                                <div class="title">{% trans 'Deployment Model' %}</div>

                                <rs:List name="selectCategoryInFs" v:data="PageEvt.getAllDeployModelItems()">
                                    <div
                                        class="fsSelectModel"
                                        v:value="rowData.value"
                                        v:selected="rowData.value === mForm.modelCategory"
                                        v:if="!rowData.invisible"
                                        v:xerror="rowData.markError ? '' : null"
                                        onclick="PageEvt.handleClickModelCategoryItem(event)"
                                    >
                                        <div class="content">
                                            <div class="title">
                                                <rs:Label>
                                                    <rs:Radio
                                                        name="modelCategory"
                                                        v:value="rowData.value"
                                                        v:checked="mForm.modelCategory === rowData.value"
                                                    >
                                                        <i class="ic-check"></i>
                                                    </rs:Radio>
                                                    <span v:text="rowData.label"></span>
                                                </rs:Label>
                                            </div>
                                            <div class="desc" v:text="rowData.description()"></div>
                                        </div>
                                    </div>
                                </rs:List>
                            </div>

                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
                            </div>
                        </div>

                        <div name="singleAccess" selected="ipv4">
                            <div class="title">{% trans 'Network Adapter Settings' %}</div>
                            <div class="des">
                                <div><span>{% trans 'Management Network Adapter' %}</span><span class="rectangle">eth0</span></div>
                                <div rs:visible="mForm.modelCategory != 'transparent'">{% trans 'Connecting protection system WebConsole (IPv4 Only), Internet/load balancer and internal servers simultaneously.' %}</div>
                            </div>
                            <div class="contentPanel">
                                <table name="table-ip">
                                    <tr rs:visible="mForm.modelCategory != 'transparent' && mForm.modelCategory != 'mirror'" style="display: table-row">
                                        <td><label>{% trans 'IP Version' %}</label></td>
                                        <td>
                                            <rs:Select name="external_ipVersion" rs:disabled="mForm.cluster_choice == 'cluster_join'">
                                                <div rs-option value="ipv4">IPv4</div>
                                                <div rs-option value="ipv46">IPv4&IPv6</div>
                                            </rs:Select>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <label rs:visible="mForm.external_ipVersion=='ipv4'">{% trans 'IP Address' %}</label>
                                            <label rs:visible="mForm.external_ipVersion=='ipv46'">{% trans 'IPv4 Address' %}</label>
                                        </td>
                                        <td><input type="text" name="external_ip"/></td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label class="ipv4-control">{% trans 'Netmask' %}</label>
                                        </td>
                                        <td><input type="text" name="external_netmask" /></td>
                                    </tr>
                                    <tr>
                                        <td><label>{% trans 'Gateway' %}</label></td>
                                        <td><input type="text" name="default_gateway"/></td>
                                    </tr>

                                    <tr name='bond_mode_row' rs:visible="mForm.modelCategory == 'transparent'">
                                        <td><label>{% trans 'Port Binding' %}</label></td>
                                        <td>
                                            <rs:Select name="bond_mode" rs:disabled="!(hasEth3 && hasEth4)">
                                                <div rs-option value="-1">{% trans 'No Bond' %}</div>
                                                <div rs-option value="0">{% trans 'balance-rr' %}</div>
                                                <div rs-option value="4">{% trans '802.3ad' %}</div>
                                            </rs:Select>
                                            <i class="ic-info" onmouseover="rs('#__bondModeTip__').show(event.pageX, event.pageY)" onmouseout="rs('#__bondModeTip__').hide()"></i>
                                        </td>
                                    </tr>
                                </table>

                                <rs:CloneNode src="#__TABLE_EXTERNALE_IPV6__"></rs:CloneNode>
                                <rs:CloneNode src="#__TABLE_HOSTSTANDBY__"></rs:CloneNode>
                                <rs:CloneNode src="#__TABLE_MUTUALSTANDBY__"></rs:CloneNode>

                                <table>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td><div class="errorMsg"></div></td>
                                    </tr>
                                </table>

                                <rs:CloneNode src="#__PORT_BOND_TEMP__"></rs:CloneNode>
                            </div>

                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button nextOrApplyBtn" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
                            </div>
                        </div>

                        <div name="singleAccountSetting">
                            <div class="title">{% trans 'Admin Account' %}</div>
                            <div class="contentPanel">
                                <table>
                                    <tr>
                                        <td class="td-text-right"><label>{% trans 'Username ' %}</label></td>
                                        <td><input type="text" name="username" disabled /></td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td class="td-text-right"><label>{% trans 'Password ' %}</label></td>
                                        <td>
                                            <input type="password" name="cluster_password" onchange="trimValue(this)" />
                                            <rs:CloneNode src="#__passwordIcon__"></rs:CloneNode>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="td-text-right"><label>{% trans 'Confirm Password' %}</label></td>
                                        <td><input type="password" name="cluster_password_confirm" onchange="trimValue(this)" /></td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td><div class="errorMsg"></div></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Apply' %}</button>
                            </div>
                        </div>

                        <div name="done">
                            <div name="doneFinish">
                                <div class="title">{% trans 'End of Wizard' %}</div>
                                <div class="des">
                                    {% trans "For a better experience with the Webconsole, you can install the SSL certificate provided below on your browsers." %}
                                </div>
                                <div name="content">
                                    <a id="downloadCert" class="finishPanel">
                                        <i class="ic-download" style="display: none;"></i>
                                        <img src="/static/img/wizard/img-certification.png" />
                                        <span id="downloadLink">{% trans 'Download SSL Certification' %}</span>
                                    </a>

                                </div>
                                <div class="bottomPanel">
                                    <a id="finishBtn" type="button" class="rect-button-48 bottomCenterBtn">{% trans 'Go To WebConsole' %}</a>
                                </div>
                            </div>

                            <div name="doneError">
                                <div class="title">{% trans 'Error applying configuration information' %}</div>
                                <div class="contentPanel">
                                    <div class="errorPanel">
                                        <i class="ic-cross-r"></i>
                                        <div name="error-reason"></div>
                                    </div>
                                </div>
                                <div class="bottomPanel">
                                    <button type="button" class="rect-button-48 bottomCenterBtn" onclick="PageEvt.handleOnPrev(event)">{% trans 'Back' %}</button>
                                </div>
                            </div>
                        </div>

                        <div name="groupAccess">
                            <div class="title" v:show="!pageInstance.isHA()">{% trans 'Create or Join a Cluster' %}</div>
                            <div class="title" v:show="pageInstance.isHA()">{% trans 'Create a Primary or Standby Node' %}</div>
                            <div class="contentPanel">
                                <table>
                                    <tr>
                                        <td class="des td-5 td-left">
                                            <div class="content-des"></div>
                                        </td>
                                        <td class="border-left">
                                            <div class="td-content">
                                                <li class="marginBottom">
                                                    <rs:Label name="cluster_create">
                                                        <rs:Radio name="cluster_choice" value="cluster_create" onchange="PageEvt.updateGroupFormVisible(event)"></rs:Radio>
                                                        <span v:show="!pageInstance.isHA()">{% trans 'Create a Cluster' %}</span>
                                                        <span v:show="pageInstance.isHA()">{% trans 'Create a Primary Node' %}</span>
                                                    </rs:Label>
                                                    {% if not can_install_clickhouse %}
                                                    <span name="Sse4Msg" class="rectangle rectangle-red" v:show="pageInstance.isHA()">{% trans 'SSE 4.2 instruction set is not supported' %}</span>
                                                    {% endif %}

                                                    <div class="innerContent">
                                                        <div class="line-des" v:show="!pageInstance.isHA()">{% trans 'Create a new cluster which more nodes could join later.' %}</div>
                                                        <div class="line-des" v:show="pageInstance.isHA()">{% trans 'A primary node must be created first.' %}</div>
                                                        <table name="createGroupForm" class="marginTop20">
                                                            <tr v:show="pageInstance.isHAInReverse()">
                                                                <td class="td-text-right"><label> {% trans 'Deployment-mode' %}</label></td>
                                                                <td>
                                                                    <rs:Label rsid="hostStandbyMode"><rs:Radio name="hotStandbyMode" value="hostStandby"></rs:Radio>{% trans 'Master-Backup mode' %}</rs:Label>
                                                                    <rs:Label rsid="mutualStandbyMode"><rs:Radio name="hotStandbyMode"  value="mutualStandby"></rs:Radio> {% trans 'Master-Master mode' %}</rs:Label>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="td-text-right"><label>{% trans 'Admin Username' %}</label></td>
                                                                <td><input name="username" type="text" disabled/></td>
                                                            </tr>
                                                            <tr>
                                                                <td class="td-text-right">
                                                                    <label v:show="!pageInstance.isHA()">{% trans 'Cluster Password' %}</label>
                                                                    <label v:show="pageInstance.isHA()">{% trans 'Password ' %}</label></td>
                                                                <td>
                                                                    <input name="cluster_password" type="password" onchange="trimValue(this)"/>
                                                                    <rs:CloneNode src="#__passwordIcon__"></rs:CloneNode>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="td-text-right"><label>{% trans 'Confirm Password' %}</label></td>
                                                                <td><input name="cluster_password_confirm" type="password" onchange="trimValue(this)"/></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </li>
                                                <div name="createGroupMessage" class="errorMsg"></div>
                                                <li>
                                                    <rs:Label name="cluster_join">
                                                        <rs:Radio name="cluster_choice" value="cluster_join" onchange="PageEvt.updateGroupFormVisible(event)"></rs:Radio>
                                                        <span v:show="!pageInstance.isHA()">{% trans 'Join an Existing Cluster' %}</span>
                                                        <span v:show="pageInstance.isHA()">{% trans 'Create a Standby Node' %}</span>
                                                    </rs:Label>

                                                    <div class="innerContent">
                                                        <div class="line-des" v:show="!pageInstance.isHA()">{% trans 'IPv4 address of management network adapter of any node in the cluster is required.' %}</div>
                                                        <div class="line-des" v:show="pageInstance.isHA()">{% trans 'To create a standby node, the IP of management network adapter, as well as the admin password of the primary node are required.' %}</div>
                                                        <table name="joinGroupForm" class="marginTop20">
                                                            <tr>
                                                                <td class="td-text-right">
                                                                    <label v:show="!pageInstance.isHA()">{% trans 'Cluster IP' %}</label>
                                                                    <label v:show="pageInstance.isHA()">{% trans 'Primary Node IP' %}</label>
                                                                </td>
                                                                <td><input name="cluster_ip" type="text" placeholder="{% trans 'IPv4 of management network adapter' %}"/></td>
                                                            </tr>
                                                            <tr>
                                                                <td class="td-text-right"><label>{% trans 'Password ' %}</label></td>
                                                                <td><input name="cluster_password" type="password" onchange="trimValue(this)"/></td>
                                                            </tr>
                                                        </table>
                                                    </div>
                                                </li>
                                                <div name="joinGroupMessage" class="errorMsg"></div>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <div class="bottomPanel">
                                    <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                    <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
                                </div>
                            </div>
                        </div>

                        <div name="makeRole">
                            <div class="title">{% trans 'Node Role in Cluster' %}</div>
                            <div class="des" name="clusterInfo">
                                <span class="rectangle rectangle-blue">{% trans 'There are <span name="cluster_node_count"></span> nodes in current cluster, including <span name="cluster_manage_count"></span> Master Node(s).' %}</span>
                            </div>
                            <div class="contentPanel">
                                <table>
                                    <tr>
                                        <td class="td-5 td-left">
                                            <div class="content-des">
                                                {% trans 'There should be a least one Master Node in a cluster. An odd number of Master Nodes, e.g. 3 or 5 is highly recommended to ensure cluster delivers service with stability.' %}<br/>
                                            </div>
                                        </td>
                                        <td class="border-left">
                                            <div class="td-content">
                                                <li class="marginBottom">
                                                    <rs:Label rsid="masterNode"><rs:Radio name="masterserver_module" value="true"></rs:Radio>{% trans 'Master Node' %}</rs:Label>
                                                    <div class="line-des innerContent">{% trans 'Responsible for cluster management and synchronization among nodes.' %}</div>
                                                </li>
                                                <li>
                                                    <rs:Label rsid="workerNode"><rs:Radio name="masterserver_module" value="false"></rs:Radio>{% trans 'Worker Node' %}</rs:Label>
                                                    <div class="line-des innerContent">{% trans 'The nodes that are not responsible for cluster management and synchronization.' %}</div>
                                                </li>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                                <div class="bottomPanel">
                                    <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                    <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
                                </div>
                            </div>
                        </div>

                        <div name="chooseComponent">
                            <span class="title">{% trans 'Node Functions' %}</span>
                            <div class="contentPanel">
                                <table>
                                    <tr>
                                        <td>
                                            <div class="row-des">
                                                <label rs:visible="mForm.modelCategory != 'mirror'" style="display: inline-block">{% trans 'Dynamic Application Protection' %}</label>
                                                <label rs:visible="mForm.modelCategory == 'mirror'" style="display: inline-block">{% trans 'Flow collection' %}</label>
                                                <span name="proxyModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="protect_node_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                            </div>
                                            <div class="line-des" rs:visible="mForm.modelCategory != 'mirror'">{% trans "Providing protections for target websites" %}</div>
                                            <div class="line-des" rs:visible="mForm.modelCategory == 'mirror'">{% trans "Collect and analyze the flow through the web server." %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="proxy_module" value="proxy_module" ></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="proxy_module" value="proxy_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans "Log Analysis S" %}</label>
                                                <span name="sailfishModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="sailfish_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                                {% if not can_install_clickhouse %}
                                                <span name="Sse4Msg" class="rectangle rectangle-red">{% trans 'SSE 4.2 instruction set is not supported' %}</span>
                                                {% endif %}
                                            </div>
                                            <div class="line-des">{% trans 'Analyzing logs and providing statistical reports (Sailfish).' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="sailfish_module" value="sailfish_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="sailfish_module" value="sailfish_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans 'Log Archiving' %}</label>
                                                <span name="logArchiveModuleDes" class="alert-msg rectangle rectangle-yellow">{% trans 'Already exist' %}</span>
                                            </div>
                                            <div class="line-des">{% trans 'Collecting and archiving logs from servers of Dynamic Application Protection. Only 1 node of Log Archiving is allowed in a cluster.' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="log_archive_server_module" value="log_archive_server_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="log_archive_server_module" value="log_archive_server_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr rs:visible="hasBtaMode" class="tableRow">
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans 'BTA' %}</label>
                                                <span name="btaModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="bta_node_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                             </div>
                                            <div class="line-des">{% trans 'To collect, analyze, detect, score, and identify automated attack behaviors on business data by using intelligent models. Complies with the OWASP automated attack specification.' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="bta_module" value="bta_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="bta_module" value="bta_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr rs:visible="hasRepuMode" class="tableRow">
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans 'Threat Intelligence Service' %}</label>
                                                <span name="repuModuleDes" class="alert-msg rectangle rectangle-yellow">{% trans 'Already exist' %}</span>
                                            </div>
                                            <div class="line-des">{% trans 'Provide threat intelligence related services, including intelligence management, intelligence generation, intelligence synchronization, etc. Only one threat intelligence service node can be configured in a cluster.' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="repu_module" value="repu_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="repu_module" value="repu_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr rs:visible="{% visibleLayout 'wizard_abd_module' %}" class="tableRow">
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans 'API Monitor' %}</label>
                                                <span name="abdModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="abd_node_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                             </div>
                                            <div class="line-des">{% trans 'API awareness, discovery, monitoring and protection.' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="abd_module" value="abd_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="abd_module" value="abd_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans 'AI intelligent detection' %}</label>
                                                <span name="aiModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="ai_node_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                            </div>
                                            <div class="line-des">{% trans 'Provide common basic functions and interfaces for AI applications' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="ai_module" value="ai_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="ai_module" value="ai_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr class="tableRow">
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans 'LLM Service' %}</label>
                                                <span name="llmModuleDes" class="alert-msg rectangle rectangle-yellow"></span>
                                            </div>
                                            <div class="line-des">{% trans 'Providing basic functions and interfaces for LLM applications. Only one LLM service node is allowed in a cluster.' %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="llm_module" value="llm_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="llm_module" value="llm_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans "Flow self-learning" %}</label>
                                                <span name="fslModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="fsl_node_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                                <span rs:visible="!mForm.proxy_module && mForm.modelCategory != 'mirror'" name="fslModuleTips" class="alert-msg rectangle rectangle-yellow" style="display: inline-block">{% trans 'To enable this function,Dynamic Application Protection must be turned on.' %}</span>
                                                <span rs:visible="!mForm.proxy_module && mForm.modelCategory == 'mirror'" name="fslModuleTips" class="alert-msg rectangle rectangle-yellow" style="display: inline-block">{% trans 'To enable this function,Flow collection must be turned on.' %}</span>
                                            </div>
                                            <div class="line-des">{% trans "Establish a normal business flow model by analyzing user flow" %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="fsl_module" value="fsl_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="fsl_module" value="fsl_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <div class="row-des">
                                                <label>{% trans "API Gateway" %}</label>
                                                <span name="apiGatewayModuleDes" class="alert-msg rectangle rectangle-blue">{% trans '<span name="api_gateway_node_count"></span> node(s) of this function exist in current cluster.' %}</span>
                                            </div>
                                            <div class="line-des">{% trans "Provide users with RAS service interface" %}</div>
                                        </td>
                                        <td>
                                            <rs:Label v:if="useNonStandardStyleInPage"><rs:Checkbox name="api_gateway_module" value="api_gateway_module"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                                            <rs:Switch name="api_gateway_module" value="api_gateway_module" v:if="!useNonStandardStyleInPage"></rs:Switch>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td colspan="2" class="errorMsg"></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button nextOrApplyBtn" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
                            </div>
                        </div>

                        <div name="accessType">
                            <div class="title">{% trans 'Network Adapters' %}</div>
                            <div class="contentPanel">
                                <table>
                                    <tr>
                                        <td class="td-5 td-left">
                                            <div class="content-des">
                                                {% trans 'There are three roles that a network adapter could play:' %}<br/>
                                                <span class="text-style-1">{% trans 'External Network Adapter' %}</span>：{% trans 'connecting Internet.' %}<br/>
                                                <span class="text-style-1">{% trans 'Internal Network Adapter' %}</span>：{% trans 'connecting internal servers.' %}<br/>
                                                <span class="text-style-1">{% trans 'Management Network Adapter' %}</span>：{% trans 'connecting the protection system WebConsole for management.' %}<br/>
                                            </div>
                                        </td>
                                        <td class="border-left">
                                            <div class="td-content">
                                                <li class="marginBottom" v:show="pageInstance.isShowSingleNetworkPort()">
                                                    <div>
                                                        <rs:Label>
                                                            <rs:Radio name="proxy_mode" value="single" onchange="PageEvt.handleOnChangeProxyMode(event)"></rs:Radio>{% trans 'Single Network Adapter' %}
                                                        </rs:Label>

                                                        <span class="rectangle">eth0</span>
                                                        <span class="single-error rectangle rectangle-red">{% trans 'Missing information for this network adapter.' %}</span>
                                                        <div class="tips-right">
                                                            <i ref="single" class="ic-info"
                                                                onmouseover="PageEvt.imgTooltip.show(event, this)"
                                                               onmouseout="PageEvt.imgTooltip.hide()"></i>
                                                        </div>
                                                    </div>
                                                    <div class="line-des">{% trans 'eth0 works as External, Internal and Management Network Adapter simultaneously.' %}</div>
                                                </li>
                                                <li class="marginBottom">
                                                    <div>
                                                        <rs:Label>
                                                            <rs:Radio name="proxy_mode" value="dual_admin_proxy" onchange="PageEvt.handleOnChangeProxyMode(event)"></rs:Radio>{% trans 'Dual Network Adapter' %}
                                                        </rs:Label>
                                                        <span class="rectangle">eth0</span><span class="rectangle rectangle-lightgreen">eth1</span>
                                                        <span class="dual-error rectangle rectangle-red">{% trans 'Missing information for this network adapter.' %}</span>
                                                        <div class="tips-right">
                                                            <i ref="dual" class="ic-info"
                                                                onmouseover="PageEvt.imgTooltip.show(event, this)"
                                                               onmouseout="PageEvt.imgTooltip.hide()"></i>
                                                        </div>
                                                    </div>
                                                    <div class="line-des">{% trans 'eth0 works as management network adapter and eth1 as external and internal network adapter.' %}</div>
                                                </li>
                                                <li class="marginBottom">
                                                    <div>
                                                        <rs:Label>
                                                            <rs:Radio name="proxy_mode" value="three" onchange="PageEvt.handleOnChangeProxyMode(event)"></rs:Radio>{% trans 'Triple Network Adapters' %}
                                                        </rs:Label>
                                                        <span class="rectangle">eth0</span><span class="rectangle rectangle-lightgreen">eth1</span>
                                                        <span class="rectangle rectangle-yellowgreen">eth2</span>
                                                        <span class="three-error rectangle rectangle-red">{% trans 'Missing information for this network adapter.' %}</span>
                                                        <div class="tips-right">
                                                            <i ref="three" class="ic-info"
                                                                onmouseover="PageEvt.imgTooltip.show(event, this)"
                                                               onmouseout="PageEvt.imgTooltip.hide()"></i>
                                                        </div>
                                                    </div>
                                                    <div class="line-des">{% trans 'eth0 works as management network adapter, eth1 as external network adapter and eth2 as internal network adapter.' %}</div>
                                                </li>
                                                <li class="marginBottom">
                                                    <div>
                                                        <rs:Label><rs:Checkbox name="log_split_enable" value="log_split_enable"></rs:Checkbox>{% trans 'Log Archiving Network Adapter' %}</rs:Label>
                                                        <span class="rectangle rectangle-gray">eth3</span>
                                                        <span class="log-split-error rectangle rectangle-red">{% trans 'Missing information for this network adapter.' %}</span>
                                                    </div>
                                                    <div class="line-des">{% trans 'Using a separate network adapter (eth3) for transferring logs to Log Archiving Node.' %}</div>
                                                </li>

                                                <li rs:visible="pageInstance.isHA()">
                                                    <div>
                                                        <rs:Label><rs:Checkbox name="keepalived_split_enable" value="keepalived_split_enable"></rs:Checkbox>{% trans 'Heartbeat Network Port Adapter' %}</rs:Label>
                                                        <span class="rectangle rectangle-gray">eth3</span>
                                                        <span class="keepalived-split-error rectangle rectangle-red">{% trans 'Missing information for this network adapter.' %}</span>
                                                    </div>
                                                    <div class="line-des">{% trans 'Using a separate network adapter (eth3) for HA heartbeat network port.' %}</div>
                                                </li>

                                                <li class="errorMsg"></li>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Next' %}</button>
                            </div>
                        </div>

                        <div name="portSettings">
                            <div class="title">{% trans 'Network Adapter Settings' %}</div>
                            <div class="contentPanel">
                                <table>
                                    <tr>
                                        <td class="td-34">
                                            <div class="min-des-center">
                                                {% trans 'Management Network Adapter' %}<span class="rectangle">eth0</span><br/>
                                                {% trans 'Connecting the protection system WebConsole.' %}
                                            </div>

                                            <div class="paddingTop20" selected="ipv4">
                                                <table>
                                                    <tr>
                                                        <td class="td-text-right"><label>{% trans 'IP Address' %}</label></td>
                                                        <td><input type="text" name="admin_ip"/></td>
                                                    </tr>
                                                    <tr>
                                                        <td class="td-text-right">
                                                            <label class="admin-ipv4-control">{% trans 'Netmask' %}</label>
                                                        </td>
                                                        <td><input name="admin_netmask" type="text"/></td>
                                                    </tr>
                                                    <tr>
                                                        <td>&nbsp;</td>
                                                        <td><div class="errorMsg" rs-for="admin_ip,admin_netmask"></div></td>
                                                    </tr>
                                                </table>

                                            </div>
                                        </td>

                                        <td class="td-34 border-left">
                                            <div class="min-des-center" name="trigleNetworkDes">
                                                {% trans 'External Network Adapter' %}<span class="rectangle rectangle-lightgreen">eth1</span><br/>
                                                {% trans 'Connecting Internet/load balancer.' %}
                                            </div>
                                            <div class="min-des-center" name="doubleNetworkDes">
                                                {% trans 'External/Internal Network Adapter' %}<span class="rectangle rectangle-lightgreen">eth1</span><br/>
                                                {% trans 'Connecting Internet/load balancer and internal servers.' %}
                                            </div>

                                            <div class="paddingTop20" id="externalPanel" selected="ipv4">
                                                <table>
                                                    <tr>
                                                        <td><label>{% trans 'IP Version' %}</label></td>
                                                        <td>
                                                            <rs:Select name="external_ipVersion" rs:disabled="mForm.cluster_choice == 'cluster_join'">
                                                                <div rs-option value="ipv4">IPv4</div>
                                                                <div rs-option value="ipv6">IPv6</div>
                                                                <div rs-option value="ipv46">IPv4&IPv6</div>
                                                            </rs:Select>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table name="table-ip" rs:visible="(mForm.proxy_mode=='dual_admin_proxy'||mForm.proxy_mode=='three')&&mForm.external_ipVersion!='ipv6'">
                                                    <tr>
                                                        <td>
                                                            <label rs:visible="(mForm.proxy_mode=='dual_admin_proxy'||mForm.proxy_mode=='three')&&mForm.external_ipVersion=='ipv4'">{% trans 'IP Address' %}</label>
                                                            <label rs:visible="(mForm.proxy_mode=='dual_admin_proxy'||mForm.proxy_mode=='three')&&mForm.external_ipVersion!='ipv4'">{% trans 'IPv4 Address' %}</label>
                                                        </td>
                                                        <td><input type="text" name="external_ip" /></td>
                                                    </tr>
                                                    <tr>
                                                        <td><label class="external-ipv4-control">{% trans 'Netmask' %}</label></td>
                                                        <td><input type="text" name="external_netmask"/></td>
                                                    </tr>
                                                    <tr v:if="!RouteSettingManager.canSettingRoute()">
                                                        <td><label>{% trans 'Gateway' %}</label></td>
                                                        <td><input name="default_gateway" type="text" /></td>
                                                    </tr>
                                                </table>

                                                <rs:CloneNode src="#__TABLE_EXTERNALE_IPV6__"></rs:CloneNode>
                                                <rs:CloneNode src="#__TABLE_HOSTSTANDBY__"></rs:CloneNode>
                                                <rs:CloneNode src="#__TABLE_MUTUALSTANDBY__"></rs:CloneNode>
                                                <table>
                                                    <tbody>
                                                        <tr>
                                                            <td>&nbsp;</td>
                                                            <td><div class="errorMsg" v:rs-for="RouteSettingManager.getErrorMarkField()"></div></td>
                                                        </tr>
                                                    </tbody>
                                                </table>

                                                <rs:CloneNode src="#__PORT_BOND_TEMP__"></rs:CloneNode>
                                            </div>

                                        </td>

                                        <td class="td-34 border-left" name="eth2">
                                            <div class="min-des-center">
                                                {% trans 'Internal Network Adapter' %}<span class="rectangle rectangle-yellowgreen">eth2</span><br/>
                                                {% trans 'Connecting internal servers.' %}
                                            </div>
                                            <div id="internalPanel" class="paddingTop20" selected="ipv4">
                                                <table>
                                                    <tr id="internalIPVersionSelect">
                                                        <td><label>{% trans 'IP Version' %}</label></td>
                                                        <td>
                                                            <rs:Select name="internal_ipVersion" rs:disabled="mForm.cluster_choice == 'cluster_join' || pageInstance.isRouting()">
                                                                <div rs-option value="ipv4">IPv4</div>
                                                                <div rs-option value="ipv6">IPv6</div>
                                                                <div rs-option value="ipv46">IPv4&IPv6</div>
                                                            </rs:Select>
                                                        </td>
                                                    </tr>
                                                </table>

                                                <table name="table-ip" rs:visible="(mForm.proxy_mode=='dual_admin_proxy'||mForm.proxy_mode=='three')&&mForm.internal_ipVersion!='ipv6'">
                                                    <tr>
                                                        <td>
                                                            <label rs:visible="(mForm.proxy_mode=='dual_admin_proxy'||mForm.proxy_mode=='three')&&mForm.internal_ipVersion=='ipv4'">{% trans 'IP Address' %}</label>
                                                            <label rs:visible="(mForm.proxy_mode=='dual_admin_proxy'||mForm.proxy_mode=='three')&&mForm.internal_ipVersion!='ipv4'">{% trans 'IPv4 Address' %}</label>
                                                        </td>
                                                        <td><input type="text" name="internal_ip" /></td>
                                                    </tr>
                                                    <tr>
                                                        <td><label class="internal-ipv4-control">{% trans 'Netmask' %}</label></td>
                                                        <td><input name="internal_netmask" type="text"/></td>
                                                    </tr>
                                                </table>

                                                <rs:CloneNode src="#__TABLE_INTERNAL_IPV6__"></rs:CloneNode>

                                                <table>
                                                    <tr>
                                                        <td>&nbsp;</td>
                                                        <td><div class="errorMsg" rs-for="internal_ip,internal_netmask,internal_ipv6,internal_prefix"></div></td>
                                                    </tr>
                                                </table>
                                            </div>

                                        </td>
                                    </tr>
                                </table>

                            </div>
                            <div name="gatewayAndRouteSettingPane" class="contentPanel">
                                <section innerSection>
                                    <header>
                                        <span>{% trans 'Default Gateway Configuration' %}</span>
                                        <p>{% trans 'Add a default gateway for the protection system.' %}</p>
                                    </header>
                                    <table id="defaultGatewayTable">
                                        <tr>
                                            <td><label>IPv4{% trans 'Gateway' %}</label></td>
                                            <td>
                                                <input type="text" name="default_gateway" />
                                                <span class="explain" v:text="RouteSettingManager.getDefaultText('ipv4')"></span>
                                            </td>
                                        </tr>
                                        <tr v:show="RouteSettingManager.isShowIPv6InDefaultGateway()">
                                            <td><label>IPv6{% trans 'Gateway' %}</label></td>
                                            <td>
                                                <input type="text" name="external_ipv6_gateway" />
                                                <span class="explain" v:text="RouteSettingManager.getDefaultText('ipv6')"></span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td><div class="errorMsg" rs-for="default_gateway, external_ipv6_gateway"></div></td>
                                        </tr>
                                    </table>
                                </section>

                                <section innerSection>
                                    <header>
                                        <span>{% trans 'Routing' %} ({% trans 'Optional' %})</span>
                                        <p>{% trans 'You can add routes for the system by clicking the Add Route button. When there is the same route (destination network, same subnet mask), the private route of this node will cover the non private route.' %}</p>
                                    </header>
                                    <div id="routeErrorMsg" class="errorMsg" rs-for="routeError"></div>
                                    <rs:Table id="__ROUTE_TABLE__"
                                        header="{% trans 'Destination' %}|{% trans 'Netmask' %} / {% trans 'Prefix Length' %}|{% trans 'Gateway' %}|{% trans 'Node Private' %}|{% trans 'Actions' %}"
                                        cells="ip|netmask|gateway|private|"
                                        editable="true"
                                        emptyText="{% trans 'No result' %}"
                                        onDataChange="RouteSettingManager.routeRowChange()">
                                        <div>
                                            <div><input type="text" name="ip" /></div>
                                            <div><input type="text" name="netmask" /></div>
                                            <div><input type="text" name="gateway" /></div>
                                            <div><rs:Checkbox name="private" rs-lazy></rs:Checkbox></div>
                                            <div><a class="inlineBlock ic-trash" title="{% trans 'Delete ' %}" @click="RouteSettingManager.removeRoute(rowIndex)"></a></div>
                                        </div>
                                    </rs:Table>
                                    <div class="rsTableFooter">
                                        <button type="button" onclick="RouteSettingManager.addRoute()" class="rsTableIncreaseButton">{% trans 'Add Route' %}</button>
                                    </div>
                                </section>
                            </div>
                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button nextOrApplyBtn" onClick="PageEvt.handleOnNext(event)">{% trans 'Apply' %}</button>
                            </div>
                        </div>

                        <div name="serverPortSettings" selected="ipv4">
                            <div class="title">{% trans 'Log Archiving Network Adapter' %}<span class="rectangle rectangle-gray">eth3</span></div>
                            <div class="des">
                                {% trans 'Using a separate network adapter (eth3) for transferring logs to Log Archiving Node.' %}
                            </div>
                            <div class="contentPanel">
                                <table>
                                     <tr>
                                        <td class="td-text-right"><label>{% trans 'IP Address' %}</label></td>
                                        <td><input type="text" name="log_ip"/></td>
                                    </tr>
                                    <tr>
                                        <td class="td-text-right">
                                            <label class="admin-ipv4-control">{% trans 'Netmask' %}</label>
                                        </td>
                                        <td><input type="text" name="log_netmask" /></td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td><div class="errorMsg"></div></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Apply' %}</button>
                            </div>
                        </div>

                        <div name="keepalivedSettings" selected="ipv4">
                            <div class="title">{% trans 'Heartbeat Network Port Adapter' %}<span class="rectangle rectangle-gray">eth3</span></div>
                            <div class="des">
                                {% trans 'Using a separate network adapter (eth3) for HA heartbeat network port.' %}
                            </div>
                            <div class="contentPanel">
                                <table>
                                     <tr>
                                        <td class="td-text-right"><label>{% trans 'IP Address' %}</label></td>
                                        <td><input type="text" name="keepalived_ip"/></td>
                                    </tr>
                                    <tr>
                                        <td class="td-text-right">
                                            <label class="admin-ipv4-control">{% trans 'Netmask' %}</label>
                                        </td>
                                        <td><input type="text" name="keepalived_netmask" /></td>
                                    </tr>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <td><div class="errorMsg"></div></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="bottomPanel">
                                <button type="button" name="prev" class="item" onClick="PageEvt.handleOnPrev(event)">{% trans 'Previous' %}</button>
                                <button type="button" name="next" class="item solid-button" onClick="PageEvt.handleOnNext(event)">{% trans 'Apply' %}</button>
                            </div>
                        </div>

                    </rs:Wizard>
                </rs:Form>
            </div>
        </div>

        <div class="mask"></div>

        <rs:Tooltip id="__passwordTooltip__">
            <div name="tooltipContent">
                <div class="des-content">
                    {% trans 'Password Rules' %}<br/>
                    <span class="passwordTooltipStyle" v:html="passwordTips._len"></span><br />
                    <span class="passwordTooltipStyle" v:html="passwordTips._complexity"></span><br />
                    <span class="passwordTooltipStyle" rs:visible="passwordTips._history_check" v:html="passwordTips._history_check"></span>
                    <span class="passwordTooltipStyle" rs:visible="passwordTips._include_name" v:html="passwordTips._include_name"></span>
                </div>
            </div>
        </rs:Tooltip>

        <rs:Tooltip id="__imgTooltip__" position="right">
            <i class="tooltip-arrow-left"></i>
            <div name="tooltipContent">
                <img class="imgTip" src="/static/img/wizard/img-single-en.png" />
            </div>
        </rs:Tooltip>

        <rs:Tooltip id="__bondModeTip__">
            <div name="tooltipContent">
                {% trans "Bond mode is for business ports" %}<br/><br/>
                1、{% trans 'balance-rr' %}<br/>
                {% trans 'Round-robin policy. Transmit packets in sequential order from the first available slave through the last. This mode provides load balancing and fault tolerance.' %}<br/><br/>
                2、{% trans '802.3ad' %}<br/>
                {% trans 'IEEE 802.3ad Dynamic link aggregation. Creates aggregation groups that share the same speed and duplex settings. Utilizes all slaves in the active aggregator according to the 802.3ad specification.' %}<br/>
            </div>
        </rs:Tooltip>

        <rs:Tooltip id="__nicBondModeTip__">
            <div name="tooltipContent">
                {% trans "Bond mode is for business ports" %}<br/><br/>
                1、{% trans 'balance-rr' %}<br/>
                {% trans 'Round-robin policy. Transmit packets in sequential order from the first available slave through the last. This mode provides load balancing and fault tolerance.' %}<br/><br/>
                2、{% trans 'active-backup' %}<br/>
                {% trans 'Only one network card is active and the other is standby. If one of the lines is disconnected, the other lines will be automatically backed up.' %}<br/><br/>
                3、{% trans '802.3ad' %}<br/>
                {% trans 'IEEE 802.3ad Dynamic link aggregation. Creates aggregation groups that share the same speed and duplex settings. Utilizes all slaves in the active aggregator according to the 802.3ad specification.' %}<br/>
            </div>
        </rs:Tooltip>

        <rs:Tooltip id="__toolTip__">
            <div name="tooltipContent"></div>
        </rs:Tooltip>
    </div>

    <script>
        'use strict';

        /**
         * 防止升级后，仍然使用的是缓存的图片
         * 
         * logo：无logo的，g_const.wizard.logoUrl === ''
         * 背景图：无背景图的，g_const.wizard.bgUrl === ''
        */
        (function loadImgsAvoidCaching() {
            var DEFAULT_IMG_URL = {
                LOGO:  '/static/img/logo_brand.png',
                BG: '/static/img/wizard/img-hero-setup.png'
            };

            var random = '?ghrdm={{ build_hash_and_layout_hash }}';

            // 获取logo
            var logoUrl = DEFAULT_IMG_URL.LOGO;
            if (g_const.wizard && g_const.wizard.logoUrl !== undefined) logoUrl = g_const.wizard.logoUrl;
            if (logoUrl) rs('#logo').style.backgroundImage = 'url(' + logoUrl + random + ')';

            // 获取头部背景图片
            var bgUrl = DEFAULT_IMG_URL.BG;
            if (g_const.wizard && g_const.wizard.bgUrl !== undefined) bgUrl = g_const.wizard.bgUrl;
            if (bgUrl) rs('#__WIZARD_CONTENT_WRAPPER__ > .head').style.backgroundImage = 'url(' + bgUrl + random + ')';
        })();

        /*
        #######################################################################
            服务器下来的数据
        #######################################################################
        */
        {% autoescape off %}
        var productType = {{ product_type|to_json }};
        var isASA = productType === 'ApiSecurityAudit';
        var isApiProduct = productType === 'ApiBotDefender' || isASA; // api 两个新 productType 暂时都归到一起


        var hasBtaMode = {% visibleLayout 'wizard_bta_module' %};
        var hasRepuMode = {% visibleLayout 'wizard_repu_module' %};
        var isSupportChooseLanguage = {% visibleLayout 'support_choose_language_in_wizard' %};


        var useNonStandardStyleInPage = g_const.wizard.nonstandard;

        var skipNetworkCfg = {{ cloud_mode|to_json }},
            inContainer = {{ in_container|to_json }},
            isDebug = {{ is_build_debug|to_json }} == 1;


        // 支持的部署模式
        var isSupportMirrorModel = {% visibleLayout 'wizard_mirror_module' %};
        var isSupportTransparentModel = !inContainer;
        var isSupportHotStandbyModel = !inContainer || !skipNetworkCfg;
        var isSupportRouteProxyModel = !(inContainer || skipNetworkCfg);
		var isSupportPluginModel = !isApiProduct;

        var single_cfg = JSON.parse({{ single_cfg|to_json }}),
            dual_admin_proxy_cfg = JSON.parse({{ dual_admin_proxy_cfg|to_json }}),
            three_cfg = JSON.parse({{ three_cfg|to_json }}),
            adapter_info = {{ adapter_info|to_json }};

        var system_route = JSON.parse({{ system_route|to_json }}),
            org_route_list = system_route;

        var default_gateway = {{ default_gateway|to_json }},
            default_gateway_ipv6 = {{ default_gateway_ipv6|to_json }};

        var canInstallClickHouse = {{ can_install_clickhouse|to_json }},
            kernelOk = {{ kernel_ok|to_json }},
            hasSupportKernel = {{ has_support_kernel|to_json }};

        var nic_info = {{ nic_info|to_json }} || [];

        // 是否arm: 'aarch64' / 'kylin_aarch64'
        var isArm = {{ machine_hardware_name|to_json }}.indexOf('aarch64') > -1;
        {% endautoescape %}


        /*
        #######################################################################
            全局变量
        #######################################################################
        */
        var wizard, form, story, pageInstance = null;
        var hasEth0 = adapter_info && !!adapter_info[0];
        var hasEth1 = adapter_info && !!adapter_info[1];
        var hasEth2 = adapter_info && !!adapter_info[2];
        var hasEth3 = adapter_info && !!adapter_info[3];
        var hasEth4 = adapter_info && !!adapter_info[4];

		var deployModelConfigList = [
			{
				name: 'reverse',
				label: '{% trans "Reverse proxy Deployment" %}',
				icon: 'fa-solid fa-diagram-predecessor',
				items: [
					{
						value: 'single',
						label: "{% trans 'Single-Node Quick Deployment' %}",
                        markError: !canInstallClickHouse,
                        description: function() {
                            var isNotCpu4InSailfish = this.markError;
                            var msg = isNotCpu4InSailfish ? '{% trans "SSE 4.2 instruction set is not supported" %}' : '{% trans "Applicable to single node with single network adapter." %}';
                            return msg;
                        },
					},
					{
						value: 'group',
						label: "{% trans 'Cluster Deployment' %}",
                        description: function() {
                            return '{% trans "Applicable to Multiple nodes with support for Log Archiving Network Adapter." %}'
                        }
					},
					{
						value: 'hotStandby',
						label: "{% trans 'Hot Standby Deployment' %}",
						invisible: !isSupportHotStandbyModel,
                        description: function() {
                            return '{% trans "Applicable to scenarios where there are no load balancers and high reliability is required." %}';
                        }
					}
				]
			},
			{
				name: 'transparent',
				label: '{% trans "Transparent Deployment" %}',
				icon: 'ic-transparent',
				invisible: !isSupportTransparentModel,
				items: [
					{
						value: 'transparent',
						label: '{% trans "Transparent Deployment" %}',
                        markError: !kernelOk || !(hasEth0 && hasEth1 && hasEth2),
                        description: function() {
                            var msg = '';

                            if (!kernelOk) {
                                msg = hasSupportKernel ? '{% trans "Please reboot" %}' : '{% trans "Kernel version does not support" %}';

                            } else if (!(hasEth0 && hasEth1 && hasEth2)) {
                                msg = '{% trans "Please make sure eth0 eth1 eth2 are present" %}';

                            } else {
                                msg = '{% trans "This type of deployment does not require any change in existing forwarding rules, network topology or the source IPs in network packages, so it is invisible to other devices within the network." %}';
                            }

                            return msg;
                        },
					},
				]
			},
			{
				name: 'mirror',
				label: '{% trans "Mirroring Deployment" %}',
				icon: 'ic-rs-network-mirror',
				invisible: !isSupportMirrorModel,
				items: [
					{
						value: 'mirror',
						label: '{% trans "Mirroring Deployment" %}',
                        description: function() {
                            return '{% trans "Suitable for receiving mirrored traffic from web servers and analyzing it for inspection." %}';
                        }
					},
				]
			},
			{
				name: 'route',
				label: '{% trans "Route Deployment" %}',
				icon: 'fa fa-random',
				invisible: !isSupportRouteProxyModel,
				items: [
					{
						value: 'routeProxy',
						label: '{% trans "Cluster Deployment" %}',
                        markError: !hasEth1,
                        description: function() {
                            return this.markError ? '{% trans "Two or more physical network ports are required for this deployment." %}' : '{% trans "Applicable to scenarios where network traffic is going based on routing. The cluster forwards traffic to servers according to routing configurations after it finishes data processing." %}';
                        },
					},
                    {
						value: 'HARouteProxy',
						label: '{% trans "Hot Standby Deployment" %}',
                        markError: !hasEth1,
                        description: function() {
                            return this.markError ? '{% trans "Two or more physical network ports are required for this deployment." %}' : '{% trans "Applicable to scenarios where a dual-node high-availability solution is required and network traffic is going based on routing. Each node in this deployment forwards traffic to servers according to routing configurations after it finishes data processing." %}';
                        },
					},
				]
			},
			{
				name: 'plugin',
				label: '{% trans "Plug-in Deployment" %}',
				icon: 'fa fa-plug',
				invisible: !isSupportPluginModel,
				items: [
					{
						value: 'plugin',
						label: '{% trans "Plug-in Deployment" %}',
                        description: function() {
                            return '{% trans "The system works as a detection service center and provides feedback to the plugin." %}';
                        }
					}
				]
			}
		];


        var TRANS_MAP = {
            'Failed to record license info.':                                   "{% trans 'Failed to record license info' %}",
            'Failed to set cluster configuration.':                             "{% trans 'Failed to set cluster configuration' %}",
            'Failed to apply cluster configuration.':                           "{% trans 'Failed to apply cluster configuration' %}",
            'Cannot connect to master node.':                                   "{% trans 'Cannot connect to master node' %}",
            'Exception occurs when joining cluster':                            "{% trans 'Exception occurs when joining cluster' %}",
            'Failed to separate cluster nodes':                                 "{% trans 'Failed to separate cluster nodes' %}",
            'Failed to set shared conf, try again later.':                      "{% trans 'Failed to set configuration. Try again later' %}",
            'Failed to create task!':                                           "{% trans 'Failed to create task' %}",
            'The version of this node does not match the cluster.':             "{% trans "The version of this node does not match the cluster's" %}",
            'Log Archive Server exists already!':                               "{% trans 'Log Archive Server exists already!' %}",
            'Reputation Server exists already!':                                "{% trans 'Reputation Server Server exists already!' %}",
            'Incorrect admin password. Please check again':                     "{% trans 'Incorrect admin password. Please check again' %}",
            'Verification succeeded':                                           "{% trans 'Verification succeeded' %}",
            'Use POST':                                                         "{% trans 'Use POST' %}",
            "The layout of this node does not match the cluster.":              "{% trans "The layout of this node does not match the cluster's" %}",
            'The language of this node does not match the cluster.':            "{% trans "The language of this node does not match the cluster's" %}",
            "Request timeout":                                                  "{% trans 'Request timeout' %}",
            "The network adapter of this node does not match the cluster's":    "{% trans "The network adapter of this node does not match the cluster's" %}",
            "A node is joining cluster. Please wait and try again":             "{% trans 'A node is joining cluster. Please wait and try again' %}",
            "Cluster management error":                                         "{% trans 'Cluster management error' %}",
            "Wizard finished already":                                          "{% trans 'Wizard has been finished' %}",
            "Configuration exception":                                          "{% trans 'Configuration exception' %}",
            "Invalid route gateway.":                                           "{% trans 'Invalid route gateway.' %}",
            "Route conflict.":                                                  "{% trans 'Route conflict.' %}",
            "Default route already exists.":                                    "{% trans 'Default route already exists.' %}",
            'Failed to join cluster':                                           "{% trans 'Failed to join cluster' %}"
        };

        /*
        #######################################################################
            Model -- TODO 默认值均由后端下发
        #######################################################################
        */
        var mForm = {
            language:                   '{{ LANGUAGE_CODE }}',
			deployCategory:				isApiProduct ? 'mirror' : 'reverse',
            modelCategory:              	isApiProduct ? 'mirror' : 'single',
            single_access_ipVersion:    'ipv4',
            server_ipVersion:           'ipv4',
            username:                   'admin',
            cluster_choice:             'cluster_create', //cluster_create(创建), cluster_join(加入集群)


            role:                       'master',
            proxy_mode:                 'single', //单双三网口, single, dual_admin_proxy, three


            proxy_module:               true,   //动态应用保护
            log_archive_server_module:  true,   //日志归档
            dataminer_module:           false,  //态势感知
            bta_module:                 true,   //业务威胁感知
            repu_module:                true,   //信誉库服务
            ai_module:                  false,  //AI智能检测
            llm_module:                 false,  //大模型服务
            fsl_module:                 false,  //流量自学习，flow self-learning
            api_gateway_module:         false,  //系统服务接口
            abd_module:                 isApiProduct,  //API监控
            sailfish_module:            canInstallClickHouse,  //新报表,

            //单网口
            external_ipVersion:         'ipv4',
            external_ip:                '',
            external_netmask:           '',
            default_gateway:            '',
            external_ipv6:              '',
            external_prefix:            '',
            external_ipv6_gateway:      '',

            //双网口
            admin_ip:                   '',
            admin_netmask:              '',

            //三网口
            internal_ipVersion:         'ipv4',
            internal_ip:                '',
            internal_netmask:           '',
            internal_ipv6:              '',
            internal_prefix:            '',

            //外网配置keepalived
            external_virtual_ip:        '',
            external_virtual_netmask:   '',
            external_virtual_router_id: '210',
            external_virtual_ipv6:        '',
            external_virtual_ipv6_prefix: '',

            hotStandbyMode: "hostStandby", //hostStandby or mutualStandby
            external_virtual2_ip:        '',
            external_virtual2_netmask:   '',
            external_virtual2_router_id: '211',
            external_virtual2_ipv6:        '',
            external_virtual2_ipv6_prefix: '',
            //创建集群  密码/确认密码
            cluster_password:           '',
            cluster_password_confirm:   '',

            //加入集群: IP/密码
            cluster_ip:                 '',
            cluster_node_count:         0,    // Cluster node count
            cluster_manage_count:       0,    // Cluster manage node count
            protect_node_count:         0,    // Cluster protect node count
            dataminer_analyse_count:    0,    // Cluster dataminer analyse node count
            sailfish_count:             0,
            log_archive_server_count:   0,    // Log Archive node count
            bta_node_count:             0,    // Cluster bta node count
            repu_node_count:            0,    // Cluster repu node count
            abd_node_count:             0,    // Cluster abd node count
            ai_node_count:              0,    // Cluster ai node count
            llm_node_count:             0,    // Cluster llm
            fsl_node_count:             0,    // Cluster flow self-learning node count
            api_gateway_node_count:     0,    // Cluster api gateway node count

            //分离日志网口
            log_split_enable:           false,
            log_ip:                     '',
            log_netmask:                '',

            //HA分离心跳网口
            keepalived_split_enable:     false,
            keepalived_ip:               '',
            keepalived_netmask:          '',

            // 透明部署相关
            bond_mode:                  '-1',   //bond模式，默认无

            // 端口绑定
            is_nic_bond:                false,
            bond_nic_1:                 '',
            bond_nic_2:                 '',
            nic_bond_mode:              0,
            bond_interface:             '',


            masterserver_module:        true,
            no_proxy_select_mode:       0,

            license_status:             'Inactive',
            license_info_max_nodes:     0,
            log_adapter_cfg:            '',

            // 路由
            route_list:                 system_route,

            _validation:{
                external_ip:[function(value) {
                    if (mForm.external_ipVersion != 'ipv6') {
                        return Validation.IPV4(value);
                    }
                }, '{% trans "Incorrect IP" %}'],

                external_netmask:[function(value) {
                    if (mForm.external_ipVersion != 'ipv6') {
                        return Validation.MASK(value);
                    }
                }, '{% trans "Incorrect netmask" %}'],

                default_gateway:[function(value) {
                    if (RouteSettingManager.canSettingRoute()) {
                        return Validation.IPV4(value);
                    } else {
                        if (mForm.external_ipVersion != 'ipv6') {
                            return Validation.IPV4(value);
                        }
                    }

                }, '{% trans "Incorrect gateway" %}'],

                admin_ip:[Validation.IPV4, '{% trans "Incorrect IP" %}'],

                internal_ip:[function(value){
                    if (mForm.internal_ipVersion != 'ipv6') {
                        return Validation.IPV4(value);
                    }
                }, '{% trans "Incorrect IP" %}'],

                internal_netmask:[function(value){
                    if (mForm.internal_ipVersion != 'ipv6') {
                        return Validation.MASK(value);
                    }
                }, '{% trans "Incorrect netmask" %}'],

                external_virtual_ip:[function(value) {
                    if (mForm.external_ipVersion != 'ipv6') {
                        return Validation.IPV4(value);
                    }
                },'{% trans "Incorrect IP" %}'],

                external_virtual_netmask:[ function(value) {
                    if (mForm.external_ipVersion != 'ipv6') {
                        return Validation.MASK(value);
                    }
                }, '{% trans "Incorrect netmask" %}'],

                external_virtual_router_id:[Validation.VRID, '{% trans "Please enter a correct VRID (1 - 255)" %}'],
                external_virtual_ipv6:[function(value) {
                    if (mForm.external_ipVersion != 'ipv4') {
                        return Validation.IPV6(value);
                    }
                }, '{% trans "Incorrect IP" %}'],

                external_virtual_ipv6_prefix:[function(value) {
                    if (mForm.external_ipVersion != 'ipv4') {
                        return Validation.IPV6_PREFIX(value);
                    }
                }, '{% trans "Incorrect prefix length" %}'],

                external_virtual2_ip:[function(value) {
                    if (mForm.external_ipVersion != 'ipv6') {
                        return Validation.IPV4(value);
                    }
                },'{% trans "Incorrect IP" %}'],

                external_virtual2_netmask:[ function(value) {
                    if (mForm.external_ipVersion != 'ipv6') {
                        return Validation.MASK(value);
                    }
                }, '{% trans "Incorrect netmask" %}'],

                external_virtual2_router_id:[ function(value) {
                    if (mForm.hotStandbyMode != 'mutualStandby') return;
                    if (value == mForm.external_virtual_router_id) return "{% trans 'VRID conflict' %}";
                    if (Validation.VRID(value)) return '{% trans "Please enter a correct VRID (1 - 255)" %}';
                }],
                external_virtual2_ipv6:[function(value) {
                    if (mForm.external_ipVersion != 'ipv4') {
                        return Validation.IPV6(value);
                    }
                }, '{% trans "Incorrect IP" %}'],

                external_virtual2_ipv6_prefix:[function(value) {
                    if (mForm.external_ipVersion != 'ipv4') {
                        return Validation.IPV6_PREFIX(value);
                    }
                }, '{% trans "Incorrect prefix length" %}'] ,

                cluster_ip:[Validation.IPV4, '{% trans "Incorrect IP" %}'],
                log_ip:[Validation.IPV4, '{% trans "Incorrect IP" %}'],
                log_netmask:[Validation.MASK, '{% trans "Incorrect netmask" %}'],
                keepalived_ip:[Validation.IPV4, '{% trans "Incorrect IP" %}'],
                keepalived_netmask:[Validation.MASK, '{% trans "Incorrect netmask" %}'],
                admin_netmask:[Validation.MASK, '{% trans "Incorrect netmask" %}'],
                cluster_password:[function(value) {
                    if (value.trim() === '') {
                        return '{% trans "Password cannot be blank" %}';
                    }
                    var include_user_name = {
                        userName: mForm.username || '',
                        include: password_config.include_user_name
                    };

                    // if (mForm.cluster_choice === 'cluster_create' && Validation.PASSWORD(value)) {
                    //     if(/[\#\<\>\-\"\'\&]/.test(value)) {
                    //         return '{% trans "It should NOT include" %}' + ' <, >, \', \", &, #, -';
                    //     } else {
                    //         return '{% trans "The password must have at least 8 characters, and should contain at least three of the four types: uppercase letters, lowercase letters, numbers and special characters" %}';
                    //     }
                    // }
                    if (mForm.cluster_choice === 'cluster_create') {
                        var formatError = Validation.CHECK_PASSWORD(value, password_config.password_length, password_config.complexity, include_user_name);
                        if(formatError) return formatError;
                    }
                }],

                external_ipv6:[function(value) {
                    if (mForm.external_ipVersion != 'ipv4') {
                        return Validation.IPV6(value);
                    }
                }, '{% trans "Incorrect IP" %}'],

                external_prefix:[function(value) {
                    if (mForm.external_ipVersion != 'ipv4') {
                        return Validation.IPV6_PREFIX(value);
                    }
                }, '{% trans "Incorrect prefix length" %}'],

                external_ipv6_gateway:[function(value) {
                    if (RouteSettingManager.canSettingRoute()) {
                        if (mForm.external_ipVersion != 'ipv4' || mForm.internal_ipVersion != 'ipv4') {
                            return Validation.IPV6(value);
                        }
                    } else {
                        if (mForm.external_ipVersion != 'ipv4') {
                            return Validation.IPV6(value);
                        }
                    }
                }, '{% trans "Incorrect gateway" %}'],

                internal_ipv6:[function(value){
                    if (mForm.internal_ipVersion != 'ipv4') {
                        return Validation.IPV6(value);
                    }
                }, '{% trans "Incorrect IP" %}'],

                internal_prefix:[function(value){
                    if (mForm.internal_ipVersion != 'ipv4') {
                        return Validation.IPV6_PREFIX(value);
                    }
                }, '{% trans "Incorrect prefix length" %}']
            }
        };

        /*
        #######################################################################
            部署模式
        #######################################################################
        */

        var RouteValidateErrorMsgMap = {
            ip: '{% trans "The destination network IP is incorrect" %}',
            mask: '{% trans "Incorrect netmask" %}',
            prefix: '{% trans "Incorrect prefix length" %}',
            gateway: '{% trans "Incorrect gateway" %}',
            ipMask: '{% trans "Destination IP should be {0}, because the netmask is {1}" %}'
        };

        function BasicMode() {
            var _this = this;
            var network = new PageNetwork();
            var chooseComponentPage = null;

            Object.defineProperties(this, {
                network: {
                    get: function() { return network; }
                },
                chooseComponentPage: {
                    get: function() {
                        if (!chooseComponentPage) chooseComponentPage = new ChooseComponentPage();
                        return chooseComponentPage;
                    }
                }
            });

            this.groupSingleAccess = {
                show: function(data, view) {
                    _this.setElemVisible(view.named('bond_mode_row'), 'none');
                    _this.isApply();
                }
            };

            this.chooseComponent = {
                show: function(data, view, bBack) {
                    if (!bBack) {
                        switch(mForm.cluster_choice) {
                            case 'cluster_create':
                                _this.chooseComponentPage.reset();
                                break;

                            case 'cluster_join':
                                _this.chooseComponentPage.update();
                                break;

                            default:
                                throw new Error('{% trans "unknown mode." %}');
                        }
                    }

                    _this.isApply();
                    if (skipNetworkCfg) _this.initCloudProperty();
                }
            }

            this.portSettings = {
                show: function(data, view) {
                }
            };

            this.validation = {
                password: function() {
                    if (mForm.cluster_password != mForm.cluster_password_confirm) {
                        return {
                            message: "{% trans "Passwords don't match" %}",
                            fields: ['cluster_password', 'cluster_password_confirm']
                        };
                    }
                }
            };

            this.next = function() {
                wizard.resume(story.next());
            };

            this.saveWizard = function() {
                if (network.loading) return;

                if (mForm.is_nic_bond) {
                    var resume = confirm("{% trans 'Note: System will reboot to configure port bonding.' %}");
                    if (!resume) return;
                }

                var focusElem = document.querySelector('*:focus');
                if (focusElem) focusElem.blur();

                if (pageInstance.isHA()) {
                    pageInstance.setSubmitData();
                }

                network.save();
            };

            this.validateRoute = function() {
                var routes = rs('#__ROUTE_TABLE__').data,
                    len = routes.length;
                if (len == 0) return true;

                for (var i = 0; i < len; i++) {
                    var route = routes[i],
                        ip = route.ip,
                        netmask = route.netmask,
                        gateway = route.gateway;

                    // RouteValidateErrorMsgMap传递的意义：中英文切换后，框架无法及时处理js中的翻译，因此将翻译挪到中来
                    var errorMsg = Validation.ROUTE(ip, netmask, gateway, RouteValidateErrorMsgMap);
                    if (errorMsg) {
                        pageInstance.showErrorMessage(errorMsg, ['routeError']);
                        var row = rs('#__ROUTE_TABLE__').child('div[row=row]', false)[i];
                        row.attr('error', '');
                        return false;
                    }
                }

                return true;
            };

            this.checkIsOK = function() {
                if (BondNicManager.canSettingBondNic()) {
                    var isPass = BondNicManager.validate();
                    if (!isPass) return;
                }

                this.resumeIfIPAndMaskIsOK();
            },

            this.resumeIfIPAndMaskIsOK = function(param) {
                network.validateIPAndMask(function(resp) {
                    if (mForm.modelCategory == 'single') {
                        _this.next();

                    } else {
                        var isAllowNext = mForm.log_split_enable || mForm.keepalived_split_enable;
                        isAllowNext ? _this.next() : _this.saveWizard();
                    }
                }, param);
            };


            // 通用方法
            this.isApply = function() {
                if (story.line.length == 0) {
                    story.update()
                }
                wizard.activeView.child('.nextOrApplyBtn').html(story.position == story.line.length - 2 ? "{% trans 'Apply' %}" : "{% trans 'Next' %}" );
            };

            this.initCloudProperty = function() {
                mForm.log_split_enable = false;
                mForm.keepalived_split_enable = false;
                mForm.proxy_mode = 'single';
                mForm.external_ipVersion = single_cfg.external.ipVersion || 'ipv4';
                _this.setNetworkDefaultValue();

                if (!mForm.default_gateway) {
                    var arr = single_cfg.external.ip.split('.');
                    arr[arr.length - 1] = '0';
                    mForm.default_gateway = arr.join('.');
                }
            };

            this.setNetworkDefaultValue = function() {
                switch(mForm.proxy_mode){
                    case 'single':
                        mForm.external_ip           = single_cfg.external.ip || '';
                        mForm.external_netmask      = single_cfg.external.netmask || '';

                        mForm.external_ipv6         = single_cfg.external.ipv6 || '';
                        mForm.external_prefix       = single_cfg.external.prefix || '';
                        break;

                    case 'dual_admin_proxy':
                        mForm.external_ip           = dual_admin_proxy_cfg.external.ip || '';
                        mForm.external_netmask      = dual_admin_proxy_cfg.external.netmask || '';
                        mForm.admin_ip              = dual_admin_proxy_cfg.admin.ip || '';
                        mForm.admin_netmask         = dual_admin_proxy_cfg.admin.netmask || '';

                        mForm.external_ipv6         = dual_admin_proxy_cfg.external.ipv6 || '';
                        mForm.external_prefix       = dual_admin_proxy_cfg.external.prefix || '';
                        break;

                    case 'three':
                        mForm.external_ip           = three_cfg.external.ip || '';
                        mForm.external_netmask      = three_cfg.external.netmask || '';
                        mForm.admin_ip              = three_cfg.admin.ip || '';
                        mForm.admin_netmask         = three_cfg.admin.netmask || '';
                        mForm.internal_ip           = three_cfg.internal.ip || '';
                        mForm.internal_netmask      = three_cfg.internal.netmask || '';

                        mForm.external_ipv6         = three_cfg.external.ipv6 || '';
                        mForm.external_prefix       = three_cfg.external.prefix || '';
                        mForm.internal_ipv6         = three_cfg.internal.ipv6 || '';
                        mForm.internal_prefix       = three_cfg.internal.prefix || '';
                        break;
                }

                RouteSettingManager.setDefaultGateway();

                if (!mForm.external_ipVersion) mForm.external_ipVersion = 'ipv4';
                if (!mForm.internal_ipVersion) mForm.internal_ipVersion = 'ipv4';
            };

            this.gatherVisibleInputFields = function(node, resultArr){
                var result = resultArr||[];
                rs.each(node.childNodes, function(index, kid) {
                    if (kid.nodeType == 1) {
                        if (kid.css('display') == 'none') return;
                        var name = kid.attr('name');
                        if (name && kid.tagName.match(/textarea|input/i) && (name in mForm)) {
                            resultArr.push(name);

                        } else if (kid.childNodes.length) {
                            _this.gatherVisibleInputFields(kid, result);
                        }
                    }
                });

                return result;
            };

            this.isDisableNextBtnInModelCategory = function() {
                var allModels = PageEvt.getAllDeployModelItems();
                var curModel = allModels.filter(function(model) {
                    return model.value === mForm.modelCategory;
                })[0];

                wizard.activeView.named('next').attr('disabled', curModel.markError === true ? true : null);
            };

            /**
             * 存在rs-for与reasonField匹配的情况下，则显示对应的这一个errorMsg
             * 否则，显示该页面下所有errorMsg
            */
            this.showErrorMessage = function(what, reasonFields) {
                if (!wizard.activeView) return;
                var msg = TRANS_MAP[what] || what;
                if (!msg) return;
                var box = wizard.activeView.child('.errorMsg', false);

                if (!box) {
                    if (msg) alert(msg);
                    return;
                }

                var isMatch = false,
                    commonErrorElems = [];
                for (var i = 0; i < box.length; i++) {
                    var b = box[i];
                    if (b.attr('disabled') == 'true') continue;

                    var targetFields = b.attr('rs-for');
                    if (targetFields) {
                        if (reasonFields) {
                            for (var j = 0; j < reasonFields.length; j++) {
                                var reg = new RegExp('\\b'+reasonFields[j]+'\\b');
                                if (reg.test(targetFields)) {
                                    show(b);
                                    isMatch = true;
                                    break;
                                }
                            }
                        }
                    } else {
                        commonErrorElems.push(b);
                    }
                }

                if (!isMatch) {
                    for (var i = 0; i < commonErrorElems.length; i++) {
                        var elem = commonErrorElems[i];
                        show(elem);
                    }
                }

                if (reasonFields) {
                    for (var i = 0; i < reasonFields.length; i++) {
                        var target = wizard.activeView.named(reasonFields[i]);
                        if (target) target.attr('error', true);
                    }
                }

                function show(b){
                    b.html(msg||'').css({ opacity: msg=='' ? 0 : 1 });
                }
            };

            this.clearErrorMessage = function() {
                if (!wizard.activeView) return;
                var box = wizard.activeView.child('.errorMsg');
                if (box) box.html('').css({ opacity: 0 });

                var target = wizard.activeView.child('[error=true]');
                if (target) target.attr('error', null);
            };

            this.clearPassword = function() {
                mForm.cluster_password = '';
                mForm.cluster_password_confirm = '';
            };

            this.showLine = function(who, bShow) {
                if (bShow) {
                    who.addClass('show-inline');
                } else {
                    who.removeClass('show-inline').hide();
                }
            };

            this.setElemVisible = function(elem, cssValue) {
                elem.css({ display: cssValue });
            };

            this.switchDescStatus = function(des, isMarkError) {
                des.removeClass(isMarkError ? 'rectangle-blue' : 'rectangle-red')
                    .addClass(isMarkError ? 'rectangle-red' : 'rectangle-blue');
            }

            this.initPortBond = function() {
                BondNicManager.initBondNic();
            }


            this.isHA = function() {
                return _this.isHAInReverse() || _this.isHAInRoute();
            }

            this.isRouting = function() {
                return _this.isRouteProxyModel() || _this.isHAInRoute();
            }

            this.isShowSingleNetworkPort = function() {
                return !_this.isRouting();
            }

            this.isRouteProxyModel = function() {
                return mForm.modelCategory === 'routeProxy';
            }

            this.isHAInReverse = function() {
                return mForm.modelCategory === 'hotStandby';
            }

            this.isHAInRoute = function() {
                return mForm.modelCategory === 'HARouteProxy';
            }



            // 节点功能 - 启用/禁用模块
            function ChooseComponentPage() {
                var view = wizard.activeView;
                var proxy = new ProxyModule(view),
                    sailfish = new SailfishModule(view),
                    logArchive = new LogArchiveModule(view),
                    bta = new BtaModule(view),
                    repu = new RepuModule(view),
                    ai = new AiModule(view),
                    abd = new ABDModule(view),
                    fsl = new FslModule(view),
                    llm = new LLMModule(view),
                    apiGateway = new ApiGatewayModule(view)

                this.reset = function() {
                    proxy.reset();
                    sailfish.reset();
                    logArchive.reset();
                    bta.reset();
                    repu.reset();
                    ai.reset();
                    abd.reset();
                    fsl.reset();
                    llm.reset();
                    apiGateway.reset();
                };

                this.update = function () {
                    proxy.update();
                    sailfish.update();
                    logArchive.update();
                    bta.update();
                    repu.update();
                    ai.update();
                    abd.update();
                    fsl.update();
                    llm.update();
                    apiGateway.update();
                };

                function ProxyModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=proxy_module]') : view.child('rs\\:Switch[value=proxy_module]');
                    var des = view.named('proxyModuleDes');
                    var loseEth1InMirror = false;
                    var desText = '{% trans '<span name="protect_node_count"></span> node(s) of this function exist in current cluster.' %}';
                    btn.bind('change',function(evt){
                        fsl.update();
                        llm.update();
                    });

                    this.reset = function() {
                        mForm.proxy_module = true;

                        des.html(desText);
                        pageInstance.switchDescStatus(des, loseEth1InMirror);
                        pageInstance.showLine(des, loseEth1InMirror);
                    }

                    this.update = function() {
                        var isOverMaxCount = mForm.license_status == '' && mForm.license_info_max_nodes <= mForm.protect_node_count;
                        btn.disabled = isOverMaxCount;
                        var isMarkError = isOverMaxCount;

                        mForm.proxy_module = !btn.disabled;     // 选项可用时，将其自动打开
                        des.html(desText);  // protect_node_count已达到license规定的限制时，也显示defaultText信息
                        pageInstance.switchDescStatus(des, isMarkError);
                        pageInstance.showLine(des, isMarkError || mForm.protect_node_count > 0);
                    };
                }

                function SailfishModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=sailfish_module]') : view.child('rs\\:Switch[value=sailfish_module]');
                    var des = view.named('sailfishModuleDes');

                    this.reset = function() {
                        mForm.sailfish_module = canInstallClickHouse;
                        btn.disabled = !canInstallClickHouse;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        mForm.sailfish_module = canInstallClickHouse && mForm.sailfish_count < 1 && mForm.dataminer_analyse_count < 1;
                        btn.disabled = !canInstallClickHouse;
                        pageInstance.showLine(des, mForm.sailfish_count > 0);
                    };
                }

                function LogArchiveModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=log_archive_server_module]') : view.child('rs\\:Switch[value=log_archive_server_module]');
                    var des = view.named('logArchiveModuleDes');

                    this.reset = function() {
                        mForm.log_archive_server_module = true;
                        btn.disabled = false;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        var hasLogArchive = mForm.log_archive_server_count > 0;
                        mForm.log_archive_server_module = !hasLogArchive;
                        btn.disabled = hasLogArchive;
                        pageInstance.showLine(des, hasLogArchive);
                    };
                }

                function BtaModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=bta_module]') : view.child('rs\\:Switch[value=bta_module]');
                    var des = view.named('btaModuleDes');

                    this.reset = function() {
                        mForm.bta_module = false;
                        btn.disabled = false;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        var hasBtaServer = mForm.bta_node_count > 0;
                        mForm.bta_module = false;
                        pageInstance.showLine(des, hasBtaServer);
                    };
                }

                function RepuModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=repu_module]') : view.child('rs\\:Switch[value=repu_module]');
                    var des = view.named('repuModuleDes');

                    this.reset = function() {
                        mForm.repu_module = false;
                        btn.disabled = false;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        var hasRepuServer = mForm.repu_node_count > 0;
                        btn.disabled = hasRepuServer;
                        pageInstance.showLine(des, hasRepuServer);
                    };
                }

                function AiModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=ai_module]') : view.child('rs\\:Switch[value=ai_module]');
                    var des = view.named('aiModuleDes');

                    this.reset = function() {
                        mForm.ai_module = false;
                        btn.disabled = false;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        pageInstance.showLine(des, mForm.ai_node_count > 0);
                    };
                }

                function ABDModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=abd_module]') : view.child('rs\\:Switch[value=abd_module]');
                    var des = view.named('abdModuleDes');

                    this.reset = function() {
                        mForm.abd_module = isApiProduct;
                        btn.disabled = false;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        var isOverMaxCount = mForm.abd_node_count >= 5;
                        btn.disabled = isOverMaxCount;

                        mForm.abd_module = false;
                        pageInstance.switchDescStatus(des, isOverMaxCount);
                        pageInstance.showLine(des, mForm.abd_node_count > 0);
                    };
                }

                function LLMModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=llm_module]') : view.child('rs\\:Switch[value=llm_module]');
                    var descElem = view.named('llmModuleDes');
                    var descTextDict = {
                        existInCluster: '{% trans "Already exist" %}',
                        sameNodeAsProxy: '{% trans "Not recommended for Dynamic Application Protection node to avoid resource overload." %}',
                        sameNodeAsTrafficCapturing: '{% trans "Not recommended for Traffic Capturing node to avoid resource overload." %}'
                    };

                    btn.bind('change', function() {
                        updateDescElem();
                    });

                    this.reset = function() {
                        mForm.llm_module = false;
                        btn.disabled = false;
                        pageInstance.showLine(descElem, false);
                    };

                    this.update = function() {
                        updateDescElem();
                    };

                    function updateDescElem() {
                        var hasLLMServer = mForm.llm_node_count > 0;
                        if (hasLLMServer) {
                            descElem.html(descTextDict.existInCluster);
                            btn.disabled = hasLLMServer;
                            pageInstance.showLine(descElem, hasLLMServer);

                        } else {
                            var isSameAsProxy = mForm.proxy_module && mForm.llm_module;
                            if (isSameAsProxy) {
                                var text = mForm.modelCategory == 'mirror' ? descTextDict.sameNodeAsTrafficCapturing : descTextDict.sameNodeAsProxy;
                                descElem.html(text);
                                pageInstance.showLine(descElem, true);
                            } else {
                                pageInstance.showLine(descElem, false);
                            }
                        }
                    }
                }

                function FslModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=fsl_module]') : view.child('rs\\:Switch[value=fsl_module]');
                    var des = view.named('fslModuleDes');

                    this.reset = function() {
                        mForm.fsl_module = false;
                        btn.disabled = !mForm.proxy_mode;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        if(!mForm.proxy_module) {
                            mForm.fsl_module = false;
                            btn.disabled = true;
                        }else{
                            btn.disabled = false;
                        }
                        pageInstance.showLine(des, mForm.fsl_node_count > 0);
                    };

                }

                function ApiGatewayModule(view) {
                    var btn = useNonStandardStyleInPage ? view.child('rs\\:Checkbox[value=api_gateway_module]') : view.child('rs\\:Switch[value=api_gateway_module]');
                    var des = view.named('apiGatewayModuleDes');

                    this.reset = function() {
                        mForm.api_gateway_module = false;
                        btn.disabled = false;
                        pageInstance.showLine(des, false);
                    };

                    this.update = function() {
                        pageInstance.showLine(des, mForm.api_gateway_node_count > 0);
                    };
                }
            }
        }

        BasicMode.instanceMap = {};
        BasicMode.getModeInstance = function(modelCategory) {
            var instance = BasicMode.instanceMap[modelCategory];
            if (!instance) {
                switch(modelCategory) {
                    case 'single':
                        instance = new SingeMode();
                        break;

                    case 'group':
                        instance = new GroupMode();
                        break;

                    case 'hotStandby':
                        instance = new HotStandbyMode();
                        break;

                    case 'transparent':
                        instance = new TransparentMode();
                        break;

                    case 'mirror':
                        instance = new MirrorMode();
                        break;

                    case 'routeProxy':
                        instance = new RouteProxyMode();
                        break;

                    case 'HARouteProxy':
                        instance = new HARouteProxyMode();
                        break;

					case 'plugin':
						instance = new PluginMode();
						break;
                }

                BasicMode.instanceMap[modelCategory] = instance;
            }

            return instance;
        };

        BasicMode.getInstance = function() {
            return BasicMode.getModeInstance(mForm.modelCategory);
        };



        /**
         * 反向代理部署 - 单节点快速部署
         * */
        function SingeMode() {
            BasicMode.call(this);
            var _this = this;

            this.singleAccess = {
                show: function(data, view) {
                    initSingleProperty();
                }
            };

            this.singleAccountSetting = {
                show: function(data, view) {
                    initSingleProperty();
                }
            };

            function initSingleProperty() {
                mForm.cluster_choice = 'cluster_create';
                mForm.proxy_module = true;
                mForm.log_archive_server_module = true;
                mForm.bta_module = hasBtaMode == 0?false:true;
                mForm.repu_module = hasRepuMode == 0?false:true;
                mForm.ai_module = false;
                mForm.fsl_module = false;
                mForm.llm_module = false;
                mForm.api_gateway_module = false;
                mForm.sailfish_module = canInstallClickHouse;
                mForm.proxy_mode = 'single';
                mForm.log_split_enable = false;
                mForm.keepalived_split_enable = false;
                mForm.masterserver_module = true;
                mForm.abd_module = isDebug || isApiProduct;
            }
        }

        /**
         * 反向代理部署 - 集群化部署
         * */
        function GroupMode() {
            BasicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "IPv4 of management network adapter" %}');

                    view.child('.td-left > .content-des').innerHTML = '{% trans 'Cluster deployment has two outstanding advantages:' %}<br/>'
                        + '{% trans '1. Multiple nodes provide better processing capacities than a single-node deployment.' %}<br/>'
                        + '{% trans '2. Multiple nodes ensure a higher availability.' %}';
                }
            };
        }


        /**
         * 双击热备基类：
         *
         * 【反向代理部署 - 双击热备部署】和【路由代理部署 - 双击热备部署】均继承此类
         *
         * */
        function HotStandbyBacicMode() {
            BasicMode.call(this);
            var _this = this;

            this.singleAccess = {
                show: function(data, view) {
                }
            };

            this.groupSingleAccess = {
                show: function(data, view) {
                    _this.setElemVisible(view.named('bond_mode_row'), 'none');
                    _this.isApply();
                }
            };

            this.portSettings = {
                show: function(data, view) {
                }
            };

            this.setSubmitData = function() {
                mForm.proxy_module = true;
                mForm.dataminer_module = false;
                mForm.bta_module = false;
                mForm.repu_module = false;
                mForm.abd_module = false;
                mForm.ai_module = false;
                mForm.fsl_module = false;
                mForm.llm_module = false;
                mForm.api_gateway_module = false;

                if (mForm.cluster_choice == 'cluster_create') {
                    mForm.role = 'master';
                    mForm.masterserver_module = true;
                    mForm.log_archive_server_module = true;
                    mForm.sailfish_module = canInstallClickHouse;
                    mForm.ai_module = false;
                    mForm.bta_module = hasBtaMode == 0?false:true;
                    mForm.repu_module = hasRepuMode == 0?false:true;
                    mForm.abd_module = isApiProduct;

                } else {
                    mForm.role = 'salve';
                    mForm.masterserver_module = false;
                    mForm.log_archive_server_module = false;
                    mForm.sailfish_module = false;
                    mForm.bta_module = false;
                    mForm.repu_module = false;
                }
            };
        }


        /**
         * 反向代理部署 - 双击热备部署
         * */
        function HotStandbyMode() {
            HotStandbyBacicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "Management IP of Primary Node" %}');
                    view.child('.td-left > .content-des').innerHTML = "<label>{% trans 'Master-Backup mode' %}</label> ：{% trans 'A hot standby system consists of a primary and a standby node online simultaneously. The primary node would be the only one working, while the redundant node always waiting to take over in case the primary node encounters any failure.' %}<br/>"
                        + "<label>{% trans 'Master-Master mode' %}</label> ：{% trans 'A hot standby system consists of a primary and a standby node online simultaneously. they are both working and waiting to take over in case the other node encounters any failure.' %}<br/><br/>"
                        + "{% trans 'Notice: as standby node has no log services installed and is allowed to be working as a backup for only 14 days, please restore the primary as soon as possible.' %}";

                    if (!canInstallClickHouse) {
                        mForm.cluster_choice = 'cluster_join';
                        view.named('cluster_create').child('rs\\:radio').disabled = true;
                    }
                }
            };
        }


        /**
         * 透明部署模式
         * */
        function TransparentMode() {
            BasicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "IPv4 of management network adapter" %}');
                    view.child('.td-left > .content-des').innerHTML = rs.formatString('{% trans "{0} can be achieved with a single node or a cluster." %}', '<label>{% trans "Transparent Deployment" %}</label>');
                }
            };

            this.groupSingleAccess = {
                show: function(data, view, bBack) {
                    mForm.external_ipVersion = 'ipv4';
                    _this.setElemVisible(view.named('bond_mode_row'), 'table-row');
                    _this.isApply();
                }
            }
        }


        /**
         * 镜像部署模式
         * */
        function MirrorMode() {
            BasicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "IPv4 of management network adapter" %}');
                    view.child('.td-left > .content-des').innerHTML = rs.formatString('{% trans "{0} can be achieved with a single node or a cluster." %}', '<label>{% trans "Mirroring Deployment" %}</label>');
                }
            };
        }


        /**
         * 路由代理 - 集群化部署模式
         * */
        function RouteProxyMode() {
            BasicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "IPv4 of management network adapter" %}');
                    view.child('.td-left > .content-des').innerHTML = rs.formatString('{% trans "{0} can be achieved with a single node or a cluster." %}', '<label>{% trans "Route Deployment" %}</label>');
                }
            }
        }

        /**
         * 路由代理 - HA模式
         * */
         function HARouteProxyMode() {
            HotStandbyBacicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "Management IP of Primary Node" %}');

                    view.child('.td-left > .content-des').innerHTML = "<label>{% trans 'Master-Backup mode' %}</label> ：{% trans 'A hot standby system consists of a primary and a standby node online simultaneously. The primary node would be the only one working, while the redundant node always waiting to take over in case the primary node encounters any failure.' %}<br/><br/>"
                        + "{% trans 'Notice: as standby node has no log services installed and is allowed to be working as a backup for only 14 days, please restore the primary as soon as possible.' %}";

                    if (!canInstallClickHouse) {
                        mForm.cluster_choice = 'cluster_join';
                        view.named('cluster_create').child('rs\\:radio').disabled = true;
                    }
                }
            };
        }


		/**
         * 插件部署
         * */
		 function PluginMode() {
            BasicMode.call(this);
            var _this = this;

            this.groupAccess = {
                show: function(data, view) {
                    view.named('cluster_ip').attr('placeholder','{% trans "IPv4 of management network adapter" %}');
                    view.child('.td-left > .content-des').innerHTML = rs.formatString('{% trans "{0} can be achieved with a single node or a cluster." %}', '<label>{% trans "Plug-in Deployment" %}</label>');
                }
            };
        }






        /*
        #######################################################################
            Init
        #######################################################################
        */
        var FormCookie = (function() {
            return {
                save: function() {
                    mForm.save();
                },

                remove: function() {
                    mForm.delSaveCache();
                }
            }
        })();

        var password_config = {};
        var passwordTips = {};

        function initPage() {
            wizard = rs('#__WIZARD__');
            form = rs('#__FORM__');
            form.model = mForm;
            mForm.language = '{{ LANGUAGE_CODE }}';

            // 获取一下密码的规则
            utils.getPasswordRule('__passwordTooltip__', function(password_config) {
                password_config = password_config;
                passwordTips = utils.passwordTips(password_config, mForm.language, true);
            });
            

            story = new Story();
            wizard.init();

            setTimeout(function() {
                wizard.bind('done', function(evt) {
                    pageInstance.network.loading = false;
                    wizard.finishState = evt.data.doneState;
                    if (wizard.finishState == 'error') wizard.errorMessage = evt.data.message;
                    pageInstance.next();
                });
            }, 1000);


            // 这两个值用户判断当前页面是刷新还是关闭
            var beginTime = 0, differTime = 0;
            window.bind('beforeunload', function(evt) {
                beginTime = new Date().getTime();
                FormCookie.save();

            }).bind('unload', function() {
                differTime = new Date().getTime() - beginTime;
                if (differTime <= 5) {  // 表示关闭浏览器窗口
                    FormCookie.remove();
                }

            }).bind('keyup', function(evt) {
                var nextBtn = wizard.activeView.named('next') || wizard.activeView.named('startBtn');
                var isDisabledNext = !nextBtn || nextBtn.disabled;
                if (evt.keyCode != 13
                    || evt.target.nodeName == 'TEXTAREA'
                    || isDisabledNext
                    || PageEvt.isLangChanging) {
                    return;
                }

                if (rs('#__passwordTooltip__')) rs('#__passwordTooltip__').hide();
                if (rs('#__imgTooltip__')) rs('#__imgTooltip__').hide();
                PageEvt.handleOnNext();
            });


            /***************************
             * Model监听事件
             * *************************/
            mForm
				.listen('external_ipVersion', function(key, value) {
					var pane = wizard.activeView.child('#externalPanel');
					if (pane) pane.attr('selected', value);
					if (pageInstance.isRouting() && mForm.proxy_mode === 'three') {
						mForm.internal_ipVersion = value;
					}
				})
				.listen('internal_ipVersion', function(key, value) {
					var pane = wizard.activeView.child('#internalPanel');
					if (pane) pane.attr('selected', value);
				})
				.listen('log_split_enable', function(key, value) {
					if (value) mForm.keepalived_split_enable = false;
				})
				.listen('keepalived_split_enable', function(key, value) {
					if (value) mForm.log_split_enable = false;
				});
        }

        function trimValue(input) {
            mForm[input.name] = input.value = input.value.trim();
        }


        /*
        #######################################################################
            Story Tree
        #######################################################################
        */

        var steps_done = {
            _show: function(data, view) {
                var finishElem = view.named('doneFinish'),
                    errorElem = view.named('doneError');

                if (wizard.finishState == 'done') {
                    rs.session.clear();
                    finishElem.show();
                    errorElem.hide();
                    pageInstance.network.finish();
                    FormCookie.remove();

                } else {
                    finishElem.hide();
                    errorElem.show();
                    view.named('error-reason').html(wizard.errorMessage);
                }
            }
        }

        var steps_serverPortSetting = {
            _show: function(data, view, bBack) {
                if (!bBack) {
                    var log_cfg = single_cfg;
                    switch(mForm.proxy_mode){
                        case 'single':
                            log_cfg = single_cfg.log;
                            break;

                        case 'dual_admin_proxy':
                            log_cfg = dual_admin_proxy_cfg.log;
                            break;

                        case 'three':
                            log_cfg = three_cfg.log;
                            break;
                    }

                    mForm.log_ip = log_cfg.ip || '';
                    mForm.log_netmask = log_cfg.netmask || '';
                }
            },
            _next:function() {
                pageInstance.network.validateIPAndMask(function(resp) {
                    pageInstance.saveWizard();
                }, 'splitNetworkPort=true');
            },
            done:steps_done
        };

        var steps_keepalivedSetting = {
            _show: function(data, view, bBack) {
                if (!bBack) {
                    mForm.keepalived_ip = '';
                    mForm.keepalived_netmask = '';
                }
            },
            _next:function() {
                pageInstance.network.validateIPAndMask(function(resp) {
                    pageInstance.saveWizard();
                }, 'splitNetworkPort=true');
            },
            done:steps_done
        };

        var steps_portSettings = {
            _show:function(data, view, bBack) {

                mForm.log_ip = mForm.log_netmask = '';
                mForm.keepalived_ip = mForm.keepalived_netmask = '';
                if (!bBack) {
                    initRoute();
                    var isReloadPage = story.line.length === 0;
                    if (isReloadPage) {
                        // DAP-18655
                        pageInstance.network.preJoinCluster(initRoute);
                    }
                }

                function initRoute() {
                    if (RouteSettingManager.canSettingRoute()) {
                        RouteSettingManager.resetRouteList();
                        rs('#__ROUTE_TABLE__').data = mForm.route_list;
                    }
                    pageInstance.setNetworkDefaultValue();
                    pageInstance.initPortBond();
                }

                var isDualNetworkPort = mForm.proxy_mode == 'dual_admin_proxy';
                var doubleDes = view.named('doubleNetworkDes'),
                    threeDes = view.named('trigleNetworkDes'),
                    eth2Setting = view.named('eth2');

                if (doubleDes) doubleDes.css({ display: isDualNetworkPort ? 'block' : 'none' });
                if (threeDes) threeDes.css({display: isDualNetworkPort ? 'none' : 'block' });
                if (eth2Setting) isDualNetworkPort ? eth2Setting.removeClass('show-table').hide() : eth2Setting.addClass('show-table');

                pageInstance.portSettings.show(data, view);
                pageInstance.isApply();
            },
            _next: function() {
                if (RouteSettingManager.canSettingRoute()) {
                    var isPass = pageInstance.validateRoute();
                    if (!isPass) return;
                    mForm.route_list = rs('#__ROUTE_TABLE__').data;
                }
                pageInstance.checkIsOK();
            },
            serverPortSettings:steps_serverPortSetting,
            keepalivedSettings:steps_keepalivedSetting,
            done:steps_done
        };

        var steps_singleAccountSetting = {
            _show: function(data, view, bBack) {
                pageInstance.singleAccountSetting.show(data, view);
            },
            _validation: function() { return pageInstance.validation.password(); },
            _next: function() {
                pageInstance.saveWizard()
            },
            done:steps_done
        };

        var steps_groupSingleAccess = {
             _show: function(data, view, bBack) {
                 mForm.log_ip = mForm.log_netmask = '';
                 mForm.keepalived_ip = mForm.keepalived_netmask = '';
                 if (!bBack) {
                     pageInstance.setNetworkDefaultValue();
                     pageInstance.initPortBond();
                 }

                 pageInstance.groupSingleAccess.show(data, view);
            },
            _next: function() {
                 pageInstance.checkIsOK();
            },
            serverPortSettings: steps_serverPortSetting,
            keepalivedSettings: steps_keepalivedSetting,
            singleAccountSetting: steps_singleAccountSetting,
            done: steps_done
        };

        var steps_singleAccess = {
            _show: function(data, view, bBack) {
                if (!bBack) {
                    mForm.external_ipVersion='ipv4';
                    mForm.internal_ipVersion='ipv4';
                    pageInstance.setNetworkDefaultValue();
                    pageInstance.initPortBond();
                }

                pageInstance.singleAccess.show(data, view);
                pageInstance.isApply();

            },
            _next: function() { pageInstance.checkIsOK(); },
            singleAccountSetting: steps_singleAccountSetting
        };

        var steps_groupAccess = {
            _show: function(data, view, bBack) {
                if (!bBack) {
                    pageInstance.clearPassword();
                }

                //ASA layout 默认镜像部署
                if (isASA) mForm.modelCategory = 'mirror';

                view.named('cluster_create').child('rs\\:radio').disabled = null;
                pageInstance.groupAccess.show(data, view);
            },
            _validation: function() {
                if (mForm.cluster_choice == 'cluster_create') {
                    return pageInstance.validation.password();
                }
            },
            _next: function() {
                pageInstance.clearErrorMessage();
                if (mForm.cluster_choice == 'cluster_create') {
                    pageInstance.next();

                } else {
                    pageInstance.network.preJoinCluster();
                }
            },
            makeRole:{
                _show: function(data, view, bBack) {
                    view.child('rs\\:Radio[value=false]').disabled = false;
                    view.named('clusterInfo').show();
                    mForm.proxy_module = mForm.log_archive_server_module = mForm.bta_module = mForm.repu_module = mForm.ai_module = mForm.abd_module = mForm.fsl_module = mForm.llm_module = mForm.api_gateway_module = false;
                },

                chooseComponent:{
                    _show:function(data, view, bBack) {
                        pageInstance.chooseComponent.show(data, view, bBack);
                    },
                    _next: function() {
                        story.position == story.line.length - 2 ? pageInstance.saveWizard() : pageInstance.next();
                    },
                    _validation:function(){
                        var isCloseAll = (!mForm.masterserver_module || mForm.masterserver_module == 'false')
                                && !mForm.proxy_module
                                && !mForm.sailfish_module
                                && !mForm.log_archive_server_module
                                && !mForm.bta_module
                                && !mForm.repu_module
                                && !mForm.ai_module
                                && !mForm.abd_module
                                && !mForm.fsl_module
                                && !mForm.llm_module
                                && !mForm.api_gateway_module;

                        if (isCloseAll) {
                            scrollToError();
                            return {message:'{% trans "Please select at least one role" %}', fields:['proxy_module', 'log_archive_server_module', 'bta_module', 'repu_module', 'ai_module', 'abd_module', 'api_gateway_module', 'sailfish_module']};
                        }

                        function scrollToError() {
                            document.body.scrollTop = rs('#wizardWapper').clientHeight - document.body.clientHeight + 200;
                        }
                    },
                    accessType:{
                        _show: function(data, view, bBack) {
                            var isSupportSinglePort = pageInstance.isShowSingleNetworkPort();
                            if (!bBack) {
                                if (hasEth0 && isSupportSinglePort) {
                                    mForm.proxy_mode = 'single';
                                } else if (hasEth1) {
                                    mForm.proxy_mode = 'dual_admin_proxy';
                                } else if (hasEth2) {
                                    mForm.proxy_mode = 'three';
                                } else {
                                    mForm.proxy_mode = '';
                                }
                            }


                            var isLoseEth0 = !adapter_info || !hasEth0,
                                isLoseEth1 = !hasEth1,
                                isLoseEth2 = !hasEth2,
                                isLoseEth3 = !hasEth3;

                            var isAllowSplit = mForm.proxy_mode == '' || isLoseEth3;

                            setSingleStataus();
                            setDualStatus();
                            setThreeStatus();
                            setSplitLogStatus();
                            setHAHearbeatStatus();


                            // 单网口
                            function setSingleStataus() {
                                var LoseSingle = isLoseEth0;
                                pageInstance.showLine(view.child('.single-error'), LoseSingle);
                                var elem = view.child('rs\\:Radio[value="single"]');
                                if (elem) elem.disabled = LoseSingle;
                            }

                            // 双网口
                            function setDualStatus() {
                                var loseDual = isLoseEth0 || isLoseEth1;
                                pageInstance.showLine(view.child('.dual-error'), loseDual);
                                var elem = view.child('rs\\:Radio[value="dual_admin_proxy"]');
                                if (elem) elem.disabled = loseDual;
                            }

                            // 三网口
                            function setThreeStatus() {
                                var loseThree = isLoseEth0 || isLoseEth1 || isLoseEth2;
                                pageInstance.showLine(view.child('.three-error'), loseThree);
                                var elem = view.child('rs\\:Radio[value="three"]');
                                if (elem) elem.disabled = loseThree;
                            }

                            // 分离日志网口
                            function setSplitLogStatus() {
                                pageInstance.showLine(view.child('.log-split-error'), isAllowSplit);
                                var elem = view.child('rs\\:Checkbox[value="log_split_enable"]');
                                elem.checked = isAllowSplit ? false : mForm.log_split_enable;
                                elem.disabled = isAllowSplit;
                            }

                            // 分离心跳网口
                            function setHAHearbeatStatus() {
                                pageInstance.showLine(view.child('.keepalived-split-error'), isAllowSplit);
                                var elem = view.child('rs\\:Checkbox[value="keepalived_split_enable"]');
                                elem.checked = isAllowSplit ? false : mForm.keepalived_split_enable;
                                elem.disabled = isAllowSplit;
                            }

                        },
                        _validation: function() {
                            if (mForm.proxy_mode=='') {
                                return {
                                    message: '{% trans "The network configuration information is incorrect, please check!" %}',
                                    fields: ['proxy_mode']
                                };
                            }
                        },

                        portSettings:steps_portSettings,
                        singleAccess:steps_groupSingleAccess
                    },
                    portSettings:steps_portSettings,
                    singleAccess:steps_groupSingleAccess
                }
            }
        };

        var steps = {
            start: {
                selectCategory:{
                    _show: function(data, view, bBack) {
                        mForm.log_split_enable = false;
                        mForm.keepalived_split_enable = false;
                        mForm.proxy_mode = 'single';
                        mForm.cluster_choice = 'cluster_create';

                        pageInstance.clearPassword();
                        if (skipNetworkCfg)pageInstance.initCloudProperty();

                        pageInstance.isDisableNextBtnInModelCategory();
                    },
                    singleAccess: steps_singleAccess,
                    groupAccess: steps_groupAccess
                },
                groupAccess: steps_groupAccess
            }
        };


        /*
        ########################################################
            Story Walker
        ########################################################
        */

        function Story(){

            rs(this);
            var line = [];

            this.getter('position', function(){
                var action = wizard.router.currentAction;
                var p = 0;
                for(var i=0; i<line.length; i++){
                    if(action==line[i]){
                        p = i;
                        break;
                    }
                }
                return p;

            }).getter('line', function(){
                return line;
            });


            var routeMap = {
                single: getSingleRoute,
                group: getGroupRoute,
                hotStandby: getHARoute,
                transparent: getTransparentRoute,
                mirror: getMirrorRoute,
                routeProxy: getRouteProxyRoute,
                HARouteProxy: getHARouteProxyRoute,
				plugin: getPluginRoute
            };

            this.update = function(){
                var routeFun = routeMap[mForm.modelCategory];
                var _line = routeFun();
                line = _line.split('/');
            }

            this.next = function() {
                this.update();
                var pos = this.position;
                if (pos<line.length-1) {
                    return line[pos+1];
                } else {
                    return null;
                }
            }

            var BASE_LINE = wizard.startPath.replace('/', '');

            // 反向代理部署 - 单节点部署
            function getSingleRoute() {
                var _line = BASE_LINE + '/selectCategory/{singleAccess}/singleAccountSetting/done';
                return formatRoute(_line, {
                    singleAccess: !skipNetworkCfg
                });
            }

            // 反向代理部署 - 集群部署
            function getGroupRoute() {
                var _line = BASE_LINE + '/selectCategory/groupAccess/{makeRole}/chooseComponent/{accessType}/{networdAddress}/{serverPortSettings}/done';
                var isClusterCreate = mForm.cluster_choice === 'cluster_create';
                return formatRoute(_line, {
                    makeRole: !isClusterCreate,
                    accessType: !skipNetworkCfg && isClusterCreate,
                    networdAddress: !skipNetworkCfg && (mForm.proxy_mode === 'single' ? 'singleAccess' : 'portSettings'),
                    serverPortSettings: !skipNetworkCfg && mForm.log_split_enable
                });
            }

            /**
             * 反向代理部署 - HA部署
             * ！！！注意：【路由代理部署 - HA部署 - getHARouteProxyRoute】也调用的此方法。修改需求时，请确认这两处的HA是否需要同步修改
            */
            function getHARoute() {
                var _line = BASE_LINE + '/selectCategory/groupAccess/{accessType}/{networdAddress}/{serverPortSettings}/{keepalivedSettings}/done';
                return formatRoute(_line, {
                    accessType: mForm.cluster_choice === 'cluster_create',
                    networdAddress: mForm.proxy_mode === 'single' ? 'singleAccess' : 'portSettings',
                    serverPortSettings: mForm.log_split_enable,
                    keepalivedSettings: mForm.keepalived_split_enable
                });
            }

            // 透明部署
            function getTransparentRoute() {
                var _line = BASE_LINE + '/selectCategory/groupAccess/{makeRole}/chooseComponent/{singleAccess}/done';
                return formatRoute(_line, {
                    makeRole: mForm.cluster_choice == 'cluster_join',
                    singleAccess: !skipNetworkCfg
                });
            }

            // 镜像部署
            function getMirrorRoute() {
                var _line = BASE_LINE + '/{selectCategory}/groupAccess/{makeRole}/chooseComponent/{singleAccess}/done';
                return formatRoute(_line, {
                    selectCategory: !isASA, // ASA Layout不能选择部署模式，需求要求按镜像部署方式走
                    makeRole: mForm.cluster_choice == 'cluster_join',
                    singleAccess: !skipNetworkCfg
                });
            }

            // 路由代理 - 集群化部署
            function getRouteProxyRoute() {
                var _line = BASE_LINE + '/selectCategory/groupAccess/{makeRole}/chooseComponent/{accessType}/{networdAddress}/{serverPortSettings}/done';
                var isClusterCreate = mForm.cluster_choice === 'cluster_create';
                return formatRoute(_line, {
                    makeRole: !isClusterCreate,
                    accessType: isClusterCreate,
                    networdAddress: mForm.proxy_mode === 'single' ? 'singleAccess' : 'portSettings',
                    serverPortSettings: mForm.log_split_enable
                });
            }

            // 路由代理 - HA部署
            function getHARouteProxyRoute() {
                return getHARoute();
            }

            // 插件部署
			function getPluginRoute() {
                var _line = BASE_LINE + '/selectCategory/groupAccess/{makeRole}/chooseComponent/{accessType}/{networdAddress}/{serverPortSettings}/done';
                var isClusterCreate = mForm.cluster_choice === 'cluster_create';
                return formatRoute(_line, {
                    makeRole: !isClusterCreate,
                    accessType: !skipNetworkCfg && isClusterCreate,
                    networdAddress: !skipNetworkCfg && (mForm.proxy_mode === 'single' ? 'singleAccess' : 'portSettings'),
                    serverPortSettings: !skipNetworkCfg && mForm.log_split_enable
                });
			}

            function formatRoute(text, config) {
                var _text = text;
                for (var k in config) {
                    var b = config[k];
                    var regx = new RegExp('{' + k + '}', 'g');
                    _text = _text.replace(regx, function() {
                        if (typeof b === 'string') {
                            return b;
                        } else {
                            return b ? k : '';
                        }
                    });
                }

                return _text.replace(/(\/)+/g, '/');
            }
        }



        /*
        ########################################################
            Network
        ########################################################
        */
        var finished_ip = '';
        function PageNetwork() {
            var _this = this;
            var isAdapterChecking = false,
                g_queryTasks = {},
                loading = false;

            Object.defineProperties(_this, {
                loading: {
                    get: function() { return rs('body').hasClass('loading'); },
                    set: function(b) {
                        b ? rs('body').addClass("loading") : rs('body').removeClass("loading");
                    }
                }
            });

            this.validateIPAndMask = function(onSuccess, param) {
                if (isAdapterChecking || _this.loading) return;

                isAdapterChecking = true;
                rs('#__validateIPAndMask__').reload(serializeMForm() + (param ? '&'+param : ''), function(res) {
                    isAdapterChecking = false;

                    if (res.result.match(/^OK$/i)) {
                        onSuccess(res);

                    } else {
                        //msg1 = Network config is not correct:: xxxxx: {jsondata}.
                        //msg2 = There is already exist ip:: **************
                        var _index = res.message.indexOf(':');
                        var message = res.message.substring(0, _index);
                        var data = res.message.substring(_index).replace(/^(\:|\:\:|\s)+/, '').trim();
                        var hasJson = /[\w\W]*\:[\s]?\{[\w\W]*\}/.test(data);

                        var reasonFields = [];
                        if (hasJson) {
                            var reg = /[\w\s]+\:/;
                            var category = data.match(reg)[0].replace(':', '').trim();
                            var fieldObj = rs.parseJson(data.replace(reg, '').replace(/\.$/, ''));
                            rs.each(fieldObj, function(key, item) {
                                var defaultKey = 'default_'+ key,
                                    categoryKey = category + '_' + key;
                                reasonFields.push(key == 'gateway' ? defaultKey : categoryKey);
                            });
                        } else {
                            var inputs = pageInstance.gatherVisibleInputFields(wizard.activeView);
                            rs.each(inputs, function(index, item){
                                var component = wizard.activeView.named(item);
                                if (component && component.value==String(data).trim()) reasonFields.push(item);
                            });
                        }

                        pageInstance.clearErrorMessage();
                        if (res.result == 'ROUTE_ERROR') reasonFields = ['routeError'];
                        pageInstance.showErrorMessage(message, reasonFields);
                    }
                });
            };

            this.save = function(onSuccess) {
                _this.loading = true;
                rs('#__finalSubmit__').reload(serializeMForm(), function(resp) {
                    handleJoinOrCreateClusterCallback(resp);
                });
            };

            this.finish = function() {
                var scriptTag = getJsonpScript(finished_ip, location.port, '/wizard/finished.js?callback=pageInstance.network.handleFinishedSuccess&flag=1');
                scriptTag.bind('error', handleOnError);
                scriptTag.appendTo(rs('head'));
            };

            this.preJoinCluster = function(success) {
                if (_this.loading) return;

                var isHotStandby = pageInstance.isHA();
                mForm.role = isHotStandby ? 'salve' : 'master';

                _this.loading = true;
                rs('#__preJoinCluster__').reload(serializeMForm(), function(resp) {
                    _this.loading = false;

                    if (resp.result != 'OK') {
                        var errorMsg = resp.message || resp.exception;
                        errorMsg = TRANS_MAP[errorMsg] || errorMsg;
                        pageInstance.showErrorMessage(errorMsg);
                        return;
                    }

                    var clusterInfo = resp.cluster_info,
                        clusterNet = resp.cluster_net;

                    if (isHotStandby) {
                        var clusterNodeCount = clusterInfo ? Object.keys(clusterInfo).length : 0;
                        if (clusterNodeCount > 1) {
                            return pageInstance.showErrorMessage('{% trans "Only one standby node is allowed to be added to the cluster. At present, there is one primary node and one standby node." %}');
                        }
                    }

                    var routeList = [].concat(system_route, resp.cluster_shared_route);
                    mForm.route_list = routeList;
                    org_route_list = routeList;

                    mForm.log_adapter_cfg = resp.log_adapter_cfg;
                    mForm.license_info_max_nodes = resp.license_info_max_nodes;
                    getClusterNodeNumber(clusterInfo);
                    var msg = getAdapterMode(clusterNet);
                    if (msg) return pageInstance.showErrorMessage(msg);
                    if (success) {
                        success();
                    } else {
                        pageInstance.next();
                    }
                });

                function getClusterNodeNumber(clusterInfo) {
                    var clusterNodeNumber = 0,
                        masterNodeNumber = 0,
                        proxyNodeNumber = 0,
                        logArchiveNodeNumber = 0,
                        logNodeNumber = 0,
                        sailfishNodeNumber = 0,
                        btaNodeNumber = 0,
                        repuNodeNumber = 0,
                        abdNodeNumber = 0,
                        aiNodeNumber = 0,
                        fslNodeNumber = 0,
                        llmNodeNumber = 0,
                        apiNodeNumber = 0;

                    for (var key in clusterInfo) {
                        if (/^_/i.test(key)) continue;
                        var info  = clusterInfo[key];
                        var keys = Object.keys(info);
                        if (keys.indexOf('_deleted') > -1) continue;

                        var roles = info['_role'];
                        if (roles.indexOf('master_server') > -1) masterNodeNumber ++;
                        if (roles.indexOf('proxy') > -1) proxyNodeNumber ++;
                        if (roles.indexOf('log_server') > -1) logNodeNumber ++;
                        if (roles.indexOf('sailfish') > -1) sailfishNodeNumber ++;
                        if (roles.indexOf('log_archive_server') > -1) logArchiveNodeNumber ++;
                        if (roles.indexOf('bta_server') > -1) btaNodeNumber ++;
                        if (roles.indexOf('repu_server') > -1) repuNodeNumber ++;
                        if (roles.indexOf('abd_server') > -1) abdNodeNumber ++;
                        if (roles.indexOf('ai_service') > -1) aiNodeNumber ++;
                        if (roles.indexOf('flowlearn_server') > -1) fslNodeNumber ++;
                        if (roles.indexOf('llm_server') > -1) llmNodeNumber ++;
                        if (roles.indexOf('api_gateway') > -1) apiNodeNumber ++;

                        mForm.license_status = info['status'] && info['status']['_license_status'];
                        clusterNodeNumber ++;
                    }

                    mForm.cluster_node_count        = clusterNodeNumber;
                    mForm.cluster_manage_count      = masterNodeNumber;
                    mForm.protect_node_count        = proxyNodeNumber;
                    mForm.dataminer_analyse_count   = logNodeNumber;
                    mForm.sailfish_count            = sailfishNodeNumber;
                    mForm.log_archive_server_count  = logArchiveNodeNumber;
                    mForm.bta_node_count            = btaNodeNumber;
                    mForm.repu_node_count           = repuNodeNumber;
                    mForm.abd_node_count            = abdNodeNumber;
                    mForm.ai_node_count             = aiNodeNumber;
                    mForm.fsl_node_count            = fslNodeNumber;
                    mForm.llm_node_count            = llmNodeNumber;
                    mForm.api_gateway_node_count    = apiNodeNumber;
                }

                function getAdapterMode(clusterNet) {
                    var adapterCount = getAdapterCount(clusterNet);
                    if (adapterCount == 0) return "{% trans 'Incorrect network information' %}";
                    mForm.proxy_mode                = ['single', 'dual_admin_proxy', 'three'][adapterCount-1];
                    mForm.external_ip               = clusterNet.external_ip || '';
                    mForm.external_netmask          = clusterNet.external_netmask || '';
                    mForm.default_gateway           = clusterNet.external_gateway || '';
                    mForm.admin_ip                  = clusterNet.admin_ip || '';
                    mForm.admin_netmask             = clusterNet.admin_netmask || '';
                    mForm.internal_ip               = clusterNet.internal_ip || '';
                    mForm.internal_netmask          = clusterNet.internal_netmask || '';
                    mForm.log_split_enable          = clusterNet.log_adapter_name == 'eth3';
                    mForm.keepalived_split_enable    = clusterNet.keepalived_adapter_name == 'eth3';

                    mForm.external_ipVersion        = clusterNet.external_ipVersion||'ipv4';
                    mForm.external_ipv6             = clusterNet.external_ip;
                    mForm.external_netmask          = clusterNet.external_netmask;
                    mForm.external_ipv6_gateway     = clusterNet.external_ipv6_gateway;
                    mForm.internal_ipVersion        = clusterNet.internal_ipVersion||'ipv4';
                    mForm.internal_ipv6             = clusterNet.internal_ipv6 || '';
                    mForm.internal_prefix           = clusterNet.internal_prefix || '';
                }

                function getAdapterCount(clusterNet) {
                    var mode = ['admin', 'external', 'internal'];
                    var adapterName = [];
                    for (var key in mode) {
                        var val = clusterNet[mode[key]+'_adapter_name'];
                        if (adapterName.indexOf(val) < 0) adapterName.push(val);
                    }
                    return adapterName.length;
                }
            };

            this.queryTaskCallback = function(result, key, reqHost) {
                var evt = rs.Event('success');
                evt.data = result;
                var arr = reqHost.split('.');
                rs('#jsonpScript'+arr.join('_')).trigger(evt);
            }

            this.handleFinishedSuccess = function(result) {
                var ip = result['server_name'] || finished_ip;
                wizard.activeView.child('#finishBtn').href= isApiProduct ? 'https://'+ip+':'+result['port']+'/#/login' : 'https://'+ip+':'+result['port']+'/user/login/';
                wizard.activeView.child('#downloadCert').href=location.protocol+'//'+ip+':'+location.port+'/wizard/system_cert_download';
            }


            function serializeMForm() {
                var cloneForm = mForm.clone(true);
                cloneForm.cluster_password = _sha1_hash(mForm.cluster_password);
                cloneForm.cluster_password_confirm = _sha1_hash(mForm.cluster_password_confirm);
                cloneForm.route_list = JSON.stringify(mForm.route_list);
                return mForm.toString.call(cloneForm, 'queryString');
            }

            function handleOnError(evt) {
                _this.loading = false;
                pageInstance.showErrorMessage(evt.exception || '{% trans "Request exception" %}');
            }

            function handleJoinOrCreateClusterCallback(resp) {
                if(resp && resp.result == 'VALIDATE_FAILED' && resp.stat.brief_msg == 'CreateClusterTaskLock') {
                    var message = '{% trans "The task of creating cluster already exists." %}';
                    var evt = rs.Event('done');
                    evt.data = {
                        doneState: 'error',
                        message: message,
                        result: null
                    };
                    wizard.trigger(evt);
                    return;
                }

                if (!resp || resp.result != 'IN_QUEUE') {
                    var message = resp && resp.result != 'IN_QUEUE' ? resp.message : '{% trans "Exceptions might occur during deployment. " %}' + ' {% trans "Please click Go Back button and try again." %}';
                    var evt = rs.Event('done');
                    evt.data = {
                        doneState: 'error',
                        message: message,
                        result: null
                    };

                    wizard.trigger(evt);
                    return;
                }

                var maxRequestTime = 1000 * 1000,
                    timeInterval = 10000,
                    reqMaxCount = maxRequestTime / timeInterval;

                var taskId = resp.task_id;
                var hostTemp = location.host.split(':');
                var cur_host = hostTemp[0];
                var cur_port = hostTemp[1];
                g_queryTasks[cur_host] = { req_count:0, req_error:0 };
                checkTaskStatus(cur_host, cur_port, taskId, timeInterval, reqMaxCount);

                try {
                    var otherIP = getAdminIP();
                    if (otherIP!=cur_host) {
                        g_queryTasks[otherIP]={ req_count:0, req_error:0 };
                        setTimeout(function () {
                            checkTaskStatus(otherIP, cur_port, taskId, timeInterval, reqMaxCount);
                        }, 8000);
                    }
                } catch(err) {
                    var evt = rs.Event('done');
                    evt.data = { 'doneState':'error', 'message':err, 'result':null };
                }
            }

            function getAdminIP() {
                var map = {
                    single: mForm.external_ip,
                    dual_admin_proxy: mForm.admin_ip,
                    three: mForm.admin_ip
                };

                var ip = map[mForm.proxy_mode];
                if (ip) {
                    return ip;
                } else {
                    throw "Invalid mode";
                }
            }

            function getJsonpScript(host, port, path) {
                var url = getNewUrl(host, port, path);
                var scriptTag = rs.Element('script');
                var arr = host.split('.');
                scriptTag.id='jsonpScript'+arr.join('_');
                scriptTag.src = url;
                return scriptTag;

                function getNewUrl(ip, port, uri, _protocol) {
                    var protocol = _protocol||location.protocol;
                    if (ip && port) return protocol + '//' + ip + ':' + port + uri;
                    var oldHost = location.host.split(':');
                    var newHost = ip;
                    if (oldHost.length > 1)
                        newHost = ip + ':' + oldHost[1];
                    return protocol + '//' + newHost + uri;
                }
            }


            function checkTaskStatus(cur_host, cur_port, task_id, timeInterval, requestMaxCount) {
                if (!g_queryTasks || (g_queryTasks && Object.keys(g_queryTasks).length==0)) {
                    _this.loading = false;
                    return;
                }

                var isSuccess = checkTaskResult(requestMaxCount);
                if (!isSuccess) return;

                g_queryTasks[cur_host]['req_count'] += 1;
                var scriptTag = createJSONPTag(cur_host, cur_port, task_id, timeInterval, requestMaxCount);
                scriptTag.appendTo(rs('head'));


                function checkTaskResult(requestMaxCount) {
                    var timeoutTaskCount = 0,
                        errorTaskCount = 0,
                        reqErrorTaskCount = 0,
                        failTaskCount = 0;

                    for (var key in g_queryTasks) {
                        var task = g_queryTasks[key];
                        if (task['req_count'] > requestMaxCount) timeoutTaskCount += 1;
                        if (task['errorResult'] != null) errorTaskCount += 1;
                        if (task['req_error'] > requestMaxCount) reqErrorTaskCount += 1;
                        failTaskCount = (timeoutTaskCount > 0 || errorTaskCount > 0 || reqErrorTaskCount > 0) ? failTaskCount + 1 : 0;
                    }


                    var taskCount = Object.keys(g_queryTasks).length;
                    if (timeoutTaskCount == taskCount || failTaskCount == taskCount) {
                        onClusterReady({ error:'timeout', message:'{% trans "Exceptions might occur during deployment. " %}' + ' {% trans "Please click Go Back button or type https://IP of network adapter eth0:20145 to enter the WebConsole and then select [System – Advanced Settings – Factory Reset] to go through the Wizard again." %}' });
                        return false;
                    }

                    if (errorTaskCount == taskCount) {
                        onClusterReady(g_queryTasks[cur_host]['errorResult']);
                        return false;
                    }

                    if (reqErrorTaskCount == taskCount) {
                        _this.loading = false;
                        pageInstance.showErrorMessage('{% trans "Service status detection request exception" %}');
                        return false;
                    }

                    return true;
                }
            }


            function createJSONPTag(cur_host, cur_port, task_id, timeInterval, requestMaxCount) {
                var hostArr = cur_host.split('.');
                var hostFlag = hostArr.join('_');
                var curScript = rs('#jsonpScript'+hostFlag);
                if (curScript) curScript.remove();

                var path = rs.formatString('{0}?callback=pageInstance.network.queryTaskCallback&task_id={1}&host={2}&random={3}', '/status/ajax/query_task_stat_callback.js', task_id, cur_host, Math.random());
                var scriptTag = getJsonpScript(cur_host, cur_port, path);
                scriptTag.cur_host = cur_host;
                scriptTag.cur_port = cur_port;
                scriptTag.task_id = task_id;
                scriptTag.timeInterval = timeInterval;
                scriptTag.requestMaxCount = requestMaxCount;

                scriptTag.bind('load', handleCheckError)
                        .bind('success', handleOnSuccess)
                        .bind('error', handleJsonpOnError);

                return scriptTag;
            }


            function handleOnSuccess(evt) {
                var scriptTag = evt.target;
                scriptTag.unbind('load', handleCheckError)
                        .unbind('success', handleOnSuccess)
                        .unbind('error', handleJsonpOnError);

                var result = evt.data;
                scriptTag.attr('error', false);

                if (result) {
                    var isFinished = result.result == 'OK' && result.stat.status == 'FINISHED';
                    if (isFinished) {
                        g_queryTasks = {};
                        finished_ip = scriptTag.cur_host;
                        onClusterReady(result);
                    } else {
                        var errorResult=null;
                        if (result.result == 'NOT_FOUND') {
                            errorResult = result;
                        } else if (result.stat && result.stat.result == 'UNKNOWN' && result.stat.status == 'ABORTED') {
                            if (result.stat.brief_msg == '') result.stat.brief_msg = "{% trans 'Exceptions might occur during deployment. ' %}" + " {% trans 'Please click Go Back button or type https://IP of network adapter eth0:20145 to enter the WebConsole and then select [System – Advanced Settings – Factory Reset] to go through the Wizard again.' %}";
                            errorResult = result;
                        }

                        if (errorResult) {
                            for (var key in g_queryTasks) {
                                g_queryTasks[key]['errorResult'] = errorResult;
                            }
                        }

                        setTimeout(function(){
                            checkTaskStatus(scriptTag.cur_host, scriptTag.cur_port, scriptTag.task_id, scriptTag.timeInterval, scriptTag.requestMaxCount);
                        },scriptTag.timeInterval);

                    }

                } else {
                    setTimeout(function(){
                        checkTaskStatus(scriptTag.cur_host, scriptTag.cur_port, scriptTag.task_id, scriptTag.timeInterval, scriptTag.requestMaxCount);
                    },scriptTag.timeInterval);
                }
            }


            function onClusterReady(result) {
                var evt = rs.Event('done');
                if (result.stat && result.stat.result == 'OK') {
                    evt.data = { 'doneState':'done', 'message':null, 'result':result };
                } else {
                    var brief_msg = result.message || result.stat.brief_msg;
                    brief_msg = TRANS_MAP[brief_msg]
                            || brief_msg
                            || "{% trans 'Exceptions might occur during deployment. ' %}" + "{% trans 'Please click Go Back button or type https://IP of network adapter eth0:20145 to enter the WebConsole and then select [System – Advanced Settings – Factory Reset] to go through the Wizard again.' %}";
                    evt.data = { 'doneState':'error', 'message':brief_msg, 'result':null };
                }
                wizard.trigger(evt);
            }


            function handleJsonpOnError(evt) {
                var scriptTag = evt.target;
                scriptTag.unbind('load', handleCheckError)
                        .unbind('success', handleOnSuccess)
                        .unbind('error', handleJsonpOnError);

                console.info('handleJsonpOnError: Failed in sending checkTaskStatus ajax');

                if (g_queryTasks) {
                    var task = g_queryTasks[scriptTag.cur_host];
                    if (task && task.hasOwnProperty('req_error')) {
                        task['req_error'] += 1;
                        setTimeout(function() {
                            checkTaskStatus(scriptTag.cur_host, scriptTag.cur_port, scriptTag.task_id, scriptTag.timeInterval, scriptTag.requestMaxCount);
                        }, scriptTag.timeInterval);
                    }
                }

            }

            function handleCheckError(evt) {
                if (evt.target.attr('error')!='false') {
                    console.info('handleCheckError: Failed in sending checkTaskStatus ajax');
                }
            }
        }


        /*
        ########################################################
            Page Event
        ########################################################
        */

        var PageEvt = (function() {
            return {
                handleOnValidateActiveViewFields: function(evt) {
                    var fields = pageInstance.gatherVisibleInputFields(wizard.activeView);
                    evt.result = mForm.validate(fields);
                },

                handleOnViewChange: function(evt){
                    form.update();
                    pageInstance.clearErrorMessage();
                    updateStepBar();

                    var textInput = wizard.activeView.child('input[type=text]');
                    if (textInput) textInput.unbind('change', handleChangeValue2Trim).bind('change', handleChangeValue2Trim);

                    var textareaInput = wizard.activeView.child('textarea');
                    if (textareaInput) textareaInput.unbind('change', handleChangeValue2Trim).bind('change', handleChangeValue2Trim);

                    function handleChangeValue2Trim(evt) {
                        evt.stopPropagation();
                        var elem = evt.currentTarget;
                        elem.value = elem.value.trim();
                        elem.trigger(rs.Event('input')); // 触发更新model
                    }
                },

                handleOnErrorMessage: function(evt) {
                    var errorObj = evt.data;
                    pageInstance.clearErrorMessage();
                    pageInstance.showErrorMessage(errorObj.message, errorObj.fields);
                },

                clearErrorMsg: function(evt) {
                    if (!pageInstance) return;
                    pageInstance.clearErrorMessage();
                },

                handleOnModelRestore: function(evt) {
                    // 刷新页面恢复mForm后修正表单 - 在切换语言时（mForm.store）
                    var startHash = '#' + wizard.startPath,
                        curHash = location.hash;
                    if (!evt.isRestore && startHash.indexOf(curHash) < 0) {
                        location.href = '/wizard/wizard_home/' + startHash;
                    }

                    pageInstance = BasicMode.getInstance();
                },

                handleOnPrev: function(evt) {
                    var curView = wizard.activeView;
                    var errorInput = curView.child('*[error=true]');
                    if (errorInput) {
                        errorInput.attr('error',null);
                        curView.child('.errorMsg').css('opacity', '0');
                    }

                    wizard.previous();
                },

                handleOnNext: function(evt) {
                    var nextAction = story.next();
                    if (nextAction) wizard.next(nextAction);
                },

				handleSelectDeployCategory: function(evt) {
					var curDeploy = evt.currentTarget.attr('name');
					if (mForm.deployCategory === curDeploy) return;

					// 选择部署大类
					mForm.deployCategory = curDeploy;
					var items = PageEvt.getDeployModeItemsConfig();
					rs('#deployModeItems').load(items);

					// 选择具体的部署方式
                    initModel(items[0].value);
                },

				handleClickModelCategoryItem: function(evt) {
					var curMode = evt.currentTarget.attr('value');
					if (mForm.modelCategory === curMode) return;
                    initModel(curMode);
				},

				getDeployModeItemsConfig: function() {
					var config = deployModelConfigList.filter(function(config) {
						return config.name === mForm.deployCategory;
					})[0];

					return config.items;
				},

                getAllDeployModelItems: function() {
                    var allModels = [];
                    deployModelConfigList.forEach(function(category) {
                        allModels = [].concat(allModels, category.items);
                    });

                    return allModels;
                },

                tooltip: {
                    show: function(e, target) {
                        target.show(e.pageX, e.pageY);
                    },

                    hide: function(e, target) {
                       target.hide();
                    }
                },

                imgTooltip: {
                    show: function(e, target) {
                        var isRouteProxy = pageInstance.isRouteProxyModel();
                        var IMG_MAP = {
                            single: 'img-single-en.png',
                            dual: isRouteProxy ? 'img-route-dual-en.png' : 'img-dual-en.png',
                            three: isRouteProxy ? 'img-route-triple-en.png' : 'img-triple-en.png'
                        };

                        rs('.imgTip').src = '/static/img/wizard/' + IMG_MAP[target.attr('ref')];
                        rs('#__imgTooltip__').show(e.pageX, e.pageY);
                    },

                    hide: function(evt, target) {
                       rs('#__imgTooltip__').hide();
                    }
                },

                updateGroupFormVisible: function(evt){
                    var createForm = wizard.activeView.named('createGroupForm'),
                        createGroupMessage = wizard.activeView.named('createGroupMessage');

                    var joinForm = wizard.activeView.named('joinGroupForm'),
                        joinGroupMessage = wizard.activeView.named('joinGroupMessage');

                    if (evt.target.groupValue=='cluster_join'){
                        createForm.hide();
                        createGroupMessage.attr('disabled', true);

                        joinForm.show();
                        joinGroupMessage.attr('disabled', false);

                    } else {
                        createForm.show();
                        createGroupMessage.attr('disabled', false);

                        joinForm.hide();
                        joinGroupMessage.attr('disabled', true);
                    }

                    pageInstance.clearPassword();
                    pageInstance.clearErrorMessage();
                    updateControlStatus();
                },

                handleOnChangeProxyMode: function(evt) {
                    mForm.external_ipVersion = mForm.internal_ipVersion = 'ipv4';
                },

                isLangChanging: false,
                handleChangeLanguage: function(evt) {
                    var lang = evt.target.value;
                    if (lang === mForm.language) return;

                    PageEvt.isLangChanging = true;
                    rs('body').attr('changeLanguage', PageEvt.isLangChanging);
                    location.href = '/wizard/wizard_home/?lang=' + lang;
                }
            };

            function initModel(selectModel) {
                mForm.modelCategory = selectModel;
                pageInstance = BasicMode.getInstance();
                pageInstance.isDisableNextBtnInModelCategory();
            }

            function updateStepBar() {
                story.update();
                var skipStep = 2;
                var stepProgress = rs('#stepProgress');
                var numberStep = rs('#__NUMBER_STEP__');
                if(story.position < skipStep){
                    stepProgress.reset();
                    numberStep.html('');
                }else{
                    stepProgress.stepCount = story.line.length - skipStep;
                    stepProgress.position = story.position - skipStep;
                    numberStep.html('Step: ' + (stepProgress.position+1) + ' of ' + stepProgress.stepCount);
                }
            }

            function updateControlStatus() {
                var isClusterCreate = mForm.cluster_choice == 'cluster_create';
                mForm.proxy_module = mForm.log_archive_server_module = mForm.bta_module = mForm.repu_module = mForm.abd_module = isClusterCreate;
                mForm.masterserver_module = true;
            }

        })();

        /*
        ########################################################
         * 默认路由配置
        ########################################################
         */
        var RouteSettingManager = new function() {
            return {
                canSettingRoute: function() {
                    return (mForm.modelCategory === 'group' || pageInstance.isRouting() || pageInstance.isHA() || mForm.modelCategory === 'plugin')
                        && (mForm.proxy_mode == 'dual_admin_proxy' || mForm.proxy_mode == 'three');
                },

                isShowIPv6InDefaultGateway: function() {
                    return RouteSettingManager.canSettingRoute() &&
                        (
                            (mForm.proxy_mode == 'dual_admin_proxy' && mForm.external_ipVersion != 'ipv4')
                            || (mForm.proxy_mode == 'three' && (mForm.external_ipVersion != 'ipv4' || mForm.internal_ipVersion != 'ipv4'))
                        );
                },

                getErrorMarkField: function() {
                    var str = 'external_ip,external_netmask,external_ipv6,external_prefix,external_virtual_ip,external_virtual_netmask,external_virtual_router_id,external_virtual_ipv6,external_virtual_ipv6_prefix,external_virtual2_ip,external_virtual2_netmask,external_virtual2_router_id,external_virtual2_ipv6,external_virtual2_ipv6_prefix';
                    if (!RouteSettingManager.canSettingRoute()) {
                        str += ',default_gateway,external_ipv6_gateway';
                    }

                    return str;
                },

                addRoute: function() {
                    rs('#__ROUTE_TABLE__').addRow({ ip:'', netmask:'', gateway:'', private: true });
                },

                removeRoute: function(rowIndex) {
                    rs('#__ROUTE_TABLE__').removeRow(rowIndex);
                },

                routeRowChange: function() {
                    if (!pageInstance) return;
                    pageInstance.clearErrorMessage();
                },

                resetRouteList: function() {
                    mForm.route_list = mForm.cluster_choice == 'cluster_join' ? org_route_list : system_route;
                },

                setDefaultGateway: function() {
                    var canSetRoute = RouteSettingManager.canSettingRoute();
                    mForm.default_gateway = canSetRoute ? '' : default_gateway;
                    mForm.external_ipv6_gateway = canSetRoute ? '' : default_gateway_ipv6;
                },

                getDefaultText: function(type) {
                    var v = (type == 'ipv6' ? default_gateway_ipv6 : default_gateway) || '{% trans "No Bond" %}';
                    return rs.formatString('{% trans "Current gateway of the system: {0}" %}', v);
                }
            }
        }();
        /*
        ########################################################
        * 端口绑定管理类
        ########################################################
        */
        var BondNicManager = new function() {
            return {
                TOOLTIP_MSG: {
                    NO_NIC: '{% trans "Insufficient NIC, unable to configure" %}',
                    LOST_NAME: '{% trans "Missing NIC information, unable to flash" %}',
                    FLASH_LIGHT: '{% trans "NIC flash" %}',
                    LIGHT_CHECKING: '{% trans "NIC: {0} flash" %}'
                },

                canSettingBondNic: function() {
                    return (mForm.modelCategory != 'mirror' && mForm.modelCategory != 'transparent')
                        && (mForm.proxy_mode == 'single' || mForm.proxy_mode == 'dual_admin_proxy')
                },

                initBondNic: function() {
                    var options = getNicOptions();
                    var hasOption = options.length > 0;

                    if (hasOption) {
                        if (rs('#bond_nic_1')) {
                            rs('#bond_nic_1').load(options).bind('change', function(evt) {
                                checkChooseBondNic(evt, rs('i[for="bond_nic_1"]'));
                            });
                        }

                        if (rs('#bond_nic_2')) {
                            rs('#bond_nic_2').load(options).bind('change', function(evt) {
                                checkChooseBondNic(evt, rs('i[for="bond_nic_2"]'));
                            });
                        }
                    }

                    mForm.is_nic_bond = false;
                    mForm.bond_nic_1 = hasOption ? options[0].key : '';
                    mForm.bond_nic_2 = hasOption ? options[1].key : '';
                    mForm.nic_bond_mode = 0;
                    mForm.bond_interface = mForm.proxy_mode == 'single' ? 'eth0' : 'eth1'
                    // IE下，元素标记disabled后，无法触发mouseover，因此改用readonly
                    rs('#nicBondCheckbox').readonly = !hasOption;
                },

                validate: function() {
                    if (!mForm.is_nic_bond) return true;
                    var nic1 = rs('#bond_nic_1'), nic2 = rs('#bond_nic_2');
                    if (nic1.value == nic2.value) {
                        pageInstance.showErrorMessage('{% trans "NIC1 and NIC2 cannot choose the same configuration" %}', ['bond_nic_1', 'bond_nic_2']);
                        nic1.attr('error', true);
                        nic2.attr('error', true);
                        return false;
                    }

                    return true;
                },

                showLightTooltip: function(evt) {
                    var lightIcon = evt.currentTarget;
                    var selectorId = lightIcon.attr('for'),
                        nicSelector = rs('#'+selectorId),
                        nicName = nicSelector.selectedData.data.name,
                        nicText = nicSelector.selectedData.value;
                    lightIcon.nicName = nicName;
                    lightIcon.nicText = nicText;

                    var tooltip = setFlashLightTooltip(lightIcon);
                    tooltip.show(evt.pageX, evt.pageY);
                },

                showNicBondSwitchTooltip: function(evt, msg) {
                    if (evt.currentTarget.attr('readonly') == null) return;
                    rs('#__toolTip__').show(evt.pageX, evt.pageY, msg);
                },

                hideTooltip: function() {
                    rs('#__toolTip__').hide();
                },

                checkNic: function(evt, selector) {
                    var lightIcon = evt.currentTarget;
                    if (!canCheckNic(lightIcon)) return;

                    setLightIconStatus(lightIcon, true);
                    selector.css({
                        pointerEvents: 'none',
                        opacity: .5
                    });

                    rs('#__checkNicPort__').reload({ nic_name: lightIcon.nicName }, function(resp) {
                        setLightIconStatus(lightIcon, false);
                        selector.css({
                            pointerEvents: 'all',
                            opacity: 1
                        });
                    });
                },

                checkNicError: function() {
                    pageInstance.showErrorMessage('{% trans "Interface exception" %}', ['bond_nic_1', 'bond_nic_2']);
                }
            }

            /**
             * 1、允许配置网卡：
             * 单网口模式：nic_info中至少需要2个网卡；勾选日志分离或心跳口分离时，至少需要3个网口
             * 双网口模式：nic_info中至少需要3个网卡；勾选日志分离或心跳口分离时，至少需要4个网口
             *
             * 2、双网口模式下，网卡选项中隐藏eth0选项
             * 3、勾选了日志分离或心跳口分离的，网卡选项隐藏eth3
            */
            function getNicOptions() {
                var isSingeMode = mForm.proxy_mode == 'single',
                    isDualMode = mForm.proxy_mode == 'dual_admin_proxy';
                if (!(isSingeMode || isDualMode)) return [];

                var hasSpliteNic = mForm.log_split_enable || mForm.keepalived_split_enable;
                var minCount2Nic = (isSingeMode ? 2 : 3) + (hasSpliteNic ? 1 : 0);
                var nicCount = nic_info.length;
                if (nicCount < minCount2Nic) return [];

                var options = [];
                for (var i = 0; i < nicCount; i++) {
                    var nic = nic_info[i];
                    if (isDualMode && nic.name == 'eth0') continue;
                    if (hasSpliteNic && nic.name == 'eth3') continue;
                    options.push({
                        key: nic.businfo,
                        value: nic.businfo.replace('pci', nic.name) + ' ' + (nic.speed.toLowerCase() == 'unknow' ? '{% trans "unknow speed" %}' : nic.speed),
                        data: nic
                    });
                }

                return options;
            }

            function checkChooseBondNic(evt, lightIcon) {
                var selectedOption = evt.sourceData,
                    data = selectedOption.data;
                var hasName = !!data.name;
                lightIcon.attr({ loseInfo: hasName ? null : '' });
                BondNicManager.validate();
            }

            /**
             * readonly 或 checking时，不发check请求
             * */
            function canCheckNic(elem) {
                return elem.attr('loseInfo') == null && elem.attr('checking') == null;
            }

            function setLightIconStatus(lightIcon, isChecking) {
                if (isChecking) {
                    lightIcon.removeClass('far').addClass('fas').attr('checking', '');
                } else {
                    lightIcon.removeClass('fas').addClass('far').attr('checking', null);
                }

                setFlashLightTooltip(lightIcon);

                // 每次只能对一张网卡闪灯。因此，其中一个网卡正在闪灯时，其他闪灯icon隐藏。
                var lights = rs('.nicLight', false);
                for (var i = 0; i < lights.length; i++) {
                    var elem = lights[i];
                    if (elem == lightIcon) continue;
                    elem.css('display', isChecking ? 'none' : 'inline-block');
                }
            }

            function setFlashLightTooltip(lightIcon) {
                var nicText = lightIcon.nicText;
                var msg = BondNicManager.TOOLTIP_MSG.FLASH_LIGHT;
                if (lightIcon.attr('loseInfo') != null) {
                    msg = BondNicManager.TOOLTIP_MSG.LOST_NAME;
                } else if (lightIcon.attr('checking') != null) {
                    msg = rs.formatString(BondNicManager.TOOLTIP_MSG.LIGHT_CHECKING, nicText);
                }

                var tooltip = rs('#__toolTip__');
                tooltip.content = msg;
                return tooltip;
            }
        }();


        /*
        ########################################################
         * 组件
        ########################################################
         */
    </script>
</body>
</html>
