{% extends "v2/base.html" %}
{% load nav_tags %}
{% load i18n %}

{% block title %}{% trans 'Overview' %}{% endblock %}

{% block navigation %}{% trans 'Overview' %}{% endblock %}

{% block content %}

<style type="text/css">
    #__SYSTEM_STATUS__ .status-ok{
        color:#27ae60;
    }
    #__SYSTEM_STATUS__ > div[state='ERROR']{
        color:#adafb3;
    }
    section{
        margin-bottom: 30px !important;
    }

    #__SYSTEM_STATUS__ div[header=header] > div:nth-child(1) { width:70px; }
    #__SYSTEM_STATUS__ div[header=header] > div:nth-child(2) { min-width:120px; }
    #__SYSTEM_STATUS__ div[header=header] > div:nth-child(3) { min-width:100px; }
    #__SYSTEM_STATUS__ div[header=header] > div:nth-child(5) { min-width:80px; }
    #__SYSTEM_STATUS__ div[header=header] > div:nth-child(7) { width:80px; }

    #__SYSTEM_STATUS__[hidecpu=true] div[header=header] > div:nth-child(6),
    #__SYSTEM_STATUS__[hidecpu=true] div[row=row] > div:nth-child(6) {
        display: none !important;
    }


    #__SYSTEM_STATUS__ .ic-warning { color: #f0c201; }

    #__SELECT_ROLE_DIALOG__ > .box { min-width:500px; max-width:600px; }
    #__SELECT_ROLE_DIALOG__ > .box > .roleContent { padding:20px; min-height:180px; max-height:365px; overflow-y:auto; }
    #__ROLE_LIST__ > div:not([itemKey=__all__]) { display:inline-block; width: 33%; }
    html[type=IE] #__ROLE_LIST__ div > label:nth-child(1) { width:auto; }
    html[type=IE] #__SELECT_ROLE_DIALOG__ > .box > .roleContent { height:180px; }
    #__SELECT_ROLE_DIALOG__ div[name=mdBottom] button[name=okBtn] { min-width:83px; width:auto; }

    html[lang=en] #__ROLE_LIST__ > div:not([itemKey=__all__]) { width: 50%; }
</style>

    <div v:if="!embedMode">
        {% include 'report/statistics_block.html' %}
    </div>


    <section v:if="auth('Cluster_Node_List', 'r')">
        <header v:if="!embedMode"><span name="sysInfoHeader">{% trans 'Cluster Node List' %}</span></header>
        <div>
            <script>

                var STATUS_ERR_KEY = 'ERRNO_';

                var SystemStatus = {
                    warning: '{% trans "Current node is not working properly." %}',
                    notice: (function() {
                        var msg = "{% trans 'Node status cannot be viewed on a big data storage node!' %}";
                        if (global_params.useSailfish) {
                            msg = "{% trans 'There is no log analysis s node (query) online!' %}";
                        } else if (global_params.usePhoenix && !global_params.isPhoenixIndex) {
                             msg = "{% trans 'There is no log analysis p node (query) online!' %}";
                        }
                        return msg;
                    })(),
                    logAvailable: global_params.logNodeAvailable && (global_params.useSailfish || global_params.usePhoenix),
                    getNodeIPDisplay: function(rowData) {
                        if (isHA) {
                            return rowData.ip + '(' + (NODE_ROLE_MAP[rowData.node_role] || '') + ')';
                        } else {
                            return rowData.ip;
                        }
                    },
                    getLogHref: function(rowData) {
                        return (global_params.useSailfish ? '/overview/?type=s&node_ip=' : '/overview/?node_ip=') + rowData.ip;
                    },
                    getStatusHelp: function(rowData) {
                        return '{% trans "Description:" %}' + rowData.service_eror + rowData.description + '<br/>' + '{% trans "Resolution:" %}' + rowData.resolution;
                    },
                    getStatusText: function(status) {
                        if (status.indexOf(STATUS_ERR_KEY) > -1) {
                            var suffixText = status.replace(STATUS_ERR_KEY, '');
                            return rs.formatString(clusterTranslateMap[STATUS_ERR_KEY], suffixText);
                        } else {
                            return clusterTranslateMap[status] || status;
                        }
                    }
                };

            </script>
            <rs:Table id="__SYSTEM_STATUS__"
                      rsid="system_status"
                      header="{% trans 'ID' %}|{% trans 'IP' %}|{% trans 'Status' %}|{% trans 'Role' %}|{% trans 'Build' %}|{% trans 'Hardware' %}|{% trans 'Actions' %}"
                      cells="id|ip|status|roleText|version|hardware|null"
                      emptyText="{% trans 'No result' %}"
                      onCreateRow="handleOnCreateSystemTableRow(event)"
                      onCreateCell="handleOnCreateSystemTableCell(event)"
                      v:hideCpu="ifHideHardwareInfo">
                <div>

                    <div>
                        <span v:if="rowData.state != 'OK'"
                              onmouseenter="page.titleTip(event.pageX, event.pageY, SystemStatus.warning)"
                              onmouseleave="page.titleTipHide()"
                              class="ic-warning" style="margin-right:5px;"></span>

                        <a v:if="SystemStatus.logAvailable"
                           v:text="rowData.id"
                           v:href="SystemStatus.getLogHref(rowData)"></a>

                        <span v:if="!SystemStatus.logAvailable"
                              v:text="rowData.id"
                              onmouseenter="page.titleTip(event.pageX, event.pageY, SystemStatus.notice)"
                              onmouseleave="page.titleTipHide()"></span>
                    </div>

                    <div>
                        <a v:if="SystemStatus.logAvailable"
                           v:text="SystemStatus.getNodeIPDisplay(rowData)"
                           v:href="SystemStatus.getLogHref(rowData)"></a>

                        <span v:if="!SystemStatus.logAvailable"
                              v:text="SystemStatus.getNodeIPDisplay(rowData)"
                              onmouseenter="page.titleTip(event.pageX, event.pageY, SystemStatus.notice)"
                              onmouseleave="page.titleTipHide()"></span>
                    </div>

                    <div>
                        <span v:if="auth('Node_Status', 'r')" v:class="rowData.state == 'OK' ? 'status-ok' : ''">
                            <span v:text="SystemStatus.getStatusText(rowData.status)"></span>
                            <span v:if="rowData.state != 'OK'"
                                  class="ic-help helpIcon"
                                  @mouseenter="page.titleTip(event.pageX, event.pageY, SystemStatus.getStatusHelp(rowData))"
                                  onmouseleave="page.titleTipHide()"></span>
                            <span v:if="rowData.node_transparent_status == 'bridge'">| {% trans "Bridge Transparent" %}</span>
                            <span v:if="rowData.node_transparent_status == 'bypass'">| {% trans "Bypass Transparent" %}</span>
                        </span>
                        <span v:if="!auth('Node_Status', 'r')">--</span>
                    </div>

                    <div>
                        <rs:RoleControl
                            v:data="rowData"
                            v:editable="auth('Change_Node_Role', 'w')" rs-lazy></rs:RoleControl>
                    </div>

                    <div v:text="rowData.version"></div>

                    <div>
                        <div v:text="rowData.cpu_model"></div>
                        <div v:text="rowData.cpu_count"></div>
                        <div v:text="rowData.max_memory"></div>
                        <div v:text="rowData.max_disk_space"></div>
                    </div>

                    <div>
                        <a v:if="auth('Delete_Node', 'w')"
                           v:title="gettext('Delete')"
                           @click="removeSystemTableRow(rowIndex, rowData)"
                           class="inlineBlock ic-trash"></a>
                        <span v:if="!auth('Delete_Node', 'w')">--</span>
                    </div>
                </div>
            </rs:Table>
        </div>
    </section>

    <rs:ModalDialog id="__SELECT_ROLE_DIALOG__">
        <div class="roleContent">
            <p>{% trans 'Dynamically modifying node roles' %}</p>
            <rs:MultiSelectList id="__ROLE_LIST__" isBatch="false"></rs:MultiSelectList>
        </div>
    </rs:ModalDialog>

{% endblock %}

{% block script %}
<script language="JavaScript" type="text/javascript">

    /*
    * ####################################################
    *   Init From Server Start
    * ####################################################
    * */

    rs.plugin({ 'MultiSelectList': '/static/js/rs-customized/ui.MultiSelectList.js' })
    .bind('ready', function() {
        if (embedMode) {
            rs('#navigationBar').setHeader('{% trans 'Cluster Node List' %}');
        }
    });

    var clusterTranslateMap={
        'Rollbacking':'{% trans "Rolling back" %}',
        'Upgrading':'{% trans "Upgrading" %}',
        'Inactive':'{% trans "Inactive" %}',
        'Offline':'{% trans "Offline" %}',
        'Online':'{% trans "Online" %}',
        'ERRNO_':'{% trans "ERRNO_{0}" %}'
    };

    var NODE_ROLE_MAP = {
        'MASTER': '{% trans "MASTER_HA" %}',
        'BACKUP': '{% trans "BACKUP_HA" %}'
    };

    var ABD_SERVER_MAX_COUNT = 5;
    var DYNAMIC_ROLES = ['ai_service', 'bta_server', 'flowlearn_server', 'api_gateway'];   // 无论其他节点是否已包含, 都可动态增删这些角色
    var LIMIT_COUNT_ROLES = { abd_server: ABD_SERVER_MAX_COUNT, repu_server: 1 ,llm_server: 1};  // 限制集群中只能有一个节点有【信誉库服务】角色
    var CAN_MODIFY_ROLES = [ '-bta_server', '-ai_service', '-llm_server', '-abd_server', '-flowlearn_server', '-api_gateway', '-repu_server'];  // 保存角色时使用
    var HA_BACKUP_ROLES = ['ai_service', 'flowlearn_server', 'api_gateway'];    // HA模式: 备节点只能动态增删【AI智能检测】、【流量自学习】、【系统API接口】这三个角色

    if (window.self !== window.top && !embedMode) window.top.location.href = window.location.href;

    {% autoescape off %}
    var isHA = {{ is_ha|to_json }};
    var status = {{ status|to_json }};
    var tableData = {{ ClusterNodeList|to_json }};
    var orgList = {{ ClusterNodeList|to_json }};
    var lic_master = {{ lic_master|to_json }};
    var abdServerCount = {{ abd_server_count|to_json }}
    var ifHideHardwareInfo = {{ if_hide_hardware_info|to_json }}
    {% endautoescape off %}

    var allRoleMap = getAllRolMap();
    initTableData();

    function getAllRolMap() {
        var proxy_txt = '{% trans "Dynamic Application Protection" %}';
        if(matchDeployMode('mirror')) proxy_txt = '{% trans "Flow collection" %}';

        var map = {
            log_server: {  text: getLogServerTxt },
            log_archive_server: { text:'{% trans "Log Archiving" %}' },
            master_server: { text:'{% trans "Master Node" %}'  },
            proxy: { text: proxy_txt },
            bta_server: { text:'{% trans "BTA" %}' },
            repu_server: { text:'{% trans "Threat Intelligence Service" %}' },
            ai_service: { text:'{% trans "AI intelligent detection" %}' },
            llm_server: { text:'{% trans "LLM Service" %}' },
            abd_server: { text:'{% trans "API Monitor" %}' },
            flowlearn_server: { text: '{% trans "Flow self-learning" %}' },
            api_gateway: { text:'{% trans "API Gateway" %}' },
            sailfish: { text: getSailfishTxt }
        };

        return map;

        function getLogServerTxt(is_search_head) {
            return is_search_head ? '{% trans "Big data analysis P (query)" %}' : '{% trans "Big data analysis P (storage)" %}';
        }

        function getSailfishTxt(is_sailfish_head) {
            return is_sailfish_head ? '{% trans "Big data analysis S (query)" %}' : '{% trans "Big data analysis S (storage)" %}';
        }
    }

    //state: OK, WARNING, ERROR
    function initTableData() {
        if (!status || status!=200 || !tableData || tableData.length==0) {
            tableData = [];
            return;
        }

        var limitCountRoles = markLimitCountRolesInCluster();
        for (var i=0; i<tableData.length; i++) {
            var clusterNode = tableData[i];
            if (!clusterNode) continue;
            clusterNode.is_lic_master = lic_master && lic_master == clusterNode.ip;
            clusterNode.roleText = getRoleText(clusterNode);
            clusterNode.selectRoleList = getSelectRoleList2Node(clusterNode, limitCountRoles);
        }
    }

    function markLimitCountRolesInCluster() {
        var allRolesInCluster = [];
        tableData.forEach(function(data) {
            allRolesInCluster = allRolesInCluster.concat(data.role.replace(/\s/g,'').split('|'));
        });

        var arr = [];   // 保证每次展示的顺序一样
        for (var roleName in LIMIT_COUNT_ROLES) {
            var maxCount = LIMIT_COUNT_ROLES[roleName];
            var roleCount = allRolesInCluster.filter(function(name) { return name === roleName; }).length;
            var isAllowAddRole = roleCount < maxCount;
            arr.push([roleName, isAllowAddRole]);
        }

        return arr;
    }

    function getRoleText(node) {
        var existRoles = node.role.replace(/\s/g,'').split('|');
        var textArr = [];
        var dataArr = [];
        existRoles.forEach(function(role, index, arr) {
            var _txt = formatRoleText(role, node);
            textArr.push(_txt);
        });

        return textArr.join(' | ');
    }

    function getSelectRoleList2Node(node, limitCountRoles) {
        var existRoles = node.role.replace(/\s/g,'').split('|');
        var dataArr = getCustomRoles2List(node);
        //remove "flowlearn_server" when proxy not in role.
        if (existRoles.indexOf("proxy")==-1){
            dataArr.some(function(item,index){
                if (item.name=="flowlearn_server"){
                    dataArr.splice(index,1)
                    return true;
                }
            })
        }
        return dataArr;

        function addExistRoles2List() {
            var list = [];
            existRoles.forEach(function(role, index, arr) {
                var data = getSelectData(role, node);
                data.checked = true;
                list.push(data);
            });

            return list;
        }

        function getCustomRoles2List(node) {
            var roles = [];
            if (isHA && node.node_role == 'BACKUP') {
                roles = [].concat(HA_BACKUP_ROLES);
            } else {
                roles = [].concat(DYNAMIC_ROLES);
                for (var i = 0; i < limitCountRoles.length; i++) {
                    var roleLimitInfo = limitCountRoles[i];
                    var roleName = roleLimitInfo[0];
                    var isEditable = roleLimitInfo[1];
                    // 如果集群中该角色未达个数上限 或 当前节点勾选了此角色, 则都应该加入选项中
                    if (isEditable || isExistRole(roleName)) roles.push(roleName);
                }
            }
            if (isSafeplusOrNGWAFWE()){
                roles = filterWAFUnneedRoles(roles);
            }

            var dataArray = [];
            roles.forEach(function(field, index, array) {
                var data = getSelectData(field);
                data.checked = isExistRole(field);
                dataArray.push(data);
            });

            return dataArray;
        }

        function filterWAFUnneedRoles(roles){
            var unneed_roles = ['repu_server','abd_server','bta_server'];
            unneed_roles.forEach(function(item){
                var index = roles.indexOf(item);
                if(index > -1){
                    roles.splice(index,1);
                }
            });
            return roles;
        }

        function isExistRole(role) {
            return existRoles.indexOf(role) > -1;
        }

        function getSelectData(role, node) {
            return { name:role, value:role, text:formatRoleText(role, node) };
        }
    }

    function formatRoleText(role, node) {
        var _txt = role;
        if (role in allRoleMap) {
            var _t = allRoleMap[role].text;
            if (role=='log_server') {
                _txt = _t(node.is_search_head);
            } else if (role=='sailfish') {
                _txt = _t(node.is_sailfish_head);
            } else {
                _txt = _t;
            }
        }

        return _txt;
    }


    /*
    * ####################################################
    *   Init From Server End
    * ####################################################
    * */

    var hasLLMLocalPkg = false;

    function initPage(){
        rs('#__SYSTEM_STATUS__').data = tableData;
        startCheckClusterStatus();


        if (isHA) {
            var headerElem = rs('body').named('sysInfoHeader');
            if (headerElem) {
                headerElem.html(headerElem.html() + ' (' + DeployModeManager.config.text + ')');
            }
        }

        rs('body')
            .attr('page_custom_made', g_const.page_custom_made || null)
            .bind('switchQuerySysStatus', function() {
                reloadPage();
            })
            .bind('sessionExpired', function() {
                stopCheckClusterStatus();
            })
            .bind('licenseHasLLMPkg', function(evt) {
                hasLLMLocalPkg = evt.hasLLMLocalPkg;
            });
    }

    function handleOnCreateSystemTableRow(event){
        event.element.attr('state', event.data.state);
    }


    rs.plugin('RoleControl', function(_) {

        var control;
        var _this = this.interface('DataContainer')

        .getter('data', function() {
            return '';

        }).setter('data', function(rowData) {
            this.html('');
            control = new RoleControl(rowData);
            control.roleTxt = rowData.roleText;
            control.roleList = rowData.selectRoleList;
            control.element.appendTo(_this);

        }).setter('editable', function(b) {
            control.editable = b;
        });
    });

    function RoleControl(node) {

        var nodeId = node.id;
        var _wrapper = rs.Element('span');
        var roleTxtSpan = rs.Element('span').attr({ name:'roleTxt' }).appendTo(_wrapper);

        if (global_params.heterogeneousCluster) {
            if (node.machine_hardware_name == 'kylin_aarch64') {
                rs.Element('i').attr({ class:'ic-product-flag ic-chnos-arm' }).appendTo(_wrapper);
            } else if (node.machine_hardware_name == 'kylin_x86_64') {
                rs.Element('i').attr({ class:'ic-product-flag ic-chnos-x86' }).appendTo(_wrapper);
            } else {
                rs.Element('i').attr({ class:'ic-product-flag ic-ubuntu-x86' }).appendTo(_wrapper);
            }
        }

        var icon = rs.Element('i')
        .attr({ class:'ic-pen ic-pen-bottom-border' })
        .css({
            marginLeft: '10px',
            color: embedMode ? '#0099ff' : '#00a6c8',
            cursor: 'pointer',
            borderRadius: '2px'
        })
        .bind('click', handleClickEditIcon);

        if (node.status == 'Online' || node.status == 'Inactive') {
            icon.appendTo(_wrapper);
        }

        var roleDatas = [];
        var oldRoleDatas = [];
        var oldSelectedDatas = [];
        var dialog = rs('#__SELECT_ROLE_DIALOG__');
        var rolePopBox = rs('#__ROLE_LIST__');

        Object.defineProperties(this, {
            element: {
                value: _wrapper
            },

            roleList: {
                set: function(arr) {
                    if (!arr || arr.length==0) {
                        icon.hide();
                    } else {
                        roleDatas = arr;
                        oldRoleDatas = cloneRoleDatas(arr);
                    }
                }
            },

            roleTxt: {
                get: function() {
                    return roleTxtSpan.html();
                },
                set: function(txt) {
                    roleTxtSpan.html(txt);
                }
            },

            editable: {
                set: function(b) {
                    if (b) {
                        icon.show();
                    } else {
                        icon.hide();
                    }
                }
            }
        });

        function handleClickEditIcon(evt) {
            rolePopBox.render(roleDatas);
            var selectedRoles = rolePopBox.selectedDatas;
            oldSelectedDatas = cloneRoleDatas(selectedRoles);
            dialog.child('p').html(rs.formatString('{% trans "Dynamically modifying {0} </ b> role of <b> node" %}',nodeId));
            dialog.open(null, handleOnModifyRole, handleOnCancelEdit, '{% trans "Change node role" %}');
        }

        function cloneRoleDatas(datas) {
            var cloneArr = [];
            datas.forEach(function(data, index, arr){
                cloneArr.push(JSON.parse(JSON.stringify(data)));
            });

            return cloneArr;
        }

        var prevAbdServer = true;
        var isFrozenFinish = false;
        var askTips = rs.formatString('{% trans "Are you sure you want to apply the changed role on node {0}?" %}', nodeId);
        var LlmInProxyTip = '{% trans "Not recommended for Dynamic Application Protection node to avoid resource overload." %}';
        var LlmInProxyMirrorTip = '{% trans "Not recommended to enable the LLM Service detection for Traffic Capturing node to avoid resource overload." %}';
        var llmPkgWillUninstallTip = '{% trans "Disabling LLM service will result in the deletion of the uploaded local LLM model." %}';
        var abdServerTip = '{% trans "There will be no API Monitor role in the cluster, and the API protection functions will not be available." %}';
        function handleOnModifyRole(evt) {
            evt.preventDefault();
            var newSelectedDatas = rolePopBox.selectedDatas;
            var curAbdServer = isSelectedSpecifiedRole(newSelectedDatas, 'abd_server');
            var originAbdServer = isSelectedSpecifiedRole(oldSelectedDatas, 'abd_server');

            /*
            * API角色勾选的情况下，检查API角色个数是否超限
            * 1、该节点原本就勾选了API的，检查abdServerCount是否超限（升级来得数据会出现此情况）
            * 2、该节点原本就未勾选了API的，检查abdServerCount+1是否超限
            */
            if (curAbdServer) {
                var curAbdCount = abdServerCount + (originAbdServer ? 0 : 1);
                if (curAbdCount > ABD_SERVER_MAX_COUNT) {
                    dialog.errorMessage = rs.formatString('{% trans "No more API Monitor roles are allowed as there are alrealy {0} exsiting in current cluster." %}', ABD_SERVER_MAX_COUNT);
                    return;
                }
            }

            var isChange = isRoleChange(oldSelectedDatas, newSelectedDatas);
            if (isChange) {
                if (isFrozenFinish && prevAbdServer == curAbdServer) {
                    dialog.errorMessage = '';
                    submitRoles(newSelectedDatas);
                } else {
                    var tip = askTips;
                    if (abdServerCount == 1 && originAbdServer && !curAbdServer) {
                        tip = abdServerTip + askTips;
                    }
                   
                    // LLM节点变更：1、若在防护节点勾选LLM服务时，则需要提示；2、取消LLM服务时，若集群中有LLM_Pkg，则需要提示
                    var curLLMServer = isSelectedSpecifiedRole(newSelectedDatas, 'llm_server');
                    var oldLLMServer = isSelectedSpecifiedRole(oldSelectedDatas, 'llm_server');
                    var isLLMServerStatusChanage = curLLMServer !== oldLLMServer;
                    if (isLLMServerStatusChanage) {
                        if (curLLMServer) {
                            var isProxyNode = node.role.replace(/\s+/g, '').split('|').indexOf('proxy') > -1;
                            if (isProxyNode){
                                if(matchDeployMode('mirror')){
                                    tip = LlmInProxyMirrorTip + tip;
                                }else{
                                    tip = LlmInProxyTip + tip;
                                }
                            }
                        } else {
                            if (hasLLMLocalPkg) {
                                tip = llmPkgWillUninstallTip + tip;
                            }
                        }
                    }

                    dialog.errorMessage = tip;
                    dialog.frozenOKBtn(3);
                    prevAbdServer = curAbdServer;
                    isFrozenFinish = true;
                }
            } else {
                closeRoleDialog();
            }
        }

        function isSelectedSpecifiedRole(selectedRoles, rolename) {
            return selectedRoles.some(function(d) { return d.name == rolename });
        }

        function submitRoles(newSelectedDatas) {
            newSelectedDatas.forEach(function(data, index, arr) {
                var idx = CAN_MODIFY_ROLES.indexOf('-' + data.name);
                if (idx > -1) {
                    CAN_MODIFY_ROLES[idx] = CAN_MODIFY_ROLES[idx].substr(1);
                }
            });

            modifyRoles(nodeId, CAN_MODIFY_ROLES.join(','));
        }

        function modifyRoles(nodeId, cmd) {
            window.service.modifyRole({"node_id":nodeId, "role":cmd}, function(res) {
                if(res.result==0) {
                    closeRoleDialog();
                    // 弹框时间设置较长的原因：为防止角色更改时间较长，弹框关闭后页面都还没有刷新的情况。
                    automaticPop.open({msg: '{% trans "Task sent successfully. Waiting for background to change the role" %}' }, null, 120000);
                } else {
                    dialog.errorMessage = res.result == 1 ? '{% trans "A node is modify role. Please wait and try again" %}' : '{% trans "Failed to change the role." %}';
                }
            }, function() {
                dialog.errorMessage = '{% trans "Server internal error" %}';
            });
        }

        function isRoleChange(oldRoles, newRoles) {
            if (oldRoles.length != newRoles.length) return true;
            var isChange = false;

            oldRoles.forEach(function(oldRole, i, arr) {
                var isNoChange = false;
                var oldname = oldRole.name;
                newRoles.forEach(function(newRole, j, arr) {
                    if (oldname==newRole.name) {
                        isNoChange = true;
                        return false;
                    }
                });

                if (!isNoChange) {
                    isChange = true;
                    return true;
                }
            });

            return isChange;
        }

        function handleOnCancelEdit(evt) {
            roleDatas = cloneRoleDatas(oldRoleDatas);   // 取消编辑, 恢复为初始数据
            closeRoleDialog();
        }

        function closeRoleDialog() {
            dialog.closeDialog();
            prevAbdServer = true;
            isFrozenFinish = false;
        }
    }

    function isNodeChanged(list, item) {
        for (var i = 0; i < list.length; i++) {
            var k = list[i];
            if (k.ip === item.ip && k.status == item.status && k.role === item.role && k.node_transparent_status === item.node_transparent_status && k.cpu_model === item.cpu_model && k.cpu_count === item.cpu_count && k.max_disk_space === item.max_disk_space && k.max_memory === item.max_memory)
                return true;
        }
        return false;
    }


    function getClusterState() {
        if (clusterAjax) clusterAjax.abort();
        var pageStatus = {{ status }};
        var clusterNodeList = tableData;

        clusterAjax = window.service.queryClusterStatus(
            function (data) {
                if (!data || typeof data != 'object') return;
                if (data['status'] != pageStatus) {
                    console.log('cluster status change, will reload.');
                    reloadPage();
                    return;
                }
                if (data['status'] != 200) return;

                var nodeStateListNew = data['response'],
                    isChanged = nodeStateListNew.length != clusterNodeList.length;
                for (var i = 0; i < nodeStateListNew.length; i++) {
                    var stateNew = nodeStateListNew[i];
                    if (!isNodeChanged(clusterNodeList, stateNew)) {
                        isChanged = true;
                        break;
                    }
                }

                if (isChanged) {
                    reloadPage();
                    return;
                }

                startCheckClusterStatus();
            },

            function () {
                console.log("Fail to get cluster status, try to get the status again in 5 seconds.");
                startCheckClusterStatus();
            }
        );
    }


    function removeSystemTableRow(index,data){
        var forceDel = "False";
        var masterCount = 0;
        var masterOnlineCount = 0;

        function isNodeOnline(nodeStatus) {
            // "Node is Online": Its status is not "Offline" nor "Zookeeper Error"
            return nodeStatus != 'Offline' && nodeStatus != 'ERRNO_004';
        }

        for (var i = 0; i < tableData.length; i++) {
            if (tableData[i].role.search("master_server") != -1) {
                masterCount++;
                if (isNodeOnline(tableData[i].status)) {
                    masterOnlineCount++;
                }
            }
        }

        if (data.role.search('master_server') != -1 && isNodeOnline(data.status) && masterOnlineCount - 1 <= (masterCount - 1) / 2) {
            rsalert("{% trans 'Warning! Not enough master nodes online! No master node can be deleted!' %}");
            return;
        }


        if (data.status == "Offline") {
            ask(
                "{% trans 'Warning! The node that you want to delete is offline. Are you sure to delete it anyway?' %}",
                function () {
                    forceDel = "True";
                    deleteNode(forceDel,data,index);
                },
                null,
                "{% trans 'Info' %}"
            );

        } else if (data.status != "Online" && data.status != "Inactive") {
            var msg1 = rs.formatString("{% trans 'Warning！{0}{1} Deleting this node may cause errors. Proceed anyway?' %}", data.service_eror,data.description);
            ask(
                msg1,
                function () {
                    forceDel = "True";
                    deleteNode(forceDel,data,index);
                },
                null,
                "{% trans 'Info' %}"
            );

        } else {
            var askMsg = [];

            if (data.is_lic_master && data.is_search_head && global_params.usePhoenix) {
                askMsg.push("{% trans 'After the big data analysis P (query) node is deleted, the system will automatically change a P (storage) node to a query node and re-accelerate the statistics system. The query performance of the statistics system will be reduced in a short time. ' %}" +
                    "{% trans 'You need to re-authorize after deleting this statistics master authorization node. ' %}" +
                    "{% trans 'If there is no big data analysis P (storage) node in cluster and there is big data analysis S node in cluster, please change the report type to sailfish.' %}");

            } else if ((data.is_search_head && global_params.usePhoenix) || (data.is_sailfish_head && global_params.useSailfish)) {
                askMsg.push("{% trans 'The Big Data Analysis (Storage) node with the smallest ID in the cluster will automatically become the Query node, but user-defined queries and dashboards will be lost.' %}");
            }

            if (abdServerCount == 1 && isNodeOnline(data.status) && data.role.search('abd_server') != -1) {
                askMsg.push("{% trans 'There will be no API Monitor role in the cluster, and the API protection functions will not be available.' %}");
            }

            if ((data.role.search('log_server') != -1 && global_params.usePhoenix) || (data.role.search('sailfish') != -1 && global_params.useSailfish)) {
                askMsg.push('{% trans "Some log data will be lost." %}');
            }

            var nodeId = data.id || '';
            var msg = "{% trans 'Sure to delete the node ' %}" + nodeId+ "?";
            if (askMsg.length > 0) {
                msg = rs.formatString('{% trans "Deleting Node {0} will result in the following consequences. Are you sure to delete?" %}', nodeId) + '<br/><br/>';
                if (askMsg.length > 1) {
                    var olList = askMsg.map(function(s, i) { return (i + 1) + '. ' + s; });
                    msg += olList.join('<br/><br/>');
                } else {
                    msg += askMsg[0];
                }
            }

            ask(msg, function () {
                deleteNode(forceDel,data,index);
            }, null, "{% trans 'Info' %}");
        }

        function deleteNode(forceDel,data,index) {
            page.wait(true);
            service.preventQuerySystemStatus();
            service.preventQueryClusterStatus();
            window.service.deleteNode(
                {
                    "id": data.id,
                    "force_del": forceDel,
                    "role": data.role,
                    "action": "DELETE"
                },
                function(res){
                    if (res.status == 500) {
                        page.wait(false);
                        rsalert(res.response);
                        return;
                    }

                    var timeout = 66000;
                    var goto_wizard = false;
                    if (data.ip === location.host.split(':')[0]) {
                        timeout = 88000;
                        goto_wizard = true;
                    }
                    setTimeout(function(){
                        page.wait(false);
                        rsalert("{% trans 'Node has been deleted!' %}", function() {
                            if (goto_wizard){
                                top.location.href = 'http://' + location.host.split(':')[0] + ':20146/';
                            } else {
                                reloadPage();
                            }
                        });
                    }, timeout);
                },
                function () {
                    page.wait(false);
                    rsalert('{% trans "Failed to delete node!" %}');
                }
            );
        }
    }

    function reloadPage() {
        if (embedMode) {
            parent.postMessage('updateMenu', '*');
            setTimeout(function() {
                self.location.href = '/overview/';
            }, 100);
        } else {
            window.top.location.href = '/overview/';
        }
    }


    var clusterTimeout, clusterAjax;
    function startCheckClusterStatus() {
        clusterTimeout = setTimeout(getClusterState, 5000);
    }

    function stopCheckClusterStatus() {
        clearTimeout(clusterTimeout);
        if (clusterAjax) clusterAjax.abort();
    }


</script>
{% endblock %}
