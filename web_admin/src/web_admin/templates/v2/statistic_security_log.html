{% load i18n %}
<style>
    #__SECURITY_LOG__ > div[filters] span {
        margin-top: 10px;
        display:inline-block;
    }
    #ThisLogDetail .box {
        width: 650px;
    }

    #ThisLogDetail div[name="content"] {
        padding: 10px 0px 20px 0px;
    }

    #ThisLogDetail #__LOG_TAB_PANEL__ > .rs-TabBox > div {
        margin-left: 6px;
    }

    #ThisLogDetail .injectPayloadRow {
        margin-bottom: 10px;
    }

    #ThisLogDetail .injectPayloadRow  textarea[name="attackPayload"] {
        height: 106px !important;
        width: 86%;
        overflow-y: auto;
        color: #313741 !important;
        cursor: default !important;
    }

    #ThisLogDetail .injectPayloadRow > .security_alert_label {
        line-height: normal;
        margin-top: 4px;
    }

    #copy_attackPayload {
        float: right;
    }


    .highlight-color-red { color: #D40000; }
    .highlight-bgcolor-red { background-color: #D40000; color: white; }

    .highlight-color-oringe { color: #ff9737; }
    .highlight-bgcolor-oringe { background-color: #ff9737; color: white; }

    .highlight-color-purple { color: #8761d9; }
    .highlight-bgcolor-purple { background-color: #8761d9; color: white; }



    #ThisLogDetail .box [name=content] .security_alert{
        max-height: 340px;
        overflow-y: auto;
    }
    #ThisLogDetail .len {
        border-bottom: 1px solid #d6d7d9;
        padding-left: 18px;
    }

    #ThisLogDetail .len .security_alert_label,
    #ThisLogDetail .len .security_alert_info {
        color: #9e9e9e !important;
        font-size: 12px;
        text-align: left;
        width: auto;
        margin-left: 0px;
    }
    #ThisLogDetail rs\:TabBox {
        margin-bottom:0px;
    }
    #ThisLogDetail .log_detail_info {
        background-color: #f8f8f8;
        height:354px;
        overflow-y: auto;
    }

    #ThisLogDetail .log_detail_info .security_alert_info{
        width: 94%;
    }

    #__THREAT_ANALYSIS_DETAIL_BUTTON__ {
        position: absolute;
        right: 10px;
        top: 70px;
    }

</style>

{% block script %}
    <script language="JavaScript" type="text/javascript">
       

        function func_copy_attackPayload() {
            var data = rs('#ThisLogDetail').data;
            if (data.attackPayloadOrigin) {
                navigator.clipboard.writeText(data.attackPayloadOrigin);
            }
        }
    </script>
{% endblock %}

<rs:Form model="#securityModule.model" onInput="securityModule.handleOnSecurityLogModelChanged(event)">

                <section id="__SECURITY_LOG__">
                    <header>
                        <span>{% trans 'Security Log' %}</span>
                    </header>

                    <div name="description" class="description"></div>

                    <div>
                        <rs:DropBox name="sortedDropBox">
                            <button rs:role="handler" type="button" dropdown="dropdown">{% trans 'Sorted By' %}</button>
                            <rs:MultiSelectList
                                class="visibleScrollbar"
                                rs:role="dropLayer" name="dropList"
                                id="__SORT_SELECT__"
                                onCreateItem="securityModule.handleOnCreateSortItem(event)"
                                onChangeCheckedStatus="securityModule.handleOnChangeAttackTypes(event)"></rs:MultiSelectList>
                        </rs:DropBox>
                        <span>
                            <label>{% trans 'From' %}</label>
                            <rs:DatePicker name="dateStart" class="datepickerTimestamp"></rs:DatePicker>
                            <label>{% trans 'To' %}</label>
                            <rs:DatePicker name="dateEnd" class="datepickerTimestamp" dpCurrent="true">
                                <button type="button" dpCurrentBtn="" onclick="securityModule.setCurrentTime(event)">{% trans 'Now' %}</button>
                            </rs:DatePicker>

                        </span>
                        <span>
                            <rs:Label><rs:Checkbox name="autoRefresh" onChange="securityModule.handleClickAutoRefresh(event)"></rs:Checkbox>{% trans 'Auto Refresh' %}</rs:Label>
                            <button type="button" name="searchSecurity" onclick="securityModule.searchSecurityLog()">{% trans 'Search ' %}</button>
                        </span>
                    </div>

                    <div filters>
                        <span>
                            {% trans "Host Header" %}
                            <input type="text" name="hostName" id="hostName" onkeydown="securityModule.handleOnEnterSearch(event)" placeholder="{% trans 'Fuzzy Match' %}" maxlength="55">
                            </input>
                        </span>
                        <span>
                            {% trans "Path" %}
                            <input type="text" name="accessPath" id="accessPath" onkeydown="securityModule.handleOnEnterSearch(event)" placeholder="{% trans 'Fuzzy Match without args' %}" maxlength="1024">
                            </input>
                        </span>
                        <span>
                            {% trans "Client IP" %}
                            <input type="text" name="sourceIP" id="sourceIP" onkeydown="securityModule.handleOnEnterSearch(event)" placeholder="{% trans 'Fuzzy Match' %}" maxlength="47">
                            </input>
                        </span>
                        <span>
                            {% trans "Rule ID" %}
                            <input type="text" name="ruleID" id="ruleID" onkeydown="securityModule.handleOnEnterSearch(event)" placeholder="{% trans 'Exact Match' %}" maxlength="10">
                            </input>
                        </span>
        <!--                <span rs:visible="securityModule.model.isShowInjectAttackType">-->
                        <span id="injectAttackTypeSpan" rs:visible="PageManage.isShowInjectType">
                            {% trans "Inject Attack Types" %}
                            <rs:Select name="injectAttackType" id="injectAttackType">
                                </rs:Select>
                        </span>
                        <span id="ErrortokenTypeSpan">
                            {% trans "Token Exception Types" %}
                            <rs:Select name="ErrortokenType" id="ErrortokenType">
                                </rs:Select>
                        </span>
                    </div>

                    <div class="securityBtnGroups">
                        <button type="button" onclick="securityModule.exportException()">{% trans 'Export' %}</button>
                        <button type="button" onclick="securityModule.clearExceptionResult()">{% trans 'Clear ' %}</button>
                    </div>
                    <rs:Table id="securityTable" name="searchResult"
                              header="{% trans 'Time ' %}|{% trans 'Type' %}|{% trans 'Risk Level' %}|{% trans 'Request Method' %}|{% trans 'Host Header' %}|{% trans 'URL' %}|{% trans 'Client IP' %}|{% trans 'Error Code' %}|{% trans 'Rule ID' %}|{% trans 'Action' %}"
                              cells="time|type|injectRiskLevel|method|host|url|clientIP|errorCode|injectRuleID|invalidRequestAction"
                              emptyText="{% trans 'No result' %}"
                              onCreateHeaderCell="securityModule.handleOnCreateSecurityLogHeaderCell(event)"
                              onCreateRow="securityModule.handleOnCreateSecurityLogRow(event)"
                              onCreateCell="securityModule.handleOnCreateSecurityLogCell(event)"></rs:Table>
                    <rs:Pages id="SecurityPageList" currentPageStyleClass="page-active"
                              hasNavigation="false" onSelectPage="securityModule.handleOnSecurityLogSelectPage(event)">
                    </rs:Pages>
                </section>

            </rs:Form>


<rs:ModalDialog id="__ADDENCA_BOX__" name="addEncapBox"
                    okText="{% trans 'OK' %}"
                    cancelText="{% trans 'Cancel' %}"
                    onOK="securityModule.encapManager.save(event)"
                    onCancel="securityModule.encapManager.close(event)">
        <div name="content">
            <div class="security_alert">
                <div>
                    <div class="security_alert_label">{% trans "URL: " %}</div>
                    <div class="security_alert_info" name="encapUrl"></div>
                </div>
                <div class="security-margin">
                    <div class="security_alert_label">{% trans "Referer: " %}</div>
                    <div class="security_alert_info" name="encapReferer"></div>
                </div>
                <div>
                    <div class="security_alert_label">{% trans "Source: " %}</div>
                    <div class="security_alert_info" name="encapSource"></div>
                </div>
                <div class="rs-asp-common-marginTop20">
                    <p name="encapTips"></p>
                    <input type="text" name="encapPathReg" value="" />
                    <input type="hidden" name="encapeComment"/>
                </div>
            </div>
        </div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__ADDREQUEST_BOX__" name="addRequestBox"
                    okText="{% trans 'OK' %}"
                    cancelText="{% trans 'Cancel' %}"
                    onOK="securityModule.reqWhiteListManager.save(event)"
                    onCancel="securityModule.reqWhiteListManager.close(event)">
        <div name="content">
            <div class="security_alert">
                <div>
                    <div class="security_alert_label">{% trans "URL: " %}</div>
                    <div class="security_alert_info" name="requestUrl"></div>
                </div>
                <div>
                    <div class="security_alert_label">{% trans "Source: " %}</div>
                    <div class="security_alert_info" name="requestSource"></div>
                </div>
                <div class="rs-asp-common-marginTop20">
                    <p name="requestTips"></p>
                    <input type="text" name="requestPathReg" value="" />
                    <input type="hidden" name="requestComment"/>
                </div>
            </div>
        </div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__ADDWAF_BOX__" name="addWAFBox"
                    okText="{% trans 'OK' %}"
                    cancelText="{% trans 'Cancel' %}"
                    onOK="securityModule.wafManager.save(event)"
                    onCancel="securityModule.wafManager.close(event)">
        <div name="content">
            <div class="security_alert">
                <div>
                    <div class="security_alert_label">{% trans "URL: " %}</div>
                    <div class="security_alert_info" name="wafUrl"></div>
                </div>
                <div>
                    <div class="security_alert_label">{% trans "Source: " %}</div>
                    <div class="security_alert_info" name="wafSource"></div>
                </div>
                <div class="rs-asp-common-marginTop20">
                    <p name="wafTips"></p>
                    <input type="text" name="wafPathReg" maxlength="1024" value="" placeholder="{% trans 'Enter a regular expression' %}" />
                    <input type="text" name="wafRuleId" maxlength="1024" value="" placeholder="{% trans 'Rule ID must be a number.' %}" />
                </div>
                <div class="rs-asp-common-marginTop20" style="display: none">
                    <p>Args: </p>
                    <input type="text" name="wafArgsReg" value="" />
                    <input type="hidden" name="wafComment"/>
                </div>
            </div>
        </div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__ADD_MPP_REQUEST_BOX__" name="addMppRequestBox"
                    okText="{% trans 'OK' %}"
                    cancelText="{% trans 'Cancel' %}"
                    onOK="securityModule.mppManager.save(event)"
                    onCancel="securityModule.mppManager.close(event)">
        <div name="content">
            <div class="security_alert">
                <div>
                    <div class="security_alert_label">{% trans "URL: " %}</div>
                    <div class="security_alert_info" name="requestUrl"></div>
                </div>
                <div>
                    <div class="security_alert_label">{% trans "Source: " %}</div>
                    <div class="security_alert_info" name="requestSource"></div>
                </div>
                <div class="rs-asp-common-marginTop20">
                    <p name="requestTips"></p>
                    <input type="text" name="requestPathReg" value="" />
                    <input type="hidden" name="requestComment"/>
                </div>
            </div>
        </div>
    </rs:ModalDialog>

    <rs:ModalDialog id="ThisLogDetail" name="showLogDetail" okText="{% trans 'Close' %}">
                <div name="content">
                    <button id="__THREAT_ANALYSIS_DETAIL_BUTTON__" rs:visible="securityModule.threatAnalystManager.checkAllowClick()"  onclick="securityModule.threatAnalystManager.show()" ><i class="fa fa-balance-scale" aria-hidden="true" /></i>{% trans 'AI Threat Analyst' %}</button>
                    <rs:TabPanel id="__LOG_TAB_PANEL__" class="logTabPane" keepIndexWhenRefresh="true">
                    <div rs:name="{% trans 'Basic Details' %}" rsid="system_log_pane_base_info" id="base_detail">
                        <div class="security_alert">
                            <div>
                                <div class="security_alert_label">{% trans 'Request Method' %}</div>
                                <div class="security_alert_info" name="method"></div>
                            </div>
                            <div class="security-margin">
                                <div class="security_alert_label">{% trans 'Host Header' %}</div>
                                <div class="security_alert_info" name="host"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">URL</div>
                                <div class="security_alert_info" name="url"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Time ' %}</div>
                                <div class="security_alert_info" name="time"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Type' %}</div>
                                <div class="security_alert_info" name="type"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Risk Level' %}</div>
                                <div class="security_alert_info" name="injectRiskLevelLabel"></div>
                            </div>

                            <div>
                                <div class="security_alert_label">{% trans 'Protocol' %}</div>
                                <div class="security_alert_info" name="Protocol"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Client IP' %}</div>
                                <div class="security_alert_info" name="clientIP"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Client Port' %}</div>
                                <div class="security_alert_info" name="clientPort"></div>
                            </div>

                            <div rs:visible="!matchDeployMode('mirror')">
                                <div class="security_alert_label">{% trans 'Target IP' %}</div>
                                <div class="security_alert_info" name="destIP"></div>
                            </div>

                            <div rs:visible="!matchDeployMode('mirror')">
                                <div class="security_alert_label">{% trans 'Target Port' %}</div>
                                <div class="security_alert_info" name="destPort"></div>
                            </div>

                            <div>
                                <div class="security_alert_label">Referer</div>
                                <div class="security_alert_info" name="referer"></div>
                            </div>
                            <div rs:visible="!matchDeployMode('mirror')">
                                <div class="security_alert_label">{% trans 'Action' %}</div>
                                <div class="security_alert_info" name="invalidRequestAction"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">User Agent</div>
                                <div class="security_alert_info" name="userAgent"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Error Code' %}</div>
                                <div class="security_alert_info" name="errorCode"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Explanation' %}</div>
                                <div class="security_alert_info" name="explanation"></div>
                            </div>
                            <div class="injectPayloadRow">
                                <div class="security_alert_label">{% trans 'Inject Payload' %}</div>
                                <div class="security_alert_info">
                                    <textarea id="attackPayload" name="attackPayload" disabled="true"></textarea>
                                    <button id="copy_attackPayload" onclick="func_copy_attackPayload()" normal>{% trans 'copy' %}</button>
                                </div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Inject Location' %}</div>
                                <div class="security_alert_info" name="attackLocation"></div>
                            </div>
                            <div>
                                <div class="security_alert_label">{% trans 'Inject Rule ID' %}</div>
                                <div class="security_alert_info" name="injectRuleID"></div>
                            </div>
                            <div>
                                <div class="security_alert_label"></div>
                                <div class="security_alert_info" name="injectRuleInfo"></div>
                            </div>

                        </div>
                    </div>
                    <div rs:name="{% trans 'Request Header' %}" rsid="system_log_pane_req_header" rs:tab-visible="PageManage.check_log_visable.req_header" id="log_request_header">
                        <div class="len">
                            <div class="security_alert_label">{% trans 'Request Header Length' %}：</div>
                            <div class="security_alert_info" name="snap_reqheader_len"></div>
                        </div>
                        <div class="log_detail_info">
                            <p id="__LOG_REQHEADER_FLAG__"> </p>
                            <div class="security_alert_info" name="api_snap_reqheader"></div>
                        </div>
                    </div>

                    <div rs:name="{% trans 'Request body' %}" rsid="system_log_pane_req_body" rs:tab-visible="PageManage.check_log_visable.req_body" id="log_request_body">
                        <div class="len">
                            <div class="security_alert_label">{% trans 'Request Body Length' %}：</div>
                            <div class="security_alert_info" name="snap_reqbody_len"></div>
                        </div>
                        <div class="log_detail_info">
                            <p id="__LOG_REQBODY_FLAG__"> </p>
                            <div class="security_alert_info" name="api_snap_reqbody"></div>
                        </div>
                    </div>
                    <div rs:name="{% trans 'Response Header' %}" rsid="system_log_pane_resp_header" rs:tab-visible="PageManage.check_log_visable.resp_header" id="log_response_header">
                        <div class="len">
                            <div class="security_alert_label">{% trans 'Response Header Length' %}：</div>
                            <div class="security_alert_info" name="snap_respheader_len"></div>
                        </div>
                        <div class="log_detail_info">
                            <p id="__LOG_RESPHEADER_FLAG__"> </p>
                            <div class="security_alert_info" name="api_snap_respheader"></div>
                        </div>
                    </div>
                    <div rs:name="{% trans 'Response Body' %}" rsid="system_log_pane_resp_body" rs:tab-visible="PageManage.check_log_visable.resp_body" id="log_response_body">
                        <div class="len">
                            <div class="security_alert_label">{% trans 'Response Body Length' %}：</div>
                            <div class="security_alert_info" name="snap_respbody_len"></div>
                        </div>
                        <div class="log_detail_info">
                            <p id="__LOG_RESPBODY_FLAG__"> </p>
                            <div class="security_alert_info" name="api_snap_respbody"></div>
                        </div>
                    </div>
                    </rs:TabPanel>
                </div>

    </rs:ModalDialog>

    {% include 'v2/waf/ai_threat_analysis_detail.html' %}
    