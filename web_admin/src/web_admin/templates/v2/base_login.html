{% load i18n %}
{% load nav_tags %}
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta name="renderer" content="webkit">
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <link rel="icon" href="/static/img/favicon.ico?ghrdm={{ build_hash_and_layout_hash }}" mce_href="/static/img/favicon.ico" type="image/x-icon"/>
        <link rel="stylesheet" href="/static/css/RSFontAwesome/style.css?ghrdm={{ build_hash_and_layout_hash }}" />
        <link rel="stylesheet" href="/static/css/theme.css?ghrdm={{ build_hash_and_layout_hash }}" />
        <link href="/static/css/layout_theme.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
        <link rel="stylesheet" href="/static/css/login.css?ghrdm={{ build_hash_and_layout_hash }}" />
        <link href="/static/css/layout_login.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
        <script src="/static/js/utils.js?ghrdm={{ build_hash_and_layout_hash }}"></script>

        <title>{% block title %}{{ brand }}{% endblock %}</title>

        <rs:Service onMessage="handleOnGetServiceMessage(event)"></rs:Service>

        <style>
            {# inner-login #}
            html, body.inner-login { min-width: 100% !important; }
            body.inner-login { overflow: hidden; min-width:440px; width:440px; }
            body.inner-login .loginBg { display: none; }
            body.inner-login aside>div { overflow: hidden; }
            body.inner-login .loginPage footer { display: none; }
            body.inner-login .loginPage form { padding:0px 10px; }
            body.inner-login p[name=forgetPassword] { display: none; }
            body.inner-login aside >div .wrapper { padding:0px; }
            body.inner-login aside section { margin-bottom: 0px; }
            body.inner-login aside h1 { margin-bottom:20px; }
            body.inner-login aside >div .wrapper .group { margin-top:20px; }
            body.inner-login aside >div .wrapper .group input[name=password] { margin-bottom:10px; }
            body.inner-login #id_captcha_1 { width:50%;}

            body.inner-login aside {
                float:none;
                display: block;
                margin-left: auto;
                margin-right: auto;
                padding: 0px 25px 20px 25px;
                box-sizing: border-box;
                width:100%;
                overflow: hidden;
            }

            body.inner-login aside >div .wrapper .group,
            body.inner-login div[name=captcha_pane],
            body.inner-login aside >div .wrapper .totpPane{
                padding: 0px 10px;
            }

            body.inner-login aside >div .wrapper input,
            body.inner-login aside >div .wrapper button {
                height: 36px;
            }

            body.inner-login #__BACK_LOGIN_BTN__ { display: none; }
            body.inner-login .resetPassword .notice { margin-top:0px; margin-bottom:10px; }
            body.inner-login aside > div .wrapper input { margin-bottom:15px; }
            {# end inner-login #}
        </style>

    </head>

    <body>
        <div class="loginBg"></div>
        <aside>
            <div>
                <div class="wrapper loginPage">
                    {% block content %}{% endblock %}
                    <footer>
                        <!-- &copy; {{ copyright_year }} {% trans 'ForceShield' %} -->
                        <span class="customProductNameFooter"></span>
                    </footer>
                </div>
            </div>
        </aside>

        <script type="text/javascript" src="{% url 'javascript-catalog' %}"></script>
        <script language="JavaScript" src="/static/js/browserAdapter.js"></script>
        <script language="JavaScript" src="/static/js/rs.js" ready="initPage()" rsTag="true"></script>
        <script language="JavaScript" src="/static/js/Validation.js"></script>
        <script language="JavaScript" src="/static/js/purviewControl.js"></script>
        <script src="/static/js/sha1.js" charset="UTF-8"></script>
        <script src="/static/js/const.js"></script>
        <script language="JavaScript">
            {% autoescape off %}
            var global_params = {
                userName:           {{ user.username|to_json }},
                clusterLanguage:    {{ cluster_language|to_json }},

            };
            {% endautoescape %}

            // inner弹框：1,true,null
            // 跳转登录：1, false, null

            // if(self!=top && !top.document.querySelector('#__SESSION_CHECK_DIALOG__[show=true]') &&
            //         (top.location.pathname.indexOf('statistics_app')>-1
            //         || (top.location.pathname.indexOf('overview')>-1 && location.search.indexOf('node_ip')>-1))) {
            //     top.location = String(self.location.href).split('?')[0]+'?next='+top.location.href;
            // }

            rs.plugin({'Service':'/static/js/rs-customized/net.Service.js'});

            function initPage(){}

            if (typeof console === "undefined" || typeof console.log === "undefined") {
                console = {};
                console.log = function() {};
            }
            if ({{ is_build_debug }} != 1) {
                console.log = function() {};
                console.info = function() {};
            }

            function markError(elem) {
                if (elem) {
                    elem.attr('error', '')
                    if (elem.focus) elem.focus();
                    if (elem.selectionStart!=null && elem.selectionEnd!=null
                        && typeof elem.selectionStart!='undefined' && typeof elem.selectionEnd!='undefined') {
                        elem.selectionStart=elem.value.length;
                        elem.selectionEnd=elem.value.length;
                    }
                }
            }

            function cancelMarkError(elem) {
                elem.attr('error', null);
            }

            function bindTextEvent() {
                var inputControls= rs('input[type=text]');
                var textareaControls= rs('textarea');
                if (inputControls) inputControls.unbind('change', handleChangeValue2Trim).bind('change', handleChangeValue2Trim);
                if (textareaControls) textareaControls.unbind('change', handleChangeValue2Trim).bind('change', handleChangeValue2Trim);
            }

            function handleChangeValue2Trim(evt) {
                evt.stopPropagation();
                var elem = evt.currentTarget;
                elem.value = elem.value.trim();
                elem.trigger(rs.Event('input'));
            }

            /**
             * 防止升级后，仍然使用的是缓存的图片
             * 
             * logo：无logo的，g_const.logoUrlInLogin === ''
             * 背景图：不需要背景图的，g_const.bgUrlInLogin === ''
            */
            (function loadImgsAvoidCaching() {
                var DEFAULT_IMG_URL = {
                    LOGO:  '/static/img/logo.png',
                    BG: '/static/img/login_bg.jpg'
                };

                var random = '?ghrdm={{ build_hash_and_layout_hash }}';

                // 获取logo
                var logoUrl = DEFAULT_IMG_URL.LOGO;
                if (g_const.login && g_const.login.logoUrl !== undefined) logoUrl = g_const.login.logoUrl;
                if (logoUrl && rs('.logo')) rs('.logo').style.backgroundImage = 'url(' + logoUrl + random + ')';

                // 获取背景图
                var bgUrl = DEFAULT_IMG_URL.BG;
                if (g_const.login && g_const.login.bgUrl !== undefined) bgUrl = g_const.login.bgUrl;
                if (bgUrl && rs('.loginBg')) rs('.loginBg').style.backgroundImage = 'url(' + bgUrl + random + ')';
            })();

        </script>
        {% block script %}{% endblock %}

    </body>
</html>
