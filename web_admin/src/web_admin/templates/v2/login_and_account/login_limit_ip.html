{% load nav_tags %}
{% load i18n %}

<section rsid="web_console_allow_ip_section" rs:visible="!global_params.inK8s && !(global_params.inContainer && global_params.isCloudMode)">
    <header>
        <span>{% trans 'Login limit on user source address' %}</span>
        <p>{% trans 'Remote login to the WebConsole or ssh is only allowed from the following addresses.' %}</p>
    </header>
    <ul>
        <li>
            <label>{% trans "Enable" %}</label>
            <rs:Checkbox
                name="web_console_allow_ip_enabled"
                id="__WEB_CONSOLE_ALLOW_IP_ENABLED__"
                rsid="web_console_allow_ip_enabled"
                onChange="LoginLimitIpManager.changeEnableStatus(event)"
            > </rs:Checkbox>
        </li>
        <li>
            <div id="allowIPPane" rs:visible="LoginLimitIpManager.getFillData().web_console_allow_ip_enabled">
                <rs:Table id="__WEB_CONSOLE_ALLOW_IP__" name="web_console_allow_ip" editable="true"
                          header="{% trans 'Type' %}|{% trans 'Address' %}|{% trans 'Netmask' %}/{% trans 'Prefix Length' %}|{% trans 'Expiration time' %}|{% trans 'Comment' %}|{% trans 'Actions' %}"
                          cells="type|ip|mask|period|comment|"
                          emptyText="{% trans 'No result' %}"
                          onCreateCell="LoginLimitIpManager.createCell(event)"
                          onDataChange="LoginLimitIpManager.rowChange(event)"></rs:Table>
                <div class="rsTableFooter" v:if="editable">
                    <button type="button" class="rsTableIncreaseButton" rsid="bt_addSystemAllowIP" onclick="LoginLimitIpManager.addRow()">{% trans 'New Configuration' %}</button>
                </div>
            </div>
        </li>
        <li>
            <label></label>
            <button type="button" class="inlineBlock" onclick="LoginLimitIpManager.save(event)"
                    id="allowIPSaveBtn"
                    v:if="editable">{% trans 'Save ' %}</button>
        </li>
    </ul>
</section>


<script language="javascript">
    function remote2ui(remoteList) {
        remoteList.forEach(function(data) {
            if (data.type === 'mac') {
                data.ip = data.mac;
            }

            if (data.mask === undefined) {
                data.mask = '';
            }
        });
    }

    function ui2remote(uiList) {
        uiList.forEach(function(data) {
            if (data.type === 'mac') {
                data.mac = data.ip;
            }
        });
    }


    var LoginLimitIpManager = (function() {
        {% autoescape off %}
        var lock_user_setting = {{lock_user_setting|to_json}};
        var web_console_allow_ip = {{ web_console_allow_ip|to_json }};
        var web_console_allow_ip_enabled = {{ web_console_allow_ip_enabled|to_json }};
        remote2ui(web_console_allow_ip)
        {% endautoescape %}


        var COMMENT_MAX_LEN = 1024;

        var _curFieldMap = {
            IP_TYPE: 'type',
            IP: 'ip',
            MASK: 'mask'
        };

        var IP_PLACEHOLDER = {
            IP: {
                ip: '{% trans "IP Address" %}',
                mac: '{% trans "MAC Address" %}'
            },
            MAC: {
                ip: '{% trans "Netmask" %}/{% trans "Prefix Length" %}',
                mac: '---'
            }
        };

        var moduleData = {
            web_console_allow_ip: formatTableDatas(),
            web_console_allow_ip_enabled: web_console_allow_ip_enabled,
            lock_limit_enable: lock_user_setting.lock_limit_enable,
            lock_limit_days: lock_user_setting.lock_limit_days
        };

        var table;

        return {
            getFillData: function() {
                return moduleData;
            },
            init: init,
            changeEnableStatus: changeEnableStatus,
            addRow: addRow,
            createCell: createCell,
            rowChange: handleRowChange,
            save: save
        };



        /**----------------- 私有方法 -----------------*/
        
        function init() {
            table = rs('#__WEB_CONSOLE_ALLOW_IP__');
        }

        function formatTableDatas() {
            if (web_console_allow_ip instanceof Array) {
                return web_console_allow_ip.map(function(ele){
                    // 选择“永久”时，没有开始结束的键值对，需要生成空字符串
                    if (ele.time_begin === undefined) ele.time_begin = '';
                    if (ele.time_end === undefined) ele.time_end = '';
                    if (ele.time_begin.indexOf('T') > 0) ele.time_begin = ele.time_begin.replace('T', ' ');
                    if (ele.time_end.indexOf('T') > 0) ele.time_end = ele.time_end.replace('T', ' ');
                    return ele;
                });
            }
            return web_console_allow_ip;
        }

        function addRow(evt) {
            cancelIPRowError();
            if (table.hasAttribute('error')) table.attr('error', null);
            table.addRow({
                ip:'',
                mask:'',
                time_begin: '',
                time_end: '',
                forever: true,
                comment:''
            });
        }

        function handleRowChange(event) {
            updateAllowIpSaveBtn();
        }

        function createCell(evt){
            evt.preventDefault();
            var row = evt.row;
            if (!evt.rowData[_curFieldMap.IP_TYPE]) {
                evt.rowData[_curFieldMap.IP_TYPE] = 'ip';
            }
            var typeValue = evt.rowData[_curFieldMap.IP_TYPE];
            switch(evt.index){
                case 0:
                    var items = [
                        { key:'ip', value:'{% trans "IP Address" %}' },
                        { key:'mac', value:'{% trans "MAC Address" %}' }
                    ];
                    createSelect({ name:evt.key }, evt.data || 'ip', items)
                        .bind('change', function(et) {
                            var curType = this.value;

                            // 更新Address列中input的placeholder
                            var ipElem = row.named(_curFieldMap.IP);
                            if (ipElem) ipElem.placeholder = IP_PLACEHOLDER.IP[curType];

                            // Netmask / Prefix leng 列的input状态
                            var maskElem = row.named(_curFieldMap.MASK);
                            if (maskElem) {
                                maskElem.value = '';
                                maskElem.placeholder = IP_PLACEHOLDER.MAC[curType];
                                maskElem.disabled = curType === 'mac';
                            }
                        }).appendTo(evt.element).bind('change', cancelIPRowError);
                    break;

                case 1:
                    var ipInput = createTextInputbox(evt.key, evt.data, IP_PLACEHOLDER.IP[typeValue]);
                    ipInput.appendTo(evt.element).bind('input', cancelIPRowError);
                    break;
                case 2:
                    var maskInput = createTextInputbox(evt.key, evt.data, IP_PLACEHOLDER.MAC[typeValue]);
                    if (typeValue === 'mac') {
                        maskInput.disabled = true;
                        var inputElem = maskInput.child('input[name="'+evt.key+'"]');
                        if (inputElem) {
                            inputElem.disabled = true;
                        }
                     }
                    maskInput.appendTo(evt.element).bind('input', cancelIPRowError);
                    break;
                case 3:
                    createPeriod(evt);
                    break;
                case 4:
                    createTextInputbox(evt.key, evt.rowData.comment, '{% trans "Comment" %}').appendTo(evt.element).bind('input', function(e){
                        cancelIPRowError();
                        evt.rowData.comment = e.target.value;
                    });
                    break;
                case 5:
                    if (!editable) return;
                    createDeleteLink(function(evt1){
                        cancelIPRowError();
                        table.removeRow(evt.row.index);
                    }).appendTo(evt.element);
                    break;
            }
        }


        function changeEnableStatus(evt) {
            var checked = evt.currentTarget.checked;
            moduleData.web_console_allow_ip_enabled = checked;
            var elem = rs('#allowIPPane');
            checked ? elem.show() : elem.hide();
            updateAllowIpSaveBtn();
        }


        function updateAllowIpSaveBtn() {
            var checked = rs('#__WEB_CONSOLE_ALLOW_IP_ENABLED__').checked;
            var ipBlank = rs('#__WEB_CONSOLE_ALLOW_IP__').data.length === 0;
            rs('#allowIPSaveBtn').disabled = checked && ipBlank;
        }


        function createPeriod(evt) {
            // 开始时间
            var begin = createDateTime('time_begin', evt.rowData.time_begin, '{% trans "Start" %}')
            .appendTo(evt.element).bind('change', cancelIPRowError);
            begin.disabled = !evt.rowData.time_begin && !evt.rowData.time_end;
            begin.child('input[name="time_begin"]').disabled = !evt.rowData.time_begin && !evt.rowData.time_end;

            // 结束时间
            var end = createDateTime('time_end', evt.rowData.time_end, '{% trans "End" %}')
            .appendTo(evt.element).bind('change', cancelIPRowError);
            end.disabled = !evt.rowData.time_begin && !evt.rowData.time_end;
            end.child('input[name="time_end"]').disabled = !evt.rowData.time_begin && !evt.rowData.time_end;

            // “永久” 复选框
            var label = rs.XElement('Label').appendTo(evt.element);
            var enable = createCheckbox({ name: 'forever' }, { rsid: 'forever'})
            .appendTo(label).bind('change', function(et) {
                cancelIPRowError();
                if (et.target.checked === true) {
                    begin.child('input[name="time_begin"]').disabled = true;
                    end.child('input[name="time_end"]').disabled = true;
                    return;
                }
                begin.child('input[name="time_begin"]').disabled = false;
                end.child('input[name="time_end"]').disabled = false;
            });
            if (evt.rowData.forever === undefined) {
                evt.rowData.forever = !evt.rowData.time_begin && !evt.rowData.time_end;
            }
            enable.checked = evt.rowData.forever;
            label.appendChild(rs.Text('{% trans "Never" %}'));
        }

        function submitWebConsoleAllowIP() {
            var datas = table.data;
            ui2remote(datas)
            var allow_ip_checkbox = rs('#__WEB_CONSOLE_ALLOW_IP_ENABLED__');
            var params = datas.map(function(item) {
                if (item.forever) item.time_begin = item.time_end = '';
                delete item.forever;
                return item;
            });
            service.saveWebConsoleAllowIP({ 'enabled': allow_ip_checkbox.checked, 'allowIP': params }, function(res) {
                if (!res) return rsalert('{% trans "Response data exception" %}');
                if (res.save_success) {
                    rsalert('{% trans "Saved successfully." %}', function() {
                        window.location.reload()
                    });
                } else {
                    rsalert(res.error_msg || '{% trans "Failed to save." %}', function () {
                        window.location.reload()
                    });
                }
            }, function() {
                if (allow_ip_checkbox.checked) {
                    rsalert('{% trans "Saved successfully." %}', function () {
                        window.location.reload()
                    });
                } else {
                    rsalert('{% trans "Request exception" %}', function () {
                        window.location.reload()
                    });
                }
            });
        }

        function save(evt) {
            var datas = table.data;
            var isPass = validateIPList(datas, table);
            var allow_ip_checkbox = rs('#__WEB_CONSOLE_ALLOW_IP_ENABLED__');
            var warn_msg = '{% trans "After confirm, only source address in cluster or white list can access system manage page and remote login" %}';

            if (!isPass) return;

            if (!datas.length && allow_ip_checkbox.checked)
                return rsalert('{% trans "White list can not blank" %}')

            if (!allow_ip_checkbox.checked) {
                submitWebConsoleAllowIP();
                return;
            }

            ask(warn_msg, submitWebConsoleAllowIP, null, '{% trans "Warning" %}', 3)
        }

        function validateIPList(datas, table) {
            var passIPs = [];
            var err_flag = true;
            var isPass = true;
            rs.each(datas, function(i, data) {
                if (!data) return false;
                if (data.type === 'mac') {
                    var error = Validation.MAC(data.ip, '{%  trans "Please enter a valid source address" %}');
                } else if (data.ip.indexOf(':') >= 0) {
                    var error = Validation.IP_AND_PREFIX(data.ip + '/' + data.mask);
                } else {
                    var error = Validation.IPV4(data.ip, '{% trans "Please enter a valid source address" %}') || Validation.MASK(data.mask);
                }
                var comment_error = '';
                if (data.comment && data.comment.length>COMMENT_MAX_LEN) comment_error = rs.formatString('{% trans "Comment cannot exceed {0} characters" %}',COMMENT_MAX_LEN);

                var repeat_error = '';
                var key = key += data.ip + '_' + data.mask + '_' + data.time_begin + '_' + data.time_end + '_' + data.forever.toString();
                if (data.forever) key = data.ip + '_' + data.mask + '_' + data.forever.toString();
                if (data.type == 'ip') {
                    key += data.mask
                }
                key = key.toLowerCase().replace(/-/g, ':');
                if (passIPs.indexOf(key) > -1) {
                    repeat_error = '{% trans "Duplicate source addresses" %}';
                } else {
                    passIPs.push(key);
                }

                var ip_error = '';
                if (data.type == 'ip' && data.mask){
                    var a = data.ip.split('.');
                    a = ((parseInt(a[0])<<24)+(parseInt(a[1])<<16)+(parseInt(a[2])<<8)+parseInt(a[3]))>>>0;
                    var b = data.mask.split('.');
                    b = ((parseInt(b[0])<<24)+(parseInt(b[1])<<16)+(parseInt(b[2])<<8)+parseInt(b[3]))>>>0;
                    if ((a|b)>>>0 != b) {
                        a &= b;
                        a = (a>>>24) + '.' + ((a>>>16)&255) + '.' + ((a>>>8)&255) + '.' + (a&255);
                        ip_error = '{% trans "IP should be " %}' + a + '{% trans ", because the netmask is " %}' + data.mask;
                    }
                }

                // 校验有效期
                var time_error = '';
                if (data.forever === false) {
                    if (data.time_begin === '' || data.time_end === '') time_error = '{% trans "Please select an Expiration time" %}';
                    if (data.time_begin && (data.time_begin === data.time_end)) time_error = '{% trans "The start and end periods cannot be the same" %}';

                    var time_begin = new Date(data.time_begin).getTime();
                    var time_end = new Date(data.time_end).getTime();
                    var difference = time_begin - time_end;
                    if (difference > 0) time_error = '{% trans "The end period cannot be earlier than the start period" %}';
                    if (difference === 0) time_error = '{% trans "The start and end validity periods cannot be the same" %}';
                }

                error = error||comment_error||repeat_error||ip_error||time_error;
                if (error) {
                    if (err_flag) {
                        {# mark error row only once #}
                        rsalert(error);
                        markRow(i);
                        err_flag = false;
                    }
                    isPass = false;
                    return false;
                }
            });

            return isPass;
        }


        function markRow(index) {
            var row = table.rows[index];
            row.attr('error', '');
        }

        function cancelIPRowError() {
            var errorRow = table.child('div[row=row][error]');
            if (errorRow) errorRow.attr('error', null);
        }

    })();
    
    basicConfigTabModules.push(LoginLimitIpManager);
</script>