{% extends "v2/base.html" %}
{% load nav_tags %}
{% load i18n %}

{% block title %}{% trans 'Login/Account'%}{% endblock %}

{% block navigation %}{% trans 'Login/Account' %}{% endblock %}

{% block headerScript %}
<script language="JavaScript" type="text/javascript" src="/static/js/sha1.js" charset="UTF-8"></script>

<script language="JavaScript" type="text/javascript">

    /**================================================================================ 
     * 全局耦合
    *================================================================================*/

    {% autoescape off %}
    var totp_enabled = {{ totp_enabled|to_json }};
    var qps_enabled = {{ qps_enabled|to_json }} || false;
    var password_config = {{ password_config|to_json }};
    {% endautoescape %}

    var editable = auth('Account_Default_Configurations', 'w');

    var EDIT_TYPE = {
        NEW: 'NEW',
        MODIFY:'MODIFY'
    };


    rs.plugin({
        File: '/static/js/rs/ui.File.js',
        ModalDialog: '/static/js/rs/ui.ModalDialog.js',
        DatePicker: '/static/js/rs-customized/ui.DatePicker.js',
        MultiSelect: '/static/js/rs-customized/ui.MultiSelect.js'

    }).plugin('SortIcon', function(_) {
        var ON_SORT = 'sort';
        var type = param('type');
        var index = param('index');
        var parent = param('parent');

        var sortUp = rs.Element('i').addClass('fa fa-sort-up');
        var sortDown = rs.Element('i').addClass('fa fa-sort-down');
        var _this = this.attr({rule: type, rsid: "RsSortButton"}).addClass('RsSortButton').bind('click', function(cevt) {
                var rule = this.attr('rule');
                var isAsc = rule == 'asc' || rule == '';
                parent.child('[rsid="RsSortButton"]').attr('rule','');
                if (isAsc) {
                    this.attr('rule','desc');
                    rule = 'desc';
                } else {
                    this.attr('rule','asc');
                    rule = 'asc';
                }
                var evt = _.Event(ON_SORT);
                evt.sortBy = rule;
                evt.index = index;
                _this.trigger(evt);
            }).append(sortUp).append(sortDown);
    });


    
    // 初始化「基础配置」页签模块，其他页签通过include引入的Vue模块
    var basicConfigTabModules = [];
    function initPage() {
        var pageData = {};
        basicConfigTabModules.forEach(function(m) {
            if (typeof m['getFillData'] === 'function') {
                var modelData = m['getFillData']();
                rs.merge(pageData, modelData);
            }

            if (typeof m['init'] === 'function') {
                m['init']();
            }
        });

        document.body.fill(pageData);
    }
    





    /**================================================================================ 
     * QRCode弹框 - 「双因素认证」和「账户列表」中的二维码都需要
    *================================================================================*/

    var QRCodeDialog = (function() {

        var dialog, SHASwitch;

        var userAlgorithmCacheMap = {};
        var curUserName = '';
        var isNeedVerify = false;


        var publicProps = {
            init: function() {
                dialog = rs('#__2FA_DIALOG__');

                // SHA选择按钮
                SHASwitch = new function(switchGroup) {
                    var selections = switchGroup.child('button');
                    var selectedAlgorithm = 'SHA1';

                    this.toggle = function(isShow) {
                        switchGroup.css('display', isShow ? 'block' : 'none');
                    }

                    this.getSelectedAlgorithm = function() {
                        return selectedAlgorithm;
                    }

                    this.select = function(index) {
                        selections[index].click();
                    }

                    this.focus = function(value) {
                        for (var i = 0; i < selections.length; i++) {
                            var bt = selections[i];
                            if (bt.attr('data-value') === value) {
                                focus(bt);
                                return;
                            }
                        }
                    }

                    function focus(target) {
                        selections.removeClass('active');
                        target.addClass('active');
                        selectedAlgorithm = target.attr('data-value');
                    }

                    function select(target) {
                        focus(target);
                        updateCurrentAlgorithmnInfo(selectedAlgorithm);
                    }

                    selections.bind('click', function(evt) {
                        if (evt.target.attr('data-value') === selectedAlgorithm) return;
                        select(evt.target);
                    });

                }(rs('#shaSwitch'));
            },

            getInstance: function() {
                return dialog || rs('#__2FA_DIALOG__')
            },
            open: open,
            close: close,
            clearAlgorithmCache: clearAlgorithmCache,
            handleOk: handleOk,
            handleCancel: handleCancel,
            markError: mark2FAError
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;



        /**----------------------- 私有属性/方法 --------------------*/

        function open(username, isVerify) {
            curUserName = username || global_params.userName;
            isNeedVerify = isVerify;

            var cached = userAlgorithmCacheMap[curUserName];
            if (cached) {
                showDialog(cached); 
            } else {
                service.get2FAConfigList({
                    username: curUserName
                }, function(response) {
                    if (!response) return rsalert('{% trans "Response data exception" %}');
                    var resp = typeof response === 'string' ? JSON.parse(response) : resp;

                    if (resp.result === 'ok') {
                        delete resp.result;
                        delete resp.message;
                        showDialog(resp);
                    } else {
                        rsalert(resp.message);
                    }
                    
                });
            }
        }

        function updateCurrentAlgorithmnInfo(algorithm) {
            var userAlgorithmMap = userAlgorithmCacheMap[curUserName];
            dialog.data = getDialogModelData(userAlgorithmMap, algorithm);
            dialog.updateProperty();
        }


        function showDialog(allAlgorithmMap) {
            userAlgorithmCacheMap[curUserName] = allAlgorithmMap;
            var curAlgorithm = allAlgorithmMap.algorithm;

            dialog.data = getDialogModelData(allAlgorithmMap, curAlgorithm);
            dialog.open(null, null, null, "{% trans 'Two-factor Authentication Code' %}");
            updateDialogStatus(curAlgorithm);
        }

        function getDialogModelData(algorithmMap, curAlgorithm) {
            var curAlgorithmInfo = algorithmMap[curAlgorithm];

            return Object.assign({
                token: '',
                name: curUserName
            }, curAlgorithmInfo, algorithmMap);
        }

        function updateDialogStatus(algorithm) {
            SHASwitch.focus(algorithm);
            SHASwitch.toggle(isNeedVerify);

            if (isSuperAdmin(curUserName)) {
                dialog.named('tokenItem').show();
                dialog.named('mdBottom').show();
                dialog.named('token').focus();
            } else {
                dialog.named('tokenItem').hide();
                dialog.named('mdBottom').hide();
            }
        }


        function handleOk(evt) {
            var formData = dialog.data;
            var username = formData.name;
            if (!isSuperAdmin(username)) return;

            if (Validation.STRING(formData.token)) {
                mark2FAError('{% trans "Please enter the authentication code" %}');
                return;
            }

            var data = Object.assign({}, formData, {
                username: username,
                algorithm: SHASwitch.getSelectedAlgorithm()
            });

            delete data.b64_qrcode;
            delete data.config_url;
            delete data.config_token;
            delete data.name;

            var verifyEvt = rs.Event('verify2FACode');
            verifyEvt.data = data;
            dialog.trigger(verifyEvt);
        }


        function handleCancel(evt) {
            if (isNeedVerify) {
                var cancelVerifyEvt = rs.Event('cancelVerify2FACode');
                dialog.trigger(cancelVerifyEvt);
            }

            close();
        }

        function close() {
            var curUserName = '';
            var isNeedVerify = false;
            dialog.closeDialog();
        }

        function clearAlgorithmCache() {
            userAlgorithmCacheMap = {};
        }

        function mark2FAError(msg) {
            dialog.errorMessage = msg;
            page.markError(dialog.named('token'));
        }

    })();
    


    /**================================================================================ 
     * 登录验证码
    *================================================================================*/
    var LoginCaptchaModule = (function() {
        {% autoescape off %}
        var captchaEnabled = {% if captcha %}true{% else %}false{% endif %};
        {% endautoescape %}

        var publicProps = {
            getFillData: function() {
                return {
                    captchaEnabled: captchaEnabled
                };
            },
            init: function() {},

            changeStatus: function(evt) {
                var elem = evt.currentTarget;
                var isEnable = elem.checked;
                window.service.checkCaptcha(
                    {enable: isEnable ? 1 : 0},
                    function(res){
                        page.wait(false);
                        if (res) {
                            if (res.result == 'OK') {
                                rsalert(isEnable ? "{% trans 'Login CAPTCHA enabled successfully' %}" : "{% trans 'Login CAPTCHA disabled successfully' %}");
                            } else {
                                rsalert(isEnable ? "{% trans 'Failed to enable Login CAPTCHA' %}" : "{% trans 'Failed to disable Login CAPTCHA' %}", function() {
                                    elem.checked = !isEnable;
                                });
                            }

                        } else {
                            rsalert("{% trans 'Operation error' %}");
                        }

                    },
                    function(){
                        page.wait(false);
                        rsalert("{% trans 'Operation error' %}");
                    }
                );
            }
        };
    
        basicConfigTabModules.push(publicProps);
        return publicProps;
    })();





    /**================================================================================ 
     * 禁止重复登录
    *================================================================================*/

    var NoRepeatLoginModule = (function() {
        {% autoescape off %}
        var kick_user_enabled = {{ kick_user_enabled|to_json }};
        {% endautoescape %}

        var publicProps = {
            getFillData: function() {
                return {
                    kickUserEnabled: kick_user_enabled
                };
            },
            init: function() {},

            changeStatus: function(evt) {
                var elem = evt.currentTarget;
                var isEnable = elem.checked;
                window.service.enableKickUser(
                    { enable: isEnable ? 1 : 0 },
                    function(resp) {
                        page.wait(false);
                        if (resp) {
                            if (resp.result == 'ok') {
                                rsalert(isEnable ? "{% trans 'No Repeat Login enabled successfully' %}" : "{% trans 'No Repeat Login disabled successfully' %}");
                            } else {
                                rsalert(isEnable ? "{% trans 'Failed to enable No Repeat Login' %}" : "{% trans 'Failed to disable No Repeat Login' %}",
                                        function() {
                                            elem.checked = !isEnable;
                                        });

                            }
                        } else {
                            rsalert("{% trans 'Operation error' %}");
                        }
                    },
                    function() {
                        rsalert("{% trans 'Operation error' %}");
                    });
            }
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;
    })();


    

    /**================================================================================ 
     * 系统会话过期时间
    *================================================================================*/        
    var SessionExpiredTimeModule = (function() {
        {% autoescape off %}
        var session_idle_timeout = {{ session_idle_timeout|to_json }};
        {% endautoescape %}

        var SESSION_TIMEOUT = {
            MIN:5,
            MAX:30
        };

        var publicProps = {
            getFillData: function() {
                return {
                    session_idle_timeout: session_idle_timeout
                };
            },
            init: function() {},

            handleSaveTimeout: function(evt) {
                evt.stopPropagation();
                var timeInput = rs('input[name=session_idle_timeout]');
                if (!timeInput) return;
                var val = timeInput.value;

                if (val.trim()!='' && (Validation.POSITIVE_INT(val) || val<SESSION_TIMEOUT.MIN || val>SESSION_TIMEOUT.MAX)) {
                    return rsalert(rs.formatString('{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', SESSION_TIMEOUT.MIN, SESSION_TIMEOUT.MAX));
                }

                service.saveSessionTimeout({session_idle_timeout:parseInt(val)}, function(res) {
                    if (res && res.result) {
                        rsalert('{% trans "System session expiration time is set successfully." %}');
                    } else {
                        rsalert('{% trans "System session expiration time setting failed." %}');
                    }
                });
            }
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;
    })();




    /**================================================================================ 
     * 密码安全设置
    *================================================================================*/

    var PasswordPolicyManager = (function() {

        var PASSWORD_LENGTH = {
            MIN: 8,
            MAX: 32
        };

        var PASSWORD_HISTORY_CHECK ={
            MIN: 0,
            MAX: 10
        };

        var PASSWORD_TIMEOUT = {
            MIN:1,
            MAX:365
        };

        var passwordRulePage = {
            password_period: password_config.period.toString(),
            password_length: password_config.password_length.toString(),
            complexity_upperWords: password_config.complexity.upper,
            complexity_lowerWords: password_config.complexity.lower,
            complexity_number: password_config.complexity.num,
            complexity_symbol: password_config.complexity.notation,
            include_user_name: password_config.include_user_name ? 'true' : 'false',
            history_check: password_config.history_check.toString()
        };


        return {
            model: passwordRulePage,
            
            setPasswordRules: function(evt) {
                evt.stopPropagation();

                // 校验
                var _len = passwordRulePage.password_length;
                if (_len) {
                    if (_len.trim() != '' && (Validation.POSITIVE_INT(_len) || _len < PASSWORD_LENGTH.MIN || _len > PASSWORD_LENGTH.MAX)) {
                        // return rsalert('密码长度请输入大于等于8并且小于等于32的整数。');
                        return rsalert('{% trans "Please enter an integer greater than or equal to 8 and less than or equal to 32 for the password length." %}');
                    }
                }

                if (!(passwordRulePage.complexity_lowerWords || passwordRulePage.complexity_upperWords ||
                    passwordRulePage.complexity_number || passwordRulePage.complexity_symbol)
                ) {
                    // return rsalert('密码复杂度请至少启用一项。');
                    return rsalert('{% trans "Please enable at least one password complexity option." %}');
                }

                var _history = passwordRulePage.history_check;
                if (_history) {
                    if (_history.trim() != '' && _history != 0 && (Validation.POSITIVE_INT(_history) || _history < PASSWORD_HISTORY_CHECK.MIN || _history > PASSWORD_HISTORY_CHECK.MAX)) {
                        // return rsalert('历史密码检查策略请输入大于等于0并且小于等于10的整数。');
                        return rsalert('{% trans "Please enter an integer greater than or equal to 0 and less than or equal to 10 for the historical password check strategy." %}');
                    }
                }

                var _period = passwordRulePage.password_period;
                if (_period) {
                    if (_period.trim() != '' && (Validation.POSITIVE_INT(_period) || _period < PASSWORD_TIMEOUT.MIN || _period > PASSWORD_TIMEOUT.MAX)) {
                        // return rsalert('有效期请输入大于等于1并且小于等于365的整数。');
                        return rsalert('{% trans "Please enter an integer greater than or equal to 1 and less than or equal to 365 for the validity period." %}');
                    }
                }

                var params = {
                    period: passwordRulePage.password_period,
                    password_length: passwordRulePage.password_length,
                    complexity: {
                        upper: passwordRulePage.complexity_upperWords,
                        lower: passwordRulePage.complexity_lowerWords,
                        num: passwordRulePage.complexity_number,
                        notation: passwordRulePage.complexity_symbol
                    },
                    history_check: passwordRulePage.history_check,
                    include_user_name: passwordRulePage.include_user_name === 'true' ? true : false,
                }

                service.savePasswordPeriod({password_config: params}, function(res) {
                    if (res && res.result) {
                        // rsalert('密码安全设置保存成功。');
                        rsalert('{% trans "Password Security saved successfully." %}');
                    } else {
                        // rsalert('密码复杂度保存失败。');
                        rsalert('{% trans "Failed to save password Security." %}');
                    }
                });
            }
        }

    })();





    /**================================================================================ 
     * 登录失败锁定
    *================================================================================*/
    var LoginFailureLockModule = (function() {
        {% autoescape off %}
        var login_failed_period = {{login_failed_period|to_json}};
        var login_failed_counts = {{login_failed_counts|to_json}};
        var lock_period = {{lock_period|to_json}};
        {% endautoescape %}


        var LOGIN_FAILED_PERIOD = {
            MIN:1,
            MAX:60
        };

        var LOGIN_FAILED_COUNTS = {
            MIN:2,
            MAX:10
        };
        
        var LOCK_PERIOD = {
            MIN:1,
            MAX:3000
        };
        

        var publicProps = {
            getFillData: function() {
                return {
                    login_failed_period: login_failed_period,
                    login_failed_counts: login_failed_counts,
                    lock_period: lock_period
                };
            },
            init: function() {},

            save: function(evt) {
                evt.stopPropagation();
                var failedPeriod = rs('input[name=login_failed_period]');
                var failedCounts = rs('input[name=login_failed_counts]');
                var lockPeriod = rs('input[name=lock_period]');
                if (!failedPeriod || !failedCounts || !lockPeriod) return;
                var valFailedPeriod = failedPeriod.value;
                var valFailedCounts = failedCounts.value;
                var valLockPeriod = lockPeriod.value;

                if (valFailedPeriod.trim()!='' && (Validation.POSITIVE_INT(valFailedPeriod) || valFailedPeriod<LOGIN_FAILED_PERIOD.MIN || valFailedPeriod>LOGIN_FAILED_PERIOD.MAX)) {
                    return rsalert(rs.formatString('{% trans "Login failed period" %}' + ': ' + '{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', LOGIN_FAILED_PERIOD.MIN, LOGIN_FAILED_PERIOD.MAX));
                }
                if (valFailedCounts.trim()!='' && (Validation.POSITIVE_INT(valFailedCounts) || valFailedCounts<LOGIN_FAILED_COUNTS.MIN || valFailedCounts>LOGIN_FAILED_COUNTS.MAX)) {
                    return rsalert(rs.formatString('{% trans "Login failed counts" %}' + ': ' + '{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', LOGIN_FAILED_COUNTS.MIN, LOGIN_FAILED_COUNTS.MAX));
                }
                if (valLockPeriod.trim()!='' && (Validation.POSITIVE_INT(valLockPeriod) || valLockPeriod<LOCK_PERIOD.MIN || valLockPeriod>LOCK_PERIOD.MAX)) {
                    return rsalert(rs.formatString('{% trans "Lock period" %}' + ': ' + '{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', LOCK_PERIOD.MIN, LOCK_PERIOD.MAX));
                }

                param = {
                    login_failed_period:parseInt(valFailedPeriod),
                    login_failed_counts:parseInt(valFailedCounts),
                    lock_period:parseInt(valLockPeriod)
                }
                service.saveLoginFailure(param, function(res) {
                    if (res && res.result) {
                        rsalert('{% trans "Login failure lock configuration is set successfully." %}');
                    } else {
                        rsalert('{% trans "Login failure lock configuration setting failed." %}');
                    }
                });
            }
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;
    })();





    /**================================================================================ 
     * SSL证书
    *================================================================================*/

    var SSLModule = (function() {
        var certForm, certFile, keyFile;

        var publicProps = {
            getFillData: function() {},
            init: function() {
                certForm = rs('#__SYSTEM_CERT_FORM__');
                certFile = rs('#id_certfile');
                keyFile = rs('#id_keyfile');
                rs('#__CERT_DOWNLOAD__').bind('click', service.downloadSSLCert);
            },

            uploadCertificate: function() {
                var isCertPass = certFile.checkType();
                if (!isCertPass) return;

                var isKeyPass = keyFile.checkType();
                if (!isKeyPass) return;

                var uploadFormData = new FormData(certForm);
                service.uploadFile('/system/cert/', uploadFormData, handleLoadEvent, loadError);
            }
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;



        /**-------------- 私有方法 --------------*/

        function handleLoadEvent(result) {
            FileUploadResultHandler.process(result, function(res) {
                if(res.error){
                    rsalert(res.error);
                }else{
                    rsalert("{% trans 'New SSL certificate has been uploaded' %}", function() {
                        certFile.refresh();
                        keyFile.refresh();
                    });
                }
            });
        }

        function loadError() {
            rsalert(gettext('File upload verification failed. Please click [OK] to re-upload the file.'), function() {
                window.location.reload(true);
            }, null, true);
        }
    })();





    /**================================================================================ 
     * 双因素认证
    *================================================================================*/
    
    var TwoFactorAuthModule = (function() {

        var F2A_ACTION_CONST = {
            VERIFY: 'verify',
            DISABLE: 'disable'
        };

        var publicProps = {
            getFillData: function() {
                return {
                    totp_enabled: totp_enabled
                };
            },
            init: function() {
                listenQRDialog();
            },

            openQRCode: openQRCode
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;


        /**-----------------私有方法----------------------*/


        function listenQRDialog() {
            QRCodeDialog.getInstance()
                .bind('verify2FACode', function(evt) {
                    var params = Object.assign({ action: F2A_ACTION_CONST.VERIFY }, evt.data);
                    verify2FACode(params);
                })
                .bind('cancelVerify2FACode', function() {
                    rs('#__TOTP_ENABLED__').checked = false;
                });
        }

        function openQRCode(evt) {
            if (evt.target.checked) {
                QRCodeDialog.open(global_params.userName, true);
            } else {
                disable2FA();
            }
        }

        function verify2FACode(data) {
            service.req2FAService(data, function(res) {
                if (!res) return;
                if (res.result === 'failed') {
                    QRCodeDialog.markError(res.message);
                } else {
                    triggerQRCodeChange(true);
                    QRCodeDialog.close();
                    rsalert('{% trans "Two-factor authentication is turned on." %}');
                }
            });
        }

        function disable2FA() {
            service.req2FAService({action: F2A_ACTION_CONST.DISABLE}, function(res) {
                if (!res) return;
                if (res.result === 'failed') {
                    rsalert(res.message);
                } else {
                    QRCodeDialog.clearAlgorithmCache();
                    triggerQRCodeChange(false);
                    rsalert('{% trans "Two-factor authentication is off." %}');
                }
            });
        }

        function triggerQRCodeChange(enable) {
            var faStatuChangeEvt = rs.Event('faStatusChange');
            faStatuChangeEvt.enabled = enable;
            rs('#__TOTP_ENABLED__').trigger(faStatuChangeEvt);
        }

    })();





    /**================================================================================ 
     * 长期用户不使用锁定
    *================================================================================*/
    var UserLockModule = (function() {
        {% autoescape off %}
        var lock_user_setting = {{lock_user_setting|to_json}};
        {% endautoescape %}


        var USER_EXPIRE = {
            MIN:30,
            MAX:90
        };

        var moduleData = {
            lock_limit_enable: lock_user_setting.lock_limit_enable,
            lock_limit_days: lock_user_setting.lock_limit_days
        };

        var publicProps = {
            getFillData: function() {
                return {
                    lock_limit_enable: lock_user_setting.lock_limit_enable,
                    lock_limit_days: lock_user_setting.lock_limit_days
                };
            },
            init: function() {},

            changeEnableStatus: function(evt) {
                var checked = evt.currentTarget.checked;
                moduleData.lock_limit_enable = checked;
                var elem = rs('#lock_limit_enable_wrap');
                checked ? elem.show() : elem.hide();
            },
            save: function(evt) {
                var limitVal = document.named('lock_limit_days').value;
                var limitEnable = document.named('lock_limit_enable').checked;

                if (limitVal!='' && (Validation.POSITIVE_INT(limitVal) || limitVal<USER_EXPIRE.MIN || limitVal>USER_EXPIRE.MAX)) {
                    return rsalert(rs.formatString('{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', USER_EXPIRE.MIN, USER_EXPIRE.MAX));
                }
                service.lockUserSetting({
                    lock_limit_enable: limitEnable,
                    lock_limit_days: limitVal
                }, function(res) {
                    if (res && res.result) {
                        rsalert('{% trans "Saved successfully." %}');
                        var refreshUserTableEvt = rs.Event('refreshUserTableByLock');
                        rs('#__TIMELIMIT_ENABLED__').trigger(refreshUserTableEvt);
                    } else {
                        rsalert('{% trans "Failed to save." %}');
                    }
                })
            }
        };

        basicConfigTabModules.push(publicProps);
        return publicProps;

    })();
    

</script>

{% endblock %}



{% block script %}
<script language="JavaScript" type="text/javascript">
    function handleOnInitRoles() {
        RSVue.emit('initRoles');
    }

    function tabPanelChange(e) {
        RSVue.emit('tabPanelChange');
    }

    var app = new RSVue({
        el: '#accountManagementTabPanel'
    });
</script>
{% endblock %}




{% block content %}

    <style>

        body > .content.mainContent {
            min-width: 1300px;
        }

        /* 用户列表 */
        rs\:Table[name="userList"] div[header] > div:last-child {
            width: 160px;
        }

        rs\:Table[name=userList] div[row] div[table-cell][name="_create_time"],
        rs\:Table[name=userList] div[row] div[table-cell][name="_lastest_login_time"],
        rs\:Table[name=userList] div[row] div[table-cell][name="_password_modify_time"] {
            white-space: pre-line;
            word-break: break-word;
        }
    
        #__USER_BOX__ div.box { width:550px; }
        #__USER_BOX__ .passwordInput { -webkit-text-security: none !important; }
        #__USER_BOX__ rs\:DatePicker {
            margin: 0;
        }
    
        #__USER_BOX__ rs\:DatePicker #validity_period {
            width: 87% !important;
            border: 0px !important;
        }
    
        html[lang=en] rs\:ModalDialog#__USER_BOX__ label {
            width: 27%;
        }
    
        #refreshUsersBtn {
            position: absolute;
            top: 20px;
            right: 20px;
        }
    
        .permanent_style {
            display: inline-block;
            width: 75px;
        }
        .permanent_style rs\:Label {
            margin: 0;
            margin-left: 15px;
        }
        .permanent_style rs\:Label rs\:Checkbox {
            top: -1px;
            margin-right: 5px;
        }
    
    
        html[lang="en"] .permanent_style rs\:Label {
            margin: 0;
            margin-left: 8px;
        }
    
    
        .manual_disable_style {
            position: relative;
            top: -1px;
            margin: 0px;
        }
        .manual_disable_style rs\:Checkbox {
            margin-right: 0px;
        }
    
        .manual_disable_style + .helpIcon {
            margin-left: 8px;
        }
    
        @media screen and (min-width:0\0) {
            .manual_disable_style {
                width: 20px;
            }
        }
    
    
        .fa-qrcode { font-size:16px !important; vertical-align:middle; }



        /* 权限管理 */
        h3[name="siteName"] {
            word-break: break-word;
        }

        rs\:Table[name="permissionList"] div[row] > div[table-cell][name="pretty_name"] {
            width: 210px;
        }

        rs\:Table[name="permissionList"] div[row] > div[table-cell]:last-child {
            min-width: 130px;
        }

        #__PolicyControl__ #operator_selector,
        #__PolicyControl__ #statistic_viewer_selector {
            width: 98%;
        }


        /* 其他 */

        rs\:Table div[header] > div[active] {
            color: #00a6c8;
        }

        #__SSL_CERT__ .ic-download { font-size:unset; }
        rs\:ToolTip { position:absolute; z-index:100000; }
        rs\:ModalDialog ul li{ margin-top:10px; margin-bottom:10px; }
        rs\:ModalDialog label{
            display: inline-block;
            width: 25%;
            text-align: right;
            margin-right: 10px;
        }

        rs\:ModalDialog input{ width: 240px!important; border:1px solid #adafb3!important; }

        rs\:ModalDialog .helpIcon {
            position: relative;
            width: 16px;
            height: 16px;
            display: inline-block;
            font-size: 14px;
            cursor: default;
        }

        rs\:ModalDialog .helpIcon:before { position: absolute; }
        rs\:ModalDialog rs\:Select { width:240px; }


        


        #lock_limit_enable_wrap {
            display: inline-block;
        }

        .fontStyle {
            font-size: 12px !important;
        }

        .lock_limit_wrap li {
            line-height: 24px;
        }

        .server_port { width:50px; }

        #__2FA_DIALOG__ img { width:55%; display: block; margin-left: auto; margin-right: auto; }
        #__2FA_DIALOG__ .box { min-width:420px; width:500px; }
        #__2FA_DIALOG__ div[name=content] { width:500px; max-height: fit-content; box-sizing:border-box; margin-bottom:10px; padding-top:20px; padding-bottom:20px; }
        #__2FA_DIALOG__ div[name=content] p { margin-bottom:10px; }
        #__2FA_DIALOG__ li[name=config_url] { word-break:break-all; line-height:1.5; padding:10px 0px; }
        #__2FA_DIALOG__ li[name=config_token] { word-break:break-all; line-height:1.5; margin-bottom: 10px; font-weight: bold; text-align: center; }
        #__2FA_DIALOG__ li[name=tokenItem] { text-align:center; }
        #__2FA_DIALOG__ ul { padding-left:0px; padding-top: 8px; }
        #__2FA_DIALOG__ ul li { margin-top:0px; margin-bottom:0px; }
        #__2FA_DIALOG__ ul li div[name="name"] {
            text-align: center;
            font-size: 13px;
            font-weight: bold;
        }

        section ul { padding-left: 0px; }

        #__AAA_SERVER__ rs\:Select,
        #__AAA_SERVER__ input[name="ip_1"],
        #__AAA_SERVER__ input[name="ip_2"],
        #__AAA_SERVER__ input[name="password"] {
            width: 220px;
        }

        #__WEB_CONSOLE_ALLOW_IP__  div[row=row] > div[table-cell]:first-child {
            width: 120px;
        }
        #__WEB_CONSOLE_ALLOW_IP__  div[row=row] > div[table-cell]:first-child rs\:Select {
            width: 110px;
        }
        #__WEB_CONSOLE_ALLOW_IP__ div[table-cell]:nth-child(4){
            width: 450px;
        }
        #__WEB_CONSOLE_ALLOW_IP__ rs\:DatePicker[name="time_begin"] {
            margin-left: 0px;
        }
        #__WEB_CONSOLE_ALLOW_IP__ rs\:label {
            margin-right: 0px;
        }
        #__WEB_CONSOLE_ALLOW_IP__ rs\:label > rs\:checkbox {
            margin-bottom: 3px;
            margin-left: 5px;
        }


        .mr-16 {
            margin-right: 16px;
        }

        .complexityLabelStyle {
            width: auto !important;
            margin-right: 16px;
        }

    </style>

    <rs:Style enable="#embedMode">
        html[lang^="en"] body > .content.mainContent {
            min-width: 1150px !important;
        }

        rs\:Table div[header] > div[active] {
            color: #549BEF;
        }
    </rs:Style>


    {% include 'vue_front/system/role_management.html' %}
    {% include 'vue_front/system/qps_quota.html' %}

    <rs:TabPanel id="accountManagementTabPanel" keepIndexWhenRefresh="true">

        <div rs:name="{% trans 'Basic settings' %}">

            <!-- CAPTCHA -->
            <section>
                <header>
                    <span>{% trans "Login CAPTCHA" %}</span>
                    <p>{% trans "It is used to enable or disable CAPTCHA on the login page of the WebConsole." %}</p>
                </header>

                <ul v:if="editable">
                    <li>
                        <label>{% trans "Enable" %}</label>
                        <rs:Checkbox name="captchaEnabled" onchange="LoginCaptchaModule.changeStatus(event)" value=1></rs:Checkbox>
                    </li>
                </ul>
            </section>

            <!-- LOGIN KICKED OTHER ONLINE USER -->
            <section>
                <header>
                    <span>{% trans "No Repeat Login" %}</span>
                    <p>{% trans "A successful login will log out the same user signed in on other browsers/devices." %}</p>
                </header>

                <ul v:if="editable">
                    <li>
                        <label>{% trans "Enable" %}</label>
                        <rs:Checkbox name="kickUserEnabled" onchange="NoRepeatLoginModule.changeStatus(event)" value="1"></rs:Checkbox>
                    </li>
                </ul>
            </section>

            <!-- SESSION EXPIRED TIME -->
            <section expertmode v:if="page.expertMode" rsid="sessionExpiredSetting">
                <header>
                    <span>{% trans 'System Session Expiration Time' %}</span>
                </header>

                <ul>
                    <li>
                        <label>{% trans 'Time ' %}</label>
                        <input type="text" name="session_idle_timeout" placeholder="30" />
                        <span>{% trans "minutes" %}</span>
                        <i>{% trans ' (An integral number: ' %} 5 - 30 {% trans ')' %}</i>
                    </li>
                    <li v:if="editable">
                        <label></label>
                        <button type="button" class="inlineBlock" onclick="SessionExpiredTimeModule.handleSaveTimeout(event)">{% trans 'Save ' %}</button>
                    </li>
                </ul>
            </section>

            <!-- PASSWORD EXPIRED TIME -->
            <section expertmode v:if="page.expertMode" rsid="passwordExpiredSetting">
                <header>
                    <span>{% trans 'Password Policy' %}</span>
                </header>
            
                <rs:Form model="#PasswordPolicyManager.model">
                    <ul>
                        <li>
                            <label>{% trans 'Minimum Length' %}</label>
                            <input type="text" name="password_length" placeholder="8" />
                            <span>{% trans 'characters' %}</span>
                            <i>{% trans ' (An integral number: ' %} 8 - 32 {% trans ')' %}</i>
                        </li>
                        <li>
                            <label>{% trans 'Password Complexity' %}</label>
            
                            <rs:Checkbox name="complexity_upperWords" id="complexity_upperWords"></rs:Checkbox>
                            <label class="complexityLabelStyle">{% trans 'Uppercase Letters' %}</label>
            
                            <rs:Checkbox name="complexity_lowerWords" id="complexity_lowerWords"></rs:Checkbox>
                            <label class="complexityLabelStyle">{% trans 'Lowercase Letters' %}</label>
            
                            <rs:Checkbox name="complexity_number" id="complexity_number"></rs:Checkbox>
                            <label class="complexityLabelStyle">{% trans 'Numbers' %}</label>
            
                            <rs:Checkbox name="complexity_symbol" id="complexity_symbol"></rs:Checkbox>
                            <label class="complexityLabelStyle">{% trans 'Other Characters' %}</label>
            
                        </li>
                        <li>
                            <label>{% trans 'Username in Password' %}</label>    
                            <rs:Label>
                                <rs:Radio id="include_user_name_true" name="include_user_name" value="true"></rs:Radio>
                                {% trans 'Allow' %}
                            </rs:Label>
                            <rs:Label>
                                <rs:Radio id="include_user_name_false" name="include_user_name" value="false"></rs:Radio>
                                {% trans 'Disallow' %}
                            </rs:Label>
                        </li>
                        <li>
                            <label>{% trans 'No Use of Previous' %}</label>
                            <input type="text" name="history_check" placeholder="5" />
                            <span>{% trans 'History Password Time' %}</span>
                            <i>{% trans ' (An integer: 0 - 10. It is the number of previous passwords not allowed to use. 0 means no constraint.)' %}</i>
                        </li>
                        <li>
                            <label>{% trans 'Expiration time' %}</label>
                            <input type="text" name="password_period" placeholder="90" />
                            <span>{% trans "days" %}</span>
                            <i>{% trans ' (An integral number: ' %} 1 - 365 {% trans ')' %}</i>
                        </li>
                        <li v:if="editable">
                            <label></label>
                            <button type="button" class="inlineBlock" onclick="PasswordPolicyManager.setPasswordRules(event)">{% trans 'Save ' %}</button>
                        </li>
                    </ul>
                </rs:Form>
            </section>
            

            <!-- LOGIN FAILURE SETTINGS -->
            <section expertmode v:if="page.expertMode" rsid="LoginFailureSettings">
                <header>
                    <span>{% trans "Login Failure Locked" %}</span>
                </header>

                <ul>
                    <li>
                        <label>{% trans "Login failed period" %}</label>
                        <input type="text" name="login_failed_period" placeholder={{ login_failed_period_default|to_json }} />
                        <span>{% trans "minutes" %}</span>
                        <i>{% trans ' (An integral number: ' %} 1 - 60 {% trans ')' %}</i>
                    </li>
                    <li>
                        <label>{% trans "Login failed counts" %}</label>
                        <input type="text" name="login_failed_counts" placeholder={{ login_failed_counts_default|to_json }} />
                        <span>{% trans "Time(s)" %}</span>
                        <i>{% trans ' (An integral number: ' %} 2 - 10 {% trans ')' %}</i>
                    </li>
                    <li>
                        <label>{% trans "Lock period" %}</label>
                        <input type="text" name="lock_period" placeholder={{ lock_period_default|to_json }} />
                        <span>{% trans "minutes" %}</span>
                        <i>{% trans ' (An integral number: ' %} 1 - 3000 {% trans ')' %}</i>
                    </li>
                    <li v:if="editable">
                        <label></label>
                        <button type="button" class="inlineBlock" onclick="LoginFailureLockModule.save(event)">{% trans 'Save ' %}</button>
                    </li>
                </ul>
            </section>

            <!-- SSL -->
            <section expertmode v:if="page.expertMode" id="__SSL_CERT__">
                <header>
                    <span>{% trans 'SSL Certificate' %}</span>
                    <p>
                        <a href="javascript:void(0);" id="__CERT_DOWNLOAD__" class=" ic-download">{% trans 'Download SSL certificate' %}</a>
                        {% trans 'and install on your computer and trust it to avoid certificate warning messages when accessing the WebConsole or the HTTPS protected sites that use the built-in certificate.' %}
                        {% trans 'The SSL certificate for download here is randomly generated by and can only be used on the current cluster.' %}
                    </p>
                    <p>
                        {% trans 'You can upload a CA certificate and private key created by yourself with tools such as OpenSSL.' %}
                        {% trans 'Note: This certificate will work on both the WebConsole and HTTPS protected sites that use the built-in certificate.' %}
                    </p>
                </header>

                <p>{{ form.non_field_errors }}</p>
                <p>{{ form.certfile.errors }}</p>
                <p>{{ form.keyfile.errors }}</p>
                <form id="__SYSTEM_CERT_FORM__">
                    {% csrf_token %}

                    <ul>
                        <li>
                            <label>{% trans 'Select a certificate file' %}</label>
                            <rs:File id="id_certfile" name="certfile" placeholder="{% trans 'Select File' %}"
                                     accept=".crt, .pem"
                                     emptyTxt="{% trans 'Please upload valid certificate file.' %}"
                                     typeErrorTxt="{% trans 'Only certificate file with [{0}] extension  are supported.' %}"></rs:File>
                        </li>
                    </ul>

                    <ul>
                        <li>
                            <label>{% trans 'Select a private key file' %}</label>
                            <rs:File id="id_keyfile" name="keyfile" placeholder="{% trans 'Select File' %}" accept=".key"
                                     emptyTxt="{% trans 'Please upload valid private key file.' %}"
                                     typeErrorTxt="{% trans 'Only private key file with [ {0} ] extension are supported.' %}"
                            ></rs:File>
                        </li>
                    </ul>

                    <ul>
                        <li>
                            <label></label>
                            <button v:if="editable" type="button" onclick="SSLModule.uploadCertificate()" class="inlineBlock">{% trans 'Update' %}</button>
                        </li>
                    </ul>

                </form>
            </section>

            <!-- AAA Support -->
            {%  include 'v2/login_and_account/remote_auth_server.html' %}

            <!-- web_console_allow_ip -->
            {%  include 'v2/login_and_account/login_limit_ip.html' %}

            <!-- 2FA-->
            <section v:if="editable">
                <header>
                    <span>{% trans "Two-factor Authentication" %}</span>
                    <p>{% trans "When enabled, all users in the system must pass two-factor authentication before they can log in." %}</p>
                </header>
                <ul>
                    <li>
                        <label>{% trans "Enable" %}</label>
                        <rs:Checkbox name="totp_enabled" id="__TOTP_ENABLED__" rsid="totp_enabled"
                                     v:disabled="!auth('Edit_2fa', 'w')"
                                     onChange="TwoFactorAuthModule.openQRCode(event)"></rs:Checkbox>
                    </li>
                </ul>
            </section>

            <!-- 账户长期不使用锁定 -->   
            <section v:if="editable">
                <header>
                    <span>{% trans "Account locked due to prolonged inactivity" %}</span>
                    <p>{% trans "When the function is enabled, the account will be locked when the account has not been used for the set time. This function is not valid for administrators" %}</p>
                </header>
                <ul class="lock_limit_wrap">
                    <li>
                        <label>{% trans "Enable" %}</label>
                        <rs:Checkbox name="lock_limit_enable" id="__TIMELIMIT_ENABLED__" rsid="timeLimit_enabled"
                                     onChange="UserLockModule.changeEnableStatus(event)">
                        </rs:Checkbox>
                        <div rs:visible="UserLockModule.getFillData().lock_limit_enable" id="lock_limit_enable_wrap">
                            <input id="__TIMELIMITVAL__"  type="text" name="lock_limit_days" autocomplete="false" placeholder="90" />
                            <span>
                                {% trans "Days" %}
                                <i>
                                    &nbsp;
                                    ({% trans "Integer:" %} 30 - 90 )
                                </i>
                            </span>
                        </div>
                    </li>
                    <li>
                        <label></label>
                        <button type="button" class="inlineBlock" onclick="UserLockModule.save(event)"
                            id="timeLimitSaveBtn">{% trans 'Save ' %}</button>
                    </li>
                </ul>
            </section>

            <!-- UserList -->
            {% include 'v2/login_and_account/account_list.html' %}
        </div>

        <div rs:name="{% trans 'Authority' %}"
             rs:tab-visible="auth('Permission_List', 'r')"
             class="permissionBox">
            {% include 'v2/login_and_account/permission_list.html' %}
        </div>

        <div rs:name="{% trans 'QPS_Management' %}"
            rs:tab-visible="auth('Qps_Management', 'r') && {% visibleLayout 'qps_management' %} && !matchDeployMode('mirror')"
            class="qpsQuotaBox"
            rs:onShow="tabPanelChange(event)">
            <qps-quota></qps-quota>
        </div>

        <div rs:name="{% trans 'Roles' %}"
            id="roleModule"
            name="roleModule"
            rs:tab-visible="auth('Role_Management', 'w') && {% visibleLayout 'role_management' %} && global_params.isEnableRoleManagement"
            rs:onInit="handleOnInitRoles(event)">
            <role-management></role-management>
        </div>
    </rs:TabPanel>



    <!-- 双因素认证、用户二维码弹框 -->
    <rs:ModalDialog id="__2FA_DIALOG__" name="2faDialog" header="{% trans 'Two-factor Authentication Code' %}"
            onOK="QRCodeDialog.handleOk(event)" onCancel="QRCodeDialog.handleCancel(event)">

        <div name="content">
            <p>{% trans "The mobile phone can access the following URL or scan the QR code to obtain the user login two-factor authentication code. If you have previously entered, you do not need to re-enter" %}</p>
            <p>{% trans "Available authenticator APP has [Google Authenticator], [Microsoft Authenticator(Only SHA-1)] etc." %}</p>
            <ul>
                <li>
                    <div name="name"></div>
                    <img name="b64_qrcode" />
                </li>
                <style>
                    .container {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                    }
                    .selector-title {
                        font-size: 14px;
                        font-weight: bold;
                        color: #333;
                        margin-bottom: 10px;
                    }
                    .switch {
                        display: flex;
                        background-color: #e0e0e0;
                        border-radius: 4px;
                        overflow: hidden;
                        margin-bottom: 8px;
                    }
                    .switch-option {
                        padding: 8px 16px;
                        font-size: 13px;
                        cursor: pointer;
                        transition: all 0.2s ease;
                        border: none;
                        background: none;
                        outline: none;
                        line-height: 15px;
                        color: white !important;
                    }
                    .switch-option.active {
                        background-color: #ca4d37 !important;
                        color: white !important;
                    }
                    .switch-option:hover:not(.active) {
                        background-color: #d0d0d0;
                    }
                </style>

                <div class="container">
                    <div class="switch" id="shaSwitch">
                        <button class="switch-option" data-value="SHA1">SHA1</button>
                        <button class="switch-option" data-value="SHA256">SHA256</button>
                        <button class="switch-option" data-value="SHA512">SHA512</button>
                    </div>
                </div>

                <li name="config_url"></li>
                <li name="config_token"></li>
                <li name="tokenItem">
                    <input type="text" name="token" placeholder="{% trans 'Authentication Code' %}" />
                </li>
            </ul>
        </div>
    </rs:ModalDialog>
{% endblock %}
