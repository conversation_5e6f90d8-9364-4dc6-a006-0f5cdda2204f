{% load i18n %}
{% load nav_tags %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-type" content="text/html; charset = utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <title>{{cluster_name}} - {% block title %}{{ brand }}{% endblock %}</title>
    <link rel="icon" href="/static/img/favicon.ico?ghrdm={{ build_hash_and_layout_hash }}" mce_href="/static/img/favicon.ico?ghrdm={{ build_hash_and_layout_hash }}" type="image/x-icon"/>

    <link rel="stylesheet" href="/static/css/FontAwesome6/css/all.css?ghrdm={{ build_hash_and_layout_hash }}"/>
    <link rel="stylesheet" href="/static/css/FontAwesome6/css/v5-font-face.css?ghrdm={{ build_hash_and_layout_hash }}">
    <link rel="stylesheet" href="/static/css/RSFontAwesome/style.css?ghrdm={{ build_hash_and_layout_hash }}" />
{#    https://fontawesome.com/search?m=free#}

    <link rel="stylesheet" href="/static/css/theme.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link rel="stylesheet" href="/static/css/layout_theme.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link rel="stylesheet" href="/static/css/rs/rs.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link rel="stylesheet" href="/static/css/common.css?ghrdm={{ build_hash_and_layout_hash }}" />
    <link rel="stylesheet" href="/static/vue/ui.css?ghrdm={{ build_commit_no }}" />
    <link href="/static/css/layout.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />
    <link href="/static/css/customRSControl.css?ghrdm={{ build_hash_and_layout_hash }}" rel="stylesheet" />

    <script src="/static/js/utils.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script src="/static/js/prepareBrowser.js"></script>

    <script src="/static/js/const.js"></script>
    <script src="{% url 'javascript-catalog' %}"></script>
    <script src="/static/js/browserAdapter.js"></script>
    <script src="/static/js/rs.js" ready="initPage()" rsTag="true" cache="false" trace="false"></script>
    <rs:Service onMessage="handleOnGetServiceMessage(event)"></rs:Service>

    <script src="/static/js/Validation.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script src="/static/js/purviewControl.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script src="/static/js/sha1.js?ghrdm={{ build_hash_and_layout_hash }}" charset="UTF-8"></script>
    <script src="/static/js/Web.Console.Util.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script src="/static/js/responseResultProcess.js?ghrdm={{ build_hash_and_layout_hash }}"></script>

    <script src="/static/vue/ui.js?ghrdm={{ build_commit_no }}"></script>

    <script>

        {% autoescape off %}
        var global_params = {
            isSuperAdmin:       {{ user.username|to_json }} === 'admin',
            role:               {{ role|to_json }},
            webFilterInstalled: {{ web_filter_install|to_json }},
            isCloudMode:        {{ is_cloud_mode|to_json }},
            isDevMode:          {{ is_dev_mode|to_json }},
            isDebug:            {{ is_build_debug|to_json }}==1,
            isDevDebug:         {{ is_prod_pkg|to_json }}==0 && {{ is_build_debug|to_json }}==1,
            isProdPKG:          {{ is_prod_pkg|to_json }}==1,
            isExpertMode:       {{ is_expert_mode|to_json }},
            isEnableWebFilter:  {{ is_web_filter_enable|to_json }},
            isEnableThreatIntelligence:  {{ is_threat_intelligence_enable|to_json }},
            isEnableFileMonitoring:  {{ is_file_monitoring_enable|to_json }},
            isEnableNetworkDDOSProtect:  {{ is_network_ddos_protect_enable|to_json }},
            isEnableRoleManagement:  {{ is_role_management_enable|to_json }},
            isSupportNetworkDDOS : {{ is_support_network_protect|to_json }},
            isEnableSensitiveLogFilterSwitch : {{ is_enable_sensitive_log_filter_switch|to_json }},
            isProxyServer:      {{ is_proxy|to_json }},
            reportType:         {{ report_type|to_json }},
            useSailfish:        {{ report_type|to_json }}=='sailfish',
            usePhoenix:         {{ report_type|to_json }}=='phoenix',
            logNodeAvailable:   {{ log_node_available|to_json }},
            hasPhoenixPkg:      {{ has_phoenix_pkg|to_json }},
            machineHardwareName:{{ machine_hardware_name|to_json }},
            osId:               {{ os_id|to_json }},
            isChnOS:            {{ is_chn_os|to_json }},
            isArm:              {{ processor|to_json }}=='aarch64',
            isBiddingVersion:   {{ is_bidding_ver|to_json}},
            systemStatus:       {{ system_status|to_json }},
            activated:          {{ activated|to_json }},
            systemNotifyInfo:   {{system_status.nav_alerts|to_json}},
            sysErrorMsg:        {{error_msg|to_json}},
            sysErrorExtra:      {{error_extra|to_json}},
            userName:           {{ user.username|to_json }},
            isPhoenixServer:    {{ is_phoenix_server|to_json }},
            isPhoenixOnline:    {{ is_phoenix_online|to_json}},
            isSailfishOnline:   {{ is_sailfish_online|to_json}},
            isSailfishServer:   {{ is_sailfish_server|to_json }},
            hasPhoenixServer:   {{ has_phoenix_server|to_json }},
            hasSailfishServer:  {{ has_sailfish_server|to_json }},
            isValidLicense:     {{ is_valid_license|to_json }},
            userName:           {{ user.username|to_json }},
            isRemote:           {% is_user_remote user.username %},
            isPhoenixIndex:     {{ is_phoenix_index|to_json }},
            menuDatas:          {{ menu|to_json }},
            hasBTAServer:       {{ has_bta_server|to_json }},
            hasAbdServer:        {{ has_abd_server|to_json }},
            hasFlowlearnServer: {{ has_flowlearn_server|to_json }},
            isBTAAppEnabled:    {{ is_bta_app_enabled|to_json }},
            isBTABizEnabled:    {{ is_bta_biz_enabled|to_json }},
            isBTAEnabled:       {{ is_bta_enabled|to_json }},
            isBSDEnabled:       {{ is_bsd_enabled|to_json }},
            isBSEEnabled:       {{ is_bse_enabled|to_json }},
            isPDEnabled:        {{ is_pd_enabled|to_json }},
            isDCEnabled:        {{ is_dc_enabled|to_json }},
            isAPIEnabled:       {{ is_api_enabled|to_json }},
            isBDCEnabled:       {{ is_bdc_enabled|to_json }},
            isAIThreatAnalystEnabled:  {{ is_ai_threat_analyst_enabled|to_json }},
            protectedLevel:     {{ protected_level|to_json }},
            isWaflayoutShowItem: {{ protected_level|to_json }}!='WE',
            hasRepuServer:      {{ has_repu_server|to_json }},
            passwordExpire:     {{ password_expire|to_json }},
            isExpireWarning:    {{ password_expire|to_json }}<10,
            buildCommitNo:      {{ build_commit_no|to_json }},
            maxAPPCount:        {{ mobile_max_app_count|to_json }},
            isShowMobileApp:    {{ show_mobile_app|to_json }},
            mobileSalt:         {{ mobile_salt|to_json }},
            isAiService:        {{ is_ai_service|to_json }},
            isAdvancedWafEnabled:{{ is_advanced_waf_enabled|to_json }},
            isLLMProtectionEnabled:{{ is_LLM_Protection_enabled|to_json }},
            isShowCMDLinePage:   {{ is_show_cmdline_page|to_json }},
            is_archive_server:  {{ is_archive_server|to_json }},
            webconsoleAdminPort: {{ webconsole_admin_port|to_json }},
            heterogeneousCluster: {{ heterogeneous_cluster|to_json }},
            isTransparentMode:  {{ is_transparent_mode|to_json }},
            useBypassCard:      {{ use_bypass_card|to_json }},
            bondMode:           {{ bond_mode|to_json }},
            isBonded:           {{ is_bonded|to_json }},
            hasBr1:             {{ has_br1|to_json }},
            transparentStatus:  {{ transparent_status|to_json }},
            site4User:          {{ site_4_user|to_json }},
            isMirrorMode:       {{ is_mirror_mode|to_json }},
            isMppEnabled:       {{ is_mpp_enabled|to_json }},
            productType:        {{ product_type|to_json }},
            productCategory:    {{ license_product_category|to_json}},
            inContainer:        {{ in_container|to_json }},
            inK8s:              {{ in_k8s|to_json }},
            clusterLanguage:    {{ cluster_language|to_json }},
            isIPv46:            {{ isIPv46|to_json}},
            nodeId:             {{ node_id|to_json }},
            isEnableAdvancedVerify: {{ is_advanced_verify_enable|to_json }},
            deployMode:         {{ deploy_mode|to_json}},
            clusterName:        {{ cluster_name|to_json }},

            hasLLMServer:       {{ has_llm_server|to_json }},
            isLLMServer:        {{ is_llm_server|to_json }}
        };

        var menu_list = {{ menu|to_json }};
        var isSupportEnHelpDoc = {% visibleLayout 'support_en_help_doc' %};
        {% endautoescape %}

        // 部署模式
        var DeployModeManager = (function() {

            var DeployModeMap = {
                unset: { icon: '', text: 'Error: No Deploy' },
                inline: { icon: 'ic-cluster', text: '{% trans "Reverse proxy Deployment" %}' },
                HA: { icon: 'ic-ha', text: '{% trans "Reverse Proxy HA" %}' },
                mirror: { icon: 'ic-rs-network-mirror', text: '{% trans "Mirroring Deployment" %}' },
                transparent: { icon: 'ic-rs-inline', text: '{% trans "Transparent Deployment" %}' },
                bypass: { icon: 'ic-rs-bypass', text: '{% trans "Bypass Transparent" %}' },
                bridge: { icon: 'ic-rs-bridge', text: '{% trans "Bridge Transparent" %}' },
                routeProxy: { icon: 'fa fa-random', text: '{% trans "Route Deployment" %}' },
                HARouteProxy: { icon: 'fa fa-random', text: '{% trans "Route Proxy HA" %}' },
                plugin: { icon: 'fa fa-plug', text: '{% trans "Plug-in Deployment" %}' }
            };

            var deployModeConfig = DeployModeMap[global_params.deployMode];

            // 透明部署由transparentStatus状态决定。transparentStatus === inline时，即为transparent部署模式
            if (global_params.deployMode === 'transparent') {
                var mode = global_params.transparentStatus === 'inline' ? 'transparent' : global_params.transparentStatus;
                deployModeConfig = DeployModeMap[mode];
            }

            return {
                config: deployModeConfig
            }
        })();
        
        
        

        // 帮助文档锚点
        var HELP_DOC = (function() {
            function getOnlineHelpDocLink() {
                // 文档路径
                var docLink = getDocLink('help.html');
                // 添加帮助文档参数（是否是信创系统）
                var is_xc = global_params.isChnOS ? '1' : '0';
                docLink += '?is_xc=' + is_xc;

                // 修正读取到的pathname
                var pathname = location.pathname;
                if (pathname.indexOf('/system/labs/') > -1) {
                    pathname = '/system/labs/';
                }

                docLink += '#' + pathname.replace(/\//g, '_');
                return docLink;
            }


            function getDocLink(filename) {
                var _filename = filename || 'help.html';
                var isDocUseEn = isSupportEnHelpDoc && page.isUseEn;
                var _path = '/static/help/' + (isDocUseEn ? 'en' : 'zh') + '/';
                return _path + _filename;
            }

            return {
                getDocLink: getDocLink,
                getOnlineHelpDocLink: getOnlineHelpDocLink
            }
        })();

    </script>

    <script>

        {% autoescape off %}
        var Authority = {{ acl|to_json }};

        /**
         * 为适配API资产管理跳转到API系统能获得部署模式、登录用户名、用户角色、系统权限信息，WebConsole将这些信息存入localstorage和sessionstorage
         * 若API session.ts中存储信息有变化，需要查看此处是否需要一并修改
         *  */
        (function adaptAPISytem() {
            rs.storage.set('asp_config', JSON.stringify({
                deploy_mode: global_params.deployMode,
                product_type: global_params.productType,
                acl: Authority
            }));

            rs.storage.set('account', global_params.userName);
            rs.session.set('userRole', global_params.role);
        })();

        {% endautoescape %}


		// 检查部署模式: inline|HA|mirror|transparent|routeProxy|plugin|HARouteProxy
		function matchDeployMode(deployMode) {
			var modes = deployMode.split('|');
			return modes.some(function(m) {
				if (m === 'transparent') {
					return global_params.isTransparentMode;
				} else {
					return global_params.deployMode === m;
				}
			});
		}



        // module: module / modules join with '|'
        // mode: r/w/rw
        /*
            <div v:if="auth('Expert_Mode', 'r')"></div>
            <div v:show="auth('Expert_Mode', 'rw')"></div>
        */

        function auth(module, mode) {

            var modules = module.split('|');
            if (modules.length > 1) {
                while (modules.length) {
                    if (auth(modules.pop(), mode)) {
                        return true;
                    }
                }
                return false;
            }

            var m = Authority[module];
            if (!m) throw new Error('Unknown module: ' + module);
            switch(mode) {
                case 'r':
                    return m.read;
                case 'w':
                    return m.write;
                case 'rw':
                    return m.read && m.write;
                default:
                    throw new Error('Unknown mode: ' + mode);
            }
        }

        window.terminated = false;
        var skipOnbeforeunload = false;
        window.onbeforeunload = function() {
            if (!skipOnbeforeunload) {
                window.terminated = true;
            }
        }




        rs.bind('ready', function() {
            var passwordInputs = rs('input.passwordInput', true);
            if (passwordInputs && passwordInputs.length) {
                passwordInputs.bind('focus', function() {
                    this.type = 'password';
                });
            }
        });

    </script>

    <script>

        /*
        * ####################################################
        *   被嵌入到API单页面的iframe中使用：
        *   1、插入定制样式表
        *   2、向外广播自身内容区域的高度
        *   3、取消心跳包对session有效性的检查
        * ####################################################
        * */

        var embedMode = self.name === 'webconsoleIFrame' || (self !== top && self.name != 'form_result');

        if (embedMode) {
            rs.bind('ready', function () {
                patchForAPI();
            });
        }

        function resetView() {
            if (document.querySelector('.mainContent')) {
                document.querySelector('.mainContent').scrollTop = 0;
            }
            parent.postMessage('resetView::{}', '*');
            reportContentStatus();
        }

        /**
         * 通知vue调用loading
         * @param wait: boolean
         * @param loadingOpts: { group: loading组名, message: 内容, sanitize: boolean, true表示防xss注入 }
         *            具体参见：http://v1.quasarchs.com/quasar-plugins/loading
        */
        function toggleLoading(wait, loadingOpts) {
            var o = { isLoading: wait };
            if (loadingOpts) o.loadingOpts = loadingOpts;
            parent.postMessage('toggleLoading::' + JSON.stringify(o), '/');
        }

        /**
         * 通知vue更新loading内容
         * @param loadingOpts: { group: loading组名, message: 内容, sanitize: boolean, true表示防xss注入 }
         *            具体参见：http://v1.quasarchs.com/quasar-plugins/loading
        */
        function updateLoadingMessage(loadingOpts) {
            parent.postMessage('updateLoadingMessage::' + JSON.stringify({ loadingOpts: loadingOpts }), '/');
        }

        function reportContentStatus() {
            setTimeout(function() {
                parent.postMessage('contentReady::{"width":'
                    + document.body.clientWidth + ',"height":'
                    + getContentHeight() +'}', '*');
            }, 200);
        }

        var OFFSET_HEIGHT = 1;  // iframe.height增加1px来处理iframe中出现滚动条的问题
        function patchForAPI() {

            rs.Element('link', {
                rel: 'stylesheet',
                href: '/static/css/api_hack.css?ghrdm={{ build_hash_and_layout_hash }}'
            }).appendTo(document.body);


            reportContentStatus();

            // api产品，isExpertMode返回的值默认是true
            //专家模式只在abd下默认打开，其他layout需用户手动打开
            // if (!global_params.isExpertMode && isApiProduct(global_params.productType)) {
            //     // 强制开启专家模式
            //     global_params.isExpertMode = true;
            //     handleToggleExpert({target:{checked: global_params.isExpertMode}});
            // }

            
            // 由API通知Botgate
            parent.addEventListener('message', function(evt) {
                var protocols = {
                    'hasLLMLocalPkg': function(hasLLMLocalPkg) {
                        emitLLMLocalPkgStatus(hasLLMLocalPkg);
                    }
                };

                var data = evt.data.split('::');
                var action = data[0];
                var value = data[1];
                if (protocols[action]) {
                    protocols[action](value ? JSON.parse(value) : undefined);
                }
            });



            for (i in SessionExpiredManager) {
                // session过期以后不再弹出登录框，由api框架代管
                SessionExpiredManager[i] = doNothing;
            }



            var bodyHeight = 0;
            setInterval(function() {
                var height = getContentHeight();
                if (height !== bodyHeight) {
                    if (height - bodyHeight == OFFSET_HEIGHT) return;
                    reportContentStatus();
                    bodyHeight = height;
                }
            }, 1000);

            // ResizeObserver ie11 不支持
            /*
            var MutationObserver = window.MutationObserver ||
            window.WebKitMutationObserver ||
            window.MozMutationObserver;
            var mo = new MutationObserver(function(mutations){
                reportContentStatus();
            });
            mo.observe(document.documentElement, {subtree: true, childList: true});*/


            function doNothing() {}
        }

        function isWafIframe() {
            return window.name == 'wafIFrame';
        }

        function getContentHeight() {
        //     if (rs('.contentWrapper')) {
        //         var navigationBar = rs('#navigationBar') ? rs('#navigationBar').clientHeight : 0;
        //         return rs('.contentWrapper').scrollHeight + navigationBar;
        //     } else {
        //         return Math.max(document.documentElement.scrollHeight, rs('.mainContent').scrollHeight);
        //     }
            return Math.max(document.documentElement.scrollHeight, (rs('.contentWrapper') || rs('.mainContent')).scrollHeight) + OFFSET_HEIGHT;
        }

        // 跳转到：可编程对抗 - 高级
        function gotoUbbResourceListInAdvancedTab(url) {
            parent.postMessage('gotoUbbResourceListInAdvancedTab::{ "iframeUrl": "'+ url + '" }', '*');
        }

        // 跳转到：业务数据采集
        function gotoBusinessDataCollection4Iframe() {
            parent.postMessage('gotoBusinessDataCollection::{}', '*');
        }

        // 跳转到：系统-通用-指定的模块
        function gotoSystemGeneral4Iframe(moduleName) {
            parent.postMessage('gotoSystemGeneral::{ "module": "' + moduleName + '" }', '*');
        }

        // 跳转到：系统 - 日志 - 指定的tab
        function gotoLog4Iframe(tabName) {
            parent.postMessage('gotoLog::{ "tab": "' + tabName + '" }', '*');
        }

        // 打开动态挑战模板
        function openChallengeTemplate() {
            parent.postMessage('openChallengeTemplate::{}', '*');
        }

        // 跳转至首页 - API系统下针对的是总览
        function gotoIndex4Iframe() {
            parent.postMessage('gotoIndex::{}', '*');
        }

        function gotoWafSetting() {
            parent.postMessage('gotoWafSetting::{}', '*');
        }

        // 是否API产品：ABD 或 ASA
        function isApiProduct(product_type) {
            return product_type === 'ApiBotDefender' || product_type === 'ApiSecurityAudit';
        }

        function isSafeplus(){
            return global_params.productType === 'Safeplus'
        }

        function isNGWAF(){
            return global_params.productType === 'Botgate' && global_params.productCategory.indexOf('NGWAF')!=-1
        }

        function isNGWAFWE(){
            return isNGWAF() && (global_params.protectedLevel === 'WE')
        }

        function isSafeplusOrNGWAFWE(){
            return isSafeplus() || isNGWAFWE()
        }

        function emitLLMLocalPkgStatus(hasLLMLocalPkg) {
            // 发布给大模型配置页面（Vue）
            RSVue.emit('licenseHasLLMPkg', hasLLMLocalPkg);

            // 发布给Botgate
            var evt = rs.Event('licenseHasLLMPkg');
            evt.hasLLMLocalPkg = hasLLMLocalPkg;
            rs('body').trigger(evt);
        }

        rs.bind('ready', function() {
            utils.oemReplace(function(result) {
                // var data = rs.killHTML(data || '');
                if (result.mode === 'clean') {
                    return false;
                } else if (result.mode === 'custom') {
                    rs('.customProductName').html(rs.killHTML(result.productName || ''));
                    rs('.customProductNameFooter').html('&copy; {{copyright_year}} ' + rs.killHTML(result.companyName || ''));
                } else {
                    rs('.customProductName').html('<span>{% trans "ForceShield Dynamic Application Protection" %}</span>');
                    rs('.customProductNameFooter').html('&copy; {{copyright_year}} '  + '{% trans "ForceShield" %}');
                }
            }); 
        });
    </script>


    <script src="/static/js/base.js"></script>


    {% block headerScript %}{% endblock %}

    <style>

        #__LOGIN_ACCOUNT__ rs\:Switch {
            float: right;
            right: 8px;
            top: 8px;
            width: 25px;
            height: 7px;
            overflow: inherit;
        }
        #__LOGIN_ACCOUNT__ rs\:Switch i { width: 12px; height: 12px; top:-3px; left:-1px; transition: all .2s ease-out;}
        #__LOGIN_ACCOUNT__ rs\:Switch[checked] i { right:-2px; left:auto; }
        rs\:Label[name=expertLabel] { display:block; margin-right:0px; }

        #__MODIFY_PASSWORD_BOX__ [name=content] p { line-height: 1.5; }
        #__MODIFY_PASSWORD_BOX__ [name=content] ul { padding-top:10px; }
        #__MODIFY_PASSWORD_BOX__ [name=content] li {
            margin-top:10px;
            margin-bottom:10px;
        }

        #__MODIFY_PASSWORD_BOX__ [name=content] li label {
            width: 120px;
            display: inline-block;
            text-align: right;
            margin-right: 20px;
            font-weight: 500;
        }

        #__MODIFY_PASSWORD_BOX__ input { width: 240px; }
        .notifyBox #__LOGIN_ACCOUNT__ .ic-caret-down { font-size: 13px; }
        .notifyBox rs\:DropBox [rs\:role=dropLayer] a[name=expertLabelItem]:hover { color:#313741; background-color: transparent; }
        #__MODIFY_PASSWORD_ITEM__[disabled] a { color:#c5c5c5; }

        #__MODIFY_PASSWORD_BOX__ .hiddenLi { height:0px; }
        #__MODIFY_PASSWORD_BOX__ .hiddenLi input[type=text]{  opacity:0;  height:0px;  width:0px;  display:inline-block;  margin:0px;  }
        #__PASSWORD_EXP__ { display: none }
        #__PASSWORD_EXP__.expire { position:relative; display:inline-block; background-color:#f59d00; border-radius:5px; line-height:20px; padding:4px 10px; width:150px; font-size:12px; top:0px; color:white }
        #__PASSWORD_EXP__.expire .icon { position:absolute; right:-15px; border-top:8px solid transparent; border-right:8px solid transparent; border-bottom:8px solid transparent; border-left:8px solid #f59d00; top:15px; }

        #__MODIFY_PASSWORD_ITEM__.expire a { color:#e67e22; }
        #__MODIFY_PASSWORD_ITEM__ .ic-warning { display: none; }
        #__MODIFY_PASSWORD_ITEM__.expire .ic-warning { color:#e67e22; display:inline-block; float:right; margin-top:5px; margin-right:8px; }

        #__LOGIN_ACCOUNT__.expire>a span,
        #__LOGIN_ACCOUNT__.expire>a span *,
        #__LOGIN_ACCOUNT__.expire>a:hover span {
            color:#e67e22;
        }

        #__DYNAMIC_CODE__ label { margin-right:10px; }

        #__SESSION_CHECK_DIALOG__ div.box { width:440px; min-width:440px; overflow: hidden; }
        #__SESSION_CHECK_DIALOG__ div[name=mdTop] { background-color:#f9fafb; line-height:40px; }
        iframe#__SESSION_CHECK_FRAME__ { width:100%; height:480px; overflow: hidden; }

        .floatRightTools {
            display: flex;
            flex-wrap: nowrap;
            justify-content: flex-end;
            flex-direction: row;
            align-items: center;
            height: 100%;
        }

        #__LOGIN_ACCOUNT__  #__LOGIN_USER__ {
            margin-left: 4px;
            margin-right: 4px;
        }

        #__LOGIN_ACCOUNT__  #__ROLE_TXT__ {
            margin-right: 4px;
        }

        a[deploy_mode_name='transparent'] > i,
        a[deploy_mode_name='bypass'] > i,
        a[deploy_mode_name='bridge'] > i {
            line-height: inherit !important;
        }

        a[deploy_mode_name='transparent'] > i:before,
        a[deploy_mode_name='bypass'] > i:before,
        a[deploy_mode_name='bridge'] > i:before {
            display: inline-block;
            transform: translate(0px, 3px);
        }

        #basePageCopyright > .customProductName, .customProductNameFooter {
            white-space: normal;
            word-break: break-word;
            word-wrap: break-word;
        }
    </style>
</head>

<body>

    <div class="wait">
        <div>
            <div id="pageWaitLoadingContent">
                <img id="__G_LOADING_GIF__" src="/static/img/loading-w.gif" />
            </div>
        </div>
    </div>

    <div class="navigation" v:show="!embedMode">
        <div class="logo">
            <img src="/static/img/logo_brand_mini.png?ghrdm={{ build_hash_and_layout_hash }}" id="logoImgMini" />
            <img src="/static/img/logo_brand.png?ghrdm={{ build_hash_and_layout_hash }}" id="logoImg" />
            <span>{% trans 'ForceShield Dynamic Application Protection' %}</span>
        </div>

        <div id="__SYSTEM_MENU__">
            <script src="/static/js/menu/createMenuBase.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
            <script src="/static/js/menu/createMenu.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
            <script>
                new SystemMenu(rs('#__SYSTEM_MENU__')).draw(menu_list);
            </script>
        </div>

        <div class="copyright">
            <div id="basePageCopyright">
                <span class="customProductName">
                </span><br/>
                <!-- {% trans 'ForceShield Dynamic Application Protection' %}<br/> -->
                <!-- &copy; {{ copyright_year }}  -->
                <!-- {% trans 'ForceShield' %} -->
                <span class="customProductNameFooter">
                </span>
            </div>
        </div>
        <div class="topImg"></div>
    </div>

    <div class="pageTitle"  v:show="!embedMode">
        <!--{{ brand }}-->
        <h3>
            <div class="sysHeader">{% trans 'ForceShield Dynamic Application Protection' %}</div>
            <div class="notifyBox floatRightTools">
                <div id="__PASSWORD_EXP__">
                    <div class="icon"></div>
                    <span name="tips"></span>
                </div>

                <rs:DropBox useHover="true" id="__LOGIN_ACCOUNT__">
                    <a href="#" rs:role="handler">
                        <span class="ic-user"></span>
                        <span id="__LOGIN_USER__"></span>
                        <span id="__ROLE_TXT__"></span>
                        <span class="ic-caret-down"></span>
                    </a>
                    <div rs:role="dropLayer" min-width="160">
                        <li v:if="auth('Expert_Mode', 'r')">
                            <a href="javascript:void(0)" name="expertLabelItem">
                                <rs:Label name="expertLabel">{% trans 'Expert Mode' %}<rs:Switch id="expertModeSwitch" name="expertMode"></rs:Switch></rs:Label>
                            </a>
                        </li>

                        <li id="__MODIFY_PASSWORD_ITEM__" rs:disabled="global_params.isRemote">
                            <a href="javascript:void(0)"
                               onmouseover="handleMouseoverChangePassword(event)"
                               onmouseout="handleMouseoutChangePassword(event)"
                               onclick="handleClickChangePassword(event)">
                                {% trans 'Change Password' %}
                                <i class="ic-warning" rs:visible="global_params.isExpireWarning"></i>
                            </a>
                        </li>

                        <li><a rsid="bt_signOut" href="/user/logout/">{% trans 'Logout' %}</a></li>
                    </div>
                </rs:DropBox>

                <a
                    v:deploy_mode_name="global_params.deployMode"
                    onmouseover="page.titleTip(event.pageX, event.pageY, DeployModeManager.config.text)"
                    onmouseout="page.titleTipHide()"
                    style="cursor: default;"
                >
                    <i v:class="DeployModeManager.config.icon"></i>
                </a>
                

                <rs:DropBox v:if="auth('Navigation_Alarm', 'r')" useHover="true" class="notifyList" id="__SYS_ALERT__" isPositionAdapt="false">
                    <a onclick="handleClickAlarm()" rs:role="handler"><span class="ic-alerts"><i name="errorCount"></i><i name="warningCount"></i></span></a>
                    <div rs:role="dropLayer">
                        <rs:List name="notifyList" onCreateItem="handleOnCreateNotifyListItem(event)" onEmptyData="handleOnEmptyNotify(event)">
                             <div class="row">
                                <div class="cell"><i name="icon"></i></div>
                                <div class="cell">
                                    <span name="label"></span>
                                    <button type="button" name="solution_title" class="floatRight"></button>
                                    <div name="message"></div>
                                </div>
                            </div>
                        </rs:List>
                    </div>
                </rs:DropBox>

                <a v:href="HELP_DOC.getOnlineHelpDocLink()" v:if="{% visibleLayout 'help_btn' %} && auth('Help_Document', 'r')" id="__HELP_BTN__" target="rs_help"><span class="ic-help"></span></a>
            </div>
        </h3>
    </div>

    <div class="content mainContent">
        <div class="contentWrapper">
            <rs:NavigationBar id="navigationBar" path="{% block navigation %}{% endblock %}" title="{% block nav_title %}{% endblock %}">
                <path name="{% trans 'Overview' %}">/overview</path>
                <path name="{% trans 'Protection' %}">/proxy</path>
                <path name="{% trans 'General' %}">/system/general/</path>
                <path name="{% trans 'Login/Account' %}">/system/account_management</path>
                <path name="{% trans 'Labs' %}">/system/labs</path>
            </rs:NavigationBar>

            <div class="contentBlock">
                {% block content %}{% endblock %}
            </div>


            <iframe id="__DOWNLOAD_IFRAME_HIDDEN__" style="display: none"></iframe>
        </div>

        <rs:ModalDialog id="__MODIFY_PASSWORD_BOX__" name="modifyPassword"
                    onOK="handleOnModifyPassword(event)"
                    onCancel="handleCancelModifyPassword(event)">

            <div name="content">
                <p id="changPasswordTip">
                    <span id="lenNotice"></span><br />
                    <span id="complexityNotice"></span><br />
                    <span id="historyCheckNotice"></span><br id="historyCheckNoticeBr" />
                    <span id="includeNameNotice"></span><br />
                </p>

                {# Receive an account automatically populated by the browser #}
                <div class="hiddenLi"><input type="text" /></div>

                <ul>
                    <li>
                        <label>{% trans 'Old password ' %}</label>
                        <input type="text" name="old_password" autocomplete="false" class="passwordInput" />
                    </li>
                    <li>
                        <label>{% trans 'New password ' %}</label>
                        <input type="text" name="new_password1" autocomplete="false" class="passwordInput" />
                    </li>
                    <li>
                        <label>{% trans 'Confirm Password' %}</label>
                        <input type="text" name="new_password2" autocomplete="false" class="passwordInput" />
                    </li>
                </ul>
            </div>

        </rs:ModalDialog>

        <rs:ModalDialog id="__DYNAMIC_CODE__" name="dynamicCode">
            <div name="content">
                <ul>
                    <li>
                        <label>{% trans "Verification Code" %}</label>
                        <input type="text" name="adv_operation_code" />
                    </li>
                </ul>
            </div>

        </rs:ModalDialog>
    </div>

    <rs:ModalDialog id="__MAIN_POP__">
        <div><div class="messagebox-msg" name="msg"></div></div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__ALERT_POP__" okText="{% trans 'OK' %}" cancelText="{% trans 'Cancel' %}" needCancelButton="false">
        <div><div class="messagebox-msg" name="msg"></div></div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__ASK_POP__" okText="{% trans 'OK' %}" cancelText="{% trans 'Cancel' %}" needCancelButton="true">
        <div><div class="messagebox-msg" name="msg"></div></div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__AUTOMATIC_POP__" isAutomaticPop="true">
        <div><div class="messagebox-msg" name="msg"></div></div>
    </rs:ModalDialog>

    <rs:Tooltip id="__titleTip__" position="left">
        <div name="tooltipContent"></div>
    </rs:Tooltip>

    <rs:ModalDialog id="__SESSION_CHECK_DIALOG__" customOperate="true">
        <iframe id="__SESSION_CHECK_FRAME__" src="about:blank"></iframe>
    </rs:ModalDialog>
    <iframe id="__FORM_RESULT__" name="form_result" style="display:none;"></iframe>

    <!-- 设置定时器dialog -->
    {% include 'v2/common/timer_dialog.html' %}


    {% block script %}{% endblock %}

</body>
</html>
