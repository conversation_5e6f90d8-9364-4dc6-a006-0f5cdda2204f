{% load i18n %}
{% load nav_tags %}

{% block content %}
    <style>
        rs\:Table[name="statisticsTable"] div[row] div[table-cell]:nth-child(1),
        rs\:Table[name="statisticsTable"] div[row] div[table-cell]:nth-child(5){
            width: 24%;
        }

        rs\:Table[name="statisticsTable"] div[row] div[table-cell]:nth-child(2){
            width: 100px;
        }

        rs\:Table[name="statisticsTable"] div[row] div[table-cell]:nth-child(3){
            width: 140px;
        }

        /*----针对IE9,IE10,IE11----*/
        @media screen and (min-width:0\0) {
            
        }
        
    </style>

    <rs:style enable="#embedMode">
        
    </rs:style>

    
    <rs:TabPanel id="__TAB_PANEL__" second keepIndexWhenRefresh="true">

            <div rs:tab-visible= "global_params.role != 'Operator'" rs:name="{% trans 'Log'%}" id="__FILE_STATISTICS__" rsid="file_statistics"
                 rs:onInit="FileStatistics.init()">
                <section>
                    <rs:Form model="#mSearch">
                        <span>
                            <input type="text" maxlength="1024" name="keyword" id="keyword" placeholder="{% trans 'Search Websites' %}" />
                        </span>
                        <span>
                            <label>{% trans 'From' %}</label>
                            <rs:DatePicker name="date_start" class="datepickerTimestamp"></rs:DatePicker>
                            <label>{% trans 'To' %}</label>
                            <rs:DatePicker name="date_end" class="datepickerTimestamp" dpCurrent="true">
                                <button type="button" dpCurrentBtn="" onclick="FileStatistics.setCurrentTime(event)">{% trans 'Now' %}</button>
                            </rs:DatePicker>
                
                        </span>
                
                        <input type="hidden" name="timeoffset"/>
                
                        <button type="button" onclick="FileStatistics.handleOnsearch()">{% trans 'Search ' %}</button>
                    </rs:Form>
                </section>
                
                <section>
                    <rs:Table name="statisticsTable"
                              header="{% trans 'File name' %}|{% trans 'File format' %}|{% trans 'File size (Byte)' %}|{% trans 'Websites' %}|{% trans 'path' %}|{% trans 'Destination address' %}|{% trans 'Download Counts' %}|{% trans 'Recent download time' %}"
                              cells="file_name|file_suffix|file_size|server_name|path|dst_ip|download_cnt|last_download_time"
                              emptyText="{% trans 'No result' %}"
                              onCreateCell="FileStatistics.handleOnCreateCell(event)"></rs:Table>
                
                    <rs:Pages id="statisticsPageList" currentPageStyleClass="page-active"
                              hasNavigation="false" onSelectPage="FileStatistics.handleOnSelectPage(event)">
                    </rs:Pages>
                </section>
            </div>

            <div rs:name="{% trans 'Settings'%}" rsid="file_settings" id="__FILE_SETTINGS__">
                <rs:Form model="#mDownload">
                    <section>
                        <header>
                            <span>{% trans 'Download Control' %}</span>
                            <p>{% trans "The Policy and Cache File (if enabled) are executed if clients download any files that meet one of the conditions below." %}</p>
                        </header>
    
                        <ul>
                            <li>
                                <label>{% trans 'File Size Threshold (byte)' %}</label>
                                <input type="text" class="use_check" name="max_length" maxlength="26" v:disabled="!editable" />
                                <rs:Label class="enable_label">{% trans "Enable" %} <rs:Checkbox name="enable_max_length" v:disabled="!editable"></rs:Checkbox></rs:Label>
                                <span
                                    class="ic-info helpIcon"
                                    onmouseenter="FileSettings.handleOnShowTip('max_length', event)"
                                    onmouseleave="FileSettings.handleOnHideTip()"></span>
                            </li>
                            <li>
                                <label>{% trans 'File extension detection' %}</label>
                                <textarea
                                    class="use_check"
                                    name="file_extension"
                                    maxlength="1024"
                                    placeholder="{% trans 'Use commas to separate multiple extensions (e.g., exe, php, html)' %}"
                                    v:disabled="!editable"></textarea>
                                <rs:Label class="enable_label">{% trans "Enable" %} <rs:Checkbox name="enable_file_extension" v:disabled="!editable"></rs:Checkbox></rs:Label>
                                <span
                                    class="ic-info helpIcon"
                                    onmouseenter="FileSettings.handleOnShowTip('file_extension', event)"
                                    onmouseleave="FileSettings.handleOnHideTip()"></span>
                            </li>
                            <li>
                                <label>{% trans 'MIME type detection' %}</label>
                                <rs:MultiSelect
                                    rs:disabled="!editable"
                                    class="use_check"
                                    name="all_mime_types"
                                    supportSelectAll="false"
                                    id="__MIME_LIST__"
                                    needFilter="true"
                                    onCheckItem="FileSettings.handleCancelErrorMark()">
                                </rs:MultiSelect>
                                <rs:Label class="enable_label">{% trans "Enable" %} <rs:Checkbox name="enable_mime_types" v:disabled="!editable"></rs:Checkbox></rs:Label>
                                <span
                                    class="ic-info helpIcon mimeTypeTip"
                                    onmouseenter="FileSettings.handleOnShowTip('all_mime_types', event)"
                                    onmouseleave="FileSettings.handleOnHideTip()"></span>
                            </li>
                            <li>
                                <label>{% trans 'Policy' %}</label>
                                <rs:Select
                                    rsid="action"
                                    v:disabled="!editable"
                                    name="action">
                                    <div rs:visible="!isMirrorMode" rs-option value="block">{% trans "Block" %}</div>
                                    <div rs-option value="pass">{% trans "Pass" %}</div>
                                </rs:Select>
                            </li>
                            <li>
                                <label>{% trans 'Whether to save file' %}</label>
                                <rs:Checkbox rsid="is_save_file" name="is_save_file" v:disabled="!editable"></rs:Checkbox>
                                <span
                                        class="ic-info helpIcon"
                                        onmouseenter="FileSettings.handleOnShowTip('is_save_file', event)"
                                        onmouseleave="FileSettings.handleOnHideTip()"></span>
                            </li>
                        </ul>
                    </section>
                    <footer v:if="editable">
                        <p>
                            <button
                                id="__SUBMIT_SETTINGS__"
                                v:disabled="!editable"
                                type="button"
                                class="floatRight"
                                important
                                operation
                                onclick="FileSettings.saveData()">{% trans 'Save ' %}</button>
                        </p>
                    </footer>
                </rs:Form>
            </div>
            

    </rs:TabPanel>
    

{% endblock %}

{% block script %}
    <script language="JavaScript" type="text/javascript">
        rs.plugin({
            DatePicker: '/static/js/rs-customized/ui.DatePicker.js',
            Pages: '/static/js/rs/ui.Pages.js'
        });

        {% autoescape off %}
            var date_start = "{{ date_start }}";
            var date_end = "{{ date_end }}";
        

        {% endautoescape off %}

        var isMirrorMode = matchDeployMode('mirror');

        /****
         * 
         * 文件监测报表
         * 
         * ****/
        var mSearch = {
            keyword: '',
            date_start: '', 
            date_end: '', 
            timeoffset: new Date().getTimezoneOffset() * 60,
            page: 0,
            keyword_searched: '', //用于存储查找过的keyword
            date_start_searched: '', //用于存储查找过的开始时间
            date_end_searched: '' //用于存储查找过的结束时间
        }

        var FileStatistics =  (function() {
            var statistics_data = [];
            
            function initFileStatistics() {
                getFileLogs();
            }

            function setCurrentTime(evt) {
                evt.stopPropagation();
                mSearch.date_end = formatDate(parseInt(Math.floor(new Date().getTime())), true);
            }

            function handleOnsearch() {
                mSearch.date_start = rs('#date_start').value; //解决时分秒选择无效的问题，todo，转化成时间戳
                mSearch.date_end = rs('#date_end').value; //解决时分秒选择无效的问题，todo，转化成时间戳
                if(fixTime(mSearch.date_start) > fixTime(mSearch.date_end)){
                    automaticPop.attr('icon-style','warning').open({ msg:"{% trans 'Start date is later than end date!' %}" });
                    return false;
                }

                //重置页码数据
                mSearch.page = 0;
                rs('#statisticsPageList').clear();

                //缓存过滤条件
                mSearch.keyword_searched = mSearch.keyword;
                mSearch.date_start_searched = fixTime(mSearch.date_start);
                mSearch.date_end_searched = fixTime(mSearch.date_end);

                searchFile();
            }

            //组装数据，查询文件
            function searchFile() {
                var data = {
                    keyword: mSearch.keyword_searched,
                    date_start: mSearch.date_start_searched, 
                    date_end: mSearch.date_end_searched, 
                    timeoffset: mSearch.timeoffset,
                    page: mSearch.page,
                }
                getFileLogs(data);
            }

            function getFileLogs(data) {
                service.get('/file_monitoring/get_file_list/',
                    {
                        mask: true,
                        data: data || '',
                        dataType: 'json',
                        success: function(res) {
                            fixLogList(res);
                        },
                        error: function() {
                            rsalert("{% trans 'Response data exception' %}");
                        }
                    }
                );
            }

            function fixLogList(res) {
                document.body.named('statisticsTable').data = res.op_logs;
                mSearch.date_start = formatDate(parseInt(res.date_start) * 1000, true);
                mSearch.date_end = formatDate(parseInt(res.date_end) * 1000, true);
                mSearch.date_start_searched = res.date_start;
                mSearch.date_end_searched = res.date_end;

                fixPageData(res.pagination);
            }

            function fixPageData(pagination) {
                var pagingComp = rs('#statisticsPageList');
                if (pagination) {
                    var page_info = pagination[pagination.length - 2][0];
                    pagingComp.init(page_info);
                } else {
                    pagingComp.clear();
                }
            }

            function fixTime(time){
                var timeTemp = new Date(time.replace(/-/g,'/')).getTime();
                return Math.floor(timeTemp/1000);
            }

            function handleOnCreateCell(evt) {
                switch (evt.index){
                    case 0: //file name
                        evt.preventDefault();
                        if (editable && (evt.data!="-")) {
                            var a = createLink(function () {
                                service.downloadFileMonitorFile(evt.data, evt.rowData.file_sign, evt.rowData.path);
                            }).appendTo(evt.element);
                            a.html(escapeHtml(evt.data));
                        } else {
                            evt.element.html(escapeHtml(evt.data));
                        }
                        break;
                    case 7: // down time
                        evt.preventDefault();
                        evt.element.html(formatDate(evt.data * 1000, true));
                        break;

                }
            }

            function handleOnSelectPage(event) {
                if(mSearch.page == event.data) return;
                mSearch.page = event.data;
                searchFile();
                scrollTop4MainContent();
            }
            
            
            return {
                init: initFileStatistics,
                setCurrentTime: setCurrentTime,
                handleOnsearch: handleOnsearch,
                handleOnCreateCell: handleOnCreateCell,
                handleOnSelectPage: handleOnSelectPage
            };
            
        })();

        

    </script>

{% endblock %}
