{% extends "v2/base.html" %}

{% load i18n %}
{% load nav_tags %}

{% block title %}{% trans 'General'%}{% endblock %}

{% block navigation %}{% trans 'General' %}{% endblock %}

{% block script %}
<script src="/static/js/exportFile.js"></script>
<script language="JavaScript" type="text/javascript">

    'use strict';

    /*
    ######################################
        Fix Dynamic Data
    ######################################
    */
   
    rs.plugin({
        Pages: '/static/js/rs/ui.Pages.js',
        FileUpload: '/static/js/rs/ui.FileUpload.js',
        PageManager: '/static/js/rs/data.PageManager.js'
    });

    var hasPreviousASP = {{result.has_previous_asp|lower}};
    var isDebugBuild = {%if is_build_debug == 1 %}true{%else%}false{%endif%};
    var isLicencseActivated = {{result.is_licencse_activated|lower}};
    var isUpstreamEditable = {{result.is_upstream_editable|lower}};
    var isMaster = {{result.is_master|lower}};
    var canUpgrade = {{result.can_upgrade|lower}};
    var inContainer = {{result.in_container|lower}};
    var inK8S = {{result.in_k8s|lower}};
    var canotUpgradeTip = '{{ result.canot_upgrade_tip }}';


    var hasError = false;
    var errorMsg = '';

    var nodeList = [];
    {% for node in result.ClusterNodeList %}
        nodeList.push({
            'ip':'{{node.ip}}',
            'id':'{{node.id}}',
            'status':'{{node.status}}',
            'role':'{{node.role}}'
        });
    {% endfor %}


    var UpdateVersionData = [{
        version:'{{ result.version_info }}',
        build:'{{ result.cluster_version }}'
    }];

    var nodeNum = 0;
    try {
        nodeNum = parseInt('{{ result.node_num }}');
    } catch(e) {
        rsalert('{% trans "The number of obtained nodes is abnormal." %}');
    };

    var errorTip = null;
    var dialog = null;
    var pkgFileControl = null;
    var geoFileControl = null;
    var systemFileControl = null;

    /*
    ######################################
        Initialize
    ######################################
    */

    {% autoescape off %}

    var CLICK_VERSION_COUNT = 0;
    var CLICK_BUILD_COUNT = 0;
    var DEVELOP_MODE_LIMIT_COUNT = 5;
    var QRCODE_LIMIT_COUNT = 3;
    var ecs_enable = {{ enhance_cluster_security | to_json }};
    var hhi_enable = {{ hide_hardware_info | to_json }};
    var ecs_ip_array = {{ ecs_ip_white_list | to_json }} .split(',');
    var relay_setting_data = {{ scheduled_export_configs | to_json }};
    var relay_setting_enabled = {{ scheduled_export | to_json }};
    var relay_setting_list = {{ scheduled_export_records | to_json }};
    var ecs_ip_white_list = rs('#__ECS_IP_WHITE_LIST__');
    var changelog = ''
    var isCVST = {{is_cvst|lower}};

    {% endautoescape %}

    var saveAndRelay = null;

    function dealFrequency(item) {
        var result = {};
        var frequencyArr = item.split(' ');
        if (frequencyArr.length === 5) {
            if (!isNaN(frequencyArr[0])
                && !isNaN(frequencyArr[1])
                && 0 <= frequencyArr[0] <= 59
                && 0 <= frequencyArr[1] <= 23
                && frequencyArr[2] == '*'
                && frequencyArr[3] == '*'
                && frequencyArr[4] == '*') {
                // 满足每天
                result.frequency = 'eachDay';
                result.minute = frequencyArr[0];
                result.hour = frequencyArr[1];
                return result;
            } else if (!isNaN(frequencyArr[0])
                && !isNaN(frequencyArr[1])
                && !isNaN(frequencyArr[4])
                && 0 <= frequencyArr[0] <= 59
                && 0 <= frequencyArr[1] <= 23
                && frequencyArr[2] == '*'
                && frequencyArr[3] == '*'
                && 0 <= frequencyArr[4] <= 6) {
                // 满足每周
                result.frequency = 'eachWeek';
                result.minute = frequencyArr[0];
                result.hour = frequencyArr[1];
                result.week = frequencyArr[4];
                return result;
            } else if (!isNaN(frequencyArr[0])
                && !isNaN(frequencyArr[1])
                && !isNaN(frequencyArr[2])
                && 0 <= frequencyArr[0] <= 59
                && 0 <= frequencyArr[1] <= 23
                && 1 <= frequencyArr[2] <= 12
                && frequencyArr[3] == '*'
                && frequencyArr[4] == '*') {
                // 满足每月
                result.frequency = 'eachMonth';
                result.minute = frequencyArr[0];
                result.hour = frequencyArr[1];
                result.month = frequencyArr[2];
                return result;
            } else {
                // 回显的数据有问题时
                result.frequency = 'eachDay';
                result.minute = '';
                result.hour = '';
                return result;
            }
        } else {
            // 回显的数据有问题时
            result.frequency = 'eachDay';
            result.minute = '';
            result.hour = '';
            return result;
        }
    }

    function saveAndRelayData(item) {
        var result = {};
        result.enabled = relay_setting_enabled;
        // 执行频率相关
        var frequencyObj = dealFrequency(item.frequency || '');
        result.frequency = frequencyObj.frequency;
        result.minute = frequencyObj.minute;
        result.hour = frequencyObj.hour;
        result.week = frequencyObj.week || '';
        result.month = frequencyObj.month || '';
        result.transmit_mode = item.transmit_mode || [{
                "protocol": "sftp",
                "remote_addr": "",
                "remote_port": "",
                "user_name": "",
                "user_pwd":  "",
                "target_path": ""
            }];
        saveAndRelay = result;

    }

    rs.bind('ready', function() {
        saveAndRelay.listen('frequency', function(key, value) {
            // 在切换值的时候将周，月置为默认值，时分置0 eachWeek eachMonth
            if (value === 'eachWeek') {
                saveAndRelay.week = 1
                saveAndRelay.month = ''
            } else if (value === 'eachMonth') {
                saveAndRelay.week = ''
                saveAndRelay.month = 1
            } else if (value === 'eachDay') {
                saveAndRelay.week = ''
                saveAndRelay.month = ''
            }
            saveAndRelay.minute = 0;
            saveAndRelay.hour = 0;
        });
    });

    saveAndRelayData(relay_setting_data);

    var settingRecordsList;

    function initPage(){
        document.body.fill({
            enhanceClusterSecurity: ecs_enable,
            hideHardwareInfo: hhi_enable,
            update_version: UpdateVersionData,
            canot_upgrade_tip: canotUpgradeTip,
            notice: '{{ result.notice }}'||'{% trans 'No previous version' %}'
        });

        errorTip = rs('#__ERROR_TIPS__');
        dialog = rs('#__INSTALL_PKG__');
        pkgFileControl = rs('#__UPLOAD_PKG_BTN__');
        geoFileControl = rs('#__UPLOAD_GEOLIB_BTN__');
        systemFileControl = rs('#__UPLOAD_SYSTEM_FILE__');
        settingRecordsList = rs('rs\\:Table[name=settingRecordsList]')

        var tmp = [];
        for (var i = 0; i < ecs_ip_array.length; i++) {
            if (ecs_ip_array[i] != '') {
                tmp.push({ip_and_prefix: ecs_ip_array[i]});
            }
        }
        ecs_ip_white_list.data = tmp;

        locateModuleByUrlQuery();

        getMonthOptions();
        initfrequencySelect('frequencySelect', frequencyOptions);
        initfrequencySelect('weekSelect', weekOptions);
        initfrequencySelect('monthSelect', monthOptions);
        settingRecordsList.data = relay_setting_list.scheduled_export_records;

        {% autoescape off %}
        CustomGeoIP.setData({{ result.custom_geo_ip|to_json }});
        {% endautoescape %}
    }


    var ModuleActions = {
        'CustomGeoIP': function() {
            rs('#CustomIpCustomSection').attr('collapse', null);
        }
    };
    function locateModuleByUrlQuery() {
        var module = utils.getUrlParams('module');
        if (module) {
            var action = ModuleActions[module];
            if (action) action();
        }
    }


    function addEcsIpWhiteList() {
        ecs_ip_white_list.addRow({ip_and_prefix: ''});
    }

    function handleOnCreateEcsIpWhiteList(event) {
        event.preventDefault();
        switch (event.index) {
            case 0:
                createTextInputbox('ip_and_prefix', event.rowData.ip_and_prefix, '{% trans "IP or network segment, e.g. ************* or ***********/24 or fd00::1 or fd00::0/16"%}')
                    .attr('disabled', auth('Enhance_Cluster_Security', 'w') ? null : '')
                    .appendTo(event.element);
                break;
            case 1:
                if (auth('Enhance_Cluster_Security', 'w')) {
                    createDeleteLink(function(){
                        ecs_ip_white_list.removeRow(event.row.index);
                    }, '{% trans "Delete"%}').appendTo(event.element);
                } else {
                    event.element.innerText = '--';
                }
                break;
        }
    }

    function handleEcsEnable(evt) {
        ecs_enable = evt.target.checked;
        if (ecs_enable) {
            rs('#ecs_ip').show();
            //rs('#ecs_mgt_port').show();
        } else {
            rs('#ecs_ip').hide();
            //rs('#ecs_mgt_port').hide();
        }
    }

    function check_ip_and_prefix(ip_and_prefix) {
        if (ip_and_prefix == '' || ip_and_prefix.replace(/\s/g,'') == '') {
            rsalert('{% trans "Cluster IP whitelist cannot be blank"%}');
            return false;
        }

        function atoi(a, max) {
            var i = parseInt(a);
            return (i.toString() == a && 0 <= i && i <= max) ? i : -1;
        }

        function ipv6ToBinary(ipv6Address) {
            // Split the IPv6 address into hexadecimal components
            var hextets = ipv6Address.split(':');
            // Convert each hextet to a 16-bit binary string
            var binaryStrings = hextets.map(function(hextet) {
                // Convert hextet to integer
                var decimalValue = parseInt(hextet, 16);
                // Convert decimal to 16-bit binary string, padded with zeros
                var binaryString = decimalValue.toString(2).padStart(16, '0');
                return binaryString;
            });
            // Join the binary strings
            var binaryString = binaryStrings.join('');
            return binaryString;
        }

        function expandIPv6(shortAddress) {
            if (shortAddress.endsWith('::')) {
                shortAddress += "0";
            }
            // Split the address into parts
            var parts = shortAddress.split(':');
            // Find the index of the compressed part (::)
            var compressedIndex = parts.indexOf('');
            // If no compression, return the original address
            if (compressedIndex === -1) {
                return shortAddress;
            }
            // Calculate the number of missing hextets
            var missingHextets = 8 - parts.length + 1;
            // Insert the missing hextets as '0'
            var expandedParts = rs.duplicate(parts);
            expandedParts.splice(compressedIndex, 1);
            for (var i = 0; i < missingHextets; i++) {
                expandedParts.splice(compressedIndex + i, 0, '0');
            }
            // Join the expanded parts with colons
            return expandedParts.join(':');
        }

        var msg = '{% trans "The cluster IP whitelist is incorrect:"%}';

        var a = ip_and_prefix.split('/');
        if (a.length > 2) {
            rsalert(msg + ip_and_prefix);
            return false;
        }

        if (a[0].indexOf(':') >= 0) {
            var prefix = 128;
            if (a.length == 2) {
                prefix = atoi(a[1], 128);
                if (prefix < 0) {
                    rsalert(msg + ip_and_prefix);
                    return false;
                }
            }
            if (Validation.IPV6(a[0])) {
                rsalert(msg + ip_and_prefix);
                return false;
            }
            if (prefix != 128) {
                if (!ipv6ToBinary(expandIPv6(a[0])).endsWith('0'.repeat(128-prefix))) {
                    rsalert(msg + ip_and_prefix);
                    return false;
                }
            }
            return true;
        } else {
            var prefix = 32;
            if (a.length == 2) {
                prefix = atoi(a[1], 32);
                if (prefix < 0) {
                    rsalert(msg + ip_and_prefix);
                    return false;
                }
            }

            var b = a[0].split('.');
            if (b.length != 4) {
                rsalert(msg + ip_and_prefix);
                return false;
            }

            var ip = 0;
            for (var i=0; i<4; i++) {
                var t = atoi(b[i], 255);
                if (t < 0) {
                    rsalert(msg + ip_and_prefix);
                    return false;
                }

                ip <<= 8;
                ip += t;
            }
            ip >>>= 0;

            if (prefix == 0) {
                if (ip != 0) {
                    rsalert(msg + ip_and_prefix);
                    return false;
                }
                return true;
            }

            if (prefix < 32) {
                var s = 32 - prefix;
                var r = ip >> s << s >>> 0;
                if (r != ip) {
                    rsalert(msg + ip_and_prefix + ' {% trans "Configuration should be"%} ' + (r>>>24) + '.' + ((r>>>16)&255) + '.' + ((r>>>8)&255) + '.' + (r&255) + '/' + prefix);
                    return false;
                }
            }
            return true;
        }
    }

    function saveEcs() {
        var ip_list = [];
        for (var i = 0; i < ecs_ip_white_list.data.length; i++) {
            var ip_and_prefix = ecs_ip_white_list.data[i].ip_and_prefix;
            if (!check_ip_and_prefix(ip_and_prefix)) {
                return;
            }
            ip_list.push(ip_and_prefix);
        }

        window.service.enhance_cluster_security(
            { enable: ecs_enable ? 1:0, ip_list: ip_list.join(',') },
            function(response){
                var res = JSON.parse(response);
                if (res.result == 'OK') {
                    rsalert("{% trans 'Saved successfully.' %}");
                } else {
                    rsalert("{% trans 'Failed to save.' %}");
                }
            });
    }

    function handleHhiEnable(evt) {
        hhi_enable = evt.target.checked;
    }

    function saveHhi() {
        window.service.hide_hardware_info(
            { enable: hhi_enable},
            function(response){
                var res = JSON.parse(response);
                if (res.result == 'OK') {
                    rsalert("{% trans 'Saved successfully.' %}");
                } else if (res.result == 'Unchanged') {
                    rsalert(gettext('Configuration unchanged'));
                } else {
                    rsalert("{% trans 'Failed to save.' %}");
                }
            });
    }

    function handleInstall(evt) {
        dialog.closeDialog();
        page.wait(true);
        service.installPKG({ action:'install' }, installSuccessCallback, installFailureCallback, nodeNum * 1200)
    }

    function handleCancelInstall(evt) {
        dialog.closeDialog();
    }

    function reloadPage() {
        if (embedMode) {
            top.location.reload();
        } else {
            refreshCurLocation();
        }
    }

    function installSuccessCallback(res) {
        page.wait(false);
        if (!res) return rsalert('{% trans "Response data exception" %}');
        if (res.stat && res.stat.status == 'FINISHED') {
            var error = res.update_error;
            if (!error) {
                if (global_params.heterogeneousCluster) {
                    var plt_name = 'x86(ubuntu)';
                    if (global_params.isArm) {
                        plt_name = 'arm';
                    } else if (global_params.isChnOS) {
                        plt_name = 'x86(kylin/uos)';
                    }
                    rsalert('{% trans "All x86 nodes in the heterogeneous cluster have been upgraded." %}'.replace('x86', plt_name), reloadPage);
                } else {
                    rsalert('{% trans "Update succeeded." %}', reloadPage);
                }
            } else {
                page.markError(pkgFileControl);
                rsalert(error);
            }
        } else {
            if (res.type == 'timeout') {
                // 升级超时时，提示用户耐心等待，升级状态查询仍继续进行。-- 给予用户提示还有个目的是告知用户系统正在升级并未卡死
                rsalert('{% trans "Updating is in progress and might take longer than usual." %}', function() {
                    page.wait(true);
                }, null, true);
            } else {
                rsalert(res.update_error || '{% trans "unknown mode." %}');
            }
        }
    }

    function handleUploadPKG(evt) {
        FileUploadResultHandler.process(evt.data, function(response) {
            if (response.error) {
                page.markError(pkgFileControl);
                rsalert(response.error);
            } else {
                rs('#__INSTALL_PKG__').open(response, null, null, "{% trans 'Version Update' %}");
            }
        });
    }

    function installFailureCallback() {
        page.wait(false);
        page.markError(pkgFileControl);
        rsalert('{% trans "System installation request exception" %}');
    }

    var canGetOperationCode = auth('Advanced_Operation_Code', 'rw');
    var needDevMode = auth('Developer_Mode', 'rw');

    function handleOnClickSystemVersion(event){
        if(!canGetOperationCode && !needDevMode) return;
        CLICK_VERSION_COUNT++;
        CLICK_BUILD_COUNT = 0;
    }

    function handleOnClickSystemBuildVersion(event){
        if(!canGetOperationCode && !needDevMode) return;
        CLICK_BUILD_COUNT++;

        if (CLICK_VERSION_COUNT != QRCODE_LIMIT_COUNT && CLICK_VERSION_COUNT != DEVELOP_MODE_LIMIT_COUNT) {
            clearCount();
        } else if (canGetOperationCode && (CLICK_VERSION_COUNT == QRCODE_LIMIT_COUNT) && (CLICK_BUILD_COUNT == QRCODE_LIMIT_COUNT)) {
            getAdvancedOperationCode();
        } else if (needDevMode && (CLICK_VERSION_COUNT == DEVELOP_MODE_LIMIT_COUNT) && (CLICK_BUILD_COUNT == DEVELOP_MODE_LIMIT_COUNT)) {
            var data = {
                serviceCallback: startDevelopMode,
                cancelRollback: function() { clearCount(); }
            };
            global_params.isDevDebug ? startDevelopMode() : triggerAdvancedQRCodeEvent(data);
        } else if (CLICK_VERSION_COUNT<CLICK_BUILD_COUNT) {
            clearCount();
        }
    }

    function getAdvancedOperationCode() {
        var qrDialog = rs('#__ADVANCED_CODE_DIALOG__');
        service.getAdvOperationCode(function(result) {
            var res = result;
            if (!res || !res.url || !res.b64_qrcode) return rsalert('{% trans "Response data exception" %}');
            res.expired = '{% trans "Verification code validation remaining time:" %}' + getTimeTxt(res.expired);
            qrDialog.data = res;
            qrDialog.open(null, null, null, '{% trans "Verification Code Acquisition" %}');
            qrDialog.named('url').href = res.url;
            clearCount();
        });

        function getTimeTxt(time) {
            var txt = '';
            if (isNaN(time)) return txt;
            var t = parseInt(time);
            var s = Math.floor((t%60));
            var m = Math.floor((t/60)%60);
            var h = Math.floor((t/60)/60);
            txt = formatTxt(h)+ ':'+ formatTxt(m)+':'+formatTxt(s);
            return txt;
        }

        function formatTxt(tx) {
            var str = tx.toString();
            if (str.length == 1) str = '0'+str;
            return str;
        }
    }

    function startDevelopMode(data) {
        window.service.startDeveloperMode(data, function success(response){
            if (response.error) {
                clearCount();
                rsalert(response.error);
                return;
            }

            rsalert("{% trans 'Enable developer mode' %}", function() {
                if (embedMode) {
                    parent.postMessage('enableDeveloperMode', '*');
                } else {
                    refreshCurLocation();
                }
            });

        }, function error(XMLHttpRequest, textStatus, errorThrown){
            err = "error_status: " + XMLHttpRequest.status + "\terror_state: " + XMLHttpRequest.readyState + "\terror_text: " + textStatus + " \terrorThrown: " + errorThrown;
            rsalert("{% trans 'Fail to enable developer mode. ' %}");
            console.log(err);
        });
    }

    function clearCount() {
        CLICK_VERSION_COUNT = 0;
        CLICK_BUILD_COUNT = 0;
    }

    function rollback() {
        if(!hasPreviousASP) return rsalert("{% trans 'No previous version' %}");
        ask("{% trans 'Are you sure you want to rollback?' %}", do_rollback);
    }

    function do_rollback() {
        page.wait(true);
        var duration = 30000;
        var taskScheduleDelay = 2000;
        service.rollback(handleSuccessRollback, handleFailRollback, duration, taskScheduleDelay);
    }


    function handleSuccessRollback(res) {
        page.wait(false);
        if (!res) return rsalert('{% trans "Response data exception" %}');
        if (res.stat && res.stat.status == 'FINISHED') {
            var error = res.update_error;
            if (!error) {
                rsalert('{% trans "Rollback succeeded" %}', function() {
                    if (embedMode) {
                        // webconsole自从被嵌入到新框架的iframe中后有两种情况：
                        // 1、新回滚到旧：需要让top加载旧界面的url
                        // 2、新回滚到新：需要让top原地刷新
                        // 在此处是没有途径知道回滚的包是属于什么情况的
                        // 为了兼容两者，这里只能让top直接跳转到主页，和原始的webconsole停留本页刷新不一致
                        top.location = location.protocol + '//' + location.host;
                    } else {
                        refreshCurLocation();
                    }
                });
            } else {
                rsalert(error, refreshCurLocation);
            }
        } else {
            if (res.type == 'timeout') {
                rsalert(res.timeoutError);
            } else {
                rsalert(res.update_error || '{% trans "unknown mode." %}');
            }
        }
    }

    function handleFailRollback(evt) {
        page.wait(false);
        rsalert('{% trans "System rollback request exception" %}');
    }

    function reboot(evt) {
        ask('{% trans "Reboot the system?" %}', function() {
            page.wait(true);
            window.service.reboot('{{ power_token }}', connectionOK, connectionFailed);
        });
    }

    function connectionOK(response){
        if(response && response.status) window.location.href = '/user/logout/';
    }

    function connectionFailed(){
        page.wait(false);
        rsalert('{% trans "Failed to reboot the system because of connection exception." %}', refreshCurLocation);
    }

    function shutdown(evt) {
        ask('{% trans "Shutdown the system?" %}', function() {
            var t = setTimeout(function(){
                window.location = location.origin;
            }, 5000);

            window.service.shutdown('{{ power_token }}', function(res){
                clearTimeout(t);
                page.wait(false);
                if (res) rsalert('{% trans "Failed to shutdown the system because of connection exception." %}', refreshCurLocation);
            });

        });
    }

    function restoreServer(){
        if((nodeList.length > 1) && isMaster){
            var masterCount = 0;
            var masterOnlineCount = 0;

            for(var i = 0; i < nodeList.length; i++){
                if(nodeList[i].role.search("master_server") != -1){
                    masterCount++;
                    if(isNodeOnline(nodeList[i].status)) masterOnlineCount++;
                }
            }

            if(masterOnlineCount-1 <= (masterCount - 1)/2)
                return rsalert("{% trans 'Warning! Not enough master nodes online! No master node can be deleted!' %}");
        }

        if (global_params.isBonded && !matchDeployMode('transparent')) {
            rsalert("{% trans 'Note: System will reboot to delete port bonding configurations.' %}", goReset);
        } else {
            setTimeout(goReset, 50);
        }
    }

    function goReset() {
        window.location = '/factory_reset';
    }

    function isNodeOnline(nodeStatus){
        // "Node is Online": Its status is not "Offline" nor "Zookeeper Error"
        return nodeStatus != 'Offline' && nodeStatus != 'ERRNO_004';
    }

    function handleUpdateGeoLib(evt) {
        FileUploadResultHandler.process(evt.data, function(res) {
            if (res.result == 1) {
                page.markError(geoFileControl);
                rsalert(res.error_msg || '{% trans "Failed to upload geographical position lib file" %}');
            } else if (res.result == 2) { // invalid file format
                page.markError(geoFileControl);
                rsalert(res.error_msg || '{% trans "Uploaded invalid database file" %}');
            } else if (res.result == 3) {
                page.markError(geoFileControl);
                rsalert(res.error_msg || '{% trans "Please upload the version 1 update package" %}');
            } else if (res.result == 4) {
                page.markError(geoFileControl);
                rsalert(res.error_msg || '{% trans "The compressed file uploaded cannot exceed 200MB" %}');
            } else if (res.result == 5) {
                page.markError(geoFileControl);
                rsalert(res.error_msg || '{% trans "The decompressed file cannot exceed 500MB" %}');
            } else if (res.result == 6) {
                page.markError(geoFileControl);
                rsalert(res.error_msg || '{% trans "Please make sure MaxMind GeoLib is included in the uploaded file" %}');
            } else {
                rsalert('{% trans "Geographical position lib file has been uploaded. Please check the latest lib file in 1 minute." %}', refreshCurLocation);
            }
        });
    }


    function handleExportSystemSettings(evt) {
        page.wait(true);
        window.service.exportSystemSettings(function(res) {
            var now = utils.formatDate(parseInt(Math.floor(new Date().getTime())),true);
            now = now.replace(/\s+/, '_').replace(/:/g, '_');
            var filename = rs.formatString('system_settings_{0}.json', now);
            exportFile(res, filename, 'json', function() {
                page.wait(false);
            });
        }, function() {
            rsalert('{% trans "Fail to download file" %}');
        }, evt.shiftKey);
    }

    function handleBeforeSubmitSystemFile(evt) {
        ask('{% trans "Import system settings maybe overwrite some currently settings. Are you sure to import?" %}', function() {
            if (global_params.isDevDebug) {
                systemFileControl.submit();
            } else {
                var data = {
                    serviceCallback: function(data) {
                        rs.Element('input').attr({ name:'adv_operation_code', type:'hidden', value:data.adv_operation_code }).appendTo(systemFileControl.child('form'));
                        systemFileControl.submit();
                    }
                };

                triggerAdvancedQRCodeEvent(data);
            }
        });
    }

    function handleUploadSystemFile(evt) {
        FileUploadResultHandler.process(evt.data, function(res) {
            if (res.importSuccess) {
                rsalert('{% trans "System settings has been imported!" %}', reloadPage);
            } else {
                page.markError(systemFileControl);
                rsalert(res.error || '{% trans "Please upload a valid system file." %}');
            }
        });
    }

    function saveClusterName(evt) {
        var cluster_name = document.named('cluster_name').value.trim();
        var len = cluster_name.length;
        if(len === 0 || len > 50){
            rsalert('{% trans "Length of cluster name should between 1 and 50" %}');
            return;
        } else if(!(/^[A-Za-z0-9\u4e00-\u9fa5]+$/.test(cluster_name))){
            rsalert('{% trans "Cluster name only support numbers, letters and Chinese." %}');
            return;
        }
        service.saveClusterName({'cluster_name': cluster_name}, function(data){
            if(data.save_success){
                rsalert('{% trans "Saved successfully." %}');
            }else{
                rsalert(data.error_msg);
            }
        });
    }

    function parse_release_date_from_version(release_version) {
        var release_date = ''
        try {
            var release_date_raw = release_version.split('.')[2].split('_')[0];
            release_date = release_date_raw.slice(0, 4) + '-' + release_date_raw.slice(4, 6) + '-' + release_date_raw.slice(6);
        } catch(error) {
            //console.log(error)
        } finally {
            return release_date
        }
    }



    /**
     * 地理位置纠正
    */
    var CustomGeoIP = (function() {
        
        var tooltip = {
            showMaxOverTip: function(evt) {
                if (isOverMaxCount()) {
                    rs('#ipRangeMaxCountTooltip').show(evt.pageX, evt.pageY);
                }
            },
            hideMaxOverTip: function() {
                rs('#ipRangeMaxCountTooltip').hide();
            },

            showIpTip: function(evt) {
                rs('#customGeoIpTip').show(evt.pageX, evt.pageY);
            },
            hideIpTip: function() {
                rs('#customGeoIpTip').hide();
            }
        };

        var EditType = {
            NEW: 'new',
            MODIFY: 'modify',
            DELETE: 'delete'
        };

        var table = null, editor = null, allCheckbox = null, bottomAllCheckbox = null, pages = null;
        var editType = EditType.NEW, editRowIndex = -1;
        var _model = getEmptyModel();
        var _selectedRows = [];
        var EMPTY_TEXT = '<span class="comment">--</span>';
        var SLASH_TEXT = '<span class="comment"> / </span>';
        var MAX_COUNT = 500;
        var oldData = '';  // 修改时，缓存编辑前的数据

        var pm = rs.XElement('PageManager', {pageSize: 20});
        pm.bind('indexChange', function(evt) {
            CustomGeoIP.init(evt.data);
            CustomGeoIP.unselectAll();
            pages.currentPage = pm.index;
            pages.init(pm.pageCount || 1);
        });

        
        return {
            model: _model,
            IPv4Prefix: [8, 32],
            IPv6Prefix: [8, 128],

            init: function(data) {
                table = rs('#__Custom_Ip_Range__');
                editor = rs('#__CustomGeoIPEditor__');
                allCheckbox = table.child('#selectedAll');
                bottomAllCheckbox = rs('#bottomSelectedAll');
                pages = rs('#_BIO_PAGES_');

                editor.child('input').bind('input change', function() {
                    clearEditorErrorMsg();
                });

                table.data = data;
            },

            setData: function(data) {
                pm.data = data;
            },

            pm: pm,

            getLocalText: function(rowData) {
                if (rowData.country || rowData.province || rowData.city) {
                    return (rs.killHTML(rowData.country) || EMPTY_TEXT)  + SLASH_TEXT + (rs.killHTML(rowData.province) || EMPTY_TEXT) + SLASH_TEXT + (rs.killHTML(rowData.city) || EMPTY_TEXT);
                } else {
                    return EMPTY_TEXT;
                }
            },

            editRow: function(rowData, rowIndex) {
                editType = EditType.MODIFY;
                editRowIndex = rowIndex;
                oldData = JSON.parse(JSON.stringify(rowData));
                openEditor(rowData);
            },

            deleteRow: function(rowData) {
                deleteProxy([rowData], '{% trans "Confirm to delete?" %}');
            },

            batchDelete: function() {
                deleteProxy(_selectedRows, rs.formatString('{% trans "{0} data is checked. Are you sure to delete?" %}', _selectedRows.length));
            },

            createNew: function() {
                if (isOverMaxCount()) return;
                editType = EditType.NEW;
                openEditor(getEmptyModel());
            },

            saveEdit: function() {
                var data = CustomGeoIP.model.getData();

                // 未修改，点击保存时无需提交请求，直接关闭弹框即可
                var isModify = editType == EditType.MODIFY;
                var dataChanged = false;
                if (isModify) {
                    dataChanged = isDataChange(data);
                    if (!dataChanged) {
                        closeEditor();
                        return;
                    }
                }

                // 校验
                var hasError = validate(data);
                if (hasError) return;

                // 保存
                if (isModify) data.old_ip = oldData.ip;  // 修改时，old_ip用于后端查找需要更新的对象
                saveConfig([data], function(isSaveSuccess, errorMsg) {
                    
                    if (isSaveSuccess) {

                        if (isModify) {
                            delete data.old_ip;
                            table.rows[editRowIndex].data = data;
                            table.updateRowChange();

                        } else {
                            rs('#_BIO_FILTER_').value = '';
                            CustomGeoIP.filterBioList();
                            pm.data.push(data);
                            pm.gotoLastPage();
                            changeAllCheckboxStatus();
                        }

                        closeEditor();
                        automaticPop.open({ msg: '{% trans "Save Successfully" %}' });
                    } else {
                        delete data.old_ip;
                        editor.errorMessage = errorMsg || '{% trans "Save failed" %}';
                    }
                }, function() {
                    delete data.old_ip;
                });
            },

            createHeaderCell: function(event) {
                switch(event.index) {
                    case 0:
                        event.preventDefault();
                        createCheckbox({}, { id: 'selectedAll', 'v:disabled': 'CustomGeoIP.isEmptyList()' })
                            .appendTo(event.element)
                            .bind('change', selecteAll);
                        break;
                }
            },

            selectItem: function(evt, rowData) {
                if (evt.currentTarget.checked) {
                    _selectedRows.push(rowData);
                } else {
                    removeDataFromSelectedRow(rowData);
                }

                changeAllCheckboxStatus();
            },

            hasSelectedItem: function() {
                return _selectedRows.length > 0;
            },

            onPageIndexChange: function(event) {
                pm.index = event.data + 1;
            },

            filterBioList: function(key) {
                if (key) {
                    pm.filter = function(item) {
                        var ip = item.ip || '';
                        if (ip.indexOf(key) > -1) {
                            return item;
                        }
                    }
                } else {
                    pm.filter = null;
                }
            },

            highlightKey: function(str, key) {
                return str.replace(key, '<b style="color: red;">' + key + '</b>');
            },

            unselectAll: unselectAll,
            selecteAll: selecteAll,
            clearEditorErrorMsg: clearEditorErrorMsg,
            isEmptyList: isEmptyList,
            isOverMaxCount: isOverMaxCount,
            close: closeEditor,
            tooltip: tooltip
        };

        // 判断编辑的数据是否改变
        function isDataChange(newData) {
            var isChange = false;
            for (var k in newData) {
                // 无需忽略大小写判断，这样方便修改大小写
                isChange = newData[k] != oldData[k];
                if (isChange) break;
            }

            return isChange;
        }

        function openEditor(data) {
            for (var key in data) {
                _model[key] = data[key];
            }

            editor.open();
            editor.named('ip').focus();
        }

        function validate(data) {
            var errorMsg = '';

            // 校验IP
            var ip = data.ip;
            if (Validation.EMPTY(ip) || Validation.IP_AND_PREFIX(ip, '', { ipv4: CustomGeoIP.IPv4Prefix[0], ipv6: CustomGeoIP.IPv6Prefix[0] })) {
                errorMsg = '{% trans "Please configure a valid IP or IP/prefix." %}'
                    + rs.formatString('{% trans "{0} prefix length supports {1} ~ {2}" %}', 'IPv4', CustomGeoIP.IPv4Prefix[0], CustomGeoIP.IPv4Prefix[1]) + '；'
                    + rs.formatString('{% trans "{0} prefix length supports {1} ~ {2}" %}', 'IPv6', CustomGeoIP.IPv6Prefix[0], CustomGeoIP.IPv6Prefix[1]) + '。';
            } else {
                // IP不能重复
                var isDuplicate = pm.data.some(function(d, i) {
                    var ipExists = d.ip.toLowerCase() === data.ip.toLowerCase();
                    if (editType === EditType.NEW) {
                        return ipExists;

                    } else if (editType === EditType.MODIFY) {
                        // var currentRow = (pm.pageSize * Math.abs(pm.index - 1)) + editRowIndex;
                        // if (i !== currentRow) {
                            // return ipExists && !dataChanged;
                        // }
                        // 编辑时不检查ip是否重复，因为在有filter存在的时候无法在全量数据中找到并排除自身
                    }
                });
                if (isDuplicate) errorMsg = '{% trans "The same IP already exists" %}';
            }

            if (errorMsg) {
                editor.errorMessage = errorMsg;
                editor.named('ip').attr('error', '').focus();
            }

            return errorMsg;
        }

        function closeEditor() {
            clearEditorErrorMsg();
            editor.closeDialog();
        }

        function clearEditorErrorMsg() {
            editor.hideErrorMessage();
            var errorElem = editor.child('input[error]');
            if (errorElem) errorElem.attr('error', null);
        }

        function deleteProxy(deleteRows, confirmMsg) {
            editType = EditType.DELETE;
            ask(confirmMsg, function() {
                saveConfig(deleteRows, function(isSaveSuccess, errorMsg) {
                    if (isSaveSuccess) {
                        var temp = Array.prototype.slice.call(deleteRows);
                        temp.forEach(function(row) {
                            var index = findRowIndex(row, pm.data);
                            if (index > -1) pm.data.splice(index, 1);
                            pm.updatePageIndex();
                        });

                        changeAllCheckboxStatus();
                        automaticPop.open({ msg: '{% trans "Delete successfully" %}' });

                    } else {
                        rsalert(errorMsg || '{% trans "Failed to delete" %}');
                    }
                });
            });
        }

        function saveConfig(param, successCallback, errorCallback) {
            var req = null;
            switch(editType) {
                case EditType.NEW:
                    req = service.post;
                    break;

                case EditType.MODIFY:
                    req = service.put;
                    break;

                case EditType.DELETE:
                    req = service.del;
                    break;
            }

            req('/system/custom_geo_ip/', {
                mask: true,
                data: JSON.stringify(param),
                dataType: 'json',
                success: function(resp) {
                    var isSaveSuccess = resp.result == 'success';
                    successCallback(isSaveSuccess, resp.message);
                },
                error: function() {
                    rsalert('{% trans "Interface exception" %}');
                    if (errorCallback) errorCallback();
                }
            });
        }

        function getEmptyModel() {
            return {
                ip: '',
                country: '',
                province: '',
                city:'',
                intranet: false
            };
        }

        function selecteAll(event) {
            var isCheck = event.currentTarget.checked;
            rs.each(table.rows, function(index, row){
                row.child('rs\\:CheckBox').checked = isCheck;
            });

            _selectedRows = isCheck ? pm.currentPageData : [];
            changeAllCheckboxStatus();
        }

        function unselectAll() {
            rs.each(table.rows, function(index, row){
                row.child('rs\\:CheckBox').checked = false;
            });

            _selectedRows = [];
            changeAllCheckboxStatus();
        }

        // 将勾选项从table中移出
        function removeDataFromTable(removeData) {
            var index = findRowIndex(removeData, table.data);
            if (index > -1) table.removeRow(index);
        }

        // 将勾选项从选中列表中移出
        function removeDataFromSelectedRow(removeData) {
            var index = findRowIndex(removeData, _selectedRows);
            if (index > -1) _selectedRows.splice(index, 1);
        }

        function findRowIndex(findData, source) {
            var index = -1;
            source.some(function(data, i) {
                if (data.ip.toLowerCase() == findData.ip.toLowerCase()) {
                    index = i;
                    return true;
                }
            });

            return index;
        }

        function changeAllCheckboxStatus() {
            var selectedLen = _selectedRows.length;
            if (selectedLen == 0) {
                allCheckbox.checked = bottomAllCheckbox.checked = false;
            } else if (selectedLen < table.data.length) {
                allCheckbox.checkedPart = bottomAllCheckbox.checkedPart = true;
            } else {
                allCheckbox.checked = bottomAllCheckbox.checked = true;
            }

            // 勾选动作或增删保存后，均需要重新触发v:xxx属性的计算
            update();
        }

        function update() {
            rs('#CustomIpCustomSection').updateDynamicAttributes();
        }

        function isOverMaxCount() {
            return table.data.length >= MAX_COUNT;
        }

        function isEmptyList() {
            return table.data.length == 0;
        }
    })();


    /************************
     * 定期转发系统配置相关
    *************************/
    var frequencyOptions = [
        {title: '{% trans "Every Day" %}', value: 'eachDay'},
        {title: '{% trans "Every Week" %}', value: 'eachWeek'},
        {title: '{% trans "Every Month" %}', value: 'eachMonth'}
    ];

    var weekOptions = [
        {title: '{% trans "Mon." %}', value: '1'},
        {title: '{% trans "Tues." %}', value: '2'},
        {title: '{% trans "Wed." %}', value: '3'},
        {title: '{% trans "Thurs." %}', value: '4'},
        {title: '{% trans "Fri." %}', value: '5'},
        {title: '{% trans "Sat." %}', value: '6'},
        {title: '{% trans "Sun." %}', value: '0'}
    ];

    var monthOptions = [];
    function getMonthOptions() {
        var result = [];
        var i = 1;
        while(i <= 31) {
            result.push({title: i+'{% trans "th" %}', value: i});
            i++;
        }
        monthOptions = result;
    }

    function initfrequencySelect(id, options) {
        var selector = rs('#' + id);
        var arr = [];
        var firstValue;
        rs.each(options, function(index, item){
            arr.push({key:item.value, value:item.title});
            if(index==0) firstValue = item.value;
        });
        selector.load(arr);
        if (!saveAndRelay) {
            selector.value = firstValue;
        }
        // if (saveAndRelay.)
    }

    var agreementOptions = [
        {key: 'sftp', value: 'sftp'}
    ]

    function markEditUserError(item, elemName) {
        page.markError(item.named(elemName));
    }

    function judgeData(item, name) {
        // 接收方IP地址 限制输入长度(最多输入100个字符)，支持IPv4、IPv6和域名
        if (name === 'remote_addr') {
            var remote_addr_data = item.named('remote_addr').value;
            var msg = Validation.IPV4(remote_addr_data) && Validation.IPV6(remote_addr_data) && Validation.DOMAIN(remote_addr_data);
            if (msg) {
                markEditUserError(item, 'remote_addr');
                rsalert('{% trans "Please enter an IPv4/IPv6 address or a qualified domain name" %}');
                return false;
            }
        }
        // 接收方端口
        if (name === 'remote_port') {
            var msg = Validation.PORT(item.named('remote_port').value);
            if (msg) {
                markEditUserError(item, 'remote_port');
                rsalert('{% trans "Please enter a valid port number" %}');
                return false;
            }
        }
        // 用户名
        if (name === 'user_name') {
            var msg = Validation.EMPTY(item.named('user_name').value);
            if (msg) {
                markEditUserError(item, 'user_name');
                rsalert('{% trans "Please enter a username!" %}');
                return false;
            }
        }
        // 用户密码
        if (name === 'user_pwd') {
            var msg = Validation.EMPTY(item.named('user_pwd').value);
            if (msg) {
                markEditUserError(item, 'user_pwd');
                rsalert('{% trans "Password cannot be blank" %}');
                return false;
            }
        }
        // 接收目录
        if (name === 'target_path') {
            var msg = Validation.EMPTY(item.named('target_path').value);
            if (msg || item.named('target_path').value.indexOf('/') != 0) {
                markEditUserError(item, 'target_path');
                rsalert('{% trans "Enter a path" %}');
                return false;
            }
        }
        return true;
    }

    var display_pwd = '****************';
    function getTransmitMode() {
        var senderListElem = rs('#transmitMode');
        var transmitModeList = senderListElem.child('.transmitModeList');
        transmitModeList = transmitModeList.length != undefined ? transmitModeList : [transmitModeList];
        var len = transmitModeList.length;
        var result = [], item = null, singleData = null, status = true;
        for(var i=0; i< len; i++){
            item = utils.deepCopyObject(saveAndRelay.transmit_mode[i]);
            var currentItem = transmitModeList[i];
            if (judgeData(currentItem, 'remote_addr') &&
            judgeData(currentItem, 'remote_port') &&
            judgeData(currentItem, 'target_path') &&
            judgeData(currentItem, 'user_name') &&
            judgeData(currentItem, 'user_pwd')) {
                item.protocol = currentItem.child('[rsid="protocol"]').value;
                item.remote_addr = currentItem.named('remote_addr').value;
                item.remote_port = currentItem.named('remote_port').value;
                item.user_name = currentItem.named('user_name').value;
                // item.user_pwd = _sha1_hash(currentItem.named('user_pwd').value);
                item.user_pwd = currentItem.named('user_pwd').value;
                item.target_path = currentItem.named('target_path').value;
                result.push(item);
            } else {
                status = false;
                return false;
            }
        }
        return {
            status: status,
            result: result
        }
    }

    function reset_show_pwd(param) {
        rs('[name=user_pwd]').value = display_pwd;
    }

    function sendRelaySetting(evt, type) {
        var param = {};
        param.enabled = saveAndRelay.enabled;
        // 点击保存的时候，如果enabled为false跳过检测,值取后端的值
        if (type === 'save' && !param.enabled) {
            param.frequency = relay_setting_data.frequency;
            param.transmit_mode = relay_setting_data.transmit_mode;
        } else {
             // 先判断执行频率
            var hEmpty = Validation.EMPTY(saveAndRelay.hour);
            var hMsg = Validation.POSITIVE_INT(saveAndRelay.hour);
            if (hEmpty || (hMsg || Number(saveAndRelay.hour) > 23) && Number(saveAndRelay.hour) !== 0) {
                markEditUserError(rs('#saveAndRelay'), 'hour');
                rsalert(rs.formatString('{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', 0, 23));
                return false;
            }

            var mEmpty = Validation.EMPTY(saveAndRelay.minute);
            var mMsg = Validation.POSITIVE_INT(saveAndRelay.minute);
            if (mEmpty || (mMsg || Number(saveAndRelay.minute) > 59) && Number(saveAndRelay.minute) !== 0) {
                markEditUserError(rs('#saveAndRelay'), 'minute');
                rsalert(rs.formatString('{% trans "Please enter a integral number greater than or equal to {0} and less than or equal to {1}" %}', 0, 59));
                return false;
            }

            // 还原frequency数据
            var month, week;
            if (saveAndRelay.frequency === 'eachDay') {
                month = '*';
                week = '*'
            } else if (saveAndRelay.frequency === 'eachWeek') {
                month = '*';
                week = saveAndRelay.week
            } else if (saveAndRelay.frequency === 'eachMonth') {
                month = saveAndRelay.month;
                week = '*'
            }
            var frequencyArr = [saveAndRelay.minute, saveAndRelay.hour, month, '*', week]

            // var frequencyArr = [saveAndRelay.minute, saveAndRelay.hour, saveAndRelay.month, '*', saveAndRelay.week]
            var frequencyStr = frequencyArr.join(' ');
            param.frequency = frequencyStr;

            // 再获取协议相关数据进行判断
            var transmitModeObj = getTransmitMode();
            if (!transmitModeObj.status) {
                    return false;
            } else {
                    param.transmit_mode = transmitModeObj.result;
            }
        }
       // 发送请求
       if (type === 'save') {
            // 保存
            saveRelaySetting(param);
       } else if (type === 'test') {
            // 测试
            testRelaySetting(param);
       }
    }

    // 保存配置
    function saveRelaySetting(param) {
        window.service.saveRelaySetting(param, function(resp) {
            if (resp.result == 'OK') {
                rsalert('{% trans "Save Successfully" %}');
            } else {
                rsalert(resp.err_msg || '{% trans "Save failed." %}');
            }
            reset_show_pwd(param);
        });
    }

    // 测试
    function testRelaySetting(param) {
        window.service.testRelaySetting(param, function(resp) {
            if (resp.result == 'OK') {
                // 刷新 定期转发系统配置执行记录
                var result = {};
                result.msg = resp.msg;
                result.status = '{% trans "Sent successfully:" %}';
                rs('#__SYSTEM_POP__').open(result, null, null, '{% trans "Test result" %}');
            } else {
                // 刷新 定期转发系统配置执行记录
                var result = {};
                result.msg = resp.msg;
                result.status = '{% trans "Send failed:" %}';
                rs('#__SYSTEM_POP__').open(result, null, null, '{% trans "Test result" %}');
            }
            // reset_show_pwd(param);
        })
    }


    function handleOnRelayListCell(evt) {
        switch(evt.index){
            case 4:
                evt.preventDefault();
                var err_msg = '';
                var cursor = 'default';
                var err_color = ''
                if (evt.data === 'failed') {
                    err_msg = evt.rowData.err_msg;
                    cursor = 'pointer';
                    err_color = 'notice';
                }
                var statusTag = rs.Element('span', {class: err_color})
                    .append(rs.Text(evt.data === 'success' ? '{% trans "success" %}' : '{% trans "Failed" %}'));
                if (evt.data === 'failed') {
                    statusTag.bind('mouseover', function(e) {
                        // page.titleTip(e.pageX, e.pageY, rs.formatString('<p>{% trans "Failure message" %}</p><textarea disabled type="text">{0}</textarea>', err_msg));
                        page.titleTip(e.pageX, e.pageY, rs.formatString('<p>{% trans "Failure message" %}</p><p class="textareaStyle" disabled type="text">{0}</p>', err_msg));
                    }).bind('mouseout', function() {
                        page.titleTipHide();
                    })
                }
                statusTag.style.cursor = cursor;
                evt.element.append(statusTag);
                break;
        }
    }

    function parseIntNum(evt) {
        evt.target.value = parseInt(evt.target.value);
    }
</script>

<style>
    ul ul{
        display: inline-block;
        padding: 0px !important;
    }

    rs\:ModalDialog [name=installDetail] {
        padding:10px 50px;
    }

    rs\:ModalDialog [name=installDetail] b {
        margin-right: 10px;
        text-align: right;
        display: inline-block;
        width: 50px;
    }

    .desc {
        margin-top:30px;
        margin-bottom:20px;
        font-size: 13px;
    }

    .inlineUl { display:inline-block; vertical-align: top; }
    .inlineUl label { width:54px; font-weight: 400; }
    html[lang="en"] .inlineUl label { width:75px; font-weight: 400; }
    div[name=rollbackBtnRow] { margin-top:20px; }
    li[name=canUpgradeItem], li[name=cannotUpgradeItem] { margin-bottom:0px; }
    #__ADVANCED_CODE_DIALOG__ img { width:50%; display: block; margin-left: auto; margin-right: auto; }
    #__ADVANCED_CODE_DIALOG__ .box { width:500px; }
    #__ADVANCED_CODE_DIALOG__ *[name=urlItem] { padding-bottom:10px; }
    #__ADVANCED_CODE_DIALOG__ *[name=url] { word-break: break-word; white-space:pre-wrap; display:block; }
    #ecs_mgt_port label:nth-child(2) { width: auto !important; }
    ul { padding-left: 0px; }

    .tooltip {
    position: relative;
    display: inline-block;
    }

    .tooltip .tooltiptext {
        display: none;
        width: 420px;
        max-height: 350px;
        overflow: auto;
        background-color: white;
        text-align: center;
        border-radius: 6px;
        padding: 0 8px;
        background-color: #1b1e24;
        box-shadow: 0 2px 4px 0 rgba(49, 55, 65, 0.5);
        border-radius: 5px;
        color: #fff;
        position: absolute;
        z-index: 99999;
    }

    .tooltip .tooltiptext p,
    .tooltip .tooltiptext h4{
        text-align: left;
        margin-block-start: 0.5em;
        margin-block-end: 0.5em;
    }

    .tooltip .tooltiptext p{
        padding-left: 20px;
    }

    .tooltip:hover .tooltiptext {
        display: inline-block!important;
        top: 0;
    }
    #__COMFIRM_RULESETPKG__ .tooltiptext {
        /* 定位 */
        position: fixed;
        top: 10%;
    }

    ul[name="wafUpgradePane"] > li, ul[name="systemUpgradePane"] > li { margin-bottom: 0px; }

    .textareaStyle {
        background-color: #f4f5f5 !important;
        border: 1px solid #d6d7d9 !important;
        color: #999 !important;
        padding: 5px;
        max-width: 160px;
        min-width: 100px;
    }

    .transmitModeList{
        margin-bottom: 12px;
    }

    .transmitModeList > label {
        display: inline-block;
        width: 160px;
        text-align: right;
        margin-right: 15px;
    }

    html[lang="en"] .transmitModeList > label {
        width: 180px;
    }

</style>

{% endblock %}

{% block content %}

    <section>

        <header><span>{% trans 'System Version' %}</span></header>
        <script>
            var isEdge = rs.browser.type === 'Edge';

            function onSelectVersion(evt) {
                if (isEdge) {
                    evt.preventDefault();
                }
            }

            function copyBuildVersion() {
                const version = '{{ result.build_info }}';
                navigator.permissions.query({
                    name: 'clipboard-write'
                }).then(function(result) {
                    if (result.state === 'granted' || result.state === 'prompt') {
                        navigator.clipboard.writeText(version).then(function() {
                            rsalert('{% trans "Copied" %}');
                        });
                    } else {
                        rsalert(version);
                    }
                });
            }
            
        </script>
        <ul>
            <li onclick="handleOnClickSystemVersion(event)" onselectstart="onSelectVersion(event)">
                <label>{% trans 'Version' %}</label>
                {{ result.version_info }}
            </li>
            <li onclick="handleOnClickSystemBuildVersion(event)" onselectstart="onSelectVersion(event)">
                <label>{% trans 'Build' %}</label>
                {{ result.build_info }}
                <a v:if="isEdge" 
                    class="ic-copy-o" 
                    style="cursor: pointer;"
                    onclick="copyBuildVersion()" 
                    onmouseover="page.titleTip(event.pageX, event.pageY, '{% trans 'copy' %}')"
                    onmouseout="page.titleTipHide()"></a>
            </li>
            <li v:if="auth('System_Update', 'w')">
                <rs:Form method="post" enctype="multipart/form-data">
                    <ul name="systemUpgradePane">
                        <li>
                            <label>{% trans 'System Update' %}</label>
                            {% csrf_token %}
                            <ul>
                            {% if result.version %}
                                <li>
                                    <rs:Table name="update_version"
                                              header="{% trans 'Version' %}|{% trans 'Build' %}"
                                              cells="version|build"></rs:Table>
                                    <button type="button" id="__INSTALL_BTN__">{% trans 'Install ' %}{% trans 'ForceShield Dynamic Application Protection' %}</button>
                                </li>
                            {% else %}
                                <li v:if="canUpgrade" name="canUpgradeItem">
                                    <rs:FileUpload rsid="uploadPKG" id="__UPLOAD_PKG_BTN__"
                                                   name="pkg" accept=".bin" action="/system/general/"
                                                   onData="handleUploadPKG(event)">
                                        <span>
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="uploadPKG" />
                                        </span>
                                    </rs:FileUpload>
                                </li>
                                <li v:if="!canUpgrade" name="cannotUpgradeItem">
                                    <span name="canot_upgrade_tip"></span>
                                    <a href="/system/license/">{% trans 'View Detail' %}</a>
                                </li>
                            {% endif %}
                            </ul>
                        </li>
                    </ul>
                </rs:Form>
            </li>
            <li v:if="auth('System_Rollback', 'r') && page.expertMode">
                <label expertMode>{% trans 'Roll Back' %}</label>
                {% if result.has_previous_asp %}
                <ul class="inlineUl" v:if="hasPreviousASP">
                    <li><span>{% trans 'Previous version info:' %}</span></li>
                    <li><label>{% trans 'Version' %}</label> {{ result.previous_asp_version }}</li>
                    <li><label>{% trans 'Build' %}</label> {{ result.previous_asp_cluster_version }}</li>
                    <li><label>{% trans 'Update Time' %}</label> {{ result.previous_asp_update_time }}</li>
                </ul>
                {% else %}
                <span name="notice" class="inlineBlock" v:if="!hasPreviousASP"></span>
                {% endif %}

                <div name="rollbackBtnRow" v:if="auth('System_Rollback', 'w')">
                    <label class="spaceLabel"></label>
                    <button rsid="bt_rollback" type="button" onclick="rollback()"
                            v:disabled="!hasPreviousASP">{% trans 'Roll Back' %}</button>
                </div>
            </li>
        </ul>
    </section>

    <section v:if="auth('Update_Geo_Lib', 'r') && page.expertMode" expertMode>
        <header><span>{% trans 'Geo Lib Update' %}</span></header>
        {% if not is_cvst %}
        <p>{% trans 'Download link:' %}<a href="https://lm.dev-rs.com/resource_manager/resource/download/1347/" target="_blank">{% trans 'Click here to login and download' %}</a> </br>
        </p>
        {% endif %}
        <ul>
            {% if is_cvst %}
            <li>
                <label>{% trans 'Last Update of GeoLib' %}</label> {{ result.geolib_update_time }}
            </li>
            <li>
                <label>{% trans 'Package Size of GeoLib' %}</label> {{ result.geolib_file_size }} MB
            </li>
            <li>
                <label></label>
                <rs:FileUpload id="__UPLOAD_GEOLIB_BTN__" name="lib" accept=".gz"
                               action="/system/updateGeoLib/"
                               onData="handleUpdateGeoLib(event)">
                    {% csrf_token %}
                </rs:FileUpload>
            </li>
            {% else %}
            <li>
                <label>{% trans 'Last Update of IPv4' %}</label> {{ result.geolib_update_time }}
            </li>
            <li>
                <label>{% trans 'Package Size of IPv4' %}</label> {{ result.geolib_file_size }} MB
            </li>
            <li>
                <label>{% trans 'Last Update of IPv6' %}</label> {{ result.geolib_update_time_v6 }}
            </li>
            <li>
                <label>{% trans 'Package Size of IPv6' %}</label> {{ result.geolib_file_size_v6 }} MB
            </li>
            <li v:if="auth('Update_Geo_Lib', 'w')">
                <label></label>
                <rs:FileUpload id="__UPLOAD_GEOLIB_BTN__" name="lib" accept=".bin"
                               action="/system/updateGeoLib/"
                               onData="handleUpdateGeoLib(event)">
                    {% csrf_token %}
                </rs:FileUpload>
            </li>
            {% endif %}
        </ul>
        
        <style>
            #__Custom_Ip_Range__ > div[header=header] > div:nth-child(1),
            #__Custom_Ip_Range__ div[name=_selected] {
                width: 60px;
            }

            #__Custom_Ip_Range__[readonly="true"] > div[header=header] > div:nth-child(1),
            #__Custom_Ip_Range__[readonly="true"] div[name=_selected] {
                display: none !important;
            }

            #__Custom_Ip_Range__ > div[header=header] > div:nth-child(2),
            #__Custom_Ip_Range__ div[name=_index] {
                width: 80px;
            }

            #__Custom_Ip_Range__ > div[header=header] > div:nth-child(3),
            #__Custom_Ip_Range__ div[name=_ip] {
                min-width: 320px;
            }

            #__Custom_Ip_Range__ > div[header=header] > div:nth-child(5),
            #__Custom_Ip_Range__ div[name=_intranet] {
                width: 100px;
                text-align: center;
            }

            #__Custom_Ip_Range__ div[name=_operation] {
                width: 120px;
            }

            #CustomIpCustomSection .bottomLeft {
                margin: 10px;
                line-height: 37px;
            }

            #CustomIpCustomSection .bottomLeft #batchDeleteBtn {
                margin: 0px;
                font-size: 13px;
            }

            #CustomIpCustomSection button[isOverMaxCount=true] {
                color: #a7a7a7;
                cursor: not-allowed;
            }

            #CustomIpCustomSection button[isOverMaxCount=true]:hover,
            #CustomIpCustomSection button[isOverMaxCount=true]:active {
                background-color: transparent;
            }
        </style>
        <section id="CustomIpCustomSection" innerSection togglesection collapse>
            <header>
                <span>{% trans "Geolocation Correction" %}</span>
            </header>
            <div>

                <input id="_BIO_FILTER_" 
                    type="text" 
                    placeholder="{% trans 'Filter by IP' %}" 
                    oninput="CustomGeoIP.filterBioList(this.value)"
                    style="margin-top: 8px;"/>

                <button add
                        v:if="auth('Update_Geo_Lib', 'w')"
                        style="float: right; font-size: 13px; margin-top: 0px;"
                        type="button"
                        onclick="CustomGeoIP.createNew()"
                        v:isOverMaxCount="CustomGeoIP.isOverMaxCount()"
                        onmouseover="CustomGeoIP.tooltip.showMaxOverTip(event)"
                        onmouseout="CustomGeoIP.tooltip.hideMaxOverTip()">{% trans 'New Configuration' %}</button>
            </div>
            <rs:Table
                id="__Custom_Ip_Range__"
                header="|{% trans 'Serial Number' %}|IP|{% trans 'Place of Belonging' %}|{% trans 'Intranet Address' %}|{% trans 'Operation' %}"
                onCreateHeaderCell="CustomGeoIP.createHeaderCell(event)"
                v:readonly="!auth('Update_Geo_Lib', 'w')">
                <div rs-temp>
                    <div name="_selected">
                        <rs:Checkbox @change="CustomGeoIP.selectItem(event, rowData)" v:disabled="!auth('Update_Geo_Lib', 'w')"></rs:Checkbox>
                    </div>
                    <div name="_index" v:text="(rowIndex + 1) + (CustomGeoIP.pm.index - 1) * CustomGeoIP.pm.pageSize"></div>
                    <div name="_ip" v:html="CustomGeoIP.highlightKey(rowData.ip, rs('#_BIO_FILTER_').value)"></div>
                    <div name="_localIp" v:html="CustomGeoIP.getLocalText(rowData)"></div>
                    <div name="_intranet">
                        <i v:if="rowData.intranet" class="ic-check statusIcon"></i>
                    </div>
                    <div name="_operation" v:if="auth('Update_Geo_Lib', 'w')">
                        <a @click="CustomGeoIP.editRow(rowData, rowIndex)" class="ic-pen" title="{% trans 'Edit ' %}"></a>
                        <a @click="CustomGeoIP.deleteRow(rowData)" class="ic-trash" title="{% trans 'Delete ' %}"></a>
                    </div>
                    <div name="_operation" v:if="!auth('Update_Geo_Lib', 'w')">--</div>
                </div>
            </rs:Table>
            <div v:show="!CustomGeoIP.isEmptyList()">
                <div v:if="auth('Update_Geo_Lib', 'w')" class="inlineBlock bottomLeft">
                    <rs:Label>
                        <rs:Checkbox
                            id="bottomSelectedAll"
                            onchange="CustomGeoIP.selecteAll(event)"
                            minsize
                            style="margin-bottom: 3px;"></rs:Checkbox>{% trans 'Checked All' %}
                    </rs:Label>
                    <button link
                        type="button"
                        onclick="CustomGeoIP.batchDelete()"
                        id="batchDeleteBtn"
                        v:show="CustomGeoIP.hasSelectedItem()">{% trans 'Delete ' %}</button>
                </div>
                <rs:Pages 
                    id="_BIO_PAGES_"
                    currentPageStyleClass="page-active"
                    hasNavigation="false" 
                    onSelectPage="CustomGeoIP.onPageIndexChange(event)"
                    style="float: right;"></rs:Pages>
            </div>
        </section>
        <rs:Tooltip id="ipRangeMaxCountTooltip">
            <div name="tooltipContent">
                {% blocktrans with count="50" %}The list has reached the limit of {{ count }} items{% endblocktrans %}
            </div>
        </rs:Tooltip>
        <rs:Tooltip id="customGeoIpTip">
            <div name="tooltipContent">
                {% blocktrans with ip="IPv4" min="8" max="32" %}{{ ip }} prefix length supports {{ min }} ~ {{ max }}{% endblocktrans %}{% trans "," %}{% trans "e.g." %}***********/24<br/>
                {% blocktrans with ip="IPv6" min="8" max="128" %}{{ ip }} prefix length supports {{ min }} ~ {{ max }}{% endblocktrans %}{% trans "," %}{% trans "e.g." %}fd00::1/64
            </div>
        </rs:Tooltip>

    </section>


    <section v:if="auth('Import_System_Settings', 'r') || auth('Export_System_Settings', 'r') || auth('Factory_Reset', 'w')">
        <header>
            <span>{% trans 'System Settings' %}</span>
            <p v:if="auth('Import_System_Settings', 'r') || auth('Export_System_Settings', 'r')">{% trans 'Export/Import System Settings' %}</p>
        </header>

        <ul>
            <li v:if="auth('Export_System_Settings', 'r')">
                <label>{% trans 'Export System Settings' %}</label>
                <button onclick="handleExportSystemSettings(event)">{% trans 'Export' %}</button>
            </li>

            <li v:if="auth('Import_System_Settings', 'w') && (isDebugBuild || isUpstreamEditable)">
                <label>{% trans 'Import System Settings' %}</label>
                <p>{{ result.config_form.non_field_errors }}</p>
                <rs:FileUpload id="__UPLOAD_SYSTEM_FILE__" name="configfile" accept=".json"
                               action="/system/import_system_config/"
                               onBeforeSubmit="handleBeforeSubmitSystemFile(event)"
                               onData="handleUploadSystemFile(event)">
                    {% csrf_token %}
                </rs:FileUpload>
            </li>
        </ul>

        <div rs:visible="page.expertMode && auth('Export_System_Settings', 'r')" expertMode innerSection>
            <header>
                <span>{% trans 'Transmit system settings regularly' %}</span>
            </header>
            <rs:Form id="saveAndRelay" model="#saveAndRelay" method="post">
                <!-- <p class="desc">{% trans 'Transmit system settings regularly' %}<span expertMode></span></p> -->
                <ul>
                    <li>
                        <label>{% trans 'Enable' %}</label>
                        <rs:Checkbox name="enabled" v:disabled="!auth('Export_System_Settings', 'w')"></rs:Checkbox>
                    </li>
                    <li v:show="saveAndRelay.enabled">
                        <label>{% trans 'Forwarding time' %}</label>
                        <rs:Select v:disabled="!auth('Export_System_Settings', 'w')" style="width: 100px;" id="frequencySelect" name="frequency" :data="frequencyOptions"></rs:Select>
                        <span>
                            <rs:Select v:disabled="!auth('Export_System_Settings', 'w')" v:show="saveAndRelay.frequency === 'eachWeek'" style="width: 100px;" id="weekSelect" name="week" :data="weekOptions"></rs:Select>
                            <rs:Select v:disabled="!auth('Export_System_Settings', 'w')" v:show="saveAndRelay.frequency === 'eachMonth'" style="width: 65px;" id="monthSelect" name="month" :data="monthOptions"></rs:Select>
                            <input v:disabled="!auth('Export_System_Settings', 'w')" oninput="parseIntNum(event)" style="width: 65px; vertical-align: middle;" placeholder="{% trans 'Hour' %}" type="number" id="hour4EachDay" name="hour" min="0" max="23">
                            <input v:disabled="!auth('Export_System_Settings', 'w')" oninput="parseIntNum(event)" style="width: 65px; vertical-align: middle;" placeholder="{% trans 'Minute' %}" type="number" id="minute4EachHour" name="minute" min="0" max="59">
                            <span>({% trans 'Hour:Minute' %})</span>
                        </span>
                    </li>
                    <li>
                        <rs:list id="transmitMode" name="transmit_mode" v:show="saveAndRelay.enabled">
                            <div class="transmitModeList" style="margin-bottom: 12px;">
                                <label>{% trans 'Forwarding target' %}</label>
                                <rs:Select v:disabled="!auth('Export_System_Settings', 'w')" style="width: 100px;" rsid="protocol" name="protocol" v:option="agreementOptions" v:value="rowData.protocol"></rs:Select>
                                <input v:disabled="!auth('Export_System_Settings', 'w')" type="text" placeholder="{% trans 'Address' %}" name="remote_addr" v:value="rowData.remote_addr" maxlength="100" />
                                <input v:disabled="!auth('Export_System_Settings', 'w')" style="width: 65px;" type="text" placeholder="{% trans 'Port' %}" name="remote_port" />
                                <input v:disabled="!auth('Export_System_Settings', 'w')" style="width: 90px;" type="text" placeholder="{% trans 'path' %}" name="target_path" v:value="rowData.target_path" maxlength="100" />
                                <input v:disabled="!auth('Export_System_Settings', 'w')" type="text" placeholder="{% trans 'Username' %}" name="user_name" v:value="rowData.user_name" maxlength="50" />
                                <input v:disabled="!auth('Export_System_Settings', 'w')" type="password" placeholder="{% trans 'Password' %}" name="user_pwd" v:value="rowData.user_pwd ? display_pwd : ''" autocomplete="off" maxlength="50" />
                                <!-- <input type="password" placeholder="{% trans 'Password' %}" name="user_pwd" v:value="rowData.user_pwd" autocomplete="off" maxlength="50" /> -->
                            </div>
                        </rs:list>
                    </li>
                    <li v:if="auth('Export_System_Settings', 'w')">
                        <label>&nbsp;</label>
                        <button type="button" onclick="sendRelaySetting(event, 'save')">{% trans 'Save' %}</button>
                        <button onclick="sendRelaySetting(event, 'test')" v:show="saveAndRelay.enabled" type="button">{% trans 'Test' %}</button>
                    </li>
                </ul>
            </rs:Form>

            <div v:show="saveAndRelay.enabled">
                <p class="desc">
                    <span>
                        {% trans 'Transmit system settings regularly execution record' %}
                    </span><br>
                    <span class="notice">{% trans 'Show only the last 10 records' %}</span>
                </p>
                <rs:Table name="settingRecordsList"
                header="{% trans 'Execution time' %}|{% trans 'File name' %}|{% trans 'Target' %}|{% trans 'Source Node' %}|{% trans 'Status' %}"
                cells="timestamp|file_name|recv_addr_port|node_ip|result"
                onCreateCell="handleOnRelayListCell(event)"
                emptyText="{% trans 'No result' %}">
                </rs:Table>
            </div>
        </div>

    </section>

    <section v:if="auth('Factory_Reset', 'w') && !inK8S">
        <header>
            <span>{% trans "Factory Reset" %}</span>
            <p>{% trans 'Reset the system to factory settings. All the changes made to the system will be lost permanently.' %}</p>
        </header>
        
        <ul>
            <li>
                <label>{% trans 'Factory Reset' %}</label>
                <button rsid="bt_restoreServer" type="button" onclick="restoreServer()">{% trans 'Factory Reset' %}</button>
            </li>
        </ul>
    </section>

    <section v:if="auth('Enhance_Cluster_Security', 'r') && !global_params.inK8s && !(global_params.inContainer && global_params.isCloudMode)" id="ecsSection" name="ecsSection">
        <header>
            <span>{% trans 'Enhance Cluster Security' %}</span>
            <p>{% trans 'Enhance RAS safety by banning IPs not included in the whitelist from accessing specific ports in the cluster.' %}</p>
        </header>

        <ul>
            <li>
                <label>{% trans 'Enable' %}</label>
                <rs:Checkbox
                    rsid="enhanceClusterSecurity"
                    name="enhanceClusterSecurity"
                    onchange="handleEcsEnable(event)"
                    v:disabled="!auth('Enhance_Cluster_Security', 'w')"></rs:Checkbox>
            </li>
            <li id="ecs_ip" rs:visible="ecs_enable">
                <rs:Table id="__ECS_IP_WHITE_LIST__"
                          editable="true"
                          header="{% trans 'Cluster IP whitelist |' %}"
                          cells="ip_and_prefix|null"
                          onCreateCell="handleOnCreateEcsIpWhiteList(event)"></rs:Table>
                <div class="rsTableFooter" v:if="auth('Enhance_Cluster_Security', 'w')">
                    <button type="button" class="rsTableIncreaseButton" onclick="addEcsIpWhiteList()">{% trans 'Add cluster IP whitelist' %}</button>
                </div>
            </li>
            <li v:if="auth('Enhance_Cluster_Security', 'w')">
                <label>&nbsp;</label>
                <button type="button" onclick="saveEcs()">{% trans 'Save' %}</button>
            </li>
        </ul>
    </section>

    <section v:if="auth('Hide_Hardware_Info', 'r')" id="hhiSection" name="hhiSection">
        <header>
            <span>{% trans 'Hide Hardware Info' %}</span>
            <p>{% trans 'Enable it to hide hardware information in Cluster Node List.' %}</p>
        </header>

        <ul>
            <li>
                <label>{% trans 'Enable' %}</label>
                <rs:Checkbox
                    rsid="hideHardwareInfo"
                    name="hideHardwareInfo"
                    onchange="handleHhiEnable(event)"
                    v:disabled="!auth('Hide_Hardware_Info', 'w')"></rs:Checkbox>
            </li>
            <li v:if="auth('Hide_Hardware_Info', 'w')">
                <label>&nbsp;</label>
                <button type="button" onclick="saveHhi()">{% trans 'Save' %}</button>
            </li>
        </ul>
    </section>

    <section v:if="(auth('Reboot', 'w') || auth('Shutdown', 'w')) && !inContainer">
        <header><span>{% trans 'Power' %}</span></header>
        <p>
            <button v:if="auth('Reboot', 'w')" type="button" onclick="reboot(event);" class="reboot">{% trans 'Reboot' %}</button>
            <button v:if="auth('Shutdown', 'w')" type="button" onclick="shutdown(event);" class="shutdown">{% trans 'Shutdown' %}</button>
        </p>
    </section>

    <section v:if="auth('Rename_Cluster', 'r')">
        <rs:Form>
            <header><span>{% trans 'Cluster Name' %}</span></header>
            <ul>
                <li>
                    <label>{% trans 'Cluster Name' %}</label>
                    <input name="cluster_name" type="text" value="{{ result.cluster_name }}" v:disabled="!auth('Rename_Cluster', 'w')" />
                </li>
                <li v:if="auth('Rename_Cluster', 'w')">
                    <label></label>
                    <button type="button" onclick="saveClusterName();">
                        {% trans 'Save ' %}
                    </button>
                </li>
            </ul>
        </rs:Form>
    </section>

    <rs:ModalDialog id="__INSTALL_PKG__" okText="{% trans 'Install' %}"
                    onOK="handleInstall(event)"
                    onCancel="handleCancelInstall(event)">
        <div name="content">
            <p>{% trans 'The new version has been uploaded. Are you sure to install?' %}</p>
            <div name="installDetail">
                <p>
                    <b>{% trans 'Version' %}</b>
                    <span name="version"></span>
                </p>
                <p>
                    <b>{% trans 'Build' %}</b>
                    <span name="cluster_version"></span>
                </p>
            </div>
        </div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__ADVANCED_CODE_DIALOG__" name="advancedCodeDialog" okText="{% trans 'Close' %}" customOperate="true">
        <div name="content">
            <p>
                {% trans "Get the verification code by visiting the following URL or scanning the QR code." %}
                <span name="expired"></span>
            </p>

            <ul>
                <li name="urlItem">
                    <a name="url" target="_blank"></a>
                </li>
                <li>
                    <img name="b64_qrcode" />
                </li>
            </ul>
        </div>
    </rs:ModalDialog>

    <rs:ModalDialog id="__SYSTEM_POP__" okText="{% trans 'OK' %}" cancelText="{% trans 'Cancel' %}" class="rs-dark-form">
        <div>
            <div class="messagebox-msg">
                <p name="status"></p>
                <textarea style="width: 82%;" disabled type="text" name="msg"></textarea>
            </div>
        </div>
    </rs:ModalDialog>

    <style>
        #__CustomGeoIPEditor__ table tr td:first-child {
            width: 90px;
            text-align: right;
            vertical-align: baseline;
            margin-bottom: 10px;
            padding-right: 8px;
            padding-top: 3px;
        }

        #__CustomGeoIPEditor__ .box {
            min-width: 565px;
            max-width: 650px;
        }

        #__CustomGeoIPEditor__ table input {
            width: 320px;
            margin-bottom: 10px;
            margin-right: 0px;
        }

        #__CustomGeoIPEditor__ table input.localField {
            display: block;
        }

        #__CustomGeoIPEditor__ table .localField {
            display: inline-block;
            margin-right: 0px;
            vertical-align: middle;
        }

        #__CustomGeoIPEditor__ table rs\:geograghselect rs\:Select {
            width: 100%;
        }

    </style>
    <rs:ModalDialog
        id="__CustomGeoIPEditor__"
        header="{% trans 'Geolocation Correction' %}"
        okText="{% trans 'Save' %}"
        cancelText="{% trans 'Cancel' %}"
        onOK="CustomGeoIP.saveEdit(event)"
        onCancel="CustomGeoIP.close()">
        <div name="content">
            <rs:Form model="#CustomGeoIP.model" id="CustomGeoIPForm">
                <p class="description">{% trans "Customize the location corresponding to the IP address, or correct the wrong location." %}</p>
                <table>
                    <tr>
                        <td rs-required style="padding-top: 5px;">IP</td>
                        <td>
                            <input name="ip" maxlength="50" type="text" placeholder="{% trans 'IP or IP/prefix' %}" />
                            <i class="ic-info inlineBlock" style="margin-top: -4px;" onmouseover="CustomGeoIP.tooltip.showIpTip(event)" onmouseout="CustomGeoIP.tooltip.hideIpTip()"></i>
                        </td>
                    </tr>
                    <tr>
                        <td>{% trans 'Place of Belonging' %}</td>
                        <td>
                            <input name="country" class="localField" maxlength="30" type="text" placeholder="{% trans 'Country' %}" />
                            <input name="province" class="localField" maxlength="30" type="text" placeholder="{% trans 'Province' %}" />
                            <input name="city" class="localField" maxlength="30" type="text" placeholder="{% trans 'City' %}" />
                        </td>
                    </tr>
                    <tr>
                        <td>{% trans 'Intranet Address' %}</td>
                        <td>
                            <rs:Checkbox name="intranet" onchange="CustomGeoIP.clearEditorErrorMsg"></rs:Checkbox>
                        </td>
                    </tr>
                </table>
            </rs:Form>
        </div>
    </rs:ModalDialog>
{% endblock %}
