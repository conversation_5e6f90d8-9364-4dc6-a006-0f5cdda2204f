{% extends "v2/base.html" %}
{% load i18n %}
{% load nav_tags %}
{% block title %} {% trans 'LLM Protection' %} {% endblock %}
{% block navigation %} {% trans 'LLM Protection' %} {% endblock %}






{% block content %}

<style>
    #__GLOBAL_LLM_INJECTION_DIALOG__ .box {
        width: 604px;
    }
    #__GLOBAL_LLM_INJECTION_DIALOG__ rs\:Slider {
        margin-right: 68px;
    }
    #__GLOBAL_LLM_INJECTION_DIALOG__ rs\:Multiselect{
        width: 90%;
        margin-right: 10px;
    }
    [name="global_llm_prompt_injection_list"] div[table-cell]:nth-child(1),
    [name="global_llm_pii_list"] div[table-cell]:nth-child(1) {
        width: 80px;
    }
    [name="global_llm_prompt_injection_list"] div[table-cell]:nth-child(3),
    [name="global_llm_pii_list"] div[table-cell]:nth-child(3) {
        width: 35%;
    }
    [name="global_llm_prompt_injection_list"] div[table-cell]:nth-child(5),
    [name="global_llm_pii_list"] div[table-cell]:nth-child(5) {
        width: 260px;
    }

    #SETTING_PANEL textarea {
        width: 80%;
        height: 68px;
    }

    #SETTING_PANEL .prompt_table div[row] div[table-cell]:nth-child(1) {
        width: 50%;
    }

    /*----针对IE9,IE10,IE11----*/
    @media screen and (min-width: 0\0
    ) {
        #__GLOBAL_LLM_INJECTION_DIALOG__ .security_alert_info input[type='text'] {
            width: 90%;
        }

        #__GLOBAL_LLM_INJECTION_DIALOG__ .security_alert_info textarea {
            width: 90% !important;
        }

        #__GLOBAL_LLM_PII_DIALOG__ .security_alert_info input[type='text'] {
            width: 90%;
        }

        #__GLOBAL_LLM_PII_DIALOG__ .security_alert_info textarea {
            width: 90% !important;
        }
    }
</style>

<rs:style smart xmlns:rs="http://www.w3.org/1999/html" xmlns:rs="http://www.w3.org/1999/html">

    html[lang='en'] {
        .longLabel {
            line-height: normal !important;
        }

        #__GLOBAL_LLM_INJECTION_DIALOG__ .box {
            width: 700px;
            max-width: 700px;
        }

        #__GLOBAL_LLM_INJECTION_DIALOG__ .security_alert_label {
            width: 30%;
        }

        #__GLOBAL_LLM_INJECTION_DIALOG__ .security_alert_info {
            width: 64%;
        }
    },

    .security_alert {
        padding-top: 0px;
    }

    .editor {

        .box > div[name='content'] {
            padding-bottom: 0px;
        }

        .description {
            color: gray;
            font-size: 12px;
            margin: 0px;
            font-weight: normal;
        }

        .rsForm {

            margin-bottom: 30px;

            input[type='text'] {
                width: 90%;
            }

            textarea {
                width: 90% !important;
            }

            .maskInputBox {
                font-size: larger;
                width: 50px !important;
                text-align: center;
            }

            .enableField {
                padding-bottom: 3px;
            }

            .slider {
                padding-top: 10px;
            }
        }
    }

</rs:style>


<rs:TabPanel id="__TAB_PANEL__" keepIndexWhenRefresh="true">

    <div rs:name="{% trans 'Protection agaist Prompt Injection' %}" rsid="promptInjectionTab">
        <section>
            <div>
                <p v:if="isEdit">
                    <button type="button" onclick="PromptInjection.addStrategy(event)">{% trans 'Create Strategy' %}</button>
                </p>

                <rs:Table name="global_llm_prompt_injection_list"
                          header="{% trans 'ID' %}|{% trans 'Name' %}|{% trans 'Descriptions' %}|{% trans 'Application Site' %}|{% trans 'Actions' %}"
                          emptyText="{% trans 'No result' %}">
                    <div rs-temp>
                        <div v:text="rowData.id"></div>
                        <div v:text="rowData.name"></div>
                        <div v:text="fixLongString(rowData.comment)"
                             @mouseover="showMoreComments(event, rowData.comment)"
                             @mouseout="hideErrorTip(event)">
                        </div>
                        <div class="table_tooltip"
                             @mouseover="showMoreSites(event,rowData.hosts)">
                            <div
                                    v:if="!isStrategyEditable(rowData, global_params.role, global_params.userName, true) && !rowData.hosts.length">
                                -</div>
                            <span v:text="fixMoreHosts(rowData.hosts,false)"></span>
                            <p v:if="fixHostsLength(rowData.hosts)" class="table_tooltiptext" style="display: none;"
                               v:text="fixMoreHosts(rowData.hosts,true)"></p>
                            <a @click="ApplySite.applySite(rowData, 'LLMPromptInjectionProtection', PromptInjection.loadList)" class="inlineBlock ic-pen"
                               href="javascript:void(0);" title="{% trans 'Modify application site' %}"
                               v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"></a>
                        </div>
                        <div>
                            <button type="button"
                                    v:if="!isStrategyEditable(rowData, global_params.role, global_params.userName) || !isEdit"
                                    @click="PromptInjection.editStrategy(rowData, false, rowIndex, event)">{% trans "Strategy Details" %}</button>
                            <button type="button"
                                    v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"
                                    @click="PromptInjection.editStrategy(rowData, true, rowIndex, event)">{% trans "Strategy Config" %}</button>
                            <button type="button"
                                    v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"
                                    @click="PromptInjection.editStrategy(rowData, true, rowIndex, event, true)">{% trans "Strategy Copy" %}</button>
                            <button type="button" important
                                    v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"
                                    @click="PromptInjection.delStrategy(rowData,event)">{% trans "Delete" %}</button>
                        </div>
                    </div>
                </rs:Table>
            </div>
        </section>

        <rs:ModalDialog id="__GLOBAL_LLM_INJECTION_DIALOG__"
                        class="editor"
                        okText="{% trans 'Save' %}"
                        cancelText="{% trans 'Close' %}"
                        onOK="PromptInjection.handleOnSaveStrategy(event)"
                        onCancel="PromptInjection.closeEditor(event)">

            <div name="content">

                <rs:Form model="#PromptInjection.model" modelName="LLM_INJECTION_EDITOR"
                         class="rsForm"
                         onValueError="PromptInjection.handleOnValueError(event)" onSetProperty="this.updateProperty()"
                         onSubmit="PromptInjection.handleOnSubmit(event)" id="__GLOBAL_LLM_INJECTION_FORM__">

                    <div class="security_alert">
                        <div class="security_alert_label">{% trans 'Name' %}</div>
                        <div class="security_alert_info">
                            <input type="text" name="name" maxlength="26" v:disabled="!PromptInjection.model.isEdit" />
                            <span class="ic-info helpIcon"
                                  onmouseover="showInputTip(event,'name')"
                                  onmouseout="hideErrorTip(event)"></span>
                        </div>
                    </div>

                    <div class="security_alert">
                        <div class="security_alert_label">{% trans 'Descriptions' %}</div>
                        <div class="security_alert_info">
                            <textarea name="comment" maxlength="200" v:disabled="!PromptInjection.model.isEdit"></textarea>
                        </div>
                    </div>

                    <div class="xmlAttackTabPane">

                        <div class="security_alert slider">
                            <div class="security_alert_label" style="transform: translateY(10px);">{% trans 'Risk Threshold' %}</div>
                            <div class="security_alert_info">
                                <rs:Slider name="prompt_injection__strictness_level"
                                           minText="{% trans 'Strict' %}" maxText="{% trans 'Loose' %}"
                                           v:disabled="!PromptInjection.model.isEdit"></rs:Slider>
                                <span class="ic-info helpIcon"
                                           onmouseover="showInputTip(event,'prompt_injection__strictness_level')"
                                           onmouseout="hideErrorTip(event)"></span>
                            </div>
                        </div>

                        <div class="security_alert">
                            <div class="security_alert_label">{% trans 'Message Roles' %}</div>
                            <div class="security_alert_info">
                                <rs:MultiSelect
                                    v:disabled="!PromptInjection.model.isEdit"
                                    supportSelectAll="false"
                                    id="prompt_injection__detect_roles"
                                    onCheckItem="PromptInjection.handleOnEnableRoles(event)"></rs:MultiSelect>

                                <span class="ic-info helpIcon"
                                      onmouseover="showInputTip(event, 'llm_detect_roles')"
                                      onmouseout="hideErrorTip(event)"></span>
                            </div>
                        </div>

                        <div class="security_alert" v:if="!isMirrorMode">
                            <div class="security_alert_label longLabel">{% trans 'Chinese Interception Response' %}</div>
                            <div class="security_alert_info">
                                <textarea name="prompt_injection__response_templates__zh" maxlength="200"
                                          v:disabled="!PromptInjection.model.isEdit"></textarea>
                                <span class="ic-info helpIcon"
                                      onmouseover="showInputTip(event, 'llm_interception_notice_cn')"
                                      onmouseout="hideErrorTip(event)"></span>
                            </div>
                        </div>

                        <div class="security_alert" v:if="!isMirrorMode">
                            <div class="security_alert_label longLabel">{% trans 'English Interception Response' %}</div>
                            <div class="security_alert_info">
                                <textarea name="prompt_injection__response_templates__en" maxlength="200"
                                            v:disabled="!PromptInjection.model.isEdit"></textarea>
                                <span class="ic-info helpIcon"
                                        onmouseover="showInputTip(event, 'llm_interception_notice_en')"
                                        onmouseout="hideErrorTip(event)"></span>
                            </div>
                        </div>

                    </div>
                </rs:Form>

            </div>
        </rs:ModalDialog>
    </div>

    <div rs:name="{% trans 'PII Detection' %}" rsid="piiDetectionTab" rs:tab-visible="!isApi">
        <section>
            <div>
                <p v:if="isEdit">
                    <button type="button" onclick="SensitiveDetection.addStrategy(event)">{% trans 'Create Strategy' %}</button>
                </p>

                <rs:Table name="global_llm_pii_list"
                          header="{% trans 'ID' %}|{% trans 'Name' %}|{% trans 'Descriptions' %}|{% trans 'Application Site' %}|{% trans 'Actions' %}"
                          emptyText="{% trans 'No result' %}">
                    <div rs-temp>
                        <div v:text="rowData.id"></div>
                        <div v:text="rowData.name"></div>
                        <div v:text="fixLongString(rowData.comment)"
                             @mouseover="showMoreComments(event, rowData.comment)"
                             @mouseout="hideErrorTip(event)">
                        </div>
                        <div class="table_tooltip"
                             @mouseover="showMoreSites(event,rowData.hosts)">
                            <div
                                    v:if="!isStrategyEditable(rowData, global_params.role, global_params.userName, true) && !rowData.hosts.length">
                                -</div>
                            <span v:text="fixMoreHosts(rowData.hosts,false)"></span>
                            <p v:if="fixHostsLength(rowData.hosts)" class="table_tooltiptext" style="display: none;"
                               v:text="fixMoreHosts(rowData.hosts,true)"></p>
                            <a @click="ApplySite.applySite(rowData, 'LLMSensitiveDetection', SensitiveDetection.loadList)" class="inlineBlock ic-pen"
                               href="javascript:void(0);" title="{% trans 'Modify application site' %}"
                               v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"></a>
                        </div>
                        <div>
                            <button type="button"
                                    v:if="!isStrategyEditable(rowData, global_params.role, global_params.userName) || !isEdit"
                                    @click="SensitiveDetection.editStrategy(rowData, false, rowIndex, event)">{% trans "Strategy Details" %}</button>
                            <button type="button"
                                    v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"
                                    @click="SensitiveDetection.editStrategy(rowData, true, rowIndex, event)">{% trans "Strategy Config" %}</button>
                            <button type="button"
                                    v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"
                                    @click="SensitiveDetection.editStrategy(rowData, true, rowIndex, event, true)">{% trans "Strategy Copy" %}</button>
                            <button type="button" important
                                    v:if="isStrategyEditable(rowData, global_params.role, global_params.userName) && isEdit"
                                    @click="SensitiveDetection.delStrategy(rowData,event)">{% trans "Delete" %}</button>
                        </div>
                    </div>
                </rs:Table>
            </div>
        </section>

        <rs:ModalDialog id="__GLOBAL_LLM_PII_DIALOG__"
                        class="editor"
                        okText="{% trans 'Save' %}"
                        cancelText="{% trans 'Close' %}"
                        onOK="SensitiveDetection.handleOnSaveStrategy(event)"
                        onCancel="SensitiveDetection.closeEditor(event)">

            <div name="content">

                <rs:Form model="#SensitiveDetection.model" modelName="LLM_PII_EDITOR"
                         class="rsForm"
                         onValueError="SensitiveDetection.handleOnValueError(event)" onSetProperty="this.updateProperty()"
                         onSubmit="SensitiveDetection.handleOnSubmit(event)" id="__GLOBAL_LLM_PII_FORM__">

                    <div class="security_alert">
                        <div class="security_alert_label">{% trans 'Name' %}</div>
                        <div class="security_alert_info">
                            <input type="text" name="name" maxlength="26" v:disabled="!SensitiveDetection.model.isEdit" />
                            <span class="ic-info helpIcon"
                                  onmouseover="showInputTip(event,'name')"
                                  onmouseout="hideErrorTip(event)"></span>
                        </div>
                    </div>

                    <div class="security_alert">
                        <div class="security_alert_label">{% trans 'Descriptions' %}</div>
                        <div class="security_alert_info">
                            <textarea name="comment" maxlength="200" v:disabled="!SensitiveDetection.model.isEdit"></textarea>
                        </div>
                    </div>

                    <div class="xmlAttackTabPane">

                        <div class="security_alert" v:if="!isMirrorMode">
                            <div class="security_alert_label">{% trans 'Masking' %}</div>
                            <div class="security_alert_info">
                                <rs:Checkbox name="sensitive_detection__anonymization__enabled"
                                             v:disabled="!SensitiveDetection.model.isEdit"></rs:Checkbox>
                                <span class="ic-info helpIcon"
                                      onmouseover="showInputTip(event,'llm_sensitive_detection_enable')"
                                      onmouseout="hideErrorTip(event)"></span>
                            </div>
                        </div>

                        <div class="security_alert" v:if="!isMirrorMode"
                             v:show="SensitiveDetection.model.sensitive_detection__anonymization__enabled">
                            <div class="security_alert_label">{% trans 'Masking Symbol' %}</div>
                            <div class="security_alert_info">
                                <input name="sensitive_detection__anonymization__mask_character"
                                       v:disabled="!SensitiveDetection.model.isEdit" type="text" maxlength="1"
                                       class="maskInputBox" />
                                <span class="ic-info helpIcon" onmouseover="showInputTip(event,'llm_mask_character')"
                                      onmouseout="hideErrorTip(event)"></span>
                            </div>
                        </div>

                        <div class="security_alert">
                                    <div class="security_alert_label">{% trans 'PII Types' %}</div>
                                    <div class="security_alert_info">
                                        <rs:CheckboxGroup name="sensitive_detection__pii_types"
                                                          options="#SensitiveDetection.piiOptions" v:disabled="!SensitiveDetection.model.isEdit" />
                                    </div>
                                </div>

                    </div>
                </rs:Form>

            </div>
        </rs:ModalDialog>
    </div >

    <div rs:name="{% trans 'LLM Corpus Upgrade' %}" rsid="llm_corpus_management" id="__LLM_CORPUS_UPGRADE__" rs:tab-visible="auth('LLM_SETTING', 'r')">
        <section id="__LLM_CORPUS_CURRENT_VERSION__">
            <header>
                <span>{% trans "Current version information" %}</span>
            </header>
            <div>
                <ul>
                    <li><label>{% trans "LLM engine version" %}</label><span name="llm_engine_version"></span></li>
                    <li><label>{% trans "LLM corpus version" %}</label><span name="llm_corpus_version"></span></li>
                    <li><label>{% trans "release date" %}</label><span name="release_date"></span></li>
                </ul>
            </div>
        </section>
        <section v:if="auth('LLM_SETTING', 'w')">
            <header>
                <span>{% trans "Manual upgrade" %}</span>
            </header>
            <div>
                <ul>
                    <li>
                        <label>{% trans "Select the llm corpus upgrade package" %}</label>
                        <span>
                                    <rs:FileUpload id="__LLM_CORPUS_FILE_FORM__"
                                                   name="llm_corpus_pkg" accept=".llm" action="/llm_setting/verify_llm_corpus_form/"
                                                   onData="LLMCorpusUpgrade.handleOnUploadCorpusPKGFeedback(event)">
                                    </rs:FileUpload>
                            </span>
                    </li>
                </ul>
            </div>
        </section>
        <section v:show="page.expertMode && auth('LLM_SETTING', 'w')" id="__LLM_CORPUS_ROLLBACK_VERSION__" expertmode>
            <header>
                <span>{% trans "rollback" %}</span>
            </header>
            <div>
                <p v:show="!LLMCorpusUpgrade.has_prev_version">{% trans 'same ruleset version' %}</p>
                <ul v:show="LLMCorpusUpgrade.has_prev_version">
                    <li>{% trans "Available Rollback LLM Corpus" %}</li>
                    <li><label>{% trans "Version" %}</label><span name="prev_version"></span></li>
                    <li><label>{% trans "release date" %}</label><span name="prev_release_date"></span></li>
                    <li><label> </label><button type="button" onclick="LLMCorpusUpgrade.confirmRollbackCorpus(event)">{% trans "rollback" %}</button></li>
                </ul>
            </div>
        </section>
        <rs:ModalDialog id="__CONFIRM_LLM_CORPUS_PKG__" okText="{% trans 'Install' %}"
                        onOK="LLMCorpusUpgrade.proceedUploadCorpusPKG(event)"
                        onCancel="LLMCorpusUpgrade.closeConfirmDialog()">
            <div name="content">
                <p name='upload_install'>{% trans 'The new llm corpus has been uploaded. Are you sure to install?' %}</p>
                <table>
                    <tbody>
                    <tr>
                        <td>{% trans 'Version' %}</td>
                        <td name="llm_corpus_version"></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </rs:ModalDialog>
    </div>

    <div rs:name="{% trans 'Settings' %}" rsid="settingsTab" id="SETTING_PANEL">

        <rs:style smart>
            #SETTING_PANEL {
                section {
                    div[row=row] > div[table-cell]:last-child {
                        width: 80px;
                    }
                    
                }
                
            }
        </rs:style>

        <section id="BLACK_LIST_SECTION">
            <header>
                <span>{% trans 'Blacklist of Prompt Words' %}</span>
                <p>{% trans 'The system will treat the blacklist prompts as malicious content when conducting risk assessment of prompt injection protections.' %}</p>
            </header>
            <rs:Table id="BLACK_LIST"
                      class="prompt_table"
                      @change="Settings.resetListError(Settings.blacklist)"
                      @input="Settings.resetListError(Settings.blacklist)"
                      onCreateHeaderCell="Settings.handleOnCreateHeaderCell(event)"
                      onCreateCell="Settings.handleOnCreateCell(event)"
                      header="{% trans 'Prompt' %}|{% trans 'Comment' %}|{% trans 'Operation' %}"
                      cells="content|comment|operation">
            </rs:Table>
            <div class="rsTableFooter" v:if="isSettingEdit">
                <button type="button"
                        class="rsTableIncreaseButton"
                        rs:disabled="!Settings.canAddMore(Settings.blacklist)"
                        onclick="Settings.addListItem(Settings.blacklist)">{% trans 'New Configuration' %}
                </button>
                <button type="button"
                        onclick="Settings.save(Settings.blacklist)">{% trans 'Save' %}
                </button>
            </div>
        </section>

        <section id="WHITE_LIST_SECTION">
            <header>
                <span>{% trans 'Whitelist of Prompt Words' %}</span>
                <p>{% trans 'The system will treat the whitelist prompts as safe content when conducting risk assessment of prompt injection protections.' %}</p>
            </header>
            <rs:Table id="WHITE_LIST"
                      class="prompt_table"
                      @change="Settings.resetListError(Settings.whitelist)"
                      @input="Settings.resetListError(Settings.whitelist)"
                      onCreateHeaderCell="Settings.handleOnCreateHeaderCell(event)"
                      onCreateCell="Settings.handleOnCreateCell(event)"
                      header="{% trans 'Prompt' %}|{% trans 'Comment' %}|{% trans 'Operation' %}"
                      cells="content|comment|operation">
            </rs:Table>
            <div class="rsTableFooter" v:if="isSettingEdit">
                <button type="button"
                        class="rsTableIncreaseButton"
                        rs:disabled="!Settings.canAddMore(Settings.whitelist)"
                        onclick="Settings.addListItem(Settings.whitelist)">{% trans 'New Configuration' %}
                </button>
                <button type="button"
                        onclick="Settings.save(Settings.whitelist)">{% trans 'Save' %}
                </button>
            </div>
        </section>

    </div>

</rs:TabPanel>

<rs:Tooltip id="__errorCodeTip__">
    <div name="tooltipContent"></div>
</rs:Tooltip>

{% include 'v2/waf/site_apply.html' %}

{% endblock %}






{% block script %}

<script src="/static/js/pages/waf/waf_common.js"></script>
<script>

    rs.plugin({
        Slider: '/static/js/rs-customized/ui.Slider.js',
        CheckboxGroup: '/static/js/rs/ui.CheckboxGroup.js',
        MultiSelect: '/static/js/rs-customized/ui.MultiSelect.js',
        FileUpload: '/static/js/rs/ui.FileUpload.js'

    }).bind('ready', function() {
        PromptInjection.init();
        SensitiveDetection.init();
        LLMCorpusUpgrade.init();
        Settings.init();
    });

    
    
    {% autoescape off %}
    var site_info = {{ waf_strategy.site_info|to_json }};
    var apply_info = {{ waf_strategy.apply_info|to_json }};
    var isEdit = auth('LLM_Protection', 'w');
    var isSettingEdit = auth('LLM_SETTING', 'w');
    var isMirrorMode = matchDeployMode('mirror');
    var isApi = isApiProduct(global_params.productType);
    {% endautoescape off %}




    var PromptInjection = new function (){

        var _this = rs(this);
        var moduleTitle = '{% trans "Protection agaist Prompt Injection" %}';
        var editor = rs('#__GLOBAL_LLM_INJECTION_DIALOG__');
        var form = rs('#__GLOBAL_LLM_INJECTION_FORM__');
        var list = rs('[name="global_llm_prompt_injection_list"]');
        var roleSelector = rs('#prompt_injection__detect_roles');
        var isCopy = false;

        this.model = {

            isEdit: true,

            id: -1,                                                                                 // -1 表示新增策略，可选策略ID（更新时使用）
            name: '',                                                                               // 策略名称（必填）
            comment: '',                                                                            // 策略描述（非必填）

            prompt_injection__enabled: true,
            prompt_injection__strictness_level: 75,                                                 // 严格度 0-100（对应滑动条），默认值90
            prompt_injection__response_templates__zh: '抱歉，我不能回答这个问题。',
            prompt_injection__response_templates__en: 'I\'m sorry, but I can\'t answer that question.',             // 遮蔽字符（默认*）
            prompt_injection__detect_roles: ['user'],
            _validation: {
                name: [function (value) { return checkName(value, list.data, _this.model.id); }],
                prompt_injection__response_templates__zh: [checkBlockTemplate, '{% trans "Chinese Interception Response cannot be empty. In addition to Chinese words, the characters allowed include numbers, letters, whitespaces, dots(.), underscores(_), dashes, parentheses, asterisks, plus, as well as commas, periods, exclamation marks, colons, semicolons and quote marks in Chinese or English." %}'],
                prompt_injection__response_templates__en: [checkBlockTemplate, '{% trans "English Interception Response cannot be empty. In addition to Chinese words, the characters allowed include numbers, letters, whitespaces, dots(.), underscores(_), dashes, parentheses, asterisks, plus, as well as commas, periods, exclamation marks, colons, semicolons and quote marks in Chinese or English." %}'],
                prompt_injection__detect_roles:[function (value) { return value.length === 0;}, "{% trans 'Please select at least one role.' %}"]
            }
        };

        this.init = function() {
            this.model.save();
            this.loadList();
            roleSelector.load(MESSAGE_ROLES);
        };

        this.loadList = function() {
            service.getLLMPromptInjectionStrategy(function success(data) {
                if (data.is_success) {
                    apply_info = data.apply_info;
                    site_info = data.site_info;
                    list.data = data.llm_prompt_injection_info.sort(function (a, b) { return a.id - b.id; });
                } else {
                    list.data = [];
                }
            });
        };

        this.addStrategy = function () {
            if (list.data.length >= MAX_LENGTH.LLMSecurityMax) {
                rsalert(rs.formatString("{% trans 'The number of module strategies exceeds {0}. You cannot create a new strategy.' %}",
                    MAX_LENGTH.LLMSecurityMax));
                return false;
            }
            resetModel();
            isCopy = false;
            this.model.isEdit = true;
            this.showEditor();
        };

        this.editStrategy = function (rowData, isEdit, rowIndex, event, copy) {
            resetModel();
            isCopy = copy === undefined ? false : copy;
            if (isCopy) {
                this.model.id = -1;
            }
            this.model.isEdit = isEdit;
            var flatten = new FlatDataConverter().flatten(rowData);
            Object.assign(this.model, flatten);
            this.showEditor();
        }

        this.handleOnSubmit = function (event) {
            event.preventDefault();
            var result = new FlatDataConverter().revert(_this.model.getData());
            delete result.isEdit;
            service.saveLLMPromptInjectionStrategy(result, function (response) {
                if (response.is_success) {
                    _this.loadList();
                    _this.closeEditor();
                    rsalert('{% trans "Save Successfully" %}');
                } else {
                    rsalert(response.error_msg);
                }
            }, function (response) {
                rsalert('{% trans "Abnormal operation! Please try again!" %}');
            });
        }

        this.delStrategy = function (rowData, event) {
            if (!isEdit) return;
            ask(rs.formatString('{% trans "Confirm to delete the strategy of {0}: {1}?" %}', moduleTitle, escapeHtml(rowData.name)), function () {
                window.service.deleteLLMPromptInjectionStrategy({ id: rowData.id }, function (result) {
                    if (result.is_success) {
                        _this.loadList();
                        rsalert('{% trans "Delete successfully" %}');
                    } else {
                        alertDeleteWithSitesTip(result);
                    }
                }, function (response) {
                    rsalert('{% trans "Abnormal operation! Please try again!" %}');
                });
            });
        }


        this.getter('editorTitle', function () {
            var title = this.isEdit ? '{% trans "Edit strategy of {0}" %}' : '{% trans "Strategy details of {0}" %}';
            title = rs.formatString(title, moduleTitle);
            if (this.model.id == -1) title = rs.formatString('{% trans "Create strategy of {0}" %}', moduleTitle);
            if (isCopy) {
                title = rs.formatString('{% trans "Copy Strategy For {0}" %}', moduleTitle);
                this.model.id = -1;
            }
            return title;
        });

        this.showEditor = function () {
            roleSelector.selectedDatas = this.model.prompt_injection__detect_roles;
            editor.open(null, null, null, this.editorTitle);
        };

        this.closeEditor = function (event) {
            editor.errorMessage = '';
            editor.hideErrorMessage();
            editor.closeDialog();
        }

        this.handleOnSaveStrategy = function (event) {
            if(this.model.id != '-1' && this.model.hosts.length > 0){
                var fix_data = [];
                for(var i = 0; i < this.model.hosts.length; i++){
                    fix_data.push(escapeHtml(this.model.hosts[i]));
                }
                ask(rs.formatString('{% trans "Saving this policy will affect the following {0} where this policy is applied. Confirm to save?<br/>{1}" %}','{% trans "Website " %}',fix_data.join('<br/>')),function() {
                    form.submit();
                });
            }else{
                form.submit();
            }
            
        }

        this.handleOnValueError = function (event) {
            rsalert(event.data.message);
        }

        this.handleOnEnableRoles = function(evt) {
            this.model.prompt_injection__detect_roles = roleSelector.selectedValues;
        }

        function resetModel() {
            _this.model.restore();
        }

        function checkBlockTemplate(value) {
            var v = value.trim();
            if (!/^(?!\s*$)[A-Za-z0-9\u4e00-\u9fa5\uFF08-\uFF09_.\-()*+，,。！：；“”‘’'";:! ]+$/.test(v)) {
                return 'error';
            }
        }

    };



    var SensitiveDetection = new function (){

        var _this = rs(this);
        var moduleTitle = '{% trans "PII Detection" %}';
        var editor = rs('#__GLOBAL_LLM_PII_DIALOG__');
        var form = rs('#__GLOBAL_LLM_PII_FORM__');
        var list = rs('[name="global_llm_pii_list"]');
        var isCopy = false;

        this.piiOptions = [
            { label: '{% trans "UnionPay Debit Card" %}', value: 'UnionPay_Debit_Card' },
            { label: '{% trans "1st-Gen ID Card" %}', value: 'First_Generation_Chinese_ID_Card' },
            { label: '{% trans "2nd-Gen ID Card" %}', value: 'Second_Generation_Chinese_ID_Card' },
            { label: '{% trans "Mobile Phone" %}', value: 'Chinese_Phone_Number' },
            { label: '{% trans "Credit Card" %}', value: 'Credit_Cards' }
        ];

        this.model = {

            isEdit: true,

            id: -1,                                                                                 // -1 表示新增策略，可选策略ID（更新时使用）
            name: '',                                                                               // 策略名称（必填）
            comment: '',                                                                            // 策略描述（非必填）

            sensitive_detection__enabled: false,
            sensitive_detection__pii_types: this.piiOptions.map(function (option) { return option.value; }),                                                                                        // 启用的检测类型（对应勾选项）
            sensitive_detection__anonymization__enabled: true,                                        // 脱敏开关（必填）
            sensitive_detection__anonymization__mask_character: '*',                                   // 遮蔽字符（默认*）

            _validation: {
                name: [function (value) { return checkName(value, list.data, _this.model.id); }],
                sensitive_detection__anonymization__mask_character: [checkMask],
                sensitive_detection__pii_types: [checkPiiEnabledTypes, gettext('Please configure at least one PII type.')] ,
            }
        };

        this.init = function() {
            this.model.save();
            this.loadList();
        };

        this.loadList = function() {
            service.getLLMSensitiveDetectionStrategy(function success(data) {
                if (data.is_success) {
                    apply_info = data.apply_info;
                    site_info = data.site_info;
                    list.data = data.llm_sensitive_detection_info.sort(function (a, b) { return a.id - b.id; });
                } else {
                    list.data = [];
                }
            });
        };

        this.addStrategy = function () {
            if (list.data.length >= MAX_LENGTH.LLMSecurityMax) {
                rsalert(rs.formatString("{% trans 'The number of module strategies exceeds {0}. You cannot create a new strategy.' %}",
                    MAX_LENGTH.LLMSecurityMax));
                return false;
            }
            resetModel();
            isCopy = false;
            this.model.isEdit = true;
            this.showEditor();
        };

        this.editStrategy = function (rowData, isEdit, rowIndex, event, copy) {
            resetModel();
            isCopy = copy === undefined ? false : copy;
            if (isCopy) {
                this.model.id = -1;
            }
            this.model.isEdit = isEdit;
            var flatten = new FlatDataConverter().flatten(rowData);
            Object.assign(this.model, flatten);
            this.showEditor();
        }

        this.handleOnSubmit = function (event) {
            event.preventDefault();
            var result = new FlatDataConverter().revert(_this.model.getData());
            delete result.isEdit;
            service.saveLLMSensitiveDetectionStrategy(result, function (response) {
                if (response.is_success) {
                    _this.loadList();
                    _this.closeEditor();
                    rsalert('{% trans "Save Successfully" %}');
                } else {
                    rsalert(response.error_msg);
                }
            }, function (response) {
                rsalert('{% trans "Abnormal operation! Please try again!" %}');
            });
        }

        this.delStrategy = function (rowData, event) {
            if (!isEdit) return;
            ask(rs.formatString('{% trans "Confirm to delete the strategy of {0}: {1}?" %}', moduleTitle, escapeHtml(rowData.name)), function () {
                window.service.deleteLLMSensitiveDetectionStrategy({ id: rowData.id }, function (result) {
                    if (result.is_success) {
                        _this.loadList();
                        rsalert('{% trans "Delete successfully" %}');
                    } else {
                        alertDeleteWithSitesTip(result);
                    }
                }, function (response) {
                    rsalert('{% trans "Abnormal operation! Please try again!" %}');
                });
            });
        }


        this.getter('editorTitle', function () {
            var title = this.isEdit ? '{% trans "Edit strategy of {0}" %}' : '{% trans "Strategy details of {0}" %}';
            title = rs.formatString(title, moduleTitle);
            if (this.model.id == -1) title = rs.formatString('{% trans "Create strategy of {0}" %}', moduleTitle);
            if (isCopy) {
                title = rs.formatString('{% trans "Copy Strategy For {0}" %}', moduleTitle);
                this.model.id = -1;
            }
            return title;
        });

        this.showEditor = function () {
            editor.open(null, null, null, this.editorTitle);
        };

        this.closeEditor = function (event) {
            editor.errorMessage = '';
            editor.hideErrorMessage();
            editor.closeDialog();
        }

        this.handleOnSaveStrategy = function (event) {
            if(this.model.id != '-1' && this.model.hosts.length > 0){
                var fix_data = [];
                for(var i = 0; i < this.model.hosts.length; i++){
                    fix_data.push(escapeHtml(this.model.hosts[i]));
                }
                ask(rs.formatString('{% trans "Saving this policy will affect the following {0} where this policy is applied. Confirm to save?<br/>{1}" %}','{% trans "Website " %}',fix_data.join('<br/>')),function() {
                    form.submit();
                });
            }else{
                form.submit();
            }
        }

        this.handleOnValueError = function (event) {
            rsalert(event.data.message);
        }

        function resetModel() {
            _this.model.restore();
        }

        function checkPiiEnabledTypes(value) {
            if (value.length === 0){
                return 'error';
            }
        }

        function checkMask(value) {
            var v = value.trim();
            if (v.length !== 1 || !/^[A-Za-z0-9@#$%&*+]+$/.test(v)) {
                return "{% trans 'Masking Symbol cannot be empty. A masking symbol could be a number or' %} @ # $ % & * +";
            }
        }


    };



    var Settings = new function() {

        var _this = rs(this);
        var LIST_LENGTH_LIMIT = 50;

        this.blacklist = rs('#BLACK_LIST');
        this.whitelist = rs('#WHITE_LIST');


        this.init = function() {
            this.loadList();
        }

        this.loadList = function() {
            service.getLLMSetting(function success(data) {
                if (data.is_success) {
                    _this.blacklist.data = data.data.blacklist;
                    _this.whitelist.data = data.data.whitelist;
                } else {
                    _this.blacklist.data = [];
                    _this.whitelist.data = [];
                }
            });
        }

        this.canAddMore = function(list) {
            return LIST_LENGTH_LIMIT > list.data.length;
        }

        this.addListItem = function(list) {
            list.addRow({content: '', comment: ''});
            updateListContainer(list);
        }

        this.removeListItemByIndex = function (list, index) {
            list.removeRow(index);
            updateListContainer(list);
        }

        this.handleOnCreateHeaderCell = function(event) {
            switch(event.index){
                case 2://操作
                    event.element.attr("v\:if","isSettingEdit");
                    break;
            }

        }

        this.handleOnCreateCell = function(event) {
            event.preventDefault();
            var textareaAttr = {
                    maxLength: 200,
                    'v:disabled': !isSettingEdit
                };
            var textareaAttrContent = {
                    maxLength: 300,
                    'v:disabled': !isSettingEdit
                };
            switch(event.index){
                case 0://content
                createTextarea(event.data, 'content', textareaAttrContent)
                            .bind('mouseover',function(event) {
                                if(isSettingEdit) showInputTip(event, 'llm_bw_list_keyword');
                            })
                            .bind('mouseout',hideErrorTip)
                            .appendTo(event.element);
                    break;
                case 1://comment
                createTextarea(event.data, 'comment', textareaAttr)
                            .appendTo(event.element);
                    break;
                case 2://操作
                    if(isSettingEdit) {
                        createDeleteLink(function(){
                            _this.removeListItemByIndex(event.target, event.row.index)
                        }).appendTo(event.element);
                    }
                    event.element.attr("v\:if","isSettingEdit");
                    break;
            }
        }

        this.save = function (list) {
            var errorObject = validateBWList(list);
            if (!errorObject.passed) {
                errorObject.element.attr('error', true);
                rsalert(errorObject.message);
                return;
            }

            var api = list === this.blacklist ? service.saveLLMPromptBlacklist : service.saveLLMPromptWhitelist;
            api(list.data, function(response) {
                if (!response.is_success) {
                    rsalert(response.error_msg);
                } else {
                    rsalert('{% trans "Save Successfully" %}');
                }
            });
        }

        this.resetListError = function (list) {
            var target = list.child('[error]', false);
            if (target) target.attr('error', null);
        }

        function updateListContainer(list) {
            var target = list === _this.blacklist ? '#BLACK_LIST_SECTION' : '#WHITE_LIST_SECTION';
            rs(target).updateProperty();
            _this.resetListError(list);
        }

        function validateBWList(list) {
            var result = {passed: true, element: null, message: ''};
            var existLib = {};
            list.data.some(function(item, index) {
                var content = item.content.toLowerCase();
                if (content === '') {
                    result.passed = false;
                    result.element = list.rows[index].child('textarea[name="content"]');
                    result.message = "{% trans 'Prompt cannot be empty.' %}";
                    return true;
                } else if (existLib[content]) {
                    result.passed = false;
                    result.element = list.rows[index].child('textarea[name="content"]');
                    result.message = "{% trans 'Duplicate prompts.' %}";
                    return true;
                }
                existLib[content] = 1;
            });
            return result;
        };
    };


</script>
<script>

    var LLMCorpusUpgrade = new function () {

        var _this = rs(this);
        var confirm_dialog = rs("#__CONFIRM_LLM_CORPUS_PKG__");
        this.has_prev_version = false;

        this.init = function() {
            this.loadPageData();
        }

        this.loadPageData = function() {
            service.getLLMUpgradeInfo(null, function success(data) {
                var current_version_data = {"llm_engine_version": '', "llm_corpus_version": '', "release_date": ''};
                var rollback_version_data = {"prev_version": '', "prev_release_date": ''};
                _this.has_prev_version = false;
                if (data.is_success) {
                    var llm_corpus_info = data.llm_corpus_info;
                    current_version_data = llm_corpus_info;
                    var rollback_llm_corpus_info = llm_corpus_info.rollback_llm_corpus_info;
                    if (rollback_llm_corpus_info) {
                        _this.has_prev_version = true;
                        rollback_version_data = rollback_llm_corpus_info;
                    }
                }
                rs('#__LLM_CORPUS_CURRENT_VERSION__').fill(current_version_data);
                rs.updateProperty(rs('#__LLM_CORPUS_ROLLBACK_VERSION__'));
                rs('#__LLM_CORPUS_ROLLBACK_VERSION__').fill(rollback_version_data);
            });
        }

        this.handleOnUploadCorpusPKGFeedback = function (evt) {
            FileUploadResultHandler.process(evt.data, function(responseData) {
                if ('warning' in responseData) {
                    rsalert(responseData['warning']);
                } else if ('error' in responseData) {
                    rsalert(gettext('File upload verification failed. Please click [OK] to re-upload the file.'));
                } else if ('success' in responseData) {
                    rsalert('{% trans "llm corpus succeed to install" %}', refreshCurLocation);
                } else if ('installError' in responseData){
                    rsalert('{% trans "Install" %}' + '{% trans "Failed" %}');
                } else {
                    responseData['type'] = '1';
                    openConfirmDialog(responseData);
                }
            });
        }

        this.proceedUploadCorpusPKG = function (evt) {
            this.closeConfirmDialog();
            var data = confirm_dialog.data;
            if(data.type == 1){
                rs('#__LLM_CORPUS_FILE_FORM__').redirectSubmit('/llm_setting/upgrade_llm_corpus/');
            }
        }

        this.confirmRollbackCorpus = function (evt) {
            if(!auth('LLM_SETTING', 'w')) return;
            if(!this.has_prev_version) return rsalert("{% trans 'No previous version' %}");
            ask("{% trans 'Are you sure you want to restore to rollback to the previous corpus?' %}", rollbackLLMCorpus);
        }

        function rollbackLLMCorpus() {
            service.rollback_llm_corpus({}, function(data){
                if(data.success){
                    rsalert('{% trans "corpus has been successfully rollbacked to " %}' + data.success, refreshCurLocation);
                }else{
                    rsalert(data.error_msg);
                }
            }, function() { rsalert("{% trans 'Abnormal operation request!' %}")});
        }

        this.closeConfirmDialog = function () {
            confirm_dialog.closeDialog();
        }

        function openConfirmDialog(data) {
            confirm_dialog.child('p').hide();
            if(data.type == 1){
                confirm_dialog.named('upload_install').show();
            }
            confirm_dialog.open(data, null, null);
        }
    }
</script>
{% endblock %}
