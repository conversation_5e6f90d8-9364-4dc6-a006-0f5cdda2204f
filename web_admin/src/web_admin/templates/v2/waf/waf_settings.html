{% extends "v2/base.html" %}
{% load i18n %}
{% load nav_tags %}

{% block title %}{% trans 'Settings' %}{% endblock %}
{% block navigation %}{% trans 'Settings' %}{% endblock %}

{% block script %}
<script src="/static/js/pages/waf/waf_common.js"></script>
<script>
    rs.plugin({
        // File: '/static/js/rs/ui.File.js',
        FileUpload: '~/ui.FileUpload.js',
    });

    {% autoescape off %}
    var editable = auth('WAF_Config', 'w');
    var collection_mode = {{ collection_mode | to_json}};
    var time_per_second = {{ time_per_second | to_json}};
    var col_response_body = {{ col_response_body | to_json }};
    var ip_black = {{ ip_black| to_json}};
    var block_config = {{ block_config | to_json}};
    var adaption_decode = {{ adaption_decode | to_json }} || {};
    var upload_file_inflate = {{ upload_file_inflate | to_json }} || {};
    var waf_source_list = {{ waf_source_list | to_json}};
    var inject_attack_types= {{inject_attack_types|to_json}};
    var log_threshold_setting= {{log_threshold_setting|to_json}};
    var enabled_full_traffic_filter_log= {{enabled_full_traffic_filter_log|to_json}};
    var waf_for_api_request_only = {{ waf_for_api_request_only | to_json }} || {};
    var detection_size = {{ detection_size | to_json}};
    {% endautoescape %}

    var ResourceManager = (function() {
        var sortType = 'asc';
        var FILE_MAX_COUNT = 50;
        var FILE_TYPE_TRANS = {
            list: "{% trans 'List' %}",
            js: 'js',
            html: 'html'
        };

        function handleUploadResult(evt) {
            evt.target.refresh();

            FileUploadResultHandler.process(evt.data, function(res) {
                var index = CONTROL_MANAGER.existNameIndex;
                CONTROL_MANAGER.existNameIndex = -1;
                if (res && 'is_success' in res && !res.is_success) return rsalert(res.error_msg);

                if (res.is_success) {
                    var info = res.info;
                    if (index > -1) {
                        var rows = CONTROL_MANAGER.resourceTable.child('div[row=row]');
                        if (rows) {
                            if (rows.length) rows = rows[index];
                            rows.data = info;
                            CONTROL_MANAGER.resourceTable.data = sortListByTime(CONTROL_MANAGER.resourceTable.data, sortType);
                        }

                    } else {
                        var o = { file_name:info.file_name, type:info.type, time:info.time, size: info.size };
                        var temp = CONTROL_MANAGER.resourceTable.data;
                        sortType == 'asc' ? temp.unshift(o) : temp.push(o);
                        CONTROL_MANAGER.resourceTable.data = temp;
                        updateCurCount();
                        rs.updateProperty(rs('#resourceSection'));
                    }

                    automaticPop.open({ msg:"{% trans 'Succeed To Upload File' %}" }, null, 2000);
                    //if (info.type == 'list') CONTROL_MANAGER.resourceTable.trigger(rs.Event('ListFileChange'));

                } else {
                    rsalert(res.error_msg || "{% trans 'Fail To Upload File' %}");
                }
            });
        }

        function handleOnCreateHeader(evt) {
            switch(evt.index) {
                case 2:
                    evt.preventDefault();
                    var txt = rs.Text(evt.data);
                    var icon = rs.Element('i').addClass('ic-caret-down').css({ display:'inline-block', padding:'4px', cursor:'pointer', fontSize:'14px' });
                    var sortIcon = rs.Element('span').css({ cursor:'pointer' }).bind('click', function(cevt) {
                        icon.toggleClass('ic-caret-down ic-caret-up');
                        var isAsc = icon.hasClass('ic-caret-down');
                        sortType = isAsc ? 'asc' : 'desc';
                        CONTROL_MANAGER.resourceTable.data = sortListByTime(CONTROL_MANAGER.resourceTable.data, sortType);

                    }).append(txt).append(icon);

                    evt.element.append(sortIcon);
                    break;
            }
        }

        function handleOnCreateCell(evt) {
            var data = evt.rowData;
            switch(evt.index) {
                case 1:
                    evt.preventDefault();
                    evt.element.append(rs.Text(transType(evt.data.toUpperCase())));
                    break;

                case 2:
                    evt.preventDefault();
                    evt.element.append(rs.Text(formatTime(evt.data)));
                    break;

                case 3:
                    evt.preventDefault();
                    var size = parseInt(evt.data);
                    var d = 'B';
                    if (size > 1024) {
                        size = Math.round(size / 1000);
                        d = 'KB';
                    }

                    evt.element.append(rs.Text(size + d));
                    break;

                case 4:
                    evt.preventDefault();
                    var name = data['file_name'];
                    var typeText = transType(data['type']);
                    // 删除
                    if (isAdministrator() && !data.is_built_in) {
                        createDeleteLink(function() {
                            ask(rs.formatString(gettext('Are you sure delete {0} file: {1}?'), typeText, name), function() {
                                service.deleteWafResourceFile({
                                    file_name:data['file_name'],
                                    file_type: data['type']
                                }, function(resp) {
                                    if (!resp) return rsalert(gettext('Request exception'));
                                    if (resp.is_success) {
                                        CONTROL_MANAGER.resourceTable.removeRow(evt.row.index);
                                        updateCurCount();
                                        rs.updateProperty(rs('#resourceSection'));
                                        automaticPop.open({ msg:gettext('Delete Success') }, null, 2000);
                                        //if (data['type'] == 'list') CONTROL_MANAGER.resourceTable.trigger(rs.Event('ListFileChange'));
                                    } else {
                                        rsalert(resp.error_msg || gettext('Delete Failed.'));
                                    }
                                });
                            });
                        }, gettext('Delete'), {rsid:'deleteWafResourceFile'}).appendTo(evt.element);
                    }

                    // 下载
                    if (editable) {
                        createDownloadLink(function() {
                            service.downloadWafResourceFile(data['file_name'], data['type']);
                        }).appendTo(evt.element);
                    } else {
                        evt.element.innerHTML = '--';
                    }
            }
        }

        function handleSetFileName(evt, fileType) {
            var elem = evt.target;
            var fileInput = elem._file;
            var pathArr = elem.value.split(/[\\/]/g);
            var curName = pathArr[pathArr.length - 1];
            var datas = CONTROL_MANAGER.resourceTable.data;
            var existNameArr = datas.filter(function(data) {
                return data.file_name == curName && data.type == fileType;
            });


            if (existNameArr.length >= 1) {
                ask(transType(fileType).toUpperCase() + gettext('A file with the same name already exists in the type. Replace it with the new file?'), function() {
                    var existData = existNameArr[0];
                    CONTROL_MANAGER.existNameIndex = datas.indexOf(existData);
                    uploadFile(elem);

                }, function() {
                    elem.refresh();
                });

            } else {
                if (isMaxCount()) {
                    rsalert(rs.formatString(gettext('The number of files has exceeded the upper limit {0}'), FILE_MAX_COUNT), function() {
                        elem.refresh();
                    });

                    return;
                }

                CONTROL_MANAGER.existNameIndex = -1;
                uploadFile(elem);
            }
        }

        function uploadFile(elem) {
            elem.submit();
        }

        function formatTime(time) {
            return formatDate(parseInt(time*1000), true);
        }

        function sortListByTime(list, sortType) {
            var type = sortType || 'asc';
            list.sort(function(o1, o2) {
                if (type == 'asc') {
                    return o2.time - o1.time;
                } else if (type == 'desc') {
                    return o1.time - o2.time;
                }
            });

            var defaultArr = list.filter(function(o) {
                return o.is_built_in != null && o.is_built_in == true;
            });

            var otherArr = list.filter(function(o) {
                return o.is_built_in == null || o.is_built_in == false;
            });

            return [].concat(defaultArr, otherArr);
        }

        function isMaxCount() {
            return CONTROL_MANAGER.resourceTable.data.length >= FILE_MAX_COUNT;
        }

        function isAdministrator() {
            return editable;
        }

        function transType(type) {
            return FILE_TYPE_TRANS[type]||type;
        }

        function updateCurCount() {
            saveFilesInfoInStorage();
            mPage.source_list = CONTROL_MANAGER.resourceTable.data;
            rs('#curCount').textContent = CONTROL_MANAGER.resourceTable.data.length;
        }

        function saveFilesInfoInStorage() {
            rs.storage.set('ResouceFiles', JSON.stringify(CONTROL_MANAGER.resourceTable.data));
        }

        return {
            handleUploadResult: handleUploadResult,
            handleOnCreateHeader: handleOnCreateHeader,
            handleOnCreateCell: handleOnCreateCell,
            sortListByTime: sortListByTime,
            handleSetFileName: handleSetFileName,
            isMaxCount: isMaxCount,
            isAdministrator: isAdministrator,
            updateCurCount: updateCurCount
        }
    })();

    var mPage = {
        collection_mode: collection_mode,
        time_per_second: time_per_second,
        col_response_body: col_response_body,
        ip_black: ip_black,
        adaption_json_parse: adaption_decode.json_parse,
        adaption_xml_parse: adaption_decode.xml_parse,
        adaption_php_unserialize: adaption_decode.php_unserialize,
        adaption_multipart_parse: adaption_decode.multipart_parse,
        adaption_url_decode: adaption_decode.url_decode,
        adaption_plus_escape: adaption_decode.plus_escape,
        adaption_html_decode: adaption_decode.html_decode,
        adaption_slash_decode: adaption_decode.slash_decode,
        adaption_base64_decode: adaption_decode.base64_decode,
        adaption_utf7_decode: adaption_decode.utf7_decode,
        adaption_hex_decode: adaption_decode.hex_decode,
        upload_inflate_enable: upload_file_inflate.enable,
        waf_for_api_request_only: waf_for_api_request_only.enable,
        url_slash_html_decode_recur_level:adaption_decode.url_slash_html_decode_recur_level || 64,
        base64_decode_recur_level:adaption_decode.base64_decode_recur_level || 2,
        utf7_decode_recur_level:adaption_decode.utf7_decode_recur_level || 1,
        hex_decode_recur_level:adaption_decode.hex_decode_recur_level || 1,
        waf_source_list: ResourceManager.sortListByTime(waf_source_list),
        block_code: block_config.block_code,
        enable_block_content: block_config.enable_block_content,
        body_size_limit:detection_size.body_size_limit,
        file_size_limit:detection_size.file_size_limit,
        resp_body_size_limit:detection_size.resp_body_size_limit
    };

    function handleCollectionMode(evt) {
        mPage.collection_mode = evt.currentTarget.value.trim();
    }
    function save_attack_log_collect_setting(evt) {
        evt.stopPropagation();
        if (mPage.collection_mode != 'all') {
            if (Validation.POSITIVE_INT(mPage.time_per_second)) {
                markErrorOnly(rs('#time_per_second'));
                rsalert('{% trans "Collect per second" %} : {% trans "please enter positive integers" %} (1-1000000)');
                return;
            } else {
                if (parseInt(mPage.time_per_second) > 1000000) {
                    markErrorOnly(rs('#time_per_second'));
                    rsalert('{% trans "Collect per second" %} : {% trans "please enter positive integers" %} (1-1000000)');
                    return;
                }
            }
        }
        var submitData = {
            collection_mode: mPage.collection_mode,
            time_per_second: parseInt(mPage.time_per_second),
            col_response_body: mPage.col_response_body,
        }
        service.submitWafLogSettings(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });
    }

    function save_adaption_decode_setting(evt) {
        evt.stopPropagation();
        if (Validation.POSITIVE_INT(mPage.url_slash_html_decode_recur_level) ||parseInt(mPage.url_slash_html_decode_recur_level) > 128 || parseInt(mPage.url_slash_html_decode_recur_level) < 1) {
            markErrorOnly(rs('#url_slash_html_decode_recur_level'));
            rsalert('{% trans "Decode Recur Level" %} : {% trans "please enter positive integers" %} (1-128)');
            return;
        }

        if (Validation.POSITIVE_INT(mPage.base64_decode_recur_level) || parseInt(mPage.base64_decode_recur_level) > 16 || parseInt(mPage.base64_decode_recur_level) < 1) {
            markErrorOnly(rs('#base64_decode_recur_level'));
            rsalert('{% trans "Decode Recur Level" %} : {% trans "please enter positive integers" %} (1-16)');
            return;
        }

        if (Validation.POSITIVE_INT(mPage.utf7_decode_recur_level) || parseInt(mPage.utf7_decode_recur_level) > 16 || parseInt(mPage.utf7_decode_recur_level) < 1) {
            markErrorOnly(rs('#utf7_decode_recur_level'));
            rsalert('{% trans "Decode Recur Level" %} : {% trans "please enter positive integers" %} (1-16)');
            return;
        }

        if (Validation.POSITIVE_INT(mPage.hex_decode_recur_level) || parseInt(mPage.hex_decode_recur_level) > 16 || parseInt(mPage.hex_decode_recur_level) < 1) {
            markErrorOnly(rs('#hex_decode_recur_level'));
            rsalert('{% trans "Decode Recur Level" %} : {% trans "please enter positive integers" %} (1-16)');
            return;
        }

        var submitData = {
            json_parse: mPage.adaption_json_parse,
            xml_parse: mPage.adaption_xml_parse,
            php_unserialize: mPage.adaption_php_unserialize,
            url_decode: mPage.adaption_url_decode,
            multipart_parse: mPage.adaption_multipart_parse,
            plus_escape: mPage.adaption_plus_escape,
            html_decode: mPage.adaption_html_decode,
            slash_decode: mPage.adaption_slash_decode,
            base64_decode: mPage.adaption_base64_decode,
            utf7_decode: mPage.adaption_utf7_decode,
            hex_decode: mPage.adaption_hex_decode,
            url_slash_html_decode_recur_level:parseInt(mPage.url_slash_html_decode_recur_level),
            base64_decode_recur_level:parseInt(mPage.base64_decode_recur_level),
            utf7_decode_recur_level:parseInt(mPage.utf7_decode_recur_level),
            hex_decode_recur_level:parseInt(mPage.hex_decode_recur_level)
        }

        service.submitWafDecoderSettings(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });
    }

    function save_upload_inflate_setting(evt) {
        evt.stopPropagation();
        var submitData = {
            enable: mPage.upload_inflate_enable,
        }

        service.submitWafUploadInflateSettings(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });
    }

    function save_waf_for_api_request_only(evt) {
        evt.stopPropagation();
        var submitData = {
            enable: mPage.waf_for_api_request_only,
        }

        service.submitWafForApiRequestOnly(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });

    }

    function save_waf_detection_size_settings(evt) {
        evt.stopPropagation();

        // 检测输入合法性 body_size_limit 范围0到100，file_size_limit范围0到102400，异常抛出
        if ( Validation.INT(mPage.body_size_limit)|| parseInt(mPage.body_size_limit) > 1024 || parseInt(mPage.body_size_limit) < 0) {
                markErrorOnly(rs('#body_size_limit'));
                rsalert('{% trans "request body size limit" %} : {% trans "please enter integers" %} (0-1024)');
                return
        }

        if (Validation.INT(mPage.resp_body_size_limit)|| parseInt(mPage.resp_body_size_limit) > 1024 || parseInt(mPage.resp_body_size_limit) < 0) {
                markErrorOnly(rs('#resp_body_size_limit'));
                rsalert('{% trans "resp body size limit" %} : {% trans "please enter integers" %} (0-1024)');
                return

        }

        if (Validation.INT(mPage.file_size_limit) || parseInt(mPage.file_size_limit) > 102400 || parseInt(mPage.file_size_limit) < 0) {
                markErrorOnly(rs('#file_size_limit'));
                rsalert('{% trans "file size limit" %} : {% trans "please enter integers" %} (0-102400)');
                return

        }




        var submitData = {
            body_size_limit :parseInt(mPage.body_size_limit),
            file_size_limit :parseInt(mPage.file_size_limit),
            resp_body_size_limit :parseInt(mPage.resp_body_size_limit),

        }

        service.submitWafDetectionSizeSettings(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });

    }

    function remove_black_ip(evt) {
        evt.stopPropagation();

        if (Validation.IP(mPage.ip_black)) {
            rsalert('{% trans "Invalid IP address" %}');
            return;
        }

        var submitData = {
            black_ip: mPage.ip_black,
        }
        service.submitWafSettingsRemoveBlackIP(submitData, function (res) {
            if (res.save_success) {
                if (submitData.black_ip == '0.0.0.0') {
                    alert('{% trans "auto black ip have been cleared" %}');
                } else {
                    alert('{% trans "IP has been deleted from auto black ip" %}');
                }
            } else {
                alert(res.error_msg || '{% trans "Failed to delete" %}');
            }
        });
    }

    var CONTROL_MANAGER = { existNameIndex: -1 };
    rs.bind('ready', function(evt) {
        CONTROL_MANAGER = {
            resourceTable: rs('#__RESOURCE_TABLE__'),
        };
        // 资源文件
        ResourceManager.updateCurCount();
        if (ResourceManager.isAdministrator()) {
            rs('#fileUploadBtn').style.display = 'inline-block';
            rs('#jsUploadBtn').style.display = 'inline-block';
            rs('#htmlUploadBtn').style.display = 'inline-block';
        } else {
            rs('#fileUploadBtn').style.display = 'none';
            rs('#jsUploadBtn').style.display = 'none';
            rs('#htmlUploadBtn').style.display = 'none';
        }
        WafLoggingThreshold.init();
    });

    function check_response_code(val, fieldName, min, max) {
        if (val.length == 0) return rs.formatString('{% trans "{0} cannot be blank." %}', fieldName);

        var msg = rs.formatString('{% trans "{0} must be an integer between {1} and {2}." %}', fieldName, min, max);

        if (isNaN(val)) return msg;

        var nval = Number(val);
        var unsupport_list = [407, 408, 444, 499];
        if (unsupport_list.indexOf(nval) > -1) {
            return rs.formatString('{% trans "{0} {1} are not supported." %}', fieldName, unsupport_list.join('{% trans "," %}'));
        }
        if (nval>=min && nval<= max) {
            if (Validation.INT(val)) return msg;
        } else {
            return msg;
        }
    }

    function save_block_config(evt) {
        evt.stopPropagation();
        error_msg = check_response_code(mPage.block_code,"{% trans 'Status Code' %}", 200, 600)
        if (error_msg) {
            rsalert(error_msg);
            return;
        }
        mPage.block_code = Number(mPage.block_code);
        var submitData = {
            block_code: mPage.block_code,
        }
        service.submitWafSettingsBlockConfig(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });
    }

    function save_block_config_waf_info(evt) {
        // evt.stopPropagation();

        var submitData = {
            enable_block_content: evt.target.checked,
        }
        service.submitWafSettingsBlockConfig(submitData, function (res) {
            if (res.save_success) {
                alert('{% trans "Saved successfully." %}');
            } else {
                alert(res.error_msg || '{% trans "Failed to save." %}');
            }
        });
    }

</script>
{% endblock %}

{% block content %}
<style type="text/css">
    div[rsid="wafDecoderConfig"] .checkboxList{
        display: inline-block;
        width: 75%;
        vertical-align: top;
    }
    div[rsid="wafDecoderConfig"] .checkboxList rs\:Label{
        margin-top: 5px;
        width: 150px;
        text-align: right!important;
    }

    div[rsid="wafDecoderConfig"] .checkboxList rs\:Checkbox {
        margin-left: 5px;
    }
    div[rsid="wafDecoderConfig"] .checkboxList .minInput {
        width: 60px;
        margin-left: 5px;
    }

    div[rsid="wafDetectionSizeSettings"] .checkboxList .minInput {
        width: 100px;
        margin-left: 5px;
    }

    div[rsid="wafDecoderConfig"] li > label{
        margin-top: 8px;
    }

    rs\:Table[name="wafLoggingThresholdList"] div[row] div[table-cell]:nth-child(1),
    rs\:Table[name="wafLoggingThresholdList"] div[row] div[table-cell]:nth-child(2){
        width: 240px;
    }

    rs\:Table[name="wafLoggingThresholdList"] div[row] div[table-cell]:nth-child(4){
        width: 150px;
    }

    #__WAF_LOGGING_THRESHOLD__ rs\:Select {
        width: 180px;
    }

    #__WAF_LOGGING_THRESHOLD__ input {
        width: 88%;
    }

    html[lang="en"] div[rsid="wafDecoderConfig"] .checkboxList rs\:Label { width: 180px;}

</style>
<rs:Form rsid="waf_attack_log_collect_setting" model="#mPage">
    <section>
        <header>
            <span>{% trans 'Attack Event Details Collection Settings' %}</span>
        </header>
        <ul>
            <li>
                <label>{% trans 'Collect mode' %}</label>
                <rs:Select name="collection_mode" rs:disabled="!editable" onChange="handleCollectionMode(event)">
                    <div rs-option value="not_col">{% trans "Not Collection" %}</div>
                    <div rs-option value="time">{% trans "Collect by per second" %}</div>
                    <div rs-option value="all">{% trans "Collect all Attack" %}</div>
                </rs:Select>
            </li>

            <li rs:visible="mPage.collection_mode=='time'">
                <label>{% trans 'Collect per second' %}</label>
                <input id="time_per_second" type="text" v:disabled="!editable" name="time_per_second" value="10" placeholder="{% trans 'please enter positive integers' %} (1-1000000)" autocomplete="off">
                <span>{% trans 'Time(s)' %}</span>
            </li>

            <li rs:visible="!matchDeployMode('plugin') && mPage.collection_mode!='not_col'">
                <label>
                    <div style="display: block;">{% trans 'Collect response log body' %}</div>
                </label>
                <rs:checkbox id="col_response_body" v:disabled="!editable" name="col_response_body"></rs:checkbox>
            </li>
            <li v:if="editable">
                <label></label>
                <button type="button" id="log_collect_submit_btn" onclick="save_attack_log_collect_setting(event)">{% trans "Save" %}</button>
            </li>

        </ul>

    </section>

    <section>
        <div rsid="wafDecoderConfig">
            <header>
                <span>{% trans 'Self-Adaption Data Parser/Decoder' %}</span>
            </header>
            <ul>
                <li>
                    <label>{% trans 'Data Parser' %} </label>
                    <div class="checkboxList">
                        <rs:label>
                            {% trans 'json Parse' %} <rs:Checkbox name="adaption_json_parse" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'plus escape' %} <rs:Checkbox name="adaption_plus_escape" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'xml Parse' %} <rs:Checkbox name="adaption_xml_parse" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'multipart Parse' %} <rs:Checkbox name="adaption_multipart_parse" v:disabled="!editable" ></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'php unserialize' %} <rs:Checkbox name="adaption_php_unserialize" v:disabled="!editable" ></rs:Checkbox>
                        </rs:label>
                    </div>

                </li>
                <li>
                    <label>{% trans 'Base Data Decoder' %} </label>
                    <div class="checkboxList">
                        <rs:label>
                            {% trans 'url decode' %} <rs:Checkbox name="adaption_url_decode" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'html decode' %} <rs:Checkbox name="adaption_html_decode" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'slash decode' %} <rs:Checkbox name="adaption_slash_decode" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'Decode Recur Level' %} <input class="minInput" type="text" id="url_slash_html_decode_recur_level"  v:disabled="!editable" name="url_slash_html_decode_recur_level" value="64" placeholder="1-128" autocomplete="off">
                        </rs:label>
                    </div>
                </li>
                <li>
                    <label>{% trans 'Advanced Data Decoder' %} </label>
                    <div class="checkboxList">
                        <rs:label>
                            {% trans 'base64 decode' %} <rs:Checkbox name="adaption_base64_decode" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'Decode Recur Level' %} <input class="minInput" type="text" id="base64_decode_recur_level" v:disabled="!editable" name="base64_decode_recur_level" value="2" placeholder="1-16" autocomplete="off">
                        </rs:label>
                        <br>
                        <rs:label>
                            {% trans 'utf7 decode' %} <rs:Checkbox name="adaption_utf7_decode" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'Decode Recur Level' %} <input class="minInput" type="text" id="utf7_decode_recur_level" v:disabled="!editable" name="utf7_decode_recur_level" value="1" placeholder="1-16" autocomplete="off">
                        </rs:label>
                        <br>
                        <rs:label>
                            {% trans 'hex decode' %} <rs:Checkbox name="adaption_hex_decode" v:disabled="!editable"></rs:Checkbox>
                        </rs:label>
                        <rs:label>
                            {% trans 'Decode Recur Level' %} <input class="minInput" type="text" id="hex_decode_recur_level" v:disabled="!editable" name="hex_decode_recur_level" value="1" placeholder="1-16" autocomplete="off">
                        </rs:label>
                    </div>
                </li>

                <li v:if="editable">
                    <label></label>
                    <button type="button" id="adaption_decode_submit_btn"
                        onclick="save_adaption_decode_setting(event)">{% trans "Save" %}</button>
                </li>
            </ul>
        </div>
    </section>
   <section>
    <div rsid="wafDetectionSizeSettings">
        <header>
            <span>{% trans 'WAF Detection Settings' %}</span>
        </header>
        <ul>
            <li>
                 <label>{% trans 'Request Body Detection Range (KB)' %}</label>
                <input class="minInput" type="text" id="body_size_limit" name="body_size_limit" value="4" v:disabled="!editable" autocomplete="off">
            </li>
            <li>
                 <label>{% trans 'Response Body Detection Range (KB)' %}</label>
                <input class="minInput" type="text" id="resp_body_size_limit" name="resp_body_size_limit" v:disabled="!editable" value="512" autocomplete="off">
            </li>
            <li>
                 <label>{% trans 'Uploaded File Detection  Range (KB)' %}</label>
                <input class="minInput" type="text" id="file_size_limit" name="file_size_limit" value="10" v:disabled="!editable"  autocomplete="off">
            </li>
            <li v:if="editable">
                <label></label>
                <button type="button" id="waf_detection_size_settings_btn" onclick="save_waf_detection_size_settings(event)">{% trans "Save" %}</button>
            </li>
        </ul>
    </div>
</section>

    <section>
        <div rsid="wafUploadInflateConfig">
            <header>
                <span>{% trans 'Upload File Inflate' %}</span>
            </header>
            <ul>
                <li>
                    <label>{% trans 'Enable' %} </label>
                    <rs:Checkbox id="upload_inflate_enable" v:disabled="!editable" name="upload_inflate_enable" onchange="save_upload_inflate_setting(event)"></rs:Checkbox>
                </li>
            </ul>
        </div>
    </section>

    <section v:if="!matchDeployMode('mirror') && editable">
        <header>
            <span>{% trans 'Auto-Blacklist Clear'%}</span>
            <p>{% trans "When you need to remove some IP addresses from the automatic blacklist, fill in the corresponding IP addresses, and then click Remove." %}</p>
        </header>

        <ul>
            <li>
                <label>IP</label>
                <input type="text" name="ip_black" placeholder="{% trans 'Clear all '%} : 0.0.0.0" class="txtInput"
                    v:disabled="mPage.isErrorData||!editable" />
                <button v:if="editable" type="button" onclick="remove_black_ip(event)"
                    v:disabled="mPage.isErrorData||!editable">{% trans 'Remove'%}</button>
            </li>
        </ul>
    </section>

    <section v:if="!matchDeployMode('mirror')">
        <header>
            <span>{% trans 'Status Code for Blocking'%}</span>
            <p>{% trans "Enter a status code sent to clients when they are denied by WAF. This setting has a low priority than that in strategies and rules." %}</p>
        </header>

        <ul>
            <li>
                <label>{% trans 'Status Code'%}</label>
                <input type="text" name="block_code" placeholder="200~600" class="txtInput"
                       v:disabled="mPage.isErrorData||!editable" />
                <button v:if="editable" type="button" onclick="save_block_config(event)"
                        v:disabled="mPage.isErrorData||!editable">{% trans "Save" %}</button>
            </li>
        </ul>

    </section>

     <section v:if="!matchDeployMode('mirror|plugin')">
        <header>
            <span>{% trans 'Featured Response for Abnormal Request'%}</span>
            <p>{% trans "Insert event ID in the response correspondingly when an abnormal request is blocked by WAF." %}</p>
        </header>

        <ul>
                <li>
                    <label>{% trans 'Enable'%}</label>
                     <rs:checkbox id="__enable_block_content__" v:disabled="!editable" name="enable_block_content" onchange="save_block_config_waf_info(event)"></rs:checkbox>
                </li>
        </ul>
    </section>

    <section id="resourceFileSection" name="resourceFileSection">
        <header>
            <span>
                {% trans 'Upload File Management'%}
                <span style="font-size:13px;">
                    （ {% trans 'Subtotal'%}：<span id="curCount">0</span> / 50）
                </span>
            </span>
            <p>
                {% trans 'The name of the uploaded file can only contain letters, numbers, underscores, and decimal points, and the number of characters cannot exceed 64. It only support the UTF-8 encoding format'%}<br/>
                {% trans 'The file cannot be empty, and the size cannot exceed 1MB. A maximum of 50 files can be uploaded. '%}
            </p>
        </header>
        <div>
            <rs:FileUpload buttonOnly="true" uploadBtnTxt="{% trans 'Upload xsd file'%}" name="schema_file" id="fileUploadBtn" class="inlineBlock"
                           v:if="editable" accept=".xsd"
                           action="/waf_strategy/save_xml_file/"
                           onData="ResourceManager.handleUploadResult(event, this)"
                           onBeforeSubmit="ResourceManager.handleSetFileName(event, 'xsd')">
                {% csrf_token %}
                <div>
                    <input name="file_name" type="hidden" />
                    <input name="type" value="list" type="hidden" />
                    <input name="is_built_in" value="" type="hidden" />
                </div>
            </rs:FileUpload>

            <rs:FileUpload buttonOnly="true" uploadBtnTxt="{% trans 'Upload wsdl file'%}" name="soap_file" id="jsUploadBtn" class="inlineBlock"
                           v:if="editable" accept=".wsdl"
                           action="/waf_strategy/save_xml_file/"
                           onData="ResourceManager.handleUploadResult(event, this)"
                           onBeforeSubmit="ResourceManager.handleSetFileName(event, 'wsdl')">
                {% csrf_token %}
                <div>
                    <input name="file_name" type="hidden" />
                    <input name="type" value="js" type="hidden" />
                </div>
            </rs:FileUpload>

            <rs:FileUpload buttonOnly="true" uploadBtnTxt="{% trans 'Upload HTML file'%}" name="html_file" id="htmlUploadBtn" class="inlineBlock"
                    v:if="editable" accept=".html"
                    action="/waf_strategy/save_xml_file/"
                    onData="ResourceManager.handleUploadResult(event, this)"
                    onBeforeSubmit="ResourceManager.handleSetFileName(event, 'html')">
                    {% csrf_token %}
                    <div>
                        <input name="file_name" type="hidden" />
                        <input name="type" value="js" type="hidden" />
                    </div>
            </rs:FileUpload>

        </div>
        <rs:Table id="__RESOURCE_TABLE__" name="waf_source_list"
                  header= "{% trans 'Name'%}|{% trans 'Type'%}|{% trans 'Upload Date'%}|{% trans 'Size'%}|{% trans 'Operation'%}"
                  cells="file_name|type|time|size|"
                  emptyText="{% trans 'No result' %}"
                  onCreateHeaderCell="ResourceManager.handleOnCreateHeader(event)"
                  onCreateCell="ResourceManager.handleOnCreateCell(event)"></rs:Table>
    </section>

    <!--攻击日志记录阈值-->
    <section id ="__WAF_LOGGING_THRESHOLD__" rs:visible="page.expertMode&&enabled_full_traffic_filter_log" expertMode>

        <header>
            <span>{% trans 'Attack logging threshold' %}</span>
            <p>
                {% trans 'all attack types will log all logs by default' %}<br />
            </p>
        </header>

        <rs:Table name="wafLoggingThresholdList"
                  editable="true"
                  header="{% trans 'Attack type' %}|{% trans 'Logging threshold' %}|{% trans 'Comment' %}|{% trans 'Actions' %}"
                  cells="inject_attack_type|loggin_threshold|comment|"
                  onCreateHeaderCell="WafLoggingThreshold.handleOnCreateTable(event)"
                  emptyText="{% trans 'No result' %}">
            <div>
                <div>
                    <rs:Select name="inject_attack_type"
                               rsid="inject_attack_type"
                               v:data="rowData.inject_attack_type"
                               v:option="inject_attack_types"
                               rs:disabled="!editable"
                               rs-lazy></rs:Select>
                </div>
                <div>
                    <rs:Select name="logging_threshold"
                               rsid="logging_threshold"
                               v:data="rowData.logging_threshold"
                               rs:disabled="!editable"
                               v:option="WafLoggingThreshold.logging_thresholds"
                               rs-lazy></rs:Select>
                </div>

                <div>
                    <input name="comment" maxlength="200" rs:disabled="!editable"  type="text">
                </div>

                <div v:show="editable">
                    <a href="javascript:void(0);"
                       title="{% trans 'Delete' %}"
                       @click="WafLoggingThreshold.remove(table, rowIndex)"
                       v:if="editable"
                       class="ic-trash"></a>
                </div>
            </div>
        </rs:Table>

        <div class="rsTableFooter">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_wafLoggingThresholdList"
                    v:if="editable"
                    onclick="WafLoggingThreshold.create(rs('rs\\:Table[name=wafLoggingThresholdList]'))">{% trans 'New Configuration' %}</button>
        </div>
        <p class="right" >
            <button type="button" v:if="editable" onclick="WafLoggingThreshold.handleOnSubmitSave(event)">{% trans 'Save ' %}</button>
        </p>

        <script language="javascript">

            var WafLoggingThreshold = (function($) {

                var wafLoggingThresholdList = [];
                return {

                    init: function() {
                        document.named('wafLoggingThresholdList').data = log_threshold_setting;
                    },

                    logging_thresholds: [
                        { key:'all', value:'{% trans "Loggin all logs" %}' },
                        //{ key:'1', value:'{% trans "Block Mode" %}' },  //bock mode is unnecessary
                        { key:'high', value:'{% trans "Loggin high-risk logs" %}' },
                        { key:'no', value:'{% trans "No logging" %}' }
                    ],


                    handleOnCreateTable: function(evt) {
                        switch(evt.index){
                            case 3:
                                if(!editable) evt.element.hide();
                                break;
                        }
                    },

                    remove: function(table, rowIndex) {
                        table.removeRow(rowIndex);
                    },

                    create: function(table) {
                        table.addRow({ inject_attack_type: '', logging_threshold: 'all',comment:''});
                    },

                    handleOnSubmitSave:function(evt){
                        evt.stopPropagation();

                        var loggingThresholdTable = document.named('wafLoggingThresholdList');
                        var submitData = loggingThresholdTable.data;
                        for(var i = submitData.length-1; i >=0; i--){
                            if(submitData[i].inject_attack_type==""){
                                 markErrorOnly(loggingThresholdTable.rows[i].querySelector('[rsid="inject_attack_type"]'));
                                 rsalert(rs.formatString('{% trans "[{0}] not selected " %}',"{%  trans 'Attack type' %}"));
                                 return false;
                            }

                            for (var j =0;j<submitData.length;j++){
                                if ( j != i && submitData[i].inject_attack_type == submitData[j].inject_attack_type){
                                     markErrorOnly(loggingThresholdTable.rows[i].querySelector('[rsid="inject_attack_type"]'));
                                     attack_type_name = inject_attack_types.find(function(element){
                                         return element.key == submitData[i].inject_attack_type
                                    });
                                     alert(rs.formatString('{% trans "[{0}] {1} duplicate configuration" %}', "{%  trans 'Attack type' %}",attack_type_name.value));
                                     return
                                }
                            }
                        }

                        service.submitLogThresholdSettings(submitData, function (res) {
                             if (res.save_success) {
                                alert('{% trans "Saved successfully." %}');
                            } else {
                                alert(res.error_msg || '{% trans "Failed to save." %}');
                            }
                        });

                    }
                };

            })();


        </script>


    </section>

    <section v:if="isApiProduct(global_params.productType)">
        <div rsid="wafForApiRequestOnly">
            <header>
                <span>{% trans 'Only available for api request' %}</span>
                <p>{% trans "Waf will skip all requests except API when switch is on. Waf will detect all requests when switch is off." %}</p>
            </header>
            <ul>
                <li>
                    <label>{% trans 'Enable' %} </label>
                    <rs:Checkbox id="waf_for_api_request_only" v:disabled="!editable" name="waf_for_api_request_only"></rs:Checkbox>
                </li>
                <li v:if="editable">
                    <label></label>
                    <button type="button" id="waf_for_api_request_only_submit_btn"
                        onclick="save_waf_for_api_request_only(event)">{% trans "Save" %}</button>
                </li>
            </ul>
        </div>
    </section>
</rs:Form>
{% endblock %}