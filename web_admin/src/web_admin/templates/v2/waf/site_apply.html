{% load i18n %}
{% load nav_tags %}
<style>
    #__APPLY_SITE_DIALOG__ .box {
        width: 90%;
        /*height: 95%;*/
        max-width: 1200px;
        right:0px;
        left:0px;
        /*min-width: 900px;*/
        /*max-height: 1000px;*/
        /*top: 0;*/
    }

    #__APPLY_SITE_DIALOG__ div[name="content"] {
        padding: 0px 15px 15px 15px;
    }

    /*#__APPLY_SITE_DIALOG__ .security_alert_label {*/
    /*    width: 10%;*/
    /*}*/

    /*#__APPLY_SITE_DIALOG__ .security_alert_info {*/
    /*    width: 25%;*/
    /*}*/

    #__APPLY_SITE_DIALOG__ [name=mdBottom] {
        padding: 20px 30px 30px 0px;
        float: right;
    }

    rs\:Table[name="d_apply_site_table"] div[row] div[table-cell]:nth-child(1) {
        width: 80px;
    }

    rs\:Table[name="d_apply_site_table"] div[row] div[table-cell]:nth-child(2) {
        width: 25%;
    }

    rs\:Table[name="d_apply_site_table"] div[row] div[table-cell]:nth-child(3) {
        width: 20%;
        max-width: 200px;
    }

    rs\:Table[name="d_apply_site_table"] div[row] div[table-cell]:nth-child(4) {
        width: 20%;
    }

    rs\:Table[name="d_apply_site_table"] div[header] > div:nth-child(6),
    rs\:Table[name="d_apply_site_table"] div[row] div[table-cell]:nth-child(6) {
        text-align: center;
    }

</style>

<rs:ModalDialog cancelText="{% trans 'Close' %}" id="__APPLY_SITE_DIALOG__" name="applySiteDialog"
                okText="{% trans 'Save' %}"
                onCancel="ApplySite.handleOnCloseDialog(event)"
                onOK="ApplySite.handleOnSaveSiteApply(event)">
    <div name="content">
        <p id="__SITE_HEADER__"> </p>
        <rs:Table id="__APPLY_SITE_TABLE__"
                  cells="checked|name|site_name|strategy_name|strategy_id|is_waf_open"
                  emptyText="{% trans 'No result' %}"
                  header="{% trans 'Enable' %}|{% trans 'Website ' %}|{% trans 'Website Name in Apply Site' %}|{% trans 'Current application strategy name' %}|{% trans 'Current application strategy ID' %}|{% trans 'WAF Enable' %}"
                  name="d_apply_site_table"
                  onCreateCell="ApplySite.handleOnCreateStrategyCell(event)"
                  onCreateHeaderCell="ApplySite.handleOnCreateStrategyHeader(event)">
        </rs:Table>
        <style>
            #__APPLY_SITE_TABLE__[type='GlobalCustomRule'] > div > div:nth-child(4),
            #__APPLY_SITE_TABLE__[type='GlobalCustomRule'] > div > div:nth-child(5){
                display: none !important;
            }

        #__APPLY_SITE_TABLE__[type='GlobalCustomRule'] > div[row] div[table-cell]:nth-child(1) {
            width: 10%;
        }

        #__APPLY_SITE_TABLE__[type='GlobalCustomRule'] > div[row] div[table-cell]:nth-child(3) {
            width: 30%;
        }

        #__APPLY_SITE_TABLE__[type='GlobalCustomRule'] > div[row] div[table-cell]:nth-child(6) {
            width: 10%;
        }

        </style>
    </div>
</rs:ModalDialog>

<script>
    rs.plugin({
        'MultiSelect': '/static/js/rs-customized/ui.MultiSelect.js',
    });
    var applySiteDialog = null
    var checked_host = []

    rs.bind('ready', function (event) {
        applySiteDialog = rs('#__APPLY_SITE_DIALOG__');
        ApplySite.init();
    });

    var ApplySite = (function () {
        var dapplySiteTable = null;
        var applySiteType = null;
        var selectID = null;
        var editStrategy = null;
        var use_default = true;
        var type_trans_dict = {
            "SiteStrategy": '{% trans "Sites Strategy" %}',
            "bruteForce": '{% trans "Brute Force Protection" %}',
            "RegionalAccessControl": '{% trans "Regional Access Control" %}',
            "resLeech": '{% trans "Anti Res_leech" %}',
            'CCAttackProtection':'{% trans "CC Attack Protection" %}',
            'XMLAttackProtection':'{% trans "XML Attack Protection" %}',
            'GlobalCustomRule':'{% trans "Global Custom Rule" %}',
            "httpProtocol": '{% trans "HTTP Protocol Detections" %}',
            "weakPassword": '{% trans "Weak Password Check" %}',
            'VulnerabilityScanProtection':'{% trans "Vulnerability Scan Protection" %}',
            'HoneyPotCheck':'{% trans "Honey Pot Check" %}',
            'CookieTamperProtection':'{% trans "Cookie Tamper Protection" %}',
            'parameterDetection': '{% trans "Parameter Compliance" %}',
            'IllegalDownloadProtection':'{% trans "Illegal Download Protection" %}',
            'GlobalCsrfProtection':'{% trans "CSRF" %}',
            'LLMPromptInjectionProtection':'{% trans "Prompt Injection Protection" %}',
            'LLMSensitiveDetection':'{% trans "PII Detection" %}',
        }
        var type_conf_name_dict = {
            "SiteStrategy": 'waf_strategy',
            "bruteForce": 'waf_brute_force_strategy',
            "RegionalAccessControl": 'waf_regional_access_strategy',
            "resLeech": 'waf_res_leech_strategy',
            'CCAttackProtection':'waf_cc_attack_strategy',
            'XMLAttackProtection':'waf_xml_attack_strategy',
            'GlobalCustomRule':'waf_global_custom_rules',
            'httpProtocol':'waf_http_protocol_strategy',
            "weakPassword": 'waf_weak_password_strategy',
            'VulnerabilityScanProtection':'waf_vulnerability_scan_strategy',
            'HoneyPotCheck':'waf_honey_pot_strategy',
            'CookieTamperProtection':'waf_cookie_tamper_strategy',
            'parameterDetection':'waf_parameter_detection_strategy',
            'IllegalDownloadProtection':'waf_illegal_download_strategy',
            'GlobalCsrfProtection':'waf_csrf_strategy',
            'LLMPromptInjectionProtection': 'llm_prompt_injection_strategy',
            'LLMSensitiveDetection': 'llm_sensitive_detection_strategy',
        }
        var type_ctrl_dict = {
            "SiteStrategy": 'WafConf',
            "bruteForce": 'BruteForce',
            "RegionalAccessControl": 'RegionalAccessControl',
            "resLeech": 'ResLeech',
            'CCAttackProtection':'CCAttackProtection',
            'XMLAttackProtection':'XMLAttackProtection',
            'GlobalCustomRule':'GlobalCustomRule',
            'httpProtocol':'HttpProtocol',
            "weakPassword": 'WeakPasswordCheck',
            'VulnerabilityScanProtection':'VulnerabilityScanProtection',
            'HoneyPotCheck':'HoneyPotCheck',
            'CookieTamperProtection':'CookieTamperProtection',
            'parameterDetection':'ParameterDetection',
            'IllegalDownloadProtection':'IllegalDownloadProtection',
            'GlobalCsrfProtection':'GlobalCsrfProtection',
            'LLMPromptInjectionProtection': 'LLMPromptInjectionProtection',
            'LLMSensitiveDetection': 'LLMSensitiveDetection',
        }

        function initHost() {
            dapplySiteTable = applySiteDialog.named('d_apply_site_table');
        }

        var is_llm = '{{info_module}}' === 'llm';
        var strategyCallback;
        function applySite(editData, siteType, updateCallback) {
            strategyCallback = updateCallback;
            rs('#__APPLY_SITE_TABLE__').attr('type', siteType);

            var c = [];
            for (var i = 0; i < site_info.length; i++) {
                if (editData.hosts.indexOf(site_info[i].name) > -1) {
                    c.push(site_info[i].value);
                }
            }
            applySiteType = siteType;
            selectID = editData.id;
            editStrategy = JSON.parse(JSON.stringify(editData));
            checked_host = [];
            applySiteDialog.named('d_apply_site_table');
            use_default = true;
            dapplySiteTable.data = site_info;
            use_default = false;
            var title = rs.formatString('{% trans "Application Site {0}" %}', type_trans_dict[applySiteType]);
            title = title + "(ID:" + editData.id + ")";
            setSiteCheckedAllStatus();
            applySiteDialog.named("okBtn").css('display', 'inline-block');
            rs('#__SITE_HEADER__').scrollIntoView();
            applySiteDialog.open(null, null, null, title);
        }


        function closeDialog(evt) {
            applySiteDialog.errorMessage = '';
            applySiteDialog.hideErrorMessage();
            applySiteDialog.closeDialog();
        }

        function saveApply(evt) {
            var data = {
                id: selectID,
                apply_site_type: type_ctrl_dict[applySiteType],
                server_names: checked_host
            }
            
            switch (applySiteType) {

                case 'SiteStrategy':
                    ask('{% trans "After saving, the current strategy will be applied to the selected site. Are you sure to save?(Unchecked sites will automatically apply the default policy)" %}', saveSiteApply);
                    break;

                case 'GlobalCustomRule':
                    ask('{% trans "After saving, the current rule will be applied to the selected site. Are you sure to save?" %}', saveSiteApply);
                    break;

                case 'LLMPromptInjectionProtection':
                case 'LLMSensitiveDetection':default:
                    ask('{% trans "After saving, the current strategy will be applied to the selected site. Are you sure to save?(This function will be closed if the site is unchecked)" %}', saveSiteApply);
                    break;
            }

            function saveSiteApply(callback) {
                //service
                window.service.saveSiteApply(data, function (result) {
                    if (result.is_success) {
                        rsalert('{% trans "Save Successfully" %}');
                        closeDialog();
                        (strategyCallback || getAllStrategys)();
                    } else {
                        rsalert(escapeHtml(result.error_msg));
                    }
                }, function (response) {
                    rsalert('{% trans "Abnormal operation! Please try again!" %}');
                });
            }

        }

        function saveSingleApplySiteStatus(value, checked) {
            var fix_checked = checked ? 1 : 0;

            var index_val = checked_host.indexOf(value);
            if (fix_checked) {
                if (index_val == -1) {
                    checked_host.push(value);
                }
            } else {
                if (index_val > -1) {
                    checked_host.splice(index_val, 1);
                }
            }
        }

        function saveApplySiteChange(evt) {
            var val = evt.target.value;
            var checked = evt.target.checked;
            if (val == "-1") {
                for (var i = 0; i < site_info.length; i++) {
                    var item = site_info[i];
                    saveSingleApplySiteStatus(item.value, checked);
                    dapplySiteTable.rows[i].child('rs\\:checkbox').checked = checked;
                }
            } else {
                saveSingleApplySiteStatus(val, checked);
            }
            setSiteCheckedAllStatus();
        }

        function setSiteCheckedAllStatus() {
            dapplySiteTable.child('[rsid="enable_all_rule"]').checked = checked_host.length == site_info.length;
        }

        function handleOnCreateStrategyHeader(evt) {
            switch (evt.index) {
                case 0: //enabled
                    evt.preventDefault();
                    var ck = createCheckbox({
                        name: 'enable_all_rule',
                        value: '-1'
                    }, {rsid: 'enable_all_rule'}).bind('change', saveApplySiteChange).appendTo(evt.element);
                    rs.Element('span').html("{% trans 'Checked All' %}").appendTo(evt.element);
                    break;
                case 5://状态
                    evt.preventDefault();
                    if (is_llm){
                        evt.element.html("{% trans 'Open Status' %}");
                    }else{
                        evt.element.html("{% trans 'WAF Enable' %}");
                    }
            }
        }

        function handleOnCreateStrategyCell(evt) {
            var site_strategy_id = evt.rowData[type_conf_name_dict[applySiteType]];
            if (site_strategy_id == '-1') {
                site_strategy_id = '-';//关闭
            }
            switch (evt.index) {
                case 0: //enabled
                    evt.preventDefault();
                    var ck = createCheckbox({
                        name: 'enable_rule',
                        value: evt.rowData.value
                    }).bind('change', saveApplySiteChange);
                    var checked = false;
                    if (use_default) {
                        if(site_strategy_id instanceof Array){
                            checked = site_strategy_id.indexOf(selectID.toString())>-1;
                        }
                         else {
                            checked = site_strategy_id == selectID;
                        }
                        if (checked) checked_host.push(evt.rowData.value);
                    } else {
                        checked = evt.rowData.checked;
                    }
                    ck.appendTo(evt.element).checked = checked;
                    break;
                case 3://当前应用策略名称
                    evt.preventDefault();
                    //导入21.03之前配置，站点策略显示原有自定义策略。
                    if (applySiteType=='GlobalCustomRule')
                    {
                        evt.element.hide();
                    }
                    else if (site_strategy_id==null||site_strategy_id==""){
                        if (applySiteType=='SiteStrategy'){
                            evt.element.html("{% trans 'Old Strategy' %}");
                        }else{
                            evt.element.html("{% trans 'None Strategy' %}");
                        }
                    }
                    else if (site_strategy_id == '-') {
                        evt.element.html("{% trans 'None Strategy' %}");
                    }else {
                        var strategy_name = apply_info[site_strategy_id].name;
                        evt.element.html(escapeHtml(strategy_name));
                    }
                    break;
                case 4:
                    evt.preventDefault();
                    if (applySiteType=='GlobalCustomRule')
                    {
                        evt.element.hide();
                    }
                    else if (site_strategy_id==null||site_strategy_id==""){
                        evt.element.html(escapeHtml('-'));
                    }else{
                        evt.element.html(escapeHtml(site_strategy_id));
                    }
                    break;
                case 5:
                    evt.preventDefault();
                    var to_check = false;
                    if (is_llm){
                        to_check = evt.rowData.is_llm_open;
                    }else{
                        to_check = evt.rowData.is_waf_open;
                    }
                    if (to_check) {
                        evt.element.html("{% trans 'Open' %}");
                    } else {
                        evt.element.html("{% trans 'Strategy Disabled' %}");
                    }
                    break;
            }
        }

        return {
            init: initHost,
            applySite: applySite,
            handleOnSaveSiteApply: saveApply,
            handleOnCloseDialog: closeDialog,
            handleOnCreateStrategyHeader: handleOnCreateStrategyHeader,
            handleOnCreateStrategyCell: handleOnCreateStrategyCell
        };

    })();
</script>
