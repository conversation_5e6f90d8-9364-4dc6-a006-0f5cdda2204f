{% extends "v2/base.html" %}
{% load nav_tags %}
{% load i18n %}

{% block title %}{% trans 'Business Data Collection' %}{% endblock %}
{% block navigation %}{% trans 'Business Data Collection' %}{% endblock %}

{% block content %}
    <style type="text/css">
        body { min-width:1150px; }
        #__COLLECTION_TABLE__ div { font-size: 13px; }
        #__COLLECTION_TABLE__ div[header] > div:nth-last-child(1) { width:130px; }
        #__COLLECTION_TABLE__ div[header] > div:nth-child(2) { min-width:60px;}
        #__COLLECTION_TABLE__ div[header] > div:nth-child(3) { min-width:95px;}
        #__COLLECTION_TABLE__ div[header] > div:nth-child(4) { min-width:95px;}
        #__COLLECTION_TABLE__ div[header] > div:nth-child(5) { min-width:130px;}
        #__COLLECTION_TABLE__ div[header] > div:nth-child(6) { min-width:95px;}
        #__COLLECTION_TABLE__ div[header] > div:nth-child(7) { min-width:75px;}
        #__COLLECTION_TABLE__ div[header] > div:nth-child(8) { min-width:95px;}
        #__COLLECTION_TABLE__ > div:nth-child(1) > div:nth-child(1) { width: 68px; text-align: left; }
        #__COLLECTION_TABLE__ rs\:Checkbox { margin-right: 0px; }

        #__COLLECTION_TABLE__[lock] .ic-pen,
        #__COLLECTION_TABLE__[lock] .ic-trash {
            display: none;
        }
        .ic-pen, .ic-trash, .fa-caret-up, .fa-caret-down{ cursor: pointer; color:#00a6c8; }
        .ic-pen[disabled], .ic-trash[disabled]{ cursor: default; color:#83878d; }
        .ic-pen[disabled]:hover, .ic-trash[disabled]:hover{ color:#83878d!important; text-shadow: none!important;}

        rs\:Table div[header] .RsSortButton[disabled]{
            cursor: default;
        }
        rs\:Table div[header] .RsSortButton[disabled] i{
            opacity: 0.3;
            cursor: default;
        }
        rs\:Table div[header] .RsSortButton[disabled][rule=asc] > i:first-child,
        rs\:Table div[header] .RsSortButton[disabled][rule=desc] > i:last-child{
            opacity: 1;
        }
        #__EDIT_DIALOG__ [name=wrapper] { padding:10px; box-sizing: border-box; }
        #__EDIT_DIALOG__ table { margin:auto; }
        #__EDIT_DIALOG__ table tr td:first-child { text-align: right; padding:0px 10px; height:36px; }

        #__EDIT_DIALOG__ table tr td rs\:Select,
        #__EDIT_DIALOG__ table tr td input {
            width: 300px;
            box-sizing: border-box;
        }

        #__EDIT_DIALOG__ table tr td rs\:Select input { border:0px; width: 100%; }
        #__EDIT_DIALOG__ .error-bar { margin: 10px 20px; }
        #__EDIT_DIALOG__ a[name='bt_add_custom_field'] i { margin-right:5px; font-size: 22px; vertical-align: sub; }
        #__EDIT_DIALOG__ a[name='bt_add_custom_field'] { line-height: 30px; }
        #__EDIT_DIALOG__ tr { display: table-row; }
        #__EDIT_DIALOG__ a[name='removeCustomField'] { display: inline-block !important; position: relative; left: -10px; }

        .businessTableSection[disable] button {
            display: none !important;
        }

        .businessTableSection[disable] div[header=header] rs\:Checkbox { display:none; }


        #__EDIT_DIALOG__ .ic-help {
            font-size: 13px;
            color: #00a6c8;
            vertical-align: top;
            margin-left: 2px;
        }

        #__EDIT_DIALOG__ table *[rs-required]:before{
            content:'*';
            color:red;
            margin-right:2px;
        }

        #__EDIT_DIALOG__ table tr td:nth-child(1) {
            white-space: nowrap;
        }

         #__EDIT_DIALOG__ .box {
             transition: right .2s ease-out; width:550px; left:0px; right:0px;
             width: 600px !important;
         }
        #__EDIT_DIALOG__[noticeOn] .box { left:0px; right:400px; }
        #__EDIT_DIALOG__ input[name=password] { width:170px; }
        #__EDIT_DIALOG__ .weakLink { text-decoration: underline; cursor:pointer; color:#3F51B5; }
        #__EDIT_DIALOG__ .weakLink:after { content:'（{% trans 'View' %}）' }
        #__EDIT_DIALOG__[noticeOn] .weakLink:after { content:'（{% trans 'Collapse' %}）' }

        #__EDIT_DIALOG__ .weakEditorPane {
            position: absolute;
            top: 11%;
            width: 400px;
            height: 570px;
            background-color: white;
            padding: 10px;
            color: #313741;
            margin: auto;
            left: 0px;
            right: 0px;
            transition: left .2s ease-out;
            overflow-y: auto;
            border: 1px solid #666;
        }

        #__EDIT_DIALOG__[noticeOn] .weakEditorPane {
            left: 299px;
            position: relative;
        }

        #__EDIT_DIALOG__ .weakEditorPane div[name=weakContent] {
            height:530px;
        }

        #__EDIT_DIALOG__ .weakEditorPane div[name=weakContent] span[name=weak_pwd_count] {
            font-size:14px;
        }

        #__EDIT_DIALOG__ .weakEditorPane textarea {
            display: block;
            width: 100%;
            height: 435px;
            line-height:1.5;
        }
    </style>

    <rs:style enable="#embedMode">
        body {
            min-width: inherit;
        }

        #__EDIT_DIALOG__ .ic-help {
            color: #549BEF;
        }
    </rs:style>

    <section class="businessTableSection">
        <header>
            <span>{% trans 'Key Business Data Collection' %}</span>
        </header>
        <p>
            <div v:show="!adjustPriority.isAdjustPriority">
                <button id="__BT_ADD_NEW__"
                           v:if="editable"
                       type="button" onclick="createExtraData()">{% trans 'Add' %}</button>
                <button id="__BT_SET_SORT__"
                           v:if="priorityEditable"
                           v:show="adjustPriority.needAdjustPriority"
                       type="button" onclick="setAdjustPriority()">{% trans 'Adjust Priority' %}</button>
            </div>
            <div v:show="adjustPriority.isAdjustPriority">
                <button id="__BT_CANCEL_PRIORITY__"
                           v:if="priorityEditable"
                       type="button" onclick="cancelAdjustPriority()">{% trans 'Cancel' %}</button>
                <button id="__BT_SAVE_PRIORITY__"
                           v:if="priorityEditable"
                       type="button" onclick="saveAdjustPriority()">{% trans 'Save Adjust Priority' %}</button>
            </div>
        </p>
        <rs:Table
            name="businessDataList"
            id="__COLLECTION_TABLE__"
            header="|ID|{% trans 'Business Name' %}|{% trans 'Business Type' %}|{% trans 'Website ' %}|{% trans 'Path' %}|{% trans 'Method' %}|{% trans 'Specify Parameter' %}|{% trans 'Operation' %}"
            cells="enable|id|businessName|businessType|serverKey|path|method|specifyParameter|null"
            onCreateHeaderCell="TableHandler.handleCreateTableHeaderCell(event)"
            onCreateCell="TableHandler.handleCreateTableCell(event)"
            onData="TableHandler.updateTableEnableSwitch()"
            onDataChange="TableHandler.updateTableEnableSwitch()"
            onEmptyData="TableHandler.getEmptyData(event)"></rs:Table>
    </section>

    <rs:ModalDialog id="__EDIT_DIALOG__"
                    name="editExtraData"
                    onOK="handleOnClickExtraDataEditorOK(event)"
                    onCancel="handleCancelEditData(event)">
        <rs:Form name="form" model="#mDialog"
                 modelName="threatSettings"
                 onValueError="handleOnEditExtraError(event)"
                 onSubmit="handleOnEditExtraData(event)">
            <div name="wrapper">
                <table>
                    <tbody>
                        <tr>
                            <td rs-required>{% trans 'Name' %}</td>
                            <td><input name="businessName" type="text" maxlength="16"></td>
                        </tr>
                        <tr>
                            <td rs-required>{% trans 'Business Type' %}</td>
                            <td>
                                <rs:Select id="businessType" name="businessType" onchange="handleChangeBusinessType(event)"></rs:Select>
                            </td>
                        </tr>
                        <tr rs:visible="mDialog.businessType==1 || mDialog.businessType==2">
                            <td rs-required class="txt" >{% trans 'Username parameter' %}</td>
                            <td><input type="text" name="account" /></td>
                        </tr>
                        <tr rs:visible="mDialog.businessType==17">
                            <td rs-required class="txt" >{% trans 'Credit card number' %}</td>
                            <td><input type="text" name="account" /></td>
                        </tr>
                        <tr rs:visible="mDialog.businessType==17">
                            <td class="txt" >{% trans 'Credit card valid date' %}</td>
                            <td><input type="text" name="validDate" /></td>
                        </tr>
                        <tr rs:visible="mDialog.businessType==17">
                            <td class="txt" >{% trans 'Credit card CV2' %}</td>
                            <td><input type="text" name="cv2Code" /></td>
                        </tr>
                        <tr rs:visible="mDialog.businessType==1 || mDialog.businessType==2">
                            <td class="txt">{% trans 'Password parameter' %}<i class="ic-help" onmouseover="handleShowWeakPwdTip(event)" onmouseout="handleHideWeakPwdTip(event)"></i></td>
                            <td>
                                <input type="text" name="password" />
                                <rs:ToggleButton class="weakLink">{% trans 'Weak password dictionary' %}</rs:ToggleButton>
                            </td>
                        </tr>
                        <tr>
                            <td rs-required>{% trans 'Website ' %}</td>
                            <td><rs:Select id="serverKey" name="serverKey"></rs:Select></td>
                        </tr>
                        <tr>
                            <td rs-required class="txt">{% trans 'Path' %}</td>
                            <td><input type="text" name="path" style="width:200px;"/>
                                <rs:label><rs:Checkbox id="regexPath" name="regexPath"></rs:Checkbox>{% trans 'Regex match' %}</rs:label></td>
                        </tr>
                        <tr>
                            <td rs-required>{% trans 'Method' %}</td>
                            <td>
                                <rs:Select name="method">
                                    <div rs-option value="0">{% trans 'Any' %}</div>
                                    <div rs-option value="GET">GET</div>
                                    <div rs-option value="POST">POST</div>
                                </rs:Select>
                            </td>
                        </tr>
                        <tr>
                            <td class="txt">{% trans 'Specify Parameter' %}</td>
                            <td><input type="text" name="specifyParameter"/></td>
                        </tr>
                        <tr>
                            <td rs-required class="txt">{% trans 'Business success keywords' %}</td>
                            <td><input type="text" name="success" class="extraSuccess" /></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td>
                                <rs:Label><rs:Checkbox name="withPostData"></rs:Checkbox>{% trans 'Collect all Post' %}</rs:Label>
                                <rs:Label><rs:Checkbox name="withCookie"></rs:Checkbox>{% trans 'Collect all Cookie' %}</rs:Label>
                                <rs:Label><rs:Checkbox name="withHeader"></rs:Checkbox>{% trans 'Collect Header without Cookie/User-Agent/Host/Referer' %}</rs:Label>
                                <rs:Label rs:visible="!matchDeployMode('plugin')"><rs:Checkbox name="withResponseBody"></rs:Checkbox>{% trans 'Collect response body' %}</rs:Label>
                           </td>
                        </tr>
                        <tr>
                            <td rs-required>{% trans 'Collection field length' %}</td>
                            <td><input type="text" name="fieldsLen" placeholder="1 ~ 65535" maxlength="5"/></td>
                        </tr>
                        <tr>
                            <td rs-required>{% trans 'Data collection length' %}<i class="ic-help" onmouseover="handleShowDataCollLenTip(event)" onmouseout="handleHideDataCollLenTip(event)"></i></td>
                            <td><input type="text" name="extraLen" placeholder="1 ~ 65535" maxlength="5"/></td>
                        </tr>
                        <tr rs:visible="__DIALOG__.customList.indexOf('1') > -1">
                            <td class="txt">{% trans 'Custom Field 1' %}</td>
                            <td><input type="text" name="custom1" />
                                <rs:Label style="display: inline-block;" rs:visible="mDialog.businessType!=1 && mDialog.businessType!=2 && mDialog.businessType!=17"><rs:Radio name="customAsUsername" value="1"></rs:Radio>{% trans 'As username' %}</rs:Label>
                                <a name="removeCustomField" index="1"><i class="ic-cross"></i></a></td>
                        </tr>
                        <tr rs:visible="__DIALOG__.customList.indexOf('2') > -1">
                            <td class="txt">{% trans 'Custom Field 2' %}</td>
                            <td><input type="text" name="custom2" />
                                <rs:Label style="display: inline-block;" rs:visible="mDialog.businessType!=1 && mDialog.businessType!=2 && mDialog.businessType!=17"><rs:Radio name="customAsUsername" value="2"></rs:Radio>{% trans 'As username' %}</rs:Label>
                                <a name="removeCustomField" index="2"><i class="ic-cross"></i></a></td>
                        </tr>
                        <tr rs:visible="__DIALOG__.customList.indexOf('3') > -1">
                            <td class="txt">{% trans 'Custom Field 3' %}</td>
                            <td><input type="text" name="custom3" />
                                <rs:Label style="display: inline-block;" rs:visible="mDialog.businessType!=1 && mDialog.businessType!=2 && mDialog.businessType!=17"><rs:Radio name="customAsUsername" value="3"></rs:Radio>{% trans 'As username' %}</rs:Label>
                                <a name="removeCustomField" index="3"><i class="ic-cross"></i></a></td>
                        </tr>
                        <tr rs:visible="__DIALOG__.customList.indexOf('4') > -1">
                            <td class="txt">{% trans 'Custom Field 4' %}</td>
                            <td><input type="text" name="custom4" />
                                <rs:Label style="display: inline-block;" rs:visible="mDialog.businessType!=1 && mDialog.businessType!=2 && mDialog.businessType!=17"><rs:Radio name="customAsUsername" value="4"></rs:Radio>{% trans 'As username' %}</rs:Label>
                                <a name="removeCustomField" index="4"><i class="ic-cross"></i></a></td>
                        </tr>
                        <tr rs:visible="__DIALOG__.customList.indexOf('5') > -1">
                            <td class="txt">{% trans 'Custom Field 5' %}</td>
                            <td><input type="text" name="custom5" />
                                <rs:Label style="display: inline-block;" rs:visible="mDialog.businessType!=1 && mDialog.businessType!=2 && mDialog.businessType!=17"><rs:Radio name="customAsUsername" value="5"></rs:Radio>{% trans 'As username' %}</rs:Label>
                                <a name="removeCustomField" index="5"><i class="ic-cross"></i></a></td>
                        </tr>
                        <tr rs:visible="__DIALOG__.customList.length < 5">
                            <td></td>
                            <td>
                                <a name="bt_add_custom_field" href="#"><i class="ic-plus-r-o"></i>{% trans 'Add custom field' %}</a>
                            </td>
                        </tr>

                    </tbody>
                </table>
            </div>
        </rs:Form>
    </rs:ModalDialog>

    <rs:Template id="__WEAK_TEMP__">
        <div name="weakContent">
            <h2>{% trans 'Weak password dictionary' %}<span name="weak_pwd_count">（0）</span></h2>
            <p>
                {% trans 'The following weak passwords are for reference only. Please modify them based on the actual business specific password algorithm, otherwise you will not be able to hit.' %}
            </p>
            <textarea name="weak_pwd_list" v:disabled="!auth('Data_Collection_Weak_Password', 'w')" onchange="handleChangeWeakPWDList(event)"></textarea>
        </div>
    </rs:Template>

{% endblock %}

{% block script %}
    <script language="JavaScript" type="text/javascript">

        var editable = auth('Data_Collection', 'w');
        var readable = auth('Data_Collection', 'r');
        var priorityEditable = auth('Data_Collection_Priority', 'w');

        rs.plugin({
            ToggleButton: '~/ui.ToggleButton.js'
        });

        // Server Data
        {% autoescape off %}
        var extraDataList = {{ extraDatas|to_json }};
        var upstreamList = {{ upstreamConfList|to_json }};
        var maxId = {{ maxId }};
        {% endautoescape %}
        var isChrome = rs('html').attr('type') == 'Chrome';
        var adjustPriority = {
            isAdjustPriority: false,
            needAdjustPriority: false,
            tableCatch: [],
            saveData: [],
            sortCatch: {
                key:'',
                sort: '',
            }
        }

        // Const
        var STATIC_CONST = {
            MAX_COUNT: 5,
            MAX_SESSION_LENGTH: 50,
            MAX_FIELD_LENGTH: 1024,
            MAX_WEAK_PWD_CHAR: 10000
        };

        var FIELD_RANGE = {
            MIN: 1,
            MAX: 65535
        };



        var businessTypeList = [
            {type:'{% trans "Login" %}', id:1},
            {type:'{% trans "Register" %}', id:2},
            {type:'{% trans "Data query" %}', id:3},
            {type:'{% trans "Promotion/Red packet scramble" %}', id:4},
            {type:'{% trans "Place an order" %}', id:5},
            {type:'{% trans "Payment" %}', id:6},
            {type:'{% trans "Vote" %}', id:7},
            {type:'{% trans "SMS interface" %}', id:8},
            {type:'{% trans "Sniping in advance" %}', id:9},
            {type:'{% trans "Sniping" %}', id:10},
            {type:'{% trans "API interface" %}', id:11},
            {type:'{% trans "Get verification code" %}', id:12},
            {type:'{% trans "Submit verification code" %}', id:13},
            {type:'{% trans "Click of ad" %}', id:14},
            {type:'{% trans "Display of ad" %}', id:15},
            {type:'{% trans "Coupon verification" %}', id:16},
            {type:'{% trans "Pay by credit card" %}', id:17},
            {type:'{% trans "Submit comments" %}', id:18},
            {type:'{% trans "Custom 1" %}', id:51},
            {type:'{% trans "Custom 2" %}', id:52},
            {type:'{% trans "Custom 3" %}', id:53}
        ];

        var BasicValidate = {
            checkSampleLength: function(value){
                if(!BasicValidate.isInt(value) || value<FIELD_RANGE.MIN || value>FIELD_RANGE.MAX || Math.ceil(value)!=value) return '{% trans "Collection length must be a positive integer bigger than 0 and less than 65536." %}';
            },

            validateFieldValue: function(value, fieldName){
                if(BasicValidate.isEmptyValue(value)) return '[ '+fieldName+' ]{% trans "cannot be blank" %}';
                if(BasicValidate.getStringLength(value) > STATIC_CONST.MAX_FIELD_LENGTH) return '[ ' + fieldName + ' ]{% trans "length has exceeded limit." %}';
                if(BasicValidate.containsIllegalText(value)) return '{% trans "Invalid character(s) found!" %}';
            },

            validateCustomFieldValue: function(value, index, fieldName){
                if(BasicValidate.getStringLength(value) > STATIC_CONST.MAX_FIELD_LENGTH) return '[ ' + fieldName + ' ]{% trans "length has exceeded limit." %}';
                if(BasicValidate.containsIllegalText(value)) return '{% trans "Invalid character(s) found!" %}';
                if(BasicValidate.hasSameCustomField(value, index)) return '{% trans "There are duplicate configuration items in the custom field." %}';
            },

            validatePathValue: function(value, fieldName) {
                if(BasicValidate.isEmptyValue(value)) return '[ '+fieldName+' ]{% trans "cannot be blank" %}';
                if(BasicValidate.getStringLength(value) > STATIC_CONST.MAX_FIELD_LENGTH) return '[ ' + fieldName + ' ]{% trans "length has exceeded limit." %}';

                if(rs("#regexPath").checked) {
                    if (!BasicValidate.regexCheck(value)) return '{% trans "Invalid character(s) found!" %}';
                } else if (BasicValidate.containsIllegalText(value)) {
                    return '{% trans "Invalid character(s) found!" %}';
                }
            },

            hasSameCustomField: function(value, index){
                if(BasicValidate.isEmptyValue(value)) return false;
                for(var i=1; i<=5; i++){
                    if(i==index) continue;
                    if(mDialog['custom'+i] == value) return true;
                }
            },

            isEmptyValue: function(value){
                return value=='' || /^[\s]+$/.test(value);
            },

            isInt: function(value){
                return /^[-]?[\d]+$/.test(value);
            },

            containsIllegalText: function(txt) {
                return /[#\'\<\>\{\}\;]/.test(txt);
            },

            getStringLength: function(str) {
                return str.replace(/[^\x00-\xff]/g,"aa").length;
            },

            regexCheck: function(value) {
                if (!value || (typeof value=='string'&&!value.trim())) {
                    return false;
                }
                if (!/^[\x20-\x7e]*$/.test(value)) {
                    return false;
                }
                return true;
            }
        };

        // model
        var mDialog = {
            enable: true,
            id: '',
            businessName: '',
            businessType: '',
            serverKey: '',
            path: '',
            regexPath: false,
            method: '0',
            success: '',
            account: '',
            custom1: '',
            custom2: '',
            custom3: '',
            custom4: '',
            custom5: '',
            withCookie: false,
            withPostData: false,
            fieldsLen: 128,
            extraLen: 4096,
            password: '',
            validDate: '',
            cv2Code: '',
            withHeader: false,
            withResponseBody: false,
            customAsUsername: 0,
            specifyParameter: '',

            _validation:{
                businessName: [function(value){
                    if(value.length > 16) {
                        return '{% trans "Business name cannot exceed 16 characters in Chinese or 16 characters in English." %}';
                    }
                    return BasicValidate.validateFieldValue(value, '{% trans "Business Name" %}');
                }],
                account: [function(value){
                    if(mDialog.businessType==1 || mDialog.businessType==2){
                        return BasicValidate.validateFieldValue(value, '{% trans "Username parameter" %}');
                    }
                    if(mDialog.businessType==17){
                        return BasicValidate.validateFieldValue(value, '{% trans "Credit card number" %}');
                    }
                }],
                validDate: [function(value){
                    if(mDialog.businessType==17){
                        if(BasicValidate.getStringLength(value) > STATIC_CONST.MAX_FIELD_LENGTH) return '[{% trans "Credit card valid date" %}]{% trans "length has exceeded limit." %}';
                        if(BasicValidate.containsIllegalText(value)) return '{% trans "Invalid character(s) found!" %}';
                    }
                }],
                cv2Code: [function(value){
                    if(mDialog.businessType==17){
                        if(BasicValidate.getStringLength(value) > STATIC_CONST.MAX_FIELD_LENGTH) return '[{% trans "Credit card CV2" %}]{% trans "length has exceeded limit." %}';
                        if(BasicValidate.containsIllegalText(value)) return '{% trans "Invalid character(s) found!" %}';
                    }
                }],
                password: [function(value) {
                    if (BasicValidate.containsIllegalText(value)) return '{% trans "Invalid character(s) found!" %}';
                    if (BasicValidate.getStringLength(value) > STATIC_CONST.MAX_FIELD_LENGTH) return '{% trans "Password parameter cannot exceed 512 characters in Chinese or 1024 characters in English." %}';
                }],
                path: [function(value){
                    return BasicValidate.validatePathValue(value, '{% trans "Path" %}');
                }],
                success: [function(value){
                    return BasicValidate.validateFieldValue(value, '{% trans "Key word for successful login" %}');
                }],
                specifyParameter: [function(value){
                    if(value.length > 100) {
                        return '{% trans "Specify parameter cannot exceed 100 characters." %}';
                    }
                }],
                fieldsLen: [BasicValidate.checkSampleLength],
                extraLen: [BasicValidate.checkSampleLength],
                custom1: [function(value){
                    return BasicValidate.validateCustomFieldValue(value, 1, '{% trans "Custom Field 1" %}');
                }],
                custom2: [function(value){
                    return BasicValidate.validateCustomFieldValue(value, 2, '{% trans "Custom Field 2" %}');
                }],
                custom3: [function(value){
                    return BasicValidate.validateCustomFieldValue(value, 3, '{% trans "Custom Field 3" %}');
                }],
                custom4: [function(value){
                    return BasicValidate.validateCustomFieldValue(value, 4, '{% trans "Custom Field 4" %}');
                }],
                custom5: [function(value){
                    return BasicValidate.validateCustomFieldValue(value, 5, '{% trans "Custom Field 5" %}');
                }]
            }
        };

        var BTAGlobalData = {
            weak_pwd_list: ''
        };

        // Init Page
        var __TABLE__ = rs('#__COLLECTION_TABLE__');
        var __DIALOG__ = rs('#__EDIT_DIALOG__');
        function initPage(){
            initDialog(upstreamList);
            if(!upstreamList.length){
                rs('.businessTableSection').attr('disable', true);
            }
            if(!editable) __TABLE__.attr('lock', true);
            __TABLE__.data = extraDataList;
            __TABLE__.bind('sortList', handleSortBussinessDataTable);
            SetTableSort('id', 'asc');
            resetNeedAdjustPriority();
        }

        function handleSortBussinessDataTable(evt) {
            var sortKey = evt.key;
            var isAsc = evt.sort=='asc';

            // sort UI
            var tempList = __TABLE__.data;
            sortBusinessDataList(tempList, sortKey, isAsc);
            __TABLE__.data = tempList;
        }

        function sortBusinessDataList(list, sortKey, isAsc) {
            // Chrome69 - v8
            if (isChrome) {
                list.forEach(function(item, index) {
                    if (item) item.oldIndex = index;
                });
            }

            list.sort(function(site1, site2) {
                var comp1 = site1[sortKey];
                var comp2 = site2[sortKey];

                if (sortKey == 'specifyParameter') {
                    comp1 = site1.isEmptyName ? '':comp1;
                    comp2 = site2.isEmptyName ? '':comp2;
                }
                if (comp1 > comp2) {
                    return isAsc ? 1 : -1;
                } else if (comp1 < comp2) {
                    return isAsc ? -1 : 1;
                } else {
                    if (isChrome) {
                        return site1.oldIndex - site2.oldIndex;     // Chrome69 - v8
                    } else {
                        return 0;
                    }
                }
            });
        }


        //########################################################
        //  Table
        //########################################################
        var TableHandler = {
            handleCreateTableHeaderCell: function(evt) {
                if(evt.index==0){
                    evt.preventDefault();
                    __TABLE__._enableSwitch = rs.XElement('Checkbox').appendTo(evt.element);
                    if(!editable) {
                        //__TABLE__._enableSwitch.disabled = true;
                        __TABLE__._enableSwitch.css({display: 'none'});
                    }else{
                        __TABLE__._enableSwitch.bind('change', function(event){
                            var checked = this.checked;
                            service.toggleBTADataStatus({enable:checked}, function(response){
                                if(response.saveSuccess){
                                    rs.each(__TABLE__.named('enable', false), function(index, cb){
                                        cb.checked = checked;
                                        cb.rowData.enable = checked;
                                    });
                                }else{
                                    saveFailed(response.error_msg);
                                    __TABLE__._enableSwitch.checked = !checked;
                                }
                            });
                        });
                    }
                }

                if (evt.index > 0 && evt.index <= 7) {
                    evt.preventDefault();

                    var txt = rs.Text(evt.data);
                    evt.element.append(txt);
                    var sortUp = rs.Element('i').addClass('fa fa-sort-up');
                    var sortDown = rs.Element('i').addClass('fa fa-sort-down');
                    var sortIcon = rs.Element('div').attr({rule:'', rsid:"RsSortButton",'rs\:disabled':'adjustPriority.isAdjustPriority'}).addClass('RsSortButton').bind('click', function(cevt) {
                        if(adjustPriority.isAdjustPriority) return;
                        var rule = this.attr('rule');
                        var isAsc = rule == 'asc' || rule == '';
                        if (isAsc) {
                            this.attr('rule','desc');
                            isAsc = false;
                        } else {
                            this.attr('rule','asc');
                            isAsc = true;
                        }
                        var sortType = isAsc ? 'asc' : 'desc';
                        SetTableSort(evt.key, sortType);
                        adjustPriority.sortCatch.key = evt.key;
                        adjustPriority.sortCatch.sort = sortType;
                    }).append(sortUp).append(sortDown);
                    evt.element.append(sortIcon);
                    //break;
                }
                evt.element.attr('name', '_' + evt.key);
            },

            handleCreateTableCell: function(evt) {
                evt.preventDefault();
                var rowData = evt.rowData;

                switch(evt.index) {
                    case 0:
                        var cb = rs.XElement('Checkbox').attr({name:'enable', 'v\:disabled': 'adjustPriority.isAdjustPriority || !editable'}).appendTo(evt.element);
                        cb.checked = rowData.enable;
                        cb.rowData = rowData;
                        if(!editable){
                            cb.disabled = true;
                        }else{
                            cb.bind('change', function(event){
                                var _this = this;
                                var data = {
                                    enable: _this.checked,
                                    id: rowData.id
                                };
                                service.toggleSingleBTADataStatus(data, function(response){
                                    if(response.saveSuccess){
                                        _this.rowData.enable = _this.checked;
                                        TableHandler.updateTableEnableSwitch();
                                    }else{
                                        saveFailed(response.error_msg);
                                        cb.checked = !cb.checked;
                                    }
                                });
                            });
                        }

                        break;

                    case 1:
                        evt.element.append(rs.Text(rowData.id));
                        break;

                    case 2://业务名称
                        evt.element.append(rs.Text(rowData.businessName));
                        break;

                    case 3://业务类型
                        var bzName = TableHandler._getBusinessTypeName(rowData.businessType);
                        evt.element.append(rs.Text(bzName));
                        break;

                    case 4://域名
                        evt.element.append(rs.Text(rowData.name));
                        break;

                    case 5://访问路径
                        evt.element.append(rs.Text(rowData.path));
                        if(rowData.regexPath) {
                            evt.element.append(rs.Element('span').html('{% trans "Regex" %}').addClass('pathNotice'));
                        }
                        break;

                    case 6://方法
                        if (rowData.method == '0')
                            evt.element.append(rs.Text('{% trans "Any" %}'));
                        else
                            evt.element.append(rs.Text(rowData.method));
                        break;

                    case 7://限定参数
                        evt.element.append(rs.Text(rowData.specifyParameter));
                        break;

                    case 8://操作

                        if (editable) {
                            var rowIndex = evt.row.index;
                            var upBtn = rs.Element('i')
                            .attr({class:'fa-thin fa-caret-up','v\:show':'adjustPriority.isAdjustPriority && rowIndex > 0', title:"{% trans 'Move Up' %}"})
                            .bind('click', TableHandler.handleUpExtraData);
                            upBtn.row = evt.row;


                            var downBtn = rs.Element('i')
                            .attr({class:'fa-thin fa-caret-down','v\:show':'adjustPriority.isAdjustPriority && rowIndex < (__TABLE__.data.length - 1)', title:"{% trans 'Move Down' %}"})
                            .bind('click', TableHandler.handleDownExtraData);
                            downBtn.row = evt.row;

                            var editBtn = rs.Element('i')
                            .attr({class:'ic-pen','rs\:disabled':'adjustPriority.isAdjustPriority'})
                            .bind('click', TableHandler.handleModifyExtraData);
                            editBtn.row = evt.row;

                            var deleteBtn = rs.Element('i')
                            .attr({ class:'ic-trash','rs\:disabled':'adjustPriority.isAdjustPriority'})
                            .bind('click', TableHandler.handleDeleteExtraData);
                            deleteBtn.row = evt.row;

                            var elem = rs.Element('div');
                            elem.append(editBtn).append(deleteBtn).append(upBtn).append(downBtn);
                            elem.appendTo(evt.element);

                        } else {
                            evt.element.innerHTML = '--';
                        }

                        break;

                }
            },

            handleUpExtraData: function(evt) {
                var currentIndex = evt.currentTarget.row.index;
                adjustExtraData(currentIndex, currentIndex-1);
            },

            handleDownExtraData: function(evt) {
                var currentIndex = evt.currentTarget.row.index;
                adjustExtraData(currentIndex, currentIndex+1);
            },

            handleModifyExtraData: function(evt){
                if(adjustPriority.isAdjustPriority) return;
                var row = evt.target.row;
                editExtraData(row.data, row.index);
            },

            handleDeleteExtraData: function(evt){
                if(adjustPriority.isAdjustPriority) return;
                ask('{% trans "Confirm to delete this rule?" %}', function(){
                    var row = evt.target.row;
                    service.removeBTADataCollectRule({id:row.data.id}, function(response){
                        if(response.saveSuccess){
                            __TABLE__.removeRowByRow(row);
                            resetNeedAdjustPriority();

                        }else{
                            alert('{% trans "Delete failed" %}');
                        }
                    });
                });
            },

            updateTableEnableSwitch: function() {
                var cbList = __TABLE__.named('enable', false);
                var hasItem = cbList && cbList.length;
                var allCheckbox = __TABLE__._enableSwitch;

                if (hasItem){
                    var allCount = cbList.length;
                    var checkedCount = 0;
                    for (var i = 0; i < allCount; i++) {
                        var cb = cbList[i];
                        if (cb.checked) checkedCount += 1;
                    }

                    if (checkedCount == 0) {
                        allCheckbox.checked = false;
                    } else if (checkedCount == allCount) {
                        allCheckbox.checked = true;
                    } else {
                        allCheckbox.checkedPart = true;
                    }

                } else {
                    allCheckbox.checked = false;
                }


                allCheckbox.disabled = !hasItem;
            },

            getEmptyData: function(evt) {
                var empty = rs.Element('div');
                var msg = '{% trans "No Data" %}';
                if (editable) {
                    if (!upstreamList || !upstreamList.length) msg = '{% trans "Please configure at least one protected site first" %}';
                }

                empty.innerText = msg;
                evt.result = empty;
            },

            _getBusinessTypeName: function(id){
                var name = '';
                rs.each(businessTypeList, function(index, item){
                    if(item.id==id){
                        name = item.type;
                        return true;
                    }
                });
                return name;
            }
        }


        //########################################################
        //  Dialog
        //########################################################
        function initDialog(upstreamList){
            initUpstreamServerSelect(upstreamList);
            initBusinessTypeSelect();
            initCustormFieldsManager();
            refactorEditDialog();

            mDialog.save();

            __DIALOG__.customList = [];

            __DIALOG__.named('form').bind('setProperty', function(){
                __DIALOG__.hideErrorMessage();
            });

            function refactorEditDialog(){
                var box = __DIALOG__.child('.box');
                var pane = rs.Element('div').addClass('weakEditorPane').attr({ 'rs\:visible':'mDialog.businessType == 1 || mDialog.businessType == 2' });
                rs('#__WEAK_TEMP__').get('weakContent').appendTo(pane);
                __DIALOG__.insertBefore(pane, box);
                __DIALOG__.child('.weakLink').bind('toggle', handleEditWeakPasswordList);
            }

            function initBusinessTypeSelect(){
                var selector = rs('#businessType');
                var arr = [];
                var firstValue;
                rs.each(businessTypeList, function(index, item){
                    arr.push({key:item.id, value:item.type});
                    if(index==0) firstValue = item.id;
                });
                selector.load(arr);
                selector.value = mDialog.businessType = firstValue;
            }

            function initUpstreamServerSelect(upstreamList) {
                var selector = rs('#serverKey');
                var arr = [];
                var firstUpstreamKey;
                rs.each(upstreamList, function (i, upstream) {
                    var protocol = upstream.IsHttps ? 'https://' : 'http://';
                    var value, tmp = upstream.name.split('@');
                    if (tmp.length > 1) {
                        value = tmp[0] + '@' + protocol + tmp[1]
                    } else {
                        value = protocol + upstream.name;
                    }
                    arr.push({key: upstream.key, value: value, displayValue: upstream.name});
                    if (i === 0) firstUpstreamKey = upstream.key;
                });
                selector.load(arr);
                selector.value = mDialog.serverKey = firstUpstreamKey;
            }

            function initCustormFieldsManager() {
                __DIALOG__.named('bt_add_custom_field')
                    .bind('click', function () {
                        for (var i = 1; i <= 5; i++){
                            var _index = "" + i;
                            if (__DIALOG__.customList.indexOf(_index) === -1){
                                __DIALOG__.customList.push(_index);
                                break;
                            }
                        }

                        rs.updateProperty();
                    });

                __DIALOG__.named('removeCustomField')
                    .bind('click', function () {
                        var _index = this.attr('index'),
                            relatedFieldName = 'custom' + _index,
                            _cusIndex = __DIALOG__.customList.indexOf(_index);

                        mDialog[relatedFieldName] = '';
                        if (_cusIndex !== -1) __DIALOG__.customList.splice(_cusIndex, 1);

                        if (mDialog.customAsUsername === _index) mDialog.customAsUsername = 0;
                        rs.updateProperty();
                    })
                    .attr('title', '{% trans "Delete this field" %}');

                try {
                    var __USER_RADIOS__ = rs.__radio_lib__.customAsUsername;
                    if (__USER_RADIOS__) {
                        for (var _id = 0; _id < __USER_RADIOS__.length; _id++) {
                            var __RADIO__ = __USER_RADIOS__[_id];
                            __RADIO__.bind("click", function (e) {
                                var _this = e.currentTarget,
                                    avtive = _this.getAttribute("data-active");

                                if (avtive === "true") {
                                    mDialog.customAsUsername = "0";
                                    _this.setAttribute("data-active", "false");
                                } else {
                                    for (var i = 0; i < __USER_RADIOS__.length; i++) {
                                        __USER_RADIOS__[i].setAttribute("data-active", "false");
                                    }
                                    _this.setAttribute("data-active", "true");
                                }
                            });
                        }
                    }
                } catch (e) {}
            }
        }

        function createNextID(){
            var id = 0;
            rs.each(__TABLE__.data, function(index, item){
                if(!isNaN(item.id)){
                    id = Math.max(parseInt(item.id), id)
                }
            });
            if (id > maxId) {
                return id+1;
            }
            else {
                return maxId+1;
            }
        }

        function createExtraData(){
            launchExtraDataEditor({id:createNextID()}, '{% trans "Data collection" %}');
        }

        function editExtraData(data, rowIndex){
            launchExtraDataEditor(data, '{% trans "Data collection" %}', rowIndex);
        }

        function resetNeedAdjustPriority() {
            adjustPriority.needAdjustPriority = __TABLE__.data.length > 1;
            rs.updateProperty();
        }

        function setAdjustPriority() {
            adjustPriority.isAdjustPriority = true;
            //列表按ID升序排列
            if(!(adjustPriority.sortCatch.key == 'id' && adjustPriority.sortCatch.sort == 'asc')) SetTableSort('id', 'asc');
            //缓存调整优先级前的数据
            adjustPriority.tableCatch = utils.deepCopyObject(__TABLE__.data);
            __TABLE__._enableSwitch.css({display: 'none'});
            rs.updateProperty();
        }

        function saveAdjustPriority() {
            var data = __TABLE__.data;
            var fixData = data.map(function(item){ return item.id});
            service.saveBTADataPriority({data: fixData}, function(resp) {
                if(resp.saveSuccess){
                    rsalert('{% trans "Saved successfully." %}');
                    __TABLE__.data = fixTableDataPriority(fixData);
                    //关闭调整优先级
                    closeAdjustPriority();
                }else{
                    rsalert('{% trans "Failed to save." %}');
                }
            }, function() {
                rsalert('{% trans "Abnormal operation request!" %}');
            });

        }

        function fixTableDataPriority(ids) {
            var data = __TABLE__.data;
            ids.sort(function(a,b){return a-b});
            ids.forEach(function(item, i) {
                data[i].id = item;
            })
            return data;
        }

        function cancelAdjustPriority() {
            //重置数据
            __TABLE__.data = utils.deepCopyObject(adjustPriority.tableCatch);
            //重置排序
            if(adjustPriority.sortCatch.key) SetTableSort(adjustPriority.sortCatch.key, adjustPriority.sortCatch.sort);
            adjustPriority.tableCatch = [];
            //关闭调整优先级
            closeAdjustPriority();
        }

        function closeAdjustPriority() {
            adjustPriority.isAdjustPriority = false;
            __TABLE__._enableSwitch.css({display: 'inline-block'});
            rs.updateProperty();
        }

        function adjustExtraData(currentIndex, targetIndex) {
            var data = __TABLE__.data;
            var tempData = data[currentIndex];
            data[currentIndex] = data[targetIndex];
            data[targetIndex] = tempData;
            __TABLE__.data = data;
            rs.updateProperty();
        }

        function SetTableSort(key,sort) {
            rs('.RsSortButton').attr('rule','');
            var sortSelect = rs('.sort-selected');
            if(sortSelect) sortSelect.removeClass('sort-selected');
            __TABLE__.named('_'+key).addClass('sort-selected');
            __TABLE__.named('_'+key).child('div[rsid="RsSortButton"]').attr({rule: sort});
            var sortEvt = rs.Event('sortList');
            sortEvt.sort = sort;
            sortEvt.key = key;
            __TABLE__.trigger(sortEvt);
        }

        function launchExtraDataEditor(data, title, rowIndex){
            mDialog.restore();
            if(data){
                rs.each(data, function(k, v){
                    mDialog[k] = v;
                });
            }

            if (/^(1|2)$/.test(data.businessType)) {
                data['weak_pwd_list'] = BTAGlobalData.weak_pwd_list == '' ? '' : BTAGlobalData.weak_pwd_list.join('\n');
            }

            __DIALOG__._editMode = !isNaN(rowIndex);
            __DIALOG__._rowIndex = isNaN(rowIndex) ? -1 : rowIndex;
            __DIALOG__.open(data, null, null, title);

            expandFilledCustomFields(data);

            var customUserRadio = __DIALOG__.child('rs\\:Radio[value="'+data.customAsUsername+'"]');
            if (customUserRadio) customUserRadio.attr('data-active', true);
        }

        function expandFilledCustomFields(data) {
            __DIALOG__.customList = [];
            for (var i = 1; i <= 5; i++) {
                var key = 'custom' + i;
                if (data[key]) __DIALOG__.customList.push("" + i);
            }
            rs.updateProperty();
        }

        function handleCancelEditData(evt) {
            closeEditor();
        }

        function closeEditor() {
            __DIALOG__.customList = [];
            rs.updateProperty();
            __DIALOG__.closeDialog();
            collapseWeakPwdList();
        }

        function collapseWeakPwdList() {
            __DIALOG__.child('.weakLink').state = false;
        }

        function handleOnClickExtraDataEditorOK(evt) {
            __DIALOG__.named('form').submit();
        }

        function handleOnEditExtraData(event){
            event.preventDefault();
            var extraData = mDialog.getData();

            var pwdList = getSubmitWeakPwdList(extraData.businessType);
            if (pwdList != '' && pwdList.join('').length > STATIC_CONST.MAX_WEAK_PWD_CHAR) {
                dialogError('{% trans "The number of weak password characters has exceeded the maximum of 10,000." %}');
                return;
            }

            handleUnneedValues(extraData)

            if(hasSameSettings(extraData, __TABLE__.data)){
                dialogError('{% trans "The same name has already existed in configuration or the same path and same method and same specify parameter have already existed in the domain name." %}');

            } else {
                service.saveBTADataCollectRule(rs.merge({}, extraData, { weak_pwd_list: pwdList }), function(response){
                    if(response.saveSuccess){
                        var d = rs('#serverKey').selectedData;
                        if (d) extraData.name = d.displayValue;

                        if(__DIALOG__._editMode){
                            var data = __TABLE__.data;
                            data[__DIALOG__._rowIndex] = extraData;
                            __TABLE__.data = data;
                        }else{
                            __TABLE__.addRow(extraData);
                        }

                        resetNeedAdjustPriority();

                        BTAGlobalData.weak_pwd_list = pwdList; // 记录当前生效的值, 用于下一次比较
                    }else{
                        alert(response.error_msg)
                    }
                });

                closeEditor();
            }
        }

        function handleUnneedValues(data) {
            var businessType = data.businessType
            if (! /^(1|2|17)$/.test(businessType)) {
                data.account = ''
            }

            if (! /^(1|2)$/.test(businessType)) {
                data.password = ''
            }

            if (! /^(17)$/.test(businessType)) {
                data.validDate = ''
                data.cv2Code = ''
            }
        }

        function hasSameSettings(data, all){
            var has = false;
            rs.each(all, function(index, item){
                if(index==__DIALOG__._rowIndex ||item == data) return;
                if((item.path==data.path && item.serverKey==data.serverKey && item.regexPath==data.regexPath && item.method==data.method && item.specifyParameter==data.specifyParameter)
                    || item.businessName==data.businessName){
                    return has = true;
                }
            });
            return has;
        }

        function handleOnEditExtraError(event){
            dialogError(event.data.message);
        }

        function dialogError(msg){
            __DIALOG__.errorMessage = msg;
        }

        //########################################################
        //  Final
        //########################################################

        function handleShowWeakPwdTip(evt) {
            page.titleTip(evt.x, evt.y, '{% trans "The collected password field is used for weak password identification. This is optional."%}');
        }

        function handleHideWeakPwdTip(evt) {
            page.titleTipHide();
        }

        function handleShowDataCollLenTip(evt) {
            page.titleTip(evt.x, evt.y, '{% trans "Enable for Post/Cookie/Header/Response-body collect length."%}');
        }

        function handleHideDataCollLenTip(evt) {
            page.titleTipHide();
        }

        function handleEditWeakPasswordList(evt) {
            var expaned = evt.currentTarget.state ? true:null;
            __DIALOG__.attr('noticeOn', expaned);

            if (evt.currentTarget.state) {
                service.getWeakPWDListService(function(resp) {
                    BTAGlobalData.weak_pwd_list = resp.weak_pwd_list;
                    updateWeakPwdCountAndValue(resp.weak_pwd_list);
                }, function() {
                    rsalert('{% trans "Failed to get weak password dictionary." %}');
                });
            }
        }

        function handleChangeBusinessType(evt) {
            var tar = evt.currentTarget.value;
            if (tar != 1 && tar != 2) {
                collapseWeakPwdList();
            }

            var disableCustomAsUsername = [1, 2, 17];
            if (disableCustomAsUsername.indexOf(tar) != -1){
                mDialog.customAsUsername = 0;
            }
        }

        function handleChangeWeakPWDList(evt) {
            var list = processWeakPwdList();
            updateWeakPwdCountAndValue(list);
        }

        function updateWeakPwdCountAndValue(list) {
            __DIALOG__.named('weak_pwd_count').html(rs.formatString('（{% trans "Already have {0}" %}）', list.length));
            __DIALOG__.named('weak_pwd_list').value = list.join('\n');
        }

        function getSubmitWeakPwdList(businessType) {
            if (/^(1|2)$/.test(businessType)) {
                return processWeakPwdList();
            }
            return [];
        }

        function processWeakPwdList() {
            var list = [];
            var curValue = __DIALOG__.named('weak_pwd_list').value;
            if (curValue != '') {
                // 去多余的空行和空格
                curValue = curValue.replace(/(\n){2,}/g, '\n').replace(/(\n){1}/g, '#=#').replace(/(\s)+/g, '').replace(/(#=#)+/g, '\n');

                // 去重
                var arr = curValue.split('\n');
                try {
                    list = Array.from(new Set(arr));

                } catch(e) {
                    while(arr.length > 0) {
                        var item = arr.shift();
                        list.push(item);
                        while((idx = arr.indexOf(item)) > -1) {
                            arr.splice(idx, 1);
                        }
                    }
                }
            }

            return list;
        }

    </script>
{% endblock %}
