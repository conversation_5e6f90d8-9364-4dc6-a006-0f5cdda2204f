{% extends "v2/base.html" %}
{% load i18n %}
{% load nav_tags %}
{% block title %} {% trans 'Programmable Defending' %} {% endblock %}
{% block navigation %}{% trans 'Programmable Defending' %}{% endblock %}

{% block content %}
    <style>

        rs\:Table { cursor:default; }

        .working{color:green;}
        .dead{color:red;}
        [lock] .working, [lock] .dead{color:#999;}

        input[short]{
            width: 62px !important;
            margin-right: 0px;
        }


        rs\:select[host]{
            margin-right: 10px !important;
        }

        .scenesTab rs\:UBBTable div[table-cell] {
            word-break: break-word !important;
        }

        .scenesTab rs\:UBBTable div[header] > div{width: 70px;}
        .scenesTab rs\:UBBTable div[header] > div[name=_id] {  min-width: 70px; }
        .scenesTab rs\:UBBTable div[header] > div[name=_enable] { width: 50px !important; min-width: 50px; }
        .scenesTab rs\:UBBTable div[name=_key] { width: 100px; }
        .scenesTab rs\:UBBTable div[header] > div[name=_action_list] { width: 120px; }

        .scenesTab rs\:UBBTable div[name=_desc],
        .scenesTab rs\:UBBTable div[name=_host],
        .scenesTab rs\:UBBTable div[name=_path],
        .scenesTab rs\:UBBTable div[name=_key_value],
        .scenesTab rs\:UBBTable div[name=_target_path] {
            min-width: 100px;
            width: auto !important;
        }

        .scenesTab rs\:UBBTable div[header] div[name=_filter_conditions],
        .scenesTab rs\:UBBTable div[header] div[name=_TIMER] {
            min-width: 75px;
        }

        .scenesTab rs\:UBBTable div[header] div[name=_hold_time] { width: 80px !important; }
        .scenesTab rs\:UBBTable div[header] div[name=_min_event]{width: 100px !important;}
        .scenesTab rs\:UBBTable div[header] div[name=_key_value]{width: 150px !important;}

        .scenesTab rs\:UBBTable div[header] > div,
        .scenesTab rs\:UBBTable div[name=_host],
        .scenesTab rs\:UBBTable div[name=_path],
        .scenesTab rs\:UBBTable div[name=_key_value],
        .scenesTab rs\:UBBTable div[name=_OPERATION],{
            overflow: hidden;
            text-overflow:ellipsis;
            white-space: nowrap;
        }

        .scenesTab rs\:UBBTable div[name=_high_freq] { width:100px; }
        .scenesTab rs\:UBBTable div[name=_OPERATION] { min-width: 110px !important; }

        .scenePane .btnsPane {
            display: none;
        }

        .scenePane .btnsPane > .normalBtn {
            display: inline-block;
        }

        .scenePane .btnsPane > .adjustBtn {
            display: none;
        }


        .scenePane[adjustOrder] {
            position: fixed;
            top: 0px;
            bottom: 0px;
            left: 0px;
            right: 0px;
            z-index: 11111;
            margin-bottom: 0px;
            overflow: auto;
            padding-left: 50px;
            padding-right: 50px;
        }


        .scenePane[adjustOrder] .btnsPane > .normalBtn {
            display: none;
        }

        .scenePane[adjustOrder] .btnsPane > .adjustBtn {
            display: inline-block;
        }


        rs\:UBBTable div[name=_OPERATION] > .ic-alerts,
        rs\:UBBTable div[name=_OPERATION] > .ic-pen,
        rs\:UBBTable div[name=_OPERATION] > .ic-trash {
            display: inline-block;
        }


        rs\:UBBTable div[name=_OPERATION] > .adjustOrderEditor {
            display: none;
        }

        .scenePane[adjustOrder] rs\:UBBTable div[name=_OPERATION] > .ic-alerts,
        .scenePane[adjustOrder] rs\:UBBTable div[name=_OPERATION] > .ic-pen,
        .scenePane[adjustOrder] rs\:UBBTable div[name=_OPERATION] > .ic-trash {
            display: none;
        }

        .scenePane[adjustOrder] rs\:UBBTable div[name=_OPERATION] > .adjustOrderEditor {
            display: block;
        }

        .scenePane[adjustOrder] rs\:UBBTable div[row=row][moved] > div[table-cell] {
            border-top: 2px solid #00a6c8;
            border-bottom: 2px solid #00a6c8;
        }



        rs\:ModalDialog#__RULE_DIALOG__ div.box { top:6%; width:700px; max-width: 700px;}

        rs\:ModalDialog#__SHARE_EDIT_BOX__ div.box {
            width:550px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ p { background-color:#ddebee; padding:10px 20px; font-weight:bold; font-size:14px; }
        rs\:ModalDialog#__RULE_DIALOG__ table,
        rs\:ModalDialog#__RULE_DIALOG__ table tr {
            width:100%;
        }
        rs\:ModalDialog#__RULE_DIALOG__ table tr>td { padding:4px 0px; width:100%; display:block; }
        rs\:ModalDialog#__RULE_DIALOG__ table tr>td>div { display:inline-block; vertical-align: middle; }

        rs\:ModalDialog#__RULE_DIALOG__ table tr>td label:not(.drsLabel),
        rs\:PolicyBox > div > label {
            width: 100px;
            min-width:80px;
            text-align:right;
            display:inline-block;
            margin-right: 10px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ rs\:Select rs\:list {
            max-height: 219px;
            overflow-y: auto;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] rs\:Select,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td>input,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=statistics] input[name=prepare_path] {
            width:400px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] rs\:Select.combination,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] rs\:Select[rsname=counter_comparison],
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] rs\:Select[rsname=counter] {
            width: 110px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] rs\:MultiSelect { display:none; }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] rs\:MultiSelect,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] input[name=counter_threshold] {
            width: 190px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td#__PATH__ div label:first-child { margin-top:0px; }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td#__PATH__ input,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td#__KEY_VALUE_ROW__.key_value_input>input[name=key_value],
        rs\:ModalDialog#__RULE_DIALOG__ div[name=statistics] input[name=prepare_path] {
            margin-right:10px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=statistics] div.exceptionItems rs\:Label { text-align:left; vertical-align:bottom; margin-top:0px; }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td#__KEY_VALUE_ROW__>rs\:Label { display:none; }
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td#__KEY_VALUE_ROW__.key_value_input>rs\:Label { display:inline-block; }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=policy] rs\:Select,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=policy] td>input,
        rs\:ModalDialog#__RULE_DIALOG__ div[name=conditionPane] td input[name=high_freq],
        rs\:ModalDialog#__RULE_DIALOG__ div[name=policy] td input[name=min_event],
        rs\:ModalDialog#__RULE_DIALOG__ div[name=policy] td input[name=auto_list] {
            width:110px;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=policy] td input[name=auto_list] {
            vertical-align: middle;
        }

        #__RULE_DIALOG__ rs\:TabBox{margin: 10px 0px 0px 0px;}
        #__RULE_DIALOG__ rs\:TabBox > div:nth-child(1){margin-left: 5px;}
        #__RULE_DIALOG__ div[rs\:name] > div:nth-child(1) p{margin-top: 0px;}

        rs\:ModalDialog#__RULE_DIALOG__ div[name=policy] div[name=challengeValue] label {
            margin-top:0px;
            width:auto;
            margin-left:6px;
        }

        rs\:ModalDialog[name='shareEditBox'] table td:first-child{
            width: 100px;
            text-align: right;
            padding-right: 10px;
        }

        rs\:ModalDialog[name='shareEditBox'] p { line-height:1.5; }

        div.CodeMirror { border:1px solid #c3c3c3; }
        .buttonPane > button { height:30px; line-height:30px; }

        textarea[name=ruleContent] { opacity:0; }
        span[name=dev_des] { display: none; }
        span[name=dev_des].inlineBlock { display: inline-block; }
        #__DEV_TYPE__ > rs\:Label { vertical-align: middle; max-width: 150px; }

        .non-item { color: #f8634d; }
        .non-item:before { content: '\e915'; display: inline-block; margin-right: 2px; } 
        .ic-warning { color: #f8634d; }
        .ic-warning:before { margin-right: 2px; }


        #__LUA_SWITCH__ { height:30px; }
        #__LUA_SWITCH__ label { font-size:13px; margin-right:10px; }

        #__CHALLENGE_TAB__ { margin-bottom:50px; display:none; }
        #__CHALLENGE_TAB__ #__CHALLENGE_PERCENT__ [name=downList] { height:190px; overflow:auto; }
        #__CHALLENGE_TAB__ .action_content { display:inline-block; margin-left:10px; }
        #__CHALLENGE_TAB__ .action_content > label { margin-right:10px; }
        #__CHALLENGE_TAB__ input[name=delay] { margin-right:20px; }
        #__CHALLENGE_TAB__ div.CodeMirror { border:1px solid #c3c3c3; }
        #__CHALLENGE_TAB__ footer { z-index:9; }
        #__CHALLENGE_TAB__ #__THRESHOLD_INFO__ { margin-left:65px; margin-bottom:5px; }
        #__CHALLENGE_TAB__ #__CHALLENGE_INFO__ { margin-bottom:2px; }

        #__CHALLENGE_TAB__ #__CHALLENGE_PERCENT__,
        #__CHALLENGE_TAB__ #__CHALLENGE_ACTION__,
        #__CHALLENGE_TAB__ .ic-info {
            display:inline-block;
        }

        input[name=challengeDelay] { margin-right:18px; }

        #CLOCK_FORM {
            min-width: 100%;
        }

        rs\:form{
            min-width: 1000px;
        }

        div[row][lock]{
            color: #999 !important;
        }

        rs\:DatePicker{
            vertical-align: baseline !important;
            margin-left: 0px !important;
            width: 163px;
        }

        rs\:MyDatePicker[error]{
            background-color: transparent !important;
            color: inherit !important;
            box-shadow: none !important;
        }

        rs\:MyDatePicker[error] rs\:DatePicker{
            background-color: #fed5ce !important;
            box-shadow: 0 0 3px #f8634d;
            color: #981d08 !important
        }

        rs\:MyDatePicker[error] rs\:DatePicker input{
            background-color: transparent !important;
        }

        span[name=templatesCount] { margin-right:20px; }
        a[name=temp_view] { font-size:12px; }
        a[name=temp_view]:before { content:'\e936'; font-family: "rs-icon"; margin-right:4px; }
        #__RULE_DIALOG__ input[name=block_value],
        #__RULE_DIALOG__ input[name=redirect_value] {
            width:145px;
        }


        .filterPageContent{
            border-bottom: 1px solid #CCC;
            margin-bottom: 10px;
            position: relative;
            min-height: 400px;
        }

        rs\:FilterSelector{
            display: inline-block;
            width: 180px;
            border-right: 1px solid #CCC;
            position: absolute;
            top:0px;
            bottom:0px;
        }

        rs\:FilterSelector > input[type=text]{
            transition: all ease-out .2s;
            display: block;
            width: 100%;
            border-width: 0px 0px 1px 0px;
            border-radius: 0px;
            border-color: #CCC;
            padding-left: 25px;
            height: 30px;
            color: #999;
        }

        rs\:FilterSelector > input[type=text]:hover,
        rs\:FilterSelector > input[type=text]:focus{
            border-width: 0px 0px 1px 0px;
            border-color: #00a6c8;
            color: black;
        }

        rs\:FilterSelector > .ic-search{
            position: absolute;
            top: 2px;
            margin: 5px;
            color: #00a6c8;
            font-size: 16px;
        }

        rs\:FilterSelector > [name=filterTempList]{
            display: block;
            overflow-y: auto;
            overflow-x: hidden;
            background-color: #ddebee;
            position: absolute;
            top: 30px;
            bottom: 0px;
            width: 100%;
        }

        rs\:FilterSelector > [name=filterTempList] > div{
            padding: 5px;
            background-color: #d5e3e6;
            transition: background-color ease-out .2s;
            cursor: pointer;
            white-space: nowrap;
            text-overflow:ellipsis;
            overflow:hidden;
        }

        rs\:FilterSelector > [name=filterTempList] > div:nth-child(2n){
            background-color: #cfdddf;
        }

        rs\:FilterSelector > [name=filterTempList] > div:hover{
            background-color: rgba(255,255,255,0.8);
        }

        rs\:FilterSelector > [name=filterTempList] b{ color: #c12020; }

        rs\:MultiSelectList [name=icon],
        .filterPageContent [name=icon]{
            display: inline-block;
            margin-right: 5px;
            font-weight: bold;
            padding: 1px 3px;
            border-radius: 3px;
            background-color: black;
            color: white;
            line-height: normal !important;
            width: 13px;
            text-align: center;
        }

        rs\:MultiSelectList [s], .filterPageContent [s]{background-color: #00a000;}
        rs\:MultiSelectList [b], .filterPageContent [b]{background-color: #48549b;}
        rs\:MultiSelectList [n], .filterPageContent [n]{background-color: #dc7832;}
        rs\:MultiSelectList [ip], .filterPageContent [ip]{background-color: #FF2F2F;}
        rs\:MultiSelectList [e], .filterPageContent [e]{background-color: #a55eb8;}

        rs\:FilterList,
        div[name=advFilterPane] {
            display: none;
            margin-left: 181px;
            height: 480px;
            overflow-y: auto;
            overflow-x: hidden;
        }

        div[name=advFilterPane] { padding:8px }
        div[name=advFilterPane] > div[name=editorPane] { margin-bottom:20px; }
        div[name=advFilterPane] > div[name=editorPane] > div { height:22px; line-height:18px; }
        div[name=advFilterPane] > div[name=editorPane] rs\:ToggleButton { float:right; color:#00a6c8; display:inline-block; cursor:pointer; }
        div[name=advFilterPane] > div[name=editorPane] rs\:FilterEditor { display:block; height:240px; }
        div[name=advFilterPane] div[name=filter_conditions_lua] {
            border: 1px solid #c3c3c3;
            height:150px; margin-top:8px;
            padding: 8px;
            line-height: 18px;
            color: #0000b7;
            overflow: auto;
            word-break: break-word;
        }


        rs\:FilterList[active=''],
        div[name=advFilterPane][active=''] {
            display:block;
        }

        rs\:FilterList [item]{
            padding: 10px 10px;
            line-height: 25px;
            background-color: #f9f9f9;
            transition: all .2s ease-out;
            position: relative;
        }

        rs\:FilterList [item]:hover{
            background-color: #eef5f7 !important;
        }

        rs\:FilterList [item] [name=filterKey]{
            font-size: 14px;
            font-weight: bold;
        }

        rs\:FilterList [item]:nth-child(2n){background-color: transparent;}

        rs\:FilterList .fa-times{
            cursor: pointer;
            position: absolute;
            right: 15px;
            top: 15px;
            font-size: 20px;
            color: #931b1b;
            transition: color .2s ease-in;
            display: none;
        }

        rs\:FilterList [item]:hover .fa-times{
            display: inline-block;
        }

        rs\:FilterList .fa-times:hover{color: #FF2F2F;}
        rs\:FilterList .operator{width: 115px;}
        rs\:FilterList .long{width: 240px;}
        rs\:FilterList .explanation{ color: gray; }
        rs\:FilterList .fieldName{
            width: 110px;
            display: inline-block;
            text-align: right;
            padding-right: 10px;
        }
        rs\:FilterList form{ display: inline-block; }

        rs\:FilterList [name=innerFilterList]:empty:after{
            content: "{% trans 'Select the item in the left field list to add a filter' %}";
            width: 100%;
            line-height: 200px;
            text-align: center;
            display: block;
            color: gray;
        }

        .enumSelector{
            width: 250px !important;
        }

        rs\:FilterList [noexist],
        rs\:FilterList [noexist]:hover {
            background-color: #fed5ce !important;
            box-shadow: 0 0 3px #f8634d;
            color: #981d08;
        }

        rs\:ConvertEditorButton { position: absolute; top: 100px; right: 8px; }
        rs\:ConvertEditorButton > input[type=checkbox] {
            position: absolute;
            border: 1px solid;
            width: 100%;
            height: 120%;
            top: -5px;
            left: 0px;
            opacity: 0;
            cursor: pointer;
            z-index: 100000;
        }



        #__RULE_DIALOG__ .editorNotice{
            position: absolute;
            top:7%;
            width: 400px;
            height: 560px;
            background-color: #fbfbfb;
            padding: 10px;
            color: #313741;
            margin: auto;
            left: 0px;
            right: 0px;
            transition: left .2s ease-out;
            overflow-y: auto;
            border: 1px solid #666;
            opacity:0;
        }

        #__RULE_DIALOG__[noticeOn] .editorNotice{
            left: 369px;
            position: relative;
            opacity:1;
        }

        #__RULE_DIALOG__ .box{ transition:right .2s ease-out; left:0px; right:0px; }
        #__RULE_DIALOG__[noticeOn] .box{ left:0px; right: 360px; }

        #__RULE_DIALOG__ #__NOTICE_BUTTON__:after {
            content:'\e966';
            background-color: #00a6c8;
            color: white;
            border-radius: 3px;
            margin-left: 4px;
            font-size: 12px;
            border: 1px solid #00a6c8;
            padding-left:2px;
        }
        #__RULE_DIALOG__[noticeOn] #__NOTICE_BUTTON__:after { content:'\e964' }
        #__RULE_DIALOG__ div[name=ratePane],
        #__RULE_DIALOG__ div[name=timePane] {
            display:none;
        }

        #__RULE_DIALOG__ div[name=timePane] rs\:Select { width:80px !important; vertical-align:bottom; }
        #__RULE_DIALOG__ div[name=timePane] input[name=time_window_value] { width:90px; }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=statistics] div.des {
            display: inline-block;
            width: 400px;
            vertical-align: top;
        }

        rs\:ModalDialog#__RULE_DIALOG__ div[name=statistics] div.des .hlight {
            color:#fd9800;
        }

        rs\:UBBTable#__SNIPING_TABLE__ div[header=header]>div[name=_enable],
        rs\:UBBTable#__SNIPING_TABLE__ div[header=header]>div[name=_id] {
            width:60px;
        }

        rs\:UBBTable#__SNIPING_TABLE__ div[header=header]>div[name=_host],
        rs\:UBBTable#__SNIPING_TABLE__ div[header=header]>div[name=_target_path] {
            min-width:100px !important;
        }

        rs\:UBBTable#__SNIPING_TABLE__ div[header=header]>div[name=_start_time],
        rs\:UBBTable#__SNIPING_TABLE__ div[header=header]>div[name=_exception_type] {
            width:100px;
        }

        #__AUTO_LIST_ROW__ i.ic-help {
            font-size: 13px;
            color: #00a6c8;
            margin-left: 2px;
        }

        #__RULE_DIALOG__ rs\:Label.freqTypeRadioLabel { width:130px !important; }

        div.editorNotice ul {
            border: 1px solid #a7c3cc;
        }

        div.editorNotice ul li {
            line-height: 24px;
            padding: 2px 8px;
            border-bottom: 1px solid #a7c3cc;
        }

        div.editorNotice ul li:first-child {
            background-color: #eef5f7;
        }

        div.editorNotice ul li:last-child {
            border-bottom: transparent;
        }

        div.editorNotice ul li>span:first-child {
            width: 100px;
            display: inline-block;
        }

        #__LUA_SWITCH__ rs\:Switch {
            vertical-align: top;
            margin-right: 30px;
        }

        .advRulePane {
            position:relative;
        }

        ul[name=luaLogOperator],
        ul[name=luaLogOperator] li {
            display:inline-block
        }

        div[name=luaLog] {
            position: absolute;
            top: 86px;
            bottom: 0px;
            z-index: 22;
            background-color: rgba(0, 0, 0, 0.85);
            left: 0px;
            right: 0px;
            color: white;
            overflow: hidden;
            display:none;
        }

        div[name=luaLog] div[name=content] {
            padding: 0px 10px;
            word-break: break-word;
            overflow: auto;
            position: absolute;
            top: 40px;
            bottom: 0px;
            left: 0px;
            right: 0px;
        }

        div[name=luaLog] .ic-cross {
            font-size: 20px;
            cursor: pointer;
            color: #00a6c8;
            display: inline-block;
            float: right;
            text-align: right;
            padding: 10px;
            word-break: break-word;
        }

        div[name=luaLog] div[name=content] > pre {
             margin-bottom:20px;
        }

        rs\:FileUpload[buttonOnly=true] [name="__file_browser_wapper__"] {
            border: 0px;
        }

        rs\:FileUpload[buttonOnly=true][name="source_file"] {
            display: none;
        }

        #__UBB_RESOURCE_TABLE__ div[row=row] div[table-cell]:first-child {
            width: 350px;
            word-break: break-word;
        }

        #__UBB_RESOURCE_TABLE__ div[row=row] div[table-cell]:nth-child(2) { width: 80px; }

        input[noTempIdError],
        rs\:Select[nonFileError] {
            background-color: #fed5ce !important;
            box-shadow: 0 0 3px #f8634d;
            color: #981d08 !important;
        }


        rs\:Table[name=shareDict] div[table-cell].shareWarning { color: orange }
        rs\:Table[name=shareDict] div[colname=_name],
        rs\:Table[name=shareDict] div[colname=_description] {
            max-width: 200px;
        }

        rs\:Table[name=shareDict] div[colname=_timestamp] { width: 160px; }
        rs\:Table[name=shareDict] div[colname=_OPERATE] { width: 120px; }
        rs\:Table[name=shareDict] .helpIcon { color: #01a6c9; }
        input[name=desc] { width: 520px; margin-left: 20px; }

        rs\:MultiSelect div.mutil-select-filter-pane > input { width:228px; }
        rs\:MultiSelect[needfilter=true] [rs\:role=dropLayer] rs\:MultiSelectList { min-width:285px; }

        rs\:MultiSelectList > div:hover {
            color: #00a6c8;
        }

        .addObjectItemButton {
            border: 1px dashed #CCC;
            color: #00a6c8;
            line-height:30px;
            text-align: center;
            width: 560px !important;
            display: inline-block;
            cursor: pointer;
            transition: all .15s ease-in;
            border-radius: 5px;
            background-color: #fafafa;
            position: relative;
        }
        .addObjectItemButton:hover {
            background-color:#00a6c8;
            color: white;
        }
        .addObjectItemButton i {
            vertical-align: middle;
            font-size: 20px !important;
        }

        .ubbCounterItem {
            padding: 10px;
            position: relative;
        }

        .ubbCounterItem:nth-child(odd) {
            background-color: #f9f9f9;
            transition: all .2s ease-out;
        }

        .ubbCounterItem:hover {
            background-color: #eef5f7
        }

        .ubbCounterItem i[name=remove] {
            position: absolute;
            top: 6px;
            right: 20px;
            font-size: 16px;
            display: none;
        }

        .ubbCounterItem:hover i[name=remove] {
            display: block;
        }

        rs\:UBBCounter:empty:after {
            content: "{% trans 'Click the button above to add the counter' %}";
            width: 100%;
            line-height: 80px;
            text-align: center;
            display: block;
            color: gray;
            margin-top: 10px;
        }

        .ubbCounterPane {
            max-height: 450px;
            overflow: auto;
            margin-bobttom: 10px;
        }


        .ubbCounterPane #addCounter {
            margin: 10px 0px;
            font-size: 12px;
        }

        #__RULE_DIALOG__ .fa-times{
            transition: all .2s ease-out;
            cursor: pointer;
            color: #931b1b;
            font-size: 19px !important;
            margin-left: 12px;/*TODO: remove this if has weight field*/
        }
        #__RULE_DIALOG__ .fa-times:hover{
            color: #FF0000;
        }

        span[name=counterWarning] {
            color: #f58200;
            font-size: 11px;
            display: inline-block;
            max-width: calc(100% - 310px);
            vertical-align: middle;
        }

        .warningColor { color: #f58200; }

        rs\:ModalDialog#__RULE_DIALOG__ rs\:Select[rsname=counter_comparison],
        rs\:ModalDialog#__RULE_DIALOG__ rs\:Select[rsname=counter_operation] {
            vertical-align: bottom;
        }
        .addCounterItemBtnPane { position: absolute; top: 90px; right: 8px; }
        .addCounterItemBtnPane i {
            vertical-align: text-bottom;
            margin-right: 6px;
        }

        #__CHALLENGE_FORWARD_PROTOCOL__ { width: 80px; }

        rs\:ShieldTimer button { margin-left: 10px; }
        rs\:GeograghSelect div[rs\:role=dropLayer] { width: 220px; }
        rs\:ModalDialog#__RULE_DIALOG__ rs\:Select { vertical-align: inherit; }

        #geoLimit > ul > li > label,
        rs\:TimerButton.timerControl > ul > li > label {
            margin-right: 19px;
        }

        #__TIMER_DIALOG__ > div.box { width: 580px; }

        rs\:UBBTable[ismirrormode="true"] div[header=header] > div[name=_action_list],
        rs\:UBBTable[ismirrormode="true"] div[row=row] > div[table-cell][name=_action_list] {
            display: none !important;
        }

        .policyCell{
            padding-bottom: 10px !important;
            text-align: center;
        }
        rs\:PolicyBox{
            border-top: 1px dashed #CCC;
            padding-top: 5px;
            display: block;
            line-height: 30px !important;
            margin-bottom: 5px;
            /*TODO: remove following rules if has weight field*/
            text-align: left;
        }
        rs\:PolicyBox > div{
            padding: 2px 0px;
        }
        rs\:PolicyBox a{
            text-decoration: underline;
        }

        rs\:PolicyBox a[name=templateLink][href='javascript:void(0)'] {
            color:#313741;
            text-decoration:none;
        }

        rs\:PolicyBox div > label:nth-child(1){
            margin-top: auto !important;
        }

        rs\:PolicyBox > div > .policyItem label{
            margin-right: 5px !important;
            font-weight: normal !important;
            width: auto !important;
            min-width: auto !important;
        }
        rs\:PolicyBox rs\:Select{
            margin-right: 6px;
            width: 80px !important;
            vertical-align: middle;
        }
        rs\:PolicyBox input{
            margin-right: 6px !important;
        }
        rs\:PolicyBox input[number]{
            width: 100px !important;
        }
        rs\:PolicyBox input[number][nospace]{
            margin-right:5px !important;
        }
        rs\:PolicyBox [space]{
            margin-left: 10px !important;
        }
        rs\:PolicyBox [name=blankArea]{
            width: 104px !important;
            display: inline-block;
            text-align: center;
        }

        rs\:PolicyBox [name=challenge_value]{
            margin-right: 10px !important;
        }
        .policyBoxCreateButton{
            border: 1px dashed #CCC;
            color: #00a6c8;
            line-height:30px;
            text-align: center;
            width: 400px !important;
            display: inline-block;
            cursor: pointer;
            transition: all .15s ease-in;
            border-radius: 5px;
            background-color: #fafafa;
        }
        .policyBoxCreateButton:hover{
            background-color:#00a6c8;
            color: white;
        }
        .policyBoxCreateButton i{
            vertical-align: middle;
            font-size: 20px !important;
        }

        rs\:PolicyBox input[name=forward_upstream_name]{
            width: 110px !important;
            max-width: 110px !important;
        }

        rs\:PolicyBox input[name=forward_port] {
            width: 50px !important;
        }

        rs\:PolicyBox input[name=delay] {
            width: 50px !important;
        }

        rs\:PolicyBox i.ic-help {
            font-size: 13px;
            color: #00a6c8;
            margin-left: 2px;
        }

        rs\:PolicyBox rs\:Select[action] {
            width: 110px !important;
        }

        rs\:PolicyBox rs\:Select[name=responseHtml_value],
        rs\:PolicyBox rs\:Select[name=insertjs_value] {
            width: 130px !important;
        }

        #__AUTO_LIST_TYPE__ {
            width: 155px !important;
        }

        .loadingInfo {
            color: #adafb3;
            border-bottom: 1px solid #d6d7d9;
            margin: 0px;
            padding: 4px 0px;
        }
    </style>

    <rs:style enable="#embedMode">
        rs\:FilterSelector > input[type=text]:hover,
        rs\:FilterSelector > input[type=text]:focus{
            border-color: #549BEF;
        }

        rs\:FilterSelector > .ic-search,
        #__AUTO_LIST_ROW__ i.ic-help,
        rs\:Table[name=shareDict] .helpIcon,
        rs\:PolicyBox i.ic-help,
        div[name=luaLog] .ic-cross,
        rs\:MultiSelectList > div:hover,
        .addObjectItemButton,
        div[name=advFilterPane] > div[name=editorPane] rs\:ToggleButton {
            color: #549BEF;
        }

        #__RULE_DIALOG__ #__NOTICE_BUTTON__:after {
            background-color: #549BEF;
            border: 1px solid #549BEF;
        }

        .addObjectItemButton:hover {
            background-color:#549BEF;
        }

        .addObjectItemButton i {
            vertical-align: baseline;
        }

        div[rsid="ubb_rule"],
        div[rsid="ubb_counter"]{
            max-height:380px;
            overflow-y: auto;
        }

        div[rsid="ubb_counter"] .ubbCounterPane {
            max-height: none;
        }

        rs\:FilterList, div[name=advFilterPane] {
            height: 380px;
        }

        .scenePane[adjustOrder] {
            padding-left: 20px;
            padding-right: 20px;
        }
    </rs:style>

    <script>
        var readable = auth('Programmable_Defending', 'r');
        var editable = auth('Programmable_Defending', 'w');
    </script>

    <link rel="stylesheet" href="/static/js/thirdPart/codemirror/codemirror.min.css"/>
    <link rel="stylesheet" href="/static/js/thirdPart/codemirror/addon/hint/show-hint.css"/>
    <link rel="stylesheet" href="/static/js/thirdPart/codemirror/mode/ubb-filter.css"/>

    <div id="ubbWarningPane" v:if="isMirrorMode">
        <section class="errorTips" error>
            <i class="ic-alert"></i>
            <span name="errorMsg">{% trans "At present, the system works in the traffic mirroring mode, and the programmable countermeasure rules only monitor and cannot be intercepted." %}</span>
        </section>
    </div>

    <rs:Form model="#mPage">
        <rs:TabPanel id="__TAB_PANEL__" keepIndexWhenRefresh="true">

            <div
                rs:name="{% trans 'Web Access Policy'%}"
                rs:onShow="updateFilterOptions()"
                rs:onInit="handleInitRuleTab('__SCENES_TAB__')"
                id="__SCENES_TAB__"
                rsid="scenes_tab"
                class="scenesTab"
            >
                <section class="scenePane">
                    <header>
                        <span>{% trans 'Counter Scene' %}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('COUNTER').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('COUNTER').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('COUNTER').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('COUNTER').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('COUNTER').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('COUNTER').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__COUNTER_TABLE__" name="counterRules"  class="rsTable" v:readonly="!editable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Counter' %}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|counter|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="COUNTER"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>
                </section>

                <section class="scenePane">
                    <header>
                        <span>{% trans 'Blacklist Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('BLACK').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('BLACK').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('BLACK').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('BLACK').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('BLACK').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('BLACK').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__BLACK_RULES_TABLE__" class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|Value|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|key_value|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="BLACK"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>

                </section>

                <section class="scenePane">
                    <header>
                        <span>{% trans 'High Frequence Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('HIGH_FREQ').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('HIGH_FREQ').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('HIGH_FREQ').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('HIGH_FREQ').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('HIGH_FREQ').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('HIGH_FREQ').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__HIGH_FREQ_TABLE__"  class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Frequence'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|high_freq|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="HIGH_FREQ"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>

                </section>

                <section class="scenePane" v:show="!isMirrorMode">
                    <header>
                        <span> {% trans 'Bad Behavior Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('BAD_BEHAVIOR').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('BAD_BEHAVIOR').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('BAD_BEHAVIOR').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('BAD_BEHAVIOR').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('BAD_BEHAVIOR').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('BAD_BEHAVIOR').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__BAD_BEHAVIOR_TABLE__"  class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|{% trans 'Number of events is less than'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|min_event|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="BAD_BEHAVIOR"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>

                </section>

                <section class="scenePane" v:if="global_params.hasRepuServer">
                    <header>
                        <span>{% trans 'Reputation Secne'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('REPUTATION').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('REPUTATION').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('REPUTATION').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('REPUTATION').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('REPUTATION').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('REPUTATION').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__SOFT_BLOCK_TABLE__"  class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Threat Value'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|threat_value|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="REPUTATION"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>
                </section>

                <section class="scenePane">
                    <header>
                        <span> {% trans 'New Comer Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('NEW_COMER').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('NEW_COMER').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('NEW_COMER').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('NEW_COMER').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('NEW_COMER').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('NEW_COMER').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__NEW_USER_TABLE__" name="newUserRules"  class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Expire Date'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|hold_time|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="NEW_COMER"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>
                </section>

                <section class="scenePane">
                    <header>
                        <span>{% trans 'Sniping Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('SNIPING').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('SNIPING').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('SNIPING').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('SNIPING').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('SNIPING').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('SNIPING').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__SNIPING_TABLE__" name="snipingRules"  class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Sniping Path'%}|Key|{% trans 'Start Time'%}|{% trans 'Error Type'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|target_path|key|start_time|exception_type|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="SNIPING"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>
                </section>

            </div>

            <div rs:name="{% trans 'Web Dynamic Challenge'%}" rsid="challenge_tab"
                 rs:tab-visible="!isApiProductOrMirrorMode && global_params.isDCEnabled" id="__CHALLENGE_TAB__"
                 rs:onInit="UBBChallengeManager.handleInitDynamicChallenge(event)">
                <section>
                    <header>
                        <span> {% trans 'Dynamic Challenge'%}</span>
                    </header>
                    <rs:Label rsid="enableCaptchaLabel"><rs:Checkbox rsid="enableCaptcha" name="enableCaptcha"
                                                                     onChange="UBBChallengeManager.handleEnableCaptcha(event)"
                                                                     v:disabled="!editable"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                </section>

                <section>
                    <header>
                        <span> {% trans 'Captcha Templates'%}</span>
                    </header>

                    <ul>
                        <li>
                            <label> {% trans 'Templates Version'%}</label>
                            <span name="version"></span>
                        </li>
                        <li>
                            <label> {% trans 'Templates Count'%}</label>
                            <span name="templatesCount"></span>
                            <a name="temp_view" href="/system/labs/captcha/?operate=detail" onclick="return templateIntroductionOnclick();" target="_blank">{% trans 'Templates Introduce'%}</a>
                        </li>
                        <li>
                            <label>{% trans 'Is it the default template?'%}</label>
                            <span name="defaultTxt"></span>
                        </li>
                        <li>
                            <label>{% trans 'Modify Time'%}</label>
                            <span name="modifyTime"></span>
                        </li>
                        <li v:if="editable">
                            <label>{% trans 'Download Template Package'%}</label>
                            <button type="button"
                                    onclick="service.downloadLabCaptchaTemp(event)"
                                    v:disabled="mPage.isErrorData">{% trans 'Download'%}</button>
                        </li>
                        <li v:if="editable">
                            <label>{% trans 'Upload Template'%}</label>
                            <rs:FileUpload id="__TEMP_UPLOAD__" name="template" action="/system/labs/captcha/"
                                           onData="UBBChallengeManager.handleUploadCallback(event)" accept=".zip"
                                           rs:disabled="mPage.isErrorData"
                                           onBeforeSubmit="UBBChallengeManager.handleUploadTempBeforeSubmit(event)">
                                {% csrf_token %}
                                <input name="operate" value="upload" type="hidden" />
                            </rs:FileUpload>
                        </li>
                        <li v:if="!mPage.isDefault && editable">
                            <label>{% trans 'Restore the default template'%}</label>
                            <button type="button" onclick="UBBChallengeManager.handleRollbackTemp(event)"
                                    v:disabled="mPage.isErrorData">{% trans 'Restore'%}</button>
                        </li>
                    </ul>
                </section>

                <section>
                    <header>
                        <span>{% trans 'Challenge Policy'%}</span>
                    </header>

                    <ul>
                        <li>
                            <label>{% trans "Threshold of Bot Identification" %}</label>
                            <rs:Slider id="__THRESHOLD__" name="threshold"></rs:Slider>
                            <i class="ic-info" id="__THRESHOLD_INFO__"></i>
                        </li>
                        <li>
                            <label>{% trans "Repeated Challenge" %}</label>
                            <rs:Checkbox name="mandatory" v:disabled="!mPage.enableCaptcha || !editable"></rs:Checkbox>
                            <i class="ic-info" id="__CHALLENGE_INFO__"></i>
                        </li>
                        <li v:show="!mPage.mandatory">
                            <label>{% trans "Action Rate" %}</label>
                            <div id="__CHALLENGE_PERCENT__"></div>
                        </li>
                        <li v:show="!mPage.mandatory">
                            <label>{% trans "Action" %}</label>
                            <div id="__CHALLENGE_ACTION__"></div>

                            <div class="action_content" v:show="mPage.challengeAction=='block_code'">
                                <label>{% trans "Status Code" %}</label>
                                <input name="challenge_block_value" type="text"
                                       v:disabled="!mPage.enableCaptcha || !editable"
                                       placeholder="{% trans 'Integer'%}: 200~600" class="numberInput" />
                            </div>

                            <div class="action_content" v:show="mPage.challengeAction=='redirect'">
                                <label>{% trans "Page Path" %}</label>
                                <input name="challenge_redirect_value" maxlength="50" type="text"
                                       v:disabled="!mPage.enableCaptcha || !editable" />
                            </div>

                            <div class="action_content" v:show="mPage.challengeAction=='forward'">
                                <rs:select id="__CHALLENGE_FORWARD_PROTOCOL__"
                                           name="challenge_forward_protocol"
                                           rs:disabled="!editable">
                                    <div rs-option value="http://">http://</div>
                                    <div rs-option value="https://">https://</div>
                                </rs:select>
                                <input name="challenge_forward_upstream_name" v:disabled="!editable" type="text" maxlength="128" placeholder="{% trans 'IPv4/IPv6/Domain' %}">
                                <input name="challenge_forward_port" v:disabled="!editable" type="text" placeholder="{% trans 'Port' %}">
                            </div>

                            <div class="action_content" v:show="mPage.challengeAction=='challenge'">
                                <label>{% trans "Template ID" %}</label>
                                <input name="challenge_template_id" type="text" maxlength="100"
                                       v:disabled="!mPage.enableCaptcha || !editable" placeholder="{% trans 'Default is random selection ID'%}" class="numberInput" />
                                <a name="temp_view" href="/system/labs/captcha/?operate=detail" onclick="return templateIntroductionOnclick();" target="_blank">{% trans 'Template Introduce'%}</a>
                            </div>

                            <div class="action_content" v:show="mPage.challengeAction=='insertjs'">
                                <label>{% trans 'Insert JS'%}</label>
                                <div class="inlineBlock" id="chanllengeJSList"></div>
                            </div>

                            <div class="action_content" v:show="mPage.challengeAction=='response_html'">
                                <label>{% trans 'return html page'%}</label>
                                <div class="inlineBlock" id="chanllengeHTMLList"></div>
                            </div>

                        </li>
                        <li v:show="!mPage.mandatory && mPage.challengeAction!=ACTION_TRANS_MAP.none.key && mPage.challengeAction!=ACTION_TRANS_MAP.insertjs.key">
                            <label>{% trans "Delay" %}</label>
                            <input name="challengeDelay" value="0" type="text"
                                   v:disabled="!mPage.enableCaptcha || !editable"
                                   placeholder="{% trans "Number:" %}0~60" />{% trans 'second(s)' %}
                        </li>

                    </ul>
                </section>

                <section>
                    <header>
                        <span>{% trans "Template Style Editor" %}</span>
                        <p>{% trans "Customize CSS for template pages." %}</p>
                    </header>

                    <div>
                        <textarea name="editContent"></textarea>
                    </div>
                </section>

                <!-- Bottom -->
                <footer v:if="editable">
                    <p>
                        <button type="button" onclick="UBBChallengeManager.handleSubmitChallengeData(event)"
                                v:disabled="mPage.isErrorData"
                                class="floatRight" important operation>{% trans 'Save All' %}</button>
                    </p>
                </footer>
            </div>

            <div
                rs:name="{% trans 'App Access Policy'%}"
                rs:onShow="updateFilterOptions()"
                rs:onInit="handleInitRuleTab('__APP_SCENES_TAB__')"
                class="scenesTab" id="__APP_SCENES_TAB__"
                rs:tab-visible="!isApiProductOrMirrorMode && hasMobileLicence()"
            >
                <section class="scenePane">
                    <header>
                        <span>{% trans 'APP Counter Scene' %}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('APP_COUNTER').createNew()" class="normalBtn">{% trans 'Add'%}</button>
                        <button type="button" onclick="getTable('APP_COUNTER').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('APP_COUNTER').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('APP_COUNTER').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('APP_COUNTER').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('APP_COUNTER').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__APP_COUNTER_TABLE__" name="appCounterRules"  class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Counter' %}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|counter|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="APP_COUNTER"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>
                </section>

                <section class="scenePane">
                    <header>
                        <span>{% trans 'App Blacklist Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('APP_BLACK').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('APP_BLACK').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('APP_BLACK').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('APP_BLACK').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('APP_BLACK').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('APP_BLACK').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__APP_BLACK_RULES_TABLE__" class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|Value|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|key_value|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="APP_BLACK"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>

                </section>


                <section class="scenePane">
                    <header>
                        <span>{% trans 'App High Frequence Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('APP_HIGH_FREQ').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('APP_HIGH_FREQ').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('APP_HIGH_FREQ').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('APP_HIGH_FREQ').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('APP_HIGH_FREQ').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('APP_HIGH_FREQ').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__APP_HIGH_FREQ_TABLE__" class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Frequence'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|high_freq|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="APP_HIGH_FREQ"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>

                </section>


                <section rs:visible="global_params.hasRepuServer" class="scenePane">
                    <header>
                        <span>{% trans 'App Reputation Scene'%}</span>
                    </header>

                    <p class="btnsPane">
                        <button type="button" onclick="getTable('APP_REPUTATION').createNew()" class="normalBtn">{% trans 'Add '%}</button>
                        <button type="button" onclick="getTable('APP_REPUTATION').enableAll()" class="normalBtn enableAllBtn">{% trans 'Check All'%}</button>
                        <button type="button" onclick="getTable('APP_REPUTATION').disableAll()" class="normalBtn disableAllBtn">{% trans 'Clear All'%}</button>
                        <button type="button" onclick="getTable('APP_REPUTATION').openAdjustOrder()" class="normalBtn adjustOrderBtn">{% trans 'Adjust Priority' %}</button>
                        <button type="button" onclick="getTable('APP_REPUTATION').revertOrder()" class="adjustBtn">{% trans 'Cancel' %}</button>
                        <button type="button" onclick="getTable('APP_REPUTATION').saveAdjustOrder()" class="adjustBtn">{% trans 'Save Adjust Priority' %}</button>
                    </p>
                    <rs:UBBTable id="__APP_SOFT_BLOCK_TABLE__" class="rsTable"
                        header= "{% trans 'Enable'%}|ID|{% trans 'Strategy Name'%}|Host|{% trans 'Path'%}|Key|{% trans 'Threat Value'%}|{% trans 'Action '%}|{% trans 'Filter '%}|{% trans 'Timer'%}|{% trans 'Operation'%}"
                        cells="enable|id|desc|host|path|key|threat_value|action_list|filter_conditions|TIMER|OPERATION"
                        businessType="APP_REPUTATION"
                        emptyText="{% trans 'No Data'%}">
                    </rs:UBBTable>

                </section>

            </div>

            <div rs:name="{% trans 'Website shield' %}" rsid="website_shield" id="__WEBSITE_SHIELD__"
                rs:tab-visible="!isMirrorMode">
                <section id="s901">
                    <header>
                        <span>{% trans 'One Click Disconnection' %} (ID: 901)</span>
                        <p>{% trans 'All requests for the following domain names will be blocked.' %}</p>
                    </header>
                    <rs:Label><rs:Checkbox onchange="WebsiteShieldManager.clickEnable(event, 901)" key="enable901"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                     <ul name="content901" class="shieldContent">
                        <li>
                            <label>Host</label>
                            <rs:FilterStringItem key="host" id="host901"></rs:FilterStringItem>
                        </li>
                        <li>
                            <label>{% trans 'Blocking action' %}</label>
                            <rs:BlockAction id="blockAction901"></rs:BlockAction>
                        </li>
                        <li>
                            <rs:TimerButton id="timer901" ruleId="901" editable="{editable}"></rs:TimerButton>
                        </li>
                    </ul>
                    <ul>
                        <li v:if="editable">
                            <label>&nbsp;</label>
                            <button type="button" onclick="WebsiteShieldManager.saveShield(event, 901)">{% trans 'Save ' %}</button>
                        </li>
                    </ul>
                </section>

                <section id="s902">
                    <header>
                        <span>{% trans 'Website lock' %} (ID: 902)</span>
                        <p>{% trans 'Users are only allowed to access the GET and HEAD methods in the following domain names.' %}</p>
                    </header>
                    <rs:Label><rs:Checkbox onchange="WebsiteShieldManager.clickEnable(event, 902)" key="enable902"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                    <ul name="content902" class="shieldContent">
                        <li>
                            <label>Host</label>
                            <rs:FilterStringItem key="host" id="host902"></rs:FilterStringItem>
                        </li>
                        <li>
                            <rs:TimerButton id="timer902" ruleId="902" editable="{editable}"></rs:TimerButton>
                        </li>
                    </ul>
                    <ul>
                        <li v:if="editable">
                            <label>&nbsp;</label>
                            <button type="button" onclick="WebsiteShieldManager.saveShield(event, 902)">{% trans 'Save ' %}</button>
                        </li>
                    </ul>
                </section>

                <section id="s903">
                    <header>
                        <span>{% trans 'Website static' %} (ID: 903)</span>
                        <p>{% trans 'Users are only allowed to access static web pages (HTML, HTM) and resource files in the following domain names, and the rest are blocked.' %}</p>
                    </header>
                    <rs:Label><rs:Checkbox onchange="WebsiteShieldManager.clickEnable(event, 903)" key="enable903"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                    <ul name="content903" class="shieldContent">
                        <li>
                            <label>Host</label>
                            <rs:FilterStringItem key="host" id="host903"></rs:FilterStringItem>
                        </li>
                        <li>
                            <label>{% trans 'Exception path' %}</label>
                            <rs:FilterStringItem key="exception_path" id="path903"></rs:FilterStringItem>
                        </li>
                        <li>
                            <rs:TimerButton id="timer903" ruleId="903" editable="{editable}"></rs:TimerButton>
                        </li>
                    </ul>
                    <ul>
                        <li v:if="editable">
                            <label>&nbsp;</label>
                            <button type="button" onclick="WebsiteShieldManager.saveShield(event, 903)">{% trans 'Save ' %}</button>
                        </li>
                    </ul>
                </section>

                <section id="s904">
                    <header>
                        <span>{% trans 'Regional lock' %} (ID: 904)</span>
                        <p>{% trans 'The following domain names are restricted to users in some regions.' %}</p>
                    </header>
                    <rs:Label><rs:Checkbox onchange="WebsiteShieldManager.clickEnable(event, 904)" key="enable904"></rs:Checkbox>{% trans 'Enable' %}</rs:Label>
                    <ul name="content904" class="shieldContent">
                        <li>
                            <label>Host</label>
                            <rs:FilterStringItem key="host" id="host904"></rs:FilterStringItem>
                        </li>
                        <li>
                            <rs:RegionLimit id="geoLimit"></rs:RegionLimit>
                        </li>

                        <li>
                            <rs:TimerButton id="timer904" ruleId="904" editable="{editable}"></rs:TimerButton>
                        </li>
                    </ul>
                    <ul>
                        <li v:if="editable">
                            <label>&nbsp;</label>
                            <button type="button" onclick="WebsiteShieldManager.saveShield(event, 904)">{% trans 'Save ' %}</button>
                        </li>
                    </ul>
                </section>
            </div>

            <div
                rs:name="{% trans 'Advanced'%}"
                rsid="advanced_tab"
                id="__ADVANCED_TAB__"
                rs:onInit="LuaEditorModule.handleInitLuaEditor(event)"
            >

                <section>
                    <header>
                        <span>{% trans 'Programmable Defending Settings' %}</span>
                        <p>
                            {% trans 'Export or import Programmable Defending settings.' %}<br />
                            <span class="notice">
                                {% trans 'Note: Resource Files cannot be exported via this operation, but can only be downloaded separately.' %}
                                <span v:if="editable">{% trans 'To avoid interruption to applications, please make sure to import the required resource files before importing this settings file.' %}</span>
                            </span>
                        </p>
                    </header>

                    <div>
                        <button type="button" onclick="handleExportUbbConfig()" style="width: 60px;">{% trans 'Export'%}</button>

                        <rs:FileUpload
                            v:if="editable"
                            buttonOnly="true"
                            uploadBtnTxt="{% trans 'Import' %}"
                            name="ubb_file"
                            id="__IMPORT_UBB_BTN__"
                            action="/ubb_scenes/import_ubb_config/"
                            accept=".json"
                            style="vertical-align: top;"
                            onBeforeSubmit="handleBeforeSubmitUbbConfig(event)"
                            onData="handleGetResult2ImportUbbConfig(event)"
                        ></rs:FileUpload>

                    </div>
                </section>

                <section v:show="!isMirrorMode && editable">
                    <header>
                        <span>{% trans 'Auto-Blacklist Clear'%}</span>
                    </header>

                    <ul>
                        <li>
                            <label>IP</label>
                            <input type="text" name="ip_black" placeholder="{% trans 'Clear all '%} : 0.0.0.0" class="txtInput" v:disabled="mPage.isErrorData||!editable" />
                            <button type="button" onclick="UbbRemoveBlackList.handleRemoveBlackIP(event)" v:disabled="mPage.isErrorData||!editable">{% trans 'Remove'%}</button>
                        </li>
                        <li v:show="!isApiProductOrMirrorMode">
                            <label>{% trans 'Fingerprint'%}</label>
                            <input type="text" name="fp_black" placeholder="{% trans 'Clear all '%}  *" class="txtInput" v:disabled="mPage.isErrorData||!editable" />
                            <button type="button" onclick="UbbRemoveBlackList.handleRemoveBlackFP(event)" v:disabled="mPage.isErrorData||!editable">{% trans 'Remove'%}</button>
                        </li>
                        <li>
                            <label>{% trans "Account" %}</label>
                            <input type="text" name="account_black" placeholder="{% trans 'Clear all '%}  *" class="txtInput" v:disabled="mPage.isErrorData||!editable" />
                            <button type="button" onclick="UbbRemoveBlackList.handleRemoveAccount(event)" v:disabled="mPage.isErrorData||!editable">{% trans 'Remove'%}</button>
                        </li>
                        <li>
                            <label>
                                {% trans "Account" %} + Hostname
                                <i class="ic-info" id="__accountHostnameTips__" onmouseover="page.titleTip(event.pageX, event.pageY, '{% trans 'Account should be directly followed by hostname, e.g. adminwww.a.com' %}')" onmouseleave="page.titleTipHide()"></i>
                            </label>
                            <input type="text" name="account_hostname_black" placeholder="{% trans 'Clear all '%}  *" class="txtInput" v:disabled="mPage.isErrorData||!editable" />
                            <button type="button" onclick="UbbRemoveBlackList.handleRemoveAccountHostname(event)" v:disabled="mPage.isErrorData||!editable">{% trans 'Remove'%}</button>
                        </li>
                    </ul>
                </section>

                <section>
                    <header>
                        <span> {% trans 'Share Memory'%} <span class="tip">{% blocktrans with tag1='<span id="shareCount">0</span>' tag2=datas.lua_info.maxShareDict %}(Amount: {{tag1}} / {{tag2}}){% endblocktrans %}</span></span>
                        <p><i class="ic-info" style="margin-right:4px; vertical-align: baseline;"></i>{% trans "Please avoid querying the remaining space during peak business hours" %}</p>
                    </header>

                    <p>
                        <button type="button" onclick="UBB_SHARE.addRecord(event)" class="inlineBlock"
                                v:disabled="mPage.isErrorData||!mPage.enableLuaInterface||(shareTable && shareTable.length >= maxShareDict)"
                                v:if="editable">{% trans 'Add'%}</button>
                        <button type="button" onclick="UBB_SHARE.checkFreeSpace(event)" class="inlineBlock"
                                v:disabled="mPage.isErrorData||!mPage.enableLuaInterface"
                                v:if="global_params.isProxyServer && editable">{% trans 'Check Free Memory'%}</button>
                    </p>
                    <rs:Table name="shareDict"
                            header= "{% trans 'Name'%}|{% trans 'Description'%}|{% trans 'Size'%} (MB)|{% trans 'Last Over-limit Date'%}|{% trans 'Free Memory'%}|{% trans 'Operation'%}"
                            cells="name|description|size|timestamp|free_memory|OPERATE"
                            emptyText="{% trans 'No Data'%}"
                            onCreateHeaderCell="UBB_SHARE.createShareDictHeader(event)"
                            onCreateCell="UBB_SHARE.createShareDictCell(event)">
                    </rs:Table>
                </section>

                <section id="resourceSection" name="resourceSection">
                    <header>
                        <span>
                            {% trans 'Resource File Configuration'%}
                            <span style="font-size:13px;">
                                （ {% trans 'Subtotal'%}：<span id="curCount">0</span> / 200）
                            </span>
                        </span>
                        <p>
                            {% trans 'The name of the uploaded file can only contain letters, numbers, underscores, and decimal points, and the number of characters cannot exceed 64. It only support the UTF-8 encoding format'%}<br/>
                            {% blocktrans with maxCount="200" %}The file cannot be empty, and the size cannot exceed 1MB. A maximum of {{ maxCount }} files can be uploaded.{% endblocktrans %}
                        </p>
                    </header>
                    <div>
                        <rs:FileUpload buttonOnly="true" uploadBtnTxt="{% trans 'Upload list file'%}" name="source_file" id="fileUploadBtn" class="inlineBlock"
                                       v:if="editable"
                                       action="/ubb_scenes/upload_resource_file/"
                                       onData="UBBResourceManager.handleUploadResult(event, this)"
                                       onBeforeSubmit="UBBResourceManager.handleSetFileName(event, 'list')">
                            {% csrf_token %}
                            <div>
                                <input name="file_name" type="hidden" />
                                <input name="type" value="list" type="hidden" />
                                <input name="is_built_in" value="" type="hidden" />
                            </div>
                        </rs:FileUpload>

                        <rs:FileUpload buttonOnly="true" uploadBtnTxt="{% trans 'Upload JS file'%}" name="source_file" id="jsUploadBtn" class="inlineBlock"
                                       v:if="editable"
                                       action="/ubb_scenes/upload_resource_file/"
                                       onData="UBBResourceManager.handleUploadResult(event, this)"
                                       onBeforeSubmit="UBBResourceManager.handleSetFileName(event, 'js')">
                            {% csrf_token %}
                            <div>
                                <input name="file_name" type="hidden" />
                                <input name="type" value="js" type="hidden" />
                            </div>
                        </rs:FileUpload>

                        <rs:FileUpload buttonOnly="true" uploadBtnTxt="{% trans 'Upload HTML file'%}" name="source_file" id="htmlUploadBtn" class="inlineBlock"
                                       v:if="editable"
                                       action="/ubb_scenes/upload_resource_file/"
                                       onData="UBBResourceManager.handleUploadResult(event, this)"
                                       onBeforeSubmit="UBBResourceManager.handleSetFileName(event, 'html')">
                            {% csrf_token %}
                            <div>
                                <input name="file_name" type="hidden" />
                                <input name="type" value="html" type="hidden" />
                            </div>
                        </rs:FileUpload>
                    </div>
                    <rs:Table id="__UBB_RESOURCE_TABLE__" name="ubb_source_list"
                            header= "{% trans 'Name'%}|{% trans 'Type'%}|{% trans 'Upload Date'%}|{% trans 'Size'%}|{% trans 'Operation'%}"
                            cells="file_name|type|time|size|"
                            emptyText="{% trans 'No result' %}"
                            onCreateHeaderCell="UBBResourceManager.handleOnCreateHeader(event)"
                            onCreateCell="UBBResourceManager.handleOnCreateCell(event)"></rs:Table>
                </section>

                <section>
                    <header> {% trans 'Manual Rule Editor'%}</header>
                    <div id="__LUA_SWITCH__" v:if="editable">
                        <label>{% trans 'Enable'%}</label>
                        <rs:Switch name="luaEditSwitch" onChange="LuaEditorModule.saveLuaRuleStatus(event, this.checked)"></rs:Switch>

                        <ul name="luaLogOperator" v:if="global_params.isProxyServer" style="margin:0px">
                            <label>{% trans 'Log'%}:</label>
                            <li style="margin-right:4px">
                                <span><i class="ic-cross-r" style="color:#cb5339; margin-right:4px;"></i><span name="ubbLogErrorCount">0</span></span>
                                <!--span><i class="ic-info" style="vertical-align: unset; margin-right:4px;"></i><span name="logInfoCount">0</span></span-->
                            </li>

                            <li>
                                <button type="button" onclick="LuaLogHandler.showLog(event)">{% trans 'View'%}</button>
                                <button type="button" onclick="LuaLogHandler.refreshLog(event)">{% trans 'Refresh'%}</button>
                                <button type="button" onclick="LuaLogHandler.clearLog(event)">{% trans 'Clear'%}</button>
                            </li>
                        </ul>

                        <div class="inlineBlock" v:if="!global_params.isProxyServer">
                            <label>{% trans 'Log'%}:</label>
                            <i class="ic-info" style="vertical-align:unset; margin-right:4px"></i>{% trans 'Check log only on Proxy Node'%}
                        </div>
                    </div>
                    <div style="display: relative">
                        <rs:TabPanel onSwitch="LuaEditorModule.handleOnSwitchLuaEditorPanel(event)">
                            <div rs:name="{% trans 'Web Manual Rule' %}" class="advRulePane">
                                <textarea name="ruleContent"></textarea>
                            </div>
                            <div rs:name="{% trans 'App Manual Rule' %}"
                                rs:tab-visible="!isApiProductOrMirrorMode && hasMobileLicence()">
                                <textarea name="appRuleContent"></textarea>
                            </div>
                            <div rs:name="{% trans 'Mpp Manual Rule' %}"
                                rs:tab-visible="({% visibleLayout 'wechat_mpp' %} || {% visibleLayout 'alipay_mpp' %} || {% visibleLayout 'mpaas_mpp' %}) && !matchDeployMode('mirror') && hasMppLicence()">
                                <textarea name="mppRuleContent"></textarea>
                            </div>
                        </rs:TabPanel>
                        <div name="luaLog">
                            <i class="ic-cross" onclick="LuaLogHandler.closeLog(event)"></i>
                            <div name="content"></div>
                        </div>
                    </div>
                    <p class="buttonPane"><button type="button" onclick="LuaEditorModule.saveLuaRuleContent(event)"
                                                  v:disabled="mPage.isErrorData"
                                                  v:if="editable" important operation>{% trans 'Save'%}</button></p>
                </section>
            </div>

        </rs:TabPanel>
    </rs:Form>

    <rs:ModalDialog id="__RULE_DIALOG__" name="editDialog"
                onOK="handleOnSaveRule(event)"
                onCancel="closeRuleEditor()">
        <form autocomplete="off">
            <div style="margin-left:20px; margin-top:10px;">
                {% trans 'Strategy Name'%}<input type="text" maxlength="20" name="desc" placeholder="{% trans 'Maximum length'%}: 20" />
            </div>
        <rs:TabPanel onSwitch="handleOnRuleDialogTabChange(event)">
            <div rs:name="{% trans 'Rule'%}" rsid="ubb_rule" rs:onShow="checkKeyAndCounterInDialog()">
                <div name="conditionPane">
                    <p>{% trans 'Condition' %}</p>
                    <table>
                        <tr>
                            <td>
                                <label>{% trans 'Host'%}</label>
                                <div>
                                    <rs:Select name="host" isPositionAdapt="false" id="upstreamList" editable="true" onChange="handleOnHostSelectorChanged(event)" host>
                                    </rs:Select>
                                </div>
                                <rs:label style="cursor: pointer;"><rs:checkbox name="is_host_regx" rsname="is_host_regx" onchange="hostRegxChange(event)"></rs:checkbox>{% trans 'Regex'%}</rs:label>

                            </td>
                        </tr>
                        <tr>
                            <td id="__PATH__">
                                <label rs:visible="!/^(SNIPING)$/i.test(mPage.ruleType)">{% trans "Path" %}</label>
                                <label rs:visible="/^(SNIPING)$/i.test(mPage.ruleType)">{% trans 'Sniping Path'%}</label>
                                <input type="text" name="path" class="txtInput" maxlength="50" />
                                <rs:label style="cursor: pointer;"><rs:checkbox name="is_path_regx"></rs:checkbox>{% trans 'Regex'%}</rs:label>
                            </td>
                        </tr>
                        <tr rs:visible="/^(APP_BLACK|BLACK|HIGH_FREQ|APP_HIGH_FREQ|COUNTER|APP_COUNTER)$/i.test(mPage.ruleType)">
                            <td>
                                <label>Key</label>
                                <div style="display:inline-block;">
                                    <rs:Select name="key" class="keySelect" onChange="changeKeyEvt(event)" key>
                                        <div value="ip" rs-option>IP</div>
                                        <div value="ip_c" rs-option>{% trans 'IP (Class C)'%}</div>
                                        <div value="app_fp" rs-option>{% trans 'App Fingerprint'%}</div>
                                        <div value="fp" rs-option>{% trans 'Fingerprint'%}</div>
                                        <div value="ua" rs-option>UA</div>
                                        <div value="cookie_id" rs-option>Cookie ID</div>
                                        <div value="combination" rs-option>{% trans 'Combine fields'%}</div>
                                    </rs:Select>

                                    <rs:MultiSelect id="fieldList"
                                                    supportSelectAll="false"
                                                    needFilter="true"
                                                    maxSelectCount="10"
                                                    onHoverItem="handleHoverFieldItem(event)"
                                                    onLeaveItem="handleLeaveFieldItem(event)"
                                                    onCheckItem="handleCheckFieldItemByKeyCombination(event)"
                                                    onCreateItem="handleCreateFieldList(event)"></rs:MultiSelect>
                                </div>
                            </td>
                        </tr>


                        <tr rs:visible="/^(APP_REPUTATION)$/i.test(mPage.ruleType)">
                            <td>
                                <label>Key</label>
                                <div style="display:inline-block;">
                                    <rs:Select name="app_reputation_key" onChange="changeSoftKeyEvt(event)" softkey>
                                        <div value="ip" rs-option>IP</div>
                                        <div value="app_fp" rs-option>{% trans 'App Fingerprint'%}</div>
                                        <div value="user_name" rs-option>{% trans 'Account'%}</div>
                                    </rs:Select>
                                </div>
                            </td>
                        </tr>


                        <tr rs:visible="mPage.ruleType=='NEW_COMER'">
                            <td>
                                <label>Key</label>
                                <div>
                                    <rs:Select name="newUserKey" id="newUserKey">
                                        <div value="ip" rs-option>IP</div>
                                        <div value="fp" rs-option rs:visible="!isApiProductOrMirrorMode">{% trans 'Fingerprint'%}</div>
                                        <div value="cookie_id" rs-option rs:visible="!isApiProductOrMirrorMode">Cookie ID</div>
                                        <div value="user_name" rs-option>{% trans 'Account'%}</div>
                                    </rs:Select>
                                </div>
                            </td>
                        </tr>
                        <tr rs:visible="mPage.ruleType=='NEW_COMER'">
                            <td>
                                <label>{% trans 'Expire Date'%}</label>
                                <input name="hold_time" type="text" class="txtInput" placeholder="1 ~ 30" short/> &nbsp;{% trans 'Days'%}
                            </td>
                        </tr>


                        <tr rs:visible="mPage.ruleType=='REPUTATION'">
                            <td>
                                <label>Key</label>
                                <div id="__SOFT_RULE_KEY_LIST__">
                                    <rs:Select name="softkey" onChange="changeSoftKeyEvt(event)" softkey>
                                        <div value="ip" rs-option>IP</div>
                                        <div value="fp" rs-option rs:visible="!isApiProductOrMirrorMode">{% trans 'Fingerprint'%}</div>
                                        <div value="user_name" rs-option>{% trans 'Account'%}</div>
                                    </rs:Select>
                                </div>
                            </td>
                        </tr>

                        <tr rs:visible="/^(REPUTATION|APP_REPUTATION)$/i.test(mPage.ruleType)">
                            <td>
                                <label>{% trans 'Threat Type'%}</label>
                                <span name="soft_block_type_box"></span>
                            </td>
                        </tr>

                        <tr name="soft_block_default">
                            <td>
                                <label>{% trans 'Threat Value'%}</label>
                                <span>
                                    <input name="threat_value_min" placeholder="0 ~ 100" type="text" short>
                                    <span style="width: 30px; display: inline-block; text-align: center;">-</span>
                                    <input name="threat_value_max" placeholder="0 ~ 100" type="text" short>
                                </span>
                            </td>
                        </tr>


                        <tr rs:visible="/^(BLACK|APP_BLACK)$/i.test(mPage.ruleType)">
                            <td id="__KEY_VALUE_ROW__">
                                <label>Value</label>
                                <input type="text" name="key_value" class="txtInput" maxlength="100000" placeholder="{% trans 'Separate multiple value with commas'%}" />
                                <rs:label style="cursor: pointer;"><rs:checkbox name="is_key_regx"></rs:checkbox>{% trans 'Regex'%}</rs:label>
                            </td>
                        </tr>


                        <tr rs:visible="/^(HIGH_FREQ|APP_HIGH_FREQ)$/i.test(mPage.ruleType)">
                            <td>
                                <label>{% trans 'limit switch'%}</label>
                                <span>
                                    <rs:Label><rs:Radio name="freq_limit_type" value="rate" onchange="handleChangeLimitType(event)"></rs:Radio>{% trans 'Speed'%}</rs:Label>
                                    <rs:Label class="freqTypeRadioLabel"><rs:Radio name="freq_limit_type" value="time_window" onchange="handleChangeLimitType(event)"></rs:Radio>{% trans 'Time Window'%}</rs:Label>
                                </span>
                            </td>
                        </tr>
                        <tr rs:visible="/^(HIGH_FREQ|APP_HIGH_FREQ)$/i.test(mPage.ruleType)">
                            <td>
                                <label>{% trans "Max. Rate" %}</label>
                                <span name="ratePane">
                                    <input type="text" name="high_freq" class="txtInput numberInput" short placeholder="0~65535" title="{% trans 'Minimum'%}: 0.001" /> {% trans 'Times per Second'%},
                                &nbsp;&nbsp;{% trans 'Burst'%} <input type="text" name="burst" placeholder="0~65535" class="txtInput numberInput" short/>
                                </span>

                                <div name="timePane">
                                    <rs:Select name="time_window_type">
                                        <div rs-option value="second">{% trans 'Per Second'%}</div>
                                        <div rs-option value="minute">{% trans 'Per Minute'%}</div>
                                        <div rs-option value="hour">{% trans 'Per Hour'%}</div>
                                        <div rs-option value="day">{% trans 'Per Day'%}</div>
                                    </rs:Select>
                                    <input name="time_window_value" placeholder="1~65535" type="text" />{% trans 'Time(s)'%}
                                </div>
                            </td>
                        </tr>
                        <tr rs:visible="mPage.ruleType=='BAD_BEHAVIOR'">
                            <td>
                                <label>{% trans "Min. Event Count" %}</label>
                                <span><input type="text" name="min_event" class="txtInput numberInput" placeholder="{% trans 'Integer:' %}1~1000000" /> {% trans "Time(s)" %}</span>
                            </td>
                        </tr>
                        <tr id="__DEV_TYPE_ROW__" rs:visible="hasDeviceType(mPage.ruleType)">
                            <td>
                                <label>{% trans 'Device Type'%}</label>
                                <div id="__DEV_TYPE__">
                                    <input type="hidden" name="dev_type" />
                                </div>
                            </td>
                        </tr>

                        <tr id="__OPERATE_BEHAVIOR_ROW__" rs:visible="mPage.ruleType=='BAD_BEHAVIOR'">
                            <td>
                                <label>{% trans "Behavior" %}</label>
                                <div id="__OPERATE_BEHAVIOR__">
                                    <input type="hidden" name="operate_type" />
                                </div>
                            </td>
                        </tr>

                        <!-- 秒杀活动 -->
                        <tr rs:visible="mPage.ruleType=='SNIPING'">
                            <td>
                                <label>{% trans 'Activity Start Time'%}</label>
                                <rs:Time name="start_time"></rs:Time>
                            </td>
                        </tr>
                        <tr rs:visible="mPage.ruleType=='SNIPING'">
                            <td>
                                <label>{% trans 'Activity Duration'%}</label>
                                <input name="duration" type="text" placeholder="0-65535" short /> {% trans 'Second'%}
                            </td>
                        </tr>

                        <!-- 计数器 -->
                        <tr rs:visible="/^(COUNTER|APP_COUNTER)$/i.test(mPage.ruleType)">
                            <td>
                                <label>{% trans 'Counter' %}</label>
                                <rs:Select name="counter" rsname="counter" onChange="checkKeyAndCounterInDialog()"></rs:Select>
                                <span name="counterWarning">
                                    <i class="ic-warning"></i> {% trans 'The current rule will not take effect: Key + counter combination needs to be configured in any rule' %}
                                </span>
                            </td>
                        </tr>
                        <tr rs:visible="/^(COUNTER|APP_COUNTER)$/i.test(mPage.ruleType)">
                            <td>
                                <label>{% trans 'Requirement' %}</label>
                                <rs:Select name="counter_comparison" rsname="counter_comparison">
                                    <div rs-option value=">">{% trans 'greater than ' %}</div>
                                    <div rs-option value="<">{% trans "less than" %}</div>
                                    <div rs-option value="==">{% trans "equal to" %}</div>
                                </rs:Select>
                                <input type="text" name="counter_threshold" placeholder="0 ~ 65535" />
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 统计条件: 秒杀活动 -->
                <div name="statistics" rs:visible="mPage.ruleType=='SNIPING'">
                    <p>{% trans 'Statistic Policy'%}</p>
                    <table>
                        <tr>
                            <td>
                                <label>{% trans 'Sniping Type'%}</label>
                                <div class="exceptionItems" style="display:inline-block">
                                    <rs:Label><rs:Radio name="exception_type" value="prepare" onchange="handleClickExceptionType(event)"></rs:Radio>{% trans 'Advance sniping'%}</rs:Label>
                                    <rs:Label><rs:Radio name="exception_type" value="ontime" onchange="handleClickExceptionType(event)"></rs:Radio>{% trans 'No referer sniping'%}</rs:Label>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label>Key</label>
                                <rs:Select name="sniping_key" id="snipingKey">
                                    <div value="ip" rs-option>IP</div>
                                    <div value="fp" rs-option rs:visible="!isApiProductOrMirrorMode">{% trans 'Fingerprint'%}</div>
                                    <div value="cookie_id" rs-option rs:visible="!isApiProductOrMirrorMode">Cookie ID</div>
                                    <div value="user_name" rs-option>{% trans 'Account'%}</div>
                                </rs:Select>
                            </td>
                        </tr>
                        <tr name="prePathPane">
                            <td>
                                <label>{% trans 'Referer Path'%}</label>
                                <input name="prepare_path" type="text" class="txtInput" placeholder="{% trans 'Refer path of the sniping activity'%}" />
                                <rs:Label><rs:Checkbox name="is_prepare_path_regx"></rs:Checkbox>{% trans 'Regex'%}</rs:Label>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label>{% trans 'Description'%}</label>
                                <div class="des">
                                    <span name="des1">{% trans 'Within 10 minutes before the start of the activity, the key values of all requests that access the sniping path in advance will be recorded; when the activity has started, the following actions will be applied on requests whose key values had been recorded.'%}</span>
                                    <span name="des2">{% trans 'When the activity has started, the following actions will be applied on requests that have not visited the referer path.'%}</span>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- 执行策略 -->
                <div name="policy" rs:visible="!isMirrorMode">
                    <p>{% trans "Policy" %}</p>
                    <table>
                        <tr>
                            <td>
                                <label policy>{% trans "Action Rate" %}</label>
                                <div>
                                    <rs:Select name="percent" onChange="changePercentEvent(event)">
                                        <div value="100" rs-option>100%</div>
                                        <div value="90" rs-option>90%</div>
                                        <div value="80" rs-option>80%</div>
                                        <div value="70" rs-option>70%</div>
                                        <div value="60" rs-option>60%</div>
                                        <div value="50" rs-option>50%</div>
                                        <div value="40" rs-option>40%</div>
                                        <div value="30" rs-option>30%</div>
                                        <div value="20" rs-option>20%</div>
                                        <div value="10" rs-option>10%</div>
                                    </rs:Select>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="policyCell">
                                <rs:PolicyBox name="action_list" addButton="#__ADD_POLICY__" id="TEST">
                                    <div>
                                        <label>{% trans 'Action '%}<span name="index"></span><i class="ic-help" onmouseover="handleShowActionTips(event)" onmouseout="handleHideActionTips(event)"></i></label>
                                        <rs:Select name="action" action></rs:Select>
                                        <span name="insertJSBox"></span>
                                        <span name="responseHTMLBox"></span>
                                        <span name="blockValueBox" class="policyItem"><label>{% trans 'Status Code'%}</label><input name="block_value" placeholder="200~600" type="text" number></span>
                                        <span name="redirectValueBox" class="policyItem"><label>{% trans 'Redirect Path'%}</label><input name="redirect_value" type="text" maxlength="50"></span>
                                        <span name="forwardValueBox">
                                            <rs:Select name="forward_protocol" forwardProtocol></rs:Select>
                                            <input name="forward_upstream_name" type="text" maxlength="128" placeholder="{% trans 'IPv4/IPv6/Domain' %}">
                                            <input name="forward_port" type="text" placeholder="{% trans 'Port' %}">
                                        </span>
                                        <span name="challengeBox" class="policyItem"><label><a title="{% trans 'Multiple template IDS can be configured, separated by commas'%}" name="templateLink"
                                                                            target="_blank"
                                                                            href="/system/labs/captcha/?operate=detail"
                                                                            onclick="return templateIntroductionOnclick();">{% trans 'Template'%} ID</a></label>
                                                                            <input name="challenge_value" maxlength="100"
                                                                                            placeholder="{% trans 'Default is random selection ID'%}"
                                                                                            type="text"
                                                                                            title="{% trans 'Default is random selection ID'%}"
                                                                                            number/></span>
                                        <span name="blankArea">--</span>
                                        <span class="policyItem" name="delayPolicyItem">
                                            <label>{% trans 'Delay'%}</label>
                                            <input
                                                name="delay"
                                                placeholder="0~60"
                                                type="text"
                                                number
                                                nospace
                                                onchange="this.value = convertDelayValue(this.value)"
                                            >{% trans 'Second'%}
                                        </span>
                                        <span style="display:none;" class="policyItem"><label space>{% trans 'Weight'%}</label><input name="weight" placeholder="1~10" type="text" number></span>
                                        <i name="remove" class="fa fa-times" aria-hidden="true" title="{% trans 'Remove rule'%}"></i>
                                </div>
                                </rs:PolicyBox>
                                <div id="__ADD_POLICY__"
                                     class="addObjectItemButton"
                                     title="{% trans 'Add Action'%}"><i class="fa fa-plus" aria-hidden="true"></i></div>
                            </td>
                        </tr>
                    </table>

                    <div id="__AUTO_LIST_ROW__" rs:visible="!isMirrorMode">
                        <p>{% trans 'Auto-Blacklist'%}</p>
                        <table>
                            <tr>
                                <td>
                                    <label>{% trans 'Continuous Blocking'%}<i class="ic-help" onmouseover="handleShowAutoBlackTips(event)" onmouseout="handleHideAutoBlackTips(event)"></i></label>
                                    <rs:Select name="auto_list_type" id="__AUTO_LIST_TYPE__" onChange="changeAutoBlackListEvt(event)">
                                        <div rs-option value="ip">IP</div>
                                        <div rs-option value="ip_c">{% trans 'IP (Class C)'%}</div>
                                        <div rs-option value="fp" rs:visible="!isApiProductOrMirrorMode && mPage.ruleType!='APP_BLACK' && mPage.ruleType!='APP_HIGH_FREQ' && mPage.ruleType!='APP_REPUTATION' && mPage.ruleType!='APP_COUNTER'">{% trans 'Fingerprint'%}</div>
                                        <div rs-option value="app_fp" rs:visible="!isApiProductOrMirrorMode && mPage.ruleType=='APP_BLACK' || mPage.ruleType=='APP_HIGH_FREQ' || mPage.ruleType=='APP_REPUTATION' ||  mPage.ruleType=='APP_COUNTER'">{% trans 'App Fingerprint'%}</div>
                                        <div rs-option value="account">{% trans 'Account' %}</div>
                                        <div rs-option value="account_hostname">{% trans 'Account' %} + Hostname</div>
                                    </rs:Select>
                                    <span><input type="text" name="auto_list" value="0" class="txtInput numberInput" placeholder="{% trans 'Integer:' %}0~********" style="width: 130px" /> {% trans 'second(s)' %}</span>
                                </td>
                            </tr>
                        </table>
                    </div>

                </div>
            </div>

            <div rs:name="{% trans 'Filter'%} (0)" rs:tab-attr="name=filterTab" rs:onShow="handleInitFilterEditor(event)">
                <rs:ConvertEditorButton name="is_advfilter">{% trans 'Use advanced filter'%}</rs:ConvertEditorButton>
                <div class="filterPageContent">
                    <rs:FilterSelector>
                        <input type="text" name="keyword" maxlength="20" placeholder="{% trans 'Filter '%}"/>
                        <span class="ic-search"></span>
                        <rs:List name="filterTempList">
                            <div item="true"><span name="icon"></span><span name="display"></span></div>
                        </rs:List>
                    </rs:FilterSelector>
                    <rs:FilterList name="filter_conditions" active>
                        <rs:List name="innerFilterList">
                            <div item="true">
                                <span name="icon"></span><span name="filterKey"></span>
                                <div class="fa fa-times" title="{% trans 'Clear'%}"></div>
                            </div>
                        </rs:List>
                    </rs:FilterList>

                    <div name="advFilterPane">
                        <div name="editorPane">
                            <div>
                                <span>{% trans 'Advanced expression'%} <span style="color:gray;font-size:12px;">（<span name="charCount">0</span> / 2000）</span></span>
                                <rs:ToggleButton id="__NOTICE_BUTTON__">{% trans 'Help'%}</rs:ToggleButton>
                            </div>
                            <rs:FilterEditor name="filterEditor"></rs:FilterEditor>
                        </div>
                        <div name="resultPane">
                            <button type="button" onclick="handleRunExp(event)">{% trans 'Trial-run Expression'%}</button>
                            <div name="filter_conditions_lua"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div rs:name="{% trans 'Counter' %}" rsid="ubb_counter">
                <div class="ubbCounterPane">
                    <rs:UBBCounter name="statistic_counter" addButton="#addCounter">
                        <div class="ubbCounterItem">
                            <table>
                                <tbody>
                                    <tr>
                                        <td>
                                            <label>Key</label>
                                            <rs:Select name="counter_key" rsname="counter_key"></rs:Select>
                                            <rs:MultiSelect name="counterFieldList" rsname="counterFieldList"
                                                            supportSelectAll="false"
                                                            needFilter="true"
                                                            maxSelectCount="10"
                                                            onHoverItem="handleHoverFieldItem(event)"
                                                            onLeaveItem="handleLeaveFieldItem(event)"
                                                            onCreateItem="handleCreateFieldList(event)"></rs:MultiSelect>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label>{% trans 'Counter Name' %}</label>
                                            <rs:Select id="counterSelect" name="counter_id" rsname="counter_id"></rs:Select>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <label>{% trans 'Actions' %}</label>
                                            <rs:Select id="counterOperation" name="counter_operation" rsname="counter_operation"></rs:Select>
                                            <input name="counter_increment" type="text" placeholder="0 ~ 65535" />
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <i name="remove" class="fa fa-times"></i>
                        </div>
                    </rs:UBBCounter>
                    <div class="addCounterItemBtnPane">
                        <div id="addCounter" class="button">
                            <i class="fa fa-plus" aria-hidden="true"></i>{% trans 'Add Counter' %}
                        </div>
                    </div>
                </div>
            </div>
        </rs:TabPanel>
        </form>
    </rs:ModalDialog>

    <rs:ModalDialog id="__SHARE_EDIT_BOX__" name="shareEditBox" header="{% trans 'Add Share Memory' %}"
            onOK="UBB_SHARE.saveShareData(event)"
            onCancel="UBB_SHARE.closeShareDialog(event)">
        <div name="content">
            <p>{% trans "NOTE: 1MB shared memory can store about 8000 statistics. When it is exceeded, the oldest data (LRU algorithm) will be deleted." %}</p>
            <table>
                <tr><td rs-required>{% trans 'Item Name' %}</td><td><input type="text" name="name" class="txtInput" maxlength="64"></td></tr>
                <tr><td rs-required>{% trans 'Size' %}</td><td><input type="text" name="size" class="txtInput numberInput" placeholder="{% trans 'Integer:' %}1~1024" /> MB</td></tr>
                <tr><td>{% trans 'Description' %}</td><td><input type="text" name="description" class="txtInput" maxlength="100" placeholder="{% trans 'Maximum length'%}: 100"/></td></tr>
            </table>
        </div>
    </rs:ModalDialog>

    <rs:Template id="__FILTER_DOC__">
        <div name="doc">
            <h2>{% trans 'Introduction of advanced filter and syntax description'%}</h2>
            <h3>{% trans 'Function tips'%}:</h3>
           1. {% trans 'The relationship between the filter and the rule triggers is And, which means both of the two conditions must be met to trigger an execution strategy.'%}<br/><br/>
           2. {% trans 'After entering the advanced filter from the filter interface, you cannot return to the filter. If you need to go back to filter, you need to click the Cancel button and re-enter the previous interface.'%}<br/><br/>
           3. {% trans 'You can enter up to 2000 characters in the advanced expression input box.'%}<br/><br/>
           4. {% trans 'Fields that appear blank or do not exist in the statistics system have a default value. When you need to use a filter to match these fields, you should set these fields to the default values for successful matching. Default values of different types of fields are listed below to ensure proper configuration in this scenario.'%}
            <ul>
                <li>
                    <span>{% trans 'Type'%}</span>
                    <span>{% trans 'Default Value'%}</span>
                </li>
                <li>
                    <span>IP / String</span>
                    <span>nil</span>
                </li>
                <li>
                    <span>Number</span>
                    <span>-1</span>
                </li>
                <li>
                    <span>Bool</span>
                    <span>false</span>
                </li>
            </ul>
            <br/><br/>

            <h3 style="color:#ec8d00">{% trans 'Syntax tips'%}:</h3>
            1. {% trans 'Use relational operators including and, or, not, and parentheses (). Parentheses can be used in a nested structure.'%}<br/><br/>
            2. {% trans 'When adding a field name, you can directly click in the field list on the left to automatically add fields to the expression in the advanced filter, or you can manually type it.'%}<br/><br/>
            3. {% trans 'The field name should be to the left of the operator, and the field value should be to the right of the operator. For example, input_all_count >= 10.'%}<br/><br/>
            4. {% trans 'When functions are used, the system only supports string fields. The field should be entered as the first parameter, for example: start_with(path, "/login")，empty(path). The following list shows the meaning of available functions'%}<br/>
            <ul>
                <li>
                    <span>{% trans 'Function'%}</span>
                    <span>{% trans 'implication'%}</span>
                </li>
                <li>
                    <span>start_with</span>
                    <span>{% trans 'Start with(no regular expression)'%}</span>
                </li>
                <li>
                    <span>end_with</span>
                    <span>{% trans 'End with(no regular expression)'%}</span>
                </li>
                <li>
                    <span>include</span>
                    <span>{% trans 'Include(no regular expression)'%}</span>
                </li>
                <li>
                    <span>regular_exp</span>
                    <span>{% trans 'Regex'%}</span>
                </li>
                <li>
                    <span>empty</span>
                    <span>{% trans 'Empty'%}</span>
                </li>
                <li>
                    <span>exclude</span>
                    <span>{% trans 'Exclude(no regular expression)'%}</span>
                </li>
                <li>
                    <span>full_match</span>
                    <span>{% trans 'Full Match'%}</span>
                </li>
            </ul>
            <br/><br/>

            5.{% trans 'When the following functions are used, the system only supports ip or string fields. The field should be entered as the first parameter and the file name as the second parameter. For example: in_list(src_ip, "ips.txt")，not_in_list(args, "args_list"). Attention: they do not support regular expression. The following list shows the meaning of available functions.'%}<br/>
            <ul>
                <li>
                    <span>{% trans 'Function'%}</span>
                    <span>{% trans 'Description'%}</span>
                </li>
                <li>
                    <span>in_list</span>
                    <span>{% trans 'In the List'%}</span>
                </li>
                <li>
                    <span>not_in_list</span>
                    <span>{% trans 'Not in the List'%}</span>
                </li>
            </ul>
            <br/><br/>

            6. {% trans 'The following list shows the operators supported by each type of fields.'%}<br/>
            <ul>
                <li>
                    <span>{% trans 'Field type'%}</span>
                    <span>{% trans 'Operator'%}</span>
                </li>
                <li>
                    <span>IP</span>
                    <span>==、!=<br />{% trans 'Example'%}：src_ip == "***********/24"</span>
                </li>
                <li>
                    <span>Boolean</span>
                    <span>==、!=<br />{% trans 'Example'%}：is_ajax == false</span>
                </li>
                <li>
                    <span>{% trans 'Integer'%}</span>
                    <span>==、!=、>、<、>=、<=<br />{% trans 'Example'%} input_key_count >= 10</span>
                </li>
                <li>
                    <span>{% trans 'String'%}</span>
                    <span>==、!=<br />{% trans 'Example'%}：args == "abc"</span>
                </li>
                <li>
                    <span>{% trans 'Enumeration'%}</span>
                    <span>==、!=<br />{% trans 'Example'%}：action == "POST, PUT"</span>
                </li>
            </ul>

            <br/><br/>

            7. {% trans 'Current regular expression library is similar to PCRE and does not support expressions to match Chinese.'%}<br/>


            <h3>{% trans 'Example'%}</h3>
            <div style="word-break: break-word;">
                (path!="/loginras/" and start_with(path,"/login") and not (input_mouse_move_count <= 2 or time_on_page < 1) and not empty(path) and is_ajax == false) or (local_ip == "**********/15,**********" and action != "POST, PUT" and battery_is_charging != true)
            </div>
        </div>
    </rs:Template>

{% endblock %}

{% block script %}
    <script language="JavaScript" type="text/javascript">
        var isApiProductOrMirrorMode = isApiProduct(global_params.productType)||matchDeployMode('mirror');
        var isMirrorMode =  matchDeployMode('mirror');
        var ApiProductAdapter = (function() {
            return {
                adaptInApiProduct: adaptInApiProduct,
                getKeyMap: getKeyMap
            };

            function adaptInApiProduct(rowData) {
                // 原旁路模式本来就没有开放UBB功能, 因此无兼容处理row.action_list老数据的需求
                if (isApiProductOrMirrorMode) {
                    if (/^(app_fp|fp|cookie_id)$/.test(rowData.key)) {
                        rowData.key = '';
                    }

                    if (/^(fp)$/.test(rowData.auto_list_type)) {
                        rowData.auto_list_type = '';
                    }

                    if (rowData.statistic_counter && Array.isArray(rowData.statistic_counter)) {
                        rowData.statistic_counter.forEach(function(counter) {
                            if (/^(fp|cookie_id)$/.test(counter.counter_key)) {
                                counter.counter_key = '';
                            }
                        });
                    }
                }
            }

            function getKeyMap() {
                return {
                    BLACK: ['ip', 'ua'],
                    APP_BLACK: ['ip', 'ua'],
                    HIGH_FREQ: ['ip', 'ip_c', 'combination'],
                    APP_HIGH_FREQ: ['ip', 'ip_c', 'combination'],
                    COUNTER: ['ip', 'ip_c', 'combination'],
                    APP_COUNTER: ['ip', 'ip_c', 'combination']
                };
            }

        })();

        {% autoescape off %}
        var allData = {{ datas|to_json }};
        var isErrorData = allData == null;
        if (isErrorData) allData = {};

        var currentTimeFromServer = allData.current_time;
        var lua_info = allData.lua_info || {};
        var maxShareDict = lua_info.maxShareDict;
        var conf = allData.captcha_info || {};
        var tempInfo = conf.templateInfo || {};
        var policy = conf.policy || {};
        var shieldList = allData.websiteShield;
        var ubb_source_list = allData.source_files || [];

        var upstreamConfList = convertUpstreamList(allData.upstream_conf_list);
        function convertUpstreamList(upstreamList) {
            var arr = [];
            var keys = {};
            if (!upstreamList || upstreamList.length == 0) return [];
            for (var i = 0; i < upstreamList.length; i++) {
                var upstream = upstreamList[i];
                if (!upstream) continue;

                var key = upstream['ServerName'];
                var name = key;
                var type = upstream['ServerNameType'];
                if (type === 'IPv6') {
                    name = '[' + key + ']';
                }

                // same Domain/IP, different port, just show one item since don't show the port
                // port should filter by rule
                if (keys[key]) {
                    continue;
                } else {
                    keys[key] = 1;
                }

                arr.push({key: key, value: name, type: type});
            }
            return arr;
        }


        {% endautoescape off %}

        var ACTION_TRANS_MAP = {
            block_code: { key: 'block_code', trans:  "{% trans 'Block'%}" },
            redirect: { key: 'redirect', trans: "{% trans 'Redirect'%}"},
            forward: { key: 'forward', trans: "{% trans 'Forward'%}"},
            pass: { key: 'pass', trans:  "{% trans 'Pass'%}" },
            transpare: { key: 'transpare', trans:  "{% trans 'Transparent'%}" },
            challenge: { key:'challenge', trans: "{% trans 'Challenge'%}" },
            insertjs: { key: 'insertjs', trans: "{% trans 'InsertJS'%}" },
            response_html: { key: 'response_html', trans: "{% trans 'return html page'%}" },
            //skip ubb check
            none: { key: 'no_action', trans: "{% trans 'None'%}" },
        };

        var InitControlManage = {
            initSelect: function(option, attr) {
                var map = option.map;
                if (!map) return;

                var arr = [];
                var keys = Object.keys(map);
                if (option.isRverseSort) keys.reverse();
                for (var i=0;i<keys.length;i++) {
                    var key = keys[i];
                    if (!key) continue;
                    var o = map[key];
                    if (!o) continue;
                    arr.push({ key:o.key, value:getTransContent(map, keys[i]) });
                }

                var select = createSelect({name:option.name, editable:false}, option.initValue||arr[0].key, arr, attr);
                if (option.changeEvt) select.bind('change', option.changeEvt);
                option.wrapper.append(select);
                return select;
            }
        };

        var VALIDATE_RULE_DATA = {
            PATH: function(val, fieldName, maxLen) {
                var pathError = VALIDATE_RULE_DATA.IS_EMPTY(val, fieldName) || VALIDATE_RULE_DATA.LIMIT_CHAR_LEN(val, fieldName, maxLen);
                if (pathError) return pathError;
                if (/\s+/g.test(val)) return rs.formatString('{% trans "[ {0} ] cannot contain spaces" %}', fieldName);
            },

            NUMBER: function(val, fieldName, range, isInt) {
                if (typeof val=='undefined' || val==null || (typeof val=='string'&&val.trim()=='')) return rs.formatString('{% trans "[ {0} ] cannot be blank" %}', fieldName);
                var msg = rs.formatString('{% trans "[ {0} ] Please enter a number greater than or equal to {1} and less than or equal to {2}" %}', fieldName, range.MIN, range.MAX);
                if (isInt) msg = rs.formatString('{% trans "[ {0} ] Please enter a integral number greater than or equal to {1} and less than or equal to {2}" %}', fieldName, range.MIN, range.MAX);
                if (isNaN(val)) return msg;
                var nval = Number(val);
                if (nval>=range.MIN && nval<=range.MAX) {
                    if (isInt && nval!=range.MIN && Validation.POSITIVE_INT(nval)) return msg;
                } else {
                    return msg;
                }
            },

            IS_EMPTY: function(val, fieldName) {
                if (Validation.STRING(val)) {
                    return rs.formatString('{% trans "[ {0} ] cannot be blank" %}', fieldName || '');
                }
            },

            LIMIT_CHAR_LEN: function(val, fieldName, maxLen) {
                if (val.len() > maxLen) {
                    return rs.formatString('{% trans "[ {0} ] should not exceed {1} characters." %}', fieldName, maxLen);
                }
            },

            TEMPLATE_ID: function(value, fieldName){
                if(value.trim()==''){
                    value = '';
                    return;
                }
                var idList = value.split(',');
                var invalidId = [];
                var noExistId = [];

                rs.each(idList, function(index, id){
                    if(id.trim()=='' || !isNatureNumber(id)){
                        invalidId.push(id);
                    }else{
                        if(mPage.templateList.indexOf(id)==-1) noExistId.push(id);
                    }
                });

                if(invalidId.length) return rs.formatString('{% trans "Invalid template ID: [{0}]"%}', invalidId.join(', '));
                if(noExistId.length) return rs.formatString('{% trans "Template does not exist: [{0}]"%}', noExistId.join(', '));

                function isNatureNumber(num){
                    return !Validation.POSITIVE_INT(num);// && num>0
                }
            }
        };

        var mPage = {
            isErrorData: isErrorData,
            ruleType: '',
            action: ''
        };

        var ruleEditor, clockDialog, devTypeWrapper, noticeButton;
        var editorLock = false;
        var isPageReady = false;

        function initPage() {
            rs('body').attr('page_custom_made', g_const.page_custom_made || null);

            isPageReady = true;

            ruleEditor = rs('#__RULE_DIALOG__');
            clockDialog = rs('#__CLOCK_DIALOG__');

            noticeButton = rs('#__NOTICE_BUTTON__');
            noticeButton.state = false;
            noticeButton.bind('toggle', handleToggleHelperDoc);

            initDialog();
            registCommonEvent();
            compatibalIE();
            addRSID2Tabs();

            if (location.search.indexOf('?tab=') > -1) {
                if (getUrlParams('tab') == 'advanced') rs('#__TAB_PANEL__').index = 4;
                if (location.hash) {
                    var a = rs.Element('a').attr({ href: location.hash });
                    a.click();
                    a.remove();
                }
            }

            if (mPage.isErrorData) {
                rsalert('{% trans "The server returned data is incomplete and may cause the page operation to not take effect. Please try this feature later." %}');
            }

            function getUrlParams(name) {
                var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                var r = window.location.search.substr(1).match(reg);
                if (r != null) return unescape(r[2]);
                return null;
            }

            function registCommonEvent() {
                rs('.txtInput').bind('change', function(evt) {
                    evt.stopPropagation();
                    evt.currentTarget.value = evt.currentTarget.value.trim();
                });

                rs('input.numberInput').bind('change', function(evt) {
                    evt.stopPropagation();
                    var val = evt.currentTarget.value;
                    if (isEmptyValue(val)) return;

                    var convertArr = [];
                    var arr = val.split(',');
                    for (var i = 0; i < arr.length; i++) {
                        var v = arr[i];
                        if (isEmptyValue(v) || isNaN(v)) {
                            convertArr.push(v);
                        } else {
                            convertArr.push(Number(v));
                        }
                    }

                    evt.currentTarget.value = convertArr.join(',');
                });
            }

            function compatibalIE() {
                if(rs.browser.type=='IE' && rs.browser.version<=11){
                    ruleEditor.named('threat_value_min').bind('change input', preventActionWhenEmpty);
                    ruleEditor.named('threat_value_max').bind('change input', preventActionWhenEmpty);

                    function preventActionWhenEmpty(evt){
                        if(evt.target.value=='') {
                            evt.stopPropagation();
                        }
                    }
                }
            }

            function addRSID2Tabs() {
                var tabboxes = rs('#__TAB_PANEL__');
                var tabItems = tabboxes.child('rs\\:TabBox')[0].elements;
                if (!tabItems || !tabItems.length || tabItems.length==0) return;
                rs.each(tabItems, function(i, item){
                    if (!item) return false;
                    item.attr('index', i);
                });
            }
        }

        function hasMobileLicence(){
            return global_params.isDebug || global_params.isShowMobileApp;
        }

        function hasMppLicence(){
            return global_params.isDebug || global_params.isMppEnabled;
        }

        function initDialog() {
            initModelDialogNotice();
            var behaviorWrapper = rs('#__OPERATE_BEHAVIOR__');
            devTypeWrapper = rs('#__DEV_TYPE__');

            ruleEditor.child('rs\\:Select[softkey]').trigger(rs.Event('change'));
            ruleEditor.child('#upstreamList').load(upstreamConfList);

            initDevTypeList();
            initBehavior();

            function initDevTypeList() {
                for (var key in DEV_TYPE_MAP) {
                    var label = rs.XElement('Label').attr('id','__'+key+'__').addClass('drsLabel').appendTo(devTypeWrapper);
                    createCheckbox({ name:key, value:key }).appendTo(label).bind('change', changeDevTypeEvent);
                    label.appendChild(rs.Text(getTransContent(DEV_TYPE_MAP, key)));

                    if (key!=DEV_TYPE_MAP.pc.key) rs.Element('span').attr({ name:'dev_des' }).html('{% trans "(Not recommended)" %}').appendTo(label);

                }
            }

            function initBehavior() {
                initRadio({name:'operate_behavior', value:'notouch', text:'{% trans "Non-Tapping" %}', checked:true}).addClass('drsLabel').appendTo(behaviorWrapper);
                initRadio({name:'operate_behavior', value:'touch', text:'{% trans "Tapping" %}'}).addClass('drsLabel').appendTo(behaviorWrapper);
            }

            function initRadio(param) {
                var label = rs.XElement('Label').appendTo(behaviorWrapper);
                var radio = rs.Element('rs\:Radio', {name:param.name, value:param.value}).as('Radio').appendTo(label);
                if (typeof radio.checked!='undefined') radio.checked = param.checked;
                var txt = rs.Text(param.text);
                label.append(txt);

                return label;
            }

            function initModelDialogNotice(){
                var box = ruleEditor.child('.box');
                var noticePane = rs.Element('div').addClass('editorNotice');
                rs('#__FILTER_DOC__').get('doc').appendTo(noticePane);
                ruleEditor.insertBefore(noticePane, box);
            }
        }

        function changeDevTypeEvent(evt) {
            var val = evt.currentTarget.value;
            var isChecked = evt.currentTarget.checked;
            var typeVals = devTypeWrapper.named('dev_type').value;
            var arr = typeVals ? typeVals.split(',') : [];
            if (isChecked) {
                arr.push(val);
            } else {
                var index = arr.indexOf(val);
                if (index > -1) arr.splice(index, 1);

            }

            devTypeWrapper.named('dev_type').value = arr.join(',');
        }

        function changeSoftKeyEvt(evt){
            evt.stopPropagation();
            ruleEditor.hideErrorMessage();
            if (/^(REPUTATION|APP_REPUTATION)$/i.test(mPage.ruleType)) {
                switch(evt.data){
                    case 'user_name':
                        document.body.named('soft_block_default').hide();
                        break;

                    case 'fp':
                    case 'ip':
                    default:
                        document.body.named('soft_block_default').show();
                        break;
                }

            } else {
                document.body.named('soft_block_default').hide();
            }



            function updateSelector(container, json, defaultValue){
                container.html('');
                var arr = transferToSelector(json);
                rs.XElement('Select').attr('name', 'threat_value').load(arr).appendTo(container).value = defaultValue;
                container.show();
            }

            function transferToSelector(json){
                var result = [];
                rs.each(json, function(i, value){
                    result.push({key:i, value:value});
                });
                return result;
            }

            updateDeviceNotice(evt.data);
        }

        function updateDeviceNotice(key){
            var des = rs('body').named('dev_des');
            if(!des) return;
            if (mPage.ruleType!='BAD_BEHAVIOR') {
                toggleDevTypeStatus(key);
                key=='fp' ? des.addClass('inlineBlock') : des.removeClass('inlineBlock');
            }
        }

        function changeKeyEvt(evt) {
            evt.stopPropagation();
            var key = evt.data;
            switch (mPage.ruleType) {
                case 'APP_HIGH_FREQ':
                    ruleEditor.updateProperty();
                    setFieldSelectStatusByCombinationKey(key, rs('#fieldList'), evt.target);
                    break;

                case 'BLACK':
                case 'APP_BLACK':
                    var row = rs('#__KEY_VALUE_ROW__');
                    if (row) {
                        key!='ip' ? row.addClass('key_value_input') : row.removeClass('key_value_input');
                        row.named('key_value').placeholder = (key=='ua') ? '' : '{% trans "Multiple values must be separated by commas." %}';
                    }

                    break;
            }


            updateDeviceNotice(key);
            checkKeyAndCounterInDialog();
            setFieldSelectStatusByCombinationKey(key, rs('#fieldList'), evt.target);
            ruleEditor.hideErrorMessage();
        }

        function changeAutoBlackListEvt(evt) {
            evt.stopPropagation();
            ruleEditor.hideErrorMessage();
        }

        function setFieldSelectStatusByCombinationKey(key, select, keyControl) {
            select.clearKeyword();
            if (key == 'combination') {
                keyControl.addClass('combination');
                select.css('display', 'inline-block');
            } else {
                keyControl.removeClass('combination');
                select.css('display', 'none');
            }
        }

        function toggleDevTypeStatus(key) {
            if(!key) return;
            var pcLabel = devTypeWrapper.child('#__pc__');
            var macLable = devTypeWrapper.child('#__mac__');
            var mobileLable = devTypeWrapper.child('#__mobile__');

            if (key=='fp') {
                macLable.bind('mouseover', showFPToolTip).bind('mouseout', hideFPToolTip);
                mobileLable.bind('mouseover', showFPToolTip).bind('mouseout', hideFPToolTip);

                setCheckboxStatus(pcLabel, true);
                setCheckboxStatus(macLable, false);
                setCheckboxStatus(mobileLable, false);
                devTypeWrapper.named('dev_type').value = 'pc';
            } else {
                macLable.unbind('mouseover', showFPToolTip).unbind('mouseout', hideFPToolTip);
                mobileLable.unbind('mouseover', showFPToolTip).unbind('mouseout', hideFPToolTip);

                setCheckboxStatus(pcLabel, true);
                setCheckboxStatus(macLable, true);
                setCheckboxStatus(mobileLable, true);
                devTypeWrapper.named('dev_type').value = 'pc,mac,mobile';
            }

            function setCheckboxStatus(label, checked) {
                var checkBox = label.child('rs\\:Checkbox');
                if (checkBox) checkBox.checked = checked;
            }
        }

        function showFPToolTip(evt) {
            var x = g_const.vertical_layout ? evt.pageX : evt.x;
            var y = g_const.vertical_layout ? evt.pageY : evt.y;
            page.titleTip(x, y, '{% trans "Fingerprints are not unique to individual device." %}');
        }

        function hideFPToolTip(evt) {
            page.titleTipHide();
        }

        function changePercentEvent(evt) {
            mPage.percent = evt.data;
        }

        function closeDialog(box) {
            box.closeDialog();
            page.cancelMarkError(box.child('input[error]'));
        }

        function getTransContent(map, key) {
            return map[key] ? map[key].trans : key;
        }


        function convertDelayValue(delay) {
            var val = delay;
            if (typeof val=='undefined' || val==null || (typeof val=='string'&&val.trim()=='') || isNaN(val)) return val;
            var arr = val.toString().split('.');
            if (arr.length>1) {
                var v = arr[1];
                if (v.length > 3) val = Number(val).toFixed(3);
            }

            return Number(val);
        }

        function isEmptyValue(val) {
            return val==null || typeof val=='undefined' || (typeof val=='string'&&val.trim()=='');
        }


        function handleToggleHelperDoc(evt) {
            ruleEditor.attr('noticeOn', noticeButton.state ? true : null);
        }

        function handleShowAutoBlackTips(evt) {
            page.titleTip(evt.x, evt.y, '{% trans 'Continue to apply the actions configured in the "Execution Strategy" above for a set time period.' %}');
        }

        function handleHideAutoBlackTips(evt) {
            page.titleTipHide();
        }

        function handleShowActionTips(evt) {
            var info =  '{% trans "Block: stop the request and send response to the client" %}' + '<br/>'
                        + '{% trans "Forward: send current request to specific upstream" %}' + '<br/>'
                        + '{% trans "Pass: current request will not be blocked by RAS but the response will be encapsulated" %}' + '<br/>'
                        + '{% trans "Transparent: current request will not be blocked by RAS and the response will not be encapsulated" %}' + '<br/>'
                        + '{% trans "None: current request will not be blocked by Programmable Defending but other modules are still working" %}' + '<br/>';

            if (!isAppTable(mPage.ruleType)) {
                info =  info
                      + '{% trans "Redirect: set status code 302 and redirect to the other path" %}' + '<br/>'
                      + '{% trans "Challenge: return reCAPTCHA(with Dynamic Challenge open)"%}' + '<br/>'
                      + '{% trans "InsertJS: insert JS code to the response file" %}' + '<br/>'
                      + '{% trans "return_html: return specific html file to the client" %}'
            }

            page.titleTip(evt.x, evt.y, info);
        }

        function handleHideActionTips(evt) {
            page.titleTipHide();
        }

        function templateIntroductionOnclick() {
            var enable = global_params.isProxyServer && mPage.orgEnableCaptcha && mPage.templatesCount > 0;
            if (enable) {
                if (embedMode) {
                    openChallengeTemplate();
                    return false;
                } else {
                    return true;
                }
            } else {
                setTimeout(function () {
                    rsalert('{% trans "Please enable Dynamic Challenge and save it before viewing the template introduction on Proxy node" %}');
                }, 0);
                return false;
            }
        }

        function handleBeforeSubmitUbbConfig(evt) {
            var importBtn = rs('#__IMPORT_UBB_BTN__');
            rs('#__ASK_POP__').errorMessage = '{% trans "Note: To avoid interruption to applications, please ensure the required Programmable Defending resource files have already been imported." %}';

            ask('{% trans "The imported Programmable Defending settings will overwrite existing settings (Resource files needs to be downloaded and uploaded separately). Are you sure to import?" %}', function() {
                if (global_params.isDevDebug) {
                    importBtn.submit();
                } else {
                    var data = {
                        serviceCallback: function(data) {
                            rs.Element('input').attr({
                                name:'adv_operation_code',
                                type:'hidden',
                                value: data.adv_operation_code
                            }).appendTo(importBtn.child('form'));

                            importBtn.submit();
                        },
                        cancelRollback: function() {
                            resetImport();
                        }
                    };

                    triggerAdvancedQRCodeEvent(data);
                }
                
                
            }, function() {
                resetImport();
            }, null, 3);
        }

        function handleGetResult2ImportUbbConfig(evt) {
            var importBtn = rs('#__IMPORT_UBB_BTN__');
            FileUploadResultHandler.process(evt.data, function(res) {
                if (res.result === 'ok') {
                    rsalert('{% trans "Import successfully" %}', function() {
                        refreshCurLocation();
                    });
                } else {
                    rsalert(res.error || '{% trans "Import failed" %}');
                    resetImport();
                }
            });
        }


        function resetImport() {
            rs('#__ASK_POP__').errorMessage = '';
            rs('#__IMPORT_UBB_BTN__').refresh();
        }

        function handleExportUbbConfig() {
            service.downloadService('/ubb_scenes/export_ubb_config/');
        }

    </script>


    <script type="text/javascript" language="JavaScript" src="/static/js/thirdPart/codemirror/codemirror.min.js"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/thirdPart/codemirror/addon/hint/show-hint.js"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/thirdPart/codemirror/mode/lua.js"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/thirdPart/codemirror/mode/editor_css.js"></script>
    <script type="text/javascript" src="/static/js/data/ubb_filter_options.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script type="text/javascript" src="/static/js/data/app_ubb_filter_options.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script type="text/javascript" language="JavaScript" src="/static/js/thirdPart/codemirror/mode/ubb-filter.js"></script>
    <script type="text/javascript" src="/static/js/pages/ubb/ubb_scenes.html.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script type="text/javascript" src="/static/js/pages/ubb/ubb_scenes_advanced.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script type="text/javascript" src="/static/js/pages/ubb/ubb_website_shield.js?ghrdm={{ build_hash_and_layout_hash }}"></script>
    <script type="text/javascript" src="/static/js/pages/ubb/ubb_dynamic_challenge.js?ghrdm={{ build_hash_and_layout_hash }}"></script>

{% endblock %}