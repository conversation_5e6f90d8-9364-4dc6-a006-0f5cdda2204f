{% extends "v2/base.html" %}

{% load i18n %}

{% load nav_tags %}

{% block title %}{% trans 'Protection' %}{% endblock %}

{% block navigation %}{% trans 'Protection' %}{% endblock %}

{% block content %}

<style type="text/css">
    body {
        min-width: 1180px;
    }

    #__IMPORT_UPSTREAM_BTN__,
    #__CREATE_NEW_SERVER__ {
        vertical-align: baseline;
        /*display: none;*/
    }

    section > p[name=proxyConfigBtns] {
        margin-top:5px;
        margin-bottom:25px;
        background-color: transparent;
    }

    section > div[name=proxyConfigBtns] [name=globalSettingBtn] {
        float:right;
        margin-bottom:20px;
    }

    section > footer > p > button[name=saveCustomSiteNamesBtn] {
        float:right;
        margin-bottom:20px;
        background-color: #cc5439 !important;
        color: #FFF !important;
    }

    .warningColor { color:#f0c200 }

    span[name=site_total_span] {
        color:#5a5f67;
        display: inline-block;
        font-weight: 400;
    }

    span[name=upstreamCount] {
        font-weight: 400;
    }

    a[name=deleteUpstreamBtn],
    a[name=exportUpstreamBtn] {
        display: none;
    }

    rs\:Table[name=upstreamList] div[row=row]:hover a[name=deleteUpstreamBtn],
    rs\:Table[name=upstreamList] div[row=row]:hover a[name=exportUpstreamBtn] {
        display: inline-block;
    }

    [name=serverCount] [name=serverList] { margin:0px; padding:0px 6px; }
    [name=serverCount] [name=serverList] li { word-wrap: break-word; word-break: break-word; }
    [name=serverCount] [name=serverList] li:not(:first-child) { margin-top:6px; }

    input[highlight][name=dname] {
        background-color: orange !important;
        box-shadow: 0 0 3px #d1850b;
        color: black !important;
    }

	rs\:Table[name=upstreamList][deploymode="plugin"] > div[header=header] > div:nth-child(4),
	rs\:Table[name=upstreamList][deploymode="plugin"] > div[row=row] > div:nth-child(4) {
		display: none !important;
	}

    input[name=upstreamSearchKey]::-ms-clear,
    input[name=upstreamSearchKey]::-ms-reveal {
        display: none;
    }

    #__BATCH_PROXY_LIST_DAILOG__ .box {
        max-width: 1000px;
        min-width: 1000px;
        top: 6%;
        left: 0px;
        right: 0px;
    }

    #__BATCH_SITE_LIST__ div[name=checkedCol] { width: 40px; }

    #oneClickSwitch {
        min-width: 90px !important;
        margin: 0px;
    }

    html[lang=en] #oneClickSwitch {
        min-width: 110px !important;
        margin: 0px;
    }

    #oneClickSwitch > li {
        text-align: center;
        margin-bottom: 0px;
        padding: 6px 0px;
    }

    #oneClickSwitch > li:hover {
        background-color: #f2fafc;
        color: #00a6c8;
    }

    #oneClickSwitch > li:not(:last-child) {
        border-bottom: 1px solid #eee;
    }

    #__BATCH_PROXY_LIST_DAILOG__ div[name=mdBottom] > div[custombottomtemp] button {
        margin-left: 5px;
        margin-right: 0px;
    }

    #__BATCH_SITE_LIST__ + p.emptyInfo {
        margin-bottom: 0px;
        border-bottom: 0px;
    }

    #__BATCH_SITE_LIST__ div[row]:nth-last-child(1) [table-cell] {
        border-bottom: 0px;
    }

    #__BATCH_SITE_LIST__ div[row] *:not(rs\:Switch, rs\:Switch i) {
        cursor: pointer !important;
    }

    #__BATCH_SITE_LIST__ div[row] > div[table-cell][name=_dname] {
        width: 270px;
        max-width: 300px;
    }

    #__BATCH_SITE_LIST__ rs\:Checkbox {
        margin-top: -4px;
    }

    #__BATCH_SITE_LIST__ .enableSiteConfSwitch {
        float: right;
        right: 20px;
        margin-bottom: 0px;
        top: 2px;
    }

    #__BATCH_PROXY_LIST_DAILOG__.loading div[name=content]:after {
        content: '';
        display: block;
        background-image: url(/static/img/loading-w.gif);
        position: absolute;
        top: 0px;
        bottom: 0px;
        left: 0px;
        right: 0px;
        background-repeat: no-repeat;
        background-color: white;
        background-position: center;
        z-index: 2
    }

    #__BATCH_PROXY_LIST_DAILOG__ #oneClickTableWrapper {
        height: 400px;
        overflow: auto;
        border: 1px solid #ddd;
    }

    #oneClickDropBox > button[dropdown=dropdown] {
        line-height: unset;
        height: 24px;
    }

    @media screen and (min-width:0\0) {
        #oneClickDropBox {
            margin-top: -1px;
        }
    }
</style>

<rs:style enable="#embedMode">
    #__BATCH_PROXY_LIST_DAILOG__ #oneClickTableWrapper {
        height: 350px;
    }
</rs:style>


<section class="errorTips" {% if error %}error{% endif %}>
    <i class="ic-warning"></i>
    <span name="errorMsg">{{ error }}</span>
</section>

<section name="configProxyList" id="configProxyList">
    <div name="proxyConfigBtns">
        <!-- 开启全流量采集 -->
        <div v:show="matchDeployMode('mirror')">
            <rs:label style="margin-bottom: 10px; margin-right: 0px;">
                <rs:Checkbox
                    type="checkbox"
                    id="__ENABLE_FULL_FLOW_COLLECTION__"
                    rsid="checkbox_enableFullFlowCollection"
                    v:checked="isFullFlowCollection"
                    v:disabled="!auth('Full_Flow_Collection', 'w')"
                    onchange="MainListManager.changeFullFlowCollection(event)"
                ></rs:Checkbox>{% trans 'Enable full flow collection' %}
            </rs:label>
            <rs:InfoIcon>{% trans 'After opening, it will monitor http traffic on all ports in the current network, but it may have a certain impact on performance. If system resources are in short supply, please turn off this option and manually add the port designated by the site to collect traffic to improve performance.' %}</rs:InfoIcon>
        </div>

        <!-- 上传 -->
        <rs:FileUpload
            id="__IMPORT_UPSTREAM_BTN__"
            rsid="bt_importWebsites"
            v:if="canImport"
            name="rawdata"
            action="/proxy/check_file_size/"
            accept=".json"
            buttonOnly="true"
            uploadBtnTxt="{% trans 'Import Websites' %}"
            onData="MainListManager.finishImport(event)"
        >
            <span>
                {% csrf_token %}
                <input type="hidden" name="action" value="import"/>
            </span>
        </rs:FileUpload>

        <!-- 添加 -->
        <button
            type="button"
            v:if="canCreate"
            id="__CREATE_NEW_SERVER__"
            rsid="bt_createNewServer"
            onclick="MainListManager.createNewSite()"
        >{% trans 'Add Websites' %}</button>

        <!-- 一键切换 -->
        <rs:DropBox id="oneClickDropBox" v:show="page.expertMode && isUpstreamEditable">
            <button rs:role="handler" type="button" dropdown="dropdown">{% trans "Quick Switch" %}</button>
            <ul
                rs:role="dropLayer"
                name="dropList"
                style="min-width: 90px;"
                id="oneClickSwitch"
            >
                <li onclick="OneClickSwitchManager.openOneclickDialog(ModeType.DISABLE)">{% trans "To Disabled" %}</li>
                <li onclick="OneClickSwitchManager.openOneclickDialog(ModeType.TRANSPARENT)" v:if="!matchDeployMode('mirror')">{% trans "To Pass-Through" %}</li>
                <li onclick="OneClickSwitchManager.openOneclickDialog(ModeType.MONITOR)" v:if="!matchDeployMode('mirror')">{% trans "To Monitor" %}</li>
                <li onclick="OneClickSwitchManager.openOneclickDialog(ModeType.BLOCK)" v:if="!matchDeployMode('mirror')">{% trans "To Block" %}</li>
            </ul>
        </rs:DropBox>

        <!-- 关键字搜索input -->
        <span name="searchWrapper">
            <input
                type="text"
                name="upstreamSearchKey"
                id="__SEARCH_UPSTREAM__"
                class="keywordSearchTriggerInput"
                placeholder="{% trans 'Search website(s)' %}"
                autocomplete="off"
            />
        </span>

        <!-- 计数与视图切换 -->
        <span name="upstreamInfoWrapper">
            <span name="site_total_span">{% trans '<span name="upstreamCount"></span> website(s) added in total' %}</span>
            <span
                id="__CHANGE_VIEW__"
                class="iconText"
                onclick="MainListManager.changeView(event)"
            >{% trans "Switch View" %}</span>
        </span>


        <!-- 全局设置 -->
        <button
            v:if="auth('Global_Settings', 'r')"
            name="globalSettingBtn"
            type="button"
            onclick="MainListManager.redirectGlobalSettings()"
        >{% trans 'Global Settings' %}</button>
    </div>

    <section v:if="auth('Protected_Websites', 'r')">
        <rs:Table
            id="mainList"
            name="upstreamList"
            header="{% trans 'Website Name' %}|{% trans 'Websites' %}|{% trans 'Protection Mode' %}|{% trans 'Server IP' %}|"
            cells="dname|name|status|ipList|"
            emptyText="{% trans 'No websites have been added yet.' %}"
            onCreateHeaderCell="MainListManager.createHeaderCell(event)"
            onCreateRow="MainListManager.createRow(event)"
            onEmptyData="MainListManager.createEmptyRow(event)"
            v:deployMode="global_params.deployMode"
        >

            <div rs-temp>
                <div>
                    <input
                        v:show="canChangeSiteName"
                        v:value="SiteNameManager.getName(rowData)"
                        v:serverKey="rowData.key"
                        v:isEmptyName="rowData.isEmptyName"
                        type="text"
                        name="dname"
                        class="websiteName"
                        onkeyup="SiteNameManager.keyup(event)"
                        onblur="MainListManager.SiteName.blur(event)"
                        onfocus="SiteNameManager.focus(event)"
                        onmouseover="SiteNameManager.mouseover(event)"
                        onmouseout="SiteNameManager.mouseout(event)"
                    />
                    <span
                        v:if="!canChangeSiteName"
                        class="websiteName"
                    >
                        <span
                            v:text="rowData.dname || '--'"
                            readonly
                            onmouseover="SiteNameManager.mouseover(event)"
                            onmouseout="SiteNameManager.mouseout(event)"
                        ></span>
                    </span>
                    
                    <rs:Switch
                        v:if="auth('Edit_Website_Config', 'w')"
                        id = "enableSiteConf"
                        name = "enable_site_conf"
                        class="enableSiteConfSwitch"
                        v:checked="rowData.enable_site_conf != false"
                        v:disabled="!canEnableConf"
                        @mouseover="MainListManager.mouseoverEnableSwitch(event)"
                        @mouseout="MainListManager.mouseoutEnableSwitch(event)"
                        @change="MainListManager.changeStatus(event, rowData.key)"
                    ></rs:Switch>
                </div>
                <div>
                    <span
                        @click="MainListManager.redirectProxyDetail(rowData.key)"
                        @mouseenter="MainListManager.mouseoverWebsite(event, rowData)"
                        @mouseleave="MainListManager.mouseoutWebsite(event)"
                    >
                        <span
                            v:if="!MainListManager.getConnectionStatus(rowData.status) && rowData.enable_site_conf != false"
                            class="ic-warning warningColor"
                            style="padding-right:5px;"
                        ></span>
                        <span name="link" v:html="rowData.upstream_protocol + rowData.name"></span>
                    </span>
                </div>
                <div>
                    <span
                        name="modeObj"
                        v:text="MainListManager.getModeObj(rowData).modeText"
                        v:class="MainListManager.getModeObj(rowData).className"
                    ></span>
                </div>
                {% if isProxyRole %}
                <div >
                    <div name="serverCount"
                         class="oval"
                         v:healthCheckNotvailable="rowData.healthCheckNotvailable ? '' : null"
                         v:allServerUp="rowData.allServerUp ? '' : null"
                         v:partialServerUp="rowData.partialServerUp ? '' : null"
                         v:allServerDown="rowData.allServerDown ? '' : null"
                         @mouseover="MainListManager.mouseoverServerList(event, this.named('serverList'), rowData)"
                         onmouseout="MainListManager.mouseoutServerList(event)">
                        <span v:text="rowData.ipList.length"></span>
                        <rs:List name="serverList" v:data="rowData.ipList" rs-lazy>
                            <li
                                v:class="MainListManager.markServerHealthFlag(rowData.health_status)"
                                v:text="matchDeployMode('mirror|transparent') ? rowData.ip : ($parent.rowData.server_protocol + rowData.ip + ':' + rowData.port)"
                            ></li>
                        </rs:List>
                    </div>
                </div>
                {% endif %}
                <div>
                    <a name="exportUpstreamBtn"
                       v:if="auth('Export_Website', 'r')"
                       title="{% trans 'Export' %}"
                       class="ic-download inlineBlock"
                       @click="MainListManager.downLoadUpstream(rowData.key)"></a>

                    <a name="deleteUpstreamBtn"
                       v:if="canDelete"
                       title="{% trans 'Delete ' %}"
                       class="ic-trash"
                       @click="MainListManager.deleteUpstream(event, rowData)"></a>
                </div>
            </div>

        </rs:Table>
    </section>
    <footer id="footerSaveAll" style="display: none">
        <p>
            <button
                v:if="canChangeSiteName"
                type="button"
                class="floatRight"
                name="saveCustomSiteNamesBtn"
                onclick="MainListManager.SiteName.save()"
            >{% trans 'Save Site Names' %}</button>
        </p>
    </footer>
</section>


<!-- 一键切换弹框 -->
<rs:ModalDialog
    id="__BATCH_PROXY_LIST_DAILOG__"
    onCancel="OneClickSwitchManager.cancel(event)"
>
    <div name="content">
        <div>
            <div style="padding-bottom: 20px;">
                <div>
                    <span name="searchWrapper" style="margin-right: 20px;">
                        <input
                            type="text"
                            name="upstreamSearchKey"
                            id="__SEARCH_UPSTREAM_IN_DIALOG__"
                            class="keywordSearchTriggerInput"
                            placeholder="{% trans 'Search website(s)' %}"
                            autocomplete="off"
                        />
                    </span>
                </div>

            </div>

            <div id="oneClickTableWrapper">
                <rs:Table
                    name="siteList"
                    id="__BATCH_SITE_LIST__"
                    header="|{% trans 'Website Name' %}|{% trans 'Websites' %}|{% trans 'Protection Mode' %}|{% trans 'Server IP' %}"
                    cells="|dname|name|status|ipList"
                    onCreateHeaderCell="OneClickSwitchManager.createOneclickHeaderCell(event)"
                    onCreateRow="OneClickSwitchManager.createOneclickRow(event)"
                    onData="OneClickSwitchManager.refreshOneclickTableDatas(event)"
                >
                    <div>
                        <div name="checkedCol">
                            <rs:Checkbox
                                v:checked="rowData.checked"
                                onchange="OneClickSwitchManager.clickSingleCheckbox(event)"
                            ></rs:Checkbox>
                        </div>

                        <div name="_dname">
                            <span class="websiteName">
                                <span
                                    v:text="rowData.isEmptyName ? '-' : rowData.dname"
                                    onmouseover="SiteNameManager.mouseover(event)"
                                    onmouseout="SiteNameManager.mouseout(event)"
                                ></span>
                            </span>
                            <rs:Switch
                                class="enableSiteConfSwitch"
                                v:checked="rowData.enable_site_conf != false"
                                v:disabled="true"
                            ></rs:Switch>
                        </div>

                        <div name="name"></div>

                        <div>
                            <span
                                name="status"
                                v:text="OneClickSwitchManager.getModeObj(rowData).modeText"
                                v:class="OneClickSwitchManager.getModeObj(rowData).className"
                                v:style="rowData.enable_site_conf ? '' : 'background-color:#adafb3 !important'"
                            ></span>
                        </div>

                        {% if isProxyRole %}
                        <div>
                            <div
                                name="serverCount"
                                v:text="rowData.ipList.length"
                                v:healthCheckNotvailable="rowData.healthCheckNotvailable ? '' : null"
                                v:allServerUp="rowData.allServerUp ? '' : null"
                                v:partialServerUp="rowData.partialServerUp ? '' : null"
                                v:allServerDown="rowData.allServerDown ? '' : null"
                            ></div>
                        </div>
                        {% endif %}
                    </div>
                </rs:Table>
            </div>
        </div>
    </div>

    <div customBottomTemp>
        <button onclick="OneClickSwitchManager.cancel(event)" name="cancel" type="button" normal>{% trans "Cancel" %}</button>
        <button onclick="OneClickSwitchManager.save(event)" name="ok" type="button" important>{% trans "Ok" %}</button>
    </div>
</rs:ModalDialog>
{% endblock %}


{% block script %}

<script language="JavaScript" type="text/javascript">

    /*
    ###########################################################
        Initialize Dynamic Data
    ###########################################################
     */

    rs.plugin({
        FileUpload: '~/ui.FileUpload.js'
    });

    var TABLE_VIEW_TYPE = {
        LIST: 'list',
        CARD: 'card'
    };

    var ModeType = {
        DISABLE: 'disable',
        BLOCK: 'block',
        MONITOR: 'monitor',
        TRANSPARENT: 'transparent'
    };

    var ModeMap = {
        disable: { type: ModeType.DISABLE, modeText: '{% trans "disabled" %}', oneclickText: '{% trans "To Disabled" %}', className: 'closeColor' },
        block: { type: ModeType.BLOCK, modeText: '{% trans "Block" %}', oneclickText: '{% trans "To Block" %}', className: 'blockColor' },
        monitor: { type: ModeType.MONITOR, modeText: '{% trans "Monitor" %}', oneclickText: '{% trans "To Monitor" %}', className: 'monitorColor' },
        transparent: { type: ModeType.TRANSPARENT, modeText: '{% trans "Transparent" %}', oneclickText: '{% trans "To Pass-Through" %}', className: 'transparentColor' },
    };

    {% autoescape off %}
    var isFullFlowCollection = {{ is_full_flow_collection|to_json }};
    var isProxyRole = {{ isProxyRole|to_json }};
    var isUpstreamEditable = {{is_upstream_editable|lower}};
    var upstreamConfList = {{ upstreamConfList|to_json }} || [];
    var maxProxies = {{ maxProxies }};
    var tableView = {{ table_view|to_json }} ? {{ table_view|to_json }} : TABLE_VIEW_TYPE.CARD;
    var appConfigList = [
        {
            siteMap: {{ wechat_occupied_sites|to_json}},
            getMsg: function(applist) {
                return '{% trans "The site was used by these wechat app, as follow: " %}' + '<br/><br/>' + applist + '<br/><br/>'
            }
        },
        {
            siteMap: {{ alipay_mpp_occupied_sites|to_json}},
            getMsg: function(applist) {
                return '{% trans "The site was used by these Alipay mini program protection, as follow: " %}' + '<br/><br/>' + applist + '<br/><br/>'
            }
        },
        {
            siteMap: {{ mpaas_mpp_occupied_sites|to_json}},
            getMsg: function(applist) {
                return '{% trans "The site was used by these Mobile mini program protection, as follow: " %}' + '<br/><br/>' + applist + '<br/><br/>'
            }
        }
    ];

    var uiData = initUIDatas();
    var isNeedCheckHealth = uiData.hasHealthCheck;
    var upstreamList = uiData.siteList;
    var upstreamMap = uiData.siteMap;
    var upstreamCount = upstreamList.length;

    var isPreventRedirect = false;
    var isChrome = rs('html').attr('type') == 'Chrome';

    {% endautoescape %}




    /**------------------------ 权限 ------------------------*/

    var _isApi = isApiProduct(global_params.productType),
        _isApiEditable = auth('API_Advance_Means', 'w');

    var canChangeSiteName = _isApi ? _isApiEditable : auth('Change_Website_Name', 'w'),
        canImport = isUpstreamEditable && (_isApi ? _isApiEditable : auth('Import_Website', 'w')),
        canCreate = isUpstreamEditable && (_isApi ? _isApiEditable : auth('Add_Website', 'w')),
        canDelete = _isApi ? _isApiEditable : auth('Delete_Website', 'w'),
        canEnableConf = _isApi ? _isApiEditable :  auth('Enable_Site_Conf', 'w');




    /**------------------------ 页面初始化 ------------------------*/

    function initPage() {
        if (isNeedCheckHealth) {
            service.checkUpstreamStatus(function(response) {
                if (!response) {
                    return;
                }

                addServerHealthStatusFields(response.servers.server);
                fillPage();

            }, function(){
                fillPage();
            });

        } else {
            fillPage();
        }

        RegistEventHandler.registUpdateBadServer(refreshUpstreamList);
    }


    function addServerHealthStatusFields(servers) {
        var healthMap = {};
        rs.each(servers, function(i, server) {
            if(!server) return false;

            var re = /(.+)(_[0-9]+)((_[a-zA-Z0-9]{32})?)(_IPv[46])?_list/g;
            var groups = re.exec(server.upstream);
            var host = groups[1];
            if (host.indexOf('.') === -1) { // host is IPv6 or Regex MD5
                host = host.replace(/_/g, ':');
            }

            var key = host + groups[2] + groups[3];
            var o = healthMap[key];
            if (o) {
                o[server.name] = server.status;
            } else {
                var obj = {};
                obj[server.name] = server.status;
                healthMap[key] = obj;
            }
        });

        for (var key in healthMap) {
            var upstream = upstreamMap[key];
            if (!upstream) continue;
            var o = healthMap[key];
            var hasSomeOneError = false;
            var hasSomeOneOK = false;
            rs.each(upstream.ipList, function(i, d) {
                var server = d.ip+':'+d.port;
                var healthStatus = o[server];
                if (!healthStatus) return false;

                d['health_status'] = healthStatus;
                hasSomeOneError = hasSomeOneError || healthStatus=='down';
                hasSomeOneOK = hasSomeOneOK || healthStatus=='up';
            });

            upstream['healthCheckNotvailable'] = !hasSomeOneError && !hasSomeOneOK;
            upstream['allServerUp'] = !hasSomeOneError && hasSomeOneOK;
            upstream['partialServerUp'] = hasSomeOneError && hasSomeOneOK;
            upstream['allServerDown'] = hasSomeOneError && !hasSomeOneOK;
        }
    }

    function fillPage() {
        MainListManager.init();
        OneClickSwitchManager.init();

        window.onfocus = function(){
            isPreventRedirect=false;
        }
    }


    function initUIDatas() {
        var siteList = [];
        var siteMap = {};
        var hasHealthCheck = false;

        var len = upstreamConfList.length;
        for (var i = 0; i < len; i++) {
            var conf = upstreamConfList[i];
            if (!conf) continue;
            if (!hasHealthCheck) {
                var isHealthCheck = conf.health_check.is_health_check_enable;
                hasHealthCheck = isHealthCheck || hasHealthCheck;
            }

            var host = conf.ServerName;
            if (conf.ServerNameType === 'IPv6') {
                host = '[' + host + ']';
            }
            host = host + ':' + conf.ListenPort;
            var obj = {
                dname:conf.site_customize_name||'',
                key:conf.key,
                name:host,
                status:conf.status,
                enabled:conf.enabled,
                learning_mode:conf.learning_mode,
                is_health_check: isHealthCheck,
                allServerUp:false,
                partialServerUp:false,
                healthCheckNotvailable:false,
                allServerDown:false,
                test_output: conf.test_output,
                scene_rules: conf.scene_rules||0,
                Extra_Business_Data: conf.Extra_Business_Data,
                upstream_protocol: conf.IsHttps?'https://':'http://',
                server_protocol: conf.IsUpstreamHttps?'https://':'http://',
                ipList:[],
                isEmptyName: !conf.site_customize_name,
                enable_site_conf: conf.enable_site_conf === false ? false : true
            };

            var serverList = conf.UpstreamList;
            if (conf.divide_ipv46_enabled) {
                serverList = conf.UpstreamList_IPv4.concat(conf.UpstreamList_IPv6);
            }
            if (!serverList) serverList = [];
            var slen = serverList.length;
            for (var j = 0; j < slen; j++) {
                var server = serverList[j];
                if (!server || !Array.isArray(server) || server.length<2) continue;
                if (!Validation.IPV6(server[0])) server[0] = '['+server[0]+']';
                obj.ipList.push({
                    ip:server[0],
                    port:server[1],
                    health_status: isHealthCheck ? 'off' : ''
                });

                if (isHealthCheck) {
                    obj.healthCheckNotvailable = true;
                }
            }

            siteList.push(obj);
            siteMap[obj.key] = obj
        }

        return {
            siteList: siteList,
            siteMap: siteMap,
            hasHealthCheck: hasHealthCheck
        };
    }


    function refreshUpstreamList(errServerList) {
        ErrorServerList = [];
        {% for upstreamConf in upstreamConfList %}
            {% if upstreamConf.status != 'test_pass' and upstreamConf.enable_site_conf != False %}
                ErrorServerList.push('{{ upstreamConf.key }}');
            {% endif %}
        {% endfor %}

        if (errServerList.length != ErrorServerList.length) {
            location.reload();
        } else {
            var equal = true;
            for( var i = 0; i< errServerList.length; i++) {
                if(!isContain(ErrorServerList, errServerList[i])) {
                    equal = false;
                    break;
                }
            }

            if (!equal) location.reload();
        }
    }


    /**------ 站点名称相关 ------*/
    var SiteNameManager = (function() {
        var NAME_MAX_LEN = 68;
        var defaultName = '{% trans "Add website name" %}';
        var changedSiteNames = {};

        return {
            getName: function(rowData) {
                return rowData.isEmptyName ? defaultName : rowData.dname
            },

            focus: function(evt) {
                evt.stopPropagation();
                if (!canChangeSiteName) return;
                isPreventRedirect = true;
                var elem = evt.currentTarget;
                var isEmptyName = elem.attr('isEmptyName')=='true';
                if (!elem.hasAttribute("highlight")) {
                    elem.oldValue = elem.value = isEmptyName ? '' : elem.value;     // isEmptyName==true, elem.value==defaultName, 因此需要转换一下值
                    elem.attr({
                        hover: null,
                        error: null,
                        isEmptyName: null,
                        edit: '',
                        highlight: null
                    });
                }
            },

            blur: function(evt, listInstance) {
                evt.stopPropagation();
                if (!canChangeSiteName) return;
                var elem = evt.currentTarget;
                var key = elem.attr('serverKey');
                var val = elem.value.trim();
                var errorMsg = _validateSiteName(val);
                if (errorMsg) {
                    isPreventRedirect = false;
                    elem.attr('error', '');
                    rsalert(errorMsg);
                    return;
                }
                if (val != elem.oldValue) {
                    _updateSiteNameInput(elem, val);
                    _updateSiteNameList(elem, key, val, listInstance);
                    changedSiteNames[key] = {"new_value":val};
                    elem.attr('highlight', '');
                    rs('#footerSaveAll').show();
                } else {
                    _updateSiteNameInput(elem, val);  // 不提交, 修改input的attribute
                    delete changedSiteNames[key];
                    if (Object.keys(changedSiteNames).length == 0) {
                        rs('#footerSaveAll').hide();
                    }
                }
            },

            mouseover: function(event) {
                event.stopPropagation();
                var tipX = g_const.vertical_layout ? event.pageX : event.clientX;
                var tipY = g_const.vertical_layout ? event.pageY : event.clientY;
                page.titleTip(tipX, tipY, rs.killHTML(event.target.value || event.target.textContent || ''));
            },

            mouseout: function(event) {
                event.stopPropagation();
                page.titleTipHide();
            },

            keyup: function(evt) {
                evt.stopPropagation();
                if (!canChangeSiteName) return;
                if (evt.keyCode==8) return;
                var elem = evt.target;
                var val = elem.value;
                if (evt.keyCode==13) {
                    isPreventRedirect = false;
                    elem.blur();
                } else {
                    var selection = window.getSelection();
                    if (val.len()>=NAME_MAX_LEN) {
                        var activeElem = document.activeElement;
                        if (activeElem && activeElem.nodeType==1 && activeElem.tagName=='INPUT') {
                            var _start = activeElem.selectionStart;
                            var _end = activeElem.selectionEnd;
                            if (_end-_start!=0) return;
                        }

                        evt.preventDefault();
                        return;
                    }
                }
            },

            // 保存站点名称
            save: function(listInstance) {
                var data = [];
                for (var i in changedSiteNames){
                    var item = changedSiteNames[i];
                    data.push([i, item.new_value]);
                }

                service.editSiteName(
                    data,
                    function(res) {
                        if (res.result!='ok') {
                            rsalert(res.message || rs.formatString('{% trans "{0} Failed to change website name." %}', ''));
                            return;
                        }
                        for (var i in changedSiteNames){
                            var item = changedSiteNames[i];
                            var elem = rs('input[serverkey="'+i+'"]');
                            _updateSiteNameInput(elem, item.new_value);
                            _updateSiteNameList(elem, i, item.new_value, listInstance);
                        }
                        rs('#footerSaveAll').hide();
                    });
            }
        };


        function _validateSiteName(val) {
            var errorMsg = '';
            if (val != '') {
                if (val.len() > NAME_MAX_LEN) {
                    errorMsg = rs.formatString('{% trans "Custom Website Name must not exceed {0} Chinese characters or {1} English characters." %}', NAME_MAX_LEN/2, NAME_MAX_LEN);
                } else if (!(/^[()-._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$/.test(val))) {
                    errorMsg = '{% trans "Custom Website Name only support numbers, letters, Chinese, and characters such as: spaces, dot(.), underline(_), hyphen(-), parentheses, asterisk(*), plus(+), comma(,)." %}';
                }
            }

            return errorMsg;
        }

        function _updateSiteNameInput(elem, val) {
            elem.attr({
                edit: null,
                hover: null,
                error: null,
                isEmptyName:(val=='') ? true : null,
                highlight: null
            });

            elem.value = (val=='') ? defaultName : val;     // 修改显示值
        }

        function _updateSiteNameList(elem, key, value, listInstance) {
            // update all upstreamList
            for (var i = 0; i < upstreamList.length; i++) {
                var upstream = upstreamList[i];
                if (!upstream) continue;
                if (upstream.key == key) {
                    upstream['dname'] = value;
                    upstream['isEmptyName'] = (value=='');
                    break;
                }
            }

            listInstance.updateSiteList(upstreamList);

            // update upstreamTable data
            var row = elem.parent('div[row=row]');
            var rowData = row.data;
            rowData.isEmptyName = (value=='');
            row.data = rowData;
        }
    })();


    /**------------------------ 构建列表基类 ------------------------*/

    var ProtectSiteListClass = function() {
        var _this = this;
        var _siteList = [];
        var _siteCount = 0;
        var _table = null;
        var _defaultSort = {
            field: 'name',
            rule: 'asc'
        };
        var _searchTimer;

        Object.defineProperties(this, {
            table: { get: function() { return _table; } },
            siteList: { get: function() { return _siteList; } },
            siteCount: { get: function() { return _siteCount; } }
        })

        _this.init = function(table, tableDatas) {
            _table = table;
            _siteList = tableDatas;
            _siteCount = _siteList.length;
        };

        _this.updateSiteList = function(newDatas) {
            _siteList = utils.deepCopyObject(newDatas);
            _siteCount = _siteList.length;
        }

        _this.createSortHeaderCell = function(evt) {
            var txt = rs.Text(evt.data);
            evt.element.append(txt);
            var sortUp = rs.Element('i').addClass('fa fa-sort-up');
            var sortDown = rs.Element('i').addClass('fa fa-sort-down');
            var sortIcon = rs.Element('div')
                .attr({rule:'', rsid: 'RsSortButton' })
                .addClass('RsSortButton')
                .bind('click', function(cevt) {
                    var rule = this.attr('rule');
                    _table.child('.sort-selected').removeClass('sort-selected');
                    evt.element.addClass('sort-selected');

                    var isAsc = rule == _defaultSort.rule || rule == '';
                    if (isAsc) {
                        this.attr('rule', 'desc');
                        isAsc = false;
                    } else {
                        this.attr('rule', 'asc');
                        isAsc = true;
                    }

                    var sortEvt = rs.Event('sortList');
                    sortEvt.sort = isAsc ? 'asc' : 'desc';    // asc由小到大
                    sortEvt.key = evt.key;
                    _table.trigger(sortEvt);

            }).append(sortUp).append(sortDown);

            if (evt.key == _defaultSort.field) {
                evt.element.addClass('sort-selected');
                sortIcon.attr('rule', _defaultSort.rule);
            }

            evt.element.append(sortIcon);
        }

        // 恢复table默认排序
        _this.restoreDefaultSortCell = function() {
            var sortKey = _defaultSort.field;
            var isAsc = _defaultSort.rule;

            // 移除当前排序
            var sortSelectedCell = _table.querySelector('div.sort-selected');
            sortSelectedCell.removeClass('sort-selected');
            sortSelectedCell.child('div.RsSortButton').attr('rule', null);

            // 恢复默认排序
            var defaultSortCell = _table.named('_' + sortKey);
            defaultSortCell.addClass('sort-selected');
            defaultSortCell.child('div.RsSortButton').attr('rule', isAsc);

        }

        _this.markRow = function(row, data) {
            var isConnect = _getConnectionStatus(data.status);
            if (!isConnect) row.attr('non-connect', '');

            if (data && data['enable_site_conf'] == false) {
                var serverCountElem = row.named('serverCount');
                if (serverCountElem) serverCountElem.style = 'background-color:#adafb3 !important';

                var modeElem = row.named('modeObj')
                if (modeElem) modeElem.style = 'background-color:#adafb3 !important';
            }
        }

        _this.searchKeyword = function(evt, callback) {
            if (evt.keyCode == 13) return;
            if (_siteCount == 0) return;

            clearTimeout(_searchTimer)
            var resultList = [];
            _searchTimer = setTimeout(function() {
                var keyword = evt.target.value.trim().toLowerCase();

                if (!keyword) {
                    resultList = _siteList;

                } else {
                    for (var i = 0; i < _siteCount; i++) {
                        var data = _siteList[i]
                        if (!data) continue;
                        if (data.server_protocol.toLowerCase().indexOf(keyword) > -1
                            || data.name.toLowerCase().indexOf(keyword) > -1
                            || data.dname.toLowerCase().indexOf(keyword) > -1
                            || data.upstream_protocol.toLowerCase().indexOf(keyword) > -1
                            || _this.getModeObj(data).modeText.toLowerCase().indexOf(keyword) > -1) {

                            resultList.push(data);

                        } else {
                            var serverLen = data.ipList.length;
                            if (serverLen == 0) continue;
                            for (var j = 0; j < serverLen; j++) {
                                var d = data.ipList[j];
                                if (!d) return false;
                                if (d.ip.toLowerCase().indexOf(keyword) > -1 || d.port.toLowerCase().indexOf(keyword) > -1) {
                                    resultList.push(data);
                                    break;
                                }
                            }
                        }
                    }
                }

                _table.data = resultList;
                if (callback) callback(resultList);

            }, 500);
        }

        _this.sortList = function(evt) {
            var sortKey = evt.key;
            var isAsc = evt.sort == 'asc';

            // 更新当前table的数据。当有过滤条件时，table.data可能不等于_siteList，因此需要分别更新table.data和_siteList
            var curTableDatas = _table.data;
            _sortUpstreamList(curTableDatas, sortKey, isAsc);
            _table.data = curTableDatas;

            // 更新原始数据
            _sortUpstreamList(_siteList, sortKey, isAsc);
        }


        _this.getConnectionStatus = _getConnectionStatus;

        _this.getModeObj = function(rowData) {
            var type = '';
            if (rowData.enabled) {
                type = rowData.learning_mode ? ModeType.MONITOR : ModeType.BLOCK;
            } else {
                type = ModeType.TRANSPARENT;
            }

            var modeObj = Object.assign({}, ModeMap[type]);
            modeObj.className += ' statusBox';
            return modeObj;
        }

        function _sortUpstreamList(list, sortKey, isAsc) {
            // Chrome69 - v8
            if (isChrome) {
                list.forEach(function(item, index) {
                    if (item) item.oldIndex = index;
                });
            }

            list.sort(function(site1, site2) {
                var comp1 = site1[sortKey];
                var comp2 = site2[sortKey];
                if (sortKey === 'dname') {
                    comp1 = site1.isEmptyName ? '':comp1;
                    comp2 = site2.isEmptyName ? '':comp2;

                } else if (sortKey === 'status') {
                    comp1 = _this.getModeObj(site1).type;
                    comp2 = _this.getModeObj(site2).type;
                }

                if (comp1 > comp2) {
                    return isAsc ? 1 : -1;
                } else if (comp1 < comp2) {
                    return isAsc ? -1 : 1;
                } else {
                    if (isChrome) {
                        return site1.oldIndex - site2.oldIndex;     // Chrome69 - v8
                    } else {
                        return 0;
                    }
                }
            });
        }

        function _getConnectionStatus(status) {
            return status === 'test_pass';
        }

    }



    /**-------------------------- 主列表 --------------------------*/

    var MainListManager = (function() {

        // createHeaderCell在init前执行了，因此instance只能在此创建，不能放到init中创建
        var _mainInstance = new ProtectSiteListClass();
        var _mainWrapper = null;
        var _mainTable = null;

        return {
            // 初始化绑定事件
            init: function() {
                _mainWrapper = rs('#configProxyList');
                _mainTable = rs('#mainList');
                _mainInstance.init(_mainTable, utils.deepCopyObject(upstreamList));

                // 视图
                var curTableView = g_const.proxy.use_cardview_as_default_display ? (tableView ? tableView : TABLE_VIEW_TYPE.CARD) : TABLE_VIEW_TYPE.LIST;
                _mainWrapper.attr('tableView', curTableView);

                // 填充数据
                document.body.fill({
                    upstreamList: _mainInstance.siteList,
                    upstreamCount: _mainInstance.siteCount,
                    upstreamSearchKey: ''    // FireFox\Chrome: Choose to remember the account/password and avoid the automatic filling of the input tag
                });


                // table排序事件
                _mainTable.bind('sortList', _mainInstance.sortList);

                // 搜索关键字事件
                var evtName = Sys.ie ? 'keydown' : 'input';
                rs('#__SEARCH_UPSTREAM__').bind(evtName, function(evt) {
                    evt.stopPropagation();
                    _mainInstance.searchKeyword(evt, function(resultList) {
                        rs('div[name=proxyConfigBtns] span[name=upstreamCount]').textContent = resultList.length;
                    });
                });
            },

            // 顶部操作按钮
            changeFullFlowCollection: changeFullFlowCollection,
            createNewSite: createNewSite,
            finishImport: finishImport,
            redirectGlobalSettings: redirectGlobalSettings,
            changeView: changeView,

            // 构建table
            createHeaderCell: createHeaderCell,
            createRow: createRow,
            createEmptyRow: createEmptyRow,
            markServerHealthFlag: markServerHealthFlag,
            getModeObj: function(rowData) {
                return _mainInstance.getModeObj(rowData);
            },
            getConnectionStatus: function(status) {
                return _mainInstance.getConnectionStatus(status);
            },

            // table中的按钮操作
            changeStatus: changeStatus,
            downLoadUpstream: downLoadUpstream,
            redirectProxyDetail: redirectProxyDetail,
            deleteUpstream: deleteUpstream,

            // table中的hover态
            mouseoverWebsite: mouseoverWebsite,
            mouseoutWebsite: mouseoutWebsite,
            mouseoverServerList: mouseoverServerList,
            mouseoutServerList: mouseoutServerList,
            mouseoverEnableSwitch: mouseoverEnableSwitch,
            mouseoutEnableSwitch: mouseoutEnableSwitch,

            // 站点名称
            SiteName: {
                blur: function(evt) {
                    SiteNameManager.blur(evt, _mainInstance);
                },
                save: function() {
                    SiteNameManager.save(_mainInstance);
                } 
            }
        };



        /**------------- 顶部操作按钮 -------------*/

        // 开启全流量采集
        function changeFullFlowCollection(evt) {
            var elem = evt.currentTarget;
            service.setFullFlowCollection({
                'enable_full_flow_collection': elem.checked
            }, function(res) {
                if (res.status === 'success') {
                    rsalert(res.message);
                } else {
                    elem.checked = !elem.checked
                    rsalert(res.message);
                }
            });
        }

        // 添加
        function createNewSite() {
            var isNotAllowCreate = maxProxies > 0 && upstreamCount >= maxProxies;
            if (isNotAllowCreate) {
                rsalert(rs.formatString("{% trans 'Current website number has already reached the ceiling ({0}), You can not add any more website.' %}", maxProxies));
            } else {
                window.location = '/proxy/detail/?action=newServer';
            }
        }

        // 上传
        function finishImport(evt) {
            var upstreamImportBtn = rs('#__IMPORT_UPSTREAM_BTN__');
            FileUploadResultHandler.process(evt.data, function(res) {
                if (res.result) {
                    upstreamImportBtn.redirectSubmit('/proxy/detail/', true);
                } else {
                    var fileName = rs('input[type=file][name=rawdata]').value.match(/[^\/]+$/i);
                    rsalert(rs.formatString("{% trans 'File {0} is empty' %}", fileName));
                    upstreamImportBtn.refresh();
                }
            });
        }

        // 全局设置
        function redirectGlobalSettings(){
            location = '/proxy/global_settings/';
        }

        // 切换视图
        function changeView(evt) {
            tableView = tableView == TABLE_VIEW_TYPE.CARD ? TABLE_VIEW_TYPE.LIST : TABLE_VIEW_TYPE.CARD;
            _mainWrapper.attr('tableView', tableView);
            service.changeTableView({ 'table_view': tableView });
        }



        /**------------- 构建table -------------*/

        // 创建header
        function createHeaderCell(evt) {
            switch(evt.index) {
                case 0:
                case 1:
                case 2:
                    evt.preventDefault();
                    _mainInstance.createSortHeaderCell(evt);
                    break;

                case 3:
                    {% if not isProxyRole %}
                    evt.element.attr('rs\:visible', false);
                    {% endif %}
                    break;
            }
        }

        // 创建Row
        function createRow(evt) {
            var row = evt.element;
            var data = evt.data;
            _mainInstance.markRow(row, data);

            row
                .bind('click', function(evt) {
                    evt.stopPropagation();
                    _handleClickUpstreamCard(evt, row, data);
                })
                .bind('mouseover', _handleMouseOverUpstream)
                .bind('mouseout', _handleMouseOutUpstream);
        }

        function _handleMouseOverUpstream(evt) {
            evt.stopPropagation();
            if (!canChangeSiteName) return;
            var elem = evt.currentTarget.named('dname');
            if (elem.hasAttribute('hover') || elem.hasAttribute('edit')) return;
            elem.attr({ 'hover': '' });
        }

        function _handleMouseOutUpstream(evt) {
            evt.stopPropagation();
            if (!canChangeSiteName) return;
            evt.currentTarget.named('dname').attr({ 'hover': null });
            page.titleTipHide();
        }

        function _handleClickUpstreamCard(evt, row, data) {
            if (evt.target.attr('table-cell') === null) return;
            if (!_isCardView()) return;
            var dnameElem = row.named('dname');
            if (dnameElem.hasAttribute('edit')) return;
            if (isPreventRedirect) {
                isPreventRedirect = false;
                return;
            }
            redirectProxyDetail(data.key);
        }


        // 空数据
        function createEmptyRow(evt) {
            var resultWrapper = rs.Element('div').html('');
            var keywordWrapper = rs.Element('h3').attr('class','tableEmptyKeyword').html('{% trans "No websites have been added yet." %}');
            var desWrapper = rs.Element('p').attr('class','tableEmptyDes').html(canCreate ? '{% trans 'Click "Add Websites" to configure websites that need protection.' %}' : '');

            var keyword = rs('#__SEARCH_UPSTREAM__').value.trim();
            if (keyword) {
                if (g_const.vertical_layout) {
                    keywordWrapper.html('').addClass('tableSearchEmpty');
                    desWrapper.textContent = rs.formatString("{% trans "No result matches '{0}'" %}", keyword);
                } else {
                    keywordWrapper.html('');
                    keywordWrapper.textContent = keyword;
                    desWrapper.textContent = '{% trans "No results found." %}';
                }
            }

            resultWrapper.append(keywordWrapper).append(desWrapper);
            evt.result = resultWrapper;
        }

        // 标记上游服务器健康状态
        function markServerHealthFlag(healthStatus) {
            switch(healthStatus) {
                case 'up':
                    return 'heath-status-icon online-icon';

                case 'down':
                    return 'heath-status-icon error-icon';

                case 'off':
                    return 'heath-status-icon offline-icon';

                default:
                    return '';
            }
        }




        /**------------- table列中的操作 -------------*/

        // 修改状态
        function changeStatus(evt, serverKey) {
            var isChecked = evt.target.checked;
            var msg = isChecked ? "{% trans 'Are you sure to enable the current site configuration?' %}"
                : "{% trans 'Are you sure to disable the current site configuration?' %}";

            ask(msg, function() {
                window.service.enableSiteConf(serverKey, isChecked, function ok(response){
                    if (response.result == 'failed') {
                        evt.target.checked = !isChecked;
                        rsalert(response.message);
                    } else {
                        window.location = '/proxy';
                    }
                }, function error(){
                    rsalert("{% trans 'Operation failed' %}");
                    evt.target.checked = !isChecked;
                });

            }, function() {
                evt.target.checked = !isChecked;
            });
        }

        // 下载
        function downLoadUpstream(serverKey) {
            window.service.downloadUpstream(serverKey);
        }

        // 点击网站域名，跳转进入站点编辑页
        function redirectProxyDetail(key) {
            location = '/proxy/detail/?action=show&serverKey='+key;
        }

        // 删除
        function deleteUpstream(evt, rowData) {
            evt.stopPropagation();
            if (rowData.Extra_Business_Data.length == 0) {
                _deleteService(rowData);
            } else {
                rsalert('{% trans 'Key business of this website has been configured in "Business Threat Awareness". You need to delete the key business in the BTA page before delete the website.' %}');
            }
        }

        function _deleteService(data) {
            var upstreamKey = data.key;
            var name = data.name;
            var msg = '';

            appConfigList.some(function(appConfig) {
                var siteMap = appConfig.siteMap;
                var apps = siteMap[upstreamKey];
                if (apps && apps.length > 0) {
                    msg = appConfig.getMsg(apps.join('<br/>'));
                    return;
                }
            });

            msg += rs.formatString("{% trans 'Are you sure that you want to permanently delete configuration: {0}' %}", name);
            ask(msg, function() {
                window.service.deleteProtectedWebsite(upstreamKey, name, function ok(response){
                    window.location = '/proxy';
                }, function error(){
                    rsalert("{% trans 'Operation error!' %}");
                });
            });
        }


        /**------------- hover信息 -------------*/

        function mouseoverWebsite(evt, rowData) {
            if (rowData.enable_site_conf != false) {
                evt.stopPropagation();
                var isConnect = _mainInstance.getConnectionStatus(rowData.status);
                var tipMsg = '';
                if (!isConnect) {
                    if (rowData.test_output) {
                        if (rowData.test_output == 'Please upload valid RSA private key file.') {
                            tipMsg = tipMsg + '<br/>' + '{% trans "Please upload valid RSA private key file." %}';
                        } else {
                            tipMsg = tipMsg + '<br/>' + escapeHtml(rowData.test_output).replace(/&lt;br\/&gt;/g, '<br/>');
                        }
                    } else {
                        tipMsg = '{% trans "Current upstream is not working properly." %}';
                    }
                }

                if (_isCardView()) {
                    var txt = rowData.upstream_protocol + rowData.name;
                    tipMsg = tipMsg ? txt+'<br/><br/>'+tipMsg : txt;
                } else if (isConnect) {
                    return;
                }

                var baseX = g_const.vertical_layout ? evt.pageX : evt.clientX;
                var baseY = g_const.vertical_layout ? evt.pageY : evt.clientY;
                page.titleTip(baseX, baseY, tipMsg);
            }
        }

        function mouseoutWebsite(evt) {
            page.titleTipHide();
        }

        function mouseoverServerList(event, rsList, rowData) {
            if (rowData.enable_site_conf != false) {
                event.stopPropagation();
                var x = g_const.vertical_layout ? event.pageX : event.clientX;
                var y = g_const.vertical_layout ? event.pageY : event.clientY;
                y = y - event.target.clientHeight - event.offsetY;
                y = g_const.vertical_layout ? y-60 : y+20;

                page.titleTip(x, y, _isAllServers(rowData) ? '{% trans "Protect all servers" %}' : rsList.outerHTML);
            }
        }

        function mouseoutServerList(event) {
            event.stopPropagation();
            page.titleTipHide();
        }

        function mouseoverEnableSwitch(event) {
            var s = document.getElementById("enableSiteConf");
            if (s) {
                event.stopPropagation();
                var tipX = g_const.vertical_layout ? event.pageX : event.clientX;
                var tipY = g_const.vertical_layout ? event.pageY : event.clientY;
                var msg = '{% trans "When switched off, this site configuration will be disabled and site service no longer available." %}';
                page.titleTip(tipX, tipY, msg);
            }
        }

        function mouseoutEnableSwitch(event) {
            event.stopPropagation();
            page.titleTipHide();
        }


        /**- 私有方法 -*/

        function _isAllServers(rowData) {
            return matchDeployMode('mirror|transparent') && rowData.ipList.length == 0;
        }

        function _isCardView() {
            return tableView === TABLE_VIEW_TYPE.CARD;
        }
    })();




     /**-------------------------- 一键切换弹框列表 --------------------------*/

    var OneClickSwitchManager = (function() {
        var instance = new ProtectSiteListClass();
        var _dialog = null;
        var _table = null;
        var _keywordElem = null;

        var _allCheckbox = null;
        var _checkedCountElem = null;
        var _okBtn = null;
        var _checkedCount = 0;

        var _curModeConfig = null;

        return {
            init: function() {
                _dialog = rs('#__BATCH_PROXY_LIST_DAILOG__');
                _table = rs('#__BATCH_SITE_LIST__');
                _keywordElem = rs('#__SEARCH_UPSTREAM_IN_DIALOG__');
                _okBtn = _dialog.named('ok');

                // table排序事件
                _table.bind('sortList', instance.sortList);

                // 搜索关键字事件
                var evtName = Sys.ie ? 'keydown' : 'input';
                _keywordElem.bind(evtName, instance.searchKeyword);
            },

            openOneclickDialog: openOneclickDialog,
            createOneclickHeaderCell: createOneclickHeaderCell,
            createOneclickRow: createOneclickRow,
            clickSingleCheckbox: clickSingleCheckbox,
            refreshOneclickTableDatas: refreshOneclickTableDatas,
            save: save,
            cancel: cancel,
            getModeObj: function(rowData) {
                return instance.getModeObj(rowData);
            },
        };


        // 打开弹框
        function openOneclickDialog(oneClickType) {
            rs('#oneClickDropBox').expand = false;

            _curModeConfig = ModeMap[oneClickType];

            _dialog.addClass('loading');

            // 打开弹框
            var title = _curModeConfig.oneclickText;
            _dialog.oneClickType = oneClickType;
            _dialog.open(null, null, null, title);
            _okBtn.textContent = title;
            if (_checkedCountElem === null) {
                _checkedCountElem = rs.Element('span').css({ margin: '0px 2px' });
            }
            _okBtn.append(_checkedCountElem);
            _updateCheckedCounter(0);
            _dialog.querySelector('#oneClickTableWrapper').scrollTop = 0;


            // 初始化table：防止阻塞dialog弹出，此处使用setTimeout
            setTimeout(function() {
                var tableDatas = _getInitDatas(oneClickType);
                instance.init(_table, utils.deepCopyObject(tableDatas));

                _dialog.data = {
                    siteList: instance.siteList,
                    upstreamCount: instance.siteCount
                };
                _dialog.removeClass('loading');
            }, 100);

        }

        function _getInitDatas(oneClickType) {

            var tableDatas = [];
            var _excludingDisableList = [];
            var _excludingBlockList = [];
            var _excludingMonitorList = [];
            var _excludingTransparentList = [];

            // 分类数据
            upstreamList.forEach(function(rowData) {
                // 重置所有checkbox状态
                rowData.checked = false;

                if (rowData.enable_site_conf !== false) {
                    // 未关闭的站点
                    _excludingDisableList.push(rowData);
                }


                var type = instance.getModeObj(rowData).type;

                // 非拦截模式的站点列表
                if (type !== ModeType.BLOCK) _excludingBlockList.push(rowData);

                // 非监控模式的站点列表
                if (type !== ModeType.MONITOR) _excludingMonitorList.push(rowData);

                // 非透传模式的站点列表
                if (type !== ModeType.TRANSPARENT) _excludingTransparentList.push(rowData);
            });


            switch(oneClickType) {
                case ModeType.DISABLE:
                    tableDatas = _excludingDisableList;
                    break;

                case ModeType.TRANSPARENT:
                    tableDatas = _excludingTransparentList;
                    break;

                case ModeType.MONITOR:
                    tableDatas = _excludingMonitorList;
                    break;

                case ModeType.BLOCK:
                    tableDatas = _excludingBlockList;
                    break;
            }

            return tableDatas;
        }


        function createOneclickHeaderCell(evt) {
            switch(evt.index) {
                case 0:
                    evt.preventDefault();
                    _allCheckbox = createCheckbox()
                        .appendTo(evt.element)
                        .bind('change', _clickAllCheckbox);
                    break;

                case 1:
                case 2:
                case 3:
                    evt.preventDefault();
                    evt.element.attr('name', '_' + evt.key);
                    instance.createSortHeaderCell(evt);
                    break;

                case 4:
                    {% if not isProxyRole %}
                    evt.element.attr('rs\:visible', false);
                    {% endif %}
                    break;
            }
        }

        function createOneclickRow(evt) {
            var rowData = evt.data;
            var row = evt.element
                .css({ cursor: 'pointer' })
                .bind('click', function(evt) {
                    var isChecked = !rowData.checked;
                    row.child('rs\\:Checkbox').checked = isChecked;
                    _selectRow(isChecked, rowData);
                });

            instance.markRow(row, rowData);
        }


        function clickSingleCheckbox(evt) {
            var cbElem = evt.currentTarget;
            var isChecked = cbElem.checked;
            var rowData = cbElem.parent('div[row=row]').data;
            _selectRow(isChecked, rowData);
        }

        function _selectRow(isChecked, rowData) {
            _checkedCount += isChecked ? 1 : -1;
            rowData.checked = isChecked;
            _updateOriginRowDataChecked(rowData, isChecked);
            _updateCheckedCounter();
            _updateAllCheckboxStatus(_checkedCount, _table.data.length);
        }

        function save() {
            var keys = [];
            instance.siteList.forEach(function(d) {
                if (d.checked) {
                    keys.push(d.key);
                }
            });

            ask(rs.formatString('{% trans "Are you sure you want to switch the selected website(s) to {0}?" %}', _curModeConfig.modeText.toLowerCase()), function() {
                page.wait(true);
                var data = {
                    action: _dialog.oneClickType,
                    sites: keys
                };
                service.post('/proxy/one_click_switch/', {
                    dataType: 'json',
                    data: data,
                    mask: false,    // 关闭接口自动启停loading，改用手动控制，页面刷新前使其loading一直存在
                    success: function(resp) {
                        if (resp.status === 'success') {
                            refreshCurLocation();
                            if (embedMode) page.wait(false);
                        } else {
                            rsalert(resp.message || '{% trans "Failed to switch" %}');
                            page.wait(false);
                        }

                    },
                    error: function() {
                        page.wait(false);
                        rsalert("{% trans 'Network Error!' %}");
                    }
                });
            });
        }

        function cancel() {
            _dialog.closeDialog();

            // 防止阻塞弹框关闭，此处使用setTimeout
            setTimeout(function() {
                _restoreDialog();
            }, 0);
        }

        // 重置弹框内容
        function _restoreDialog() {
            _checkedCount = 0;
            _keywordElem.value = '';
            instance.siteList.forEach(function(site) {
                site.checked = false;
            });

            _updateCheckedCounter(_checkedCount);
            _updateAllCheckboxStatus(_checkedCount, instance.siteList.length);
            instance.restoreDefaultSortCell();
        }


        // table.data = 赋值时会触发此方法（初始化、过滤、排序），此时需要更新全选checkbox状态
        function refreshOneclickTableDatas() {
            var curTableDatas = instance.table.data;
            var curRowCount = curTableDatas.length;
            var checkedCount = 0;

            if (curRowCount > 0) {
                curTableDatas.forEach(function(d) {
                    if (d.checked) checkedCount += 1;
                });
            }

            _updateAllCheckboxStatus(checkedCount, curRowCount);
        }

        // 全选
        function _clickAllCheckbox(evt) {
            var isNewChecked = evt.target.checked;
            rs.each(_table.rows, function(index, row){
                // 更新checkbox状态和勾选计数
                var checkbox = row.child('rs\\:CheckBox');
                var isOldChecked = checkbox.checked;
                if (isNewChecked) {
                    // 勾选全部：若当前项本来就是勾选的，则不管；若未勾选，则状态就需变为勾选且勾选计数+1
                    if (isOldChecked === false) {
                        checkbox.checked = isNewChecked;
                        _checkedCount += 1;
                    }
                } else {
                    // 取消勾选全部：只有在当前列表项全都勾选的情况，才会出现“取消勾选全部”，因此逐个变更checkbox状态且勾选计数-1即可
                    checkbox.checked = isNewChecked;
                    _checkedCount -= 1;
                }

                // 更新原始值
                _updateOriginRowDataChecked(row.data, isNewChecked);
            });

            _updateCheckedCounter(_checkedCount);
        }


        // 更新原始数据
        function _updateOriginRowDataChecked(rowData, isChecked) {
            instance.siteList.some(function(site) {
                if (site.key === rowData.key) {
                    site.checked = isChecked;
                    return true;
                }
            });
        }


        // 更新全选按钮的状态
        function _updateAllCheckboxStatus(checkedCount, rowCount) {
            if (checkedCount === 0 || rowCount === 0) {
                _allCheckbox.disabled = rowCount === 0;
                _allCheckbox.checked = false;

            } else {
                _allCheckbox.disabled = false;
                if (checkedCount == 0) {
                    _allCheckbox.checked = false;
                } else if (checkedCount >= rowCount) {
                    _allCheckbox.checked = true;
                } else {
                    _allCheckbox.checkedPart = true;
                }
            }
        }

        // 更新确认按钮的勾选计数器的值
        function _updateCheckedCounter(checkedCount) {
            if (!_checkedCountElem) return;

            var count = checkedCount;
            if (checkedCount === undefined) {
                count = instance.siteList.filter(function(d) {
                    return d.checked === true;
                }).length;
            }

            _checkedCountElem.textContent = rs.formatString('{% trans " ({0} selected)" %}', count);
            if (count > 0) {
                _checkedCountElem.style.display = 'inline-block';
                _okBtn.attr('disabled', null);
            } else {
                _checkedCountElem.style.display = 'none';
                _okBtn.attr('disabled', '');
            }
        }

    })();
</script>
{% endblock %}
