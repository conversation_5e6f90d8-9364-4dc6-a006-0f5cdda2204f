{% load nav_tags %}
{% load i18n %}


<rs:Style smart>

.llm_option_detail {

    li {

        label {
            width: 100px;
            text-align: right;
            margin-right: 10px;
            display: inline-block;
        }

        span {
            color: gray;
        }

        .long_string {
            width: 70%;
            display: inline-block;
            word-break: break-word;
            word-wrap: break-word;
            vertical-align: text-top;
        }

        ul {
            padding: 0px;
        }
    }
},

.siteItem {
    line-height: 20px;
    color: #666;
    border: 1px solid #d9d9d9;
    background-color: white;
    padding: 0px 8px;
    margin: 2px;
    border-radius: 5px;
}

.piiItem {
    display: inline-block;
    line-height: 20px;
    background-color: #666;
    color: white;
    padding: 0px 8px;
    margin: 2px;
    border-radius: 5px;
}

html[lang=en] .llm_option_detail li label {
    width: 200px;
}

</rs:Style>



<section>
    <header>
        <span>{% trans 'Choose Prompt Injection Protection strategy' %}</span>
    </header>
    <ul class="llm_option_detail">
        <li>
            <label>{% trans 'Rule Set' %}:</label>
            <rs:Select id="llm_prompt_injection_strategy" 
                name="llm_prompt_injection_strategy"
                onchange="LLMProtectionPage.onSelect(event, 'promptInjection')"></rs:Select>
        </li>
        <li id="prompt_injection_detail"
            v:show="mPage.llm_prompt_injection_strategy && mPage.llm_prompt_injection_strategy !== '-1'">
            <ul>
                <li>
                    <label>ID:</label>
                    <span name="id"></span>
                </li>
                <li>
                    <label>{% trans 'Name' %}:</label>
                    <span name="name"></span>
                </li>
                <li>
                    <label>{% trans 'Descriptions' %}:</label>
                    <span class="long_string" name="comment"></span>
                </li>
                <li>
                    <label>{% trans 'Risk Threshold' %}:</label>
                    <span name="prompt_injection__strictness_level"></span>&nbsp;/&nbsp;<span>100</span>
                </li>
                <li>
                    <label>{% trans 'Message Roles' %}:</label>
                    <span name="prompt_injection__detect_roles"></span>
                </li>
                <li v:if="!isMirrorMode">
                    <label>{% trans 'Chinese Interception Response' %}:</label>
                    <span class="long_string" name="prompt_injection__response_templates__zh"></span>
                </li>
                <li v:if="!isMirrorMode">
                    <label>{% trans 'English Interception Response' %}:</label>
                    <span class="long_string" name="prompt_injection__response_templates__en"></span>
                </li>
            </ul>
        </li>
    </ul>
</section>


<section  rs:visible="!isApi">
    <header>
        <span>{% trans 'Choose LLM Sensitive Information Protection strategy' %}</span>
    </header>
    <ul class="llm_option_detail">
        <li>
            <label>{% trans 'Rule Set' %}:</label>
            <rs:Select id="llm_sensitive_detection_strategy" 
                name='llm_sensitive_detection_strategy'
                onchange="LLMProtectionPage.onSelect(event, 'sensitiveDetection')"></rs:Select>
        </li>
        <li id="sensitive_detection_detail"
            v:show="mPage.llm_sensitive_detection_strategy && mPage.llm_sensitive_detection_strategy !== '-1'">
            <ul>
                <li>
                    <label>ID:</label>
                    <span name="id"></span>
                </li>
                <li>
                    <label>{% trans 'Name' %}:</label>
                    <span name="name"></span>
                </li>
                <li>
                    <label>{% trans 'Descriptions' %}:</label>
                    <span class="long_string" name="comment"></span>
                </li>
                <li v:if="!isMirrorMode">
                    <label>{% trans 'Masking' %}:</label>
                    <span name="sensitive_detection__anonymization__enabled"></span>
                </li>
                <li name="sensitive_mask_character_li" style="display: none;">
                    <label>{% trans 'Masking Symbol' %}:</label>
                    <span name="sensitive_detection__anonymization__mask_character"></span>
                </li>
                <li>
                    <label>{% trans 'PII Types' %}:</label>
                    <span name="sensitive_detection__pii_types"></span>
                </li>
            </ul>
        </li>
    </ul>
</section>



<script src="/static/js/pages/waf/waf_common.js"></script>
<script language="javascript">
    
    var LLMProtectionPage = (function($) {
        
        var upstreamConf = $.upstreamConf, 
        mPage = $.mPage;

        rs.merge(mPage, {
            llm_prompt_injection_strategy: upstreamConf.llm_prompt_injection_strategy || '-1',
            llm_sensitive_detection_strategy: upstreamConf.llm_sensitive_detection_strategy || '-1'
        });

        var promptOption = [];
        var sensitiveOption = [];
        var piiOptions = [
            { label: '{% trans "UnionPay Debit Card" %}', value: 'UnionPay_Debit_Card' },
            { label: '{% trans "1st-Gen ID Card" %}', value: 'First_Generation_Chinese_ID_Card' },
            { label: '{% trans "2nd-Gen ID Card" %}', value: 'Second_Generation_Chinese_ID_Card' },
            { label: '{% trans "Mobile Phone" %}', value: 'Chinese_Phone_Number' },
            { label: '{% trans "Credit Card" %}', value: 'Credit_Cards' }
        ];

        return {

            init: function() {
                service.getLLMSecurityProtectionStrategy(function (res) {
                    rs('#llm_prompt_injection_strategy').option = promptOption = toOptionList(res.llm_prompt_injection_info);
                    rs('#llm_sensitive_detection_strategy').option = sensitiveOption = toOptionList(res.llm_sensitive_detection_info);
                });
            },

            checkList: function() {
                return [];
            },

            onSelect: function (event, category) {
                // category: promptInjection or sensitiveDetection
                var fromOptionList = category === 'promptInjection' ? promptOption : sensitiveOption;
                var optionItem = fromOptionList.find(function (item) {
                    return item.key === event.data;
                });
                
                if (optionItem && optionItem.raw) {
                    var flatten = new FlatDataConverter().flatten(optionItem.raw);
                    flatten.hosts = layoutArray(flatten.hosts);
                    if(flatten.prompt_injection__detect_roles) {
                        flatten.prompt_injection__detect_roles = getMultiSelectText(flatten.prompt_injection__detect_roles, MESSAGE_ROLES);
                    }
                    if (category === 'sensitiveDetection') {
                        flatten.sensitive_detection__pii_types = layoutPIIType(flatten.sensitive_detection__pii_types);
                        if(flatten.sensitive_detection__anonymization__enabled && !isMirrorMode) {
                            rs('#sensitive_detection_detail').named('sensitive_mask_character_li').show();
                        } else {
                            rs('#sensitive_detection_detail').named('sensitive_mask_character_li').hide();
                        }
                        flatten.sensitive_detection__anonymization__enabled = flatten.sensitive_detection__anonymization__enabled ? '{% trans "Enabled" %}' : '{% trans "Disabled" %}';
                    }
                    flatten.comment = utils.escapeHtml(flatten.comment);
                    var output = category === 'promptInjection' ? '#prompt_injection_detail' : '#sensitive_detection_detail';
                    rs(output).fill(flatten);
                }
            }
        };

        function layoutArray(array) {
            if (array.length) {
                for (var i = 0; i < array.length; i++) {
                    array[i] = '<div class="siteItem">' + array[i] + '</div>';
                }
                return array.join('');
            } else {
                return '--';
            }
        }

        function toOptionList(list) {
            var options = [{key: '-1', value: '{% trans "Close" %}'}];
            for (var i = 0; i < list.length; i++) {
                options.push({
                    value: list[i].name,
                    key: String(list[i].id),
                    raw: list[i]
                });
            }
            return options;
        }


        function layoutPIIType(array) {
            var output = [];
            for (var i = 0; i < array.length; i++) {
                output.push('<div class="piiItem">' + translatePIIType(array[i]) + '</div>');
            }
            return output.join('');
        }

        function translatePIIType(value) {
            var targetOption = piiOptions.find(function (item) {
                return item.value === value;
            });
            return targetOption ? targetOption.label : value;
        }

    })(app);
    var isApi = isApiProduct(global_params.productType)

    modules.push(LLMProtectionPage);

    if (!Array.prototype.find) {
        Array.prototype.find = function(predicate) {
            if (this == null) {
                throw new TypeError('Array.prototype.find called on null or undefined');
            }
            if (typeof predicate !== 'function') {
                throw new TypeError('predicate must be a function');
            }
            var list = Object(this);
            var length = list.length >>> 0;
            var thisArg = arguments[1];
            for (var i = 0; i < length; i++) {
                var value = list[i];
                if (predicate.call(thisArg, value, i, list)) {
                    return value;
                }
            }
            return undefined;
        };
    }
</script>
