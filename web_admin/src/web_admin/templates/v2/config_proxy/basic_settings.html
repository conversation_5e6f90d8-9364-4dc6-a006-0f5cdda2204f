{% load nav_tags %}
{% load i18n %}


<!--保护模式-->
<section class="basicSettings">

    <header><span>{% trans 'Mode' %}</span></header>

    <div class="modeGroup">
        <rs:ProtectModeGroup key="optionsRadiosinline"></rs:ProtectModeGroup>

        <ul class="labelGroup optionGroup">
            <li rs:visible="{% visibleLayout 'web_primary_protection' %} && global_params.isWaflayoutShowItem && !matchDeployMode('mirror')">
                <rs:Label><rs:Checkbox name="web_primary_protection"
                                       rsid="web_primary_protection"
                                       onChange="BasicMode.handleChangeWebStandardProtection(event)"></rs:Checkbox>{% trans 'Primary Web Protection' %}</rs:Label>
            </li>
            <li rs:visible="{% visibleLayout 'web_standard_protection' %}">
                <rs:Label><rs:Checkbox name="web_standard_protection"
                                       rsid="web_standard_protection"
                                       onChange="BasicMode.handleChangeWebStandardProtection(event)"></rs:Checkbox>{% trans 'Essential Web Protection' %}</rs:Label>
            </li>
            <li rs:visible="!matchDeployMode('plugin') && {% visibleLayout 'web_advanced_protection' %}">
                <rs:Label rs:visible="!isLicenseBeProtected || global_params.isDebug">
                    <rs:Checkbox name="web_advanced_protection"
                                 rsid="web_advanced_protection"
                                 onChange="BasicMode.handleChangeWebAdvancedProtection(event)"></rs:Checkbox>{% trans 'Power Web Protection' %}
                </rs:Label>
            </li>
            <li rs:visible="!matchDeployMode('mirror') && {% visibleLayout 'mobile_sdk_protection' %}">
                <rs:Label rs:visible="global_params.isDebug || global_params.isShowMobileApp">
                    <rs:Checkbox name="mobile_sdk_protection"
                                 rsid="mobile_sdk_protection"
                                 onChange="BasicMode.toggleTabContentByTileDisplay(event.target.checked, 'appProtection')"></rs:Checkbox>{% trans 'Mobile Protection' %}
                </rs:Label>
            </li>
            <li>
                <rs:Label rs:visible="isEnabledAdvancedWaf" rsid="injection_attack_label">
                    <rs:Checkbox name="injection_attack_interception"
                                       rsid="injection_attack_interception"
                                       onChange="BasicMode.toggleTabContentByTileDisplay(event.target.checked, 'wafProtection')"></rs:Checkbox><span>{% trans 'WAF' %}</span></rs:Label>
            </li>
            <li>
                <rs:Label rs:visible="isEnabledAdvancedWaf && isAiService">
                    <rs:Checkbox name="aiwaf_attack_interception"
                                 rsid="aiwaf_attack_interception"
                                 onChange="BasicMode.toggleTabContentByTileDisplay(event.target.checked, 'aiWafProtection')"></rs:Checkbox>{% trans 'AI-WAF' %}
                </rs:Label>
            </li>
            <li rs:visible="!matchDeployMode('plugin') &&isEnabledLLMProtection&& {% visibleLayout 'llm_protection' %}">
                <rs:Label rsid="llm_label">
                    <rs:Checkbox name="llm_protection"
                                 rsid="llm_protection"
                                 onChange="BasicMode.toggleTabContentByTileDisplay(event.target.checked, 'llmProtection')"
                                 ></rs:Checkbox><span>{% trans 'LLM Protection' %}</span></rs:Label>
            </li>
            <li rs:visible="{% visibleLayout 'wechat_mpp' %} && (global_params.isDebug || app.has_mpp_license) && !matchDeployMode('mirror')">
                <rs:Label style="margin-right: 0px;">
                    <rs:Checkbox name="wechat_mini_protection"
                                 rsid="wechat_mini_protection"
                                 v:checked="BasicMode.closeWechatMPP()"
                                 v:disabled="BasicMode.mppIsUnavaliable() || !auth('Edit_Website_Config', 'w')"
                                 onChange="BasicMode.mppOnChangeHandle(event, 'wechatMPP')">
                    </rs:Checkbox>{% trans 'WeChat Mini-Program Protection' %}
                </rs:Label>
                <i class="ic-info helpIcon" v:show="BasicMode.mppIsUnavaliable()"
                    onmouseenter="page.titleTip(event.pageX, event.pageY, '{% trans "Only https site or terminal to https, and not Regex site." %}')"
                    onmouseleave="page.titleTipHide()"></i>
            </li>
            <li rs:visible="{% visibleLayout 'alipay_mpp' %} && (global_params.isDebug || app.has_mpp_license) && !matchDeployMode('mirror')">
                <rs:Label style="margin-right: 0px;">
                    <rs:Checkbox name="alipay_mini_program_protection"
                                 rsid="alipay_mini_program_protection"
                                 v:checked="BasicMode.closeAlipayMPP()"
                                 v:disabled="BasicMode.mppIsUnavaliable() || !auth('Edit_Website_Config', 'w')"
                                 onChange="BasicMode.mppOnChangeHandle(event, 'alipayMPP')">
                    </rs:Checkbox>{% trans 'Alipay Mini Program Protection' %}
                </rs:Label>
                <i class="ic-info helpIcon" v:show="BasicMode.mppIsUnavaliable()"
                    onmouseenter="page.titleTip(event.pageX, event.pageY, '{% trans "Only https site or terminal to https, and not Regex site." %}')"
                    onmouseleave="page.titleTipHide()"></i>
            </li>
            <li rs:visible="{% visibleLayout 'mpaas_mpp' %} && (global_params.isDebug || app.has_mpp_license) && !matchDeployMode('mirror')">
                <rs:Label style="margin-right: 0px;">
                    <rs:Checkbox name="mpaas_mpp"
                                 rsid="mpaas_mpp"
                                 v:checked="BasicMode.closeMPaasMPP()"
                                 v:disabled="BasicMode.mppIsUnavaliable() || !auth('Edit_Website_Config', 'w')"
                                 onChange="BasicMode.mppOnChangeHandle(event, 'mpaasMPP')">
                    </rs:Checkbox>{% trans 'Mobile Mini Program Protection' %}
                </rs:Label>
                <i class="ic-info helpIcon" v:show="BasicMode.mppIsUnavaliable()"
                    onmouseenter="page.titleTip(event.pageX, event.pageY, '{% trans "Only https site or terminal to https, and not Regex site." %}')"
                    onmouseleave="page.titleTipHide()"></i>
            </li>
        </ul>
    </div>

    <div innerSection rs:visible="!matchDeployMode('mirror')">
        <ul>
            <li>
                <label style="width: auto;">{% trans 'Strategy' %}</label>
                <rs:Select name='invalid_action'
                           class="invalid_action_control"
                           rs:readonly="mPage.optionsRadiosinline != 'Open'">
                    <div rs-option value='redirect'>{% trans 'Redirect action' %}</div>
                    <div rs-option value='reject'>{% trans 'Reject' %}</div>
                    <div rs-option value='blank'>{% trans 'Blank' %}</div>
                    <div rs-option value='drop' v:if="!matchDeployMode('plugin')">{% trans 'Drop' %}</div>
                </rs:Select>
                <input rs:visible="mPage.invalid_action=='redirect'"
                       rs:readonly="mPage.optionsRadiosinline != 'Open'"
                       type="text"
                       name="entry_path"
                       onchange="BasicMode.formatEntryPath(event)"
                       placeholder="{% trans 'path' %}" />
                <i class="ic-info helpIcon"
                    onmouseenter="page.titleTip(event.pageX, event.pageY, '{% trans "This policy is only for invalid response policy processing in the request direction, not applicable to the response direction." %}')"
                    onmouseleave="page.titleTipHide()"></i>
            </li>
        </ul>
    </div>

    <script language="javascript">

    var BasicMode = (function($) {

        var mPage = $.mPage;
        var upstreamConf = $.upstreamConf;

        {% autoescape off %}
        var wechat_occupied_sites = {{ wechat_occupied_sites|to_json}};
        var alipay_mpp_occupied_sites = {{ alipay_mpp_occupied_sites|to_json}};
        var mpaas_mpp_occupied_sites = {{ mpaas_mpp_occupied_sites|to_json}};
        {% endautoescape %}

        rs.merge(mPage, {
            txsafe_on: matchDeployMode('mirror') || {{ txsafe_on|to_json }},
            learning_mode: matchDeployMode('mirror') || upstreamConf.learning_mode,
            invalid_action: upstreamConf.action || 'reject',
            entry_path: upstreamConf.EntryPath || '',
            optionsRadiosinline: matchDeployMode('mirror') ? PROTECT_MODE.LEARNING : PROTECT_MODE.OPEN
        });

        mergeTabBasicData();

        function formatEntryPath(event){
            mPage.entry_path = mPage.entry_path.trim();
            if(!/^\//.test(mPage.entry_path)) mPage.entry_path = '/' + mPage.entry_path;
        }

        function toggleTabContentByTileDisplay(isEnable, elemName) {
            if (g_const.proxy.is_tile_display) {
                var elem = rs('body').named(elemName);
                if (!elem) return;
                if (isEnable) {
                    elem.show();
                } else {
                    elem.hide();
                }
            }
        }

        function mergeTabBasicData() {
            initProtectModeRadioOption();
            var basicMode = upstreamConf.protected_list;
            if (g_const.is_close_standard_and_advanced_as_default || matchDeployMode('mirror')) {

                basicMode.web_standard_protection = basicMode.web_advanced_protection = false;
                if (isCreateServer && !isImport) {
                    basicMode.injection_attack_interception = true;
                }
            } else {
                if (!global_params.isDebug && 'web_advanced_protection' in basicMode)
                    basicMode.web_advanced_protection = !isLicenseBeProtected && basicMode.web_advanced_protection;
            }
            basicMode.web_primary_protection= global_params.protectedLevel=='LE' && basicMode.web_primary_protection

            rs.merge(mPage, basicMode);

            function initProtectModeRadioOption() {
                if (mPage.txsafe_on) {
                    mPage.optionsRadiosinline = mPage.learning_mode ? PROTECT_MODE.LEARNING : PROTECT_MODE.OPEN;
                } else {
                    mPage.optionsRadiosinline = PROTECT_MODE.CLOSE;
                }
            }
        }

        function popUpPromptMppUnchecked(e) {
            if (e.target && !e.target.checked) {
                if (e.target.name == 'wechat_mini_protection' && upstreamConf.protected_list.wechat_mini_protection && wechat_occupied_sites && wechat_occupied_sites[upstreamConf.key].length > 0) {
                    var msg = '{% trans "The change disable wechat mini program protection, the site will be automatically remove from wechat mini program protection, as follow: " %}' + '<br/><br/>' +
                            wechat_occupied_sites[upstreamConf.key].join('<br/>') + '<br/><br/>' +
                            '{% trans "If enable it again, you must add the site to mini program by manually." %}';
                    ask(msg, null, function() {e.target.checked = !e.target.checked});
                } else if (e.target.name == 'alipay_mini_program_protection' && upstreamConf.protected_list.alipay_mini_program_protection && alipay_mpp_occupied_sites && alipay_mpp_occupied_sites[upstreamConf.key].length > 0) {
                    var msg = '{% trans "The change disable alipay mini program protection, the site will be automatically remove from alipay mini program protection, as follow: " %}' + '<br/><br/>' +
                            alipay_mpp_occupied_sites[upstreamConf.key].join('<br/>') + '<br/><br/>' +
                            '{% trans "If enable it again, you must add the site to mini program by manually." %}';
                    ask(msg, null, function() {e.target.checked = !e.target.checked});
                } else if (e.target.name == 'mpaas_mpp' && upstreamConf.protected_list.mpaas_mpp && mpaas_mpp_occupied_sites && mpaas_mpp_occupied_sites[upstreamConf.key].length > 0) {
                    var msg = '{% trans "The change disable mobile mini program protection, the site will be automatically remove from mobile mini program protection, as follow: " %}' + '<br/><br/>' +
                            mpaas_mpp_occupied_sites[upstreamConf.key].join('<br/>') + '<br/><br/>' +
                            '{% trans "If enable it again, you must add the site to mini program by manually." %}';
                    ask(msg, null, function() {e.target.checked = !e.target.checked});
                }
            }
        }

        function mppOnChangeHandle(event, elemName) {
            toggleTabContentByTileDisplay(event.target.checked, elemName);
            popUpPromptMppUnchecked(event);
        }

        return {

            init: function() {
                mPage.listen('optionsRadiosinline', function(key, value){
                    switch(value){
                        case PROTECT_MODE.LEARNING:
                            mPage.txsafe_on = true;
                            mPage.learning_mode = true;
                            break;
                        case PROTECT_MODE.CLOSE:
                            mPage.txsafe_on = false;
                            mPage.learning_mode = false;
                            break;
                        default:
                            mPage.txsafe_on = true;
                            mPage.learning_mode = false;
                            break;
                    }
                });
                toggleTabContentByTileDisplay(mPage.web_standard_protection, 'essentialWebProtection');
                toggleTabContentByTileDisplay(mPage.web_advanced_protection, 'powerWebProtection');
                toggleTabContentByTileDisplay(mPage.mobile_sdk_protection, 'appProtection');
                toggleTabContentByTileDisplay(mPage.injection_attack_interception, 'wafProtection');
                toggleTabContentByTileDisplay(mPage.aiwaf_attack_interception, 'aiWafProtection');
                toggleTabContentByTileDisplay(mPage.llm_protection, 'llm_protection');
                toggleTabContentByTileDisplay(mPage.wechat_mini_protection, 'wechatMPP');
                toggleTabContentByTileDisplay(mPage.alipay_mini_program_protection, 'alipayMPP');
                toggleTabContentByTileDisplay(mPage.mpaas_mpp, 'mpaasMPP');
            },

            handleChangeWebStandardProtection: function(evt) {
                if(!evt.target.checked) {
                    mPage.web_advanced_protection = false;

                    // forceshield下, 取消【基础保护】时, 对应的高级保护模块也应该隐藏
                    toggleTabContentByTileDisplay(evt.target.checked, 'powerWebProtection');
                }

                toggleTabContentByTileDisplay(evt.target.checked, 'essentialWebProtection');
            },

            handleChangeWebAdvancedProtection: function(evt) {
                if(evt.target.checked) {
                    mPage.web_standard_protection = true;

                    // forceshield下, 取消【高级保护】时, 基础保护模块也应该隐藏
                    toggleTabContentByTileDisplay(evt.target.checked, 'essentialWebProtection');
                }

                toggleTabContentByTileDisplay(evt.target.checked, 'powerWebProtection');
            },

            toggleTabContentByTileDisplay: toggleTabContentByTileDisplay,

            formatEntryPath: formatEntryPath,

            mppIsUnavaliable: function () {
                var isUnavaliable = (function (){
                    if (mPage.server_name_type == 'Regex') {
                        return true;
                    }

                    if (mPage.terminal_enable) {
                        return mPage.is_terminal_https == false;
                    }

                    return mPage.is_https == false;
                })();

                return isUnavaliable;
            },

            closeWechatMPP: function() {
                var isUnavaliable = BasicMode.mppIsUnavaliable();
                return isUnavaliable ? false : mPage.wechat_mini_protection;
            },

            closeAlipayMPP: function() {
                var isUnavaliable = BasicMode.mppIsUnavaliable();
                return isUnavaliable ? false : mPage.alipay_mini_program_protection;
            },

            closeMPaasMPP: function() {
                var isUnavaliable = BasicMode.mppIsUnavaliable();
                return isUnavaliable ? false : mPage.mpaas_mpp;
            },

            mppOnChangeHandle: mppOnChangeHandle
        };

    })(app);
    function handleAutoDetectWebsocket(evt) {
        mPage.autoDetectWebsocket = evt.target.checked;
    }
</script>

</section>

<!--站点设置-->
<section>

    <header>
        <span>{% trans 'Website Settings' %}
            <rs:Switch id="enableSiteConf"
            name="enable_site_conf"
            v:if="auth('Edit_Website_Config', 'w')"
            v:disabled="!editable"
            style="margin-left: 20px"
            class="enableSiteConfSwitch"
            onmouseover="SiteName.mouseOverSiteConfSwitch(event)"
            onmouseout="SiteName.mouseOutSiteConfSwitch(event)"
            onchange="SiteName.UpdateSiteConfStatus(event)"></rs:Switch>
            <input type="hidden" name="enable_site_conf" v:value="mPage.enable_site_conf != false ? 'on' : 'off'" v:disabled="mPage.enable_site_conf != false"/>
        </span>
        <p>{% trans 'Specify the protocol, domain name and port number of the website to be protected.' %}</p>
    </header>

    <!--站点名称-->
    <div name="customizeNameWrapper">
        <ul>
            <li>
                <label style="width: auto">{% trans 'Website Name' %}</label>
                <input type="text"
                       name="site_customize_name"
                       v:placeholder="SiteName.getNamePlaceholder()"
                       rs:disabled="!auth('Edit_Website_Config', 'w')" />
            </li>
        </ul>
    </div>

    <!--目标网站协议域名和端口号-->
    <div class="inlineBlock siteInput">
        <rs:Select rsid="site_protocol"
                   name="site_protocol"
                   onchange="SiteName.handleChangeSiteProtocol(event)"
                   rs:disabled="!isCreateServer"
                   class="protocol-select">
            <div rs-option value="http://">http://</div>
            <div rs-option value="https://" v:if="!matchDeployMode('plugin')">https://</div>
        </rs:Select>
        <rs:Select rsid="server_name_type"
                   name="server_name_type"
                   onchange="SiteName.handleChangeServerNameType(event)"
                   rs:disabled="!isCreateServer">
            <div rs-option value="Domain">{% trans 'Domain' %}</div>
            <div rs-option value="IPv4">IPv4</div>
            <div rs-option value="IPv6">IPv6</div>
            <div rs-option value="Regex">{% trans 'Regex' %}</div>
        </rs:Select>
        <input name="action" type="hidden" value="{{action}}"/>
        <input name="is_https" type="hidden" />
        <input name="site_name" type="text" maxlength="110" />
        <input name="site_port" rs:disabled="matchDeployMode('plugin')" type="text" placeholder="{% trans 'Port' %}" port="port" class="inlineBlock" />
    </div>

    <div class="inlineBlock siteInput">
        <rs:Label>
            <rs:Checkbox id="enable_business_path" name="enable_business_path" v:readonly="!isCreateServer"></rs:Checkbox>
            {% trans 'Enable Business Path' %}
        </rs:Label>
        <span class="inlineBlock" rs:visible="mPage.enable_business_path">
            <input type="text" name="business_path" style="width: 500px;" v:readonly="!isCreateServer" placeholder="{% trans 'Regex Model' %}">
        </span>
    </div>

    <!--协议和端口-->
    <div class="inlineBlock siteInput" rs:visible="!matchDeployMode('mirror') && ({% visibleLayout 'terminal_port' %} && global_params.isWaflayoutShowItem)">
        <rs:Label><rs:Checkbox name="terminal_enable"></rs:Checkbox>{% trans 'Port Offset' %}</rs:Label>
        <span class="inlineBlock" rs:visible="mPage.terminal_enable">
            <rs:Select name="terminal_protocol" class="protocol-select">
                <div rs-option value="http://">http://</div>
                <div rs-option value="https://">https://</div>
            </rs:Select>
            <input type="hidden" name="is_terminal_https">
            <input type="text" name="terminal_port" placeholder="{% trans 'Port' %}" port="port">
        </span>
    </div>

    <!--国际标准证书 -->
    <div name="httpsCertWrapper" rs:visible="mPage.is_https">
        <ul rs:visible="!matchDeployMode('mirror')">
            <li>
                <label>{% trans 'Use international standard certificate' %}</label>
                <div style=" display: inline-block; vertical-align: top; max-width: 70%; ">
                    <rs:Checkbox rsid="useInternationalCert" name="useInternationalCert" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Checkbox>
                </div>
            </li>
        </ul>
        <ul rs:visible="mPage.useInternationalCert && !matchDeployMode('mirror')">
            <li>
                <label>{% trans 'Certificate type' %}</label>
                <span>
                    <rs:Label rsid="uploadCert">
                        <rs:Radio name="useBuiltInCert" value="false" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Radio>
                        {% trans 'Upload certificate' %}
                    </rs:Label>
                    <rs:Label rsid="buildInCert">
                        <rs:Radio name="useBuiltInCert" value="true" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Radio>
                        {% trans 'Built in certificate' %}
                    </rs:Label>
                </span>
            </li>
        </ul>

        <ul rs:visible="mPage.useBuiltInCert == 'false' && mPage.useInternationalCert">
            <li rs:visible="!matchDeployMode('mirror')">
                <label>{% trans 'Certificate file' %}</label>
                <rs:FileUpload id="__CERT_UPLOAD__" name="upload_file" action="/proxy/detail/getplaintext/"
                               onData="SiteName.handleGetUploadCertResult(event)" accept=".crt,.pem"
                               v:disabled="!auth('Edit_Website_Config', 'w')">
                    {% csrf_token %}
                </rs:FileUpload>
                <input type="hidden" name="cert_plain_text"/>
                <input type="hidden" name="cert_file_name"/>
            </li>
            <li>
                <label>{% trans 'Private key file' %}</label>
                <rs:FileUpload id="__CERT_KEY_UPLOAD__" name="upload_file" action="/proxy/detail/getplaintext/"
                               onData="SiteName.handleGetUploadPrivateKeyResult(event)" accept=".key"
                               v:disabled="!auth('Edit_Website_Config', 'w')">
                    {% csrf_token %}
                </rs:FileUpload>
                <input type="hidden" name="key_plain_text"/>
                <input type="hidden" name="key_file_name"/>
            </li>
        </ul>
    </div>

    <!--国密标准证书-->
    <div name="httpsChinaSecurityCertWrapper" rs:visible="mPage.is_https && !matchDeployMode('mirror')">
        <ul>
            <li>
                <label>{% trans 'Use China security standard certificate' %}</label>
                <div style=" display: inline-block; vertical-align: top; max-width: 70%; ">
                    <rs:Checkbox rsid="useChinaSecurityCert" name="useChinaSecurityCert" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Checkbox>
                </div>
            </li>
        </ul>

        <ul rs:visible="mPage.useChinaSecurityCert" >
            <li>
                <label>{% trans 'Certificate type' %}</label>
                <span>
                    <rs:Label rsid="uploadChinaSecurityCert">
                        <rs:Radio name="useBuiltInChinaSecurityCert" value="false" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Radio>
                        {% trans 'Upload certificate' %}
                    </rs:Label>
                    <rs:Label rsid="buildInChinaSecurityCert">
                        <rs:Radio name="useBuiltInChinaSecurityCert" value="true" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Radio>
                        {% trans 'Built in certificate' %}
                    </rs:Label>
                </span>
            </li>
        </ul>
        <ul rs:visible="mPage.useBuiltInChinaSecurityCert == 'false' && mPage.useChinaSecurityCert" >
            <li>
                <label>{% trans 'Signature certificate file' %}</label>
                <rs:FileUpload id="__GM_SIGN_CERT_UPLOAD__" name="upload_file" action="/proxy/detail/getplaintext/"
                               onData="SiteName.handleGetUploadCSSignCertResult(event)" accept=".crt,.pem"
                               v:disabled="!auth('Edit_Website_Config', 'w')">
                    {% csrf_token %}
                </rs:FileUpload>
                <input type="hidden" name="gm_sign_cert_plain_text"/>
                <input type="hidden" name="gm_sign_cert_file_name"/>
            </li>
            <li>
                <label>{% trans 'Signature private key file' %}</label>
                <rs:FileUpload id="__GM_SIGN_CERT_KEY_UPLOAD__" name="upload_file" action="/proxy/detail/getplaintext/"
                               onData="SiteName.handleGetUploadCSSignPrivateKeyResult(event)" accept=".key"
                               v:disabled="!auth('Edit_Website_Config', 'w')">
                    {% csrf_token %}
                </rs:FileUpload>
                <input type="hidden" name="gm_sign_key_plain_text"/>
                <input type="hidden" name="gm_sign_key_file_name"/>
            </li>
            <li>
                <label>{% trans 'Encryption certificate file' %}</label>
                <rs:FileUpload id="__GM_ENC_CERT_UPLOAD__" name="upload_file" action="/proxy/detail/getplaintext/"
                               onData="SiteName.handleGetUploadCSEncCertResult(event)" accept=".crt,.pem"
                               v:disabled="!auth('Edit_Website_Config', 'w')">
                    {% csrf_token %}
                </rs:FileUpload>
                <input type="hidden" name="gm_enc_cert_plain_text"/>
                <input type="hidden" name="gm_enc_cert_file_name"/>
            </li>
            <li>
                <label>{% trans 'Encryption private key file' %}</label>
                <rs:FileUpload id="__GM_ENC_CERT_KEY_UPLOAD__" name="upload_file" action="/proxy/detail/getplaintext/"
                               onData="SiteName.handleGetUploadCSEncPrivateKeyResult(event)" accept=".key"
                               v:disabled="!auth('Edit_Website_Config', 'w')">
                    {% csrf_token %}
                </rs:FileUpload>
                <input type="hidden" name="gm_enc_key_plain_text"/>
                <input type="hidden" name="gm_enc_key_file_name"/>
            </li>
        </ul>
    </div>

    <div name="http2Wrapper" rs:visible="mPage.is_https && page.expertMode && !matchDeployMode('mirror') && !mPage.source_port_keep_workable" style="margin-top: 20px;">
        <ul>
            <li>
                <label expertMode>{% trans 'HTTP/2 Protocol' %}</label>
                <div style=" display: inline-block; vertical-align: top; max-width: 70%; ">
                    <span style="margin-right:4px;">{% trans 'Enable' %}</span>
                    <rs:Checkbox name="enableHttp2" v:disabled="!auth('Edit_Website_Config', 'w')"></rs:Checkbox>
                    <p style="color: orange; margin-bottom: 0px;" rs:visible="mPage.enableHttp2">{% trans 'Attention: When a https site enable http/2 protocol, the other sites with the same listen port will enable automatically; When you disable it, you must do it in all https site with the same listen port.' %}</p>
                </div>
            </li>
        </ul>
    </div>

    <!--HTTP跳转HTTPS-->
    <div rs:visible="mPage.is_https && page.expertMode && !matchDeployMode('mirror|transparent')" rsid="http2httpsPane">
        <ul>
            <li>
                <label expertMode>{% trans 'HTTP to HTTPS' %}</label>
                <span>
                    <span style="margin-right:4px;">{% trans 'Enable' %}</span>
                    <rs:Checkbox name="enable_http2https"></rs:Checkbox>
                </span>

                <span rs:visible="mPage.enable_http2https" class="inlineBlock">
                    <span style="margin-right:6px;">{% trans 'HTTP Port' %}</span>
                    <input name="http2https_org_port" type="text" />
                </span>
            </li>
        </ul>
    </div>

    <!--SSL算法配置-->
    <div innerSection rs:visible="mPage.is_https && page.expertMode && !matchDeployMode('mirror')" expertMode toggleSection collapse rsid="siteSSLPane">
        <header>
            <span>{% trans 'SSL Algorithm' %}</span>
            <p>
                {% trans 'The default setting is highly compatible (this configuration does not support IE6. To support IE6, manually add the SSLv3 protocol). If safety is paramount concern, use the following settings:' %}<br/>
                SSL_Protocol: {{ default_scrurity_ssl_protocols }}<br/>
                SSL_Ciphers: {{ default_security_ssl_ciphers }}<br/>
            </p>
        </header>

        <rs:Table name="SSLAlogorithmConfigurationTable_nginx"
                  header="{% trans 'Parameter Name' %}|{% trans 'Parameter Value' %}"
                  cells="label|value">
            <div>
            <div v:text="rowData.label"></div>
            <div>
                <input v:name="SiteName.nameMap[rowData.label]"
                        v:value="mPage[SiteName.nameMap[rowData.label]]"
                        rs:visible="!SiteName.readOnlySSL()"
                        type="text"/>

                <input v:name="'default_' + SiteName.nameMap[rowData.label]"
                        v:value="SiteName.defaultValueMap[rowData.label]"
                        rs:visible="SiteName.readOnlySSL()"
                        disabled
                        type="text"/>

                <p name="sslTips"
                    class="sslTips"
                       v:if="SiteName.readOnlySSL()">{% trans "The active anti-scan function is enabled, and the system will enable security configuration by default." %}</p>

                </div>
            </div>
        </rs:Table>
    </div>

    <!--允许的HTTP请求方法-->
    <div innerSection toggleSection collapse rsid="allowHttpMethodPane" rs:visible="!matchDeployMode('mirror|plugin')">
        <header>
            <span>{% trans 'Allowed HTTP Request Methods' %}</span>
        </header>

        <p>{% trans 'The system allows and protects selected HTTP request methods.' %}</p>
        <rs:Label><rs:Checkbox name="limit_except1" onChange="SiteName.handleChangeMethods(event)"></rs:Checkbox> GET</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except2"></rs:Checkbox> POST</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except3"></rs:Checkbox> PUT</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except4"></rs:Checkbox> DELETE</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except5"></rs:Checkbox> OPTIONS</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except6"></rs:Checkbox> HEAD</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except9"></rs:Checkbox> PATCH</rs:Label>
        <br>
        <p v:if="{% visibleLayout 'http_methods_webdav_tip' %}">{% trans 'The system allows selected WebDAV request methods' %}<span v:if="{% visibleLayout 'http_methods_tip' %} && global_params.isWaflayoutShowItem">{% trans ', but does not provide dynamic token protections to those requests.' %}</span></p>
        <rs:Label><rs:Checkbox name="limit_except10"></rs:Checkbox> MKCOL</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except11"></rs:Checkbox> COPY</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except12"></rs:Checkbox> MOVE</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except13"></rs:Checkbox> PROPFIND</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except14"></rs:Checkbox> PROPPATCH</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except15"></rs:Checkbox> LOCK</rs:Label>
        <rs:Label><rs:Checkbox name="limit_except16"></rs:Checkbox> UNLOCK</rs:Label>
    </div>

    <!--保护系统与被保护站点的连接超时设置-->
    <div innerSection rs:visible="page.expertMode && !matchDeployMode('mirror|plugin')" expertMode toggleSection collapse rsid="websocketPathPane">
        <header>
            <span>{% trans 'Websocket' %}</span>
        </header>
        <ul>
            <li>
                <label>{% trans 'Auto detect websocket' %}</label>
                <rs:Checkbox name="autoDetectWebsocket" onChange="handleAutoDetectWebsocket(event)" ></rs:Checkbox>
            </li>
            <li rs:visible="!mPage.autoDetectWebsocket">
                <p>
                    {% trans 'Please input Websocket path，example：/rest/api/websocket or /path/entry' %} <br/>
                    {% trans 'Note：path should be the same with the websocket request, support regular expression' %}
                </p>
                <rs:ProtectTable name="WebsocketPaths"
                                 businessType="websocketPaths"
                                 id="__WEBSOCKET_PATHS__"
                                 header="{% trans 'Path' %}|{% trans 'Comment' %}|{% trans 'Actions' %}"
                                 cells="websocket_path|websocket_comment|_OPERATION" class="rsTable"></rs:ProtectTable>
                <div class="rsTableFooter">
                    <button type="button"
                            class="rsTableIncreaseButton"
                            rsid="bt_addWebsocketPaths"
                            onclick="PageHandler.getTable('websocketPaths').createNewRow()">{% trans 'New Configuration' %}</button>
                </div>
            </li>
        </ul>
    </div>

    <script language="javascript">

        {% autoescape off %}


        var SiteName = (function($) {

            var SSL_DEFAULT = {
                SECURITY: {
                    PROTOCOL: {{ default_scrurity_ssl_protocols|to_json}},
                    CIPHERS: {{ default_security_ssl_ciphers|to_json }}
                }
            };

            var mPage = $.mPage;
            var upstreamConf = $.upstreamConf;
            var NAME_MAX_LEN = 68;
            var WebsocketPaths = $.initProtectList(upstreamConf.websocket_paths,
                                                    {
                                                        websocket_path:'',
                                                        websocket_comment:'',
                                                        websocket_path_case_sensitive: true
                                                    });

            rs.merge(mPage, {
                action: {{ action|to_json }},
                site_name: upstreamConf.ServerName || '',
                site_port: upstreamConf.ListenPort || $.getDefaultPort(),
                server_name_type: upstreamConf.ServerNameType,
                server_name_type_trans:"{% trans 'Domain' %}",
                is_https: upstreamConf.IsHttps,
                site_protocol: upstreamConf.IsHttps ? 'https://' : 'http://',
                is_terminal_https : upstreamConf.IsTerminalHttps,
                terminal_port: upstreamConf.TerminalPort || '80',
                terminal_protocol: upstreamConf.IsTerminalHttps ? 'https://' : 'http://',
                enable_business_path: upstreamConf.enable_business_path || false,
                business_path: upstreamConf.business_path || '',
                server_key: upstreamConf.server_key,
                divide_ipv46_enabled: upstreamConf.divide_ipv46_enabled || false,
                terminal_enable: upstreamConf.TerminalEnabled,
                useBuiltInCert: upstreamConf.useBuiltInCert ? 'true' : 'false',
                cert_file_name: upstreamConf.cert_file_name || '',
                key_file_name: upstreamConf.key_file_name || '',
                cert_plain_text: '',
                key_plain_text: '',
                useChinaSecurityCert: upstreamConf.useChinaSecurityCert,
                useInternationalCert: upstreamConf.useInternationalCert,
                useBuiltInChinaSecurityCert: upstreamConf.useBuiltInChinaSecurityCert ? 'true' : 'false',
                gm_sign_cert_file_name: upstreamConf.gm_sign_cert_file_name || '',
                gm_sign_cert_plain_text: upstreamConf.gm_sign_cert_plain_text || '',
                gm_sign_key_file_name: upstreamConf.gm_sign_key_file_name || '',
                gm_sign_key_plain_text: upstreamConf.gm_sign_key_plain_text || '',
                gm_enc_cert_file_name: upstreamConf.gm_enc_cert_file_name || '',
                gm_enc_cert_plain_text: upstreamConf.gm_enc_cert_plain_text || '',
                gm_enc_key_file_name: upstreamConf.gm_enc_key_file_name || '',
                gm_enc_key_plain_text: upstreamConf.gm_enc_key_plain_text || '',
                http2https_org_port: upstreamConf.http2https_org_port||'80',
                enable_http2https: upstreamConf.enable_http2https,
                site_customize_name: upstreamConf.site_customize_name || '',
                enable_site_conf: upstreamConf.enable_site_conf === false ? false : true,
                certUploadTxt: upstreamConf.key_file_name?'{% trans "Update" %}':'{% trans "Upload" %}',
                gmCertUploadTxt: upstreamConf.gm_sign_cert_file_name?'{% trans "Update" %}':'{% trans "Upload" %}',
                certificationKey: upstreamConf.CertificationKey || '',
                certification: upstreamConf.Certification || '',
                isExistCert: upstreamConf.cert_saved,
                autoDetectWebsocket: upstreamConf.auto_detect_websocket,

                SSLAlogorithmConfigurationTable_nginx: [
                    {label:'{% trans "SSL_Protocol" %}'},
                    {label:'{% trans "SSL_Ciphers" %}'}
                ],

                enableHttp2: upstreamConf.enableHttp2,
                serverList_data: '',
                serverList_IPv4_data: '',
                serverList_IPv6_data: '',
                source_port_keep_workable: {{ source_port_keep_workable | to_json}}
            });

            function checkBusinessPath(value) {
                if (!value || (typeof value == 'string' && !value.trim())) {
                    return rs.formatString('{% trans "reg expression can not be empty" %}');
                }

                if (!/^[\x20-\x7e]*$/.test(value) || /[\s"]+/.test(value)) {
                    return rs.formatString('{% trans "Regular expressions prohibit the entry of double quotes, spaces, tabs, page feeds, and line breaks, as well as invisible characters other than 0x20-0x7e!" %}');
                }
            }

            rs.merge(mPage._validation, {

                site_name: [function (value) {
                    if (mPage.is_https && mPage.site_name.length > 64) {
                        return '{% trans "HTTPS site names cannot exceed 64 characters." %}';
                    }

                    if (mPage.server_name_type === 'Domain') {
                        return Validation.DOMAIN(value);
                    } else if (mPage.server_name_type === 'IPv4') {
                        return Validation.IPV4(value);
                    } else if (mPage.server_name_type === 'IPv6') {
                        return Validation.IPV6(value);
                    } else if (mPage.server_name_type === 'Regex') {
                        var popStr = rs.formatString('{% trans "Regex is incorrect" %}');
                        try {
                            var siteNameStr = value;
                            siteNameStr = siteNameStr.replace(/\\\\/g, '');
                            siteNameStr = siteNameStr.replace(/\\\"/g, '');
                            if (!value || (typeof value == 'string' && !value.trim())) {
                                return rs.formatString('{% trans "reg expression can not be empty" %}');
                            }
                            if (/(?:\"|\/)/.test(siteNameStr)) {
                                return popStr;
                            }
                            new RegExp(value);
                        } catch (e) {
                            return popStr;
                        }
                    }
                }],

                site_port: [function(value) {
                    return Validation.PORT(value, "{% trans 'Please enter a correct port number' %}", {
                        isLimitRange: true,
                        isLimitAdminPort: true,
                        isTransparentMode: matchDeployMode('transparent'),
                        adminPort: global_params.webconsoleAdminPort
                    });
                }],

                terminal_port: [function(value){
                    if(mPage.terminal_enable) return Validation.PORT(value);
                }, "{% trans 'Invalid Port' %}"],

                business_path: [function (value) {
                    if (mPage.enable_business_path) {
                        if (value.length > 1024) {
                            return rs.formatString('{% trans "The length of {0} can not exceed {1} characters." %}', '{% trans "Business Path" %}', 1024);  // TODO: Path 用大写还是小写
                        }
                        return checkBusinessPath(value);
                    }
                }],

                site_customize_name:[function(value){
                    var val = value.trim();
                    if (val.len() > NAME_MAX_LEN) {
                        return rs.formatString('{% trans "Custom Website Name must not exceed {0} Chinese characters or {1} English characters." %}', NAME_MAX_LEN/2, NAME_MAX_LEN);
                    } else if (val && !(/^[()-._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$/.test(val))) {
                        return '{% trans "Custom Website Name only support numbers, letters, Chinese, and characters such as: spaces, dot(.), underline(_), hyphen(-), parentheses, asterisk(*), plus(+), comma(,)." %}';
                    }
                }],

                http2https_org_port: [function(value){
                    if(mPage.enable_http2https) return Validation.PORT(value);
                }, "{% trans 'Invalid Port' %}"]
            });

            initLimitExceptList(mPage);



            function initLimitExceptList(model) {
                var arr = upstreamConf.limit_except || [true, true, true, true, true, true, false, false, true, false, false, false, false, false, false, false];
                if (matchDeployMode('mirror')) {
                    arr = [true, true, true, true, true, true, true, true, true, false, false, false, false, false, false, false];
                }

                for(var i=0; i<arr.length; i++) {
                    var d = arr[i];
                    if (d!=null && typeof d!='undefined') {
                        var index = i+1;
                        model['limit_except'+index] = d;
                    }
                }
            }

            function initCertUploadControl() {
                var certUpload = rs('#__CERT_UPLOAD__');
                var certKeyUpload = rs('#__CERT_KEY_UPLOAD__');
                var str = '{% trans '"{0}" is the file currently being used' %}';
                if (mPage.cert_file_name) certUpload.placeholder = rs.formatString(str, mPage.cert_file_name);
                if (mPage.key_file_name) certKeyUpload.placeholder = rs.formatString(str, mPage.key_file_name);
                certUpload.uploadBtnTxt = mPage.certUploadTxt;
                certKeyUpload.uploadBtnTxt = mPage.certUploadTxt;

                var gm_sign_certUpload = rs('#__GM_SIGN_CERT_UPLOAD__');
                var gm_sign_certKeyUpload = rs('#__GM_SIGN_CERT_KEY_UPLOAD__');
                if (mPage.gm_sign_cert_file_name) gm_sign_certUpload.placeholder = rs.formatString(str, mPage.gm_sign_cert_file_name);
                if (mPage.gm_sign_key_file_name) gm_sign_certKeyUpload.placeholder = rs.formatString(str, mPage.gm_sign_key_file_name);
                gm_sign_certUpload.uploadBtnTxt = mPage.gmCertUploadTxt;
                gm_sign_certKeyUpload.uploadBtnTxt = mPage.gmCertUploadTxt;

                var gm_enc_certUpload = rs('#__GM_ENC_CERT_UPLOAD__');
                var gm_enc_certKeyUpload = rs('#__GM_ENC_CERT_KEY_UPLOAD__');
                if (mPage.gm_enc_cert_file_name) gm_enc_certUpload.placeholder = rs.formatString(str, mPage.gm_enc_cert_file_name);
                if (mPage.gm_enc_key_file_name) gm_enc_certKeyUpload.placeholder = rs.formatString(str, mPage.gm_enc_key_file_name);
                gm_enc_certUpload.uploadBtnTxt = mPage.gmCertUploadTxt;
                gm_enc_certKeyUpload.uploadBtnTxt = mPage.gmCertUploadTxt;
            }

            function handleKeydownCustomName(evt) {
                if (evt.keyCode==13||evt.keyCode==8||evt.keyCode==9) return;
                var val = evt.target.value;
                var selection = window.getSelection();
                if (val.len()>=NAME_MAX_LEN) {
                    var activeElem = document.activeElement;
                    if (activeElem && activeElem.nodeType==1 && activeElem.tagName=='INPUT') {
                        var _start = activeElem.selectionStart;
                        var _end = activeElem.selectionEnd;
                        if (_end-_start!=0) return;
                    }

                    evt.preventDefault();
                    return;
                }
            }

            return {

                init: function() {
                    mPage.listen('site_protocol', function(key, value){
                        mPage.is_https = value == 'https://';

                    }).listen('terminal_protocol', function(key, value){
                        mPage.is_terminal_https = value == 'https://';
                    });

                    rs('input[name=site_customize_name]').bind('keydown', handleKeydownCustomName);
                    initCertUploadControl();

                    rs('#__WEBSOCKET_PATHS__').data = WebsocketPaths;
                },

                handleChangeSiteProtocol: function(evt) {
                    if (!isPageInit) return;
                    var value = evt.item.attr('value');
                    mPage.site_port = $.getDefaultPort(value);
                    if (value == 'https://') {
                        //default ssl check
                        if (!mPage.useChinaSecurityCert) {
                            mPage.useInternationalCert =  true;
                        }
                    }
                },

                handleChangeServerNameType: function(evt) {
                    if (!isPageInit) return;
                    mPage.server_name_type = evt.item.attr('value');
                    mPage.server_name_type_trans = evt.item.innerHTML;
                },

                handleGetUploadCertResult: function(evt) {
                    FileUploadResultHandler.process(evt.data, function(response) {
                        if(response['plain_text']) {
                            mPage['cert_plain_text'] = response['plain_text'];
                            mPage['cert_file_name'] = response['file_name'];
                            rs('#__CERT_UPLOAD__').uploadBtnTxt = '{% trans "Uploaded" %}';
                            rsalert('{% trans "Certificate file has been uploaded." %}');
                        } else {
                            rsalert('{% trans "Invalid Certificate file." %}');
                        }
                    });
                },

                handleGetUploadPrivateKeyResult: function(evt) {
                    FileUploadResultHandler.process(evt.data, function(response) {
                        if(response['plain_text']) {
                            mPage['key_plain_text'] = response['plain_text'];
                            mPage['key_file_name'] = response['file_name'];
                            rs('#__CERT_KEY_UPLOAD__').uploadBtnTxt = '{% trans "Uploaded" %}';
                            rsalert('{% trans "Private key file has been uploaded." %}');
                        } else {
                            rsalert('{% trans "Invalid Private key file." %}');
                        }
                    });
                },

                handleGetUploadCSSignCertResult: function(evt) {
                    FileUploadResultHandler.process(evt.data, function(response) {
                        if(response['plain_text']) {
                            mPage['gm_sign_cert_plain_text'] = response['plain_text'];
                            mPage['gm_sign_cert_file_name'] = response['file_name'];
                            rs('#__GM_SIGN_CERT_UPLOAD__').uploadBtnTxt = '{% trans "Uploaded" %}';
                            rsalert('{% trans "Signature certificate file has been uploaded." %}');
                        } else {
                            rsalert('{% trans "Invalid signature certificate file." %}');
                        }
                    });
                },

                handleGetUploadCSSignPrivateKeyResult: function(evt) {
                    FileUploadResultHandler.process(evt.data, function(response) {
                        if(response['plain_text']) {
                            mPage['gm_sign_key_plain_text'] = response['plain_text'];
                            mPage['gm_sign_key_file_name'] = response['file_name'];
                            rs('#__GM_SIGN_CERT_KEY_UPLOAD__').uploadBtnTxt = '{% trans "Uploaded" %}';
                            rsalert('{% trans "Signature private key file has been uploaded." %}');
                        } else {
                            rsalert('{% trans "Invalid signature private key file." %}');
                        }
                    });
                },

                handleGetUploadCSEncCertResult: function(evt) {
                    FileUploadResultHandler.process(evt.data, function(response) {
                        if(response['plain_text']) {
                            mPage['gm_enc_cert_plain_text'] = response['plain_text'];
                            mPage['gm_enc_cert_file_name'] = response['file_name'];
                            rs('#__GM_ENC_CERT_UPLOAD__').uploadBtnTxt = '{% trans "Uploaded" %}';
                            rsalert('{% trans "Encryption certificate file has been uploaded." %}');
                        } else {
                            rsalert('{% trans "Invalid encryption certificate key file." %}');
                        }
                    });
                },

                handleGetUploadCSEncPrivateKeyResult: function(evt) {
                    FileUploadResultHandler.process(evt.data, function(response) {
                        if(response['plain_text']) {
                            mPage['gm_enc_key_plain_text'] = response['plain_text'];
                            mPage['gm_enc_key_file_name'] = response['file_name'];
                            rs('#__GM_ENC_CERT_KEY_UPLOAD__').uploadBtnTxt = '{% trans "Uploaded" %}';
                            rsalert('{% trans "Encryption private key file has been uploaded." %}');
                        } else {
                            rsalert('{% trans "Invalid encryption private key file." %}');
                        }
                    });
                },

                handleChangeMethods: function(evt) {
                    if(evt.target.checked) mPage.limit_except6 = true;
                },

                nameMap: {
                    SSL_Protocol: 'sslProtocol',
                    SSL_Ciphers: 'sslCiphers'
                },

                defaultValueMap: {
                    SSL_Protocol: SSL_DEFAULT.SECURITY.PROTOCOL,
                    SSL_Ciphers: SSL_DEFAULT.SECURITY.CIPHERS
                },

                readOnlySSL: function() {
                    return mPage.prevent_scanner && mPage.optionsRadiosinline != 'Close';
                },

                mouseOverSiteConfSwitch: function(event) {
                    // showSiteConfSwitchTip(event);
                    var s = document.getElementById("enableSiteConf");
                    if (s) {
                        event.stopPropagation();
                        var tipX = g_const.vertical_layout ? event.pageX : event.clientX;
                        var tipY = g_const.vertical_layout ? event.pageY : event.clientY;
                        var msg = "{% trans 'When switched off, this site configuration will be disabled and site service no longer available.' %}";
                        page.titleTip(tipX, tipY, msg);
                    }
                },

                mouseOutSiteConfSwitch: function(event) {
                    // hideSiteConfSwitchTip(event);
                    event.stopPropagation();
                    page.titleTipHide();
                },

                UpdateSiteConfStatus: function(event) {
                    var msg = "";
                    if (event.target.checked) {
                        event.target.value = 'on';
                        msg = "{% trans 'Are you sure to enable the current site configuration?' %}";
                    } else {
                        event.target.value = 'off';
                        msg = "{% trans 'Are you sure to disable the current site configuration?' %}";
                    }
                    ask(msg, function() {
                    }, function() {
                        event.target.checked = !event.target.checked;
                        event.target.value = event.target.checked ? 'on' : 'off';
                    });
                },

                getNamePlaceholder: function() {
                    if (auth('Edit_Website_Config', 'w')) {
                        return rs.formatString('{% trans "{0} Chinese characters or {1} English characters" %}', NAME_MAX_LEN/2, NAME_MAX_LEN);
                    } else {
                        return NONE_TEXT;
                    }
                }
            };

        })(app);
        {% endautoescape %}
    </script>

</section>

<!--服务器-->
<section rs:visible="!matchDeployMode('plugin')">
    <header>
        <span>{% trans 'Servers' %}</span>
    </header>
    <div class="modeGroup" rs:visible="global_params.isIPv46 && !matchDeployMode('mirror|transparent')">
        <ul class="labelGroup optionGroup">
            <li>
                <rs:label><rs:Checkbox id='divide_ipv46_enabled' name="divide_ipv46_enabled" onclick="if (!isCreateServer) { this.checked = !this.checked }"></rs:Checkbox>{% trans 'Distinguish between IPv4 and IPv6' %}</label>
            </li>
        </ul>
    </div>

    <div innerSection v:if="!mPage.divide_ipv46_enabled">
        <header>
            <p rs:visible="!matchDeployMode('mirror|transparent')">
                {% trans 'Specify the protocols, IPs, port numbers and weight of servers of the website added above. ' %}
                {% trans "Notice: The Health Check would be disabled if domain names are used instead of servers' IPs." %}
            </p>
            <p rs:visible="matchDeployMode('mirror|transparent')">
                {% trans "IP 0.0.0.0 means all IP.If you set IP address,then service means the IP you entered." %}
            </p>
        </header>

        <rs:Table name="serverList"
                editable="true"
                header="{% trans 'Enable' %}|{% trans 'Status' %}|{% trans 'Protocol' %}|{% trans 'IP' %}|{% trans 'Port' %}|{% trans 'Weight' %}|{% trans 'Actions' %}"
                cells="manual_down||upstream_protocol|upstream_ip|upstream_port|upstream_weight|"
                onCreateCell="ServerList.handleOnCreateServerListCell(event)"
                onCreateHeaderCell="ServerList.handleOnCreateServerListHeaderCell(event)"></rs:Table>

        <div class="rsTableFooter" v:if="auth('Edit_Website_Config', 'w')">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_addServerList"
                    onclick="ServerList.create('serverList')">{% trans 'New Configuration' %}</button>
        </div>
        <input type="hidden" name="serverList_data"/>
    </div>

    <div innerSection v:if="mPage.divide_ipv46_enabled">
        <header>
            <p rs:visible="!matchDeployMode('mirror|transparent')">
                {% trans 'Specify the protocols, IPv4s, port numbers and weight of servers of the website added above. ' %}
                {% trans "Notice: The Health Check would be disabled if domain names are used instead of servers' IPs." %}
            </p>
            <p rs:visible="matchDeployMode('mirror|transparent')">
                {% trans "IP 0.0.0.0 means all IP.If you set IP address,then service means the IP you entered." %}
            </p>
        </header>

        <rs:Table name="serverList_IPv4"
                editable="true"
                header="{% trans 'Enable' %}|{% trans 'Status' %}|{% trans 'Protocol' %}|{% trans 'IP' %}|{% trans 'Port' %}|{% trans 'Weight' %}|{% trans 'Actions' %}"
                cells="manual_down||upstream_protocol|upstream_ip|upstream_port|upstream_weight|"
                onCreateCell="ServerList.handleOnCreateServerListCell(event)"
                onCreateHeaderCell="ServerList.handleOnCreateServerListHeaderCell(event)"></rs:Table>

        <div class="rsTableFooter" v:if="auth('Edit_Website_Config', 'w')">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_addServerList_IPv4"
                    onclick="ServerList.create('serverList_IPv4')">{% trans 'New Configuration' %}</button>
        </div>
        <input type="hidden" name="serverList_IPv4_data"/>
    </div>

    <div innerSection v:if="mPage.divide_ipv46_enabled">
        <header>
            <p rs:visible="!matchDeployMode('mirror|transparent')">
                {% trans 'Specify the protocols, IPv6s, port numbers and weight of servers of the website added above. ' %}
                {% trans "Notice: The Health Check would be disabled if domain names are used instead of servers' IPs." %}
            </p>
            <p rs:visible="matchDeployMode('mirror|transparent')">
                {% trans "IP 0.0.0.0 means all IP.If you set IP address,then service means the IP you entered." %}
            </p>
        </header>

        <rs:Table name="serverList_IPv6"
                editable="true"
                header="{% trans 'Enable' %}|{% trans 'Status' %}|{% trans 'Protocol' %}|{% trans 'IP' %}|{% trans 'Port' %}|{% trans 'Weight' %}|{% trans 'Actions' %}"
                cells="manual_down||upstream_protocol|upstream_ip|upstream_port|upstream_weight|"
                onCreateCell="ServerList.handleOnCreateServerListCell(event)"
                onCreateHeaderCell="ServerList.handleOnCreateServerListHeaderCell(event)"></rs:Table>

        <div class="rsTableFooter" v:if="auth('Edit_Website_Config', 'w')">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_addServerList_IPv6"
                    onclick="ServerList.create('serverList_IPv6')">{% trans 'New Configuration' %}</button>
        </div>
        <input type="hidden" name="serverList_IPv6_data"/>
    </div>

    <script>
    {% autoescape off %}

        var ServerList = (function($) {

            var upstreamConf = $.upstreamConf;
            var mPage = $.mPage;

            rs.merge(mPage, {
                is_upstream_https: upstreamConf.IsUpstreamHttps,
                upstream_protocol: upstreamConf.IsUpstreamHttps ? 'https://' : 'http://',
                proxy_connect_timeout: upstreamConf.proxy_connect_timeout||'',
                proxy_send_timeout: upstreamConf.proxy_send_timeout||'',
                proxy_read_timeout: upstreamConf.proxy_read_timeout||'',
            });

            rs.merge(mPage._validation, {
                proxy_connect_timeout:[Validation.POSITIVE_INTEGER_OR_EMPTY],
                proxy_send_timeout:[Validation.POSITIVE_INTEGER_OR_EMPTY],
                proxy_read_timeout:[Validation.POSITIVE_INTEGER_OR_EMPTY]
            });

            function initServerList(obj, UpstreamListName) {

                var arr = [];
                var upstreamList = upstreamConf[UpstreamListName];
                if (!upstreamList || upstreamList.length === 0) return arr;

                var len = upstreamList.length;
                if (len == 1 && matchDeployMode('mirror|transparent') && !upstreamList[0][0]) return arr;

                for(var i = 0; i < len; i++) {
                    var data = upstreamList[i];
                    if ($.isNotArray(data)) continue;
                    arr.push({
                        manual_down:data[2],
                        upstream_protocol: obj.upstream_protocol,
                        upstream_ip:data[0],
                        upstream_port:data[1],
                        upstream_weight:data[3]||1
                    });
                }

                return arr;
            }
            var serverList = initServerList(mPage, 'UpstreamList');
            var serverList_IPv4 = initServerList(mPage, 'UpstreamList_IPv4');
            var serverList_IPv6 = initServerList(mPage, 'UpstreamList_IPv6');

            function addUpstreamItem(tableName){
                var table = document.named(tableName);
                table.addRow({
                    manual_down: true,
                    upstream_protocol: mPage.upstream_protocol,
                    upstream_ip: '',
                    upstream_port: $.getDefaultPort(mPage.upstream_protocol),
                    upstream_weight: 1
                });
                form.update(table, ['upstream_protocol']);
                updateAllUpsteamEnabledSwitch(tableName);
            }

            function handleOnCreateServerListHeaderCell(event){
                var table = event.target;
                var tableName = table.getAttribute('name');
                switch(event.index){
                    case 0:
                        event.preventDefault();
                        if (matchDeployMode('mirror|transparent')) {
                            event.element.hide();
                        } else {
                            var label = rs.XElement('Label').appendTo(event.element);
                            createCheckbox({ name:'upstreamEnableSwitch'}).appendTo(label).bind('change', toggleAllUpstreamEnabled);
                            label.appendChild(rs.Text(event.data));
                        }

                        break;

                    case 1:
                        if (matchDeployMode('mirror|transparent')) {
                            event.element.hide();
                        } else {
                            event.element.attr('rs\:visible', 'ServerList.needShowServerStatus()');
                        }

                        break;

                    case 2:
                    case 4:
                        if (matchDeployMode('mirror|transparent')) event.element.hide();
                        break;
                    case 5:
                        event.element.attr('rs\:visible', '!matchDeployMode("mirror|transparent")');
                        break;
                }
            }

            function needShowServerStatus(){
                {% if not isProxyRole %}
                return false;
                {% endif %}
                return /^(tcp|http)$/i.test(mPage.health_check_open) && !mPage.disableHealthCheck;
            }

            function handleOnCreateServerListCell(event){

                var table = event.target;
                var tableName = table.getAttribute('name');
                event.preventDefault();
                switch(event.index){
                    case 0: //enable
                        var h = createHiddenField(null, event.data?'True':'False').appendTo(event.element);
                        createCheckbox({name:event.key}, {rsid:'server_enable'}).bind('change', function(evt) {
                            h.value = evt.target.checked ? 'True' : 'False';
                            var statusElem = evt.target.parent('div[row=row]').named('serverStatus');
                            if (statusElem) statusElem.data = evt.target.checked ? 1:0;
                            updateAllUpsteamEnabledSwitch(tableName);

                        }).appendTo(event.element).checked = event.data;

                        if (matchDeployMode('mirror|transparent')) { event.element.hide(); }
                        break;

                    case 1: //Status
                        event.element.attr('rs\:visible', 'ServerList.needShowServerStatus() && !matchDeployMode("mirror|transparent")');
                        rs.Element('div')
                            .as(StatusIcon, {status:event.rowData.manual_down ? 1 : 0})
                            .attr('name', 'serverStatus')
                            .appendTo(event.element);

                        break;

                    case 2: //protocol
                        createSelect({name: event.key}, event.data, [
                            {key: 'http://', value: 'http://'},
                            {key: 'https://', value: 'https://'}
                        ], {rsid: 'server_protocol'})
                        .addClass('protocol-select')
                        .appendTo(event.element)
                        .bind('change', function (cevt) {
                            var val = cevt.data;
                            var port = event.row.named('upstream_port');
                            if (port.value == $.getDefaultPort(cevt.oldValue)) {
                                port.value = $.getDefaultPort(val);
                            }
                            setHealthSelectStatus(val);
                        });

                        if (matchDeployMode('mirror|transparent')) event.element.hide();
                        break;

                    case 3: //IP
                        var msg = '{% trans "Server IP" %}'
                        if (matchDeployMode('mirror|transparent')){
                            msg = '{% trans "Server IP/IP Segment" %}';
                        }
                        if (tableName == 'serverList_IPv4') {
                            msg = '{% trans "Server" %}{% trans "IPv4 Address" %}'
                        } else if (tableName == 'serverList_IPv6') {
                            msg = '{% trans "Server" %}{% trans "IPv6 Address" %}'
                        }
                        createTextInputbox(event.key, event.data, msg, {rsid:'server_ip'})
                            .bind('change', handleOnChangeServerIP)
                            .appendTo(event.element);

                        break;

                    case 4: //port
                        createTextInputbox(event.key, event.data, '{% trans "Upstream Port" %}', {rsid:'server_port'})
                        .appendTo(event.element)
                        .attr('port', 'port');
                        if (matchDeployMode('mirror|transparent')) event.element.hide();
                        break;

                    case 5: //weight
                        event.element.attr('rs\:visible', '!matchDeployMode("mirror|transparent")');
                        createTextInputbox(event.key, event.data, '{% trans "Server weight for load balancing" %}', {rsid:'weight'})
                        .appendTo(event.element);
                        break;

                    case 6: //operation
                        if (editable) {
                            var delBtn = createDeleteLink(function(evt){
                                if(evt.target.attr('disabled')) return;
                                table.removeRow(event.row.index);
                                updateIfShouldDisableHealthCheck(tableName);
                                updateAllUpsteamEnabledSwitch(tableName);
                            }, '{% trans "Delete " %}').appendTo(event.element);

                            delBtn.attr('rs\:disabled', "(document.named('" + tableName + "').length==1 && !matchDeployMode('mirror|transparent'))");

                        } else {
                            event.element.innerText = NONE_TEXT;
                        }

                        break;
                }
            }

            function toggleAllUpstreamEnabled(evt){
                var enabled = evt.target.checked;
                var table = getTableElementByCol(evt.target)
                rs.each(table.rows, function(index, row){
                    var checkbox = row.child('rs\\:CheckBox');
                    var hidden = checkbox.parent().child('input[type=hidden]');
                    checkbox.checked = enabled;
                    hidden.value = enabled?'True':'False';

                    var statusElem = row.named('serverStatus');
                    if(statusElem) statusElem.data = enabled?1:0;
                });
            }

            function updateAllUpsteamEnabledSwitch(tableName){
                if (!matchDeployMode('mirror|transparent')) {
                    var table = document.named(tableName);
                    var rowCount = table.data.length;
                    var checkedCount = 0;
                    if (table.data && rowCount>0) {
                        rs.each(table.data, function(index, item){
                            if (item.manual_down) checkedCount+=1;
                        });
                    }

                    var checkbox = table.named('upstreamEnableSwitch').parent('rs\\:CheckBox');

                    if (checkedCount == 0) {
                        checkbox.checked = false;
                    } else if (checkedCount == rowCount) {
                        checkbox.checked = true;
                    } else {
                        checkbox.checkedPart = true;
                    }
                }
            }

            function setHealthSelectStatus(val) {
                mPage.upstream_protocol = val; // todo: 不知为何需要手动同步一次
                if (val=='https://' && mPage.health_check_open == 'http')
                    mPage.health_check_open = 'off';
            }

            function getTableElementByCol(ele) {
                try {
                    var element = ele.parentElement;
                    var style = element.getAttribute('style');
                    if (!style || style.indexOf('table;') < 0) {
                        return getTableElementByCol(element);
                    }
                    return element;
                } catch (e) {
                    return ele;
                }
            }

            function handleOnChangeServerIP(evt) {
                evt.stopPropagation();
                var tableName = getTableElementByCol(evt.target).getAttribute('name');
                updateIfShouldDisableHealthCheck(tableName);
            }

            function updateIfShouldDisableHealthCheck(tableName){
                var hasErrorIP = false;
                var table = document.named(tableName);
                rs.each(table.data, function(index, item){
                    hasErrorIP = hasErrorIP || (item.upstream_ip && !!Validation.IP(item.upstream_ip));
                    if(hasErrorIP) return true;
                });
                mPage.disableHealthCheck = hasErrorIP;
                if(hasErrorIP) mPage.health_check_open = 'off';
            }

            function doUpstreamCheck() {
                var isHealthCheckEnable = mPage.is_health_check_enable;
                var isExistsHealthCheckUpstream = false;
                var arr = [{tableName: 'serverList', data: serverList},
                           {tableName: 'serverList_IPv4', data: serverList_IPv4},
                           {tableName: 'serverList_IPv6', data: serverList_IPv6}];
                var len = arr.length;
                for (var i = 0; i < len; i++) {
                    rs.each(arr[i].data, function(index, item) {
                        isExistsHealthCheckUpstream = isExistsHealthCheckUpstream || item.manual_down;
                        if(isExistsHealthCheckUpstream) return true;
                    });
                }
                if(isHealthCheckEnable && isExistsHealthCheckUpstream) checkUpstream();
            }

            function checkUpstream(){
                {% if not isProxyRole %}
                return;
                {% endif %}
                service.checkUpstreamStatus(function ok(response){
                    if (!response) {
                        return;
                    }

                    var data = response.servers;
                    var isServerNameTypeRegex = mPage.server_name_type == "Regex";
                    var siteHostname = !Validation.IPV6(mPage.site_name) ? mPage.site_name.replace(/\:/g,'_') : mPage.site_name;
                    if (isServerNameTypeRegex) {
                        siteHostname = md5(siteHostname);
                    }
                    var checkItmes = ServerList.getCheckListArr();
                    var ServerList_Arr = ['', '_IPv4', '_IPv6'];
                    var suffix = {'serverList': '', 'serverList_IPv4': '_IPv4', 'serverList_IPv6': '_IPv6'}
                    for (var i = 0; i < checkItmes.length; i++) {
                        var tableName = checkItmes[i];

                        var curSite = mPage.server_key + '_list' + suffix[tableName];
                        curSite = curSite.replace(/:/g, '_');
                        var table = document.named(tableName);
                        rs.each(table.data, function(rowIndex, rowData){
                            if (!rowData) return false;
                            var row = table.rows[rowIndex];
                            var statusIcon = row.named('serverStatus');
                            var host_with_port = rowData.upstream_ip + ":" + rowData.upstream_port;
                            if (!Validation.IPV6(rowData.upstream_ip)) {
                                host_with_port = '[' + rowData.upstream_ip +']' + ':' + rowData.upstream_port;
                            }

                            var manual_down = rowData.manual_down;
                            if (manual_down) {
                                rs.each(data.server, function(index, server) {
                                    if (!server) return false;
                                    var siteName = server.upstream;
                                    var serverName = server.name;
                                    var status = server.status;
                                    if (curSite==siteName && host_with_port==serverName) {
                                        switch(status){
                                            case 'up':
                                                statusIcon.data = 2;
                                                break;
                                            case 'down':
                                                statusIcon.data = 1;
                                                break;
                                        }
                                    }
                                });
                            } else {
                                statusIcon.data = 0;
                            }
                        });
                    }

                }, null, function always() {
                    var t = null;
                    clearTimeout(t);
                    if (!SessionExpiredManager.isSessionExpired()) {
                        t = setTimeout(checkUpstream, 5000);
                    }

                });
            }

            function initServerListTable(tableName, tableData) {
                mPage.listen('upstream_protocol', function(key, value){
                        mPage.is_upstream_https = value == 'https://';
                });
                document.body.fill(tableData);
                updateAllUpsteamEnabledSwitch(tableName);
                updateIfShouldDisableHealthCheck(tableName);
            }

            function load_balancing_strategy_enabled() {
                return ((!mPage.divide_ipv46_enabled && document.named('serverList').data.length > 1)
                 || (mPage.divide_ipv46_enabled && document.named('serverList_IPv4').data.length > 1)
                 || (mPage.divide_ipv46_enabled && document.named('serverList_IPv6').data.length > 1))
            }

           function getTableJsonData(tableName) {
                var table = document.named(tableName);
                var rowCount = table.data.length;
                var jsonData = {};
                if (table.data && rowCount > 0) {
                    rs.each(table.data, function(index, item){
                        jsonData[index.toString()] = [item.upstream_ip, item.upstream_port, item.manual_down, item.upstream_weight || 1];
                    });
                }
                return JSON.stringify(jsonData);
            }

            function getCheckListArr() {
                var checkItmes = [];
                if (mPage.divide_ipv46_enabled) {
                    checkItmes.push('serverList_IPv4');
                    checkItmes.push('serverList_IPv6');
                } else {
                    checkItmes.push('serverList');
                }
                return checkItmes;
            }

            function enableInputData(name) {
                var columnElementList = TopWindow().document.getElementsByName(name);
                if (!columnElementList) return;

                rs.each(columnElementList, function(i, ce) {
                    if (!ce || ce['disabled'] == undefined) return false;

                    ce.removeAttribute('disabled');
                });
            }

            function disableInputData(name) {
                var columnElementList = rs('input[name=' + name + ']');
                if (!columnElementList) return;

                if (columnElementList && !columnElementList.length) {
                    columnElementList.attr('disabled',true);
                    return;
                }

                rs.each(columnElementList, function(i, ce) {
                    if (!ce) return false;

                    ce.attr('disabled',true);
                });
            }

            function enableInputDataAll() {
                enableInputData('manual_down');
                enableInputData('upstream_ip');
                enableInputData('upstream_port');
                enableInputData('upstream_weight');
                rs('#__FORM_RESULT__').unbind('load', enableInputDataAll);
            }

            function disableInputDataAll() {
                disableInputData('manual_down');
                disableInputData('upstream_ip');
                disableInputData('upstream_port');
                disableInputData('upstream_weight');
                rs('#__FORM_RESULT__').bind('load', enableInputDataAll);
            }

            return {

                init: function() {
                    if(!app.isCreateServer){
                        rs("input[name='site_name']").attr('readonly', true);
                        rs("input[name='site_port']").attr('readonly', true);
                        rs("rs\\:checkbox[id='divide_ipv46_enabled']").attr('readonly', false);
                        doUpstreamCheck();
                        rs('body').bind('switchQuerySysStatus', function() {
                            doUpstreamCheck();
                        });
                    }

                    if (matchDeployMode('mirror|transparent') || !global_params.isIPv46) {
                        mPage.divide_ipv46_enabled = false;
                    }

                    initServerListTable('serverList', {serverList: serverList});
                    initServerListTable('serverList_IPv4', {serverList_IPv4: serverList_IPv4});
                    initServerListTable('serverList_IPv6', {serverList_IPv6: serverList_IPv6});

                    enableInputData('manual_down');
                    enableInputData('upstream_ip');
                    enableInputData('upstream_port');
                    enableInputData('upstream_weight');
                },

                length: function() {
                    var table = document.named('serverList');
                    return table.data.length;
                },

                create: addUpstreamItem,
                handleOnCreateServerListCell: handleOnCreateServerListCell,
                handleOnCreateServerListHeaderCell: handleOnCreateServerListHeaderCell,
                needShowServerStatus: needShowServerStatus,
                load_balancing_strategy_enabled: load_balancing_strategy_enabled,
                getTableJsonData: getTableJsonData,
                getCheckListArr: getCheckListArr,
                disableInputDataAll: disableInputDataAll

            };

        })(app);

    {% endautoescape %}
    </script>

    <div innerSection
         rs:visible="mPage.is_upstream_https && page.expertMode && !matchDeployMode('mirror')"
         expertMode>
        <header>
            <span>{% trans 'Protection System To Protected Site SSL Algorithm' %}</span>
        </header>

        <rs:Table name="SSLAlogorithmConfigurationTable_server"
                  header="{% trans 'Parameter Name' %}|{% trans 'Parameter Value' %}"
                  cells="label|value">
            <div>
                <div v:text="rowData.label"></div>
                <div><input v:name="SSLAlgorithm.nameMap[rowData.label]"
                            v:value="mPage[SSLAlgorithm.nameMap[rowData.label]]"
                            type="text"/></div>
            </div>
        </rs:Table>
    </div>

    <script>
    {% autoescape off %}

        var SSLAlgorithm = (function($) {

            var mPage = $.mPage;
            var default_ssl_protos = {{ default_ssl_protos|to_json }};
            var default_ssl_ciphers = {{ default_ssl_ciphers|to_json }};
            var default_proxy_ssl_protocols = {{ default_proxy_ssl_protocols|to_json }};
            var default_proxy_ssl_ciphers = {{ default_proxy_ssl_ciphers|to_json }};

            rs.merge(mPage, {
                sslProtocol: upstreamConf.ssl_protocols||default_ssl_protos,
                sslCiphers: upstreamConf.ssl_ciphers||default_ssl_ciphers,
                proxySSLProtocols: upstreamConf.proxy_ssl_protocols||default_proxy_ssl_protocols,
                proxySSLCiphers: upstreamConf.proxy_ssl_ciphers||default_proxy_ssl_ciphers,
                SSLAlogorithmConfigurationTable_server: [
                    {label: '{% trans "Proxy_SSL_Protocols" %}'},
                    {label: '{% trans "Proxy_SSL_Ciphers" %}'}
                ]
            });

            rs.merge(mPage._validation, {
                sslProtocol: [function(value) {
                    if(mPage.is_https && value=='')
                        return "{% trans 'SSL Protocol cannot be blank' %}";
                }],

                sslCiphers:[function(value){
                    if(mPage.is_https && value=='')
                        return "{% trans 'SSL Ciphers cannot be blank' %}";
                }],

                proxySSLProtocols: [function(value) {
                    if(mPage.is_upstream_https && value=='')
                        return "{% trans 'Proxy SSL Protocols cannot be blank' %}";
                }],

                proxySSLCiphers:[function(value){
                    if(mPage.is_upstream_https && page.expertMode && value=='')
                        return "{% trans 'Proxy SSL Ciphers cannot be blank' %}";
                }]
            });

            return {
                init: function () {
                    setTimeout(function() {
                        form.update(document.body.named('SSLAlogorithmConfigurationTable_server'), ['proxySSLCiphers', 'proxySSLProtocols']);
                    }, 0);
                },
                nameMap: {
                    Proxy_SSL_Protocols: 'proxySSLProtocols',
                    Proxy_SSL_Ciphers: 'proxySSLCiphers'
                }
            };

        })(app);

    {% endautoescape %}
    </script>

    <div innerSection v:if="!matchDeployMode('mirror|transparent')">

        <input type="hidden" name="is_upstream_https" />
        <input type="hidden" name="enable_health_check">
        <input type="hidden" name="health_check_type">

        <ul class="load-balancing-strategy" rs:visible="!matchDeployMode('mirror|transparent')">
            <li>
                <label>{% trans "Load Balancing Strategy" %}</label>
                <rs:Select name="load_balancing_strategy" rs:disabled="!ServerList.load_balancing_strategy_enabled()">
                    <div rs-option value="ip_hash">{% trans 'IP Hash' %}</div>
                    <div rs-option value="round_robin">{% trans 'Round Robin' %}</div>
                    <div rs-option value="cookie_sticky">{% trans 'Cookie Sticky' %}</div>
                    <div rs-option value="least_conn">{% trans 'Least Connected' %}</div>
                </rs:Select>
            </li>
        </ul>

        <ul id="__HEALTH_EXAM_UL__" rs:visible="!matchDeployMode('transparent|mirror')">
            <li>
                <label>{% trans 'Health Examination' %}</label>
                <rs:Select name="health_check_open"
                           rsid="health_check_open"
                           class="health_check_select"
                           onchange="HealthCheck.onChange(event)"
                           rs:readonly="mPage.disableHealthCheck">
                    <div rs-option value="off">{% trans 'Close' %}</div>
                    <div rs-option value="tcp">{% trans 'TCP' %}</div>
                    <div rs-option value="http" rs:visible="/http:/i.test(mPage.upstream_protocol)">{% trans 'HTTP' %}</div>
                </rs:Select>

                <span class="health_check_options" rs:visible="mPage.health_check_open.match(/^(tcp|http)$/i)">
                    <label>{% trans 'Time Interval' %}</label>
                    <input class="health_check_input"
                           name="health_check_interval"
                           type="number"
                           min="1"
                           max="99"
                           title="{% trans 'Value must be between 1 and 99' %}"/>{% trans 'second(s)' %}

                    <label style="width: auto; margin-left: 10px;">{% trans 'Number of Retries' %}</label>
                    <input class="health_check_input"
                           name="health_check_reset_times"
                           type="number"
                           min="1"
                           max="99"
                           title="{% trans 'Value must be between 1 and 99' %}"/>{% trans 'time(s)' %}

                    <label style="width: auto; margin-left: 10px;">{% trans 'Timeout' %}</label>
                    <input class="health_check_input"
                       name="health_check_timeouts"
                       type="number"
                       min="1"
                       max="99"
                       title="{% trans 'Value must be between 1 and 99' %}"/>{% trans 'second(s)' %}
                </span>
            </li>

            {% if not isProxyRole %}
            <li>
                <label>&nbsp;</label>
                <span style="color: red">{% trans 'This is not a Proxy node, so it cannot check the status of the server(s).' %}</span>
            </li>
            {% endif %}
        </ul>

        <div class="healthCheckPanel" name="healthCheckPanel" rs:visible="!matchDeployMode('mirror|transparent')">
            <ul rs:visible="mPage.health_check_open.match(/^http$/i)">
                <li>
                    <label>&nbsp;</label>
                    <span>
                        <rs:Label><rs:Radio name="health_check_http_mode" value="path_ua"></rs:Radio>{% trans 'Path and UA' %}</rs:Label>
                        <rs:Label><rs:Radio name="health_check_http_mode" value="custom_request"></rs:Radio>{% trans 'Custom request' %}</rs:Label>
                        <span v:show="mPage.health_check_http_mode.match(/^custom_request$/i)" style="color: red;"> {% trans 'Please make sure the defined request headers/body are correct to avoid health check errors.' %}</span>
                        <span class="ic-info helpIcon" v:show="/^custom_request$/i.test(mPage.health_check_http_mode)" onmouseover="showInputTip(event,'name')" onmouseout="hideErrorTip(event)" style="vertical-align: baseline;"</span>
                    </span>
                </li>
                <ul rs:visible="mPage.health_check_http_mode.match(/^path_ua$/i)">
                    <li>
                        <label>{% trans 'Check Path' %}</label>
                        <input name="health_check_path" type="text" maxlength="128"/>
                    </li>
                    <li>
                        <label>{% trans 'Check User-Agent' %}</label>
                        <input name="health_user_agent" type="text" maxlength="128"/>
                    </li>
                </ul>
                <ul rs:visible="mPage.health_check_http_mode.match(/^custom_request$/i)">
                    <li>
                        <label>{% trans 'Request headers' %}</label>
                        <textarea name="health_check_http_custom_request_header" v:disabled="!editable" maxlength="1024" placeholder="{% trans 'Customize health check request headers' %}"></textarea>
                    </li>
                    <li>
                        <label>{% trans 'Request body' %}</label>
                        <textarea name="health_check_http_custom_request_body" v:disabled="!editable" maxlength="1024" placeholder="{% trans 'Customize health check request body' %}"></textarea>
                    </li>
                </ul>
            </ul>
        </div>
        <rs:Tooltip id="__errorCodeTip__">
            <div name="tooltipContent"></div>
        </rs:Tooltip>
    </div>

    <div rs:visible="page.expertMode && !matchDeployMode('mirror')"
         innerSection
         expertMode
         toggleSection collapse>
        <header>
            <span>{% trans 'Connection Timeout' %}</span>
        </header>
        <ul>
            <li>
                <label>{% trans 'proxy_connect_timeout' %}</label>
                <input name="proxy_connect_timeout" type="text" placeholder="10" />
                <rs:InfoIcon>{% trans 'The timeout (in seconds) of establishing connection between the DAP system and the protected server,usually no longer than 75 seconds.' %}</rs:InfoIcon>
            </li>
            <li>
                <label>{% trans 'proxy_send_timeout' %}</label>
                <input name="proxy_send_timeout" type="text" placeholder="60" pattern="[0-9]{0,6}" />
                <rs:InfoIcon>{% trans 'The timeout (in seconds) of transmitting a request to the protected server. The timeout is set only between two successive write operations, not for the whole request. If the protected server does not receive anything within this time, the connection will be closed.' %}</rs:InfoIcon>
            </li>
            <li>
                <label>{% trans 'proxy_read_timeout' %}</label>
                <input name="proxy_read_timeout" type="text" placeholder="300" />
                <rs:InfoIcon>{% trans 'The timeout (in seconds) of reading a response from the protected server. The timeout is set only between two successive read operations, not for the whole response. If the protected server does not transmit anything within this time, the connection will be closed.' %}</rs:InfoIcon>
            </li>
        </ul>
    </div>

    <script language="javascript">
    {% autoescape off %}

        var HealthCheck = (function($) {

            var upstreamConf = $.upstreamConf;
            var healthCheck = (!matchDeployMode('mirror') && upstreamConf.health_check) ? upstreamConf.health_check : { is_health_check_enable:false };
            healthCheck.health_check_open = healthCheck.is_health_check_enable ? healthCheck.health_check_type : 'off';
            healthCheck.enable_health_check = healthCheck.is_health_check_enable;
            healthCheck.health_check_http_mode = (healthCheck.health_check_http_mode ? healthCheck.health_check_http_mode : 'path_ua');
            healthCheck.health_check_http_custom_request_header = (healthCheck.health_check_http_custom_request_header ? healthCheck.health_check_http_custom_request_header : 'GET / HTTP/1.0\r\nUser-Agent:Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)');
            healthCheck.health_check_http_custom_request_body = (healthCheck.health_check_http_custom_request_body ? healthCheck.health_check_http_custom_request_body : '');

            rs.merge($.mPage, healthCheck, {
                disableHealthCheck: true,
                load_balancing_strategy: upstreamConf.load_balancing_strategy||'ip_hash'
            });

            rs.merge($.mPage._validation, {

                health_check_interval: [validateHealthCheckTimes],
                health_check_reset_times: [validateHealthCheckTimes],
                health_check_timeouts: [validateHealthCheckTimes],

                health_check_path: [function(value){
                    if (/^http$/i.test(mPage.health_check_open) && mPage.health_check_http_mode.match(/^path_ua$/i) && Validation.STRING(value))
                        return "{% trans 'Path cannot be blank.' %}";
                }],

                health_user_agent: [function(value){
                    if (/^http$/i.test(mPage.health_check_open) && mPage.health_check_http_mode.match(/^path_ua$/i) && Validation.STRING(value))
                        return "{% trans 'User Agent cannot be blank' %}";
                }],

                health_check_http_custom_request_header: [function(value){
                    if (/^http$/i.test(mPage.health_check_open) && mPage.health_check_http_mode.match(/^custom_request$/i))
                        if (Validation.STRING(value))
                            return "{% trans 'Value cannot be blank' %}";
                        if (value.length > 1024)
                            return "{% trans 'Customize health check request headers' %} {% trans 'Maximum length' %} 1024";
                        if (value && !(/^[!@&=+;?()\[\]\*\s\r\n0-9a-zA-Z:/,._-]+$/.test(value.trim())))
                            return "{% trans 'only support numbers, letters, spaces, carriage return, line feed and characters such as: ! @ & = + ; ? () [] * : / , . _ -' %} ";

                }],

                health_check_http_custom_request_body: [function(value){
                    if (/^http$/i.test(mPage.health_check_open) && mPage.health_check_http_mode.match(/^custom_request$/i))
                        if(value.length > 1024)
                            return "{% trans 'Customize health check request body' %} {% trans 'Maximum length' %} 1024";
                        if (value && !(/^[!@&=+;?()\[\]\*\s\r\n0-9a-zA-Z:/,._-]+$/.test(value.trim())))
                            return "{% trans 'only support numbers, letters, spaces, carriage return, line feed and characters such as: ! @ & = + ; ? () [] * : / , . _ -' %} ";
                }],

            });

            function initNumberInputBoxOfHealthCheckPanel(){
                document.child('.health_check_options .health_check_input').as(NumbericInput);
            }

            function validateHealthCheckTimes(value) {
                if (mPage.health_check_open != 'off') return Validation.POSITIVE_INT(value);
            }

            return {
                init: function() {
                    initNumberInputBoxOfHealthCheckPanel();
                },

                onChange: function(event){
                    var newValue = event.data;
                    mPage.enable_health_check = newValue != 'off';
                    mPage.health_check_type = mPage.enable_health_check ? newValue : mPage.health_check_type;
                }
            };

        })(app);

        function showInputTip(e,type) {
                var tips = {
                    'name':'{% trans "only support numbers, letters, spaces, carriage return, line feed and characters such as: ! @ & = + ; ? () [] * : / , . _ -" %}',
                    'cookie_token_refresh_rate':'{% trans "The interval at which a new token would be generated." %}',
                    'cookie_token_expiration_time':'{% trans "The length of time from generation of a token to its expiration." %}'
                }
                if(tips[type]) rs('#__errorCodeTip__').show(e.pageX,e.pageY,tips[type]);
        }

        function hideErrorTip(e) {
            rs('#__errorCodeTip__').hide();
        }

    {% endautoescape %}
    </script>

</section>

<!--获取源IP-->
<section toggleSection rsid="source_ip" id="SourceIps">
    <header>
        <span>{% trans 'Obtaining Source IP' %}</span>
        <p>{% trans 'Based on the current network structure, select the IP layer or request header X-Real-IP, X-Forwarded-For, or custom request header as the source IP. You can add up to 10 rules and match the rules in order from top to bottom.' %}</p>
    </header>

    <ul>
        <li>
            <label>{% trans 'Use global configuration' %}</label>
            <rs:Checkbox name="src_ip_use_global_setting" onChange="ObtainingSourceIP.handleOnchangeSrcIPCfg(event)" rsid="srcIPCheckbox"> </rs:Checkbox>
        </li>
    </ul>
    <!-- 用于数据展示 -->
    <rs:XSourceIP
        disabled="!editable"
        v:data="ObtainingSourceIP.sourceList"
        v:show="!mPage.src_ip_use_global_setting"
        onCreateItem="ObtainingSourceIP.handleCreateItem(event)">
    </rs:XSourceIP>
    <!-- 用于数据提交 -->
    <input type="hidden" name="src_ip_strategy_list" />

    <script language="javascript">
        var DEFAULT_RULE = [{ src_ip_from_type: '__SRCIP_TCPIP__', src_ip_customed_name: '', xff_position: 'last' }];

        var ObtainingSourceIP = (function($) {
            var mPage = $.mPage;
            var upstreamConf = $.upstreamConf;
            var _list = upstreamConf.src_ip_strategy_list;
            var siteSourceIpList = _list && _list.length > 0 ? _list : DEFAULT_RULE;

            rs.merge(mPage, {
                src_ip_use_global_setting: upstreamConf.src_ip_use_global_setting == null || upstreamConf.src_ip_use_global_setting,
                src_ip_strategy_list: JSON.stringify(siteSourceIpList)
            });

            return {
                sourceList: JSON.parse(JSON.stringify(siteSourceIpList)),

                init: function() {},

                handleOnchangeSrcIPCfg: function(evt) {
                    setTimeout(function() {
                        rs('#SourceIps').updateDynamicAttributes();
                    }, 100);

                },

                handleCreateItem: function(evt) {
                    rs('rs\\:XSourceIP').createItem(evt);
                }
            };
        })(app);
    </script>
</section>


<!--IP白名单-->
<section toggleSection rsid="ipWhitePane" rs:visible="!matchDeployMode('mirror')">
    <header>
        <span>{% trans 'IP-Based Protection' %}</span>
        <p>{% trans 'Pass through requests from certain IPs for testing or automation purposes.' %}</p>
    </header>

    <rs:Label><rs:Radio name="ip_white_black_list_switch" value="all_ip_white_list"></rs:Radio>{% trans 'Protect All IPs (Default)' %}</rs:Label>
    <rs:Label><rs:Radio name="ip_white_black_list_switch" value="ip_white_list"></rs:Radio>{% trans 'Do Not Protect Following IPs' %}</rs:Label>
    <rs:Label><rs:Radio name="ip_white_black_list_switch" value="ip_black_list"></rs:Radio>{% trans 'Protect Following IPs Only' %}</rs:Label>

   <div rs:visible="mPage.ip_white_black_list_switch=='ip_white_list'" style="margin-top:20px">

        <button type="button"
                rsid="ipWhiteBatchEditButton"
                onclick="BatchDialogHandler.openBatchEdit('ip_white_list')"
                v:show="editable">{% trans 'Batch editing' %}</button>

        <rs:IPConfTable name="ipWhiteList"
                        businessType="ip_white_list"
                        id="__IP_WHITE_LIST__"
                        header="{% trans 'Type' %}|IP / {% trans 'Start IP' %}|{% trans 'Netmask' %} / {% trans 'Prefix Length' %} / {% trans 'End IP' %}|{% trans 'Comment' %}|{% trans 'Actions' %}"
                        cells="whitelist_ip_type|whitelist_ip|whitelist_mask|whitelist_ip_comment|"
                        editable="#editable"></rs:IPConfTable>

       <div class="rsTableFooter" v:show="editable">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_addIPWhiteList"
                    onclick="PageHandler.getTable('ip_white_list').createNewRow()">{% trans 'New Configuration' %}</button>
        </div>
    </div>

    <div rs:visible="mPage.ip_white_black_list_switch=='ip_black_list'" style="margin-top:20px;">

        <button type="button"
                rsid="ipBlackBatchEditButton"
                onclick="BatchDialogHandler.openBatchEdit('ip_black_list')"
                v:show="editable">{% trans 'Batch editing' %}</button>

        <rs:IPConfTable name="ipBlackList"
                        businessType="ip_black_list"
                        id="__IP_BLACK_LIST__"
                        header="{% trans 'Type' %}|IP / {% trans 'Start IP' %}|{% trans 'Netmask' %} / {% trans 'Prefix Length' %} / {% trans 'End IP' %}|{% trans 'Comment' %}|{% trans 'Actions' %}"
                        cells="blacklist_ip_type|blacklist_ip|blacklist_mask|blacklist_ip_comment|"
                        editable="#editable"></rs:IPConfTable>

        <div class="rsTableFooter" v:show="editable">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_addIPBlackList"
                    onclick="PageHandler.getTable('ip_black_list').createNewRow()">{% trans 'New Configuration' %}</button>
        </div>
    </div>

    <script>

        var IPFilter = (function($) {

            var upstreamConf = $.upstreamConf;
            var ipWhiteList = $.initProtectList(upstreamConf.IpWhiteList, { whitelist_ip:'', whitelist_mask:'***************', whitelist_ip_comment:'', whitelist_ip_type:'mask' });
            var ipBlackList = $.initProtectList(upstreamConf.IpBlackList, { blacklist_ip:'', blacklist_mask:'***************', blacklist_ip_comment:'', blacklist_ip_type:'mask' });

            rs.merge($.mPage, {
                ip_white_black_list_switch: upstreamConf.IpListSwitch||'all_ip_white_list'
            });

            return {
                init: function() {
                    document.body.fill({
                        ipWhiteList: ipWhiteList,
                        ipBlackList: ipBlackList
                    });
                }
            };

        })(app);

    </script>
</section>

<!--健康检查-->
<section rs:visible="!matchDeployMode('mirror|plugin')">
    <header>
        <span>{% trans 'Health Check' %}</span>
        <p>
            {% trans 'After it is enabled, front network devices can send request to the configured access path to check the health status of the site.' %}</br>
            {% trans 'Note:' %}<span class="notice">{% trans 'the access path supports parameters but not regular expressions.' %}</span>
        </p>
    </header>

    <ul>
        <li>
            <label>{% trans 'Enable' %}</label>
            <rs:Checkbox name="enable_self_health_check"> </rs:Checkbox>
        </li>
        <li rs:visible="mPage.enable_self_health_check">
            <label>{% trans 'Path' %}</label>
            <input name="self_health_check_path" type="text" maxlength="128" onchange="SelfHealthCheck.onChange(event)" />
        </li>
    </ul>

    <script language="javascript">

        var SelfHealthCheck = (function($) {

            var upstreamConf = $.upstreamConf;

            rs.merge($.mPage, {
                self_health_check_path: upstreamConf.self_health_check_path || '/healthcheck',
                enable_self_health_check: upstreamConf.enable_self_health_check
            });

            return {
                init: function() {

                },

                onChange: function(evt) {
                    var value = evt.currentTarget.value.trim();
                    evt.currentTarget.value = value;
                }
            };

        })(app);

    </script>
</section>

<!--系统内部资源路径-->
<section toggleSection rs:visible="!matchDeployMode('mirror|plugin') && ({% visibleLayout 'internal_res_path' %} && global_params.isWaflayoutShowItem)">
    <header>
        <span>{% trans 'Internal Resource Path' %}</span>
        <p>{% trans 'The following path will be added to the URL of Internal Resources.' %}</p>
    </header>

    <ul>
        <li>
            <label>{% trans 'Path' %}</label>
            <input name="internal_res_path" type="text" maxlength="128" onchange="InternalResourcePath.onChange(event)" />
        </li>
    </ul>

    <script language="javascript">

        var InternalResourcePath = (function($) {

            var upstreamConf = $.upstreamConf;

            rs.merge($.mPage, {
                internal_res_path: upstreamConf.internal_res_path||'/',
                charset: upstreamConf.charset || '',
                enable_charset: upstreamConf.enable_charset,
                disable_upstream_keepalive: upstreamConf.disable_upstream_keepalive,
                keep_http_version: upstreamConf.keep_http_version
            });

            return {
                init: function() {

                },

                onChange: function(evt) {
                    var value = evt.currentTarget.value.trim();
                    if (!value) value = '/';
                    evt.currentTarget.value = value;
                }
            };

        })(app);

    </script>
</section>

<!--静态资源文件定义-->
<section expertMode v:show="page.expertMode && ({% visibleLayout 'static_source' %} && global_params.isWaflayoutShowItem) && !isApiProduct(global_params.productType)">
    <header>
        <span>{% trans 'Definition of static resource file' %}</span>
        <p>
            {% trans 'Configure extensions for static resource files, separated by commas.' %}
            <span v:show="!matchDeployMode('mirror')">{% trans 'Token validity of static resource requests will not be verified.' %}</span><br/>
            {% trans 'Note: The entered extension can only contain' %}<span class="notice">{% trans 'Letters, numbers, underscores (_), and dashes (-)' %}</span>
        </p>
    </header>
    <ul>
        <li>
            <label>{% trans 'Extension name' %}</label>
            <textarea name="static_resource_list" maxlength="2048" onchange="StaticResource.onChange(event)"></textarea>
        </li>
    </ul>
    <script language="javascript">

        var StaticResource = (function($) {

            var upstreamConf = $.upstreamConf;
            var staticResourceList = upstreamConf.static_resource_list;
            if (staticResourceList == null) {
                staticResourceList = '7z,a,aac,amr,apk,ar,asm,avi,bac,backup,bak,bat,bin,bmp,bz2,c,cab,cache,cbz,ceb,cebx,cert,cfm,cmake,coffee,com,conf,config,cpp,crt,css,csv,dat,db,deb,default,dll,dmg,doc,docx,dot,ehp,eml,env,eot,et,exe,fla,flac,flc,flv,fon,font,fot,gdb,gho,gif,git,gitignore,gz,gzip,hlp,hpp,htaccess,htc,htpasswd,ico,image,inc,include,inf,ini,ins,ipa,iso,jar,java,jpeg,jpg,js,json,key,lib,lock,log,lua,lzma,m4a,manifest,map,md,md5,mdb,mid,mim,mkv,mod,mov,mp3,mp4,mpa,mpeg,mpg,mpp,msi,mysql,nil,numbers,obj,ocx,odp,ods,odt,ogg,olb,old,ole,otf,out,ova,pages,pas,passwd,pcap,pdf,pem,pgm,pgsql,pic,pl,pli,plist,png,pom,ppm,pps,ppt,pptx,properties,psd,pub,pwd,py,pyc,qcow2,qif,qtx,ra,ram,rar,rb,reg,res,rm,rmvb,rpm,rtf,rtmp,sbl,sfx,sh,sha,sha1,so,sql,sqlite,sqlite3,sqlitedb,svg,swa,swf,swp,sys,tar,taz,temp,tgz,tif,tiff,tmp,torrent,tpl,tsv,ttf,txt,vb,vsd,vss,vsw,vxd,war,wav,webm,webp,wim,wma,wmv,woff,woff2,wps,xbm,xls,xlsx,xml,xpm,xsl,xz,yaml,yml,z,zip';
            }

            rs.merge($.mPage, {
                static_resource_list: staticResourceList
            });

            rs.merge($.mPage._validation, {
                static_resource_list:[function(value) {
                    if (/[^a-zA-Z_-\d\,]/.test(value))  return "{% trans 'The filename extension can only contain letters, numbers, underscores (_), and dashes (-).' %}";
                }]
            });

            return {
                init: function() {

                },

                onChange: function(evt) {
                    evt.stopPropagation();
                    var val = evt.target.value;
                    val = val.replace(/\s/g, '').replace(/[,]{2,}/g, ',');
                    if (val.indexOf(',')==0) val = val.substr(1,val.length);
                    if (val.lastIndexOf(',')==val.length-1) val = val.substr(0,val.length-1);
                    evt.target.value = val.toLowerCase();
                }
            };

        })(app);



    </script>
</section>

<!--HTTP请求头信息设置-->
<section rs:visible="page.expertMode && !matchDeployMode('mirror|plugin')" expertMode toggleSection rsid="httpRequestHeaderPane">
    <header>
        <span>{% trans 'HTTP Request Headers' %}</span>
        <p>{% trans 'Set the following information in a valid request forwarded to the server.' %}</p>
    </header>

    <ul class="HTTPResponseHeader">
        <li>
            <label>Accept-Encoding</label>
            <input name="Accept_Encoding" type="text" placeholder="{% trans "Accept-Encoding in HTTP header." %}"/>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_Accept_Encoding"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
        <li>
            <label>Host</label>
            <input name="Host" type="text" placeholder="{% trans "Host in HTTP header." %}"/>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_host"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
        <li>
            <label>X-Real-IP</label>
            <input name="X_Real_IP" type="text" placeholder="{% trans "X-Real-IP in HTTP header" %}"/>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_X_Real_IP"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
        <li>
            <label>X-Forwarded-For</label>
            <input name="X_Forwarded_For" type="text" placeholder="{% trans "X-Forwarded-For in HTTP header" %}"/>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_X_Forwarded_For"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
        <li>
            <label>WL-Proxy-Client-IP</label>
            <input name="WL_Proxy_Client_IP" type="text" placeholder="{% trans "WL-Proxy-Client-IP in HTTP header" %}"/>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_WL_Proxy_Client_IP"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
        <li>
            <label>{% trans 'Custom' %}</label>
            <span>
                <input name="Request_Custom_Head_Key" type="text" placeholder="{% trans 'HTTP header name' %}"/>
                <input name="Request_Custom_Head_Value" type="text" placeholder="{% trans 'HTTP header value' %}"/>
            </span>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_Request_Custom_Head"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
    </ul>

    <script>

        var HTTPRequestHeader = (function($) {

            var upstreamConf = $.upstreamConf;

            rs.merge($.mPage, {
                Accept_Encoding: upstreamConf.Accept_Encoding||'',
                Host: getDefaultParamValue(upstreamConf.Host),
                enable_Accept_Encoding: upstreamConf.enable_Accept_Encoding,
                enable_host: upstreamConf.enable_host,
                enable_X_Real_IP: upstreamConf.enable_X_Real_IP,
                enable_X_Forwarded_For: upstreamConf.enable_X_Forwarded_For,
                enable_WL_Proxy_Client_IP: upstreamConf.enable_WL_Proxy_Client_IP,
                enable_Request_Custom_Head: upstreamConf.enable_Request_Custom_Head,
                Request_Custom_Head_Key: upstreamConf.Request_Custom_Head_Key || '',
                Request_Custom_Head_Value: upstreamConf.Request_Custom_Head_Value||'',
                X_Real_IP: getDefaultParamValue(upstreamConf.X_Real_IP),
                X_Forwarded_For: getDefaultParamValue(upstreamConf.X_Forwarded_For),
                WL_Proxy_Client_IP: getDefaultParamValue(upstreamConf.WL_Proxy_Client_IP)
            });

            function getDefaultParamValue(val) {
                return val == null ? '' : val;
            }

            return {
                init: function() {

                }
            };

        })(app);

    </script>

</section>

<!--HTTP响应头信息设置-->
<section rs:visible="page.expertMode && !matchDeployMode('mirror|plugin')" expertMode toggleSection rsid="httpResponseHeaderPane">
    <header>
        <span>{% trans 'HTTP Response Headers' %}</span>
        <p>{% trans "Insert the following header when the server responds to the client." %}</p>
    </header>

    <ul class="HTTPResponseHeader">
        <li>
            <label>X-Frame-Options</label>
            <div>
                <rs:Select name="X_Frame_Options">
                    <div rs-option value="SAMEORIGIN">SAMEORIGIN</div>
                    <div rs-option value="DENY">DENY</div>
                    <div rs-option value="ALLOW-FROM">ALLOW-FROM</div>
                </rs:Select>
                <rs:Label class="httpResLabel"><rs:CheckBox name="enable_x_frame_option"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
                <input type="text"
                       name="x_frame_option_allow_uri"
                       rs:visible="mPage.X_Frame_Options=='ALLOW-FROM'"
                       value="{{ upstreamConf.x_frame_option_allow_uri }}"
                       placeholder="{% trans 'allowed URL' %}"/>

                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_Frame_Options=='SAMEORIGIN'">{% trans 'SAMEORIGIN means the page can only be displayed in a frame of the same origin as the page itself.' %}</div>
                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_Frame_Options=='DENY'">{% trans 'DENY means the page cannot be displayed in any frame.' %}</div>
                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_Frame_Options=='ALLOW-FROM'">{% trans 'ALLOW-FROM means the page can only be displayed in a frame of specified origin.' %}</div>
            </div>
        </li>
        <li>
            <label>X-Content-Type-Options</label>
            <div>
                <input type="text" value="nosniff" disabled />
                <rs:Label class="httpResLabel"><rs:CheckBox name="enable_X_Content_Type_Options"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
                <div class="HTTPResponseHeader_lineInfo">{% trans 'Sending the new X-Content-Type-Options response header with the value nosniff will prevent Internet Explorer from MIME-sniffing a response away from the declared content-type.' %}</div>
            </div>
        </li>
        <li>
            <label>X-XSS-Protection</label>
            <div>
                <rs:Select name="X_XSS_Protection">
                    <div rs-option value="0"> 0 </div>
                    <div rs-option value="1"> 1 </div>
                    <div rs-option value="1; mode=block"> 1; mode=block </div>
                    <div rs-option value="1; report="> 1; report= </div>
                </rs:Select>
                <rs:Label class="httpResLabel"><rs:CheckBox name="enable_X_XSS_Protection"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
                <input name="X_XSS_Protection_report_uri"
                       type="text"
                       rs:visible="mPage.X_XSS_Protection=='1; report='"
                       value="{{ upstreamConf.X_XSS_Protection_report_uri }}"
                       placeholder="{% trans 'Report URL' %}" />

                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_XSS_Protection=='0'">{% trans 'Disable XSS Protections.' %}</div>
                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_XSS_Protection=='1'">{% trans 'Enables XSS Protections.' %}</div>
                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_XSS_Protection=='1; mode=block'">{% trans 'Enable XSS protections and block pages from loading if XSS is detected.' %}</div>
                <div class="HTTPResponseHeader_lineInfo" rs:visible="mPage.X_XSS_Protection=='1; report='">{% trans 'It is a Chrome and WebKit only directive that tells the user-agent to report potential XSS attacks to a single URL. Data will be posted to the report URL in JSON format.' %}</div>
            </div>
        </li>
        <li>
            <label>P3P</label>
            <div>
                <div>policyref : /w3c/p3p.xml</div>
                <div>CP : </div>
                <textarea type='text' name='P3P_CP'>{{ upstreamConf.P3P_CP }}</textarea>
                <rs:Label class="httpResLabel"><rs:CheckBox name="enable_P3P_Options"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
            </div>
        </li>
        <li>
            <label>{% trans 'Custom' %}</label>
            <div name="Custom_Response_Setting">
                <input name="Response_Custom_Head_Key" type="text" placeholder="{% trans 'HTTP header name' %}"/>
                <input name="Response_Custom_Head_Value" type="text" placeholder="{% trans 'HTTP header value' %}"/>
                <rs:Select name="Response_Custom_Head_Mode">
                    <div rs-option value="append">{% trans 'Append' %}</div>
                    <div rs-option value="replace">{% trans 'Replace' %}</div>
                </rs:Select>
            </div>
            <rs:Label class="httpResLabel"><rs:CheckBox name="enable_Response_Custom_Head"></rs:CheckBox>{% trans 'Enable' %}</rs:Label>
        </li>
    </ul>

    <script>

        var HTTPResponseHeader = (function($) {

            var upstreamConf = $.upstreamConf;

            rs.merge($.mPage, {
                X_Frame_Options: upstreamConf.X_Frame_Options || 'SAMEORIGIN',
                enable_x_frame_option: upstreamConf.enable_x_frame_option,
                x_frame_option_allow_uri: upstreamConf.x_frame_option_allow_uri || '',
                enable_X_Content_Type_Options: upstreamConf.enable_X_Content_Type_Options,
                X_XSS_Protection: upstreamConf.X_XSS_Protection||'1; mode=block',
                enable_X_XSS_Protection: upstreamConf.enable_X_XSS_Protection,
                X_XSS_Protection_report_uri: upstreamConf.X_XSS_Protection_report_uri || '',
                enable_P3P_Options: upstreamConf.enable_P3P_Options,
                Response_Custom_Head_Key: upstreamConf.Response_Custom_Head_Key||'',
                Response_Custom_Head_Value: upstreamConf.Response_Custom_Head_Value||'',
                Response_Custom_Head_Mode: upstreamConf.Response_Custom_Head_Mode||'append',
                enable_Response_Custom_Head: upstreamConf.enable_Response_Custom_Head,
            });

            return {
                init: function() {

                }
            };

        })(app);

    </script>

</section>

<!--修正Content-Type-->
<section rs:visible="page.expertMode && !matchDeployMode('mirror|plugin')" expertMode toggleSection>
    <header>
        <span>{% trans 'Content-Type Correction' %}</span>
        <p>{% trans 'The DAP system will process responses containing paths specified below with appropriate measures according to the content type selected.' %}</p>
    </header>

    <ul>
        <li>
            <label>{% trans 'Enable' %}</label>
            <rs:Checkbox name="content_type_overwrite" rsid="contentTypeCheckbox"></rs:Checkbox>
        </li>
    </ul>
    <div rs:visible="mPage.content_type_overwrite" style="margin-bottom:20px;">
        <rs:Table name="ContentTypeOverwriteEntry"
                  editable="true"
                  header="{% trans 'Path' %}|{% trans 'Content Type' %}|{% trans 'Actions' %}"
                  cells="content_type_url|content_type_select|"
                  emptyText="{% trans 'No result' %}">
            <div>
                <div>
                    <input name="content_type_url" type="text" @change="handleChangeValue2Trim(event)" placeholder="{% trans 'Specify a path in regular expression' %}"/>
                </div>
                <div>
                    <rs:Select name="content_type_select" rs-lazy>
                        <div rs-option value="html">HTML</div>
                        <div rs-option value="javascript">Javascript</div>
                        <div rs-option value="resource">Resource</div>
                    </rs:Select>
                </div>
                <div>
                    <a href="javascript:void(0)"
                       @click="ContentTypeCorrection.remove(rowIndex)"
                       title="{% trans 'Delete ' %}"
                       class="inlineBlock ic-trash"
                       v:show="editable"></a>

                    <span v:show="!editable">--</span>
                </div>
            </div>
        </rs:Table>
        <div class="rsTableFooter" v:show="editable">
            <button type="button"
                    class="rsTableIncreaseButton"
                    rsid="bt_addContentTypeEntry"
                    onclick="ContentTypeCorrection.create()">{% trans 'New Configuration' %}</button>
        </div>
    </div>
    <input type="hidden" name="content_type_selector_hide"/>
    <input type="hidden" name="no_content_type">
    <script language="javascript">

        var ContentTypeCorrection = (function($) {

            var upstreamConf = $.upstreamConf;
            var table = rs('rs\\:Table[name=ContentTypeOverwriteEntry]');
            var ContentTypeOverwriteEntry = $.initProtectList(
                    upstreamConf.ContentTypeOverwriteEntry,
                    {content_type_url: '', content_type_select: 'html'}
                );

            rs.merge($.mPage, {
                content_type_overwrite: upstreamConf.content_type_overwrite,
                no_content_type: upstreamConf.no_content_type,
                content_type_selector_hide: ''
            });

            return {

                init: function() {
                    document.body.fill({
                        ContentTypeOverwriteEntry: ContentTypeOverwriteEntry
                    });
                },

                create: function() {
                    table.addRow({content_type_url:'', content_type_select:'html'});
                },

                remove: function(index) {
                    table.removeRow(index);
                }
            };

        })(app);

    </script>

    <!--字符集设置-->
    <div innersection rs:visible="page.expertMode && !matchDeployMode('mirror')" expertMode>
        <header>
            <span>{% trans 'Adding Charset' %}</span>
            <p>{% trans 'It allows the DAP system to add a Charset in Content-Type field of response headers if no Charset is found in the field.' %}</p>
        </header>

        <ul>
            <li>
                <label>{% trans 'Enable' %}</label>
                <rs:Checkbox name="enable_charset"></rs:Checkbox>
            </li>
            <li rs:visible="mPage.enable_charset">
                <label>Charset</label>
                <input name="charset" type="text" />
            </li>
        </ul>
    </div>
</section>

<!--高级配置-->
<section rs:visible="page.expertMode && !matchDeployMode('mirror|plugin')" expertMode toggleSection>
    <header>
        <span>{% trans 'Advanced Configuration' %}</span>
    </header>

    <div id="__Advanced_CONF_2__">
        <ul rs:visible="!matchDeployMode('mirror|transparent')">
            <li>
                <rs:Label>
                    <rs:Checkbox id='__DISABLE_UPSTREAM_KEEPALIVE__' name="disable_upstream_keepalive"></rs:Checkbox>{% trans 'Disable Keep-Alive on Server Side' %}
                </rs:Label>
            </li>
            <li>
                <rs:Label style="margin-right: 0px;">
                    <rs:Checkbox id='__KEEP_HTTP_VERSION__' name="keep_http_version"></rs:Checkbox>{% trans 'Keep HTTP Version of Request' %}
                </rs:Label>
                <i class="ic-info helpIcon" style="display: inline-block; margin-bottom: 2px;"
                    onmouseenter="page.titleTip(event.pageX, event.pageY, '{% trans "HTTP 2.0 is not supported by this feature." %}')"
                    onmouseleave="page.titleTipHide()"></i>
            </li>
        </ul>
    </div>

    {% if upstreamConf.scc and upstreamConf.scc.text %}
    <hr/>

    <div rs:visible="page.expertMode && !isApiProduct(global_params.productType)" expertMode rsid="sccSection">
        <header>
            <span>{% trans 'Website Config Editor' %}</span>
        </header>
        <div>
            {% if upstreamConf.scc.enable %}
                <p>
                    {% trans 'Website Config Editor is enabled.' %}
                </p>
            {% else %}
                <p>
                    {% trans 'Website Config Editor has data but not enabled.' %}
                </p>
            {% endif %}
            <p>
                {% trans 'Please going to [LAB] > [Website Config Editor] to edit.' %}
            </p>
            <pre style="padding: 4px; max-height: 150px;overflow: auto;border: 1px solid #313741" rsid="sccBlock">{{ upstreamConf.scc.text|escape }}</pre>
        </div>
    </div>
    {% endif %}

</section>


<script>

    var BasicPage = (function($) {

        var basicAdvancedHiddenMap = {
            'disable_123456': '{% trans "Disable /123456." %}'
        };

        var modules = [BasicMode, SiteName, ServerList, SSLAlgorithm, IPFilter, ObtainingSourceIP, StaticResource,
                        HealthCheck, HTTPRequestHeader, HTTPResponseHeader, ContentTypeCorrection, InternalResourcePath, SelfHealthCheck];
        
        function checkProtectionMode() {
            var fields = [];
            var itemsText = [];
            var checkedLeastOne = false;

            // TODO 读取element校验
            // Web标准保护、高级保护
            if (g_const.is_check_standard_and_advanced) {
                checkedLeastOne = checkedLeastOne || mPage.web_standard_protection || mPage.web_advanced_protection;
                fields = [].concat(['web_standard_protection', 'web_advanced_protection'], fields);
                if (isLicenseBeProtected) {
                    itemsText.push('<b>{% trans "Essential Web Protection" %}</b>');
                } else {
                    itemsText = [].concat(['<b>{% trans "Essential Web Protection" %}</b>', '<b>{% trans "Power Web Protection" %}</b>'], itemsText);
                }
            }
            // WAF 初级保护，仅layout为WAF，且license为动态WAF时才会存在
            if (g_const.is_check_primary && global_params.protectedLevel=='LE') {
                checkedLeastOne = checkedLeastOne || mPage.web_primary_protection;
                fields.push('web_primary_protection');
                itemsText.push('<b>{% trans "Primary Web Protection" %}</b>');
            }

            // App保护
            if (!matchDeployMode('mirror') && isMobileAppShow){
                checkedLeastOne = checkedLeastOne || mPage.mobile_sdk_protection;
                fields.push('mobile_sdk_protection');
                itemsText.push('<b>{% trans "Mobile Protection" %}</b>');
            }

            if (isEnabledAdvancedWaf) {
                // WAF
                checkedLeastOne = checkedLeastOne || mPage.injection_attack_interception;
                fields.push('injection_attack_interception');
                itemsText.push('<b>{% trans "WAF" %}</b>');

                // AI-WAF
                checkedLeastOne = checkedLeastOne || mPage.aiwaf_attack_interception;
                fields.push('aiwaf_attack_interception');
                itemsText.push('<b>{% trans "AI-WAF" %}</b>');
            }

            // 微信小程序保护同时受layouts和license控制，因此需要同时检查这两个条件
            if (g_const.is_check_wechat && isEnableWechatMPP) {
                checkedLeastOne = checkedLeastOne || mPage.wechat_mini_protection;
                fields.push("wechat_mini_protection");
                itemsText.push("<b>{% trans 'WeChat Mini-Program Protection' %}</b>");
            }

            if (g_const.is_check_alipay_mpp && isEnableAlipayMPP) {
                checkedLeastOne = checkedLeastOne || mPage.alipay_mini_program_protection;
                fields.push("alipay_mini_program_protection");
                itemsText.push("<b>{% trans 'Alipay Mini Program Protection' %}</b>");
            }

            if (g_const.is_check_mpaas_mpp && isEnableMPaasMPP) {
                checkedLeastOne = checkedLeastOne || mPage.mpaas_mpp;
                fields.push("mpaas_mpp");
                itemsText.push("<b>{% trans 'Mobile Mini Program Protection' %}</b>");
            }

            if (!matchDeployMode('plugin') &&isEnabledLLMProtection){
                checkedLeastOne = checkedLeastOne || mPage.llm_protection;
                fields.push("llm_protection");
                itemsText.push("<b>{% trans 'LLM Protection' %}</b>");
            }

            if (!matchDeployMode('mirror') && !checkedLeastOne) {
                rsalert(rs.formatString('{% trans "Please select at least one item from {0}." %}', itemsText.join('、')));
                return fields;
            }
        }

        function checkSiteProtocol() {
            if (matchDeployMode('mirror') && mPage.is_https && ServerList.length() == 0) {
                rsalert('{% trans "HTTPS server IP cannot be empty." %}');
                return rs('rs\\:Table[name="serverList"]');
            }
        }

        function checkCertificateWithoutBuiltInCert(){
            if (mPage.is_https) {
                if (matchDeployMode('mirror')) {
                    if (!mPage.key_file_name) {
                        rsalert("{% trans 'Please upload valid RSA private key file.' %}");
                        return ['key_file_name'];
                    }
                    return
                }

                if (!mPage.useChinaSecurityCert && !mPage.useInternationalCert) {
                    rsalert("{% trans 'Please select one of the HTTPS certificate type.' %}");
                    return rs('rs\\:Checkbox[rsid="useInternationalCert"]');
                }

                if (mPage.useInternationalCert && mPage.useBuiltInCert == 'false' && !(mPage.cert_file_name && mPage.key_file_name)) {
                    rsalert("{% trans 'Please upload a valid international certificate and private key.' %}");
                    return ['cert_plain_text'];
                }

                if (mPage.useChinaSecurityCert && mPage.useBuiltInChinaSecurityCert == 'false' && !(mPage.gm_sign_cert_file_name && mPage.gm_sign_key_file_name && mPage.gm_enc_cert_file_name && mPage.gm_enc_key_file_name)) {
                    rsalert("{% trans 'Please upload a valid China security certificate and private key.' %}");
                    return ['gm_sign_cert_plain_text'];
                }

            }
        }

        function checkUpstreamList(){
			if (matchDeployMode('plugin')) return null;
            var checkItmes = ServerList.getCheckListArr();
            var checkLen = checkItmes.length;
            for (var index = 0; index < checkLen; index++) {
                var serverListTable = document.named(checkItmes[index]);
                var tableName = checkItmes[index];
                var serverRows = serverListTable.rows;
                var len = serverRows.length;

                if (!matchDeployMode('mirror|transparent') && len == 0) {
                    rsalert("{% trans 'Please enter a correct IP.' %}");
                    return [checkItmes[index]];
                }

                var allUnEnable = true;
                for (var i = 0; i < len; i++) {
                    var row = serverRows[i];
                    var ip = row.data.upstream_ip;
                    var port = row.data.upstream_port;
                    if (matchDeployMode('mirror|transparent')) {
                        if (Validation.IP_AND_PREFIX(row.data.upstream_ip)) {
                            rsalert("{% trans 'Please enter an available IP address' %}");
                            return row.named('upstream_ip');
                        } else {
                            continue;
                        }
                    }

                    if (Validation.DOMAIN(ip)) {
                        if ((tableName == 'serverList' && Validation.IP(ip))
                        ||  (tableName == 'serverList_IPv4' && Validation.IPV4(ip))
                        ||  (tableName == 'serverList_IPv6' && Validation.IPV6(ip))) {
                            rsalert("{% trans 'Please enter an available IP address' %}");
                            return row.named('upstream_ip');
                        }
                    }

                    if (Validation.PORT(port)) {
                        rsalert("{% trans 'Invalid Port' %}");
                        return row.named('upstream_port');
                    }

                    if (row.named('upstream_weight')) {
                        errWeight = Validation.INT(row.data.upstream_weight)
                        if (errWeight == undefined) errWeight = Validation.OUT_OF_NUMBER_RANGE(row.data.upstream_weight, { min: 1, max: 10000})
                        if (errWeight) {
                            rsalert(errWeight);
                            return row.named('upstream_weight');
                        }
                    }

                    allUnEnable = allUnEnable && !row.data.manual_down;
                }

                if (!matchDeployMode('mirror|transparent') && allUnEnable) {
                    rsalert("{% trans 'Please start at least one service' %}");
                    return [tableName];
                }
            }
        }

        function checkWhiteBlackIPList() {
            var choose = mPage.ip_white_black_list_switch;
            if (choose == 'all_ip_white_list') return;
            var table = choose == 'ip_white_list' ? rs('rs\\:IpConfTable[name="ipWhiteList"]') :  rs('rs\\:IpConfTable[name="ipBlackList"]');
            return table.checkIpConfTable();
        }

        function checkHttpMethod() {
            if (!(mPage.limit_except1||mPage.limit_except2||mPage.limit_except3
                    ||mPage.limit_except4||mPage.limit_except5||mPage.limit_except6||mPage.limit_except9
                    ||mPage.limit_except10||mPage.limit_except11||mPage.limit_except12||mPage.limit_except13
                    ||mPage.limit_except14||mPage.limit_except15||mPage.limit_except16 )) {
                rsalert("{% trans 'Please select at least one HTTP request method.' %}");
                return ['limit_except1','limit_except2','limit_except3','limit_except4','limit_except5','limit_except6','limit_except9',
                        'limit_except10','limit_except11','limit_except12','limit_except13','limit_except14','limit_except15','limit_except16'];
            }
        }

        function checkWebsocketPaths() {
            var table = document.named("WebsocketPaths");
            if(!table || !table.is('DataContainer')) return;
            var data = table.data;
            for(var i=0; i<data.length; i++){
                var path = data[i]["websocket_path"];
                if (!path) {
                    rsalert('{% trans "Websocket path cannot be blank." %}');
                    return table.rows[i].named("websocket_path");
                }

                if (path.length > 256){
                    rsalert('{% trans "Websocket path is too long." %}');
                    return table.rows[i].named("websocket_path");
                }
            }

            var errorElem = app.checkTextFieldInTable('WebsocketPaths', 'websocket_path', "{% trans 'websocket path cannot be blank!' %}");
            if (errorElem) return ['websocket_path'];

            var repeatError = app.checkListHasRepeat('WebsocketPaths', 'websocket_path', "{% trans 'There are duplicate configuration items in websocket path.' %}");
            if (repeatError) return repeatError;
        }
        function checkInternalResPath() {
            var value = mPage.internal_res_path.trim();
            if ((value.length > 128) || (value.length == 0)){
                rsalert("{% trans 'Illegal path' %}");
                return ['internal_res_path'];
            }

            var pathArray = value.split("/");
            for (var i = 0; i < pathArray.length; i++){
                if ((pathArray[i] != "") && (/^[a-zA-Z0-9_.-]+$/.test(pathArray[i]) != true)) {
                    rsalert("{% trans 'Illegal path' %}");
                    return ['internal_res_path'];
                }

                if ((pathArray[i] == "") && (i != 0) && (i != pathArray.length - 1)){
                    rsalert("{% trans 'Illegal path' %}");
                    return ['internal_res_path'];
                }
            }
        }

        function checkSelfHealthCheck() {
            var value = mPage.self_health_check_path.trim();
            if ((value.length > 128) || (value.length == 0) || (value === '/')){
                rsalert("{% trans 'The path length cannot be larger than 128 characters, the path cannot be blank or /' %}");
                return ['self_health_check_path'];
            }

            var tmp = value;
            if (tmp[0] == '/') {
                tmp = tmp.slice(1);
            }

            if (tmp[tmp.length - 1] == '/') {
                tmp = tmp.slice(0,-1)
            }

            var pathArray = tmp.split("/");
            for (var i = 0; i < pathArray.length; i++){
                if ((pathArray[i] == "") || (pathArray[i] == "..") || (pathArray[i] == ".")
                    || (/^[a-zA-Z0-9_.?=&-]+$/.test(pathArray[i]) != true)) {
                    rsalert("{% trans 'Illegal path' %}");
                    return ['self_health_check_path'];
                }
            }
        }

        function checkEnableCharset() {
            if(!mPage.enable_charset) return;

            var value = mPage.charset = mPage.charset ? mPage.charset.trim() : '';
            if(!value) {
                rsalert('{% trans "The input of charset cannot be blank." %}');
                return ['charset'];
            }

            var msg = Validation.HAS_CHN(value);
            if (msg) {
                rsalert(msg);
                return ['charset'];
            }

            if (value.len() > PROXY_CONST.MAX_CHARSET_LENGTH) {
                rsalert('{% trans "The input of charset has exceeded the limit of 28 characters." %}');
                return ['charset'];
            }
        }

        function checkContentTypeList() {
            var isEnabled = mPage.content_type_overwrite;
            var table = document.named('ContentTypeOverwriteEntry');
            var list = table.data;
            if(isEnabled && list.length > 256) {
                rsalert("{% trans 'Content Type URL List exceed 256' %}");
                return ['ContentTypeOverwriteEntry'];
            }
            var urlTemp = [];
            var resultData = [];

            for(var i=0; i<list.length; i++){
                var item = list[i];
                if (!item) continue;
                item.content_type_url = item.content_type_url.trim();

                if (isEnabled) {
                    var errorMsg = checkUrlContent(item.content_type_url, urlTemp);
                    if (errorMsg) {
                        rsalert(errorMsg);
                        return table.rows[i].named('content_type_url');

                    } else {
                        urlTemp.push(item.content_type_url);
                    }
                }

                resultData.push({content_url: item.content_type_url, content_type: item.content_type_select});
            }

            mPage.content_type_selector_hide = rs.toJson(resultData);


            function checkUrlContent(urlContent, urlSet) {
                var errorMsg = '';
                var strlen = urlContent.length;
                if (strlen == 0) {
                    errorMsg = "{% trans 'Content Type URL cannot be blank' %}";
                } else if (strlen > 512) {
                    errorMsg = "{% trans 'The path entered should not exceed 512 characters.' %}";
                }

                // repeat
                if (urlSet.indexOf(urlContent) > -1) errorMsg = "{% trans 'Duplicate Content Type URL' %}";
                return errorMsg;
            }
        }

        function checkCustomHeader() {
            var isReqEnable = mPage.enable_Request_Custom_Head;
            var reqKey = mPage.Request_Custom_Head_Key = mPage.Request_Custom_Head_Key ? mPage.Request_Custom_Head_Key.trim():'';
            var reqValue = mPage.Request_Custom_Head_Value = mPage.Request_Custom_Head_Value ? mPage.Request_Custom_Head_Value.trim():'';
            var reqType = 'request';

            var isRespEnable = mPage.enable_Response_Custom_Head;
            var respKey = mPage.Response_Custom_Head_Key = mPage.Response_Custom_Head_Key ? mPage.Response_Custom_Head_Key.trim():'';
            var respValue = mPage.Response_Custom_Head_Value = mPage.Response_Custom_Head_Value ? mPage.Response_Custom_Head_Value.trim():'';
            var respType = 'response';

            var headerErrorMap = {
                keyIllegalMsg: '{% trans "The user-defined HTTP header name includes invalid character(s)." %}',

                request: {
                    keyName: ['Request_Custom_Head_Key'],
                    valueName: ['Request_Custom_Head_Value'],
                    emptyMsg: '{% trans "The user-defined HTTP request header name cannot be blank." %}',
                    keyLenMsg: '{% trans "The user-defined HTTP request header name has exceeded the limit of 128 characters." %}',
                    valueLenMsg: '{% trans "The user-defined HTTP request header value has exceeded the limit of 128 characters." %}'
                },
                response: {
                    keyName: ['Response_Custom_Head_Key'],
                    valueName: ['Response_Custom_Head_Value'],
                    emptyMsg: '{% trans "The user-defined HTTP response header name cannot be blank." %}',
                    keyLenMsg: '{% trans "The user-defined HTTP response header name has exceeded the limit of 128 characters." %}',
                    valueLenMsg: '{% trans "The user-defined HTTP response header value has exceeded the limit of 128 characters." %}'
                }
            };

            // ====== check Empty ======
            var reqEmptyError = checkHeaderEmpty(isReqEnable, reqKey, reqValue, reqType, headerErrorMap);
            if (reqEmptyError) return reqEmptyError;

            var respEmptyError = checkHeaderEmpty(isRespEnable, respKey, respValue, respType, headerErrorMap);
            if (respEmptyError) return respEmptyError;

            // ====== check Length ======
            var lenError = checkHeaderLen(reqKey,reqValue,reqType, respKey,respValue,respType, headerErrorMap);
            if (lenError) return lenError;

            // ====== check Illegal ======
            var illegalError = checkHeaderIllegalChar(reqKey,reqType, respKey,respType, headerErrorMap);
            if (illegalError) return illegalError;

        }

        function checkHeaderIllegalChar(reqKey,reqType, respKey,respType, headerErrorMap) {
            if (/[^\x21-\x39|\x3b-\x7e]/g.test(reqKey)) {
                rsalert(headerErrorMap['keyIllegalMsg']);
                return headerErrorMap[reqType].keyName;
            }

            if (/[^\x21-\x39|\x3b-\x7e]/g.test(respKey)) {
                rsalert(headerErrorMap['keyIllegalMsg']);
                return headerErrorMap[respType].keyName;
            }
        }

        function checkHeaderLen(reqKey,reqValue,reqType, respKey,respValue,respType, headerErrorMap) {
            var res = headerErrorMap[reqType];
            var resp = headerErrorMap[respType];

            if (reqKey.len() > PROXY_CONST.MAX_HEADER_LENGTH) {
                rsalert(res.keyLenMsg);
                return res.keyName;
            }

            if (reqValue.len() > PROXY_CONST.MAX_HEADER_VALUE_LENGTH) {
                rsalert(res.valueLenMsg);
                return res.valueName;
            }

            if (respKey.len() > PROXY_CONST.MAX_HEADER_LENGTH) {
                rsalert(resp.keyLenMsg);
                return resp.keyName;
            }

            if (respValue.len() > PROXY_CONST.MAX_HEADER_VALUE_LENGTH) {
                rsalert(resp.valueLenMsg);
                return resp.valueName;
            }
        }

        function checkHeaderEmpty(isEnable, key, value, type, headerErrorMap) {
            if ((key==''&&value) || (isEnable&&key=='')) {
                rsalert(headerErrorMap[type].emptyMsg);
                return headerErrorMap[type].keyName;
            }
        }

        function checkHttpRequsetInfo() {
            var inputs = rs('.HTTPResponseHeader')[0].child('input[type=text]');
            for(var i=0;i<inputs.length;i++){
                if(inputs[i].value.match(/\'|\"/)){
                    rsalert("{% trans 'Invalid character(s) found ' %}" + '\' \"' + "{% trans ' in HTTP Request Headers. ' %}");
                    return inputs[i];
                }
            }
        }

        function checkResponseInfoSettings(){
            if(/^ALLOW-FROM$/i.test(mPage.X_Frame_Options) && !validateUriWithSchema(mPage.x_frame_option_allow_uri)){
                rsalert("{% trans 'Invalid X-Frame-Option settings' %}");
                return ['x_frame_option_allow_uri'];
            }

            if(mPage.X_XSS_Protection=='1; report=' && !validateUriWithSchema(mPage.X_XSS_Protection_report_uri)){
                rsalert("{% trans 'Invalid X-XSS-Protection settings' %}");
                return ['X_XSS_Protection_report_uri'];
            }

            function validateUriWithSchema(str_url) {
                var Expression = /http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- .\/?%&=]*)?/;
                var objExp = new RegExp(Expression);
                return objExp.test(str_url);
            }
        }

        function checkSourceIp() {
            if (mPage.src_ip_use_global_setting) return;
            var error = rs('rs\\:XSourceIP').validate();
            if (error) return error.item;
        }

        return {

            init: function() {
                rs.each(modules, function(index, m) {
                    if (m.init) m.init();
                });
                $.initAdvancedConf(basicAdvancedHiddenMap, rs('#__Advanced_CONF_2__')); // 基础设置
            },

            checkList: function() {
                return [checkProtectionMode, checkSiteProtocol, checkCertificateWithoutBuiltInCert, checkInternalResPath, checkSelfHealthCheck, checkEnableCharset,
                    checkSourceIp, checkUpstreamList, checkWhiteBlackIPList, checkHttpMethod, checkWebsocketPaths,
                    checkContentTypeList, checkCustomHeader, checkHttpRequsetInfo, checkResponseInfoSettings];
            },

            prepareData: function() {
                changeCheckboxName('manual_down');
                if (mPage.divide_ipv46_enabled) {
                    mPage.serverList_IPv4_data = ServerList.getTableJsonData('serverList_IPv4');
                    mPage.serverList_IPv6_data = ServerList.getTableJsonData('serverList_IPv6');
                } else {
                    mPage.serverList_data = ServerList.getTableJsonData('serverList');
                }

                // 源IP
                if (!mPage.src_ip_use_global_setting) {
                    mPage.src_ip_strategy_list = JSON.stringify(rs('rs\\:XSourceIP').data);
                }
            },

            disableInvalidDataSubmit: function() {
                ServerList.disableInputDataAll();
            }
        };

    })(app);
    modules.push(BasicPage);

</script>

<style>

    input[name=site_name]{
        width: 240px !important;
    }

    input[name$=_port]{
        width: 80px;
        margin-right:20px;
    }

    .basicSettings > rs\:TabBox li:before{
        content: ' ';
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 1px solid #adafb3;
        border-radius: 100%;
        vertical-align: middle;
        margin-right: 10px;
    }
    .basicSettings > rs\:TabBox li[focus]:after{
        content: ' ';
        display: inline-block;
        width: 8px;
        height: 8px;
        background-color: white;
        border-radius: 100%;
        vertical-align: middle;
        margin-right: 10px;
        position: absolute;
        left: 19px;
        top: 13px;
    }
    .basicSettings > rs\:TabBox li{
        padding: 7px 14px;
        display: inline-block !important;
        border-radius: 3px;
        margin-right: 15px;
        position: relative;
        font-size: 14px;
        font-weight: normal;
    }

    .health_check_options {
        display: inline-block;
    }
    .health_check_options label {
        width: auto;
        margin-left: 0px;
        margin-right: 5px;
    }
    .health_check_options label:first-child {
        margin-left: 10px;
    }
    .health_check_options input[type=number] {
        width: 50px;
    }

    .sslTips {
        margin-bottom: 0px;
    }

    .HTTPResponseHeader > li > div{
        display: inline-block;
        vertical-align: top;
        line-height: 30px;
        /*color: #d6d7d9;*/
    }
    .HTTPResponseHeader > li > label{
        line-height: 30px;
    }

    .HTTPResponseHeader > li input,
    .HTTPResponseHeader > li textarea,
    .HTTPResponseHeader > li rs\:Select{
        width: 360px;
        margin-right:20px;
    }

    .HTTPResponseHeader > li textarea{
        height: 65px;
    }

    .HTTPResponseHeader > li rs\:Label.httpResLabel {
        width:auto;
        text-align:left;
        font-weight: 200 !important;
    }

    .HTTPResponseHeader_lineInfo {
        color: #949494;
        line-height: 30px;
    }

    .protocol-select {
        width:96px;
        vertical-align: top;
    }

    .HTTPResponseHeader > li input[name=Request_Custom_Head_Key],
    .HTTPResponseHeader > li input[name=Request_Custom_Head_Value],
    .HTTPResponseHeader > li input[name=Response_Custom_Head_Key],
    .HTTPResponseHeader > li input[name=Response_Custom_Head_Value]{
        width:168px;
    }

    rs\:ProtectModeGroup > rs\:Label { padding:4px 15px; background-color:#fff; font-size:14px; border-radius:3px; margin-right:15px; }
    rs\:ProtectModeGroup > rs\:Label > rs\:Radio { width:18px; height:18px; border:1px solid #adafb3; background-color:transparent;  }
    rs\:ProtectModeGroup > rs\:Label > rs\:Radio[checked=true] { background-color:transparent; border-color:white; }

    rs\:ProtectModeGroup > rs\:Label > rs\:Radio:hover,
    rs\:ProtectModeGroup > rs\:Label > rs\:Radio[checked=false]:hover,
    rs\:ProtectModeGroup > rs\:Label > rs\:Radio[checked=true]:hover {
        box-shadow:none;
        border-color:white;
        background-color:transparent;
    }

    rs\:ProtectModeGroup > rs\:Label:hover > rs\:Radio { border-color:white; }
    rs\:ProtectModeGroup > rs\:Label[checked=true], rs\:ProtectModeGroup > rs\:Label:hover { color:white; }

    rs\:ProtectModeGroup > rs\:Label[option='Open'][checked=true],
    rs\:ProtectModeGroup > rs\:Label[option='Open']:hover{
        background-color:#2FC5A8;
    }

    rs\:ProtectModeGroup > rs\:Label[option='LearningMode'][checked=true],
    rs\:ProtectModeGroup > rs\:Label[option='LearningMode']:hover {
        background-color:#935EA8;
    }

    rs\:ProtectModeGroup > rs\:Label[option='Close'][checked=true],
    rs\:ProtectModeGroup > rs\:Label[option='Close']:hover {
        background-color:#92B2B4;
    }

    input[name="site_customize_name"] { width:660px; }
    rs\:Select[rsid=server_name_type] { vertical-align: top; }
    input[name=entry_path] { display:inline-block; vertical-align:middle; width:410px; }

    rs\:Table[name=SSLAlogorithmConfigurationTable_nginx] div[table-cell]>p,
    rs\:Table[name=SSLAlogorithmConfigurationTable_server] div[table-cell]>p { margin-top:5px; margin-bottom:0px; color:#0085b3; }
    rs\:Table[name=SSLAlogorithmConfigurationTable_nginx] div[table-cell]:first-child
    rs\:Table[name=SSLAlogorithmConfigurationTable_server] div[table-cell]:first-child { width:25%; }

    .healthCheckPanel input[type=number]{
        width: 120px;
    }

    .healthCheckPanel input[name=health_check_path],
    .healthCheckPanel input[name=health_user_agent],
    input[name=entry],
    textarea[name=static_resource_list]{
        width: 50%;
    }

    textarea[name=health_check_http_custom_request_header],
    textarea[name=health_check_http_custom_request_body]{
    width: 720px;
    height:170px;
    line-height:1.5;
    }

    textarea[name=static_resource_list]{
        height:100px;
        line-height:1.5;
    }

    rs\:IPConfTable div[row=row]>div[table-cell]:first-child { width:190px; }
    div[name=Custom_Response_Setting] { display: inline-block; }
    div[name=Custom_Response_Setting] rs\:Select { display: block; margin-top:10px; }
    .invalid_action_control {
        width:130px;
    }
    div[name=httpsCertWrapper] {
        margin-top:5px;
    }

    div.siteInput { margin-bottom:15px; }
    #__HEALTH_EXAM_UL__ { margin-top:10px; }
    div[name=customizeNameWrapper] { margin-top:20px; margin-bottom:20px; }
    input[name=charset] {
        width:320px;
    }
    .load-balancing-strategy>li>label { vertical-align: middle; }
    [name=ContentTypeOverwriteEntry] rs\:Select {
        width:100%;
    }
    input[name=x_frame_option_allow_uri],
    input[name=X_XSS_Protection_report_uri]{
        margin-top:8px;
    }

    #__CERT_UPLOAD__,
    #__CERT_KEY_UPLOAD__ { line-height:20px; }

    #__GM_SIGN_CERT_UPLOAD__,
    #__GM_SIGN_CERT_KEY_UPLOAD__ { line-height:20px; }

    #__GM_ENC_CERT_UPLOAD__,
    #__GM_ENC_CERT_KEY_UPLOAD__ { line-height:20px; }

    .modeGroup {margin-bottom: 20px;}
    .modeGroup > .optionGroup { margin-top:20px; }
    .modeGroup > .optionGroup > rs\:Label { margin:0px 0px 5px 0px;  }
</style>
