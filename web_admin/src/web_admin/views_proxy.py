# encoding=utf-8
'''
@author: tom tang
@copyright: (c) 2014 River Security Technology Corporation, Ltd. All rights reserved.
'''

from django.http.response import HttpResponseBadRequest
import netaddr
import base64
import logging
import json
import os
import operator
import re
import tempfile
import hashlib
import ctypes
import random
import time
import cgi
import tarfile
try:
    from pcre import compile as pcre_compile
except:
    from re import compile as pcre_compile
from datetime import datetime
from django.http import HttpResponseRedirect, HttpResponse, StreamingHttpResponse, JsonResponse
from django.utils.translation import ugettext as _, ugettext_noop
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.html import escape
from Conf_Nginx import NginxConf, MOBILE_CONFIG_VERSION_V3_BIT
from generic.utils import unescape
from operation_log import operation_log
from proxy.proxy_models import NgxConfModel
from views import CONTENT_TYPE_JSON, CONTENT_TYPE_PLAIN, file_iterator, base_render_to_response
from view_lib import login_required, json_ok_response, json_fail_response, get_current_is_debug
from asp_conf_ctrl import ConfDb
from asp_utils.utils import get_release_file, get_manifest_info, get_product_type, exe_with_output, \
    DEFAULT_SSL_CIPHERS, DEFAULT_PROXY_SSL_CIPHERS, DEFAULT_SSL_PROTOCOLS, DEFAULT_PROXY_SSL_PROTOCOLS, DEFAULT_SECURITY_SSL_PROTOCOLS, DEFAULT_SECURITY_SSL_CIPHERS, \
    get_upgrade_lib_path, get_sync_file_path, RESULT, ip_to_int, mask_to_int, is_primary_protection, \
    get_reload_status_code, get_nginx_pids, get_cpu_core_num, get_all_good_bot_names, get_good_bot_version, validate_custom_geo_info, get_default_custom_geo, \
    parse_upload_ip_list, merge_exist_ip, delete_expired_ip, exe, is_port_already_in_use
from Conf_Base import BaseConf
from asp_utils.CommonLibs import compress_IP,valid_IP,is_Local_Loopback,valid_IPv4,valid_IPv6,valid_IP_or_CIDR
from web_admin.decorators import expert_mode_required, allow_method, check_permission, has_permission, check_permission_list
#from wizard import ip_to_int, mask_to_int, netaddr_parse, netaddr_build
from Conf_Webconsole import WebconsoleConf
from asp_utils.waf_util import get_waf_default_disabled_rules, include_file_mapping, get_default_waf_module_config
import asp_utils.waf_util as waf_util
from Conf_Waf import WafConf,WafStrategyCtrl
from Conf_Flowlearn import FlowlearnConf
from Conf_Mpp import WechatConf, AlipayConf, MPaasConf
from site_acl_util import (
    assign_site_4_user,
    get_sites_4_user,
    clean_site_config_2_blank,
    check_site_exists,
    UserNotExists,
    SiteNotExists,
)
from web_admin.decorators import adv_operation_code_required
from asp_utils.goodbots_auto_update import GoodBotUpgrade
from asp_utils.utils import OneClickSwitch

logger = logging.getLogger('views_proxy')

Protected_List_Type_Basic_Protection = 0
Protected_List_Type_Advanced_Protection = 1
Protected_List_Type_SDK = 2
Protected_List_Type_Injection = 3
Protected_List_Type_Automation = 4
Protected_List_Type_Crack = 5
Protected_List_Type_AIWAF = 6

JOSN_ARRAY_FIX_LEN = 3      # "" + ,

def all_protection_list():
    # New elements can only be added at the end
    ALL_PROTECTION_LISTS = [('web_standard_protection', True),
                            ('web_advanced_protection', False),
                            ('mobile_sdk_protection', False),
                            ('injection_attack_interception', False),
                            ('automated_tool_intercept', True),
                            ('crack_behavior_interception', True),
                            ('aiwaf_attack_interception', False),
                            ('ajax_response_encryption', False),
                            ('ajax_request_body_encryption', False),
                            ('wechat_mini_protection', False),
                            ('web_primary_protection', False),
                            ('alipay_mini_program_protection', False),
                            ('mpaas_mpp', False),
                            ('llm_protection', False),
                            ]
    return ALL_PROTECTION_LISTS


def all_protection_features():
    ALL_PROTECTION_FEATURES = [('cookie_token', _('Enable Cookie Token Protection')),
                               ('url', _('Enable URL Token Protection')),
                               ('form', _('Form Data Obfuscation')),
                               ('bot', _('Enable Bot Detection')),
                               ('cookie', _('Cookie Encryption')),
                               ]
    return ALL_PROTECTION_FEATURES


class URLRuleChecker(object):
    """
    请求白名单
        格式为：[reg, comment, method, case_sensitive, selected_type]
    响应白名单
        格式为：[reg, comment, case_sensitive, selected_type]
    封装名单
        格式为：[reg, comment, case_sensitive]
    验证名单
        格式为：[reg, comment, method, case_sensitive]
    Ajax免令牌名单
    Ajax请求/响应Body混淆名单
    Ajax响应Body混淆的Referer名单
        格式为：[reg, comment, case_sensitive]
    微信、支付宝、移动小程序请求白名单
        格式为：[reg, comment]
    """

    def __init__(self, name, rules, max_reg_length=1024):
        self.name = name
        self.rules = rules
        self.max_reg_length = max_reg_length
        self.max_comment_length = 1000

    def check(self):
        for rule in self.rules:
            reg = rule[0]
            comment = rule[1]

            if not check_single_quote_and_space(reg):
                return _('Invalid character ') + "<span style='color:red'>" + _("' and space") + "</span>"

            return self._common_check(reg, comment, self.max_reg_length)

    def _common_check(self, reg, comment, max_reg_length):
        if not check_regular_valid(reg):
            return _('Include invalid regular expression of {}').format(self.name)

        if len(reg) > max_reg_length:
            return _("The length of {0} can not exceed {1} characters.").format(self.name + _("path"), max_reg_length)

        if len(comment) > self.max_comment_length:
            return _("The length of {0} can not exceed {1} characters.").format(self.name + _("Comment"), self.max_comment_length)


class UrlRuleWithSelectTypeChecker(URLRuleChecker):
    """
    标准保护的请求/响应白名单，根据 select_type 动态设置 url 的最大允许长度
    """
    def __init__(self, name, rules, max_reg_length=1024):
        super(UrlRuleWithSelectTypeChecker, self).__init__(name, rules, max_reg_length)

    def check(self):
        URL_AS_REGEXP = 0
        URL_AS_SUFFIX_NAME = 1
        for rule in self.rules:
            reg = rule[0]
            comment = rule[1]
            select_type = rule[-1]

            if select_type != URL_AS_REGEXP:  # not regular
                max_reg_length = self.max_reg_length
                if "'" in reg:
                    return _('Invalid character ') + "<span style='color:red'>" + _("quotes") + "</span>"
            else:
                max_reg_length = 6000
                if not check_single_quote_and_space(reg):
                    return _('Invalid character ') + "<span style='color:red'>" + _("' and space") + "</span>"

            error = self._common_check(reg, comment, max_reg_length)
            if error:
                return error

            if select_type == URL_AS_SUFFIX_NAME:
                # 名单类型选择“路径后缀为”时，表达式用逗号分割，每个后缀名不能为空或长度超过10字符
                for suffix in reg.split(','):
                    suffix_len = len(suffix)
                    if suffix_len == 0 or suffix_len > 10:
                        return _('The length of each suffix cannot exceed 10 characters.')
                    if not re.match('^[a-zA-Z0-9]+$', suffix):
                        return _('The suffix name only supports letters and numbers, and multiple values are separated by English commas.')

@login_required
@check_permission('Add_Website', 'write')
def check_file_size(request):
    jsonFile = request.FILES.get('rawdata')
    file_size = jsonFile.size
    if file_size == 0:
        return HttpResponse(json.dumps({'result':False, 'msg':'file content is empty'}), content_type=CONTENT_TYPE_PLAIN)
    else:
        return HttpResponse(json.dumps({'result':True, 'msg':''}), content_type=CONTENT_TYPE_PLAIN)

# 和前端校验单引号和空格的函数validateSingleInputValue等价
def check_single_quote_and_space(value):
    if re.search("'|\s", value):
        return False
    else:
        return True

def check_regular_valid(regular):
    try:
        re.compile(regular)
        return True
    except:
        return False

def check_pcre_regex_valid(regular):
    try:
        pcre_compile(regular)
        return True
    except:
        return False

def check_tamper_config(model,max_length):
    result = None
    model_name = _('Tamper Cache path')

    if not model.enable_tamper:
        return result

    check_fun_dict = {
        'tamper_diskspace': (lambda x: valid_number_range(x, max_v=100, min_v=1))
    }
    check_auto_update = {
        'tamper_web_similarity': (lambda x: valid_number_range(x, max_v=100, min_v=0)),
        'tamper_expire_time': (lambda x: valid_number_range(x, max_v=60, min_v=1)),
    }
    if model.tamper_auto_update:
        check_fun_dict.update(check_auto_update)

    result = valide_rule(model,check_fun_dict,model_name)

    if result is None:
        result = valid_url_list(model.tamper_cache_urls, max_length, model_name)

    return result

def valid_url_list(url_list,max_length, model_name):
    result = None
    if len(url_list) > max_length:
        result =  _('{0} list has already reached limit {1}.').format(model_name, max_length)

    if result is None:
        for item in url_list:
            if not check_regular_valid(item[0]):
                result = _('Invalid regular expression of {0}.').format(model_name)
                break

    return result

def valide_rule(model, check_fun_dict, model_name):
    for k, func in check_fun_dict.items():
        val = getattr(model,k)
        try:
            func(val)
        except Exception as e:
            logging.error(e.message)
            return _('{} config Error').format(model_name)

def valid_number_range(numb, max_v=60, min_v=1):
    try:
        numb = int(numb)
        if not (min_v <= numb <= max_v):
            raise Exception(ugettext('{} is out of the range'), numb)
    except Exception as e:
        raise Exception(ugettext('{} format error: {}'), numb, numb_type)

def valid_upstream_weight(upstream):
    if len(upstream) < 4:
        return True

    if not str(upstream[3]).isdigit():
        return False

    upstream[3] = int(upstream[3])
    if upstream[3] < 1 or upstream[3] > 10000:
        return False

    return True

def valid_src_ip_strategy_list(src_ip_strategy_list):
    if not isinstance(src_ip_strategy_list, list) or len(src_ip_strategy_list) < 1 or len(src_ip_strategy_list) > 10:
        return _('Invalid source IP acquisition strategy')

    check_list = []
    type_list = ['__SRCIP_TCPIP__', 'X-Forwarded-For', 'X-Real-IP', 'Custom']
    type_list_lower = [t.lower() for t in type_list]
    position_list = ['last', 'penultimate', 'first']

    for src_ip_strategy in src_ip_strategy_list:
        if not isinstance(src_ip_strategy, dict) or len(src_ip_strategy.keys()) > 3:
            return _('Invalid source IP acquisition strategy')

        from_type = src_ip_strategy.get('src_ip_from_type')
        from_name = src_ip_strategy.get('src_ip_customed_name')
        from_position = src_ip_strategy.get('xff_position', 'last')
        if from_type not in type_list or from_position not in position_list or len(from_name) > 128:
            return _('Invalid source IP acquisition strategy')

        check_item = from_type
        if from_type == 'Custom':
            if from_name is None or from_name.lower() in type_list_lower:
                return _('Invalid source IP acquisition strategy')
            if not re.match('^[a-zA-Z_0-9\-]+$', from_name):
                return _('Custom headers only support letters, numbers, underscores, and hyphens')
            check_item = from_name
        elif from_type == 'X-Forwarded-For':
            check_item = from_type + from_position

        if check_item in check_list:
            return _('Invalid source IP acquisition strategy')
        else:
            check_list.append(check_item)

def update_src_ip_strategy_list(upstreamConf):
    src_ip_strategy_list = upstreamConf.get('src_ip_strategy_list')
    if src_ip_strategy_list is None:
        src_ip_from = upstreamConf.get('src_ip_from', '')
        xff_position = upstreamConf.get('xff_position', 'last')
        if src_ip_from:
            src_ip_from_type, src_ip_customed_name = (src_ip_from, '') if src_ip_from in ('X-Forwarded-For', 'X-Real-IP') else ('Custom', src_ip_from)
            upstreamConf['src_ip_strategy_list'] = [{'src_ip_from_type': src_ip_from_type, 'src_ip_customed_name': src_ip_customed_name, 'xff_position': xff_position}]
    if isinstance(src_ip_strategy_list, list):
        upstreamConf['src_ip_strategy_list'] = src_ip_strategy_list[:10]

def get_src_ip_strategy_list(request):
    src_ip_strategy_list = request.POST.get('src_ip_strategy_list')
    if src_ip_strategy_list == None:
        from_type = request.POST.get('src_ip_from_type', '')
        from_name = request.POST.get('src_ip_customed_name', '')
        from_position = request.POST.get('xff_position', 'last')
        if from_type == '':
            from_type = '__SRCIP_TCPIP__'
        src_ip_strategy_list = json.dumps([{'src_ip_from_type': from_type, 'src_ip_customed_name': from_name, 'xff_position': from_position}])
    return src_ip_strategy_list

def get_license_protected_level():
    conf = ConfDb()
    license_info = conf.get_license_info(i18n_support=False, care_cluster= False)
    return license_info.get_license_protected_level()


def set_default_health_check_conf(upstreamConf):
    # PreVersion not have health check. set default
    if upstreamConf.get('health_check') is None:
        health_check_collection = {
            'is_health_check_enable': False,
            'health_check_type': 'tcp',
            "health_check_reset_times": "3",
            "health_check_path": "/",
            "health_check_interval": "5",
            "health_check_timeouts": "5",
            "health_user_agent": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)",
            "health_check_http_mode":"path_ua",
            "health_check_http_custom_request_header":"GET / HTTP/1.0\r\nUser-Agent:Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)",
            "health_check_http_custom_request_body":""
        }
        upstreamConf['health_check'] = health_check_collection

        def setDefaultUpstreamListConf(Ups):
            UpstreamList = upstreamConf.get(Ups,[])
            for upstream in UpstreamList:
                upstream.append(True)
            upstreamConf[Ups] = UpstreamList

        setDefaultUpstreamListConf('UpstreamList')
        setDefaultUpstreamListConf('UpstreamList_IPv4')
        setDefaultUpstreamListConf('UpstreamList_IPv6')
    elif upstreamConf['health_check'].get('health_user_agent') is None:
        upstreamConf['health_check'][
            'health_user_agent'] = 'Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)'
    else:
        pass


def util_check_ip_conflict(left_ip, right_ip):
    for left in left_ip:
        left_ip_int, left_ip_ver = ip_to_int(left.get('ip'))
        left_mask_int, left_mask_ver = mask_to_int(left.get('mask'))
        for right in right_ip:
            right_ip_int, right_ip_ver = ip_to_int(right.get('ip'))
            right_mask_int, right_mask_ver = mask_to_int(right.get('mask'))
            if ((left_ip_ver + left_mask_ver + right_ip_ver + right_mask_ver) / 4) <= 0:
                ret = {'message': 'IP version is not same', 'result': 'error'}
                return ret
            if right_ip_int < 0:
                ret = {'message': 'invalid ip {0}'.format(right.get('ip')), 'result': 'error'}
                return ret

            if left_ip_int < 0:
                ret = {'message': 'invalid ip {0}'.format(left.get('ip')), 'result': 'error'}
                return ret

            if right_ip_int == left_ip_int:
                ret = {'message': 'ip conflict {0}'.format(left.get('ip')), 'result': 'error'}
                return ret

            tmp_set = set()
            left_and_oper = left_ip_int & left_mask_int
            tmp_set.add(left_and_oper)
            left_and_right_oper = left_ip_int & right_mask_int
            tmp_set.add(left_and_right_oper)
            right_and_oper = right_ip_int & right_mask_int
            tmp_set.add(right_and_oper)
            right_and_left_oper = right_ip_int & left_mask_int
            tmp_set.add(right_and_left_oper)

            if len(tmp_set) != 4:
                ret = {'message': 'Protect IP list and Black IP list have conflicted IPs', 'result': 'error'}
                return ret

    return ''

def convert_condition(key, condition, orgCondition="", toShow=False):
    if key == 'isroot' or key == 'isemulator' or key == 'hasxposed' or key == 'hascydiasubstrate' or key == 'hasappium':
        if condition == '1':
            return 'True'
        elif condition == '0':
            return 'False'
        elif condition == 'true':
            return '1'
        elif condition == 'false':
            return '0'

    if key == 'platform':
        if condition == 'ios':
            return 'iOS'
        if condition == 'android':
            return 'Android'

    if key == 'fingerprint':
        index = orgCondition.lower().find(condition)
        if index != -1:
            return orgCondition[index : index + len(condition)]

    if key == 'path':
        index = orgCondition.lower().find(condition)
        if index != -1:
            condition =  orgCondition[index : index + len(condition)].replace('"', '').replace("'", "")
        if toShow:
            condition = condition.replace('|', ' | ')

    return condition

def convert_key(key):
    if key == 'appversion':
        return 'APPVersion'
    elif key == 'sdkversion':
        return 'SDKVersion'
    elif key == 'platform':
        return 'Platform'
    elif key == 'isroot':
        return 'IsRoot'
    elif key == 'isemulator':
        return 'IsEmulator'
    elif key == 'fingerprint':
        return 'Fingerprint'
    elif key == 'hasxposed':
        return 'HasXposed'
    elif key == 'hascydiasubstrate':
        return 'HasCydiaSubstrate'
    elif key == 'hasappium':
        return 'HasAppium'
    elif key == 'path':
        return 'Path'

    return key

def get_mobile_protect_rules_list(rules_str):
    if not rules_str:
        return []
    out_list = []
    last_rule = {}
    rules_list = rules_str.split(':')
    for rule in rules_list:
        item = rule.split(',')
        if len(item) != 5:
            logger.info('Load error mobile protect config! rule is {0}'.format(rule))
            return []

        tmp = {}
        tmp['rule_id'] = item[0]
        tmp['rule_action'] = item[1]
        tmp['rule_condition'] = convert_key(item[2]) + " " + item[3] + " " + convert_condition(item[2], item[4], toShow=True)
        if not last_rule:
            last_rule = tmp
            continue

        if last_rule['rule_id'] == tmp['rule_id']:
            last_rule['rule_condition'] = last_rule['rule_condition'] + ' AND ' + tmp['rule_condition']
            continue
        else:
            out_list.append(last_rule)
            last_rule = tmp

    out_list.append(last_rule)
    return out_list

def split_mobile_rule(condition, orgCondition):
    condition = condition.replace(' ', '')
    orgCondition = orgCondition.replace(' ', '')
    if '<=' in condition:
        regx = condition.split('<=')
        return regx[0] + ',' + '<=' + ',' + convert_condition(regx[0], regx[1], orgCondition)
    elif '<' in condition:
        regx = condition.split('<')
        return regx[0] + ',' + '<' + ',' + convert_condition(regx[0], regx[1], orgCondition)
    elif '>=' in condition:
        regx = condition.split('>=')
        return regx[0] + ',' + '>=' + ',' + convert_condition(regx[0], regx[1], orgCondition)
    elif '>' in condition:
        regx = condition.split('>')
        return regx[0] + ',' + '>' + ',' + convert_condition(regx[0], regx[1], orgCondition)
    elif '==' in condition:
        regx = condition.split('==')
        return regx[0] + ',' + '==' + ',' + convert_condition(regx[0], regx[1], orgCondition)
    elif '!=' in condition:
        regx = condition.split('!=')
        return regx[0] + ',' + '!=' + ',' + convert_condition(regx[0], regx[1], orgCondition)

def get_mobile_protect_rules_str(id, action, condition):
    length = len(id)
    if length != len(action) or length != len(condition):
        logger.info('Error mobile protect rules length!')
        return ""

    rule_list = []
    for i in range(0, length):
        condition_list = []
        condition_str = condition[i].lower()
        if ' and ' in condition_str:
            condition_list = condition_str.split(' and ')

        if condition_list:
            for j in range(0, len(condition_list)):
                rule = id[i] + ',' + action[i] + ',' + split_mobile_rule(condition_list[j], condition[i])
                rule_list.append(rule)
        else:
            rule = id[i] + ',' + action[i] + ',' + split_mobile_rule(condition_str, condition[i])
            rule_list.append(rule)

    return ':'.join(rule_list)

def upgrade_inject_whitelist(old_full_inject_whitelist):

    '''
    uprade len(inject_whitelist) == 3 or len(inject_whitelist) == 4 to len(inject_whitelist) == 5
    :param old_full_inject_whitelist:
    :return: full_inject_whitelist
    '''
    full_inject_whitelist = []
    eq_inject_white_list = {}
    for index,inject_whitelist_item in enumerate(old_full_inject_whitelist):
        inject_wl_id = ''
        inject_wl_path = ''
        inject_wl_type = ''
        inject_wl_comment = ''
        if len(inject_whitelist_item) == 5:
            inject_wl_type, inject_wl_path,inject_wl_id, inject_wl_arg, inject_wl_comment = \
                inject_whitelist_item[0], inject_whitelist_item[1], inject_whitelist_item[2], \
                inject_whitelist_item[3],inject_whitelist_item[4]
        elif len(inject_whitelist_item) == 4:
            inject_wl_type, inject_wl_path, inject_wl_arg, inject_wl_comment = \
                inject_whitelist_item[0], inject_whitelist_item[1], inject_whitelist_item[2], \
                inject_whitelist_item[3]
            if inject_wl_type == 'id':
                inject_wl_id = inject_wl_path
                inject_wl_path = ''

        elif len(inject_whitelist_item) == 3:
            # upgrade from old version to len 5
            inject_wl_type = 'url'
            inject_wl_path, inject_wl_arg, inject_wl_comment = inject_whitelist_item[0], inject_whitelist_item[1], \
                                                               inject_whitelist_item[2]

        #meger the rule id with same path
        path_key = '@beginwith /'
        if inject_wl_path != '':
            path_key = inject_wl_path
        eq_inject_obj = eq_inject_white_list.get(path_key)
        inject_wl_arg = ""
        if eq_inject_obj == None:
            # str(index) is used to sort the full_inject_whitelist(the element position is inject_wl_type not used before)
            eq_inject_obj = [str(index), inject_wl_path, inject_wl_id, inject_wl_arg, inject_wl_comment]
        else:
            if eq_inject_obj[2] == inject_wl_id:
                pass
            elif eq_inject_obj[2] == "" or inject_wl_id == "":
                eq_inject_obj[2] = ""
            else:
                new_ids = inject_wl_id.split(',')
                old_ids = eq_inject_obj[2].split(',')
                eq_inject_obj[2] = ','.join(list(set(new_ids + old_ids)))

            if inject_wl_comment != "" and len(eq_inject_obj[4] + "\n" + inject_wl_comment) < 1024 \
                    and inject_wl_comment not in eq_inject_obj[4]:
                if len(eq_inject_obj[4]) == 0:
                    eq_inject_obj[4] = inject_wl_comment
                else:
                    eq_inject_obj[4] += "\n" + inject_wl_comment

        eq_inject_white_list[path_key] = eq_inject_obj

    for item in eq_inject_white_list:
        item_val = eq_inject_white_list.get(item)
        full_inject_whitelist.append(item_val)

    full_inject_whitelist.sort()
    return full_inject_whitelist



@login_required
def check_white_ip_conflict(request):
    if request.method == "POST":
        ret = {}
        data = json.loads(request.body)

        left_ip = data.get('left_ip')
        right_ip = data.get('right_ip')

        ret = util_check_ip_conflict(left_ip, right_ip)

        if ret:
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

        tmp_ip = left_ip
        left_ip = right_ip
        right_ip = tmp_ip

        ret = util_check_ip_conflict(left_ip, right_ip)

        if ret:
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

        ret = {'message': 'ok', 'result': 'ok'}

        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)


@login_required
@csrf_exempt
@check_permission('Edit_Website_Config', 'write')
def proxy_get_plain_text(request):
    ret_data = {}
    upload_file = request.FILES.get('upload_file')
    if upload_file:
        file_size = upload_file.size
        if file_size < 2 ** 20:
            plain_text = upload_file.read()
            try:
                # check whether upload file is plain text by using json dumps
                json.dumps(plain_text)
                ret_data['plain_text'] = plain_text
            except:
                logger.info('upload file is not plain text')
        file_name = upload_file.name
        ret_data['file_name'] = file_name
    return HttpResponse(json.dumps(ret_data), content_type=CONTENT_TYPE_PLAIN)


def has_permission_of_website(username, server_key, action='write'):
    # the role of admin is Administrator
    role = WebconsoleConf().get_user_role(username)
    if role == 'Administrator':
        return True
    if role == 'Viewer' and action == 'read':
        return True
    if role == 'Operator' and server_key in [site['key'] for site in get_sites_4_user(username)]:
        return True
    if role == 'StatisticViewer' and action == 'read' and server_key in [site['key'] for site in get_sites_4_user(username)]:
        return True
    if role not in ('Administrator', 'Viewer', 'Operator', 'StatisticViewer'):
        return True
    return False


def health_check_custom_request_validity(request):
    # health check custom headers and body
    is_health_check_enable = request.POST.get('enable_health_check') == 'true'
    health_check_type = request.POST.get('health_check_type')
    health_check_http_mode = request.POST.get('health_check_http_mode')
    health_check_http_custom_request_header = request.POST.get('health_check_http_custom_request_header')
    health_check_http_custom_request_body = request.POST.get('health_check_http_custom_request_body')
    if is_health_check_enable and health_check_type == 'http' and health_check_http_mode == 'custom_request':
        if health_check_http_custom_request_header:
            if str(health_check_http_custom_request_header).strip() == '' or len(str(health_check_http_custom_request_header).strip()) > 1024 or not re.match('^[!@&=+;?()\[\]\*\s\r\n0-9a-zA-Z:/,._-]+$', health_check_http_custom_request_header):
                return False
        else:
            return False

        if health_check_http_custom_request_body:
            if len(str(health_check_http_custom_request_body).strip()) > 1024 or not re.match('^[!@&=+;?()\[\]\*\s\r\n0-9a-zA-Z:/,._-]+$', health_check_http_custom_request_body):
                return False
    return True


def regex_consistence_check(regex_text, test_obj):
    error = None
    regex = 'r' + "'" + regex_text + "'"
    try:
        re.match(regex, test_obj)
        regex_consistence = ctypes.cdll.LoadLibrary(get_release_file('regex_consistence.so'))
        if isinstance(regex_text, unicode):
            normalized = regex_text.encode("utf-8")
        else:
            normalized = regex_text
        boost_check = bool(
            regex_consistence.check_boost_regex(normalized, len(normalized)))
        pcre_check = bool(
            regex_consistence.check_pcre_regex(normalized, len(normalized)))

        if not boost_check or not pcre_check:
            error = _('Regex(%s) is incorrect') % (normalized)
    except Exception, e:
        error = _('Regex(%s) is incorrect') % (regex_text)

    return error


def get_folder_size(path):
    size = 0
    if not os.path.exists(path):
        return size

    lt = os.listdir(path)
    for i in lt:
        s = os.path.join(path, i)
        if os.path.isdir(s):
            size += get_folder_size(s)
        else:
            size += os.path.getsize(s)
    return size


def check_content(context):
    if len(context) > 1024 * 1024:
        return False, '', _('Single protection list has exceeded 1M')
    paths = context.splitlines()
    for path in paths:
        if path == '':
            continue
        if not path.startswith('/'):
            return False, path, _('Path must begin with {0}').format('/')
        if len(path) > 500:
            return False, path, _('The length of the path has exceeded {0} characters').format(500)
        if path.find('//') >= 0:
            return False, path, _('The sub-path between / cannot be empty')
        if path.find('*') >= 0 and not path.endswith('/*'):
            return False, path, _('Wildcard character must end with {0}').format('/*')
        if path.find('?') >= 0 or path.find('#') >= 0:
            return False, path, _('Only supports path and path parameters')

        segments = re.split('/|;', path)
        segments.pop(0)
        if len(segments) > 100:
            return False, path, _('The depth of the path has exceeded {0}').format(100)

        args = []
        for s in segments:
            err_msg = _('Path templates only supports the following formats: /a/{id} or /a;{id}')
            if s.find('{') >= 0 or s.find('}') >= 0:
                if s.startswith('{'):
                    if not s.endswith('}'):
                        return False, path, err_msg
                    if s == "{}":
                        return False, path, _('Path templates cannot be empty')
                    if not re.match(r'^{[a-zA-Z0-9_-]{1,20}}$', s):
                        return False, path, _('Path templates must only contain letters, numbers, underscores (_), and dashes(-), and the length of the templates cannot exceed {0}'.format(20))

                    if s in args:
                        return False, path, _('Path templates cannot have duplicate names')
                    else:
                        args.append(s)
                else:
                    return False, path, err_msg

    return True, '', ''


def protection_list_sync_file(site_dir, server_key):
    sync_dir = '/etc/asp/release/web_admin/static/sync_file'
    exe('cd {} && tar cf - $(find . | sort) | gzip -n -c > {}/{}.tmp'.format(site_dir, sync_dir, server_key))
    exe('mv -f {0}/{1}.tmp {0}/protection_list_{1}.tgz'.format(sync_dir, server_key))
    return BaseConf().sync_file('{}/protection_list_{}.tgz'.format(sync_dir, server_key))

def valid_protection_list_file(server_key, file_name):
    if not server_key or not file_name:
        return

    if '..' in server_key or '..' in file_name:
        return

    base_path = '/etc/asp/release/nginx/protection_list'
    path = os.path.abspath(os.path.join(base_path, server_key, file_name))
    if not path.startswith(base_path) or not os.path.isfile(path):
        return

    return path


def get_protection_list_file(server_key, file_name):
    result = {'result': 'FAILED', 'message': _('The protection list to be edited does not exist. Please refresh the page and try again.')}
    path = valid_protection_list_file(server_key, file_name)
    if not path:
        return result

    file_name = os.path.basename(path)
    src = '/tmp/current.txt'
    if os.path.exists(src):
        os.remove("{}".format(src))
    exe('unzip {} -d /tmp'.format(path))
    with open(src, 'r') as f:
        content = f.read()

    conf = NginxConf()
    upstream_conf = conf.get_upstream_conf(server_key)
    key_path = ['request_protection_list', 'history_list', file_name]

    if file_name.endswith('_recommended.zip'):
        key_path = ['request_protection_list', 'recommended', file_name]

    history_file = get_value_of_nested_dict(upstream_conf, key_path, None)
    if not history_file:
        return result

    comments = history_file['comments']
    amount = history_file['amount']
    result = {'result': 'ok', 'name': file_name, 'comments': comments, 'content': content, 'amount': amount}

    return result


def protection_list_remove_file(dist, data):
    if not os.path.exists(dist) or not data:
        return

    file_list = os.listdir(dist)
    for f in file_list:
        if f == 'current.txt' or f.endswith('_recommended.zip'):
            continue

        if f not in data.get('history_list', {}).keys():
            logging.info("protection list remove file that not in excluded list: " + f)
            try:
                os.remove("{}/{}".format(dist, f))
            except Exception as e:
                logging.error('protection list file remove', exc_info=True)


def add_protection_list(data, context, server_key, release_path):
    site_dir = '{}/{}'.format(release_path, server_key)
    current_file = '{}/current.txt'.format(site_dir)

    key_set = {'comments', 'amount', 'size', 'content'}
    context_key_set = set(context.keys())
    if key_set.issubset(context_key_set) and context['content']:
        # add new file
        if not os.path.exists(site_dir):
            os.makedirs(site_dir)

        if get_folder_size(release_path) > 100 * 1024 * 1024:
            return {}, _('The total size of all protection lists has exceeded 100M')

        ret, path, msg = check_content(context['content'])
        if not ret:
            return {}, _('Configuration error ({0}): ').format(path) + msg

        history_list = data.get('history_list', {})
        history_list_keys = sorted(history_list.keys())
        while len(history_list_keys) > 9:
            history_list.pop(history_list_keys.pop(0))

        protection_list_remove_file(site_dir, data)
        with open(current_file, 'w') as f:
            f.write(context['content'])
            f.flush()
            logger.info('write to txt: {}'.format(current_file))

        name = datetime.now().strftime("%Y-%m-%dT%H-%M-%S.zip")
        zip_path = '{}/{}'.format(site_dir, name)
        exe('zip -j {} {}'.format(zip_path, current_file))
        logger.info('zip history file to {}'.format(zip_path))
        data['history_list'][name] = {'comments': context['comments'], 'amount': context['amount'], 'size': context['size']}
        data['current_index'] = name

        ret = protection_list_sync_file(site_dir, server_key)
        if ret != RESULT.OK:
            return data, _('Failed to sync the protection list')
        return data, None
    else:
        return {}, _('The protection list data is incomplete')

# 前端保存的时候，上传的 request_protection_list 只是变更（修改、新增、删除）的部分，不是全部配置
# 此函数会根据这些变更的部分，结合原有的配置，在内存里 merge 一个最终的配置，后续会和其他配置一起统一存入zk和配置文件
def save_protection_list(server_key, protection_list, old_upstream_conf):
    if not protection_list:
        return None, None

    if isinstance(protection_list, unicode):
        protection_list = protection_list.encode("utf-8")
    data = json.loads(protection_list)

    if not data:
        return None, None

    release_path = '/etc/asp/release/nginx/protection_list'
    site_dir = '{}/{}'.format(release_path, server_key)
    current_file = '{}/current.txt'.format(site_dir)

    request_protection_list = old_upstream_conf.get('request_protection_list', {})
    request_protection_list['enable'] = data.get('enable', False)
    history_list = request_protection_list.get('history_list', {})
    recommended = request_protection_list.get('recommended', {})
    recommended['update_by_flowlearn'] = False # 当 update_by_flowlearn 为 True 时，nginx不会reload
    recommended['remove_learning_history'] = False

    modify_dict = data.get('modify_dict', {})
    for fn, tasks in modify_dict.items():
        if tasks.get('delete', False):
            history_list.pop(fn, {})
            full_name = valid_protection_list_file(server_key, fn)
            if full_name:
                os.remove(full_name)
                logging.info("protection list remove file : " + full_name)

        if 'comments' in tasks and fn in history_list:
            history_list[fn]['comments'] = tasks.get('comments', '')

    context = data.get('context')
    if not isinstance(context, dict) or not context:
        return request_protection_list, None

    if 'history_name' in context and context['history_name']:       # apply history file
        name = context['history_name']
        history_file = valid_protection_list_file(server_key, name)
        if not history_file:
            return {}, _('Failed to save due to changed configurations. Please refresh the page and try again.')

        if name.endswith('_recommended.zip'):
            recommended['remove_learning_history'] = True
            if not context.get('content', None):
                context['content'] = get_protection_list_file(server_key, name).get('content', None)


            return add_protection_list(request_protection_list, context, server_key, release_path)

        if name not in history_list:
            return {}, _('Failed to save due to changed configurations. Please refresh the page and try again.')

        protection_list_remove_file(site_dir, request_protection_list)
        if os.path.exists(current_file):
            os.remove("{}".format(current_file))

        exe('unzip {} -d {}'.format(history_file, site_dir))
        request_protection_list['current_index'] = name

        return request_protection_list, None

    return add_protection_list(request_protection_list, context, server_key, release_path)



def get_value_of_nested_dict(nested_dict, key_path, default=None):
    current = nested_dict
    for key in key_path:
        if key in current:
            current = current[key]
            if not isinstance(current, dict):
                return default
        else:
            return default
    return current


@login_required
def read_protection_list_file(request):
    if request.method == 'GET':
        file_name = request.GET.get('file_name')
        server_key = request.GET.get('server_key')
        result = get_protection_list_file(server_key, file_name)
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
    else:
        return HttpResponse(status=403)


@login_required
def proxy_detail(request):
    nginxConf = NginxConf()
    existedServerNames = nginxConf.get_all_existed_server_names()
    error = None
    conf = ConfDb()
    license_info = conf.get_license_info(i18n_support=False, care_cluster=False)
    is_license_active = license_info.is_valid_license()
    is_license_be_protected_level = (license_info.get_license_protected_level() == 'BE')
    is_license_expired = not license_info.is_in_effect()
    is_license_evaluate = license_info.is_evaluative_license()
    has_mpp_license = license_info.is_mpp_enabled()
    max_proxies = license_info.max_proxies
    saveSuccess = None
    waf_default_disabled_rules = get_waf_default_disabled_rules()
    is_transparent_mode = conf.get_value('nginx/transparent_mode/enable')
    is_mirror_mode = conf.is_mirror()
    waf_default_rule_module_config = get_default_waf_module_config()
    source_port_keep_workable = is_transparent_mode and conf.get_value('_private/os/network/source_port_keep')

    logger.info('********************************************** ::: {0}'.format(is_license_be_protected_level))

    username = request.user.username

    encrypt_path_args_by_labels = ['a', 'form', 'script', 'img', 'audio', 'video', 'link', 'iframe', 'frame', 'input', 'meta', 'bgsound', 'source']
    encrypt_path_args_default_list = {'by_label': [], 'by_path': []}

    if request.method == "GET":
        action = request.GET.get('action')

        if action == 'newServer':
            if not has_permission(username, 'Add_Website', 'write'):
                return HttpResponse(status=403)
            if get_manifest_info().get('is_debug'):
                check_console_open_v2 = False
            else:
                check_console_open_v2 = True

            auto_disable = False
            enable_X_Real_IP = True
            enable_X_Forwarded_For = True
            global_src_ip_strategy_list = nginxConf.get_src_ip_strategy_list()
            for src_ip_strategy in global_src_ip_strategy_list:
                if src_ip_strategy.get("src_ip_from_type") in ('X-Forwarded-For', 'X-Real-IP'):
                    auto_disable = True
                    break

            if auto_disable or is_transparent_mode:
                enable_X_Real_IP = False
                enable_X_Forwarded_For = False

            upstream_model = NgxConfModel()
            if conf.is_plugin():
                upstream_model.listen_port = '8080'
            upstream_model.enable_x_real_ip = enable_X_Real_IP
            upstream_model.enable_x_forwarded_for = enable_X_Forwarded_For
            upstream_model.check_console_open_v2 = check_console_open_v2
            upstreamConf = upstream_model.dict
            upstreamConf.pop('Certification', None)
            upstreamConf.pop('CertificationKey', None)
            upstreamConf.pop('gm_sign_certification', None)
            upstreamConf.pop('gm_sign_certificationKey', None)
            upstreamConf.pop('gm_enc_certification', None)
            upstreamConf.pop('gm_enc_certificationKey', None)
            upstreamConf[nginxConf.UK_Limit_Except] = [True, True, True, True, True, True, False, False, True, True, True, True, True, True, True, True]
        elif action == 'show':
            serverKey = request.GET.get('serverKey')
            if not has_permission(username, 'Show_Website', 'read') or not has_permission_of_website(username, serverKey, 'read'):
                return HttpResponse(status=403)
            if not nginxConf.get_conf().get_all('nginx/upstreams/' + serverKey):
                return HttpResponseRedirect("/overview/")
            upstreamConf = nginxConf.get_upstream_conf(serverKey)
            saveSuccess = request.GET.get('saveSuccess')
            enable_tamper = upstreamConf.get('enable_tamper')

            upstreamConf['server_key'] = serverKey

            if upstreamConf.get('ajax_token_path_type') is None:
                upstreamConf['ajax_token_path_type'] = 0

            if enable_tamper:
                WafConf.get_tamper_urls_update_time(upstreamConf)
        else:
            # Invalid server name
            return HttpResponseRedirect("/overview/")

        nginxConf.show_upstream_conf(upstreamConf)

        # When Upgrade for protected_level
        if is_upgrade_for_protected_level(upstreamConf):
            set_upgrade_protected_list(nginxConf, upstreamConf)
        else:
            set_default_protected_list(nginxConf, upstreamConf)

        # PreVersion not have health check. set default
        set_default_health_check_conf(upstreamConf)

        if upstreamConf.get('P3P_CP') is None:
            upstreamConf[NginxConf.UK_P3P_CP] = 'NOI DSP PSAa OUR BUS IND ONL UNI COM NAV INT LOC'
            upstreamConf[NginxConf.UK_ENABLE_P3P_OPTIONS] = False

        # upgrade from 2.2.5 to 1707 version, nginxConf.UK_IpListSwitch is None
        if upstreamConf.get(nginxConf.UK_IpListSwitch) is None:
            if not upstreamConf.get(nginxConf.UK_IpWhiteList):
                upstreamConf[NginxConf.UK_IpListSwitch] = 'all_ip_white_list'
            else:
                upstreamConf[NginxConf.UK_IpListSwitch] = 'ip_white_list'

        # upgrade to 1707 version, need check and set variable all_ip_white_list
        if upstreamConf.get(nginxConf.UK_IpListSwitch) == 'ip_white_list' and not upstreamConf.get(
                nginxConf.UK_IpWhiteList):
            upstreamConf[NginxConf.UK_IpListSwitch] = 'all_ip_white_list'

        if upstreamConf.get(NginxConf.UK_LoadBalancingStrategy) is None:
            upstreamConf[NginxConf.UK_LoadBalancingStrategy] = 'ip_hash'

        if upstreamConf.get(NginxConf.UK_Response_Custom_Head_Mode) is None:
            upstreamConf[NginxConf.UK_Response_Custom_Head_Mode] = 'append'

        # Check is has limit_except
        limit_except = upstreamConf.get('limit_except')
        # When upgrade first to load the json
        limit_upgrade_default = [True, True, True, True, True, True, False, False, True, False, False, False, False, False, False, False]
        if not limit_except or len(limit_except) != len(limit_upgrade_default):
            if limit_except:
                for y in range(min(len(limit_except), len(limit_upgrade_default))):
                    limit_upgrade_default[y] = limit_except[y]
            upstreamConf[nginxConf.UK_Limit_Except] = limit_upgrade_default

        # Check is has mobile protect rules
        if upstreamConf.get(NginxConf.UK_Mobile_Protect_Rules) is None:
            upstreamConf[NginxConf.UK_Mobile_Protect_Rules] = ''

        mobile_protect_rules_str = upstreamConf[NginxConf.UK_Mobile_Protect_Rules]
        mobile_protect_rules_list = get_mobile_protect_rules_list(mobile_protect_rules_str)
        upstreamConf['show_sdk_v1_support'] = nginxConf.get_show_sdk_v1_support()
        upstreamConf['version_platform_inhibit'] = mobile_protect_rules_list

        if upstreamConf.get(NginxConf.UK_MOBILE_V1_PROCESS_MODE) is None:
            upstreamConf[NginxConf.UK_MOBILE_V1_PROCESS_MODE] = 'block'

        update_waf(nginxConf, upstreamConf, ignore_compare=True)
        upstreams = nginxConf.get_all_upstreams()

        upstreamConf['encrypt_path_args_by_labels'] = encrypt_path_args_by_labels
        upstreamConf['EncPathArgsList'] = upstreamConf.get('EncPathArgsList', encrypt_path_args_default_list)

        upstreamConf['mpaas_mpp_req_body_encrypt'] = upstreamConf.get('mpaas_mpp_req_body_encrypt', True)
        upstreamConf['mpaas_mpp_resp_body_encrypt'] = upstreamConf.get('mpaas_mpp_resp_body_encrypt', True)
        upstreamConf['bot_link_detect'] = upstreamConf.get('bot_link_detect', False)
        update_src_ip_strategy_list(upstreamConf)
        return base_render_to_response(request, 'v2/config_proxy.html',
                                       {
                                           'upstreamConf': upstreamConf,
                                           'action': action,
                                           'existedServerNames': existedServerNames,
                                           'advancedHidden': 'hidden',
                                           'nginx_config_status': True,
                                           'all_protection_features': all_protection_features(),
                                           'txsafe_on': upstreamConf.get('enabled', True),
                                           'error_msg': error,
                                           'maxProxies': max_proxies,
                                           'is_license_active': is_license_active,
                                           'is_license_be_protected_level': is_license_be_protected_level,
                                           'is_license_expired': is_license_expired,
                                           'is_license_evaluate': is_license_evaluate,
                                           'has_mpp_license': has_mpp_license,
                                           'wechat_occupied_sites': WechatConf().get_mpp_occupied_site(upstreams),
                                           'alipay_mpp_occupied_sites': AlipayConf().get_mpp_occupied_site(upstreams),
                                           'mpaas_mpp_occupied_sites': MPaasConf().get_mpp_occupied_site(upstreams),
                                           'saveSuccess': saveSuccess,
                                           'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                           'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                           'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                           'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                           'default_security_ssl_ciphers': DEFAULT_SECURITY_SSL_CIPHERS,
                                           'default_scrurity_ssl_protocols': DEFAULT_SECURITY_SSL_PROTOCOLS,
                                           'waf_default_disabled_rules': waf_default_disabled_rules,
                                           'waf_default_rule_module_config': waf_default_rule_module_config,
                                           'admin_port': nginxConf.get_conf().get_web_port(),
                                           'isProxyRole': conf.is_proxy(),
                                           'source_port_keep_workable': source_port_keep_workable,
                                           'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                       })
    elif request.method == 'POST':
        action = request.POST.get('action')

        if not get_current_is_debug():
            if not is_license_active:
                return HttpResponseRedirect("/proxy/")

        if not health_check_custom_request_validity(request):
            return HttpResponse(status=403)

        if action == 'import':
            if not has_permission(username, 'Import_Website', 'write'):
                return HttpResponse(status=403)
            tempConf = '/tmp/tempConf.json'
            if os.path.exists(tempConf):
                os.remove(tempConf)

            jsonFile = request.FILES.get('rawdata')
            with open(tempConf, 'wb+') as destination:
                for chunk in jsonFile.chunks():
                    destination.write(chunk)
                destination.flush()

            conf = UpstreamConfDb(tempConf)
            if not conf:
                # Empty or load json failed
                return render_proxys(request, NginxConf(), _('Invalid JSON file'))

            # a large try-catch block to ensure conf valid
            try:
                serverKey = conf.get_value("server_key")
                if not serverKey:
                    return render_proxys(request, NginxConf(), _('Field server_key not found in the JSON file'))

                # compatible with the old format : nginx/upstreams/{serverKey}
                upstreamConf = conf.get_value('upstream') or conf.get_all('nginx/upstreams/' + serverKey)
                if not upstreamConf:
                    return render_proxys(request, NginxConf(), _('Field upstream not found in the JSON file'))

                if is_transparent_mode or is_mirror_mode:
                    if upstreamConf.get('divide_ipv46_enabled', False):
                        return render_proxys(request, NginxConf(), _('The current deployment mode does not support the import of the selected site configuration'))

                upstreamConf['key'] = serverKey
                ary = serverKey.split('_')
                upstreamConf['name'] = ary[0]

                if upstreamConf.get(NginxConf.UK_ServerNameType) is None:
                    upstreamConf[NginxConf.UK_ServerNameType] = 'Domain'
                if upstreamConf.get(NginxConf.UK_ServerName) is None:
                    upstreamConf[NginxConf.UK_ServerName] = ary[0]
                    if valid_IPv6(ary[0]):
                        upstreamConf[NginxConf.UK_ServerNameType] = 'IPv6'
                    elif valid_IPv4(ary[0]):
                        upstreamConf[NginxConf.UK_ServerNameType] = 'IPv4'

                if upstreamConf.get(NginxConf.UK_Https) and upstreamConf.get(NginxConf.UK_UseInternationalCert, None) is None:
                    upstreamConf[NginxConf.UK_UseInternationalCert] = True

                upstreamConf[NginxConf.UK_CERTIF] = ''
                upstreamConf[NginxConf.UK_CERTIF_KEY] = ''

                upstreamConf[NginxConf.UK_GM_SIGN_CERTification] = ''
                upstreamConf[NginxConf.UK_GM_SIGN_CERTificationKey] = ''
                upstreamConf[NginxConf.UK_GM_ENC_CERTification] = ''
                upstreamConf[NginxConf.UK_GM_ENC_CERTificationKey] = ''

                limit_except = upstreamConf.get('limit_except')
                limit_upgrade_default = [True, True, True, True, True, True, False, False, True, False, False, False, False, False, False, False]
                if not limit_except or len(limit_except) != len(limit_upgrade_default):
                    if limit_except:
                        for y in range(min(len(limit_except), len(limit_upgrade_default))):
                            limit_upgrade_default[y] = limit_except[y]
                    upstreamConf[nginxConf.UK_Limit_Except] = limit_upgrade_default

                if upstreamConf.get(NginxConf.UK_SecurityLevelMin) is None:
                    upstreamConf[NginxConf.UK_SecurityLevelMin] = NginxConf.DEFAULT_SECURITY_FEATURES
                if upstreamConf.get(NginxConf.UK_SecurityLevelMax) is None:
                    upstreamConf[NginxConf.UK_SecurityLevelMax] = NginxConf.DEFAULT_SECURITY_FEATURES

                if upstreamConf.get(NginxConf.UK_LoadBalancingStrategy) is None:
                    upstreamConf[NginxConf.UK_LoadBalancingStrategy] = 'ip_hash'

                # handshake key
                upstreamConf['mobile_customer_id'] = '' if NginxConf().get_license_info().get_dict().get(
                    'mobile_handshakeKey') is None else NginxConf().get_license_info().get_dict().get(
                    'mobile_handshakeKey')

                if upstreamConf.get('request_protection_list') is None:
                    upstreamConf['request_protection_list'] = {
                        "history_list": {},
                        "current_index": '',
                        'enable': False
                    }

                if upstreamConf.get('WL_Proxy_Client_IP') is None:
                    # set default value while import old upstream config
                    upstreamConf[NginxConf.UK_WL_Proxy_Client_IP] = '$remote_addr'
                    upstreamConf[NginxConf.UK_Enable_WL_Proxy_Client_IP] = False

                if upstreamConf.get('P3P_CP') is None:
                    upstreamConf[NginxConf.UK_P3P_CP] = 'NOI DSP PSAa OUR BUS IND ONL UNI COM NAV INT LOC'
                    upstreamConf[NginxConf.UK_ENABLE_P3P_OPTIONS] = False

                # upgrade from 2.2.5 to 1707 version, nginxConf.UK_IpListSwitch is None
                if upstreamConf.get(nginxConf.UK_IpListSwitch) is None:
                    if not upstreamConf.get(nginxConf.UK_IpWhiteList):
                        upstreamConf[NginxConf.UK_IpListSwitch] = 'all_ip_white_list'
                    else:
                        upstreamConf[NginxConf.UK_IpListSwitch] = 'ip_white_list'

                # upgrade to 1707 version, need check and set variable all_ip_white_list
                if upstreamConf.get(nginxConf.UK_IpListSwitch) == 'ip_white_list' and not upstreamConf.get(
                        nginxConf.UK_IpWhiteList):
                    upstreamConf[NginxConf.UK_IpListSwitch] = 'all_ip_white_list'

                if upstreamConf.get(NginxConf.UK_IE_RENDERING_MODE) is None:
                    upstreamConf[NginxConf.UK_IE_RENDERING_MODE] = False
                if upstreamConf.get(NginxConf.UK_Request_Custom_Head_Key) is None:
                    upstreamConf[NginxConf.UK_Request_Custom_Head_Key] = ''
                if upstreamConf.get(NginxConf.UK_Request_Custom_Head_Value) is None:
                    upstreamConf[NginxConf.UK_Request_Custom_Head_Value] = ''
                if upstreamConf.get(NginxConf.UK_enable_Request_Custom_Head) is None:
                    upstreamConf[NginxConf.UK_enable_Request_Custom_Head] = False
                if upstreamConf.get(NginxConf.UK_Response_Custom_Head_Key) is None:
                    upstreamConf[NginxConf.UK_Response_Custom_Head_Key] = ''
                if upstreamConf.get(NginxConf.UK_Response_Custom_Head_Value) is None:
                    upstreamConf[NginxConf.UK_Response_Custom_Head_Value] = ''
                if upstreamConf.get(NginxConf.UK_Response_Custom_Head_Mode) is None:
                    upstreamConf[NginxConf.UK_Response_Custom_Head_Mode] = 'append'
                if upstreamConf.get(NginxConf.UK_enable_Response_Custom_Head) is None:
                    upstreamConf[NginxConf.UK_enable_Response_Custom_Head] = False
                if upstreamConf[NginxConf.UK_Host] is None:
                    upstreamConf[NginxConf.UK_Host] = '$http_host'
                if upstreamConf.get(NginxConf.UK_Enable_host) is None:
                    upstreamConf[NginxConf.UK_Enable_host] = False
                    upstreamConf[NginxConf.UK_Host] = '$http_host'

                if upstreamConf.get(NginxConf.UK_Disable_Upstream_Keepalive) is None:
                    upstreamConf[NginxConf.UK_Disable_Upstream_Keepalive] = True
                if upstreamConf.get(NginxConf.UK_Keep_Http_Version) is None:
                    upstreamConf[NginxConf.UK_Keep_Http_Version] = False

                # When Upgrade for protected_level
                if is_upgrade_for_protected_level(upstreamConf):
                    set_upgrade_protected_list(nginxConf, upstreamConf)
                else:
                    set_default_protected_list(nginxConf, upstreamConf)

                if not get_current_is_debug():
                    if not is_license_active:
                        upstreamConf['enabled'] = False

                    if is_license_expired and is_license_evaluate:
                        upstreamConf['enabled'] = False

                # PreVersion not have health check. set default
                set_default_health_check_conf(upstreamConf)

                # Upgrade should add default value or show mobile protect rules
                if upstreamConf.get(NginxConf.UK_Mobile_Protect_Rules) is None:
                    upstreamConf[NginxConf.UK_Mobile_Protect_Rules] = ''

                mobile_protect_rules_str = upstreamConf[NginxConf.UK_Mobile_Protect_Rules]
                mobile_protect_rules_list = get_mobile_protect_rules_list(mobile_protect_rules_str)
                upstreamConf['version_platform_inhibit'] = mobile_protect_rules_list

                if upstreamConf.get('ajax_token_path_type') is None:
                    upstreamConf['ajax_token_path_type'] = 0

                if upstreamConf.get(NginxConf.UK_MOBILE_V1_PROCESS_MODE) is None:
                    upstreamConf[NginxConf.UK_MOBILE_V1_PROCESS_MODE] = 'block'

                upstreamConf.pop('Enable_Attack_Sensation', None)
                upstreamConf.pop('Enable_Cookie_Collection', None)
                upstreamConf.pop('Enable_Post_Data_Collection', None)
                upstreamConf.pop('Extra_Business_Data', None)
                upstreamConf.pop('Extra_Session_in_Cookie', None)

                update_waf(nginxConf, upstreamConf)

                if 'scc' in upstreamConf:
                    # if import from other
                    # disable scc is MORE SECURE！
                    upstreamConf['scc']['enable'] = False

                # 从 21.05 以前版本导入站点配置，可能没有 enableHttp2 配置项，设置默认值
                if upstreamConf.get(NginxConf.UK_Enable_HTTP_2) is None:
                    upstreamConf[NginxConf.UK_Enable_HTTP_2] = False

                if 'check_console_open_v2' not in upstreamConf:
                    # 导入2209以前版本的站点配置，继承check_console_open的配置值
                    upstreamConf['check_console_open_v2'] = upstreamConf.get('check_console_open', True)

                upstreamConf['encrypt_path_args_by_labels'] = encrypt_path_args_by_labels
                upstreamConf['EncPathArgsList'] = upstreamConf.get('EncPathArgsList', encrypt_path_args_default_list)
                upstreamConf['bot_link_detect'] = upstreamConf.get('bot_link_detect', False)
                update_src_ip_strategy_list(upstreamConf)
                return base_render_to_response(request, 'v2/config_proxy.html',
                                               {
                                                   'upstreamConf': upstreamConf,
                                                   'action': 'newServer',
                                                   'is_import': True,
                                                   'existedServerNames': existedServerNames,
                                                   'advancedHidden': 'hidden',
                                                   'nginx_config_status': True,
                                                   'all_protection_features': all_protection_features(),
                                                   'txsafe_on': upstreamConf.get('enabled', True),
                                                   'error_msg': error,
                                                   'maxProxies': max_proxies,
                                                   'is_license_active': is_license_active,
                                                   'is_license_be_protected_level': is_license_be_protected_level,
                                                   'is_license_expired': is_license_expired,
                                                   'is_license_evaluate': is_license_evaluate,
                                                   'has_mpp_license': has_mpp_license,
                                                   'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                                   'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                                   'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                                   'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                                   'waf_default_disabled_rules': waf_default_disabled_rules,
                                                   'waf_default_rule_module_config': waf_default_rule_module_config,
                                                   'admin_port': nginxConf.get_conf().get_web_port(),
                                                   'isProxyRole': nginxConf.get_conf().is_proxy(),
                                                   'source_port_keep_workable': source_port_keep_workable,
                                                   'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                               })
            except Exception as e:
                logger.error('Import upstream error with : {}'.format(e))
                return render_proxys(request, NginxConf(), _('Failed to import websites'))

        else:
            if not has_permission(username, 'Edit_Website_Config', 'write'):
                return HttpResponse(status=403)
            # Add or save proxy
            # print 'is_new_server:' + str(is_new_server)
            model = NgxConfModel()

            model.enabled = request.POST.get('txsafe_on') == 'on'
            post_basic_render_response = {
                # 'upstreamConf': model.show_conf(), # to fill at last
                'action': action,
                'existedServerNames': existedServerNames,
                'advancedHidden': 'hidden',
                'nginx_config_status': True,
                'all_protection_features': all_protection_features(),
                'txsafe_on': model.enabled,
                # 'error_msg': error, # if error exists
                'maxProxies': max_proxies,
                'is_license_active': is_license_active,
                'is_license_be_protected_level': is_license_be_protected_level,
                'is_license_expired': is_license_expired,
                'is_license_evaluate': is_license_evaluate,
                'has_mpp_license': has_mpp_license,
                'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                'waf_default_disabled_rules': waf_default_disabled_rules,
                'waf_default_rule_module_config': waf_default_rule_module_config,
                'admin_port': nginxConf.get_conf().get_web_port(),
                'source_port_keep_workable': source_port_keep_workable,
                'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
            }

            types = {'Domain', 'IPv4', 'IPv6', 'Regex'}
            model.server_name_type = request.POST.get('server_name_type', '')
            site_name_error = None
            if model.server_name_type not in types:
                return HttpResponse(status=403)

            model.is_https = request.POST.get('is_https') == "true"
            site_name = request.POST.get('site_name', '')
            # DAP-28304, DAP-28667
            site_name_max_length = 64 if model.is_https else 110
            if not site_name or len(site_name) > site_name_max_length:
                return HttpResponse(status=403)

            if model.server_name_type == 'Regex':
                regex_check = regex_consistence_check(site_name, 'www.testweb.com')
                if regex_check:
                    site_name_error = regex_check

                # site_name check
                # First, delete all two consecutive backslashes.
                site_name_check_str = re.sub(r'\\\\', '', site_name)
                # then, remove the escaped double quotes.
                site_name_check_str = re.sub(r'\\"', '', site_name_check_str)
                if site_name_check_str.find('"') > 0 or site_name_check_str.find('/') > 0:
                    site_name_error = _('Regex(%s) is incorrect') % (site_name)

                model.server_name = site_name
                serverName = hashlib.md5(model.server_name).hexdigest()
            elif model.server_name_type == 'Domain':
                if valid_IP(site_name) or not re.match(
                        r'^(?=^.{3,128}$)[a-zA-Z0-9][\-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][\-a-zA-Z0-9]{0,62})+$',
                        site_name):
                    site_name_error = _("Please select matched type of domain name or input valid domian")
                serverName = site_name
                model.server_name = site_name
            else:
                serverName = compress_IP(site_name)
                model.server_name = serverName

            model.site_customize_name = request.POST.get('site_customize_name', '')
            model.enable_site_conf = request.POST.get('enable_site_conf', 'on') != 'off'

            if conf.is_plugin():
                listenPort = '8080'
            else:
                listenPort = request.POST.get('site_port')

            if not listenPort.isdigit():
                return HttpResponse(status=403)
            model.listen_port = listenPort

            serverKey = serverName + '_' + listenPort

            business_path_error = None
            model.business_path = ''
            model.enable_business_path = request.POST.get('enable_business_path') == 'on'
            if model.enable_business_path:
                model.business_path = request.POST.get('business_path', '')
                if model.business_path == '':
                    business_path_error = _("reg expression can not be empty")
                if len(model.business_path) > 1024:
                    business_path_error = _("The length of {0} can not exceed {1} characters.").format(_('Business Path'), 1024)
                try:
                    if not re.search(r'^[\x20-\x7e]*$', model.business_path) or re.search(r'[\s"]+', model.business_path):
                        business_path_error = _("Regular expressions prohibit the entry of double quotes, spaces, tabs, page feeds, and line breaks, as well as invisible characters other than 0x20-0x7e!")
                except Exception, e:
                    business_path_error = _('Regex(%s) is incorrect') % (model.business_path)
                regex_check = regex_consistence_check(model.business_path, '/path/test')
                if regex_check:
                    business_path_error = regex_check

                serverKey += '_' + hashlib.md5(model.business_path).hexdigest()

            all_upstream_names = nginxConf.get_all_upstream_site_names()
            is_new_server = serverKey not in all_upstream_names
            if not is_new_server and not has_permission_of_website(username, serverKey):
                site_name_error = _("Website name conflict with website of other operator, please contact administrator.")

            # 保存旧的配置
            old_upstream_conf = nginxConf.get_upstream_conf(serverKey)

            model.divide_ipv46_enabled = request.POST.get('divide_ipv46_enabled') == 'on'
            model.terminal_enabled = request.POST.get('terminal_enable') == 'on'
            model.terminal_port = request.POST.get('terminal_port')
            model.is_terminal_https = request.POST.get('is_terminal_https') == "true"
            model.is_upstream_https = request.POST.get('is_upstream_https') == "true"
            load_balancing_strategy = request.POST.get('load_balancing_strategy')
            if load_balancing_strategy not in ['ip_hash', 'round_robin', 'cookie_sticky', 'least_conn']:
                load_balancing_strategy = 'ip_hash'
            model.load_balancing_strategy = load_balancing_strategy

            if is_primary_protection():
                model.cookie_token_usable_times = request.POST.get('cookie_token_usable_times', '50')
            else:
                model.cookie_token_usable_times = request.POST.get('cookie_token_usable_times', '1')

            whitelist_reg = request.POST.getlist('whitelist_reg')
            whitelist_comment = request.POST.getlist('whitelist_comment')
            whitelist_get_only = request.POST.getlist('whitelist_get_only_v')
            whitelist_select = request.POST.getlist('whitelist_select')
            whitelist_reg_case_sensitive = [item == 'true' for item in
                                            request.POST.getlist('whitelist_reg_case_sensitive')]

            _full_whitelist = []  # [(regex,comment,get_only_flag),]
            if not whitelist_reg_case_sensitive:
                whitelist_reg_case_sensitive = [True for i in range(0, len(whitelist_reg))]
            for index in range(0, len(whitelist_reg)):
                if whitelist_select[index] != '5':  # not UA whitelist:
                    whitelist_reg[index] = nginxConf.replace_special_character(whitelist_reg[index])
                _full_whitelist.append((whitelist_reg[index], whitelist_comment[index], whitelist_get_only[index],
                                        whitelist_reg_case_sensitive[index], int(whitelist_select[index])))
            model.full_whitelist = _full_whitelist

            whitelist_out_reg = request.POST.getlist('whitelist_out_reg')
            whitelist_out_comment = request.POST.getlist('whitelist_out_comment')
            whitelist_out_select = request.POST.getlist('whitelist_out_select')
            whitelist_out_reg_case_sensitive = [item == 'true' for item in
                                                request.POST.getlist('whitelist_out_reg_case_sensitive')]

            _full_whitelist_out = []  # [(regex,comment),]
            if not whitelist_out_reg_case_sensitive:
                whitelist_out_reg_case_sensitive = [True for i in range(0, len(whitelist_out_reg))]
            for index in range(0, len(whitelist_out_reg)):
                whitelist_out_reg[index] = nginxConf.replace_special_character(whitelist_out_reg[index])
                _full_whitelist_out.append(
                    (whitelist_out_reg[index], whitelist_out_comment[index], whitelist_out_reg_case_sensitive[index],
                     int(whitelist_out_select[index])))
            model.full_whitelist_out = _full_whitelist_out

            web_advanced_protection = request.POST.get('web_advanced_protection')
            # advance ajax protection list
            ajax_response_encryption = request.POST.get('ajax_response_encryption')
            _ajax_referer_list = []  # [(regex,comment,case_sensitive),]

            # ajax response encryption list
            _ajax_response_encryption_list = []  # [(regex,comment,case_sensitive),]
            #如果配置的开关都打开，则读取post请求中的配置数据，否则读取旧的正确配置
            if 'on' == ajax_response_encryption and 'on' == web_advanced_protection:
                ajax_referer_list_reg = request.POST.getlist('ajax_referer_list_reg')
                ajax_referer_list_comment = request.POST.getlist('ajax_referer_list_comment')
                ajax_referer_list_reg_case_sensitive = [item == 'true' for item in
                                                        request.POST.getlist('ajax_referer_list_reg_case_sensitive')]
                for index in range(0, len(ajax_referer_list_reg)):
                    _ajax_referer_list.append(
                        (nginxConf.replace_special_character(ajax_referer_list_reg[index]),
                        ajax_referer_list_comment[index],
                        ajax_referer_list_reg_case_sensitive[index]))

                ajax_response_encryption_list_reg = request.POST.getlist('ajax_response_encryption_list_reg')
                ajax_response_encryption_list_comment = request.POST.getlist('ajax_response_encryption_list_comment')
                ajax_response_encryption_list_reg_case_sensitive = [item == 'true' for item in
                                                                    request.POST.getlist(
                                                                        'ajax_response_encryption_list_reg_case_sensitive')]
                for index in range(0, len(ajax_response_encryption_list_reg)):
                    _ajax_response_encryption_list.append(
                        (nginxConf.replace_special_character(ajax_response_encryption_list_reg[index]),
                        ajax_response_encryption_list_comment[index],
                        ajax_response_encryption_list_reg_case_sensitive[index]))
            else:
                if 'mock.NginxConf' not in str(type(old_upstream_conf)) and old_upstream_conf and old_upstream_conf.get('ajax_referer_list') != None:
                    _ajax_referer_list = old_upstream_conf.get('ajax_referer_list')
                if 'mock.NginxConf' not in str(type(old_upstream_conf)) and old_upstream_conf and old_upstream_conf.get('AjaxResponseEncryptionList') != None:
                    _ajax_response_encryption_list = old_upstream_conf.get('AjaxResponseEncryptionList')
            model.ajax_referer_list = _ajax_referer_list
            model.ajax_response_encryption_list = _ajax_response_encryption_list

            ajax_request_body_encryption = request.POST.get('ajax_request_body_encryption')
            _ajax_request_body_encryption_list = []  # [(regex,comment,case_sensitive),]
            if 'on' == ajax_request_body_encryption and 'on' == web_advanced_protection:
                ajax_request_body_encryption_list_reg = request.POST.getlist('ajax_request_body_encryption_list_reg')
                ajax_request_body_encryption_list_comment = request.POST.getlist('ajax_request_body_encryption_list_comment')
                ajax_request_body_encryption_list_reg_case_sensitive = [item == 'true' for item in
                                                                        request.POST.getlist(
                                                                            'ajax_request_body_encryption_list_reg_case_sensitive')]
                for index in range(0, len(ajax_request_body_encryption_list_reg)):
                    _ajax_request_body_encryption_list.append(
                        (nginxConf.replace_special_character(ajax_request_body_encryption_list_reg[index]),
                        ajax_request_body_encryption_list_comment[index],
                        ajax_request_body_encryption_list_reg_case_sensitive[index]))
            elif 'mock.NginxConf' not in str(type(old_upstream_conf)) and old_upstream_conf and old_upstream_conf.get('AjaxRequestBodyEncryptionList') != None:
                _ajax_request_body_encryption_list = old_upstream_conf.get('AjaxRequestBodyEncryptionList')
            model.ajax_request_body_encryption_list = _ajax_request_body_encryption_list

            ##### IP White List #####
            whitelist_ip_type_list = request.POST.getlist('whitelist_ip_type')
            whitelist_ip = request.POST.getlist('whitelist_ip')
            if whitelist_ip:
                whitelist_ip = [compress_IP(x) for x in whitelist_ip]

            whitelist_mask = request.POST.getlist('whitelist_mask')
            for i in range(0, len(whitelist_mask)):
                ip_type = whitelist_ip_type_list[i]
                if ip_type == 'segment':
                    whitelist_mask[i] = compress_IP(whitelist_mask[i])

            whitelist_ip_comment = request.POST.getlist('whitelist_ip_comment')

            ip_whitelist = []  # [(ip,mask),]
            for index in range(0, len(whitelist_ip)):
                ip_whitelist.append((whitelist_ip[index], whitelist_mask[index], whitelist_ip_comment[index],
                                     whitelist_ip_type_list[index]))
            model.ip_white_list = ip_whitelist
            ##### END IP white list #####

            ##### IP Black List #####
            black_ip_type_list = request.POST.getlist('blacklist_ip_type')
            blacklist_ip = request.POST.getlist('blacklist_ip')
            if blacklist_ip:
                blacklist_ip = [compress_IP(x) for x in blacklist_ip]

            blacklist_mask = request.POST.getlist('blacklist_mask')
            for i in range(0, len(blacklist_mask)):
                ip_type = black_ip_type_list[i]
                if ip_type == 'segment':
                    blacklist_mask[i] = compress_IP(blacklist_mask[i])

            blacklist_ip_comment = request.POST.getlist('blacklist_ip_comment')

            ip_blacklist = []  # [(ip,mask),]
            for index in range(0, len(blacklist_ip)):
                ip_blacklist.append((blacklist_ip[index], blacklist_mask[index], blacklist_ip_comment[index],
                                     black_ip_type_list[index]))
            model.ip_black_list = ip_blacklist

            model.ip_list_switch = request.POST.get('ip_white_black_list_switch')
            ##### END IP Black List #####

            mobile_whitelist_reg = request.POST.getlist('mobile_whitelist_reg')
            mobile_whitelist_comment = request.POST.getlist('mobile_whitelist_comment')

            mobile_whitelist = []  # [(regex,comment),]
            for index in range(0, len(mobile_whitelist_reg)):
                mobile_whitelist_reg[index] = nginxConf.replace_special_character(mobile_whitelist_reg[index])
                mobile_whitelist.append((mobile_whitelist_reg[index], mobile_whitelist_comment[index]))
            model.mobile_white_list = mobile_whitelist


            verification_list = []  # [(regex,comment,post_only_flag),]
            encapsulation_list_out = []  # [(regex,comment),]
            if 'on' == web_advanced_protection:
                verificationlist_reg = request.POST.getlist('verificationlist_reg')
                verificationlist_comment = request.POST.getlist('verificationlist_comment')
                verificationlist_post_only = request.POST.getlist('verificationlist_post_only_v')
                verificationlist_reg_case_sensitive = [item == 'true' for item in
                                                    request.POST.getlist('verificationlist_reg_case_sensitive')]
                if not verificationlist_reg_case_sensitive:
                    verificationlist_reg_case_sensitive = [True for i in range(0, len(verificationlist_reg))]
                for index in range(0, len(verificationlist_reg)):
                    verificationlist_reg[index] = nginxConf.replace_special_character(verificationlist_reg[index])
                    verification_list.append((verificationlist_reg[index], verificationlist_comment[index],
                                            verificationlist_post_only[index],
                                            verificationlist_reg_case_sensitive[index]))
                encapsulationlist_out_reg = request.POST.getlist('encapsulationlist_out_reg')
                encapsulationlist_out_comment = request.POST.getlist('encapsulationlist_out_comment')
                encapsulationlist_out_reg_case_sensitive = [item == 'true' for item in request.POST.getlist(
                    'encapsulationlist_out_reg_case_sensitive')]
                if not encapsulationlist_out_reg_case_sensitive:
                    encapsulationlist_out_reg_case_sensitive = [True for i in range(0, len(encapsulationlist_out_reg))]
                for index in range(0, len(encapsulationlist_out_reg)):
                    encapsulationlist_out_reg[index] = nginxConf.replace_special_character(encapsulationlist_out_reg[index])
                    encapsulation_list_out.append((encapsulationlist_out_reg[index], encapsulationlist_out_comment[index],
                                                encapsulationlist_out_reg_case_sensitive[index]))
            else:
                if 'mock.NginxConf' not in str(type(old_upstream_conf)) and old_upstream_conf and old_upstream_conf.get('VerificationList') != None:
                    verification_list = old_upstream_conf.get('VerificationList')
                if 'mock.NginxConf' not in str(type(old_upstream_conf)) and old_upstream_conf and old_upstream_conf.get('EncapsulationListOut') != None:
                    encapsulation_list_out = old_upstream_conf.get('EncapsulationListOut')
            model.verification_list = verification_list
            model.encapsulation_list_out = encapsulation_list_out

            mobile_whitelist = []  # [(regex,comment),]
            for index in range(0, len(mobile_whitelist_reg)):
                mobile_whitelist_reg[index] = nginxConf.replace_special_character(mobile_whitelist_reg[index])
                mobile_whitelist.append((mobile_whitelist_reg[index], mobile_whitelist_comment[index]))
            model.mobile_white_list = mobile_whitelist

            enc_path_args_by_labels = request.POST.getlist('enc_path_args_by_label')
            enc_path_args_list_by_path = request.POST.getlist('enc_path_args_by_path')
            enc_path_args_list_by_path_enabled = request.POST.getlist('enc_path_args_by_path_enabled')
            enc_path_args_list_by_args = request.POST.getlist('enc_path_args_by_args')
            enc_path_args_list_by_args_enabled = request.POST.getlist('enc_path_args_by_args_enabled')
            enc_path_args_list_comment = request.POST.getlist('enc_path_args_comment')
            enc_path_args_list = {
                "by_label": enc_path_args_by_labels,
                "by_path": []
            }
            for index in range(0, len(enc_path_args_list_by_path)):
                enc_path_args_list["by_path"].append((enc_path_args_list_by_path[index],
                                           bool(int(enc_path_args_list_by_path_enabled[index])),
                                           ','.join(filter(lambda x: x.strip().isdigit() and int(x), enc_path_args_list_by_args[index].split(','))),
                                           bool(int(enc_path_args_list_by_args_enabled[index])),
                                           enc_path_args_list_comment[index]))
            model.enc_path_args_list = enc_path_args_list

            model.entry_path = request.POST.get('entry_path', '')
            model.static_resource_list = request.POST.get('static_resource_list', '')
            invalid_action = request.POST.get('invalid_action', '')
            model.action = 'reject' if not invalid_action else invalid_action
            injections_whitelist_path = request.POST.getlist('waf_whitelist_path')
            injections_whitelist_path = map(lambda p: p.strip(), injections_whitelist_path)
            injections_whitelist_id = request.POST.getlist('waf_whitelist_id')
            injections_whitelist_comment = request.POST.getlist('waf_whitelist_comment')
            full_injections_whitelists = []
            for index in range(0, len(injections_whitelist_path)):
                full_injections_whitelists.append((
                    'url',  # waf whitelist type now not used
                    injections_whitelist_path[index],
                    injections_whitelist_id[index],
                    '',  # waf whitelist args now not used
                    injections_whitelist_comment[index],
                ))
            model.inject_white_list = full_injections_whitelists

            model.enable_tamper = request.POST.get('enable_tamper','off') == 'on'
            model.tamper_diskspace = request.POST.get('tamper_diskspace',"60")
            model.tamper_auto_update = request.POST.get('tamper_auto_update',False)
            model.tamper_web_similarity = request.POST.get('tamper_web_similarity',"70")
            model.tamper_expire_time = request.POST.get('tamper_expire_time',"60")
            tamper_urls = request.POST.getlist('tamper_urls')
            tamper_urls = map(lambda p: p.strip(), tamper_urls)
            tamper_url_regexs = request.POST.getlist('tamper_url_regexs')
            tamper_mimes = request.POST.getlist('tamper_mimes')
            tamper_cache_urls = []
            for index in range(0, len(tamper_urls)):
                fix_tamper_url_regexs = tamper_url_regexs[index] == 'true'
                fix_tamper_mimes = json.loads(tamper_mimes[index])
                tamper_cache_urls.append((
                    tamper_urls[index],
                    fix_tamper_url_regexs,
                    fix_tamper_mimes
                ))
            model.tamper_cache_urls = tamper_cache_urls

            injections_decode_path = request.POST.getlist('waf_decode_path')
            injections_decode_path = map(lambda p: p.strip(), injections_decode_path)
            injections_decode_arg = request.POST.getlist('waf_decode_arg')
            injections_decode_case_sensitive = request.POST.getlist('waf_decode_case_sensitive')
            injections_decode_type = request.POST.getlist('waf_decode_type')
            full_injections_decodelists = []
            for index in range(0, len(injections_decode_path)):
                decode_type = injections_decode_type[index].split(',')
                decode_case_sensitive = injections_decode_case_sensitive[index] == 'true'
                full_injections_decodelists.append((
                    injections_decode_path[index],
                    injections_decode_arg[index],
                    decode_case_sensitive,
                    decode_type
                ))
            model.waf_decode_list = full_injections_decodelists

            for key in model.waf_enabled_modules:
                value = request.POST.get(key, 'off')
                model.waf_enabled_modules[key] = value == 'on'

            model.x_frame_options = unescape(request.POST.get('X_Frame_Options', ''))
            model.x_frame_option_allow_uri = unescape(request.POST.get('x_frame_option_allow_uri', ''))
            model.enable_x_frame_option = request.POST.get('enable_x_frame_option') == 'on'

            model.x_content_type_options = request.POST.get('X_Content_Type_Options', 'nosniff')
            model.enable_x_content_type_options = request.POST.get('enable_X_Content_Type_Options') == 'on'

            model.x_xss_protection = unescape(request.POST.get('X_XSS_Protection', ''))
            model.x_xss_protection_report_uri = unescape(request.POST.get('X_XSS_Protection_report_uri', ''))
            model.enable_x_xss_protection = request.POST.get('enable_X_XSS_Protection') == 'on'

            model.p3p_cp = unescape(request.POST.get('P3P_CP', ''))
            model.enable_p3p_options = request.POST.get('enable_P3P_Options') == 'on'

            model.accept_encoding = request.POST.get('Accept_Encoding', '')
            model.enable_accept_encoding = request.POST.get('enable_Accept_Encoding') == 'on'

            model.wl_proxy_client_ip = request.POST.get('WL_Proxy_Client_IP', '')
            model.enable_wl_proxy_client_ip = request.POST.get('enable_WL_Proxy_Client_IP') == 'on'

            model.request_custom_head_key = request.POST.get('Request_Custom_Head_Key', '')
            model.request_custom_head_value = request.POST.get('Request_Custom_Head_Value', '')
            model.enable_request_custom_head = request.POST.get('enable_Request_Custom_Head') == 'on'

            model.response_custom_head_key = request.POST.get('Response_Custom_Head_Key', '')
            model.response_custom_head_value = request.POST.get('Response_Custom_Head_Value', '')
            model.response_custom_head_mode = request.POST.get('Response_Custom_Head_Mode', 'append')
            model.enable_response_custom_head = request.POST.get('enable_Response_Custom_Head') == 'on'

            model.reduce_level_of_protection_by_ua = request.POST.get('reduce_level_of_protection_by_ua', 'off') == 'on'
            model.enable_webrtc = request.POST.get('enable_webrtc', 'off') == 'on'
            model.disable_upstream_keepalive = request.POST.get('disable_upstream_keepalive', 'off') == 'on'
            model.keep_http_version = request.POST.get('keep_http_version', 'off') == 'on'
            model.prevent_corejs_reentry = request.POST.get('prevent_corejs_reentry', 'off') == 'on'
            model.internal_res_path = request.POST.get('internal_res_path', '/')
            internal_res_path_error = None
            if len(model.internal_res_path) > 128 or len(model.internal_res_path) == 0:
                internal_res_path_error =_("Illegal path")
            else:
                i = 0
                pathList = model.internal_res_path.split('/')
                for subPath in pathList:
                    if subPath != "" and not re.match('^[a-zA-Z0-9_.-]+$', subPath):
                        internal_res_path_error =_("Illegal path")
                        break
                    if subPath == "" and i != 0 and i != len(pathList) - 1:
                        internal_res_path_error =_("Illegal path")
                        break
                    i += 1
            model.enable_self_health_check = request.POST.get('enable_self_health_check') == 'on'
            model.self_health_check_path = request.POST.get('self_health_check_path', '/healthcheck')
            self_health_check_path_error = None
            if len(model.self_health_check_path) > 128 or len(model.self_health_check_path) == 0 or model.self_health_check_path == '/':
                self_health_check_path_error =_("The path length cannot be larger than 128 characters, the path cannot be blank or /")
            else:
                tmp = model.self_health_check_path
                if (tmp[0] == '/'):
                    tmp = tmp[1:]

                if (tmp[-1] == '/'):
                    tmp = tmp[0:-1]

                pathList = tmp.split('/')
                for subPath in pathList:
                    if subPath == "" or subPath == ".." or subPath == "." or not re.match('^[a-zA-Z0-9_.?=&-]+$', subPath):
                        self_health_check_path_error =_("Illegal path")
                        break
            model.proxy_connect_timeout = request.POST.get('proxy_connect_timeout', '')
            model.proxy_send_timeout = request.POST.get('proxy_send_timeout', '')
            model.proxy_read_timeout = request.POST.get('proxy_read_timeout', '')
            model.reserve_comment = request.POST.get('reserve_comment', 'off') == 'on'
            model.disable_encoding_ajax_args = request.POST.get('disable_encoding_ajax_args', 'off') == 'on'
            model.disable_123456 = request.POST.get('disable_123456', 'off') == 'on'

            model.host = request.POST.get('Host', '')
            model.enable_host = request.POST.get('enable_host') == 'on'

            model.x_real_ip = request.POST.get('X_Real_IP', '')
            model.enable_x_real_ip = request.POST.get('enable_X_Real_IP') == 'on'

            model.x_forwarded_for = request.POST.get('X_Forwarded_For', '')
            model.enable_x_forwarded_for = request.POST.get('enable_X_Forwarded_For') == 'on'

            model.no_content_type = request.POST.get('no_content_type') == 'on'
            model.content_type_overwrite = request.POST.get('content_type_overwrite') == 'on'
            content_type_overwrite_entry = []
            content_type_overwrite_value = request.POST.get('content_type_selector_hide')
            if content_type_overwrite_value:
                content_type_overwrite_value = json.loads(content_type_overwrite_value)
                for i in content_type_overwrite_value:
                    content_type_overwrite_entry.append((i.get('content_url'), i.get('content_type')))
            model.content_type_overwrite_entry = content_type_overwrite_entry

            model.src_ip_use_global_setting = request.POST.get('src_ip_use_global_setting') == 'on'
            src_ip_strategy_error = None
            src_ip_strategy_list = get_src_ip_strategy_list(request)

            try:
                src_ip_strategy_list = json.loads(src_ip_strategy_list)
                src_ip_strategy_error = valid_src_ip_strategy_list(src_ip_strategy_list)
                if not src_ip_strategy_error:
                    model.src_ip_strategy_list = src_ip_strategy_list
            except:
                src_ip_strategy_error = _('Invalid source IP acquisition strategy')

            model.ie_rendering_mode = request.POST.get('ie_rendering_mode') == "on"

            model.learning_mode = request.POST.get('learning_mode') == 'on'

            model.enable_charset = request.POST.get('enable_charset') == 'on'
            model.charset = request.POST.get('charset', '')

            model.ssl_ciphers = request.POST.get('sslCiphers', DEFAULT_SSL_CIPHERS)
            model.proxy_ssl_ciphers = request.POST.get('proxySSLCiphers', DEFAULT_PROXY_SSL_CIPHERS)
            model.ssl_protocols = request.POST.get('sslProtocol', DEFAULT_SSL_PROTOCOLS)
            model.proxy_ssl_protocols = request.POST.get('proxySSLProtocols', DEFAULT_PROXY_SSL_PROTOCOLS)

            model.enable_http2https = request.POST.get('enable_http2https') == 'on'
            model.http2https_org_port = request.POST.get('http2https_org_port')
            if model.is_https:
                model.http2https_new_port = request.POST.get('site_port')

            limit_excepts = []
            for i in range(1, 17):
                if conf.is_plugin():
                    is_checked = True
                else:
                    is_checked = (request.POST.get('limit_except' + str(i)) == 'on')
                limit_excepts.append(is_checked)
            model.limit_except = limit_excepts

            b64_injections = request.POST.getlist('sqlinjection_item_v')
            injections_comments = request.POST.getlist('sqlinjection_comment')
            injections_ids = request.POST.getlist('sqlinjection_id')
            injections_rule_flags = request.POST.getlist('sqlinjection_flag_v')
            injection_rule_type = request.POST.getlist('sqlinjection_detect_type')
            injections_header = request.POST.getlist('injection_header_item_v')

            decoded_injections = [base64.decodestring(sqlreg) for sqlreg in b64_injections]
            waf_strategy_error=None
            for _decoded_injection in decoded_injections:
                _injection_infos=json.loads(_decoded_injection)
                for _injection_info in _injection_infos:
                    if _injection_info.get('key',None)=='Request_Source_IP':
                        _ip_infos=_injection_info.get('value',None)
                        if _ip_infos:
                            for _tmp in _ip_infos.split(','):
                                if not valid_IP_or_CIDR(_tmp):
                                    waf_strategy_error=_("Invalid IP found in Custom rules.")
                        break
                if waf_strategy_error:
                    break

            full_injections = []
            for index in range(0, len(injections_ids)):
                full_injections.append((decoded_injections[index], injections_comments[index], injections_ids[index],
                                        injections_rule_flags[index], injection_rule_type[index]))
            model.inject_pattern_list = full_injections

            model.waf_strategy = request.POST.get('waf_strategy')
            wafConf = WafConf()
            if not waf_strategy_error:
                waf_strategy_error = wafConf.valid_waf_site_strategy_id_exist(model.waf_strategy)

            # WAF-CSRF
            model.waf_anti_csrf_enable = request.POST.get('waf_anti_csrf_enable',False)
            csrf_paths=request.POST.getlist("csrf_path",[])
            csrf_case_sensitive=request.POST.getlist("csrf_case_sensitive",[])
            csrf_method=request.POST.getlist("csrf_method",[])
            csrf_comment=request.POST.getlist("csrf_comment",[])
            waf_anti_csrf_list=[]
            waf_csrf_error = None
            if model.waf_anti_csrf_enable not in ['on',False]:
                waf_csrf_error = _('unknown error')
            if not len(csrf_paths) == len(csrf_case_sensitive) == len(csrf_method) == len(csrf_comment):
                waf_csrf_error = _('unknown error')
            if len(csrf_paths)>100:
                waf_csrf_error = _('{0} list has already reached limit {1}.').format(_('CSRF'), 100)
            if not waf_csrf_error:
                for index in range(0, len(csrf_paths)):
                    if not isinstance(csrf_paths[index],unicode) or len(csrf_paths[index])>1024:
                        waf_csrf_error = _('unknown error')
                        break
                    if not check_regular_valid(csrf_paths[index]):
                        waf_csrf_error = _('Invalid regular expression of CSRF.')
                        break
                    if csrf_case_sensitive[index] not in ('true', 'false'):
                        waf_csrf_error = _('unknown error')
                        break
                    if csrf_method[index] not in ('GET', 'POST', 'PUT', 'DELETE', 'GET POST DELETE PUT'):
                        waf_csrf_error = _('unknown error')
                        break
                    if len(csrf_comment[index])>1024:
                        waf_csrf_error = _('unknown error')
                        break
                    waf_anti_csrf_list.append((csrf_paths[index],csrf_case_sensitive[index],csrf_method[index],csrf_comment[index]))
            if not waf_csrf_error:
                model.waf_anti_csrf_list = waf_anti_csrf_list
                model.waf_anti_csrf_site_whitelist =request.POST.get('csrf_site_whitelist',"")

            if not waf_strategy_error:
                waf_strategy_error = WafStrategyCtrl().save_site_strategy_valid(model,request)


            def get_request_upstream_list(dataName):
                upstreams = []
                upstream_ips = []
                upstream_error = None
                if dataName:
                    try:
                        data = request.POST.get(dataName,'')
                        if isinstance(data, unicode):
                            data = data.encode("utf-8")
                        results = json.loads(data)
                        if len(results) > 0:
                            # 0 upstream_ip, 1 upstream_port, 2 manual_down
                            for index in range(len(results)):
                                key = str(index)
                                if valid_upstream_weight(results[key]) == False:
                                    upstream_error = _('Invalid upstream weight')
                                    break
                                results[key][2] = results[key][2] == 'True'
                                results[key][0] = compress_IP(results[key][0])
                                upstreams.append(results[key])
                                upstream_ips.append(results[key][0])
                    except:
                        return upstreams, upstream_ips, upstream_error
                return upstreams, upstream_ips, upstream_error

            upstreams = []
            upstream_ips = []
            upstream_error = None
            if model.divide_ipv46_enabled:
                model.upstream_list_IPv4, upstream_ips_IPv4, upstream_error = get_request_upstream_list('serverList_IPv4_data')
                model.upstream_list_IPv6, upstream_ips_IPv6, upstream_error = get_request_upstream_list('serverList_IPv6_data')
                upstream_ips = upstream_ips_IPv4 + upstream_ips_IPv6
                upstreams = model.upstream_list_IPv4 + model.upstream_list_IPv6
            else:
                model.upstream_list, upstream_ips, upstream_error = get_request_upstream_list('serverList_data')
                upstreams = model.upstream_list

            #为了兼容自动化用例，因此保留以前的逻辑
            if len(upstreams) == 0:
                model.divide_ipv46_enabled = False
                upstream_ips = [compress_IP(x) for x in request.POST.getlist('upstream_ip', '')]
                is_upstream_manual_downs = request.POST.getlist('manual_down')
                upstream_ports = request.POST.getlist('upstream_port', '')
                upstream_weights = request.POST.getlist('upstream_weight')
                upstream_weights_len = len(upstream_weights)
                for i in range(len(upstream_ips)):
                    ip = upstream_ips[i]
                    port = upstream_ports[i]
                    is_down = is_upstream_manual_downs[i] == 'True'
                    weight = upstream_weights[i] if upstream_weights_len > i else 1

                    upstream = [ip, port, is_down, weight]
                    if valid_upstream_weight(upstream) == False:
                        upstream_error = _('Invalid upstream weight')
                        break

                    upstreams.append(upstream)
                model.upstream_list = upstreams

            is_health_check_enable = request.POST.get('enable_health_check') == 'true'
            health_check_type = request.POST.get('health_check_type')
            health_check_interval = request.POST.get('health_check_interval')
            health_check_ua = request.POST.get('health_user_agent')
            health_check_reset_times = request.POST.get('health_check_reset_times')
            health_check_timeouts = request.POST.get('health_check_timeouts')
            health_check_path = request.POST.get('health_check_path')
            health_check_http_mode = request.POST.get('health_check_http_mode')
            health_check_http_custom_request_header = request.POST.get('health_check_http_custom_request_header')
            health_check_http_custom_request_body = request.POST.get('health_check_http_custom_request_body')
            health_check_collection = None
            origin_conf = nginxConf.get_upstream_conf(serverKey)
            if not is_new_server:
                # modify server keep origin conf
                health_check_collection = origin_conf.get('health_check')
            if health_check_collection is None:
                health_check_collection = {
                    'is_health_check_enable': False,
                    'health_check_type': 'tcp',
                    "health_check_reset_times": "3",
                    "health_check_path": "/",
                    "health_check_interval": "5",
                    "health_check_timeouts": "5",
                    "health_user_agent": "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)",
                    "health_check_http_mode":"path_ua",
                    "health_check_http_custom_request_header":"GET / HTTP/1.0\r\nUser-Agent:Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1; Trident/4.0)",
                    "health_check_http_custom_request_body": ""
                }

            if conf.is_plugin():
                is_health_check_enable = False
                health_check_collection['is_health_check_enable'] = False
            else:
                health_check_collection['is_health_check_enable'] = is_health_check_enable and not is_transparent_mode

            if is_health_check_enable:
                if health_check_type == 'http':
                    health_check_collection['health_check_type'] = 'http'
                else:
                    health_check_collection['health_check_type'] = 'tcp'
                health_check_collection['health_check_interval'] = health_check_interval
                health_check_collection['health_check_reset_times'] = health_check_reset_times
                health_check_collection['health_check_timeouts'] = health_check_timeouts
                if health_check_path:
                    health_check_collection['health_check_path'] = health_check_path
                if health_check_ua:
                    health_check_collection['health_user_agent'] = health_check_ua
                health_check_collection['health_check_http_mode'] = health_check_http_mode
                health_check_collection['health_check_http_custom_request_header'] = health_check_http_custom_request_header
                health_check_collection['health_check_http_custom_request_body'] = health_check_http_custom_request_body

            model.health_check = health_check_collection

            # Get BE configurations
            security_level_cfg = {
                'web_standard_protection': 'cookie_token',
                'web_advanced_protection': 'url',
                'injection_attack_interception': 'waf',
                'automated_tool_intercept': 'bot',
                'crack_behavior_interception': 'crack',
                'level_max_form': 'form',
                'level_max_cookie': 'cookie',
                'web_primary_protection': 'primary_token',
                'llm_protection': 'llm',
            }
            security_level_max = []
            for _level_key, _level_val in security_level_cfg.items():
                if request.POST.get(_level_key) == 'on':
                    security_level_max.append(_level_val)

            if request.POST.get('web_advanced_protection') == 'on':
                if get_current_is_debug():
                    is_ae_protection = True
                else:
                    license_protected_level = conf.get_value('cluster/license_protected_level')
                    if license_protected_level and license_protected_level == 'AE':
                        is_ae_protection = True
                    else:
                        is_ae_protection = False
            else:
                is_ae_protection = False

            security_level_max = list(set(security_level_max))
            level_ae = ['url', 'form', 'cookie']
            if is_ae_protection:
                security_level_min = security_level_max
            else:
                if is_primary_protection():
                    level_ae = ['url', 'cookie']
                security_level_min = list((set(security_level_max) | set(level_ae)) ^ set(level_ae))

            security_level_min.sort()
            security_level_max.sort()
            model.security_level_min = security_level_min
            model.security_level_max = security_level_max

            model.prevent_scanner = request.POST.get('prevent_scanner') == 'on'
            model.bot_link_detect = request.POST.get('bot_link_detect') == 'on'

            model.enable_compatible_mode = request.POST.get('enable_compatible_mode') == 'on'
            model.check_console_open_v2 = request.POST.get('check_console_open_v2') == 'on'
            model.enable_client_protection = request.POST.get('enable_client_protection') == 'on'

            model.enable_client_protection = request.POST.get('enable_client_protection') == 'on'

            protected_list = {}
            all_protection = all_protection_list()
            for i in range(0, len(all_protection)):
                protected_item = request.POST.get(all_protection[i][0]) == 'on'
                protected_list[all_protection[i][0]] = protected_item
            model.protected_list = protected_list

            model.waf_learning_mode = request.POST.get('waf_learning_mode') == 'on'

            ####### AI-WAF #######
            aiwaf_modules = model.aiwaf_modules
            aiwaf_modules['aiwaf_enable'] = request.POST.get('aiwaf_attack_interception') == 'on'
            aiwaf_modules['aiwaf_learning_mode'] = request.POST.get('aiwaf_learning_mode') == 'on'

            # RISK
            aiwaf_risk_cfg = ['xss_inject_model', 'sql_inject_model']
            temp = {}
            for j in range(0, len(aiwaf_risk_cfg)):
                risk_key = aiwaf_risk_cfg[j]
                temp[risk_key] = request.POST.get(risk_key) == 'on'
                aiwaf_modules['aiwaf_risk_model'] = temp

            # White List
            aiwaf_whitelist_reg = request.POST.getlist('aiwaf_whitelist_reg')
            aiwaf_whitelist_comment = request.POST.getlist('aiwaf_whitelist_comment')
            aiwaf_whitelist_reg_case_sensitive = [item == 'true' for item in
                                                  request.POST.getlist('aiwaf_whitelist_reg_case_sensitive')]
            aiwaf_whitelist = []
            for index in range(0, len(aiwaf_whitelist_reg)):
                aiwaf_whitelist_reg[index] = nginxConf.replace_special_character(aiwaf_whitelist_reg[index])
                aiwaf_whitelist.append(
                    (aiwaf_whitelist_reg[index], aiwaf_whitelist_comment[index],
                     aiwaf_whitelist_reg_case_sensitive[index]))
            aiwaf_modules['aiwaf_whitelist'] = aiwaf_whitelist

            ####### END AI-WAF #######

            model.reload_status_code = get_reload_status_code() if not request.POST.get(
                'reload_status_code') else request.POST.get('reload_status_code')

            mobile_max_app = nginxConf.get_license_info().get_dict().get("mobile_max_app")
            mobile_max_app_count = 0
            if (mobile_max_app):
                mobile_max_app_count = int(mobile_max_app)

            model.mobile_customer_id = '' if nginxConf.get_license_info().get_dict().get(
                'mobile_handshakeKey') is None else nginxConf.get_license_info().get_dict().get(
                'mobile_handshakeKey')

            # Attack Sensation
            # model.enable_attack_sensation, enable_bta_analysis = nginxConf.get_enable_bta()
            # model.enable_cookie_collection, model.enable_post_data_collection = nginxConf.get_cookie_post_enabled()
            # model.extra_session_in_cookie = nginxConf.get_extract_session()
            # if 'extraSession_0' in request.POST:   # Extract cookie feature, reuse attack extraSession key.
            #     model.extra_session_in_cookie = request.POST.get('extraSession_0', '')
            #
            # extra_business_name = request.POST.getlist('extraBusinessName')     # 0
            # extra_url = request.POST.getlist('extraUrl')
            # extra_url = [url.strip() for url in extra_url]                      # 1
            # extra_username = request.POST.getlist('extraUsername')              # 2
            # extra_phonenum = request.POST.getlist('extraPhonenum')              # 3
            # extra_email = request.POST.getlist('extraEmail')                    # 4
            # extra_success = request.POST.getlist('extraSuccess')                # 5
            # extra_body_type = request.POST.getlist('extraBodyType')             # 6
            # extra_block_enable = request.POST.getlist('extraBlockEnable')       # 7
            # extra_business_type = request.POST.getlist('extraBusinessType')     # 8
            # extra_define1 = request.POST.getlist('extraDefine1')                # 9
            # extra_define2 = request.POST.getlist('extraDefine2')                # 10
            # extra_define3 = request.POST.getlist('extraDefine3')                # 11
            # extra_define4 = request.POST.getlist('extraDefine4')                # 12
            # extra_define5 = request.POST.getlist('extraDefine5')                # 13
            # extra_enable = request.POST.getlist('extraEnable')                  # 14
            # extra_business_id = request.POST.getlist('extraBusinessId')         # 15
            # extra_method = request.POST.getlist('extraMethod')                  # 16
            # extra_cookie = request.POST.getlist('extraCookie')                  # 17
            # extra_post = request.POST.getlist('extraPost')                      # 18
            # extra_fields_len = request.POST.getlist('extraFieldsLen')           # 19
            # extra_extra_len = request.POST.getlist('extraExtraLen')             # 20
            #
            # extra_business_data = []
            # for index in range(len(extra_business_name)):
            #     extra_business_data.append((extra_business_name[index],
            #                                 extra_url[index],
            #                                 extra_username[index],
            #                                 extra_phonenum[index],
            #                                 extra_email[index],
            #                                 extra_success[index],
            #                                 extra_body_type[index],
            #                                 extra_block_enable[index],
            #                                 extra_business_type[index],
            #                                 extra_define1[index],
            #                                 extra_define2[index],
            #                                 extra_define3[index],
            #                                 extra_define4[index],
            #                                 extra_define5[index],
            #                                 extra_enable[index] == 'True',
            #                                 int(extra_business_id[index]),
            #                                 extra_method[index],
            #                                 extra_cookie[index] == 'True',
            #                                 extra_post[index] == 'True',
            #                                 int(extra_fields_len[index]),
            #                                 int(extra_extra_len[index])
            #                                 ))
            # model.extra_business_data = extra_business_data
            model.extra_business_data = nginxConf.get_conf().get_value(
                'nginx/upstreams/{}/Extra_Business_Data'.format(serverKey), [])

            rulesID_list = request.POST.getlist('rule_id')
            rulesAction_list = request.POST.getlist('rule_action')
            rulesCondition_list = request.POST.getlist('rule_condition')
            model.mobile_protect_rules = get_mobile_protect_rules_str(rulesID_list, rulesAction_list,
                                                                      rulesCondition_list)

            model.enable_mobile_protection = protected_list.get('mobile_sdk_protection')
            if is_primary_protection():
                model.mobile_only = model.enable_mobile_protection and request.POST.get('web_primary_protection') != 'on'
            else:
                model.mobile_only = model.enable_mobile_protection and request.POST.get('web_standard_protection') != 'on'

            model.mobile_v1_process_mode = request.POST.get('mobile_v1_process_mode', 'block')
            if not model.enabled:  # only enable at passthrough mode
                model.mobile_passthrough_collection = request.POST.get('mobile_passthrough_collection') == 'on'

            model.mobile_enable_online_perceptron = request.POST.get('mobile_enable_online_perceptron') == 'on'
            model.enable_mobile_list = request.POST.get('enable_mobile_list') == 'on'
            model.mobile_enable_ignore_jsonp = request.POST.get('mobile_enable_ignore_jsonp') == 'on'

            mobile_body_whitelist_reg = request.POST.getlist('mobile_body_whitelist_reg')
            mobile_body_whitelist_comment = request.POST.getlist('mobile_body_whitelist_comment')
            mobile_body_whitelist = []  # [(regex,comment),]
            for index in range(0, len(mobile_body_whitelist_reg)):
                mobile_body_whitelist_reg[index] = nginxConf.replace_special_character(mobile_body_whitelist_reg[index])
                mobile_body_whitelist.append((mobile_body_whitelist_reg[index], mobile_body_whitelist_comment[index]))
            model.mobile_body_white_list = mobile_body_whitelist

            mobile_body_verificationlist_reg = request.POST.getlist('mobile_body_verificationlist_reg')
            mobile_body_verificationlist_comment = request.POST.getlist('mobile_body_verificationlist_comment')
            mobile_body_verificationlist = []  # [(regex,comment),]
            for index in range(0, len(mobile_body_verificationlist_reg)):
                mobile_body_verificationlist_reg[index] = nginxConf.replace_special_character(
                    mobile_body_verificationlist_reg[index])
                mobile_body_verificationlist.append(
                    (mobile_body_verificationlist_reg[index], mobile_body_verificationlist_comment[index]))
            model.mobile_body_verification_list = mobile_body_verificationlist

            # mobile white list
            model.enable_mobile_block_whitelist = request.POST.get('enable_mobile_block_whitelist') == 'on'
            mobile_block_whitelist_reg = request.POST.getlist('mobile_block_whitelist_reg')
            mobile_block_whitelist_reg_len = 0
            mobile_block_whitelist_comment = request.POST.getlist('mobile_block_whitelist_comment')
            mobile_block_whitelist = []  # [(regex,comment),]
            for index in range(0, len(mobile_block_whitelist_reg)):
                mobile_block_whitelist_reg[index] = nginxConf.replace_special_character(
                    mobile_block_whitelist_reg[index])
                mobile_block_whitelist_reg_len += len(mobile_block_whitelist_reg[index]) + JOSN_ARRAY_FIX_LEN
                mobile_block_whitelist.append(
                    [mobile_block_whitelist_reg[index], mobile_block_whitelist_comment[index]])

            model.mobile_block_white_list = mobile_block_whitelist

            # mobile ui wigdit touch config list
            model.enable_interested_widget_list = request.POST.get('enable_interested_widget_list') == 'on'
            mobile_interested_widget_path_reg = request.POST.getlist('mobile_interested_widget_path_reg')
            mobile_interested_widget_path_reg_len = 0
            mobile_interested_widget_ids = request.POST.getlist('mobile_interested_widget_ids')
            mobile_interested_widget_comment = request.POST.getlist('mobile_interested_widget_comment')
            mobile_interested_widget_list = []  # [(regex,ids,comment),]
            for index in range(0, len(mobile_interested_widget_path_reg)):
                mobile_interested_widget_path_reg[index] = nginxConf.replace_special_character(
                    mobile_interested_widget_path_reg[index])
                mobile_interested_widget_path_reg_len += len(mobile_interested_widget_path_reg[index]) + JOSN_ARRAY_FIX_LEN
                mobile_interested_widget_list.append(
                    [mobile_interested_widget_path_reg[index], mobile_interested_widget_ids[index], mobile_interested_widget_comment[index]])

            model.mobile_interested_widget_list = mobile_interested_widget_list

            # mobile local html list
            model.enable_special_local_html_list = request.POST.get('enable_special_local_html_list') == 'on'
            mobile_special_local_html_list_reg = request.POST.getlist('mobile_special_local_html_list_reg')
            mobile_special_local_html_list_reg_len = 0
            mobile_special_local_html_list_comment = request.POST.getlist('mobile_special_local_html_list_comment')
            mobile_special_local_html_list = []  # [(regex,comment),]
            for index in range(0, len(mobile_special_local_html_list_reg)):
                mobile_special_local_html_list_reg[index] = nginxConf.replace_special_character(
                    mobile_special_local_html_list_reg[index])
                mobile_special_local_html_list_reg_len += len(mobile_special_local_html_list_reg[index]) + JOSN_ARRAY_FIX_LEN
                mobile_special_local_html_list.append(
                    [mobile_special_local_html_list_reg[index], mobile_special_local_html_list_comment[index]])

            model.mobile_special_local_html_list = mobile_special_local_html_list
            model.mobile_enable_url_confusion = request.POST.get('mobile_enable_url_confusion') == 'on'

            model.mobile_config_version = origin_conf.get(NginxConf.UK_MobileConfigVersion)
            if not is_new_server:
                enable_mobile_list_old = origin_conf.get(NginxConf.UK_Enable_Mobile_List, False)
                enable_mobile_block_whitelist_old = origin_conf.get(NginxConf.UK_Enable_Mobile_Block_WhiteList, False)
                mobile_block_white_list_old = [y[0] for y in origin_conf.get(NginxConf.UK_MobileBlockWhiteList, [])]
                enable_special_local_html_list_old = origin_conf.get(NginxConf.UK_Enable_Mobile_Special_Local_html_List, False)

                mobile_ui_widget_list_path_reg_olds = [y[0] for y in origin_conf.get(NginxConf.UK_MobileInterestedWidgetList, [])]
                mobile_ui_widget_list_ids_olds = [y[1] for y in origin_conf.get(NginxConf.UK_MobileInterestedWidgetList, [])]
                enable_ui_widget_touch_config_list_old = origin_conf.get(NginxConf.UK_Enable_Mobile_UI_Widget_touch_config_List, False)

                mobile_special_local_html_list_old = [y[0] for y in origin_conf.get(NginxConf.UK_MobileSpecialLocalHtmlList, [])]
                static_resource_list_old = origin_conf.get('static_resource_list', '')
                mobile_enable_url_confusion_old = origin_conf.get(NginxConf.UK_Mobile_Enable_Url_Confusion, False)
                internal_res_path_old = origin_conf.get('internal_res_path', '')

                # 0. lower version update, and some fields is none
                # 1. data obfuscation
                # 2. mobile whitelist
                # 3. special local html list
                # 4. static resource
                if enable_mobile_list_old != model.enable_mobile_list \
                    or enable_mobile_block_whitelist_old != model.enable_mobile_block_whitelist \
                    or (model.enable_mobile_block_whitelist and set(mobile_block_white_list_old) != set(mobile_block_whitelist_reg)) \
                    or enable_special_local_html_list_old != model.enable_special_local_html_list \
                    or (model.enable_special_local_html_list and set(mobile_special_local_html_list_old) != set(mobile_special_local_html_list_reg)) \
                    or enable_ui_widget_touch_config_list_old != model.enable_interested_widget_list \
                    or (model.enable_interested_widget_list and (set(mobile_ui_widget_list_path_reg_olds) != set(mobile_interested_widget_path_reg) or set(mobile_ui_widget_list_ids_olds) != set(mobile_interested_widget_ids))) \
                    or static_resource_list_old != model.static_resource_list \
                    or mobile_enable_url_confusion_old != model.mobile_enable_url_confusion \
                    or internal_res_path_old != model.internal_res_path:
                    model.mobile_config_version = int(time.time())

            else:
                model.mobile_config_version = int(time.time())

            model.mobile_config_version |= MOBILE_CONFIG_VERSION_V3_BIT

            model.mobile_risk_interception = int(request.POST.get('mobile_risk_interception', 0))

            # websocket paths process
            model.auto_detect_websocket = request.POST.get('autoDetectWebsocket') == 'on'
            websocket_paths = request.POST.getlist('websocket_path')
            websocket_paths = map(lambda p: p.strip(), websocket_paths)
            websocket_paths_comment = request.POST.getlist('websocket_comment')
            websocket_paths_case_sensitive = [item == 'true' for item in
                                              request.POST.getlist('websocket_path_case_sensitive')]
            if not websocket_paths_case_sensitive:
                websocket_paths_case_sensitive = [True for i in range(0, len(websocket_paths))]
            model.websocket_paths = zip(websocket_paths, websocket_paths_comment, websocket_paths_case_sensitive)

            # Simple Common Config
            scc_enable = request.POST.get('sccEnable', 'false') == 'true'
            scc_text = request.POST.get('sccText', '').replace("\r\n", "\n")
            model.scc = dict(enable=scc_enable, text=scc_text)

            # ajax token path process
            ajax_token_path = request.POST.getlist('ajax_token_path')
            ajax_token_path = map(lambda p: p.strip(), ajax_token_path)
            ajax_token_path_comment = request.POST.getlist('ajax_token_path_comment')
            ajax_token_path_case_sensitive = [item == 'true' for item in
                                                     request.POST.getlist('ajax_token_path_case_sensitive')]
            if not ajax_token_path_case_sensitive:
                ajax_token_path_case_sensitive = [True for i in range(0, len(ajax_token_path))]
            model.ajax_token_path_list = zip(ajax_token_path, ajax_token_path_comment, ajax_token_path_case_sensitive)

            ajax_token_bypass = request.POST.getlist('ajax_token_bypass')
            ajax_token_bypass = map(lambda p: p.strip(), ajax_token_bypass)
            ajax_token_bypass_comment = request.POST.getlist('ajax_token_bypass_comment')
            ajax_token_bypass_case_sensitive = [item == 'true' for item in
                                                    request.POST.getlist('ajax_token_bypass_case_sensitive')]
            if not ajax_token_bypass_case_sensitive:
                ajax_token_bypass_case_sensitive = [True for i in range(0, len(ajax_token_path))]
            model.ajax_token_bypass_list = zip(ajax_token_bypass, ajax_token_bypass_comment, ajax_token_bypass_case_sensitive)

            model.ajax_token_path_type = int(request.POST.get('ajax_token_path_type', '0'))

            # http2 config
            model.enableHttp2 = True if model.is_https and request.POST.get('enableHttp2') == 'on' else False

            # wechat mini program
            # un-conditional save below configures for quick re-configuration
            wechat_access_white_paths = request.POST.getlist('wechat_access_white_path')
            wechat_access_white_comments = request.POST.getlist('wechat_access_white_comment')
            model.wechat_access_white_list = zip(wechat_access_white_paths, wechat_access_white_comments)
            model.wechat_app_req_body_encrypt = request.POST.get('wechat_app_req_body_encrypt') == 'on'
            model.wechat_app_resp_body_encrypt = request.POST.get('wechat_app_resp_body_encrypt') == 'on'
            if request.POST.get('wechat_mini_protection') == 'on':
                # wechat protect and mobile protect need mutex
                model.mobile_only = False

            # alipay mini program
            # un-conditional save below configures for quick re-configuration
            alipay_mpp_access_white_paths = request.POST.getlist('alipay_mpp_access_white_path')
            alipay_mpp_access_white_comments = request.POST.getlist('alipay_mpp_access_white_comment')
            model.alipay_mpp_access_white_list = zip(alipay_mpp_access_white_paths, alipay_mpp_access_white_comments)
            model.alipay_mpp_req_body_encrypt = request.POST.get('alipay_mpp_req_body_encrypt') == 'on'
            model.alipay_mpp_resp_body_encrypt = request.POST.get('alipay_mpp_resp_body_encrypt') == 'on'
            if request.POST.get('alipay_mini_program_protection') == 'on':
                # alipay protect and mobile protect need mutex
                model.mobile_only = False

            # mpaas mini program
            # un-conditional save below configures for quick re-configuration
            mpaas_mpp_access_white_paths = request.POST.getlist('mpaas_mpp_access_white_path')
            mpaas_mpp_access_white_comments = request.POST.getlist('mpaas_mpp_access_white_comment')
            model.mpaas_mpp_access_white_list = zip(mpaas_mpp_access_white_paths, mpaas_mpp_access_white_comments)
            model.mpaas_mpp_req_body_encrypt = request.POST.get('mpaas_mpp_req_body_encrypt') == 'on'
            model.mpaas_mpp_resp_body_encrypt = request.POST.get('mpaas_mpp_resp_body_encrypt') == 'on'
            if request.POST.get('mpaas_mpp') == 'on':
                # mpaas protect and mobile protect need mutex
                model.mobile_only = False

            if conf.is_plugin():
                model.wechat_app_req_body_encrypt = False
                model.wechat_app_resp_body_encrypt = False
                model.alipay_mpp_req_body_encrypt = False
                model.alipay_mpp_resp_body_encrypt = False
                model.mpaas_mpp_req_body_encrypt = False
                model.mpaas_mpp_resp_body_encrypt = False

            error = None
            model.name = serverName

            maximum_size = nginxConf.get_response_token_maximum_size()
            if len(model.static_resource_list) + mobile_block_whitelist_reg_len + mobile_interested_widget_path_reg_len + mobile_special_local_html_list_reg_len > maximum_size:
                logger.error('The value length of mobile part config cannot exceed {4} characters! mobile whitelist reg length:{0}, mobile interested widget path reg length:{1}, mobile special local html list reg length: {2}, mobile static resource list length:{3}.'
                             .format(maximum_size, mobile_block_whitelist_reg_len, mobile_interested_widget_path_reg_len, mobile_special_local_html_list_reg_len, len(model.static_resource_list)))
                post_basic_render_response.update(
                    {'upstreamConf': model.show_conf(), 'error_msg': _('The value length of mobile part config cannot exceed {} characters!').format(maximum_size)})
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            request_protection_list = request.POST.get('request_protection_list')
            protection_list, protection_list_error = save_protection_list(serverKey, request_protection_list, old_upstream_conf)
            if protection_list:
                model.request_protection_list = protection_list
            if protection_list_error:
                post_basic_render_response.update(
                    {'upstreamConf': model.show_conf(), 'error_msg': protection_list_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            if internal_res_path_error:
                post_basic_render_response.update({'upstreamConf': model.show_conf(), 'error_msg': internal_res_path_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            if self_health_check_path_error:
                post_basic_render_response.update({'upstreamConf': model.show_conf(), 'error_msg': self_health_check_path_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)
            if site_name_error:
                post_basic_render_response.update({'upstreamConf': model.show_conf(), 'error_msg': site_name_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)
            if business_path_error:
                post_basic_render_response.update({'upstreamConf': model.show_conf(), 'error_msg': business_path_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            if waf_strategy_error:
                post_basic_render_response.update(
                    {'upstreamConf': model.show_conf(), 'error_msg': waf_strategy_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            if waf_csrf_error:
                post_basic_render_response.update(
                    {'upstreamConf': model.show_conf(), 'error_msg': waf_csrf_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            if upstream_error:
                post_basic_render_response.update(
                    {'upstreamConf': model.show_conf(), 'error_msg': upstream_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            if src_ip_strategy_error:
                post_basic_render_response.update(
                    {'upstreamConf': model.show_conf(), 'error_msg': src_ip_strategy_error, })
                return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            for row in enc_path_args_list_by_args:
                if row.strip() == '':
                    continue

                posList = row.split(',')
                for pos in posList:
                    if pos.strip().isdigit() and int(pos) > 0:
                        continue

                    msg = _('path and args encryption')
                    post_basic_render_response.update(
                        {'upstreamConf': model.show_conf(), 'error_msg': _('{} config Error').format(msg), })
                    return base_render_to_response(request, 'v2/config_proxy.html', post_basic_render_response)

            product_type = get_product_type()
            if is_new_server and max_proxies > 0 and len(all_upstream_names) >= max_proxies and product_type != 'Safeplus':
                error = _(
                    'Current website number has already reached the ceiling ({0}), You can not add any more website.').format(
                    max_proxies)
                return base_render_to_response(request, 'v2/config_proxy.html',
                                               {
                                                   'upstreamConf': model.show_conf(),
                                                   'action': action,
                                                   'existedServerNames': existedServerNames,
                                                   'advancedHidden': 'hidden',
                                                   'nginx_config_status': True,
                                                   'all_protection_features': all_protection_features(),
                                                   'txsafe_on': model.enabled,
                                                   'error_msg': error,
                                                   'maxProxies': max_proxies,
                                                   'is_license_active': is_license_active,
                                                   'is_license_be_protected_level': is_license_be_protected_level,
                                                   'is_license_expired': is_license_expired,
                                                   'is_license_evaluate': is_license_evaluate,
                                                   'has_mpp_license': has_mpp_license,
                                                   'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                                   'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                                   'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                                   'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                                   'waf_default_disabled_rules': waf_default_disabled_rules,
                                                   'waf_default_rule_module_config': waf_default_rule_module_config,
                                                   'admin_port': nginxConf.get_conf().get_web_port(),
                                                   'source_port_keep_workable': source_port_keep_workable,
                                                   'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                               })

            mobile_cur_app_count = len(conf.get_mobile_certification_pkgname())
            if protected_list.get('mobile_sdk_protection') and mobile_max_app and \
                    mobile_cur_app_count > mobile_max_app_count:
                error = _('The maximum number of registered apps allowed for the cluster is ({0}).'
                          'Please delete some app(s) from the list of Registered Apps and try again'
                          ).format(mobile_max_app_count)
                model.key = serverKey
                model.name = serverName
                return base_render_to_response(request, 'v2/config_proxy.html',
                                               {
                                                   'upstreamConf': model.show_conf(),
                                                   'action': action,
                                                   'existedServerNames': existedServerNames,
                                                   'advancedHidden': 'hidden',
                                                   'nginx_config_status': True,
                                                   'all_protection_features': all_protection_features(),
                                                   'txsafe_on': model.enabled,
                                                   'error_msg': error,
                                                   'maxProxies': max_proxies,
                                                   'is_license_active': is_license_active,
                                                   'is_license_be_protected_level': is_license_be_protected_level,
                                                   'is_license_expired': is_license_expired,
                                                   'is_license_evaluate': is_license_evaluate,
                                                   'has_mpp_license': has_mpp_license,
                                                   'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                                   'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                                   'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                                   'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                                   'waf_default_disabled_rules': waf_default_disabled_rules,
                                                   'waf_default_rule_module_config': waf_default_rule_module_config,
                                                   'admin_port': nginxConf.get_conf().get_web_port(),
                                                   'source_port_keep_workable': source_port_keep_workable,
                                                   'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                               })

            def ipv(ipstr):
                return 6 if ':' in ipstr else 4

            if is_transparent_mode or is_mirror_mode:
                supported_ip_version = {4, 6}
            else:
                confdb = ConfDb()
                ip1 = confdb.get_value('_private/os/network/internal/ip')
                ip2 = confdb.get_value('_private/os/network/internal/dual_ip')
                supported_ip_version = {ipv(ip1)}
                if ip2:
                    supported_ip_version.add(ipv(ip2))

            for upstream_item in upstreams:
                _upstream_ip = upstream_item[0]
                # only valid ip must check whether ip version match with interface ip adress,otherswise as a domain address.
                if valid_IP(_upstream_ip):
                    if ipv(_upstream_ip) not in supported_ip_version:
                        error = _("The system service port is not configured with {0}, so {0} upstream is not reachable. Please change the server IP to {1} to ensure the protection takes effect").format('IPv6' if ':' in _upstream_ip else 'IPv4', 'IPv4' if ':' in _upstream_ip else 'IPv6')
                        model.key = serverKey
                        model.name = serverName
                        return base_render_to_response(request, 'v2/config_proxy.html',
                                                       {
                                                           'upstreamConf': model.show_conf(),
                                                           'action': action,
                                                           'existedServerNames': existedServerNames,
                                                           'advancedHidden': 'hidden',
                                                           'nginx_config_status': True,
                                                           'all_protection_features': all_protection_features(),
                                                           'txsafe_on': model.enabled,
                                                           'error_msg': error,
                                                           'maxProxies': max_proxies,
                                                           'is_license_active': is_license_active,
                                                           'is_license_be_protected_level': is_license_be_protected_level,
                                                           'is_license_expired': is_license_expired,
                                                           'is_license_evaluate': is_license_evaluate,
                                                           'has_mpp_license': has_mpp_license,
                                                           'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                                           'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                                           'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                                           'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                                           'waf_default_disabled_rules': waf_default_disabled_rules,
                                                           'waf_default_rule_module_config': waf_default_rule_module_config,
                                                           'admin_port': nginxConf.get_conf().get_web_port(),
                                                           'source_port_keep_workable': source_port_keep_workable,
                                                           'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                                       })
            model.useInternationalCert  = request.POST.get('useInternationalCert') == 'on'
            cert_file_name = request.POST.get('cert_file_name')
            cert_key_file_name = request.POST.get('key_file_name')
            cert = request.POST.get('cert_plain_text')
            key = request.POST.get('key_plain_text')
            model.useBuiltInCert = request.POST.get('useBuiltInCert') == "true"

            model.useChinaSecurityCert = request.POST.get('useChinaSecurityCert') == 'on'
            gm_sign_cert_file_name = request.POST.get('gm_sign_cert_file_name')
            gm_sign_key_file_name = request.POST.get('gm_sign_key_file_name')
            gm_enc_cert_file_name = request.POST.get('gm_enc_cert_file_name')
            gm_enc_key_file_name = request.POST.get('gm_enc_key_file_name')
            gm_sign_cert_plain_text = request.POST.get('gm_sign_cert_plain_text')
            gm_sign_key_plain_text = request.POST.get('gm_sign_key_plain_text')
            gm_enc_cert_plain_text = request.POST.get('gm_enc_cert_plain_text')
            gm_enc_key_plain_text = request.POST.get('gm_enc_key_plain_text')
            model.useBuiltInChinaSecurityCert = request.POST.get('useBuiltInChinaSecurityCert') == "true"

            if model.is_https:
                error = None
                if not (model.useInternationalCert or model.useChinaSecurityCert):
                    error = _('Please select one of the HTTPS certificate type.')
                else:
                    if is_mirror_mode and key:
                        if ('--BEGIN RSA PRIVATE KEY--' not in key) or ('--END RSA PRIVATE KEY--' not in key):
                            error = _('Please upload valid RSA private key file.')

                    # verify certificate and private key if needed
                    if model.useInternationalCert:
                        if not model.useBuiltInCert:
                            model.cert_file_name = cert_file_name
                            model.cert_key_file_name = cert_key_file_name

                            if cert:
                                if cert.find('--BEGIN CERTIFICATE--') > 0:
                                    model.certification = cert
                                else:
                                    error = _('Please upload a valid international certificate and private key.')

                            if not error and key:
                                if key.find('PRIVATE KEY--') > 0:
                                    model.certification_key = key
                                else:
                                    error = _('Please upload a valid international certificate and private key.')
                    else:
                        model.useBuiltInCert = False

                    if not error and model.useChinaSecurityCert:
                        if not model.useBuiltInChinaSecurityCert:
                            model.gm_sign_cert_file_name = gm_sign_cert_file_name
                            model.gm_sign_key_file_name = gm_sign_key_file_name
                            model.gm_enc_cert_file_name = gm_enc_cert_file_name
                            model.gm_enc_key_file_name = gm_enc_key_file_name

                            if gm_sign_cert_plain_text:
                                if gm_sign_cert_plain_text.find('--BEGIN CERTIFICATE--') > 0:
                                    model.gm_sign_certification = gm_sign_cert_plain_text
                                else:
                                    error = _('Please upload a valid China security certificate and private key.')
                            if not error and gm_sign_key_plain_text:
                                if gm_sign_key_plain_text.find('PRIVATE KEY--') > 0:
                                    model.gm_sign_certification_key = gm_sign_key_plain_text
                                else:
                                    error = _('Please upload a valid China security certificate and private key.')
                            if not error and gm_enc_cert_plain_text:
                                if gm_enc_cert_plain_text.find('--BEGIN CERTIFICATE--') > 0:
                                    model.gm_enc_certification = gm_enc_cert_plain_text
                                else:
                                    error = _('Please upload a valid China security certificate and private key.')
                            if not error and gm_enc_key_plain_text:
                                if gm_enc_key_plain_text.find('PRIVATE KEY--') > 0:
                                    model.gm_enc_certification_key = gm_enc_key_plain_text
                                else:
                                    error = _('Please upload a valid China security certificate and private key.')
                    elif not error:
                        model.useBuiltInChinaSecurityCert = False

                if error:
                    model.key = serverKey
                    model.name = serverName
                    return base_render_to_response(request, 'v2/config_proxy.html',
                                                   {
                                                       'upstreamConf': model.show_conf(),
                                                       'action': action,
                                                       'existedServerNames': existedServerNames,
                                                       'advancedHidden': 'hidden',
                                                       'nginx_config_status': True,
                                                       'all_protection_features': all_protection_features(),
                                                       'txsafe_on': model.enabled,
                                                       'error_msg': error,
                                                       'maxProxies': max_proxies,
                                                       'is_license_active': is_license_active,
                                                       'is_license_be_protected_level': is_license_be_protected_level,
                                                       'is_license_expired': is_license_expired,
                                                       'is_license_evaluate': is_license_evaluate,
                                                       'has_mpp_license': has_mpp_license,
                                                       'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                                       'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                                       'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                                       'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                                       'waf_default_disabled_rules': waf_default_disabled_rules,
                                                       'waf_default_rule_module_config': waf_default_rule_module_config,
                                                       'admin_port': nginxConf.get_conf().get_web_port(),
                                                       'source_port_keep_workable': source_port_keep_workable,
                                                       'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                                   })

            error = check_core_web_url(model)

            if error is None:
                full_whitelist_mobile = model.mobile_white_list
                for reg, _comment in full_whitelist_mobile:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of Mobile Protection White List.')
                        break

            # Add error_extra in return data to specify which regex is wrong
            error_extra = ""

            if error is None:
                if is_primary_protection():
                    err_stat = request.POST.get('web_standard_protection') == 'on' or request.POST.get(
                        'web_advanced_protection') == 'on'
                else:
                    err_stat = request.POST.get('web_primary_protection') == 'on'
                if err_stat:
                    error = _('Dynamic protection configuration does not match layout.')

            if error is None:
                full_inject = model.inject_pattern_list
                # regs_str data sample: "[{key:检测位置1,value:检测表达式1},{key:检测位置2,value:检测表达式2}]"
                for regs_str, _comment, inject_id, inject_rule_flag, inject_rule_type in full_inject:
                    regs_list = json.loads(regs_str)
                    for item in regs_list:
                        place = item.get("key", "")
                        reg = item.get("value", "")
                        if not check_pcre_regex_valid(reg):
                            error = _('Invalid regular expression of user-defined rules. Id: {}. Place: {}').format(
                                inject_id, place)
                            error_extra = place
                            break

            if error is None:
                content_type_overwrite_entry_list = model.content_type_overwrite_entry
                for reg, _comment in content_type_overwrite_entry_list:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of Content-Type Correction List.')

            if error is None:
                mobile_body_verification_list = model.mobile_body_verification_list
                for reg, _comment in mobile_body_verification_list:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of Request Path.')
                        break

            if error is None:
                mobile_body_white_list = model.mobile_body_white_list
                for reg, _comment in mobile_body_white_list:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of Response Path.')
                        break

            if error is None:
                mobile_block_white_list = model.mobile_block_white_list
                for reg, _comment in mobile_block_white_list:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of White List.')
                        break

            if error is None:
                for wl_type, wl_path, wl_id, wl_args, wl_comment in model.inject_white_list:
                    if wl_path != "":
                        if not check_regular_valid(wl_path):
                            error = _('Invalid waf whitelist path.')
                            break
                    if wl_id != "":
                        if not re.match(r'^[1-9][0-9]{0,9}(,[1-9][0-9]{0,9})*$', wl_id):
                            error = _('Invalid waf whitelist id.')
                            break

            if error is None:
                error = check_tamper_config(model,WafConf.MAX_LIST_LENGTH)

            if error is None:
                if model.enable_tamper:
                    total_used, now_use = wafConf.get_total_diskspace(serverKey)
                    if (total_used + int(model.tamper_diskspace))>WafConf.DEFAULT_GLOBLE_CACHE_SPACE:
                        error=_("[CSRF] The total cache disk space exceeds the limit of 1G. Please reduce the current or other sites settings for cache disk space.")

            if error is None:
                for dl_path, dl_arg, dl_case, dl_type in model.waf_decode_list:
                    if not check_regular_valid(dl_path):
                        error = _('Invalid regular expression of WAF decode url.')
                        break
                    if not check_regular_valid(dl_arg):
                        error = _('Invalid regular expression of WAF decode url parameter.')
                        break

            if error is None:
                websocket_path_list = model.websocket_paths
                for reg, comment, case_sensitive in websocket_path_list:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of websocket path.')
                        break

            if error is None:
                aiwaf_whitelist = model.aiwaf_modules['aiwaf_whitelist']
                for reg, _comment, case_sensitive in aiwaf_whitelist:
                    if not check_regular_valid(reg):
                        error = _('Invalid regular expression of AI WAF White List.')
                        break

            # if '' in extra_url:
            #     error = _('Empty URL.')
            #
            # if len(extra_url) != len(set(extra_url)):
            #     error = _('Duplicate URL.')

            if error is None:
                if serverKey == '{}_{}'.format(nginxConf.get_conf().get_admin_ip(),
                                               nginxConf.get_conf().get_web_port()):
                    error = _('Conflict with WebConsole')
                elif serverKey == '{}_20146'.format(nginxConf.get_conf().get_admin_ip()):
                    error = _('Conflict with WebConsole')

            if error is None:
                http_ports, https_ports = nginxConf.get_nginx_listen_ports_on_eth_external(exclude_site_name=serverKey)
                if model.is_https:
                    if model.listen_port in http_ports:
                        error = True
                    elif model.enable_http2https:
                        if model.http2https_org_port in https_ports:
                            error = True
                        if model.http2https_org_port == model.listen_port:
                            error = True
                else:
                    if model.listen_port in https_ports:
                        error = True

                if error:
                    error = _(
                        "Sites that use different protocol (http and https) are not allowed to listen on the same port.")

            if filter(lambda j: is_Local_Loopback(j), upstream_ips):
                error = _('Upstream is not allowed to be set to loopback address.')

            if error is None:
                if is_port_already_in_use(listenPort, conf):
                    error = _('Port {0} is used already. Please change the port number or try again later.').format(listenPort)
                elif model.is_https and is_port_already_in_use(model.http2https_org_port, conf):
                    error = _('Port {0} is used already. Please change the port number or try again later.').format(model.http2https_org_port)

            if error:
                model.key = serverKey
                model.name = serverName

                return base_render_to_response(request, 'v2/config_proxy.html',
                                               {
                                                   'upstreamConf': model.show_conf(),
                                                   'action': action,
                                                   'existedServerNames': existedServerNames,
                                                   'advancedHidden': 'hidden',
                                                   'nginx_config_status': True,
                                                   'all_protection_features': all_protection_features(),
                                                   'txsafe_on': model.enabled,
                                                   'error_msg': error,
                                                   'error_extra': error_extra,
                                                   'maxProxies': max_proxies,
                                                   'is_license_active': is_license_active,
                                                   'is_license_be_protected_level': is_license_be_protected_level,
                                                   'is_license_expired': is_license_expired,
                                                   'is_license_evaluate': is_license_evaluate,
                                                   'has_mpp_license': has_mpp_license,
                                                   'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                                   'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                                   'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                                   'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                                   'waf_default_disabled_rules': waf_default_disabled_rules,
                                                   'waf_default_rule_module_config': waf_default_rule_module_config,
                                                   'admin_port': nginxConf.get_conf().get_web_port(),
                                                   'source_port_keep_workable': source_port_keep_workable,
                                                   'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                               })
            site_extra_values = {}
            # update waf site strategy used list
            waf_site_strategy_values = wafConf.update_waf_site_strategy(model.waf_strategy, serverKey)
            waf_strategy_values = WafStrategyCtrl().update_strategy_used_list(serverKey, action='save', model=model)
            if waf_site_strategy_values:
                site_extra_values.update(**waf_site_strategy_values)
            if waf_strategy_values:
                site_extra_values.update(**waf_strategy_values)

            # assign the site to user if user is Operator when this Operator add new site
            if WebconsoleConf().get_user_role(username) == 'Operator' and is_new_server:
                site_extra_values.update(**assign_site_4_user(serverKey, username, no_apply=True, no_check=True))

            code, output, cmd_line = nginxConf.save_upstream(serverKey, model.dict, site_extra_values)

            if model.server_name_type == 'IPv6':
                site_key = '[' + model.server_name + ']:' + model.listen_port
            else:
                site_key = model.server_name + ':' + model.listen_port
            site_info = (model.site_customize_name + '@' + site_key) if (model.site_customize_name != '') else site_key
            if is_new_server:
                operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Add'), code, {
                    'msg': ugettext_noop('Website added'), 'spliceMsg': ': ' + site_info})
            else:
                op_log_inf = diff_upstream(model.dict, old_upstream_conf)
                operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Modify'), code, {
                    'msg': ugettext_noop('Website modified'), 'spliceMsg': ': ' + site_info, 'info': op_log_inf})

            if code == 0:
                logger.info('* Saved successfully: cmd: {0}, output: {1}.'.format(cmd_line, output, ))
                saveSuccess = 'success'
                # Add successfully, redirect to upstreams list.
                return HttpResponseRedirect(
                    "/proxy/detail/?action=show&serverKey={0}&saveSuccess={1}".format(serverKey, saveSuccess)
                )

            # failed to add, back to edit
            logger.error(
                '* Failed to save upstream configuration: {0}, cmd: {1}, output: {2}.'.format(code, cmd_line, output, ))

            error = _('Save failed.')
            # Back to edit.
            return base_render_to_response(request, 'v2/config_proxy.html',
                                           {
                                               'upstreamConf': model.dict,
                                               'action': 'edit',
                                               'existedServerNames': existedServerNames,
                                               'advancedHidden': 'hidden',
                                               'nginx_config_status': True,
                                               'txsafe_on': model.enabled,
                                               'error_msg': error,
                                               'is_license_active': is_license_active,
                                               'is_license_be_protected_level': is_license_be_protected_level,
                                               'is_license_expired': is_license_expired,
                                               'is_license_evaluate': is_license_evaluate,
                                               'has_mpp_license': has_mpp_license,
                                               'default_ssl_ciphers': DEFAULT_SSL_CIPHERS,
                                               'default_proxy_ssl_ciphers': DEFAULT_PROXY_SSL_CIPHERS,
                                               'default_ssl_protos': DEFAULT_SSL_PROTOCOLS,
                                               'default_proxy_ssl_protocols': DEFAULT_PROXY_SSL_PROTOCOLS,
                                               'waf_default_disabled_rules': waf_default_disabled_rules,
                                               'waf_default_rule_module_config': waf_default_rule_module_config,
                                               'admin_port': nginxConf.get_conf().get_web_port(),
                                               'source_port_keep_workable': source_port_keep_workable,
                                               'global_src_ip_strategy_list': nginxConf.get_src_ip_strategy_list()
                                           })
    else:
        # Invalid request
        return HttpResponseRedirect("/overview/")

def check_core_web_url(model):
    checkers = [
        UrlRuleWithSelectTypeChecker(_('Request White List'), model.full_whitelist),
        UrlRuleWithSelectTypeChecker(_('Response White List'), model.full_whitelist_out),
        URLRuleChecker(_('Request Verification List'), model.verification_list),
        URLRuleChecker(_('Response Encapsulation List'), model.encapsulation_list_out),
        URLRuleChecker(_('Ajax/Fetch Request Body Encryption List'), model.ajax_request_body_encryption_list),
        URLRuleChecker(_('Ajax/Fetch Referer List'), model.ajax_referer_list),
        URLRuleChecker(_('Ajax/Fetch Response Encryption List'), model.ajax_response_encryption_list),
        URLRuleChecker(_('Ajax/Fetch Req URL_Token_Free'), model.ajax_token_path_list if model.ajax_token_path_type else model.ajax_token_bypass_list, 256),
        URLRuleChecker(_('WeChat mini-program request whitelist'), model.wechat_access_white_list),
        URLRuleChecker(_('Alipay mini program request whitelist'), model.alipay_mpp_access_white_list),
        URLRuleChecker(_('Mobile mini program request whitelist'), model.mpaas_mpp_access_white_list)
    ]

    for checker in checkers:
        error = checker.check()
        if error:
            return error

def respo(request, serverKey, name, resp_data):
    op_code = '0' if resp_data.get('saveSuccess') else '1'

    data = {
        'encapsulation list': ugettext_noop(
                'Import the encapsulation list configuration for the site {upstream_key} from the security log.'
            ),
        'request whitelist': ugettext_noop(
                'Import the request whitelist configuration for the site {upstream_key} from the security log.'
            ),
        'wechat request whitelist': ugettext_noop(
                'Import the wechat request whitelist configuration for the site {upstream_key} from the security log.'
            ),
        'alipay mpp request whitelist': ugettext_noop(
                'Import the alipay mpp request whitelist configuration for the site {upstream_key} from the security log.'
            ),
        'mpaas mpp request whitelist': ugettext_noop(
                'Import the mobile mpp request whitelist configuration for the site {upstream_key} from the security log.'
            )
    }

    if get_product_type() == 'Safeplus':
        log_from = ugettext_noop('Statistics_Report')
    else:
        log_from = ugettext_noop('Log')

    operation_log(request, log_from, ugettext_noop('Import'), op_code, {
        'msg': data.get(name, ''),
        'extra': {
            'upstream_key': serverKey,
        }
    })
    return JsonResponse(resp_data)

@login_required
@check_permission('Security_Log', 'write')
def proxy_add_encapsulation(request):
    nginxConf = NginxConf()
    serverKeys = nginxConf.get_all_upstream_site_names()
    errorMsg = None
    if request.method == 'POST':
        serverKey = request.POST.get('serverKey')

        conf = nginxConf.get_upstream_by_server_key(serverKey)
        site_name = conf['ServerName']
        if conf['ServerNameType'] == 'IPv6':
            site_name = '[' + site_name + ']'
        site_name += ':' + conf['ListenPort']
        if conf['site_customize_name']:
            site_name = conf['site_customize_name'] + '@' + site_name

        if not has_permission_of_website(request.user.username,
                                         ''.join(reversed(''.join(reversed(serverKey))))):
            return respo(request, site_name, 'encapsulation list', {'saveSuccess': False, 'errorCode': 'errorHost'})
        encape = request.POST.get('encape')
        encapeComment = request.POST.get('encapeComment')

        if not check_regular_valid(encape):
            logger.info('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ERROR REG')
            return respo(request, site_name, 'encapsulation list', {'saveSuccess': False, 'errorCode': 'errorReg'})

        isHost = False
        for key in serverKeys:
            if serverKey == key:
                isHost = True
                break

        if not isHost:
            return respo(request, site_name, 'encapsulation list', {'saveSuccess': False, 'errorCode': 'errorHost'})

        # Deduplication
        encapsulationListOut = conf['EncapsulationListOut']
        for i in range(0, len(encapsulationListOut)):
            encapsulationOut = encapsulationListOut[i]
            if encapsulationOut[0] == encape:
                return respo(request, site_name, 'encapsulation list', {'saveSuccess': False, 'errorCode': 'dupReg'})

        encapsulationListOut.append([encape, encapeComment])

        nginxConf.save_upstream(serverKey, conf)

        return respo(request, site_name, 'encapsulation list', {'saveSuccess': True})


@login_required
@check_permission('Security_Log', 'write')
def proxy_add_request_whitelist(request):
    nginxConf = NginxConf()
    serverKeys = nginxConf.get_all_upstream_site_names()
    errorMsg = None
    if request.method == 'POST':
        serverKey = request.POST.get('serverKey')

        conf = nginxConf.get_upstream_by_server_key(serverKey)
        site_name = conf['ServerName']
        if conf['ServerNameType'] == 'IPv6':
            site_name = '[' + site_name + ']'
        site_name += ':' + conf['ListenPort']
        if conf['site_customize_name']:
            site_name = conf['site_customize_name'] + '@' + site_name

        if not has_permission_of_website(request.user.username,
                                         ''.join(reversed(''.join(reversed(serverKey))))):
            return respo(request, site_name, 'request whitelist', {'saveSuccess': False, 'errorCode': 'errorHost'})
        reg = request.POST.get('reg')
        comment = request.POST.get('comment')

        if not check_regular_valid(reg):
            logger.info('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ERROR REG')
            return respo(request, site_name, 'request whitelist', {'saveSuccess': False, 'errorCode': 'errorReg'})

        isHost = False
        for key in serverKeys:
            if serverKey == key:
                isHost = True
                break

        if not isHost:
            return respo(request, site_name, 'request whitelist', {'saveSuccess': False, 'errorCode': 'errorHost'})

        # Deduplication
        requestWhitelistOut = conf['FullWhiteList']
        for i in range(0, len(requestWhitelistOut)):
            requestWhiteObj = requestWhitelistOut[i]
            if requestWhiteObj[0] == reg:
                return respo(request, site_name, 'request whitelist', {'saveSuccess': False, 'errorCode': 'dupReg'})

        requestWhitelistOut.append([reg, comment, 'all'])

        nginxConf.save_upstream(serverKey, conf)

        return respo(request, site_name, 'request whitelist', {'saveSuccess': True})

@login_required
@check_permission('Security_Log', 'write')
def proxy_add_wechat_request_whitelist(request):
    nginxConf = NginxConf()
    serverKeys = nginxConf.get_all_upstream_site_names()
    errorMsg = None
    if request.method == 'POST':
        serverKey = request.POST.get('serverKey')
        reg = request.POST.get('reg')
        comment = request.POST.get('comment')

        conf = nginxConf.get_upstream_by_server_key(serverKey)
        site_name = conf['ServerName']
        if conf['ServerNameType'] == 'IPv6':
            site_name = '[' + site_name + ']'
        site_name += ':' + conf['ListenPort']
        if conf['site_customize_name']:
            site_name = conf['site_customize_name'] + '@' + site_name

        if not check_regular_valid(reg):
            logger.info('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ERROR REG')
            return respo(request, site_name, 'wechat request whitelist', {'saveSuccess': False, 'errorCode': 'errorReg'})

        isHost = False
        for key in serverKeys:
            if serverKey == key:
                isHost = True
                break

        if not isHost:
            return respo(request, site_name, 'wechat request whitelist', {'saveSuccess': False, 'errorCode': 'errorHost'})

        # Deduplication
        access_white_list = conf['wechat_access_white_list']
        for i in range(0, len(access_white_list)):
            requestWhiteObj = access_white_list[i]
            if requestWhiteObj[0] == reg:
                return respo(request, site_name, 'wechat request whitelist', {'saveSuccess': False, 'errorCode': 'dupReg'})

        access_white_list.append([reg, comment, 'all'])

        nginxConf.save_upstream(serverKey, conf)

        return respo(request, site_name, 'wechat request whitelist', {'saveSuccess': True})

@login_required
@check_permission('Security_Log', 'write')
def proxy_add_alipay_mpp_request_whitelist(request):
    nginxConf = NginxConf()
    serverKeys = nginxConf.get_all_upstream_site_names()
    errorMsg = None
    if request.method == 'POST':
        serverKey = request.POST.get('serverKey')
        reg = request.POST.get('reg')
        comment = request.POST.get('comment')

        conf = nginxConf.get_upstream_by_server_key(serverKey)
        site_name = conf['ServerName']
        if conf['ServerNameType'] == 'IPv6':
            site_name = '[' + site_name + ']'
        site_name += ':' + conf['ListenPort']
        if conf['site_customize_name']:
            site_name = conf['site_customize_name'] + '@' + site_name

        if not check_regular_valid(reg):
            logger.info('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ERROR REG')
            return respo(request, site_name, 'alipay mpp request whitelist', {'saveSuccess': False, 'errorCode': 'errorReg'})

        isHost = False
        for key in serverKeys:
            if serverKey == key:
                isHost = True
                break

        if not isHost:
            return respo(request, site_name, 'alipay mpp request whitelist', {'saveSuccess': False, 'errorCode': 'errorHost'})

        # Deduplication
        access_white_list = conf['alipay_mpp_access_white_list']
        for i in range(0, len(access_white_list)):
            requestWhiteObj = access_white_list[i]
            if requestWhiteObj[0] == reg:
                return respo(request, site_name, 'alipay mpp request whitelist', {'saveSuccess': False, 'errorCode': 'dupReg'})

        access_white_list.append([reg, comment, 'all'])

        nginxConf.save_upstream(serverKey, conf)

        return respo(request, site_name, 'alipay mpp request whitelist', {'saveSuccess': True})

@login_required
@check_permission('Security_Log', 'write')
def proxy_add_mpaas_mpp_request_whitelist(request):
    nginxConf = NginxConf()
    serverKeys = nginxConf.get_all_upstream_site_names()
    errorMsg = None
    if request.method == 'POST':
        serverKey = request.POST.get('serverKey')
        reg = request.POST.get('reg')
        comment = request.POST.get('comment')

        conf = nginxConf.get_upstream_by_server_key(serverKey)
        site_name = conf['ServerName']
        if conf['ServerNameType'] == 'IPv6':
            site_name = '[' + site_name + ']'
        site_name += ':' + conf['ListenPort']
        if conf['site_customize_name']:
            site_name = conf['site_customize_name'] + '@' + site_name

        if not check_regular_valid(reg):
            logger.info('@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@ ERROR REG')
            return respo(request, site_name, 'mpaas mpp request whitelist', {'saveSuccess': False, 'errorCode': 'errorReg'})

        isHost = False
        for key in serverKeys:
            if serverKey == key:
                isHost = True
                break

        if not isHost:
            return respo(request, site_name, 'mpaas mpp request whitelist', {'saveSuccess': False, 'errorCode': 'errorHost'})

        # Deduplication
        access_white_list = conf['mpaas_mpp_access_white_list']
        for i in range(0, len(access_white_list)):
            requestWhiteObj = access_white_list[i]
            if requestWhiteObj[0] == reg:
                return respo(request, site_name, 'mpaas mpp request whitelist', {'saveSuccess': False, 'errorCode': 'dupReg'})

        access_white_list.append([reg, comment, 'all'])

        nginxConf.save_upstream(serverKey, conf)

        return respo(request, site_name, 'mpaas mpp request whitelist', {'saveSuccess': True})

@login_required
@check_permission_list(['Security_Log','WAF_Analysis_And_Process'], 'write')
def proxy_add_waf_whitelist(request):
    nginxConf = NginxConf()
    errorMsg = None
    if request.method == 'POST':
        serverKey = request.POST.get('serverKey')
        wafPathReg = request.POST.get('wafPathReg', '')
        wafRuleId = request.POST.get('wafRuleId', '')
        server_name = request.POST.get('serverName', '')
        args = request.POST.get('args')
        comment = request.POST.get('comment')
        referer = request.POST.get('referer')

        def resp(resp_data):
            operation_log_str = ugettext_noop(
                    'Import the WAF whitelist configuration for the site {upstream_key} from the security log.'
                )
            if referer == '2':
                operation_log_str = ugettext_noop(
                        'Import the WAF whitelist configuration for the site {upstream_key} from the waf misreport analyse.'
                    )
            op_code = '0' if resp_data.get('saveSuccess') else '1'

            if referer == '2':
                log_from = ugettext_noop('WAF')
            else:
                if get_product_type() == 'Safeplus':
                    log_from=ugettext_noop('Statistics_Report')
                else:
                    log_from=ugettext_noop('Log')
                resp_data['log_from']=log_from
            operation_log(request, log_from, ugettext_noop('Import'), op_code, {
                'msg': operation_log_str,
                'extra': {
                    'upstream_key': server_name,
                }
            })

            return JsonResponse(resp_data)

        # serverKey use ':' between name and port, we should replace the last ':' with '_' before check permission
        if not has_permission_of_website(request.user.username,
                                         ''.join(reversed(''.join(reversed(serverKey))))):
            return resp({'saveSuccess': False, 'errorCode': 'errorHost'})

        if server_name == '':
            server_name = serverKey
        conf = nginxConf.get_upstream_by_server_key(serverKey)

        if conf == None:
            return resp({'saveSuccess': False, 'errorCode': 'errorHost'})

        if wafRuleId != '':
            if not re.match(r'^[1-9][0-9]{0,9}(,[1-9][0-9]{0,9})*$', wafRuleId):
                errormsg = _('Invalid waf whitelist id.')
                return resp({'saveSuccess': False, 'errorCode': 'dupReg', 'errorMsg':errormsg})

        isMegerd = False
        # Deduplication
        wafWhitelist = conf['Inject_Whitelist']
        for i in range(0, len(wafWhitelist)):
            wafWhiteObj = wafWhitelist[i]
            duplication = False
            errormsg = ''
            duplicationCount = 0
            if len(wafWhiteObj) > 3:
                # here is new version (wl_type,wl_path, wl_id, wl_args, wl_comment)
                if wafWhiteObj[1] == wafPathReg:
                    if wafWhiteObj[2] == wafRuleId:
                        duplication = True
                    elif len(wafWhiteObj[2]) == 0 or len(wafRuleId) == 0:
                        isMegerd = True
                        wafWhiteObj[2] = ''
                    else:
                        ids = wafRuleId.split(',')
                        exist_ids = wafWhiteObj[2].split(',')
                        for id_item in ids:
                            if id_item not in exist_ids:
                                isMegerd = True
                                wafWhiteObj[2] += ','+id_item
                            else:
                                duplicationCount += 1
                        if duplicationCount == len(ids):
                            duplication = True

            else:
                # here is old version
                if wafWhiteObj[0] == wafPathReg:
                    duplication = True

            if duplication:
                errormsg = _('There are duplicate configuration items in the WAF white list.')
                return resp({'saveSuccess': False, 'errorCode': 'dupReg', 'errorMsg':errormsg})

            if isMegerd:
                wafWhitelist[i] = wafWhiteObj
                path_tip = wafPathReg
                if len(wafPathReg) == 0:
                    path_tip = _('empty path')
                errormsg = _('Saved successful,and has been merged into the WAF whitelist configuration with path {}.').format(cgi.escape(path_tip))
                break

        if not isMegerd:
            errormsg = ''
            if not check_regular_valid(wafPathReg):
                errormsg = _('Invalid waf whitelist path.')
                return resp({'saveSuccess': False, 'errorCode': 'dupReg', 'errorMsg':errormsg})
            wafWhitelist.append(['url', wafPathReg, wafRuleId, '', comment])

        nginxConf.save_upstream(serverKey, conf)

        return resp({'saveSuccess': True, 'errorMsg':errormsg})

def is_upgrade_for_protected_level(upstreamConf):
    protected_list = upstreamConf.get('protected_list')
    if not protected_list:
        return True
    return False


def is_upstream_editable():
    conf = ConfDb()
    license_info = conf.get_license_info(i18n_support=False)
    is_license_active = license_info.is_valid_license()
    is_license_expired = not license_info.is_in_effect()
    is_license_evaluate = license_info.is_evaluative_license()

    if get_current_is_debug():
        return True

    if not is_license_active:
        return False
    elif is_license_expired:
        if is_license_evaluate:
            return False
        else:
            return True
    else:
        return True


def set_upgrade_protected_list(nginxConf, upstreamConf):
    protected_list = upstreamConf.get('protected_list')
    if not protected_list:
        protected_list = {}
    # Get current protect min level
    protected_min_level = upstreamConf.get('security_level_min')
    inject_detect = upstreamConf.get('check_inject_detect_open')
    all_protection = all_protection_list()
    if protected_min_level:
        if 'cookie_token' in protected_min_level:
            protected_list[all_protection[Protected_List_Type_Basic_Protection][0]] = True
            protected_list[all_protection[Protected_List_Type_Advanced_Protection][0]] = False
            protected_list[all_protection[Protected_List_Type_SDK][0]] = False
            protected_list[all_protection[Protected_List_Type_AIWAF][0]] = False
            protected_list['ajax_request_body_encryption'] = False
        if 'url' in protected_min_level:
            protected_list[all_protection[Protected_List_Type_Basic_Protection][0]] = True
            protected_list[all_protection[Protected_List_Type_Advanced_Protection][0]] = True
            protected_list[all_protection[Protected_List_Type_SDK][0]] = False
            protected_list[all_protection[Protected_List_Type_AIWAF][0]] = False

        if inject_detect:
            protected_list[all_protection[Protected_List_Type_Injection][0]] = True
        else:
            protected_list[all_protection[Protected_List_Type_Injection][0]] = False
        if 'bot' in protected_min_level:
            protected_list[all_protection[Protected_List_Type_Automation][0]] = True
            protected_list[all_protection[Protected_List_Type_Crack][0]] = True
        else:
            protected_list[all_protection[Protected_List_Type_Automation][0]] = False
            protected_list[all_protection[Protected_List_Type_Crack][0]] = False

    upstream_enable = upstreamConf.get('enabled', True)
    if not upstream_enable:
        if upstreamConf.get('learning_mode'):
            upstreamConf['learning_mode'] = False
    else:
        pass

    upstreamConf[nginxConf.UK_PROTECTED_LIST] = protected_list
    txsafe_min_level = upstreamConf[nginxConf.UK_SecurityLevelMin]
    txsafe_max_level = upstreamConf[nginxConf.UK_SecurityLevelMax]

def set_default_protected_list(nginxConf, upstreamConf):
    protected_list = upstreamConf.get('protected_list')
    if not protected_list:
        protected_list = {}
    full_protection_list = all_protection_list()
    protected_list_len = len(protected_list)
    full_protection_list_len = len(full_protection_list)
    if protected_list_len < full_protection_list_len:
        for i in range(protected_list_len, full_protection_list_len):
            protected_list[full_protection_list[i][0]] = full_protection_list[i][1]
        # Update the Config
        upstreamConf[nginxConf.UK_PROTECTED_LIST] = protected_list


def protected_list_update(nginxConf, upstreamConf):
    protected_list = upstreamConf.get('protected_list')
    if not protected_list:
        protected_list = []
    full_protection_list = all_protection_list()
    protected_list_len = len(protected_list)
    full_protection_list_len = len(full_protection_list)
    if protected_list_len < full_protection_list_len:
        for i in range(protected_list_len, full_protection_list_len):
            protected_list.append(full_protection_list[i][1])
        # Update the Config
        upstreamConf[nginxConf.UK_PROTECTED_LIST] = protected_list

def is_save_replace_config_valid(subs_string_in, subs_string_out, subs_string_path):
    try:
        re.compile(subs_string_in)
    except:
        # not valid regular expression
        return 1

    with tempfile.NamedTemporaryFile(mode='w+t', bufsize=0) as tmp_config:
        # prepare the temporary config file
        sub_strings_in_encrypted = base64.b64encode(subs_string_in.replace('\r\n', '\n'))
        sub_strings_out_encrypted = base64.b64encode(subs_string_out.replace('\r\n', '\n'))
        sub_strings_path_encrypted = base64.b64encode(subs_string_path)
        config_string = 'events {} http { server { subs_filter %s %s %s r; } }' % \
                             (sub_strings_path_encrypted, sub_strings_in_encrypted, sub_strings_out_encrypted)
        tmp_config.write(config_string)

        # verify the configuration with nginx command
        code, output, _ = exe_with_output('sudo /usr/sbin/limited_exe nginx-t {}'.format(tmp_config.name), False)
        logger.debug('The result of nginx config verification is %s' % output)

    # check the result of nginx verification
    if re.search('test is successful', output):
        # verification successful
        return 0
    elif re.search('unknown.*variable', output):
        # there are named references in replace content
        return 2
    elif re.search('too many regex substrings', output):
        # more references than capturing groups
        return 3
    else:
        # other errors
        return 4


def check_subfilter_input_length(path, sin, sout, comment):
    if len(path) > 1024:
        return _("The length of {0} can not exceed {1} characters.").format(_("Search Path"), 1024)
    if len(sin) > 500:
        return _("The length of {0} can not exceed {1} characters.").format(_("Search Content"), 500)
    if len(sout) > 500:
        return _("The length of {0} can not exceed {1} characters.").format(_("Replace With"), 500)
    if len(comment) > 500:
        return _("The length of {0} can not exceed {1} characters.").format(_("Comment"), 500)

@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
def save_replace(request):
    if request.method == "POST":
        subfilter_array = json.loads(request.body)
        nginxConf = NginxConf()
        subfilter_strings_array = []
        if len(subfilter_array) > 0:
            for index in range(0, len(subfilter_array)):
                subfilter = subfilter_array[index]
                subfilter_is_regex = subfilter['subfilter_is_regex']
                subfilter_string_path = subfilter['subfilter_replace_path']
                subfilter_string_in = subfilter['subfilter_string_in']
                subfilter_comment = subfilter['subfilter_comment']
                subfilter_string_out = subfilter['subfilter_string_out']

                errorMsg = check_subfilter_input_length(subfilter_string_path, subfilter_string_in, subfilter_string_out, subfilter_comment)
                if errorMsg:
                    return HttpResponse(json.dumps({'saveSuccess': False,
                                                        'errorCode': 'errorReg',
                                                        'errorMsg': errorMsg,
                                                        'index': index}),
                                            content_type='application/json')

                if not check_regular_valid(subfilter_string_path):
                        logger.error('Regular expression of search path is not valid.')
                        return HttpResponse(json.dumps({'saveSuccess': False,
                                                        'errorCode': 'errorReg',
                                                        'errorMsg': _('Regular expression of search path is not valid.'),
                                                        'index': index}),
                                            content_type='application/json')
                if subfilter_is_regex == '1':
                    result = is_save_replace_config_valid(subfilter_string_in, subfilter_string_out, subfilter_string_path)
                    if result == 1:
                        logger.error('Regular expression of search content is not valid.')
                        return HttpResponse(json.dumps({'saveSuccess': False,
                                                        'errorCode': 'errorReg',
                                                        'errorMsg': _(
                                                            'Regular expression of search content is not valid.'),
                                                        'index': index}),
                                            content_type='application/json')
                    elif result == 2:
                        logger.error('Do not support named references or position 0 reference in replace content.')
                        return HttpResponse(json.dumps({'saveSuccess': False,
                                                        'errorCode': 'errorReg',
                                                        'errorMsg': _(
                                                            'Do not support named references or position 0 reference in replace content.'),
                                                        'index': index}),
                                            content_type='application/json')
                    elif result == 3:
                        logger.error('Regular expression capturing groups do not match references.')
                        return HttpResponse(json.dumps({'saveSuccess': False,
                                                        'errorCode': 'errorReg',
                                                        'errorMsg': _(
                                                            'Regular expression capturing groups do not match references.'),
                                                        'index': index}),
                                            content_type='application/json')
                    elif result == 4:
                        logger.error('Other errors when verify the nginx configuration.')
                        return HttpResponse(json.dumps({'saveSuccess': False,
                                                        'errorCode': 'errorReg',
                                                        'errorMsg': _(
                                                            'Errors when verify configuration, see logs for detail information.'),
                                                        'index': index}),
                                            content_type='application/json')
                subfilter_strings_in_encypted = base64.b64encode(subfilter_string_in.replace('\r\n','\n'))
                subfilter_comment_encypted = base64.b64encode(subfilter_comment.replace('\r\n','\n'))
                subfilter_strings_out_encypted = base64.b64encode(subfilter_string_out.replace('\r\n','\n'))
                subfilter_strings_path_encypted = base64.b64encode(subfilter_string_path)
                subfilter_strings_array.append((subfilter_strings_in_encypted, subfilter_strings_out_encypted, subfilter_strings_path_encypted, subfilter_is_regex, subfilter_comment_encypted))

            nginxConf.set_subfilter_string(subfilter_strings_array)
        else:
            nginxConf.set_subfilter_string(subfilter_strings_array)

        operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Modify'), '0',
            {'msg': ugettext_noop('Replacement rules have been modified')},
            user=request.POST.get('username'))
        return HttpResponse(json.dumps({'saveSuccess': True}), content_type='application/json')
    return HttpResponse(json.dumps({'saveSuccess': True}), content_type='application/json')


@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
def save_watermark(request):
    if request.method == "POST":
        watermark_array = json.loads(request.body)

        # 验证数据合法性
        for i, rule in enumerate(watermark_array):
            url = rule['url']
            content = rule['content']
            if len(url) > 1024:
                return HttpResponse(json.dumps({'saveSuccess': False, 'errPos': [i, 0], 'msg':  _('[ {0} ] should not exceed {1} characters.').format(_('Watermark URL'), 1024)}), content_type='application/json')
            if len(content) > 26:
                return HttpResponse(json.dumps({'saveSuccess': False, 'errPos': [i, 1], 'msg':  _('[ {0} ] should not exceed {1} characters.').format(_('Watermark text'), 26)}), content_type='application/json')
            if regex_consistence_check(url, '/watermark') is not None:
                return HttpResponse(json.dumps({'saveSuccess': False, 'errPos': [i, 0], 'msg':  _('Regex(%s) is incorrect') % (url)}), content_type='application/json')

        nginx_conf = NginxConf()
        old_watermark_array = nginx_conf.get_watermark()

        action = ugettext_noop('Modify')
        detail = {'msg': ugettext_noop('watermark rules have been modified')}
        diff = ''
        if len(watermark_array) == len(old_watermark_array): #modify
            found_modify = False
            for i, new in enumerate(watermark_array):
                for k, v in new.items():
                    if old_watermark_array[i][k] != v:
                        diff += k + ': OLD{' + json.dumps(old_watermark_array[i][k], ensure_ascii=False) + '} ---> NEW{' + json.dumps(v, ensure_ascii=False) + '}\n'
                        found_modify = True

                if found_modify:
                    break

            if diff is not '':
                detail['info'] = diff
        elif len(watermark_array) > len(old_watermark_array): #add
            info = 'Add Rule:\n' + json.dumps(watermark_array[-1], ensure_ascii=False, indent=4)
            action = ugettext_noop('Add')
            detail = {'msg': ugettext_noop('add new watermark rule'), 'info': info}
        else: #delete
            found_del = True
            for old in old_watermark_array:
                for new in watermark_array:
                    if old['url'] == new['url']: #这里直接用url来判断，若配置了相同url的多条规则，会有问题；正常不应该配置相同url的多条规则
                        found_del = False
                        break
                if found_del:
                    break

            info = 'Deleted Rule:\n' + json.dumps(old, ensure_ascii=False, indent=4)
            action = ugettext_noop('Delete')
            detail = {'msg': ugettext_noop('delete watermark rule'), 'info': info}

        nginx_conf.set_watermark(watermark_array)

        operation_log(request, ugettext_noop('Global_Settings'), action, '0',
                      detail, user=request.POST.get('username'))
        return HttpResponse(json.dumps({'saveSuccess': True}), content_type='application/json')

def get_errpage_filename(errpage_type):

    upload_page_type = {'err_upload_400': 'err_400.html',
                        'err_upload_403': 'err_403.html',
                        'err_upload_404': 'err_404.html',
                        'err_upload_500': 'err_500.html',
                        'err_upload_501': 'err_501.html',
                        'err_upload_502': 'err_502.html',
                        'err_upload_503': 'err_503.html',
                        'err_upload_504': 'err_504.html'}

    for key, value in upload_page_type.items():
        if errpage_type == key:
            return value

    return ''

def get_errpage_checksum(filepath):

    if filepath != '':
        code, output, cmd_line = exe_with_output('cksum {0}'.format(filepath), False)
        if code == 0:
            checksum = output.split()
        return checksum[0]

    return 0

def save_upload_errpage(filetype, errPage):
    try:
        filename = get_errpage_filename(filetype)
        fullpath = get_sync_file_path(filename)

        write_errpage_ret = open(fullpath, 'wb+')
        write_errpage_ret.write(errPage)
        write_errpage_ret.close()

        ret = BaseConf().sync_file(fullpath)
        logger.info('######## upload error page ret = {}'.format(ret))

        return True if ret == RESULT.OK else False
    except Exception, e:
        logger.exception('######## upload error page exception:' + str(e))
        return False


def get_ip_list(content, range_list, key_list, enable_validity = False, validity_duration = 0):
    content = content.replace(' ', '')
    valid_list = []
    invalid_list = []
    try:
        json.dumps(content)
        lines = re.split('\n|\r|\r\n', content)
        if len(lines) == 0:
            return [], []

        valid_list, invalid_list = parse_upload_ip_list(lines, range_list, key_list, enable_validity, validity_duration)

    except Exception as e:
        logger.info('get ip list failed {0}'.format(str(e)))
        return valid_list, invalid_list

    return valid_list, invalid_list


def save_ip_list_file(filetype, ip_list, path):
    ip_list = sorted(ip_list, key=lambda x: x.split(',')[0])
    base_conf = BaseConf()
    base_conf.set_asp_conf_values([(path + '/value/', ip_list), (path + '/count/', len(ip_list))])

    return len(ip_list)


def err_page_src(request):
    err_page_dict = ['err_400.html', 'err_403.html', 'err_404.html', 'err_500.html',
                     'err_501.html', 'err_502.html', 'err_503.html', 'err_504.html']
    page_name = request.GET.get('page_name')

    if page_name not in err_page_dict:
        # Raise 403 error page
        return HttpResponse("")

    fn = get_release_file('web_admin/src/web_admin/templates/report/' + page_name)
    with open(fn, 'rb') as fp:
        content = fp.read()

    content = '\xEF\xBB\xBF' + content

    return HttpResponse(content, content_type='text/plain')


def check_subfilter_args(args):
    argsCollection = 'irog'
    for ch in args:
        if ch in argsCollection:
            continue
        return False
    return True


def render_proxys(request, nginx_conf, error=None, *args, **kwargs):
    if not error:
        error = ''
    # sort Website List
    upstreamConfListTmp = nginx_conf.get_all_upstreams_for_show()
    upstreamConfListTmp2 = sorted(upstreamConfListTmp, key=operator.itemgetter('name'))
    upstreamConfList = []

    user_name = request.user.username
    if WebconsoleConf().get_user_role(user_name) == 'Operator':
        sites_of_user = {site['key'] for site in get_sites_4_user(user_name)}
        for upstream in upstreamConfListTmp2:
            if upstream['key'] in sites_of_user:
                upstreamConfList.append(upstream)
    else:
        upstreamConfList = upstreamConfListTmp2

    # sort Upstream Address List
    for upstream in upstreamConfList:
        upstream['UpstreamList'].sort()
        upstream['ServerName'] = escape(upstream['ServerName'])
        extract_data = upstream.get('Extra_Business_Data', [])

        if upstream.get('ajax_token_path_type') is None:
            upstream['ajax_token_path_type'] = 0

        for one_data in extract_data:
            if len(one_data) <= 9:
                upstream['Extra_Business_Data'] = []
                break
    upstream_editable = is_upstream_editable()
    table_view = WebconsoleConf().get_tab_view(request.user.username)
    return base_render_to_response(request, 'v2/config_proxy_list.html',
                                   {
                                       'maxProxies': nginx_conf.get_license_info().max_proxies,
                                       'upstreamConfList': upstreamConfList,
                                       'error': error,
                                       'is_upstream_editable': upstream_editable,
                                       'table_view': table_view,
                                       'isProxyRole': ConfDb().is_proxy(),
                                       'wechat_occupied_sites': WechatConf().get_mpp_occupied_site(upstreamConfList),
                                       'alipay_mpp_occupied_sites': AlipayConf().get_mpp_occupied_site(upstreamConfList),
                                       'mpaas_mpp_occupied_sites': MPaasConf().get_mpp_occupied_site(upstreamConfList),
                                   })

@login_required
def proxys(request):
    nginxConf = NginxConf()
    username = request.user.username

    if request.method == "POST":
        action = request.POST.get('action')
        if action == 'delete':
            serverKey = request.POST.get('serverKey')
            siteKey = request.POST.get('siteKey')
            siteCustomizeName = nginxConf.get_upstream_conf(serverKey).get('site_customize_name', '')
            if not has_permission(username, 'Delete_Website', 'write') or not has_permission_of_website(username, serverKey):
                return HttpResponse(status=403)

            try:
                clean_site_config_2_blank(serverKey)
            except SiteNotExists:
                pass
            except:
                raise

            flowlearnConf=FlowlearnConf()
            flow_learn_sitekey=flowlearnConf.get_sitekey_by_serverkey(serverKey)

            # Remove config and persis and reload nginx.
            logger.info('#### Remove upstream: ' + serverKey)
            waf_conf = WafConf()
            site_extra_values = {}
            # update waf site strategy used list
            waf_site_strategy_values = waf_conf.update_waf_site_strategy(site_strategy_id=None, server_name=serverKey,
                                                                        action='delete')
            waf_strategy_values = WafStrategyCtrl().update_strategy_used_list(serverKey, action='delete')
            if waf_site_strategy_values:
                site_extra_values.update(**waf_site_strategy_values)
            if waf_strategy_values:
                site_extra_values.update(**waf_strategy_values)
            code, output, cmd_line = nginxConf.remove_upstream(serverKey, extra_values=site_extra_values)

            if code == 0:
                flowlearnConf.update_used_list(flow_learn_sitekey)

            siteInfo = (siteCustomizeName + '@' + siteKey) if (siteCustomizeName != '') else siteKey
            operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Delete'), code, {
                'msg': ugettext_noop('Website deleted'), 'spliceMsg': ': '  + siteInfo})
            if code != 0:
                error = 'Failed to remove upstream, error code: {0}, cmd: {1}, messages: {2}'.format(
                    code, cmd_line, output)
                logger.error(error)

            # Reload config
            nginxConf = NginxConf()
        else:
            pass

    if not has_permission(username, 'Protected_Websites', 'read'):
        return HttpResponse(status=403)
    return render_proxys(request, nginxConf)

@login_required
@allow_method('post')
@check_permission('Enable_Site_Conf', 'write')
def enable_site_conf(request):
    nginxConf = NginxConf()
    serverKey = request.POST.get('serverKey')

    siteConf = nginxConf.get_conf().get_all('nginx/upstreams/' + serverKey)
    if siteConf is None or siteConf == {} or siteConf.get('_deleted') == 1:
        return json_fail_response(_('The site does not exist. Please refresh the page and try again.'))

    enableSiteConf_org = nginxConf.get_upstream_conf(serverKey).get('enable_site_conf', True)

    if not has_permission_of_website(request.user.username, serverKey):
        return HttpResponse(status=403)

    enableSiteConf_req = request.POST.get('enable_site_conf')
    if (enableSiteConf_req != 'true' and enableSiteConf_req != 'false'):
        return HttpResponse(status=403)

    enableSiteConf = enableSiteConf_req != 'false'

    if enableSiteConf != enableSiteConf_org:
        # update config and persis and reload nginx.
        info = '#### Update site conf status : ' + serverKey + ' {}->{}'.format(enableSiteConf_org, enableSiteConf)
        logger.info(info)
        code, output, cmd_line = nginxConf.set_enable_site_conf_switch(serverKey, enableSiteConf)

        operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Modify'), code,
            {'msg': ugettext_noop('Website modified'),
            'extra': serverKey,
            'info': info})

        if code == 0:
            logger.info('Update Site Conf Status Saved successfully: cmd: {0}, output: {1}.'.format(cmd_line, output, ))
            return json_ok_response(_('Site configuration state has been modified'))
        else:
            logger.error('Update Site Conf Status Saved Failed! : cmd: {0}, output: {1}.'.format(cmd_line, output, ))
            return json_fail_response(_('Failed to modify site configuration state'))


@login_required
@check_permission('Global_Settings', 'write')
def stream_forward(request):
    data = {}
    if request.method == 'POST':
        conf = NginxConf()
        forward_config = json.loads(request.body)
        stream_forward_whitelist = forward_config.get('pattern_list', [])
        # handle http request line forwarding, we need restart nginx, so we need taskid here
        values = {
            'nginx/stream_forward_whitelist': stream_forward_whitelist,
        }
        code, output, cmd_line = conf.set_asp_conf_values(values, sync=False)
        data.update(output);
        return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Global_Settings', 'read')
def proxy_global_settings(request):
    error = None
    user_name = request.user.username
    is_expert_mode = WebconsoleConf().is_expert_mode(user_name)
    nginxConf = NginxConf()
    license_info = nginxConf.get_license_info()
    license_salt = license_info.get_dict().get('mobile_salt')
    upload_max_size = nginxConf.get_upload_max_size()
    upload_max_size_num = int(filter(str.isdigit, upload_max_size.encode("ascii")))
    statusMap = {'fail': 'fail', 'success': 'success', 'default': 'default'}
    show_global_setting = statusMap['default']
    uploadErrPageSuccess = statusMap['default']
    setUploadMaxeSizeSuccess = statusMap['default']
    configSourceIpSuccess = statusMap['default']

    uploadIpBlackSuccess = ''
    ip_black_count = 0
    if request.method == "POST":
        if not has_permission(user_name, 'Global_Settings', 'write'):
            return HttpResponse(status=403)
        action = request.POST.get('action')

        if action == 'set_upload_max_size':
            logger.info("######## set upload max size")
            upload_max_size_num = request.POST.get('upload_max_size_num')
            if upload_max_size_num.isdigit() and int(upload_max_size_num) in range(1,1024*1024+1):
                upload_max_size = upload_max_size_num + 'M'
                nginxConf.set_upload_max_size(upload_max_size)
                setUploadMaxeSizeSuccess = statusMap['success']
                operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop("Modify"), '0',
                              {'msg': ugettext_noop('Update Max Upload File Size Value: {0}'), 'extra': [upload_max_size]},
                              user=request.POST.get('username'))

        elif action == 'config_source_ip':
            logger.info("######## config source ip")
            src_ip_strategy_error = None
            configSourceIpSuccess = statusMap['success']
            src_ip_strategy_list = get_src_ip_strategy_list(request)

            try:
                src_ip_strategy_list = json.loads(src_ip_strategy_list)
                src_ip_strategy_error = valid_src_ip_strategy_list(src_ip_strategy_list)
                if src_ip_strategy_error:
                    configSourceIpSuccess = statusMap['fail']
                else:
                    code, output, cmdline = nginxConf.set_src_ip_strategy_list(src_ip_strategy_list)
                    if code != 0:
                        configSourceIpSuccess = statusMap['fail']
            except Exception as e:
                src_ip_strategy_error = '{}'.format(e)
                configSourceIpSuccess = statusMap['fail']

            result,ret_code = ('ok','0') if configSourceIpSuccess == 'success' else ('failed','1')
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop("Modify"),ret_code,
                          {'msg': ugettext_noop('Update Obtaining Source IP')},
                          user=request.POST.get('username'))

            ret = {'result': result, 'msg': src_ip_strategy_error}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

        elif action == 'upload_err_page':
            logger.info('######## upload error page')
            errPage = None
            file_size = 0
            isInvalidSuffix = False

            for filename, myfile in request.FILES.iteritems():
                file_size = request.FILES[filename].size
                errPage = request.FILES[filename]
                if not re.match('^.*\.(htm|html)$',errPage.name):
                    isInvalidSuffix = True

            if errPage is None or file_size == 0 or file_size > 2 ** 20:
                logger.info('uploaded error page is empty or larger than 1M')
                uploadErrPageSuccess = statusMap['fail']
            elif isInvalidSuffix:
                logger.error('The uploaded error page template carries an invalid suffix name.')
                uploadErrPageSuccess = statusMap['fail']
            else:
                try:
                    errPage = errPage.read()
                    # check whether upload file is plain text by using json dumps
                    json.dumps(errPage)
                    if save_upload_errpage(filename, errPage) == True:
                        uploadErrPageSuccess = statusMap['success']
                        # e.g. err_upload_400， just use the last code in operation_log
                        updated_item = ""
                        try:
                            updated_item = filename.split("_")[-1]
                        except Exception:
                            pass
                        operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop("Modify"), '0',
                                      {'msg': ugettext_noop('Upload error page template: {0}'), 'extra': [updated_item]},
                                      user=request.POST.get('username'))
                    else:
                        uploadErrPageSuccess = statusMap['fail']
                except:
                    uploadErrPageSuccess = statusMap['fail']
                    logger.info('uploaded error page is not plain text')

            show_global_setting = statusMap['success']

        elif action == 'update_errpage_status':
            customized_error_pages = str(request.POST.get('custom_errpage_status'))

            if customized_error_pages == '':
                logger.info('no data !!!!!!!!')
                errpage = ''
            else:
                errpage = customized_error_pages.split(',')

            def check_errpage(errage):
                if errpage == '':
                    return True
                for e in errage:
                    if e not in ('400', '403', '404', '500', '501', '502', '503', '504'):
                        return False
                return True

            if check_errpage(errpage):
                nginxConf.set_errpage_status(errpage)
                updated_items = ""
                try:
                    updated_items = ", ".join(errpage)
                except Exception:
                    pass
                operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop("Modify"), '0',
                              {'msg': ugettext_noop('Update Error Page Templates Settings: {0}'), 'extra': [updated_items]},
                              user=request.POST.get('username'))
                nginxConf = NginxConf()

        else:
            pass

    errpage_return = []
    errpage_json = nginxConf.get_errpage_status()

    if errpage_json == None:
        errpage_return = [0]
    else:
        for item in errpage_json:
            errpage_return.append(int(item))

    # sort Website List
    upstreamConfListTmp = nginxConf.get_all_upstreams_for_show()
    upstreamConfList = sorted(upstreamConfListTmp, key=operator.itemgetter('name'))

    # sort Upstream Address List
    for upstream in upstreamConfList:
        upstream['UpstreamList'].sort()
    upstream_editable = is_upstream_editable()

    encypted_subfilterString = nginxConf.get_subfilter_string()
    decypted_subfilterString = []
    if encypted_subfilterString is not None:
        for encypted_subfilter in encypted_subfilterString:
            decypted_subfilter = []
            # If upgrade from 1705 or before
            try:
                decypted_subfilter.append(base64.b64decode(encypted_subfilter[0]))
                decypted_subfilter.append(base64.b64decode(encypted_subfilter[1]))
                decypted_subfilter.append(base64.b64decode(encypted_subfilter[2]))
                decypted_subfilter.append(encypted_subfilter[3])
                if len(encypted_subfilter) > 4:
                    decypted_subfilter.append(base64.b64decode(encypted_subfilter[4]))
                else:
                    decypted_subfilter.append('')
            except:
                decypted_subfilter = []
                decypted_subfilter.append(encypted_subfilter[0])
                # If has blank
                isRegex = False
                split_array = encypted_subfilter[1].split(" ")
                split_array_no_blank = filter(lambda x: x != '', split_array)
                if len(split_array_no_blank) > 1:
                    encypted_subfilter[1] = split_array_no_blank[0].strip()
                    # is regex before
                    if 'r' in split_array_no_blank[1] and check_subfilter_args(split_array_no_blank[1]):
                        isRegex = True

                decypted_subfilter.append(encypted_subfilter[1])
                decypted_subfilter.append("/*")
                if isRegex:
                    decypted_subfilter.append("1")
                else:
                    decypted_subfilter.append("0")

            decypted_subfilterString.append(decypted_subfilter)

    # get ip black list info
    ip_black_count = ConfDb().get_ip_black_count()
    if not ip_black_count:
        ip_black_count = 0

    enable_direct_conn_ctrl = 'true' if nginxConf.get_direct_conn_ctrl_switch() else 'false'
    direct_conn_ctrl_ip_list = nginxConf.get_direct_conn_ctrl_ip_list()

    enable_ip_black = nginxConf.get_ip_black_switch()
    if not enable_ip_black:
        enable_ip_black = 'false'
    else:
        enable_ip_black = 'true'

    # get ip white list info
    ip_white_count = ConfDb().get_ip_white_count()
    if not ip_white_count:
        ip_white_count = 0

    enable_ip_white = nginxConf.get_ip_white_switch()
    if not enable_ip_white:
        enable_ip_white = 'false'
    else:
        enable_ip_white = 'true'

    enable_keep_src_ip = nginxConf.get_keep_src_ip_switch()
    if not enable_keep_src_ip:
        enable_keep_src_ip = 'false'
    else:
        enable_keep_src_ip = 'true'

    enable_admin_listen = nginxConf.get_admin_listen_switch()
    if not enable_admin_listen:
        enable_admin_listen = 'false'
    else:
        enable_admin_listen = 'true'

    token_rename = nginxConf.get_token_rename()

    stream_forward_whitelist = nginxConf.get_conf().get_value('nginx/stream_forward_whitelist', [])
    watermarkArray = nginxConf.get_watermark()

    goodBotConf = nginxConf.get_value("good_bot")
    static, dynamic = get_all_good_bot_names()
    all_bots_name = static | dynamic
    if not goodBotConf:
        goodBotConf = {
            "search_engines": dict(map(lambda x: [x,[True, False]], all_bots_name)),
            "enable_search_engines": False,
            "enable_online_upgrade": False,
            "upgrade_server": 'update.riversecurity.com',
            "upgrade_period": 1,
            "upgrade_time": "01:00",
            "version": get_good_bot_version(),
            "custom_ua": {
                "enable": False,
                "ua_list": []
            }
        }
    goodBotConf['download_url'] = GoodBotUpgrade(nginxConf).get_signed_url('get', GoodBotUpgrade.UPDATE_PATH, {})
    is_license_valid = license_info.is_in_effect() or get_current_is_debug()

    client_type_features = nginxConf.get_value('client_type_features', {})
    multiEndpointsStrategy = {
        'transparent_mpp_requests': client_type_features.get('mpp', {}).get('enable', False),
        'transparent_app_native_requests': client_type_features.get('app_native', {}).get('enable', False),
        'transparent_app_h5_requests': client_type_features.get('app_h5', {}).get('enable', False),
        'transparent_external_site_requests': client_type_features.get('external_site', {}).get('enable', False),
        'referers': client_type_features.get('external_site', {}).get('rules', [])
    }


    return base_render_to_response(request, 'v2/proxy_global_settings.html',
                                   {
                                       'maxProxies': nginxConf.get_license_info().max_proxies,
                                       'upstreamConfList': upstreamConfList,
                                       'subfilterStringsArray': decypted_subfilterString,
                                       'watermarkArray':watermarkArray,
                                       'uploadErrPageSucc': str(uploadErrPageSuccess),
                                       'custom_errpage_status': errpage_return,
                                       'sites_with_xrealip_xforwarded4_enabled': NginxConf().get_sites_with_xrealip_xforwarded4_enabled(),
                                       'config_source_ip_success':str(configSourceIpSuccess),
                                       'show_global_setting': show_global_setting,
                                       'upload_max_size_num': upload_max_size_num,
                                       'set_upload_max_size_success': str(setUploadMaxeSizeSuccess),
                                       'is_upstream_editable': upstream_editable,
                                       'goodBotConf': goodBotConf,
                                       'keep_src_ip': enable_keep_src_ip,
                                       'admin_listen': enable_admin_listen,
                                       'single_adapter': 'true' if nginxConf.get_conf().is_single_adapter() else 'false',
                                       'enable_update_error_page': nginxConf.get_update_error_page_switch(),
                                       'has_license_salt': license_salt and license_salt != 'None',
                                       'enable_ip_black': enable_ip_black,
                                       'enable_direct_conn_ctrl': enable_direct_conn_ctrl,
                                       'direct_conn_ctrl_ip_list': direct_conn_ctrl_ip_list,
                                       'enable_alert_box_for_cookie': nginxConf.get_alert_box_for_cookie_switch(),
                                       'enable_strictly_match_host': nginxConf.get_strictly_match_host_switch(),
                                       'enable_gm_algorithm': nginxConf.get_gm_algorithm_switch(),
                                       'is_gm_supported': license_info.is_gm_algorithm_supported(),
                                       'ip_black_count': ip_black_count,
                                       'enable_ip_white': enable_ip_white,
                                       'ip_white_count': ip_white_count,
                                       'token_rename': token_rename,
                                       'uploadIpBlackSuccess': str(uploadIpBlackSuccess),
                                       'httpForward' : stream_forward_whitelist,
                                       'is_license_valid': is_license_valid,
                                       "global_src_ip_strategy_list": nginxConf.get_src_ip_strategy_list(),
                                       'multiEndpointsStrategy': multiEndpointsStrategy,
                                       'enable_slow_http_attack': nginxConf.get_slow_http_attack(),
                                   })


@login_required
@check_permission('Global_Settings', 'write')
@require_http_methods(['POST'])
def multi_endpoints_strategy(request):
    # 验证数据有效性：长度，格式
    if len(request.body) > 3000:
        return JsonResponse({'errorMsg': _('Invalid data')})

    def validate(strategy):
        check_type = lambda k: k in strategy and isinstance(strategy[k], bool)
        if not (check_type('transparent_mpp_requests') \
                and check_type('transparent_app_native_requests') \
                and check_type('transparent_app_h5_requests') \
                and check_type('transparent_external_site_requests')):
            return False

        if 'referers' in strategy:
            if not isinstance(strategy['referers'], list):
                return False

            # 校验总条数不能超过10条
            count = len(strategy['referers'])
            if count > 10:
                return False

            # 校验是否存在重复数据
            if count != len(set(strategy['referers'])):
                return False

            # 校验单个referer长度及格式
            check_referer = lambda k: isinstance(k, unicode) and len(k) <= 256 and re.match('^[\x20-\x7E]+$', k)
            for referer in strategy['referers']:
                if not check_referer(referer):
                    return False

        return True

    try:
        curStrategy = json.loads(request.body)
    except:
        return JsonResponse({'errorMsg': _('Invalid data')})

    if not validate(curStrategy):
        return JsonResponse({'errorMsg': _('Invalid data')})

    nginxConf = NginxConf()
    oldStrategy = nginxConf.get_value('client_type_features', {})
    oldExternalSite = oldStrategy.get('external_site', {'enable': False, 'rules': []})
    newExternalSite = {}
    newExternalSite['enable'] = curStrategy['transparent_external_site_requests']
    newExternalSite['rules'] = curStrategy.get('referers', []) if newExternalSite['enable'] else oldExternalSite['rules']

    newStrategy = {
        'mpp': {
            'enable': curStrategy['transparent_mpp_requests'],
            'rules': oldStrategy.get('mpp', {}).get('rules', []) # 因为这个规则，在界面上不允许配，但是可能由系统导入进来，因此，此处应该要保留原来的规则。
        },
        'app_native': {
            'enable': curStrategy['transparent_app_native_requests'],
            'rules': oldStrategy.get('app_native', {}).get('rules', [])
        },
        'app_h5': {
            'enable': curStrategy['transparent_app_h5_requests'],
            'rules': oldStrategy.get('app_h5', {}).get('rules', [])
        },
        'external_site': newExternalSite
    }

    result = nginxConf.set_value('client_type_features', newStrategy)
    if result != 0:
        operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Modify'), '1',
                      {'msg': ugettext_noop('Failed to modify Multi-Client Protection Policy.')},
                      user=request.POST.get('username'))
        return JsonResponse({'errorMsg': _('Save failed')})
    else:
        detail = {
            'msg': ugettext_noop('Multi-Client Protection Policy has been modified successfully.'),
            'info': 'OLD: {} -> NEW: {}'.format(json.dumps(oldStrategy), json.dumps(newStrategy))
        }
        operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Modify'), '0',detail, user=request.POST.get('username'))
        return JsonResponse({'saveSuccess': True})


class UpstreamConfDb(ConfDb):
    def __init__(self, jsonfile):
        if os.path.exists(jsonfile):
            with open(jsonfile, 'rb') as fp:
                try:
                    conf = json.load(fp, 'utf-8')
                    super(UpstreamConfDb, self).__init__(conf)
                except Exception as e:
                    print('!!! Failed to import ASP configuration database: {}'.format(e))
                    super(UpstreamConfDb, self).__init__({})
        else:
            super(UpstreamConfDb, self).__init__({})
        self.fn = jsonfile


def export_waf_strategy(conf):
    waf_strategy_id = conf.get('waf_strategy')
    if waf_strategy_id:
        waf_strategy_id = int(waf_strategy_id)
        if waf_strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:
            conf = WafConf()
            return conf.export_waf_site_strategy_info(waf_strategy_id)
    return None


def export_waf_syntax_strategy(conf):
    waf_strategy_id = conf.get('waf_strategy')
    if waf_strategy_id:
        waf_strategy_id = int(waf_strategy_id)
        if waf_strategy_id > waf_util.CUSTOM_STRATEGY_MIN_ID:
            conf = WafConf()
            return conf.export_waf_site_syntax_info(waf_strategy_id)
    return None

@login_required
@check_permission('Export_Website', 'read')
def export_upstream(request):
    if request.method == "GET":
        action = request.GET.get('action')
        if action == 'export':
            serverKey = request.GET.get('serverKey')
            if not has_permission_of_website(request.user.username, serverKey):
                return HttpResponse(status=403)
            tempConf = '/tmp/exportConf.json'
            if os.path.exists(tempConf):
                os.remove(tempConf)

            conf = UpstreamConfDb(tempConf)
            conf.set_value("server_key", serverKey)
            upstream_info = ConfDb().get_values("nginx/upstreams/" + serverKey)
            if not upstream_info:
                logger.error("export site failed, site {} dose not exists".format(serverKey))
                return JsonResponse({"message": "site {} dose not exists".format(serverKey)}, status=404)

            # clean Certification and CertificationKey before export
            upstream_info.pop('Certification', None)
            upstream_info.pop('CertificationKey', None)
            upstream_info.pop('cert_file_name', None)
            upstream_info.pop('key_file_name', None)
            upstream_info.pop('gm_sign_certification', None)
            upstream_info.pop('gm_sign_certificationKey', None)
            upstream_info.pop('gm_sign_cert_file_name', None)
            upstream_info.pop('gm_sign_key_file_name', None)
            upstream_info.pop('gm_enc_certification', None)
            upstream_info.pop('gm_enc_certificationKey', None)
            upstream_info.pop('gm_enc_cert_file_name', None)
            upstream_info.pop('gm_enc_key_file_name', None)
            upstream_info.pop('Enable_Attack_Sensation', None)
            upstream_info.pop('Enable_Cookie_Collection', None)
            upstream_info.pop('Enable_Post_Data_Collection', None)
            upstream_info.pop('Extra_Business_Data', None)
            upstream_info.pop('Extra_Session_in_Cookie', None)
            upstream_info.pop('request_protection_list', None)

            # export waf stragtegy
            waf_strategy_info = export_waf_strategy(upstream_info)
            if waf_strategy_info:
                upstream_info['waf_strategy_info'] = waf_strategy_info

            waf_syntax_info = export_waf_syntax_strategy(upstream_info)
            if waf_syntax_info:
                upstream_info['waf_syntax_info'] = waf_syntax_info

            WafStrategyCtrl().export(upstream_info)

            conf.set_value("upstream", upstream_info)
            conf.save()

            #operation log info
            code = '0'
            site_customize_name = upstream_info.get("site_customize_name", "")
            if site_customize_name: site_customize_name += '@'
            servername = upstream_info.get("ServerName","")
            listenport = upstream_info.get("ListenPort","")
            if not servername or not listenport : code = '1'
            if upstream_info.get("ServerNameType","") == "IPv6":
                op_log_info = site_customize_name + '[' + servername + ']:' +listenport
            else:
                op_log_info = site_customize_name + servername + ':' +listenport
            with open(tempConf) as json_file:
                response = HttpResponse(json_file.read(), content_type='application/json')
                response['Content-Disposition'] = "attachment; filename=%s" % (serverKey + '.json')
                operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Export'), code, {
                    'msg': ugettext_noop('Upstream Config has been Exported: {upstream}'),
                    'extra':{"upstream" : op_log_info},
                    },
                    user=request.POST.get('username'))
                return response

    # Invalid request
    return HttpResponseRedirect("/overview/")

@login_required
@check_permission('Global_Settings', 'read')
def download_ip_black_file(request):
    if request.method == 'GET':
        base_conf = BaseConf()
        ip_black_list = base_conf.get_value('ip_black/value/', '')
        ip_black = '\n'.join(ip_black_list)
        response = StreamingHttpResponse(ip_black)
        response['Content-Type'] = 'application/octet-stream'
        response['Content-Disposition'] = 'attachment;filename="{0}"'.format('ip_black_file.txt')
        return response

    else:
        result = {'result': 'FAILED', 'message': 'Invalid request.'}
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Global_Settings', 'write')
def upload_ip_black_file(request):
    confdb = ConfDb()
    origin_ip_black_count = confdb.get_ip_black_count()
    if request.method == 'POST':
        logger.info('######## upload ip black file')
        ip_file_name = None
        ip_file = None
        ip_file_content = None
        ip_file_size = 0

        action = request.POST.get('action', '')
        enable_validity = request.POST.get('enable_validity', 'false')
        validity_duration = request.POST.get('validity_duration', 0)

        try:
            validity_duration = int(validity_duration)
        except:
            ret = {'count': origin_ip_black_count, 'success': False, 'msg': 'Invalid Arguments', 'err_code': 0}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        if action not in ("append", "overwrite") or enable_validity not in ('true', 'false') or validity_duration < 0 or validity_duration > 63072000:
            ret = {'count': origin_ip_black_count, 'success': False, 'msg': 'Invalid Arguments', 'err_code': 0}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        enable_validity = enable_validity == 'true'

        for filename, ipfile in request.FILES.iteritems():
            ip_file_name = request.FILES[filename].name
            ip_file = request.FILES[filename]
            ip_file_size = request.FILES[filename].size

        if ip_file_size > 10 * 2 ** 20:
            uploadIpBlackSuccess = 'IP Black file exceed 10M'
            ret = {'count': origin_ip_black_count, 'success': False, 'msg': uploadIpBlackSuccess, 'err_code': 1}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        if ip_file:
            ip_file_content = ip_file.read()

        if not ip_file_content or not ip_file_name:
            uploadIpBlackSuccess = 'IP Black file is empty'
            ret = {'count': origin_ip_black_count, 'success': False, 'msg': uploadIpBlackSuccess, 'err_code': 2}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        range_list = []
        key_list = []
        ip_list, invalid_list = get_ip_list(ip_file_content, range_list, key_list, enable_validity, validity_duration)
        if len(ip_list) == 0 and len(invalid_list) != 0:
            ret = {'count': origin_ip_black_count, 'success': False, 'msg': 'Invalid IP Black file', 'err_code': 3}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        if action == 'append':
            origin_ip_black_list = confdb.get_ip_black_list()
            merge_exist_ip(origin_ip_black_list, ip_list, range_list, key_list)

        ip_list = delete_expired_ip(ip_list)

        if len(ip_list) > 100000:
            uploadIpBlackSuccess = 'The number of data entries in the file has exceeded 100,000'
            ret = {'count': origin_ip_black_count, 'success': False, 'msg': uploadIpBlackSuccess, 'err_code': 1}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)
        else:
            ret_count = save_ip_list_file(ip_file_name, ip_list, 'ip_black')
            if ret_count == 0 and len(ip_list) != 0:
                ret = {'count': origin_ip_black_count, 'success': False, 'msg': 'Invalid IP Black file', 'err_code': 3}
                return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

            ip_black_count = ConfDb().get_ip_black_count()
            logger.info('#### update ip black list, count:{} action:{} enable_validity:{} validity_duration:{}'.format(ip_black_count, action, enable_validity, validity_duration))
            ret = {'count': ip_black_count, 'success': True, 'msg': 'ok'}
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Append') if action == 'append' else ugettext_noop('Overwrite'), '0',
                            {'msg': ugettext_noop('The IP black list file has been uploaded')},
                            user=request.POST.get('username'))
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

    ret = {'count': origin_ip_black_count, 'success': False, 'msg': 'Upload IP Black file failed', 'err_code': 4}
    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)


@login_required
@check_permission('Global_Settings', 'write')
def enable_ip_black(request):
    if request.method == 'POST':
        enable_ip_black = request.POST.get('enable') == 'true'
        nginxConf = NginxConf()
        nginxConf.set_ip_black_switch(enable_ip_black)
        nginxConf = NginxConf()

        enable_ip_black = nginxConf.get_ip_black_switch()
        if not enable_ip_black:
            enable_ip_black = 'false'
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                          {'msg': ugettext_noop('Disable IP Blacklist')}, user=request.POST.get('username'))
        else:
            enable_ip_black = 'true'
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Enable'), '0',
                          {'msg': ugettext_noop('Enable IP Blacklist')}, user=request.POST.get('username'))

        logger.info('#### update enable_ip_black to: {}'.format(enable_ip_black))

        ret = {'enable': enable_ip_black, 'success': True}
        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

    ret = {'enable': False, 'success': False}
    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Global_Settings', 'write')
def clear_ip_black(request):
    if request.method == 'DELETE':
        base_conf = BaseConf()
        base_conf.set_asp_conf_values([('ip_black/value/', []), ('ip_black/count/', 0)])

        operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Clear'), '0',
                      {'msg': ugettext_noop('Clear IP Blacklist')}, user=request.POST.get('username'))

        ret = {'success': True}
        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

    ret = {'success': False}
    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Global_Settings', 'read')
def download_ip_white_file(request):
    if request.method == 'GET':
        base_conf = BaseConf()
        ip_white_list = base_conf.get_value('ip_white/value/', '')
        ip_white = '\n'.join(ip_white_list)
        response = StreamingHttpResponse(ip_white)
        response['Content-Type'] = 'application/octet-stream'
        response['Content-Disposition'] = 'attachment;filename="{0}"'.format('ip_white_file.txt')
        return response

    else:
        result = {'result': 'FAILED', 'message': 'Invalid request.'}
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Global_Settings', 'write')
def upload_ip_white_file(request):
    confdb = ConfDb()
    origin_ip_white_count = confdb.get_ip_white_count()
    if request.method == 'POST':
        logger.info('######## upload ip white file')
        ip_file_name = None
        ip_file = None
        ip_file_content = None
        ip_file_size = 0

        action = request.POST.get('action', '')
        enable_validity = request.POST.get('enable_validity', 'false')
        validity_duration = request.POST.get('validity_duration', 0)

        try:
            validity_duration = int(validity_duration)
        except:
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': 'Invalid Arguments', 'err_code': 0}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        if action not in ("append", "overwrite") or enable_validity not in ('true', 'false') or validity_duration < 0 or validity_duration > 63072000:
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': 'Invalid Arguments', 'err_code': 0}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        enable_validity = enable_validity == 'true'

        if action not in ("append", "overwrite"):
            uploadIpWhiteSuccess = 'unsupported action'
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': uploadIpWhiteSuccess, 'err_code': 1}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        for filename, ipfile in request.FILES.iteritems():
            ip_file_name = request.FILES[filename].name
            ip_file = request.FILES[filename]
            ip_file_size = request.FILES[filename].size

        if ip_file_size > 10 * 2 ** 20:
            uploadIpWhiteSuccess = 'IP White file exceed 10M'
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': uploadIpWhiteSuccess, 'err_code': 1}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        if ip_file:
            ip_file_content = ip_file.read()

        if not ip_file_content or not ip_file_name:
            uploadIpWhiteSuccess = 'IP White file is empty'
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': uploadIpWhiteSuccess, 'err_code': 2}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        range_list = []
        key_list = []
        ip_list, invalid_list = get_ip_list(ip_file_content, range_list, key_list, enable_validity, validity_duration)
        if len(ip_list) == 0 and len(invalid_list) != 0:
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': 'Invalid IP White file', 'err_code': 3}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

        if action == 'append':
            origin_ip_white_list = confdb.get_ip_white_list()
            merge_exist_ip(origin_ip_white_list, ip_list, range_list, key_list)

        ip_list = delete_expired_ip(ip_list)

        if len(ip_list) > 10000:
            uploadIpWhiteSuccess = 'The number of data entries in the file has exceeded 10,000'
            ret = {'count': origin_ip_white_count, 'success': False, 'msg': uploadIpWhiteSuccess, 'err_code': 1}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)
        else:
            ret_count = save_ip_list_file(ip_file_name, ip_list, 'ip_white')
            if ret_count == 0 and len(ip_list) != 0:
                ret = {'count': origin_ip_white_count, 'success': False, 'msg': 'Invalid IP White file', 'err_code': 3}
                return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

            ip_white_count = ConfDb().get_ip_white_count()
            logger.info('#### update ip white list, count:{} action:{} enable_validity:{} validity_duration:{}'.format(ip_white_count, action, enable_validity, validity_duration))
            ret = {'count': ip_white_count, 'success': True, 'msg': 'ok'}
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Append') if action == 'append' else ugettext_noop('Overwrite'), '0',
                            {'msg': ugettext_noop('The IP white list file has been uploaded')},
                            user=request.POST.get('username'))
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)

    ret = {'count': origin_ip_white_count, 'success': False, 'msg': 'Upload IP White file failed', 'err_code': 4}
    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_PLAIN)


@login_required
@check_permission('Global_Settings', 'write')
def enable_ip_white(request):
    if request.method == 'POST':
        enable_ip_white = request.POST.get('enable') == 'true'
        nginxConf = NginxConf()
        nginxConf.set_ip_white_switch(enable_ip_white)
        nginxConf = NginxConf()

        enable_ip_white = nginxConf.get_ip_white_switch()
        if not enable_ip_white:
            enable_ip_white = 'false'
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                          {'msg': ugettext_noop('Disable IP Whitelist')}, user=request.POST.get('username'))
        else:
            enable_ip_white = 'true'
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Enable'), '0',
                          {'msg': ugettext_noop('Enable IP Whitelist')}, user=request.POST.get('username'))

        logger.info('#### update enable_ip_white to: {}'.format(enable_ip_white))

        ret = {'enable': enable_ip_white, 'success': True}
        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

    ret = {'enable': False, 'success': False}
    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Global_Settings', 'write')
def clear_ip_white(request):
    if request.method == 'DELETE':
        base_conf = BaseConf()
        base_conf.set_asp_conf_values([('ip_white/value/', []), ('ip_white/count/', 0)])

        operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Clear'), '0',
                      {'msg': ugettext_noop('Clear IP Whitelist')}, user=request.POST.get('username'))

        ret = {'success': True}
        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

    ret = {'success': False}
    return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)


def check_ip_list_comments_validity(val):
    val = val.strip() if val else u''
    valTmp = val.decode("utf-8") if isinstance(val, str) else val

    if len(valTmp) == 0:
        return True

    return re.match(u'^[()-._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$', valTmp) if len(valTmp) <= 32 else False

@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
def handle_direct_conn_ctrl(request):
    set_result = False

    if request.method == 'POST':
        enable_direct_conn_ctrl = request.POST.get('enable') == 'true'
        direct_conn_ctrl_ip_list = request.POST.get('ip_list')

        try:
            ip_list = json.loads(direct_conn_ctrl_ip_list)
            CONST_INDEX_COMMENT = 3
            for row in range(len(ip_list)):
                if (len(ip_list[row]) >= CONST_INDEX_COMMENT and not check_ip_list_comments_validity(ip_list[row][CONST_INDEX_COMMENT])):
                    logger.error('#### handle_direct_conn_ctrl: Invalid Comments on line {}'.format(row + 1))
                    result = {'result': 'FAILED', 'success': False, 'message': 'Invalid Comments.'}
                    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        except Exception as e:
            logger.error(e.message)
            return JsonResponse({'result': 'FAILED', 'success': False})

        nginxConf = NginxConf()
        old_direct_conn_ctrl_ip_list = nginxConf.get_direct_conn_ctrl_ip_list()
        old_enable_direct_conn_ctrl = nginxConf.get_direct_conn_ctrl_switch()
        ret, output, error_msg = nginxConf.set_direct_conn_ctrl(enable_direct_conn_ctrl, direct_conn_ctrl_ip_list)

        if ret != 0:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                          {'msg': ugettext_noop('Failed to save configuration')}, user=request.POST.get('username'))
        else:
            set_result = True
            diff = ''

            if old_enable_direct_conn_ctrl != enable_direct_conn_ctrl:
                if enable_direct_conn_ctrl:
                    old_status = _('disabled')
                    new_status = _('enabled')
                else:
                    old_status = _('enabled')
                    new_status = _('disabled')
                diff += 'state: OLD{' + old_status + '} ---> NEW{' + new_status + '}\n'

            if old_direct_conn_ctrl_ip_list != direct_conn_ctrl_ip_list:
                diff += 'ip list: OLD{' + old_direct_conn_ctrl_ip_list + '} ---> NEW{' + direct_conn_ctrl_ip_list + '}'

            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Modify'), '0',
                          {'msg': ugettext_noop('Modify direct connection control'),'info': diff}, user=request.POST.get('username'))

        logger.info('#### update enable_direct_connection_control to: {}'.format(set_result))

    return HttpResponse(json.dumps({'success': set_result}), content_type = CONTENT_TYPE_JSON)

@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
def enable_alert_box_for_cookie(request):
    if request.method == 'POST':
        nginxConf = NginxConf()
        is_enable = request.POST.get('enable_alert_box_for_cookie') == 'true'
        nginxConf.set_alert_box_for_cookie_switch(is_enable)
        if is_enable:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Enable'), '0',
                      {'msg': ugettext_noop('Open Ask to Enable Cookie')},
                      user=request.POST.get('username'))
        else:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                        {'msg': ugettext_noop('Close Ask to Enable Cookie')},
                        user=request.POST.get('username'))

        return HttpResponse(json.dumps({'success': True}), content_type=CONTENT_TYPE_JSON)
    return HttpResponse(json.dumps({'success': False}), content_type=CONTENT_TYPE_JSON)


@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
def enable_strictly_match_host(request):
    if request.method == 'POST':
        nginxConf = NginxConf()
        is_enable = request.POST.get('enable_strictly_match_host') == 'true'
        nginxConf.set_strictly_match_host_switch(is_enable)
        if is_enable:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Enable'), '0',
                        {'msg': ugettext_noop('Open Strictly Match Host Header')},
                        user=request.POST.get('username'))
        else:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                        {'msg': ugettext_noop('Close Strictly Match Host Header')},
                        user=request.POST.get('username'))

        return HttpResponse(json.dumps({'success': True}), content_type=CONTENT_TYPE_JSON)
    return HttpResponse(json.dumps({'success': False}), content_type=CONTENT_TYPE_JSON)

@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
def enable_slow_http_attack(request):
    if request.method == 'POST':
        nginxConf = NginxConf()
        if nginxConf.get_conf().is_mirror() or nginxConf.get_conf().is_plugin():
            return HttpResponse(status=403)
        is_enable = request.POST.get('enable_slow_http_attack') == 'true'
        nginxConf.set_slow_http_attack(is_enable)
        if is_enable:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Enable'), '0',
                          {'msg': ugettext_noop('Open Slow Http Attack Protect')},
                          user=request.POST.get('username'))
        else:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                          {'msg': ugettext_noop('Close Slow Http Attack Protect')},
                          user=request.POST.get('username'))

        return HttpResponse(json.dumps({'success': True}), content_type=CONTENT_TYPE_JSON)
    return HttpResponse(json.dumps({'success': False}), content_type=CONTENT_TYPE_JSON)

@login_required
@expert_mode_required
@check_permission('Global_Settings', 'write')
@adv_operation_code_required
def enable_gm_algorithm(request):
    if request.method == 'POST':
        nginxConf = NginxConf()
        is_enable = request.POST.get('enable_gm_algorithm') == 'true'

        old_ngx_pid = get_nginx_pids()
        cpu_num = get_cpu_core_num()
        ret_code, status, msg = nginxConf.set_gm_algorithm_switch(is_enable)
        if ret_code != 0:
            return JsonResponse({'result': 'failed', 'error': ugettext_noop('Failed')})

        for i in range(120):
            cur_ngx_pid = get_nginx_pids()
            # 网页防篡改功能使用了 proxy_cache_path 指令，nginx会拉起一个独立的进程用来专门维护缓存相关的内容，所以nginx的worker数是大于cpu核数的
            # 这里判断只要新的worker进程数大于了cpu核数，就认为reload完成
            # 需要注意的是：reload的时候，对于存在长连接的老worker不会立即被关闭，如果浏览器端刷新页面的时候，使用之前的连接继续发送请求，这个请求将任然在老的nginx worker上处理。
            if len(cur_ngx_pid - old_ngx_pid) >= cpu_num:
                break
            time.sleep(0.5)

        if is_enable:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Enable'), '0',
                        {'msg': ugettext_noop('Open GM Algorithm')},
                        user=request.POST.get('username'))
        else:
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop('Disable'), '0',
                        {'msg': ugettext_noop('Close GM Algorithm')},
                        user=request.POST.get('username'))

        return JsonResponse({'result': 'ok'})

    return JsonResponse({'result': 'failed', 'error': _('Failed')})


@login_required
@expert_mode_required
@check_permission('Update_Geo_Lib', 'write')
def system_updateGeolib(request):
    if request.method == "POST":
        logger.info('start to update Geo lib')
        conf_db = ConfDb()
        geolib_conf = conf_db.get_value("nginx/geolib", {})

        ret = 1 # failed
        try:
            fullpath = ''
            if geolib_conf and 'mmdb_cvst' == geolib_conf.get('geolib_type', 'awdb'):
                filename = 'GeoIP2-City.tar.gz'
                fullpath = get_sync_file_path(filename)
                f = request.FILES['lib']
                if f.size > (200 * 1024 * 1024):
                    # Bigger than 200MB
                    ret = 4
                    raise ValueError("The compressed file uploaded cannot exceed 200MB")
                with open(get_upgrade_lib_path(fullpath), 'wb') as of:
                    for chunk in f.chunks():
                        of.write(chunk)
                t = tarfile.open(fullpath, 'r')
                totalsize = 0
                for member_info in t.getmembers():
                    totalsize += member_info.size
                    if  totalsize > 500 * 1024 * 1024:
                        os.remove(fullpath)
                        ret = 5
                        raise ValueError("The decompressed file cannot exceed 500MB")
                ret = exe('tar -tf {} | grep GeoIP2-City.mmdb'.format(fullpath), error_on_exit=False)
                if ret != 0:
                    os.remove(fullpath)
                    ret = 6
                    raise ValueError("Please make sure MaxMind GeoLib is included in the uploaded file")
            else:
                filename = 'AIWENGEO_v1.awdb.tar'
                fullpath = get_sync_file_path(filename)
                f = request.FILES['lib']
                magic_offset = 8
                payload_len_offset = magic_offset + 2
                payload = ''
                of_md5 = hashlib.new('md5')
                with open(get_upgrade_lib_path(fullpath), 'wb') as of:
                    first_chunk = True
                    for chunk in f.chunks():
                        if not first_chunk:
                            of.write(chunk)
                            of_md5.update(chunk)
                            continue

                        # first chunk contains meta info
                        magic = chunk[0:magic_offset]
                        if magic != "AWDBv100":
                            ret = 3
                            raise ValueError("Unexpected bin file, magic check failed")

                        import struct
                        payload_len = int(struct.unpack('>H', chunk[magic_offset:payload_len_offset])[0])
                        payload_offset = payload_len_offset + payload_len

                        payload = chunk[payload_len_offset:payload_offset]
                        if len(payload) != payload_len:
                            ret = 2 # invalid file format
                            raise ValueError("Unexpected bin file, do not found md5 Tag")

                        of.write(chunk[payload_offset:])
                        of_md5.update(chunk[payload_offset:])
                        first_chunk = False

                # 计算MD5和meta载荷前32字节是否一致
                # meta载荷后面的字节留作后面再使用, maybe?
                if of_md5.hexdigest() != payload[0:32]:
                    ret = 2
                    raise ValueError("md5 checksum failed")

            result = BaseConf().sync_file(fullpath, sync=False)
            logger.info('######## upload remote geo lib ret = {}'.format(result))

            ret = 0 if result == RESULT.OK else 1
        except Exception as e:
            logger.error(e)

        operation_log(request, ugettext_noop('System'), ugettext_noop('Upload'), str(ret), {
                'msg': ugettext_noop('Upload geolocation library.')
        })

        return HttpResponse(json.dumps({"result": ret}), content_type=CONTENT_TYPE_PLAIN)


@login_required
@expert_mode_required
@check_permission('Update_Geo_Lib', 'write')    # 这里使用的是地理库升级权限
@require_http_methods(['POST', 'PUT', 'DELETE'])
def custom_geo_ip(request):
    errResp = {"result": "error", "message": _("Invalid post body")}

    if len(request.body) > 1024 * 500:
        return JsonResponse(errResp)

    try:
        data = json.loads(request.body)
        validate_custom_geo_info(data)
    except Exception as e:
        logger.error(e.message)
        return JsonResponse(errResp)

    ngxConf = NginxConf()
    confData = ngxConf.get_value('custom_geo_ip', get_default_custom_geo())

    def findItem(ip):
        for item in confData:
            if ip.upper() == item['ip'].upper():
                return item

    method = request.method
    if method == 'POST':
        # 新增
        action = ugettext_noop('Add')
        item = data[0]  # 新增时，提交数据格式为数组，仅包含一条数据
        detail = {
            'msg': ugettext_noop('Add custom geo ip'),
            'info': 'Add: {}'.format(json.dumps(item, ensure_ascii=False))
        }
        if len(data) + len(confData) > 500:
            operation_log(request, ugettext_noop('System'), action, '1', detail)
            return JsonResponse({"result": "error", "message": _("A maximum of 500 records are allowed to be added.")})

        if not findItem(item['ip']):
            confData.append(item)
        else:
            operation_log(request, ugettext_noop('System'), action, '1', detail)
            return JsonResponse({"result": "error", "message": _("Duplicate IP")})
    elif method == 'PUT':
        # 编辑
        action = ugettext_noop('Modify')
        detail = {
            'msg': ugettext_noop('Modify custom geo ip'),
            'info': ''
        }
        submit_item = data[0]  # 编辑时，提交数据格式为数组，仅包含一条数据
        conf_item = findItem(submit_item['old_ip'])    # 对于编辑的情况，在提交数据中增加了额外的 old_ip 来关联配置
        old_ip = submit_item['old_ip']
        new_ip = submit_item['ip']
        if not conf_item:
            # 被编辑的ip记录不存在
            operation_log(request, ugettext_noop('System'), action, '1', detail)
            return JsonResponse({"result": "error", "message": _("IP ({}) does not exist").format(old_ip)})

        if old_ip.upper() != new_ip.upper() and findItem(submit_item['ip']):
            # 编辑后的目标IP已存在
            operation_log(request, ugettext_noop('System'), action, '1', detail)
            return JsonResponse({"result": "error", "message": _("IP ({}) already exists").format(new_ip)})

        submit_item.pop('old_ip')
        detail['info'] = 'OLD: {} -> NEW: {}'.format(
            json.dumps(conf_item, ensure_ascii=False), json.dumps(submit_item, ensure_ascii=False))
        conf_item.update(submit_item)

    elif method == 'DELETE':
        # 删除
        action = ugettext_noop('Delete')
        keys = [item['ip'].upper() for item in data]    # 需要删除的ip
        confData = [item for item in confData if item['ip'].upper() not in keys]
        detail = {
            'msg': ugettext_noop('Delete custom geo ip'),
            'info': 'Delete {}'.format(json.dumps(data, ensure_ascii=False))
        }

    ret = ngxConf.set_value('custom_geo_ip', confData)
    if ret == 0:
        operation_log(request, ugettext_noop('System'), action, '0', detail)
        return JsonResponse({"result": "success", "message": ""})
    else:
        operation_log(request, ugettext_noop('System'), action, '1', detail)
        return JsonResponse({"result": "error", "message": _("Save failed")})


def check_site_customize_name_validity(val):
    val = val.strip() if val else u''
    valTmp = val.decode("utf-8") if isinstance(val, str) else val

    if len(valTmp) == 0:
        return True

    return re.match(u'^[()-._\sA-Za-z0-9\u4e00-\u9fa5\uff08-\uff09]+$', valTmp) if len(valTmp) <= 68 else False

@login_required
@allow_method('post')
@check_permission('Change_Website_Name', 'write')
def add_site_customize_name(request):
    try:
        # changes = [[server_key_1, value1],[server_key_2, value2]]
        changes = json.loads(request.body)
    except Exception:
        return HttpResponse(status=403)
    op_log_info = []
    changed_sites = []
    for serverKey, new_value in changes:
        try:
            check_site_exists(serverKey)
            if not check_site_customize_name_validity(new_value):
                return HttpResponse(status=403)
        except Exception:
            return HttpResponse(status=403)

        if not has_permission_of_website(request.user.username, serverKey):
            return HttpResponse(status=403)
        changed_sites.append(serverKey)
        op_log_info.append("Update {} name to: {}".format(serverKey, new_value))

    nginxConf = NginxConf()
    code, output, cmd_line = nginxConf.set_site_names(changes)

    if code == 0:
        logger.info('Add site_customize_name Saved successfully: cmd: {0}, output: {1}.'.format(cmd_line, output, ))
        operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Modify'), code,
                    {'msg': ugettext_noop('Website modified'),
                     'extra': ': ' + ';'.join(changed_sites),
                     'info': '\n'.join(op_log_info)})
        return json_ok_response(_('Add Site Customize Name OK!'))
    else:
        logger.error('Add site_customize_name Saved Failed! : cmd: {0}, output: {1}.'.format(cmd_line, output, ))
        return json_fail_response(_('Add Site Customize Name Failed!'))


def diff_upstream(conf, old_conf):
    diff = ''
    try:
        import operator
        IGNORED_PROPERTIES = ['key', 'status', 'CertificationKey', 'Certification', 'gm_sign_certificationKey', 'gm_sign_certification', 'gm_enc_certificationKey', 'gm_enc_certification']

        # trun tuple to list
        new_conf = json.loads(json.dumps(conf))

        for k, new in new_conf.items():
            old = old_conf.get(k)
            if operator.eq(new, old):
                continue

            if k not in IGNORED_PROPERTIES:
                if type(new) is dict and type(old) is dict:
                    for kk in new.keys():
                        if old.has_key(kk) and operator.eq(new[kk],old[kk]):
                            new.pop(kk)
                            old.pop(kk)
                diff += k + ': OLD{' + json.dumps(old) + '} ---> NEW{' + json.dumps(new) + '}\n'

    except Exception as e:
        logger.error('diff_upstream failed: {}'.format(e))

    return diff


def update_waf_syntax_default_strategy(upstreamConf) :
    strategies = waf_util.get_waf_default_syntax_strategies()
    waf_syntax_info = upstreamConf.get('waf_syntax_info', {})
    for k,v in strategies.items():
        module = waf_syntax_info.get(k)
        if not module:
            waf_syntax_info[k] = {
                "enabled": False,
                "default_strategy_id": "1",
            }
    upstreamConf['waf_syntax_info'] =  waf_syntax_info
    return waf_syntax_info

def update_waf(nginxConf, upstreamConf,ignore_compare=False):
    #Previous WAF upgrade code before version 20.01
    old_full_inject_whitelist = upstreamConf.get('Inject_Whitelist', [])
    full_inject_whitelist = upgrade_inject_whitelist(old_full_inject_whitelist)
    upstreamConf['Inject_Whitelist'] = full_inject_whitelist


    if upstreamConf.get(nginxConf.UK_WAF_CSRF_ENABLE) is None:
        upstreamConf[nginxConf.UK_WAF_CSRF_ENABLE] = False
    if upstreamConf.get(nginxConf.UK_WAF_CSRF_LIST) is None:
        upstreamConf[nginxConf.UK_WAF_CSRF_LIST] = []
    if upstreamConf.get(nginxConf.UK_WAF_CSRF_SITE_WHITELIST) is None:
        upstreamConf[nginxConf.UK_WAF_CSRF_SITE_WHITELIST] = ""

    if upstreamConf.get(nginxConf.UK_WAF_RES_LEECH_STRATEGY) is None:
        upstreamConf[nginxConf.UK_WAF_RES_LEECH_STRATEGY] = '-1'

    try:
        # Before custom rules enhancement, the regs item is a regex string
        # Old version data sample:
        #     "Inject_Patternlist": {
        #     "_value": [
        #         [
        #             ".*",
        #             "test",
        #             "1001",
        #             "1",
        #             "1"
        #         ]
        #     ]
        #     },
        custom_rules = upstreamConf.get('Inject_Patternlist', [])
        # upgrade from v20.03 or below to v20.05
        # rule_type item is always "1" before
        if custom_rules:
            for id in range(0, len(custom_rules)):
                rule_type = custom_rules[id][4]
                if rule_type == "1":
                    reg_list = [{"key": "Request_All", "value": custom_rules[id][0]}]
                    custom_rules[id][0] = json.dumps(reg_list)
                    custom_rules[id][4] = "USER-DEFINE"
            upstreamConf['Inject_Patternlist'] = custom_rules

        waf_strategy_info = upstreamConf.get('waf_strategy_info')
        waf_strategy = upstreamConf.get(nginxConf.UK_WAF_STRATEGY)

        waf_syntax_info = update_waf_syntax_default_strategy(upstreamConf)

        # upgrade bruteforce from 22.09 to 22.11
        waf_brute_force_info = upstreamConf.get('waf_brute_force_info', {})
        for limit in waf_brute_force_info.get('path_limit', []):
            if len(limit) == 3:
                limit['user_param'] = ''
                limit['pwd_param'] = ''

        if not ignore_compare:
            WafStrategyCtrl().compare_strategy_info(upstreamConf)

        # compare waf startegy info of imported 21.07 site config, del waf_strategy_info if waf_strategy_info equal
        # waf strategy info of confdb according to waf_strategy
        if waf_strategy and waf_strategy_info:
            confWaf = WafConf()
            if confWaf.compare_site_strategy_info(waf_strategy, waf_strategy_info) and confWaf.compare_site_syntax_info(waf_strategy, waf_syntax_info):
                del upstreamConf['waf_strategy_info']
                del upstreamConf['waf_syntax_info']


        # uprade from v21.03 or upward  to v21.07
        if not waf_strategy:
            waf_custom_set_template = upstreamConf.get(nginxConf.UK_WAF_CUSTOM_SET_TEMPLATE)
            waf_enabled_modules = upstreamConf.get('WafEnabledModules', {})
            rule_set = upstreamConf.get(nginxConf.UK_RULE_SET)

            if rule_set != "customSet":
                waf_strategy = waf_util.get_default_site_strategy_id_from_rule_set(rule_set)
                upstreamConf['waf_strategy'] = waf_strategy
            else:
                waf_strategy_info = {}
                default_module_strategies = waf_util.get_waf_default_module_strategies()
                waf_enabled_modules.update({module_name: False for module_name in include_file_mapping().keys() if
                                            module_name not in waf_enabled_modules.keys()})
                waf_add_disabled_set = upstreamConf.get(nginxConf.UK_WAF_ADD_DISABLED_SET, '')
                waf_del_disabled_set = upstreamConf.get(nginxConf.UK_WAF_DEL_DISABLED_SET, '')

                waf_rule_module_config = upstreamConf.get('waf_rule_module_config', {})
                default_waf_rule_module_config = get_default_waf_module_config()
                for k, v in default_waf_rule_module_config.items():
                    waf_rule_module_config.setdefault(k, v)

                strategy_name = _('custom strategy_{}').format(random.randint(1000, 9999))
                old_action_dict = {"pass": 1, "replace": 3}
                for module, enabled in waf_enabled_modules.items():
                    waf_change_rules = {}
                    for item in waf_add_disabled_set.split(','):
                        if re.match(default_module_strategies.get(module).get('rule_pre'), item):
                            waf_change_rules[item] = {'enabled': 0, 'action': 2}
                    for item in waf_del_disabled_set.split(','):
                        if re.match(default_module_strategies.get(module).get('rule_pre'), item):
                            waf_change_rules[item] = {'enabled': 1, 'action': 2}
                    strategy_id = waf_util.get_default_site_strategy_id_from_rule_set(waf_custom_set_template)
                    module_info = {}
                    module_info['enabled'] = enabled
                    module_info['template'] = strategy_id if waf_change_rules else ''
                    module_info['default_strategy_id'] = '' if waf_change_rules else strategy_id
                    module_info['waf_change_rules'] = waf_change_rules
                    module_info['name'] = strategy_name if waf_change_rules else ''
                    module_info['comment'] = _('Auto Added')
                    if 'sensitive_info_filter_interception' in module:
                        special_config = waf_rule_module_config.get("sensitive_info_filter_interception")
                        action = special_config["action"]
                        special_config["action"] = old_action_dict.get(action, action)
                        module_info['special_config'] = special_config
                    waf_strategy_info[module] = module_info
                upstreamConf['waf_strategy_info'] = waf_strategy_info

        enable_tamper = upstreamConf.get('enable_tamper')
        if enable_tamper is None:
            upstreamConf['enable_tamper'] = WafConf.DEFAULT_ENABLE_TAMPER
            upstreamConf['tamper_diskspace'] = WafConf.DEFAULT_TAMPER_DISKSPACE
            upstreamConf['tamper_auto_update'] = WafConf.DEFAULT_TAMPER_AUTO_UPDATE
            upstreamConf['tamper_web_similarity'] = WafConf.DEFAULT_TAMPER_WEB_SIMILARITY
            upstreamConf['tamper_expire_time'] = WafConf.DEFAULT_TAMPER_EXPIRE_TIME
            upstreamConf['tamper_cache_urls'] = WafConf.DEFAULT_TAMPER_CACHE_URLS

    except Exception as ex:
        logger.error('update_waf error ： {}'.format(ex))
    else:
        logger.info('update_waf successfully')


@login_required
@require_http_methods(["POST"])
@check_permission_list(['Edit_Website_Config', 'Enable_Site_Conf'], 'write')
def one_click_switch(request):
    data = OneClickSwitch.validate_one_click_switch(request.body)
    if data is None:
        operation_log(request,
                      ugettext_noop('Proxy'),
                      ugettext_noop('OneClickSwitch'), '1',
                      detail = {
                          'msg': ugettext_noop('Invalid post body'),
                    })
        return JsonResponse({'status': 'failed', 'message': _('Invalid post body')})

    nginx_conf = NginxConf()
    action = data['action']
    sites = data['sites']
    action_conf = OneClickSwitch.get_action_conf(action)
    values = []

    upstreams = nginx_conf.get_conf().get_all('nginx/upstreams/', {})
    for server_key in sites:
        if server_key not in upstreams:
            operation_log(request, ugettext_noop('Proxy'), ugettext_noop('OneClickSwitch'), '1', detail = { 'msg': ugettext_noop('Invalid post body')})
            return JsonResponse({'status': 'failed', 'message': _('Invalid post body')})

        for ac in action_conf:
            values.append(('nginx/upstreams/{}/{}'.format(server_key, ac[0]), ac[1]))

    nginx_conf.set_asp_conf_values(values)
    operation_log(request,
                  ugettext_noop('Proxy'),
                  ugettext_noop('OneClickSwitch'), '0',
                  detail = {
                      'msg': ugettext_noop('One Click Switch to {action}'),
                      'extra': {'action': _(action.capitalize())},
                      'info': sites
                  })
    return JsonResponse({"status": "success", "message": ""})


