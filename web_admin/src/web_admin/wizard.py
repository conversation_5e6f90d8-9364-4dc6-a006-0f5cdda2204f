# -*- coding:utf-8 -*-
from __builtin__ import True
import inspect
import logging
import os
import subprocess
import platform

from django.contrib.auth.models import User
from django.contrib.auth.hashers import make_password
from django.http.response import HttpResponseRedirect, HttpResponse
from django.utils.translation import ugettext as _
from django.utils.translation import ugettext_noop, activate
from views import base_render_to_response

from Conf_Webconsole import WebconsoleConf
from module_ctrls.network.network import get_current_ip_config, usable_device_names
from operation_log import get_client_ip
from service_mgr_rest_api import join_cluster, pre_join_cluster_internal
from asp_conf_ctrl import ConfDb
import json
from views import base_render_to_response, CONTENT_TYPE_JSON, CONTENT_TYPE_JS
from view_lib import get_current_build_info, get_current_is_debug, get_current_is_prod_pkg, get_current_has_phoenix_pkg
from asp_utils.utils import RESULT, get_release_file, ip_to_int, mask_to_int, get_product_type, exe_with_output, in_container, current_machine_hardware_name, is_product_category_NGWAF, not_ubuntu, os_id
from asp_utils.license_info import get_please_contact_us
from asp_utils.CommonLibs import compress_IP, is_same_subnet
from web_admin.Conf_Network import NetworkConf
from web_admin.utils_route import check_route_uniqueness, check_route_parameters, flat_network_if_cfg
from asp_utils.bond_util import get_all_nic_info, identify_nic


logger = logging.getLogger('wizard')


def is_wizard_security_check_pass(request):
    conf = WebconsoleConf()
    if conf.is_wizard_finished():
        return False

    return True


def redirect_to_login(request):
    return HttpResponseRedirect('/user/login/?next=' + request.path)


def redirect_to_root(request):
    conf = ConfDb()
    servers = conf.get_master_servers()
    assert(servers)
    cluster_ip = servers.values()[0].get('admin_ip')
    return HttpResponseRedirect('https://{0}:{1}/'.format(cluster_ip, conf.get_port_web_console_https()))


def wizard_security_check(view_func):
    """
    Make another a function more beautiful.
    """
    def _decorated(*args, **kwargs):
        request = args[0]
        if not is_wizard_security_check_pass(request):
            if view_func.__name__.startswith('ajax_'):
                msg1 = _('Exceptions might occur during deployment. ')
                msg2 = _('Please type https://IP of network adapter eth0:20145 enter the WebConsole and then select [System - Advanced Settings - Factory Reset] to go through the Wizard again.')
                msg = msg1 + msg2
                return render_json_response(RESULT.BAD_COMMAND, msg)
            return redirect_to_root(request)

        return view_func(*args, **kwargs)
    return _decorated


def wizard_render_to_response(request, template, dictionary=None):
    if dictionary is None:
        dictionary = {}
    conf = WebconsoleConf()

    d = dict(dictionary)
    if conf.is_wizard_finished() is True:
        d['is_wizard_finished'] = 'true'
    else:
        d['is_wizard_finished'] = 'false'

    dm_dir = get_release_file('data_miner_lite')
    if os.path.exists(dm_dir):
        d['with_dm'] = 1
    else:
        d['with_dm'] = 0

    print "==> " + template
    return base_render_to_response(request, template, d)


def print_current_function_name():
    print "==> " + inspect.stack()[1][3]


@wizard_security_check
def wizard_home(request):
    '''
    This interface is working for wizard request
    :param request:
    :return:
    '''

    # Ensure wizard finish redirect to login
    request.session.flush()

    lang_from_url_args = 'en' if request.GET.get('lang') == 'en' else 'zh'
    activate(lang_from_url_args)

    single_cfg = build_adapters_cfg_dict('single')
    load_values_to_wizard(single_cfg, ugettext_noop('admin'))
    load_values_to_wizard(single_cfg, ugettext_noop('internal'))
    load_values_to_wizard(single_cfg, ugettext_noop('external'))
    load_values_to_wizard(single_cfg, 'log')
    load_values_to_wizard(single_cfg, ugettext_noop('keepalived'))

    dual_admin_proxy_cfg = build_adapters_cfg_dict('dual_admin_proxy')
    load_values_to_wizard(dual_admin_proxy_cfg, 'admin')
    load_values_to_wizard(dual_admin_proxy_cfg, 'internal')
    load_values_to_wizard(dual_admin_proxy_cfg, 'external')
    load_values_to_wizard(dual_admin_proxy_cfg, 'log')
    load_values_to_wizard(dual_admin_proxy_cfg, 'keepalived')

    three_cfg = build_adapters_cfg_dict('three')
    load_values_to_wizard(three_cfg, 'admin')
    load_values_to_wizard(three_cfg, 'internal')
    load_values_to_wizard(three_cfg, 'external')
    load_values_to_wizard(three_cfg, 'log')
    load_values_to_wizard(three_cfg, 'keepalived')

    build_info = get_current_build_info()
    cloud_mode = ConfDb().is_cloud_mode()
    adapter_info = usable_device_names(5)

    machine_hardware_name = current_machine_hardware_name()
    can_install_clickhouse = True
    if machine_hardware_name == 'x86_64':
        try:
            can_install_clickhouse = False
            if os.system('/etc/asp/base_pkg/bin/check_sse4_2') == 0:
                can_install_clickhouse = True
        except:
            pass

    system_route = get_system_route()
    default_gateway_ipv4, default_gateway_ipv6 = get_system_default_gateway()

    if os_id() == 'uos':
        plt_info = ' (uos)'
    elif os_id() == 'kylin':
        plt_info = ' (kylin)'
    else:
        plt_info = ''

    if platform.processor() == 'aarch64':
        plt_info += ' (arm)'


    return wizard_render_to_response(request, 'wizard/wizard.html',
                                     {'build_info': build_info,
                                      'single_cfg': json.dumps(single_cfg),
                                      'dual_admin_proxy_cfg': json.dumps(dual_admin_proxy_cfg),
                                      'three_cfg': json.dumps(three_cfg),
                                      'cloud_mode': cloud_mode,
                                      'in_container': in_container(),
                                      'is_build_debug': get_current_is_debug(),
                                      'is_prod_pkg': get_current_is_prod_pkg(),
                                      'has_phoenix_pkg': get_current_has_phoenix_pkg(),
                                      'plt_info': plt_info,
                                      'can_install_clickhouse': can_install_clickhouse,
                                      'machine_hardware_name': machine_hardware_name,
                                      'kernel_ok': os.path.isdir('/'.join(('/etc/asp/release/transparent_mode', platform.processor(), platform.release()))),
                                      'has_support_kernel': machine_hardware_name == 'x86_64' and os.path.exists('/boot/vmlinuz-4.4.0-141-generic'),
                                      'adapter_info': adapter_info,
                                      'system_route': json.dumps(system_route),
                                      'default_gateway': default_gateway_ipv4,
                                      'default_gateway_ipv6': default_gateway_ipv6,
                                      'lang_from_url_args': lang_from_url_args,
                                      'nic_info': get_all_nic_info()
                                      })


def load_values(mytree, request, intf, from_request=False):
    ipVersion = request.POST.get(intf + '_ipVersion')
    ip = request.POST.get(intf + '_ip')
    ipv6 = request.POST.get(intf + '_ipv6')
    netmask = request.POST.get(intf + '_netmask', '')
    prefix = request.POST.get(intf + '_prefix', '')
    hostname = request.POST.get(intf + '_hostname', '')
    default_gateway_IPv4 = request.POST.get('default_gateway', '')
    default_gateway_IPv6 = request.POST.get('external_ipv6_gateway', '')

    logger.info("use hostname: {}".format(hostname))

    if hostname:
        mytree[intf]["hostname"] = hostname

    if mytree[intf]['adapter_name'] != 'Unknown' and not from_request and not ip and not ipv6:
        load_values_from_os(mytree, intf)
    else:
        mytree[intf]['dual_ip']      = None
        mytree[intf]['dual_netmask'] = None
        if not mytree[intf].get('shared_with'):
            mytree[intf]['gateway'] = None
            mytree[intf]['dual_gateway'] = None

        if ipVersion == 'ipv4':
            mytree[intf]['ip'] = compress_IP(ip)
            mytree[intf]['netmask'] = compress_IP(netmask)
            if 'gateway' in mytree[intf].keys():
                mytree[intf]['gateway'] = compress_IP(default_gateway_IPv4) if is_same_subnet(ip, default_gateway_IPv4, netmask) else None

        elif ipVersion == 'ipv6':
            mytree[intf]['ip'] = compress_IP(ipv6)
            mytree[intf]['netmask'] = compress_IP(prefix)
            if 'gateway' in mytree[intf].keys():
                mytree[intf]['gateway'] = compress_IP(default_gateway_IPv6) if is_same_subnet(ipv6, default_gateway_IPv6, prefix) else None

        elif ipVersion == 'ipv46':
            mytree[intf]['ip'] = compress_IP(ip)
            mytree[intf]['netmask'] = compress_IP(netmask)
            if 'gateway' in mytree[intf].keys():
                mytree[intf]['gateway'] = compress_IP(default_gateway_IPv4) if is_same_subnet(ip, default_gateway_IPv4, netmask) else None

            mytree[intf]['dual_ip'] = compress_IP(ipv6)
            mytree[intf]['dual_netmask'] = compress_IP(request.POST.get(intf + '_prefix', ''))
            if 'dual_gateway' in mytree[intf].keys():
                mytree[intf]['dual_gateway'] = compress_IP(default_gateway_IPv6) if is_same_subnet(ipv6, default_gateway_IPv6, prefix) else None

        else:
            mytree[intf]['ip'] = compress_IP(ip)
            mytree[intf]['netmask'] = compress_IP(netmask)
            if 'gateway' in mytree[intf].keys():
                mytree[intf]['gateway'] = compress_IP(default_gateway_IPv4) if is_same_subnet(ip, default_gateway_IPv4, netmask) else None


def build_adapters_cfg_dict(proxy_mode):
    '''
    Build configuration dictionary based on the @proxy_mode.

    eth0 is used for admin by default
    eth3 is used for log or keepalived
    '''
    ETH0, ETH1, ETH2, ETH3 = usable_device_names(4)
    logging.info("Got usable adapters: {}".format(json.dumps([ETH0, ETH1, ETH2, ETH3])))

    # 4 adapters configuration
    cfg = {
        'role': [],

        'keepalived': {
            'adapter_name': ETH3,
            'shared_with': 'external',
            'ip': '',
            'netmask': '',
        },

        'log': {
            'adapter_name': ETH3,
            'shared_with': 'admin',
            'ip': '',
            'netmask': '',
        },

        'internal': {
            'adapter_name': ETH2,
            'shared_with': '',
            'ip': '',
            'netmask': ''
        },

        'external': {
            'adapter_name': ETH1,
            'shared_with': '',
            'ip': '',
            'netmask': ''
        },

        'admin':    {
            'adapter_name': ETH0,
            'shared_with': '',
            'ip': '',
            'netmask': '',
        },
    }

    if proxy_mode == 'dual_admin_proxy':
        cfg['internal']['shared_with'] = 'external'
        cfg['internal']['adapter_name'] = ETH1
    elif proxy_mode == 'three':
        pass
    else:
        # Single interface mode by default
        cfg['external']['adapter_name'] = ETH0
        cfg['internal']['adapter_name'] = ETH0
        cfg['admin']['adapter_name'] = ETH0
        cfg['internal']['shared_with'] = 'external'
        cfg['admin']['shared_with'] = 'external'

    return cfg


def load_values_from_os(config, ifname):
    intf = config[ifname]

    ifname = intf['adapter_name']

    (ip, mask, gw), (dual_ip, dual_mask, dual_gw) = get_current_ip_config(ifname)

    if ip != None:
        intf['ip'] = ip
        intf['netmask'] = mask
    if gw != None:
        intf['gateway'] = gw

    if dual_ip != None:
        intf['dual_ip'] = dual_ip
        intf['dual_netmask'] = dual_mask
    if dual_gw != None:
        intf['dual_gateway'] = dual_gw


def load_values_to_wizard(config, ifname):
    intf = config[ifname]

    ifname = intf['adapter_name']

    (ip4, mask4, gw4), (ip6, mask6, gw6) = get_current_ip_config(ifname)
    intf['ip'] = ip4
    intf['netmask'] = mask4
    if gw4 is not None:
        intf['gateway'] = gw4

    intf['ipv6'] = ip6
    intf['prefix'] = mask6
    if gw6 is not None:
        intf['ipv6_gateway'] = gw6

    if ip4 and ip6:
        intf['ipVersion'] = 'ipv46'
    elif ip6:
        intf['ipVersion'] = 'ipv6'
    else:
        intf['ipVersion'] = 'ipv4'


def save_password(passwd):
    passwd = str(passwd)
    user = User.objects.get(username='admin')
    user.set_password(passwd)
    user.save()
    return None


def wizard_input_to_asp_conf_values(config, is_create_cluster):
    result = {}

    for intf in ADAPTERS:
        flat_network_if_cfg(result, '_private/os/network/{}'.format(intf), config[intf])

        shared_route_path = 'os/network/{}/route_list'.format(intf)
        for key, value in config["shared_route"][intf].items():
            flat_network_if_cfg(result, os.path.join(shared_route_path, key) , value)

        private_route_path = '_private/os/network/{}/route_list'.format(intf)
        for k, v in config["private_route"][intf].items():
            flat_network_if_cfg(result, os.path.join(private_route_path, k), v)

    result['_private/cluster/role'] = config['role']

    return result


def clone_shared_config(config, intf):
    origId = config[intf]
    shareName = origId['shared_with']
    if shareName != '':
        shareId = config[shareName]
        origId['ip'] = shareId['ip']
        origId['netmask'] = shareId['netmask']
        if intf == 'internal' or intf == 'external':
            if shareId.has_key('dual_ip'):
                origId['dual_ip'] = shareId['dual_ip']
            if shareId.has_key('dual_netmask'):
                origId['dual_netmask'] = shareId['dual_netmask']
        origId['adapter_name'] = shareId['adapter_name']


def get_inner(kv, key):
    if type(kv) is not dict:
        return kv
    return get_inner(kv.get(key), key)


def get_err_msg(msg):
    msgs = msg.split(":")
    # for passing the license allowed max nodes num in msg string : "Reached the ceiling:max_nodes:5"
    reach_ceil = "Reached the ceiling"
    max_nodes = None
    if reach_ceil in msg:
        max_nodes = msgs[2]
        msg = reach_ceil

    err_msg = {"[Errno 113] No route to host": _("[Errno 113] No route to host"),
               "[Errno 111] Connection refused": _("[Errno 111] Connection refused"),
               "Password mismatch!": _("Passwords do not match!"),
               reach_ceil: _("The number of nodes in this cluster has exceeded the upper limit ({0}).").format(max_nodes),
               "Exceed cpu or mem": _("The number of CPU cores or memory size has exceeded the upper limit.") + get_please_contact_us(),
               }
    result = err_msg.get(msg)
    return result or _("Unknown error")


import re


def parse_err_msg(msg):
    result = re.findall(r'\s+response: ([^\}\{]+)', msg)
    return 'Unknown error' if len(result) == 0 else result[len(result) - 1]


def finished(request):
    conf = ConfDb()
    result = {
        'port': conf.get_port_web_console_https()
    }

    callback = request.GET.get('callback')
    text = '''(function() {{ var data = {0}; \n{1}(data); }})();'''.format(result, callback)
    return HttpResponse(text, content_type=CONTENT_TYPE_JS)


def render_json_response(result, message, data=None):
    response = {'result': result,
                'message': message,
                }
    if data is not None:
        response['data'] = data

    return HttpResponse(json.dumps(response), content_type=CONTENT_TYPE_JSON)


ADAPTERS = ['admin', 'external', 'internal', 'log', 'keepalived']


def check_network_ip_cfg(ip, netmask, gateway):
    ip_int, ip_ver = ip_to_int(ip)
    if ip_ver not in [4, 6]:
        return 'INVALID_IP'

    mask_int, mask_ver = mask_to_int(netmask)
    if mask_ver not in [4, 6] :
        return 'INVALID_IP'

    if ip_ver != mask_ver:
        return 'NOT_MATCH_IPVERSION'

    if gateway:
        gateway_int, gateway_ver = ip_to_int(gateway)
        if gateway_ver not in [4, 6]:
            return 'INVALID_IP'

        if ip_ver != gateway_ver:
            return 'NOT_MATCH_IPVERSION'

        if ip == gateway:
            return 'CONFLICT_IP'


def get_invalid_adapter(adapter, ip, netmask, gateway):
    ret = {'adapter_name': adapter.get('adapter_name')}

    if ':' in ip:
        ret['ipv6'] = ip
        ret['prefix'] = netmask
        if gateway:
            ret['ipv6_gateway'] = gateway
    else:
        ret['ip'] = ip
        ret['netmask'] = netmask
        if gateway:
            ret['gateway'] = gateway

    return ret


def check_ip_conflict(ip1, mask1, ip2, mask2):
    if not ip1 or not ip2 or not mask1 or not mask2:
        return False

    if ip1 == ip2:
        return True
    if mask1 == mask2 and is_same_subnet(ip1, ip2, mask1):
        return True

    return False


def check_adapter_parameters(cfg, request, check_split_cfg=False):
    havecheck_adapter = {}
    gateway_list = []
    dual_gateway_list = []
    is_single_eth = cfg['admin'].get("ip") == cfg['external'].get("ip")
    is_IPv6 = False

    for name in ADAPTERS:
        adapter = cfg[name]
        if (name == 'log' or name == 'keepalived') and not check_split_cfg:
            continue

        if not adapter.get('netmask') or not adapter.get('ip') or not adapter.get('adapter_name'):
            return _('Invalid adapter settings for:{0}: {1}.').format(name, json.dumps(adapter))

        ip = adapter.get('ip')
        netmask = adapter.get('netmask')
        gateway = adapter.get('gateway')
        if ":" in ip:
            is_IPv6 = True
        if gateway:
            gateway_list.append(gateway)
        check = check_network_ip_cfg(ip, netmask, gateway)
        if check:
            return _('Network config is incorrect:{0}: {1}.').format(name, json.dumps(get_invalid_adapter(adapter,ip,netmask,gateway)))

        dual_ip = adapter.get('dual_ip')
        dual_netmask = adapter.get('dual_netmask')
        dual_gateway = adapter.get('dual_gateway')
        if dual_gateway:
            dual_gateway_list.append(dual_gateway)
        if dual_ip:
            is_IPv6 = True
            if not dual_netmask:
                return _('Invalid adapter settings for:{0}: {1}.').format(name, json.dumps(get_invalid_adapter(adapter,dual_ip,dual_netmask,dual_gateway)))

            check = check_network_ip_cfg(dual_ip, dual_netmask, dual_gateway)
            if check:
                return _('Network config is incorrect:{0}: {1}.').format(name, json.dumps(get_invalid_adapter(adapter,dual_ip,dual_netmask,dual_gateway)))

        adapter_name = adapter['adapter_name']
        if adapter_name not in havecheck_adapter.keys():
            for check_adapter_name, check_adapter in havecheck_adapter.items():
                if check_ip_conflict(ip, netmask, check_adapter['adapter'].get('ip'), check_adapter['adapter'].get('netmask')):
                    return _('IP address conflict with {0} port:{1}: {2}.').format(_(check_adapter['name']), name, json.dumps(
                        get_invalid_adapter(adapter, ip, netmask, gateway)))
                if check_ip_conflict(dual_ip, dual_netmask, check_adapter['adapter'].get('dual_ip'), check_adapter['adapter'].get('dual_netmask')):
                    return _('IP address conflict with {0} port:{1}: {2}.').format(_(check_adapter['name']), name, json.dumps(
                            get_invalid_adapter(adapter, dual_ip, dual_netmask, dual_gateway)))
                if check_ip_conflict(ip, netmask, check_adapter['adapter'].get('dual_ip'), check_adapter['adapter'].get('dual_netmask')):
                    return _('IP address conflict with {0} port:{1}: {2}.').format(_(check_adapter['name']), name, json.dumps(
                            get_invalid_adapter(adapter, ip, netmask, gateway)))
                if check_ip_conflict(dual_ip, dual_netmask, check_adapter['adapter'].get('ip'), check_adapter['adapter'].get('netmask')):
                    return _('IP address conflict with {0} port:{1}: {2}.').format(_(check_adapter['name']), name, json.dumps(
                            get_invalid_adapter(adapter, dual_ip, dual_netmask, dual_gateway)))


            havecheck_adapter[adapter_name] = {'name':name, 'adapter':adapter}

    # check is gateway matched IP
    ipv4_gateway = request.POST.get('default_gateway')
    ipv6_gateway = request.POST.get('external_ipv6_gateway')

    # IPv4
    if not gateway_list or compress_IP(ipv4_gateway) not in gateway_list:
        if is_single_eth:
            return _('IP address and gateway are not in the same network segment:{0}: {1}.').format('external', json.dumps(
                get_invalid_adapter(cfg['external'], cfg['external'].get('ip'), cfg['external'].get('netmask'), ipv4_gateway)))
        else:
            return "{0}::{1}".format(_('Incorrect gateway'), ipv4_gateway)
    # IPv6
    if is_IPv6:
        if compress_IP(ipv6_gateway) in dual_gateway_list or compress_IP(ipv6_gateway) in gateway_list:
            return ''
        else:
            if is_single_eth:
                err_eth = get_invalid_adapter(cfg['external'], cfg['external'].get('dual_ip'), cfg['external'].get('dual_netmask'), ipv6_gateway)
                return _('IP address and gateway are not in the same network segment:{0}: {1}.').format('external', json.dumps(err_eth))
            else:
                return "{0}::{1}".format(_('Incorrect gateway'), ipv6_gateway)

    return ''


@wizard_security_check
def ajax_join_or_create_cluster(request):

    if request.method != "POST":
        return render_json_response(RESULT.BAD_COMMAND, 'Use POST instead')

    language = 'en' if request.POST.get('language') == 'en' else 'zh'
    activate(language)

    if request.POST.get('logserver_module') == 'true' and request.POST.get('sailfish_module') == 'true':
        render_json_response(RESULT.VALIDATE_FAILED, _('Please do not select roles both big data analysis P and big data analysis S!'))

    if request.POST.get("modelCategory") == "mirror" and (get_product_type() not in ['ApiBotDefender','ApiSecurityAudit','Safeplus']) and not is_product_category_NGWAF():
        return render_json_response(RESULT.MODEL_CATEGORY_NOT_MATCH, _('Inconsistent deployment model'))

    print_current_function_name()

    #cookies = getCookies(request)

    config = build_adapters_cfg_dict(request.POST.get('proxy_mode'))

    if request.POST.get('log_split_enable') == 'true':
        config['log']['shared_with'] = ''

    if request.POST.get('keepalived_split_enable') == 'true':
        config['keepalived']['shared_with'] = ''

    # set role list
    if request.POST.get('logserver_module') == 'true':
        config['role'].append('log_server')
    if request.POST.get('log_archive_server_module') == 'true':
        config['role'].append('log_archive_server')
    if request.POST.get('proxy_module') == 'true':
        config['role'].append('proxy')
    if request.POST.get('masterserver_module') == 'true':
        config['role'].append('master_server')
    if request.POST.get('dataminer_module') == 'true':
        config['role'].append('dataminer_server')
    if request.POST.get('bta_module') == 'true':
        config['role'].append('bta_server')
    if request.POST.get('repu_module') == 'true':
        config['role'].append('repu_server')
    if request.POST.get('ai_module') == 'true':
        config['role'].append('ai_service')
    if request.POST.get('llm_module') == 'true':
        config['role'].append('llm_server')
    if request.POST.get('fsl_module') == 'true':
        if 'proxy' in config['role']:
            config['role'].append('flowlearn_server')
    if request.POST.get('abd_module') == 'true':
        config['role'].append('abd_server')
    if request.POST.get('api_gateway_module') == 'true':
        config['role'].append('api_gateway')
    if request.POST.get('sailfish_module') == 'true':
        config['role'].append('sailfish')

    network_from_request = request.POST.get("network_readonly") != "true"
    load_values(config, request, 'admin', from_request=network_from_request)
    load_values(config, request, 'internal', from_request=network_from_request)
    load_values(config, request, 'external', from_request=network_from_request)
    load_values(config, request, 'log', from_request=network_from_request)
    load_values(config, request, 'keepalived', from_request=network_from_request)

    get_optimal_gateway(config)

    clone_shared_config(config, 'internal')
    clone_shared_config(config, 'external')
    clone_shared_config(config, 'admin')
    clone_shared_config(config, 'log')
    clone_shared_config(config, 'keepalived')

    check_split_cfg = request.POST.get('splitNetworkPort')
    if not check_split_cfg:
        check_split_cfg = False

    # Verify input parameters
    error = check_adapter_parameters(config, request, check_split_cfg)
    if error:
        render_json_response(RESULT.INVALID_PARAMS, error)

    is_create_cluster = request.POST.get('cluster_choice') == 'cluster_create'
    cluster_password = request.POST.get('cluster_password')
    if not cluster_password:
        return render_json_response(RESULT.INVALID_PARAMS, _('Password is required.'))

    is_single_mode = request.POST.get('proxy_mode') == 'single'
    if is_single_mode:
        if is_create_cluster:
            # single mode shouldn't have route_list in cluster create
            route_list = []
        else:
            # just synchronize shared route, no need to check
            route_list = list(filter(lambda x: not x.get('private'), json.loads(request.POST.get('route_list', '[]'))))
    else:
        route_list = json.loads(request.POST.get('route_list', '[]'))

        # check route parameters
        if route_list:
            ret = check_route_params(route_list)
            if ret:
                return ret
        else:
            route_list = []

    ret = load_route(config, route_list, is_single_mode)
    if ret:
        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    if is_create_cluster and 'master_server' not in config['role']:
        config['role'].append('master_server')

    cluster_ip = request.POST.get('cluster_ip')
    logger.info('Join cluster: {0}'.format(cluster_ip))

    config_values = wizard_input_to_asp_conf_values(config, is_create_cluster)
    config_values['_private/node_id'] = ConfDb().get_value('_private/node_id')

    config_values['modelCategory'] = request.POST.get('modelCategory')

    config_values['nginx/transparent_mode/enable'] = config_values['modelCategory'] == 'transparent'
    bond_mode = request.POST.get('bond_mode')
    if bond_mode in ('0', '4'):
        config_values['_private/os/network/bond/mode'] = int(bond_mode)
    else:
        config_values['_private/os/network/bond/mode'] = -1

    if config_values['modelCategory'] == 'single':
        config_values['modelCategory'] = 'group'
    config_values['_private/node_role'] = request.POST.get('role')

    if config_values['modelCategory'] == 'mirror':
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_MIRROR
    elif config_values['modelCategory'] == 'transparent':
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_TRANSPARENT
        config_values['nginx/stream_forward'] = True
        config_values['nginx/stream_forward_whitelist'] = [
            {"method" : "CONNECT"},
            {"method" : "RPC_IN_DATA"},
            {"method" : "RPC_OUT_DATA"}
        ]
    elif config_values['modelCategory'] == 'hotStandby':
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_HA
    elif config_values['modelCategory'] == 'routeProxy':
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_ROUTEPROXY
    elif config_values['modelCategory'] == 'plugin':
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_PLUGIN
    elif config_values['modelCategory'] == 'HARouteProxy':
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_HA_ROUTEPROXY
    else:
        config_values['nginx/deploy_mode'] = ConfDb.DEPLOY_MODE_INLINE

    if 'proxy' in config['role']:
        config_values['_private/captcha_server/enabled'] = 1

    if not_ubuntu() and 'log_archive_server' in config['role']:
        if os.path.exists('/opt/greatdb.tar.xz'):
            config_values['_private/greatdb/enabled'] = 1
            config_values['_private/greatsql/enabled'] = 0
            config_values['_private/gsql_log_sender/enabled'] = 1
        elif os.path.exists('/opt/greatsql-mini.tar.xz'):
            config_values['_private/greatdb/enabled'] = 0
            config_values['_private/greatsql/enabled'] = 1
            config_values['_private/gsql_log_sender/enabled'] = 1

    if request.POST.get('modelCategory') in ('hotStandby', 'HARouteProxy') and is_create_cluster:
        #check VIP params
        ret = check_VIP_params(request, config)
        if ret:
           return ret

        #hotStandby and HARouteProxy
        set_VIP_config(request,config_values)

    if is_create_cluster:
        config_values['os/network/ntp_server'] = ''

    # config bond if needed
    if request.POST.get('is_nic_bond', 'false') == 'true':
        bond_cfg_str = json.dumps({
            request.POST.get('bond_interface'): {
                'mode': str(request.POST.get('nic_bond_mode')),
                'members': [
                    request.POST.get('bond_nic_1'),
                    request.POST.get('bond_nic_2')
                ]
            }
        })
        config_values['_private/bond_cfg_str'] = bond_cfg_str

    config_values['language'] = language
    try:

        ret = join_cluster(config_values, cluster_ip,
                           cluster_password, make_password(cluster_password), is_create_cluster,
                           request.user.username, get_client_ip(request))
        save_password(cluster_password)
        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
    except Exception as e:
        logger.exception('join_cluster with exception.')
        s = parse_err_msg(e.message)
        i18n_error = _('Failed to join cluster: {0}').format(get_err_msg(s))
        return render_json_response(RESULT.EXCEPTION, i18n_error)


@wizard_security_check
def ajax_pre_cluster_join(request):
    if request.method != "POST":
        return render_json_response(RESULT.BAD_COMMAND, 'Use POST instead')

    try:
        language = request.POST.get('language')
        if language not in ('zh', 'en'):
            return render_json_response(RESULT.EXCEPTION, 'Invalid language')
        activate(language)

        cluster_password = request.POST.get('cluster_password')
        if not cluster_password:
            return render_json_response(RESULT.INVALID_PARAMS, _('Password is required.'))

        cluster_ip = request.POST.get('cluster_ip')
        if not cluster_ip:
            return render_json_response(RESULT.INVALID_PARAMS, _('IP is required.'))

        config_values = {}

        config_values['language'] = language
        config_values['modelCategory'] = request.POST.get('modelCategory')
        if config_values['modelCategory'] == 'single':
            config_values['modelCategory'] = 'group'

        ret = pre_join_cluster_internal(False, config_values, cluster_ip, cluster_password, make_password(cluster_password),
                                        request.user.username, get_client_ip(request), usable_device_names(5))

        if ret.get('exception'):
            ret = {'result': RESULT.CANNOT_CONNECT_TO_SERVER, 'message': _('Cannot connect to master node.')}

        return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
    except Exception as e:
        logger.exception('pre_join_cluster_internal with exception.')
        s = parse_err_msg(e.message)
        i18n_error = _('Failed to join cluster: {0}').format(get_err_msg(s))
        return render_json_response(RESULT.EXCEPTION, i18n_error)


@wizard_security_check
def ajax_check_adapter_cfg(request):
    if request.method != "POST":
        return render_json_response(RESULT.BAD_COMMAND, 'Use POST instead')

    language = 'en' if request.POST.get('language') == 'en' else 'zh'
    activate(language)

    config = build_adapters_cfg_dict(request.POST.get('proxy_mode'))

    if request.POST.get('log_split_enable') == 'true':
        config['log']['shared_with'] = ''

    if request.POST.get('keepalived_split_enable') == 'true':
        config['keepalived']['shared_with'] = ''

    # set role list
    if request.POST.get('logserver_module') == 'true':
        config['role'].append('log_server')
    if request.POST.get('log_archive_server_module') == 'true':
        config['role'].append('log_archive_server')
    if request.POST.get('proxy_module') == 'true':
        config['role'].append('proxy')
    if request.POST.get('masterserver_module') == 'true':
        config['role'].append('master_server')
    if request.POST.get('dataminer_module') == 'true':
        config['role'].append('dataminer_server')

    check_split_cfg = request.POST.get('splitNetworkPort')
    if not check_split_cfg:
        check_split_cfg = False

    load_values(config, request, 'admin', from_request=True)
    load_values(config, request, 'internal', from_request=True)
    load_values(config, request, 'external', from_request=True)
    load_values(config, request, 'log', from_request=True)
    load_values(config, request, 'keepalived', from_request=True)

    get_optimal_gateway(config)

    clone_shared_config(config, 'internal')
    clone_shared_config(config, 'external')
    clone_shared_config(config, 'admin')
    clone_shared_config(config, 'log')
    clone_shared_config(config, 'keepalived')

    # Verify input parameters
    error = check_adapter_parameters(config, request, check_split_cfg)
    if error:
        return render_json_response(RESULT.INVALID_PARAMS, error)

    is_create_cluster = request.POST.get('cluster_choice') == 'cluster_create'

    is_single_mode = request.POST.get('proxy_mode') == 'single'
    if is_single_mode:
        if is_create_cluster:
            # single mode shouldn't have route_list in cluster create
            route_list = []
        else:
            # just synchronize shared route, no need to check
            route_list = list(filter(lambda x: not x.get('private'), json.loads(request.POST.get('route_list', '[]'))))
    else:
        route_list = json.loads(request.POST.get('route_list', '[]'))

        # check route parameters
        if route_list:
            ret = check_route_params(route_list)
            if ret:
                return ret
        else:
            route_list = []

    ret = load_route(config, route_list, is_single_mode)
    if ret:
        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    is_create_cluster = request.POST.get('cluster_choice') == 'cluster_create'
    cluster_ip = request.POST.get('cluster_ip')
    if is_create_cluster and 'master_server' not in config['role']:
        config['role'].append('master_server')

    config_values = wizard_input_to_asp_conf_values(config, is_create_cluster)
    config_values['_private/node_id'] = ConfDb().get_value('_private/node_id')

    config_values['modelCategory'] = request.POST.get('modelCategory')
    if config_values['modelCategory'] == 'single':
        config_values['modelCategory'] = 'group'

    #check VIP params
    if request.POST.get('modelCategory') in ('hotStandby', 'HARouteProxy') and is_create_cluster:
       ret = check_VIP_params(request, config)
       if ret:
           return ret

    if not is_create_cluster:
        try:
            ret = pre_join_cluster_internal(True, config_values, cluster_ip, '', '', '', '', '')
            if ret.get('exception'):
                ret = {'result': RESULT.CANNOT_CONNECT_TO_SERVER, 'message': _('Cannot connect to master node.')}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
        except Exception as e:
            ret = {'result': RESULT.EXCEPTION, 'message': str(e)}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

    return render_json_response(RESULT.OK, '')


def set_VIP_config(request,config_values):
    config_values['os/network/keepalived/on'] = True
    config_values['os/network/keepalived/external/only_listen_vip'] = True

    standbyMode = request.POST.get('hotStandbyMode', '')
    external_ipVersion = request.POST.get('external_ipVersion')

    external_virtual_ip = request.POST.get('external_virtual_ip')
    external_virtual_netmask = request.POST.get('external_virtual_netmask')
    external_virtual_router_id = request.POST.get('external_virtual_router_id')
    external_virtual_ipv6 = request.POST.get('external_virtual_ipv6')
    external_virtual_ipv6_prefix = request.POST.get('external_virtual_ipv6_prefix')

    if external_ipVersion == 'ipv4' or external_ipVersion == 'ipv46':
        config_values['os/network/keepalived/external/virtual_ip'] = external_virtual_ip
        config_values['os/network/keepalived/external/virtual_netmask'] = external_virtual_netmask

    if external_ipVersion == 'ipv6' or external_ipVersion == 'ipv46':
        config_values['os/network/keepalived/external/virtual_ip_ipv6'] = external_virtual_ipv6
        config_values['os/network/keepalived/external/virtual_ip_ipv6_prefix'] = external_virtual_ipv6_prefix
    config_values['os/network/keepalived/external/vrrp_id'] = external_virtual_router_id

    if standbyMode == 'mutualStandby':
        external_virtual_ip = request.POST.get('external_virtual2_ip')
        external_virtual_netmask = request.POST.get('external_virtual2_netmask')
        external_virtual_router_id = request.POST.get('external_virtual2_router_id')
        external_virtual_ipv6 = request.POST.get('external_virtual2_ipv6')
        external_virtual_ipv6_prefix = request.POST.get('external_virtual2_ipv6_prefix')

        if external_ipVersion == 'ipv4' or external_ipVersion == 'ipv46':
            config_values['os/network/keepalived/external/virtual2_ip'] = external_virtual_ip
            config_values['os/network/keepalived/external/virtual2_netmask'] = external_virtual_netmask

        if external_ipVersion == 'ipv6' or external_ipVersion == 'ipv46':
            config_values['os/network/keepalived/external/virtual2_ip_ipv6'] = external_virtual_ipv6
            config_values['os/network/keepalived/external/virtual2_ip_ipv6_prefix'] = external_virtual_ipv6_prefix
        config_values['os/network/keepalived/external/vrrp2_id'] = external_virtual_router_id


def _check_VIP_params_(request, all_ip, key_prefix ,external_virtual_ip, external_virtual_netmask,
                        external_virtual_router_id, external_virtual_ipv6, external_virtual_ipv6_prefix):

    err_info = 'external_' + key_prefix + ': {}'.format(
        json.dumps(dict(
            virtual_ip=external_virtual_ip,
            virtual_netmask=external_virtual_netmask,
            virtual_router_id=external_virtual_router_id,
            virtual_ipv6=external_virtual_ipv6,
            virtual_ipv6_prefix=external_virtual_ipv6_prefix,
        ))
    )

    external_ipVersion = request.POST.get('external' + '_ipVersion')
    if external_ipVersion == 'ipv4' or external_ipVersion == 'ipv46':
        if not external_virtual_ip or not external_virtual_netmask or not external_virtual_router_id\
                    or not 1 <= int(external_virtual_router_id) <= 255:
            ret = {'result': RESULT.INVALID_PARAMS, 'message': '{0}:: {1}'.format(
                _('Invalid parameter in virtual IP.'), err_info)}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
        ip_int, ip_ver = ip_to_int(external_virtual_ip)
        netmask_int, mask_ver = mask_to_int(external_virtual_netmask)
        if ip_int < 0 or netmask_int < 0 or ip_ver != mask_ver:
            ret = {'result': RESULT.INVALID_PARAMS, 'message': '{0}:: {1}'.format(
                _('Invalid parameter in virtual IP.'), err_info
            )}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

        if compress_IP(external_virtual_ip) in all_ip:
            ret = {'result': RESULT.INVALID_PARAMS, 'message': '{0}:: external: {1}'.format(
                _('IP address conflict.'), json.dumps({key_prefix + "_ip": external_virtual_ip})
            )}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
        all_ip.add(external_virtual_ip)
    if external_ipVersion == 'ipv6' or external_ipVersion == 'ipv46':
        if not external_virtual_ipv6 or not external_virtual_ipv6_prefix or not external_virtual_router_id\
                    or not 1 <= int(external_virtual_router_id) <= 255:
            ret = {'result': RESULT.INVALID_PARAMS, 'message': '{0}:: {1}'.format(
                _('Invalid parameter in virtual IP.'), err_info)}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
        ip_int, ip_ver = ip_to_int(external_virtual_ipv6)
        netmask_int, mask_ver = mask_to_int(external_virtual_ipv6_prefix)
        if ip_int < 0 or netmask_int < 0 or ip_ver != mask_ver:
            ret = {'result': RESULT.INVALID_PARAMS, 'message': '{0}:: {1}'.format(
                _('Invalid parameter in virtual IP.'), err_info
            )}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)

        if compress_IP(external_virtual_ipv6) in all_ip:
            ret = {'result': RESULT.INVALID_PARAMS, 'message': '{0}:: external: {1}'.format(
                _('IPv6 address conflict.'), json.dumps({key_prefix + "_ipv6": external_virtual_ipv6})
            )}
            return HttpResponse(json.dumps(ret), content_type=CONTENT_TYPE_JSON)
        all_ip.add(external_virtual_ipv6)
    return None


def check_VIP_params(request, config):
    all_ip = set()
    for name in ADAPTERS:
        adapter = config.get(name)
        if adapter:
            all_ip.add(adapter.get('ip'))
            all_ip.add(adapter.get('dual_ip'))

    external_virtual_ip = request.POST.get('external_virtual_ip')
    external_virtual_netmask = request.POST.get('external_virtual_netmask')
    external_virtual_router_id = request.POST.get('external_virtual_router_id')
    external_virtual_ipv6 = request.POST.get('external_virtual_ipv6')
    external_virtual_ipv6_prefix = request.POST.get('external_virtual_ipv6_prefix')

    ret = _check_VIP_params_(request, all_ip, 'virtual', external_virtual_ip, external_virtual_netmask,
                        external_virtual_router_id, external_virtual_ipv6, external_virtual_ipv6_prefix)
    if ret:
        return ret

    standbyMode = request.POST.get('hotStandbyMode', "hostStandby")
    if standbyMode == 'mutualStandby':
        external_virtual_ip = request.POST.get('external_virtual2_ip')
        external_virtual_netmask = request.POST.get('external_virtual2_netmask')
        external_virtual_router_id = request.POST.get('external_virtual2_router_id')
        external_virtual_ipv6 = request.POST.get('external_virtual2_ipv6')
        external_virtual_ipv6_prefix = request.POST.get('external_virtual2_ipv6_prefix')
        ret = _check_VIP_params_(request, all_ip, 'virtual2', external_virtual_ip, external_virtual_netmask,
                        external_virtual_router_id, external_virtual_ipv6, external_virtual_ipv6_prefix)
        if ret:
            return ret
    return None


def get_system_route():
    """

    :return: [
        {'ip': '**********', 'gateway': '************', 'netmask': '*************', 'private': True},
        {'ip': '**********', 'gateway': '************', 'netmask': '*************', 'private': True}
    ]
    """
    cmd = "route -n"
    ret_code, out, cmd = exe_with_output(cmd)
    if ret_code:
        logging.error("Command '{}' execute failed, output: {}.".format(cmd, out))
        return

    data_list = out.split("\n")
    if len(data_list) < 3:
        logging.info("System has no route.")
        return

    route_list = []
    key_list = ['ip', 'gateway', 'netmask']
    data_list = list(map(lambda x: x.split()[:3], data_list[2:]))

    for i in data_list:
        if not i:
            continue
        elif len(i) < 3:
            continue
        elif "0.0.0.0" == i[0]:
            continue
        elif "0.0.0.0" == i[1]:
            continue
        else:
            i_dict = dict(zip(key_list, i))
            i_dict['private'] = True
            route_list.append(i_dict)

    return route_list


def get_system_default_gateway():
    """

        :return: ("*************", "")
        """
    default_gateway_ipv4 = None
    default_gateway_ipv6 = None

    for ipv in [4, 6]:
        cmd = 'ip -{} route list scope global match default'.format(ipv)
        p = subprocess.Popen([cmd], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, shell=True)
        out, err = p.communicate()
        if p.returncode != 0:
            logging.info("Command '{}' execute failed, output: {}, error: {}.".format(cmd, out, err))
            gateway = ""
        else:
            if not out:
                gateway = ""
            else:
                gateway = out.split()[2]
        exec("default_gateway_ipv{} = '{}'".format(ipv, gateway))

    return default_gateway_ipv4, default_gateway_ipv6


def check_route_params(route_conf):
    route_list = list(map(lambda x: "{}/{}".format(x.get('ip'), x.get('netmask')), route_conf))
    route = check_route_uniqueness(route_list)
    if route:
        route = route.replace(":", "\:")
        ret = {'result': RESULT.ROUTE_ERROR, 'message': '{0}: {1}'.format(_('Route conflict.'), route)}
        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def load_route(conf, route_list, is_single_mode=False):
    asp_conf = ConfDb()
    exist_private_routes = asp_conf.get_all("_private/os/network/admin/route_list").keys()

    shared_routes = dict(zip(ADAPTERS, [{} for i in range(len(ADAPTERS))]))
    private_routes = dict(zip(ADAPTERS, [{} for i in range(len(ADAPTERS))]))
    if route_list:
        for route in route_list:
            r_ip = compress_IP(route.get('ip'))
            r_mask = route.get('netmask')
            r_gw = compress_IP(route.get('gateway'))
            r_private = route.get('private')

            try:
                check_route_parameters(r_ip, r_mask, r_gw, None)
            except ValueError as e:
                ret = {'result': RESULT.ROUTE_ERROR, 'message': e.message}
                return ret

            if is_single_mode:
                suitable_intf = (ADAPTERS[1], 0)
            else:
                suitable_intf = None
                for intf in ADAPTERS:
                    conf_ip = conf[intf]["ip"]
                    conf_mask = conf[intf]["netmask"]
                    conf_dual_ip = conf[intf]['dual_ip']
                    conf_dual_mask = conf[intf]["dual_netmask"]

                    conflict_ret = {'result': RESULT.ROUTE_ERROR,
                                    'message': '{0}: {1}/{2}'.format(_('Route conflict.'), route.get('ip').replace(":", "\:"), r_mask)}

                    if is_same_subnet(r_gw, conf_ip, conf_mask):
                        if conf_mask == r_mask and is_same_subnet(r_ip, conf_ip, conf_mask):
                            return conflict_ret
                        if suitable_intf:
                            suitable_intf = (intf, conf_mask) if conf_mask > suitable_intf[1] else suitable_intf
                        else:
                            suitable_intf = (intf, conf_mask)
                    elif is_same_subnet(r_gw, conf_dual_ip, conf_dual_mask):
                        if conf_dual_mask == r_mask and is_same_subnet(r_ip, conf_dual_ip, conf_dual_mask):
                            return conflict_ret
                        if suitable_intf:
                            suitable_intf = (intf, conf_dual_mask) if conf_dual_mask > suitable_intf[1] else suitable_intf
                        else:
                            suitable_intf = (intf, conf_dual_mask)

            if suitable_intf:
                new_key = "{}:{}:{}".format(r_ip, r_mask, 1 if r_private else 0)
                routes = {NetworkConf.IP: r_ip,
                          NetworkConf.NETMASK: r_mask,
                          NetworkConf.GATEWAY: r_gw,
                          'private': r_private}

                if r_private:
                    private_routes[suitable_intf[0]][new_key] = routes
                else:
                    shared_routes[suitable_intf[0]][new_key] = routes
                    p_new_key = new_key[:-1] + "1"
                    if p_new_key in exist_private_routes:
                        private_routes[suitable_intf[0]][p_new_key] = None

            else:
                ret = {'result': RESULT.ROUTE_ERROR, 'message': '{0}: {1}'.format(_('Invalid route gateway.'), route.get('gateway').replace(":", "\:"))}
                return ret

    for exist_route in exist_private_routes:
        if exist_route not in private_routes['admin'].keys():
            private_routes['admin'][exist_route] = None
            logger.info("Delete private route: {}".format(exist_route))

    conf['shared_route'] = shared_routes
    conf['private_route'] = private_routes



@wizard_security_check
def blinking_nic_port(request):
    if request.method != "GET":
        return render_json_response(RESULT.BAD_COMMAND, 'Use GET instead')

    nic_name = request.GET.get('nic_name', '')
    identify_nic(nic_name)

    return render_json_response(RESULT.OK, '')


def get_optimal_gateway(wizard_dict):
    gateway_check_dict = dict()
    for intf in ADAPTERS:
        gateway = wizard_dict[intf].get('gateway')
        if gateway:
            if gateway not in gateway_check_dict.keys():
                gateway_check_dict[gateway] = {'intf': intf, 'netmask': wizard_dict[intf].get('netmask')}
            else:
                if wizard_dict[intf].get('netmask') > gateway_check_dict[gateway]['netmask']:
                    del wizard_dict[gateway_check_dict[gateway]['intf']]['gateway']
                    gateway_check_dict[gateway] = {'intf': intf, 'netmask': wizard_dict[intf].get('netmask')}
                else:
                    del wizard_dict[intf]['gateway']

        dual_gateway = wizard_dict[intf].get('dual_gateway')
        if dual_gateway:
            if dual_gateway not in gateway_check_dict.keys():
                gateway_check_dict[dual_gateway] = {'intf': intf, 'netmask': wizard_dict[intf].get('dual_netmask')}
            else:
                if wizard_dict[intf].get('dual_netmask') > gateway_check_dict[dual_gateway]['netmask']:
                    if wizard_dict[gateway_check_dict[dual_gateway]['intf']].get('gateway') == dual_gateway:
                        del wizard_dict[gateway_check_dict[dual_gateway]['intf']]['gateway']
                    if wizard_dict[gateway_check_dict[dual_gateway]['intf']].get('dual_gateway') == dual_gateway:
                        del wizard_dict[gateway_check_dict[dual_gateway]['intf']]['dual_gateway']
                    gateway_check_dict[dual_gateway] = {'intf': intf, 'netmask': wizard_dict[intf].get('netmask')}
                else:
                    del wizard_dict[intf]['dual_gateway']

    external_gateway = wizard_dict.get('external', {}).get('gateway', {})
    if external_gateway and ':' not in external_gateway:
        wizard_dict['admin']['gateway'] = None

