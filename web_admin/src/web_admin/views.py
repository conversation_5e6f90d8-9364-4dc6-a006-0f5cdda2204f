# encoding=utf-8

'''
@author: tom tang
@copyright: (c) 2014 River Security Technology Corporation, Ltd. All rights reserved.
'''
import copy
import hmac
import json
import random
import re
import shutil
import string
import _strptime # workaround for https://bugs.python.org/issue7980
import time
import os
import errno
import locale
import httplib
import urllib
import operator
import pickle
import uuid
import warnings
import zipfile
import qrcode
import cStringIO
import threading
import logging
import xml.etree.ElementTree as ET
import datetime
import commands
import uptime
import base64
import yaml
import hashlib
import math
from tempfile import NamedTemporaryFile
from django import forms
from django import http
from django.conf import settings
from django.contrib.auth import REDIRECT_FIELD_NAME, login as auth_login, update_session_auth_hash
from django.contrib.auth.models import User, Group
from django.contrib.auth import get_user_model, authenticate
from django.contrib.auth.hashers import make_password
from django.contrib.auth.forms import AuthenticationForm, PasswordChangeForm
from django.core.cache import cache
from django.core.urlresolvers import reverse
from django.http import HttpResponseRedirect, HttpResponse, JsonResponse
from django.shortcuts import render_to_response, resolve_url
from django.template import loader
from django.template.context import RequestContext
from django.template.defaulttags import register
from django.template.response import TemplateResponse
from django.utils.translation import ugettext as _, ugettext_lazy, ugettext_noop, get_language
from django.utils.http import is_safe_url
from django.views.decorators.csrf import csrf_exempt, csrf_protect
from django.views.decorators.debug import sensitive_post_parameters
from django.views.decorators.cache import never_cache
from django.contrib.sites.shortcuts import get_current_site
from django.contrib.auth.decorators import login_required as django_login_required
from django.core.validators import validate_ipv4_address, validate_integer, validate_ipv46_address, is_valid_ipv6_address
from django.core.exceptions import ValidationError
from django.views.decorators.http import require_http_methods
import django_views_static as static #Fix CVE-2017-7234 DAP-24200
from asp_utils.rsa_sign import get_asp_aes_key, aes_decrypt, aes_encrypt,asp_encrypt,asp_decrypt
from asp_utils.CommonLibs import compress_IP, mask_to_prefix, valid_IP_or_CIDR
from asp_utils.asp_certs import AspCerts
from asp_utils.waf_util import get_waf_conf_path
from asp_utils.signature import new_session_with_sign
from asp_utils.filter_rules import filter_rules_check
from asp_utils.utils import get_product_type
from generic.response import BaseTemplateResponse
from generic.utils import compare_dict, kick_user_session
from django.contrib.auth.hashers import check_password
from web_admin.exceptions import ERR_CODE_ADRULES_MAX_LIMIT
from web_admin.exceptions import ERR_CODE_ADRULES_INVALID_RULE
from web_admin.exceptions import ERR_CODE_ADRULES_TIMEOUT
from web_admin.exceptions import ERR_CODE_ADRULES_INVALID_FILE
from web_admin.labs import rules_is_valid
from web_admin.Conf_Logserver import LogserverConf
from web_admin.Conf_Network import NetworkConf
from web_admin.Conf_Nginx import NginxConf
from web_admin.Conf_Nginx import MobileConf
from web_admin.Conf_Mpp import WechatConf, AlipayConf, MPaasConf
from web_admin.Conf_Upgrade import UpgradeConf
from module_ctrls.upgrade.cipher_wafruleset import verifyPatch, extractPatch
from module_ctrls.upgrade.cipher_util import save_pkg, pkg_must_verify, extract_patch
from module_ctrls.network.network import usable_device_names
from module_ctrls.waf_rule_ctrl import WAFGrayBox
from web_admin.Conf_Webconsole import WebconsoleConf
from web_admin.operation_log import operation_log, get_client_ip
from web_admin.forms import AuthenticationCaptchaForm
from web_admin.site_acl_util import clean_site_operator_by_user, get_sites_4_user, clean_site_config_2_blank, assign_site_4_user, get_users_with_role
from web_admin.view_lib import Pagination, time_to_date_str, \
    to_int, SECS_OF_DAY, login_required, json_ok_response, \
    json_fail_response, get_date_start_end_param, get_current_git_commit_no, get_current_build_info, \
    get_current_is_debug, get_current_is_prod_pkg, wizard_status_check, session_idle_logout
from web_admin.git import Git
from web_admin.Conf_Base import BaseConf
from web_admin.decorators import expert_mode_required, allow_method,  adv_operation_code_required, has_dm_permission, \
    check_permission, has_permission, role_management_enabled
from web_admin.settings import STATICFILES_DIR

import service_mgr_rest_api
from service_mgr_rest_api import query_online_phoenix_nodes, get_cluster_node_list, \
    get_all_zookeeper_client
from service_mgr_rest_api import service_mgr_set_asp_config, service_mgr_set_command, ensure_task_completed, \
    service_mgr_syncfile_remove, service_mgr_syncfile_create

from asp_conf_ctrl import ConfDb, exe_call_asp_ctrl, conf_mask, restart_services
from asp_utils import rsa_sign
from asp_utils.CommonLibs import valid_Domain, valid_IP, valide_netmask, valid_IPv4, valid_IPv6, valid_IPv6_with_bracket, is_Local_Loopback, is_same_subnet, is_same_CIDR
from asp_utils.utils import get_release_file, exe, exe_with_output, get_version, get_sync_file_path, \
    get_manifest_info, get_default_bot_check_config, get_transparent_mode_effective_br_num, \
    get_upgrade_lib_path, netaddr_parse, ipv4_len2mask, RESULT, get_layout, parse_bool, get_all_ifaces_info, \
    get_build_hash_and_layout_hash, to_safe_csv, get_all_good_bot_names, current_machine_hardware_name, \
    get_default_custom_geo, delete_expired_ip, is_port_already_in_use, \
    get_copyright, in_k8s, is_product_category_NGWAF, construct_ifaces_info, is_iface_editable, \
    DEFAULT_PERF_PARAMS_OPT_WORKER_CONNECTIONS, is_layout_valid

from asp_utils.license_info import LicenseInfo
from asp_utils.phoenix_util import phoenix_restapi_request, phoenix_restapi_screen_request, \
    PHOENIX_ADMIN, PHOENIX_ADMIN_PWD
from asp_utils.sailfish_util import sailfish_restapi_request, sailfish_request
from asp_utils.rsa_sign import simple_decrypt_and_verify
from asp_utils.utils import is_debug_install, get_upstream_port_map, in_container, get_product_type
from asp_utils.network_protect_util import is_support_current_platform
from asp_utils.waf_util import buildin_ruleset_file_pathname, sync_wafruleset_filename
from asp_utils.bond_util import get_bond_info, get_bond_info_in_cfg
from asp_utils.zk_nodelock import ZkNodeLock
from asp_utils.zk_client import ZkClient
from asp_utils.goodbots_auto_update import GoodBotUpgrade
from web_admin.utils import ClusterNodeStat, create_1pixel_transparent_png_file, create_1pixel_transparent_ico_file, get_rcm_info
from web_admin.utils_route import check_route_uniqueness, check_route_parameters
from system.utils.totp import get_totp, get_or_create_totp
from Conf_Waf import WafConf
from abd_utils.utils.func_table_update import update_all_pg_settings_table
from abd_utils.peewee_migrate import get_major_version
from api_base.lru_cache import clear_global_lru_cache
from asp_utils.utils import RESULT

logger = logging.getLogger('view')

CONTENT_TYPE_PLAIN = 'text/plain'
CONTENT_TYPE_JSON = 'application/json'
CONTENT_TYPE_JS = 'application/javascript'
CONTENT_TYPE_FORM_ENCODED = 'application/x-www-form-urlencoded'
CONTENT_TYPE_OCT_STREAM = 'application/octet-stream'
CONTENT_TYPE_ZIP = 'application/zip'

COUNT_PER_PAGE = 50
MAX_ADRULES_COUNT = 100
MAX_NETWORK_ACCESS_CONTROL_RULES_COUNT = 50

ASP_POWER_STATUS = ""


PATH_TO_SAILFISH_EXTERNAL_NODE = 'sailfish/external_node_list'
PATH_TO_SAILFISH_ALLOW_ACCESS_LIST = 'sailfish/allow_access_list'

ADAPTER_LIST = [NetworkConf.IF_ADMIN, NetworkConf.IF_EXTERNAL, NetworkConf.IF_INTERNAL, NetworkConf.IF_LOG, NetworkConf.IF_KEEPALIVED]

ECHO_PASSWORD = "*" * 16

def base_render_to_response(request, template, dictionary=None, conf=None):
    """!!! FIXME:  USE BaseTemplateResponse Instead !!!"""
    warnings.warn('USE BaseTemplateResponse Instead',
                  DeprecationWarning, stacklevel=2)
    return BaseTemplateResponse(request, template, dictionary)


def get_brand_str():
    return _('ForceShield Dynamic Application Protection')

'''
Distinguish different 404 errors with request.path.
'''


@csrf_exempt
def custom_404_view(request, template_name='404.html'):
    request_path = request.path

    failed_reason = 'The requested URL %s was not found on this server.' % request_path

    t = loader.get_template(template_name)  # You need to create a 404.html template.
    return http.HttpResponseNotFound(t.render(RequestContext(request, {'failed_reason': failed_reason})))

@wizard_status_check
@session_idle_logout
def entry(request):
    ua = request.META['HTTP_USER_AGENT']
    is_IE_10_or_lower = re.findall(r'MSIE [2-9]', ua) or ua.find('MSIE 10') != -1
    if is_IE_10_or_lower:
        return HttpResponseRedirect("/download_browser/")

    return HttpResponseRedirect("/index")


@wizard_status_check
@session_idle_logout
def index(request):
    ua = request.META['HTTP_USER_AGENT']
    is_IE_10_or_lower = re.findall(r'MSIE [2-9]', ua) or ua.find('MSIE 10') != -1
    if is_IE_10_or_lower:
        return HttpResponseRedirect("/download_browser/")

    return static.serve(request, 'index.html', settings.STATICFILES_DIR + "/api/")

@login_required
def rootFolder(request):
    return HttpResponseRedirect("/overview/")


from django.http import StreamingHttpResponse
from urllib import quote

def file_iterator(file_name, chunk_size=8192):
    with open(file_name) as f:
        while True:
            c = f.read(chunk_size)
            if c:
                yield c
            else:
                break


def chrome_file_download(request):
    the_file_name = "/var/www/Chrome.exe"
    out_put_file_name = quote("Chrome.exe")
    response = StreamingHttpResponse(file_iterator(the_file_name))
    response['Content-Type'] = 'application/octet-stream'
    response['Content-Disposition'] = 'attachment;filename={0}'.format(out_put_file_name)
    return response

zip_path = '/tmp/archive_log/'
raw_log_path = '/var/log/asp/archived/'


@login_required
@check_permission('System_Log', 'read')
def log_file_download(request):
    if request.method == 'GET':
        file_name = request.GET.get('file_name')
        if not file_name:
            result = {'result':'FAILED', 'message': 'file name is empty.'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        pattern = r'^[.0-9_-]{1,60}\.zip$'
        if not re.match(pattern, file_name):
            result = {'result':'FAILED', 'message': 'Invalid request.'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        path = zip_path + file_name
        if os.path.exists(path):
            out_put_file_name = quote(file_name)
            response = StreamingHttpResponse(file_iterator(path))
            response['Content-Type'] = 'application/octet-stream'
            response['Content-Disposition'] = 'attachment;filename={0}'.format(out_put_file_name)
            operation_log(request, ugettext_noop('Log'), ugettext_noop('Download'), '0', {
                'msg': ugettext_noop('System Log has been Downloaded'),
            },
                          user=request.POST.get('username'))
            return response

        else:
            result = {'result': 'FAILED', 'message': 'request file name is not existed.'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    else:
        result = {'result': 'FAILED', 'message': 'Invalid request.'}
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Archived_Log', 'read')
def raw_log_file_download(request):
    if request.method == 'GET':
        file_name = request.GET.get('file_name')
        if not file_name:
            result = {'result': 'FAILED', 'message': 'file name is empty.'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        pattern = r'^[a-zA-Z0-9-_\.:]*\.(log|gz|1)$'
        if not re.match(pattern, file_name):
            result = {'result': 'FAILED', 'message': 'Invalid file name.'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        path = raw_log_path + file_name
        if os.path.exists(path):
            out_put_file_name = quote(file_name)
            response = StreamingHttpResponse(file_iterator(path))
            response['Content-Type'] = 'application/octet-stream'
            response['Content-Disposition'] = 'attachment;filename={0}'.format(out_put_file_name)
            operation_log(request, ugettext_noop('Log'), ugettext_noop('Download'), '0', {
                'msg': ugettext_noop('Archived Log has been Downloaded'),
            },
                          user=request.POST.get('username'))
            return response
        else:
            result = {'result': 'FAILED', 'message': 'request file name is not existed.'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
    else:
        result = {'result': 'FAILED', 'message': 'Invalid request.'}
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def help_page(request):
    return base_render_to_response(request, 'v2/about.html')


def get_cluster_node_count(role=None):
    valid_exist_node_num = 0
    cluster_nodes = service_mgr_rest_api.get_cluster_node_list()
    for key, val in cluster_nodes.items():
        if key.startswith('_'):
            continue
        deleted = val.get('_deleted')
        if deleted:
            continue

        if role:
            all_role = val.get('_role')
            if all_role is None:
                continue
            if role in all_role:
                valid_exist_node_num += 1
        else:
            valid_exist_node_num += 1
    return valid_exist_node_num


@login_required
def config_ip(request):
    return config_network(request, 'v2/config_ip.html', [NetworkConf.IF_EXTERNAL, NetworkConf.IF_INTERNAL])


def config_network(request, html, intfs):
    networkConf = NetworkConf()
    error = None

    bypass_transparent = networkConf.get_value('_private/os/network/bypass_transparent', def_val=False, is_abs=True)
    bridge_transparent = networkConf.get_value('_private/os/network/bridge_transparent', def_val=False, is_abs=True)
    port_status_forward = networkConf.get_value('_private/os/network/port_status_forward', def_val=False, is_abs=True)
    source_port_keep = networkConf.get_value('_private/os/network/source_port_keep', def_val=False, is_abs=True)

    if request.method == "POST":
        if not has_permission(request.user.username, 'Network_Configuration', 'write'):
            return HttpResponse(status=403)
        # network
        result = handle_post_for_network_config_box(request, networkConf, intfs[0])
        if networkConf.get_value('nginx/transparent_mode/enable', def_val=False, is_abs=True):
            def _def_switch(val, default):
                """如果没有传值，使用原始值"""
                is_transparent_mode = WebconsoleConf().get_conf().get_value('nginx/transparent_mode/enable', False)
                return default if not is_transparent_mode or val is None else val == 'on'

            bypass_transparent_ = _def_switch(request.POST.get('bypass_transparent'), bypass_transparent)
            bridge_transparent_ = _def_switch(request.POST.get('bridge_transparent'), bridge_transparent)
            port_status_forward_ = _def_switch(request.POST.get('port_status_forward'), port_status_forward)
            source_port_keep_ = _def_switch(request.POST.get('source_port_keep'), source_port_keep)

            if bypass_transparent_ != bypass_transparent:
                operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), '0', {'msg': ugettext_noop('Enable bypass') if bypass_transparent_ else ugettext_noop('Disable bypass')})

            if bridge_transparent_ != bridge_transparent:
                operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), '0', {'msg': ugettext_noop('Enable bridge transparent') if bridge_transparent_ else ugettext_noop('Disable bridge transparent')})

            if port_status_forward_ != port_status_forward:
                operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), '0', {'msg': ugettext_noop('Enable port status forward') if port_status_forward_ else ugettext_noop('Disable port status forward')})

            if source_port_keep_ != source_port_keep:
                operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), '0', {'msg': ugettext_noop('Enable souce port keep') if source_port_keep_ else ugettext_noop('Disable source port keep')})

            if bypass_transparent_ != bypass_transparent or bridge_transparent_ != bridge_transparent or port_status_forward_ != port_status_forward or source_port_keep_ != source_port_keep:
                networkConf.set_asp_conf_values({
                    '_private/os/network/bypass_transparent': bypass_transparent_,
                    '_private/os/network/bridge_transparent': bridge_transparent_,
                    '_private/os/network/port_status_forward': port_status_forward_,
                    '_private/os/network/source_port_keep': source_port_keep_,
                })
                if result.get('result') == 'SAME_CONFIG':
                    result['result'] = 'SUCCESS'

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    if not has_permission(request.user.username, 'Network_Configuration', 'read'):
        return HttpResponse(status=403)
    ifaces_info = construct_ifaces_info(networkConf)
    _, all_route_list = networkConf.get_conf().get_route_conf_dict()

    return base_render_to_response(request, html,
                                   {
                                       'error': error,
                                       'is_cloud_mode': WebconsoleConf().is_cloud_mode(),
                                       'bypass_transparent': bypass_transparent,
                                       'bridge_transparent': bridge_transparent,
                                       'port_status_forward': port_status_forward,
                                       'source_port_keep': source_port_keep,
                                       'host_white_lists': networkConf.get_webconsole_host_white_lists(),
                                       'all_route_list': all_route_list,
                                       'admin_adapter': networkConf.get_adapter_name('admin'),
                                       'ifaces_info': ifaces_info
                                   })

@login_required
@check_permission('Network_Configuration', 'write')
def adjust_datetime(request):
    if request.method == "POST":
        base_conf = BaseConf()
        admin_ip = base_conf.get_value('_private/os/network/admin/ip')
        new_date = json.loads(request.body).get("date")
        # call <asp_config date > cmd to set up local datetime
        date_format = '%Y-%m-%d %H:%M:%S'
        try:
            t = time.strptime(new_date, date_format)
        except Exception as e:
            return json_fail_response('Parse datetime string error: %s' % new_date)

        now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        result = base_conf.exc_asp_conf_command(values=['set_utc_time', str(time.mktime(t)), "--cluster_sync"], sync=False)

        if result['result'] == RESULT.IN_QUEUE:
            operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), "0",
                            {'msg': ugettext_noop('Adjust system datetime'),
                             "info": "System datetime has been changed from {0} to {1} on node {2}".format(now, new_date, admin_ip)},
                              request.POST.get('username'))
            return json_ok_response(
                msg=_("Adjust datetime successfully.Cluster datetime synchronization will be conducted later."),
                result="OK")
        else:
            operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), "1",
                              {'msg': ugettext_noop('Adjust system datetime'),
                               "info": "System datetime failed in changing from {0} to {1} on node {2}".format(now, new_date, admin_ip)
                               }, request.POST.get('username'))
            return json_ok_response(
                    msg=_("Adjust datetime successfully.Cluster datetime synchronization failed."),
                    result="OK")

    else:
        return json_fail_response("Not allowed method", status=405)


@login_required
@check_permission('Network_Configuration', 'read')
def get_ifaces_info(request):
    ifaces_info = construct_ifaces_info(NetworkConf())
    return HttpResponse(json.dumps(ifaces_info, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def log_and_return_json_response(request, msg, node_id, iface_name=None, result='1', info=None):
    detail = {'msg': msg}
    if iface_name:
        detail['extra'] = {'node_id': node_id, 'iface_name': iface_name}
    else:
        detail['extra'] = {'node_id': node_id}
    if info:
        detail['info'] = info
    operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), result, detail)

    if iface_name:
        if result == '0':
            return json_ok_response(_(msg).format(node_id=node_id, iface_name=iface_name))
        else:
            return json_fail_response(_(msg).format(node_id=node_id, iface_name=iface_name))
    else:
        if result == '0':
            return json_ok_response(_(msg).format(node_id=node_id))
        else:
            return json_fail_response(_(msg).format(node_id=node_id))


@login_required
@check_permission('Network_Configuration', 'write')
@allow_method('post')
def edit_iface(request):
    try:
        params = json.loads(request.body)
    except:
        return json_fail_response('bad json')

    iface_name = params.get('iface_name')
    ipv4 = params.get('ipv4')
    ipv6 = params.get('ipv6')
    gateway = params.get('gateway')
    ipv6_gateway = params.get('ipv6_gateway')
    ipv4_route = params.get('ipv4_route')
    master_ips = params.get('master_ips')
    access_webconsole = params.get('access_webconsole', False)
    access_webconsole_ipv6 = params.get('access_webconsole_ipv6', False)
    services = []
    if access_webconsole:
        services.append('webconsole')
    if access_webconsole_ipv6:
        services.append('webconsole_ipv6')

    net_conf = NetworkConf()
    ifaces_info = construct_ifaces_info(net_conf, internal=True)
    conf_db = ConfDb()
    node_id = conf_db.get_value('_private/node_id')
    adapter_role = conf_db.get_adapter_role(iface_name)

    if not ifaces_info.get(iface_name):
        return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid NIC name {iface_name}'), node_id, iface_name)
    if not is_iface_editable(iface_name, conf_db):
        return log_and_return_json_response(request, ugettext_noop('Node {node_id}: {iface_name} is not allowed to edit'), node_id, iface_name)

    iface_info = ifaces_info[iface_name]
    org_params = {'access_webconsole': ifaces_info[iface_name]['access_webconsole'],
                  'access_webconsole_ipv6': ifaces_info[iface_name]['access_webconsole_ipv6']}
    new_params = {'access_webconsole': access_webconsole,
                  'access_webconsole_ipv6': access_webconsole_ipv6}
    for k in ('ipv4', 'ipv6', 'ipv4_route', 'master_ips'):
        if iface_info.get(k):
            org_params[k] = iface_info.get(k)
        if params.get(k):
            new_params[k] = params.get(k)
    for k in ('gateway', 'ipv6_gateway'):
        if iface_info.get(k) and iface_info[k][0]:
            org_params[k] = iface_info.get(k)
        if params.get(k) and params[k][0]:
            new_params[k] = [params.get(k)]
    roles = iface_info.get('roles')

    # verify number of ipv4 and ipv6 for all nic
    if iface_info['could_add_vlan']:
        # could add vlan and max ip number is 30
        if ipv4 and type(ipv4) == list and ipv6 and type(ipv6) == list and len(ipv4) + len(ipv6) > 30:
            return log_and_return_json_response(request, ugettext_noop(
                'Node {node_id}: More than 30 IP addresses for NIC {iface_name}'), node_id, iface_name)
    else:
        if ipv4 and type(ipv4) == list and len(ipv4) > 1:
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: More than 1 IPv4 addresses for NIC {iface_name}'), node_id, iface_name)
        if ipv6 and type(ipv6) == list and len(ipv6) > 1:
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: More than 1 IPv6 addresses for NIC {iface_name}'), node_id, iface_name)

    ip = None
    netmask = None
    other_ipv4_list = list()
    other_ipv6_list = list()
    ip_v6 = None
    ipv6_netmask = None
    # verify ip address and netmask
    if ipv4:
        if type(ipv4) != list:
            return log_and_return_json_response(request, ugettext_noop(
                'Node {node_id}: Invalid IPv4 address for NIC {iface_name}'), node_id, iface_name)
        ipv4_set = set()
        for item in ipv4:
            if type(item) != list and len(item) != 3:
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid IPv4 address for NIC {iface_name}'), node_id, iface_name)
            try:
                vlan_id = item[0]
                if vlan_id and not (0 < int(vlan_id) < 4095):
                    return log_and_return_json_response(request, ugettext_noop(
                        'Node {node_id}: Invalid VLAN ID for NIC {iface_name}'), node_id, iface_name)
            except:
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid VLAN ID for NIC {iface_name}'), node_id, iface_name)
            if not valid_IPv4(item[1]):
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid IPv4 address for NIC {iface_name}'), node_id, iface_name)
            if not valide_netmask(item[2]):
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid IPv4 netmask for NIC {iface_name}'), node_id, iface_name)
            ipv4_set.add(item[1])
            if item[0] == '' and ip is None:
                ip = item[1]
                netmask = item[2]
            else:
                other_ipv4_list.append(item)
        if ip is None and other_ipv4_list:
            return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: NIC {iface_name} should have 1 IPv4 address without VLAN'), node_id, iface_name)
        if len(ipv4_set) < len(ipv4):
            return log_and_return_json_response(request, ugettext_noop(
                'Node {node_id}: NIC {iface_name} have duplicated IPv4 addresses'), node_id, iface_name)
    if ipv6:
        if type(ipv6) != list:
            return log_and_return_json_response(request, ugettext_noop(
                'Node {node_id}: Invalid IPv6 address for NIC {iface_name}'), node_id, iface_name)
        ipv6_set = set()
        for item in ipv6:
            if type(item) != list and len(item) != 3:
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid IPv6 address for NIC {iface_name}'), node_id, iface_name)
            try:
                vlan_id = item[0]
                if vlan_id and not (0 < int(vlan_id) < 4095):
                    return log_and_return_json_response(request, ugettext_noop(
                        'Node {node_id}: Invalid VLAN ID for NIC {iface_name}'), node_id, iface_name)
            except:
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid VLAN ID for NIC {iface_name}'), node_id, iface_name)
            if not valid_IPv6(item[1]):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv6 address for NIC {iface_name}'), node_id, iface_name)
            else:
                item[1] = compress_IP(item[1])
            if not valide_netmask(item[2]):
                return log_and_return_json_response(request, ugettext_noop(
                    'Node {node_id}: Invalid IPv6 netmask for NIC {iface_name}'), node_id, iface_name)
            ipv6_set.add(item[1])
            if item[0] == '' and ip_v6 is None:
                ip_v6 = item[1]
                ipv6_netmask = item[2]
            else:
                other_ipv6_list.append(item)
        if ip_v6 is None and other_ipv6_list:
            return log_and_return_json_response(request, ugettext_noop(
                'Node {node_id}: NIC {iface_name} should have 1 IPv6 address without VLAN'), node_id, iface_name)
        if len(ipv6_set) < len(ipv6):
            return log_and_return_json_response(request, ugettext_noop(
                'Node {node_id}: NIC {iface_name} have duplicated IPv6 addresses'), node_id, iface_name)

    if roles:
        assert adapter_role != ''
        # modify iface used by Botgate, change asp_conf.json and interfaces file
        bond_cfg = get_bond_info_in_cfg().get(iface_name)

        if 'admin' in roles and len(conf_db.get_all_admin_ip()) > 1 \
                and conf_db.get_value('cluster/enhance_cluster_security', False):
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Please disable enhance cluster security before edit admin NIC {iface_name}'), node_id, iface_name)

        # verify number of ipv4 and ipv4 for botgate nic
        if not ipv4 and ('admin' in roles or 'log' in roles):
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: At least 1 IPv4 address is needed for current NIC as it has role admin or log'), node_id)
        if not (ipv4 or ipv6):
            if 'external' in roles or 'internal' in roles:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: NIC {iface_name} should have 1 IPv4 or IPv6 address'), node_id, iface_name)
            else:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: NIC {iface_name} should have 1 IPv4 address'), node_id, iface_name)
        # set ip and netmask in configuration
        ifcfg = dict()
        for role in roles:
            if ipv4:
                ifcfg[role] = {
                    'ip': ip, 'netmask': netmask,
                    'dual_ip': ip_v6, 'dual_netmask': ipv6_netmask,
                    'gateway': None, 'dual_gateway': None,
                }
            else:
                ifcfg[role] = {
                    'ip': ip_v6, 'netmask': ipv6_netmask,
                    'dual_ip': None, 'dual_netmask': None,
                    'gateway': None, 'dual_gateway': None,
                }
            if role == adapter_role:
                ifcfg[role]['service_list'] = services

        # verify gateway and ip in the same network
        if gateway:
            if not ifaces_info[iface_name]['gateway']:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Other NIC has IPv4 gateway already'), node_id)
            if not (valid_IPv4(gateway) and ip and netmask):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 gateway for NIC {iface_name}'), node_id, iface_name)
            if not is_same_subnet(gateway, ip, netmask):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: IPv4 gateway and address not in the same network for NIC {iface_name}'), node_id, iface_name)
        if ipv6_gateway:
            if not ifaces_info[iface_name]['ipv6_gateway']:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Other NIC has IPv6 gateway already'), node_id)
            if not (valid_IPv6(ipv6_gateway) and ip_v6 and ipv6_netmask):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv6 gateway for NIC {iface_name}'), node_id, iface_name)
            if not is_same_subnet(ipv6_gateway, ip_v6, ipv6_netmask):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: IPv6 gateway and address not in the same network for NIC {iface_name}'), node_id, iface_name)

        # set gateway in configuration
        gateway_role = None
        ipv6_gateway_role = None
        if 'external' in roles:
            gateway_role = 'external'
            ipv6_gateway_role = 'external'
        elif 'admin' in roles:
            gateway_role = 'admin'
            ipv6_gateway_role = 'admin'
        elif 'internal' in roles:
            gateway_role = 'internal'
            ipv6_gateway_role = 'internal'
        elif 'log' in roles:
            gateway_role = 'log'
            ipv6_gateway_role = 'log'
        elif 'keepalived' in roles:
            gateway_role = 'keepalived'
        if gateway and gateway_role:
            ifcfg[gateway_role]['gateway'] = gateway
        if ipv6_gateway and ipv6_gateway_role:
            if ifcfg[ipv6_gateway_role].get('dual_ip'):
                ifcfg[ipv6_gateway_role]['dual_gateway'] = ipv6_gateway
            else:
                ifcfg[ipv6_gateway_role]['gateway'] = ipv6_gateway

        clone_shared_config(net_conf, ifcfg)

        if 'external' in roles:
            ifcfg['external']['other_ip_list'] = other_ipv4_list + other_ipv6_list
        elif 'internal' in roles:
            ifcfg['internal']['other_ip_list'] = other_ipv4_list + other_ipv6_list
        elif 'admin' in roles:
            ifcfg['admin']['other_ip_list'] = other_ipv4_list + other_ipv6_list
        elif 'log' in roles:
            ifcfg['log']['other_ip_list'] = other_ipv4_list + other_ipv6_list

        abs_value = dict()
        node_list = {'cluster': {'node_list': {node_id: {}}}}
        for role in roles:
            node_list['cluster']['node_list'][node_id][role] = copy.deepcopy(ifcfg[role])
        if 'keepalived' not in roles and ifcfg.get('keepalived'):
            node_list['cluster']['node_list'][node_id]['keepalived'] = copy.deepcopy(ifcfg['keepalived'])
        abs_value.update(node_list)

        # set ipv4_route in configuration
        # ipv4_route is private and no need to set to zk
        if ipv4_route:
            if 'admin' not in roles:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Only admin interface could add route'), node_id)
            if not (type(ipv4_route) == list and type(ipv4_route[0]) == list and len(ipv4_route[0]) == 3 and ip and netmask):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route for NIC {iface_name}'), node_id, iface_name)
            route_network, route_netmask, route_gateway = ipv4_route[0][0], ipv4_route[0][1], ipv4_route[0][2]
            if not (route_network and valid_IPv4(route_network)):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route network for NIC {iface_name}'), node_id, iface_name)
            if not (route_netmask and valide_netmask(route_netmask)):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route netmask for NIC {iface_name}'), node_id, iface_name)
            if not (route_gateway and valid_IPv4(route_gateway)):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route gateway for NIC {iface_name}'), node_id, iface_name)
            if not is_same_subnet(route_gateway, ip, netmask):
                for item in other_ipv4_list:
                    if is_same_subnet(route_gateway, item[1], item[2]):
                        break
                else:
                    return log_and_return_json_response(request, ugettext_noop('Node {node_id}: IPv4 route and address not in the same network for NIC {iface_name}'), node_id, iface_name)

            if route_gateway == ip:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route gateway for NIC {iface_name}'), node_id, iface_name)
            for item in other_ipv4_list:
                if route_gateway == item[1]:
                    return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route gateway for NIC {iface_name}'), node_id, iface_name)
                if is_same_CIDR(item[1] + '/' + item[2], route_network + '/' + route_netmask):
                    return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route network for NIC {iface_name}'), node_id, iface_name)
            if is_same_CIDR(ip + '/' + netmask , route_network + '/' + route_netmask):
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IPv4 route network for NIC {iface_name}'), node_id, iface_name)

            ifcfg[gateway_role]['route_list'] = {
                '{}:{}:1'.format(route_network, route_netmask): {
                    'ip': route_network,
                    'netmask': route_netmask,
                    'gateway': route_gateway,
                    'private': True
                }
            }

        # set master_ips in configuration
        if 'admin' in roles and not master_ips:
            master_ips = {node_id: ip}
        if master_ips:
            if type(master_ips) != dict:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IP of master node in cluster'), node_id)
            master_cfg = {'master': {'servers': {}}}
            for k, v in master_ips.items():
                if not valid_IPv4(v):
                    return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Invalid IP of master node in cluster'), node_id)
                master_cfg['master']['servers'][k] = {'admin_ip': v, 'log_ip': None}
            abs_value.update(master_cfg)

        logger.debug('edit_iface ifcfg is:\n%s' %  json.dumps(ifcfg, encoding='utf-8'))
        logger.debug('edit_iface abs_value is:\n%s' % json.dumps(abs_value, encoding='utf-8'))

        if 'admin' in roles and org_params != new_params:
            operation_log(request, ugettext_noop('Network'), ugettext_noop("Modify"), '0', {
                'msg': ugettext_noop('Node {node_id}: Edit NIC {iface_name}'),
                'extra': {'node_id': node_id, 'iface_name': iface_name},
                'info': 'Before edit:\n {}\n\nAfter edit:\n {}'.format(json.dumps(org_params, indent=4),
                                                                     json.dumps(new_params, indent=4))})

        # apply all config by calling service manager
        if abs_value:
            stat = net_conf.apply_cfg_iface(ifcfg, abs_values=abs_value)
        else:
            stat = net_conf.apply_cfg_iface(ifcfg)
        if stat.get('task_id') is None:
            if _('No configuration has been changed!') == stat['message']:
                return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} successfully'), node_id, iface_name, '0',
                                                    info='Before edit:\n {}\n\nAfter edit:\n {}'.format(json.dumps(org_params, indent=4),
                                                                     json.dumps(new_params, indent=4)))
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} failed'), node_id, iface_name)
        task_id = stat['task_id']
        from task_queue import TaskBase
        finished, task_status, stat = ensure_task_completed(task_id)

        # fix bond configuration in interfaces file when needed
        if bond_cfg and (bond_cfg != get_bond_info_in_cfg().get(iface_name)
                         or (org_params.get('ipv4') is None and new_params.get('ipv4'))):
            exe('''sudo /usr/sbin/limited_exe fix_bond_cfg '{}' '''.format(json.dumps({iface_name: bond_cfg})), False, False)

        if task_status == TaskBase.STATUS_TIMEOUT:
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} timeout'), node_id, iface_name)
        elif task_status != TaskBase.STATUS_FINISHED:  # config task fail
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} failed'), node_id, iface_name)
        else:
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} successfully'), node_id, iface_name, '0',
                                                info='Before edit:\n {}\n\nAfter edit:\n {}'.format(json.dumps(org_params, indent=4),
                                                                     json.dumps(new_params, indent=4)))
    else:
        assert adapter_role == ''
        # modify iface not used by Botgate, only change interfaces file
        if ip and netmask:
            ipv4_and_netmask = '{}/{}'.format(ip, netmask)
        else:
            ipv4_and_netmask = '-'

        if ip_v6 and ipv6_netmask:
            ipv6_and_prefix = '{}/{}'.format(ip_v6, ipv6_netmask)
        else:
            ipv6_and_prefix = '-'

        access_nginx = params.get('access_nginx', False)
        if ifaces_info[iface_name]['access_nginx']['display']:
            if access_nginx:
                services.append('nginx')
            org_params['access_nginx'] = ifaces_info[iface_name]['access_nginx']['enabled']
            new_params['access_nginx'] = access_nginx

        asp_conf_ctrl = get_release_file('bin/asp_conf_ctrl')
        if exe('{} --logfile /var/log/asp/asp_conf_ctrl.log edit_iface {} {} {} {} {}'.format(
                asp_conf_ctrl, iface_name, ipv4_and_netmask, ipv6_and_prefix,
                ",".join("{}/{}/{}".format(item[0], item[1], item[2]) for item in (other_ipv4_list + other_ipv6_list)) or '-', ','.join(services) or '-'
        ), False, False) == 0:
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} successfully'), node_id, iface_name, '0',
                                                info='Before edit:\n {}\n\nAfter edit:\n {}'.format(json.dumps(org_params, indent=4),
                                                                     json.dumps(new_params, indent=4)))
        else:
            return log_and_return_json_response(request, ugettext_noop('Node {node_id}: Edit NIC {iface_name} failed'), node_id, iface_name)


def find_route_gwintf(networkConf, route_target_ip, route_target_mask, gw):
    #notes:
    #if admin port ip/mask is *******/********* and external ip/mask is *******/***********
    #then the ip ******* can match both admin port and external port.
    suitable_intf, suitable_ip, suitable_mask, suitable_prefix = None, None, None, -1
    for role_or_name, ip, mask in ConfDb().get_name_ip_mask_list(ipv4=':' not in gw, ipv6=':' in gw):
        if gw == ip:
            raise ValueError, networkConf.error_msg[NetworkConf.ROUTE_LIST + '/' + 'gateway'] + ' ' + gw
        if is_same_CIDR(ip+'/'+mask, route_target_ip+'/'+route_target_mask):
            raise ValueError, _('Route conflict.') + ' {}/{}'.format(ip, mask)
        if is_same_subnet(gw, ip, mask):
            prefix = mask_to_prefix(mask)
            if prefix > suitable_prefix:
                suitable_intf, suitable_ip, suitable_mask, suitable_prefix = role_or_name, ip, mask, prefix
            elif prefix == suitable_prefix and suitable_intf != role_or_name:
                raise ValueError, _('Route conflict.') + '{}'.format(gw)


    if suitable_intf is None:
        raise ValueError, networkConf.error_msg[NetworkConf.ROUTE_LIST + '/' + 'gateway'] + ' ' + gw

    return suitable_intf

def parse_post_route(request, networkConf):
    confilct_check_set = set()
    old_conf_dict, __ = ConfDb().get_route_conf_dict()
    new_conf_dict = {}
    route_changes = set()

    for field_name in request.POST.keys():
        m = re.search('^rt_ip(\d+)$', field_name)
        if m is not None:
            sufix_num = m.groups()[0]
            ip = compress_IP(request.POST.get('rt_ip' + sufix_num).strip())
            mask = request.POST.get('rt_nm' + sufix_num).strip()
            gw = compress_IP(request.POST.get('rt_gw' + sufix_num).strip())
            priv = request.POST.get('rt_priv' + sufix_num).strip().lower() == 'on'
            new_key = ip + ":" + mask + ":" + ('1' if priv else '0')

            check_route_parameters(ip, mask, gw, networkConf)
            ip_and_mask = "/".join([ip, mask])
            if ip_and_mask in confilct_check_set:
                raise ValueError, _('Route conflict.') + ' ' + ip_and_mask
            else:
                confilct_check_set.add(ip_and_mask)

            # check gw and find next hop interface...
            role_or_name = find_route_gwintf(networkConf, ip, mask, gw)
            if 'other_adapters/' in role_or_name:
                if priv:
                    path = '_private/os/network/{}/route_list/{}/'.format(role_or_name, new_key)
                else:
                    raise ValueError, networkConf.error_msg[NetworkConf.ROUTE_LIST + '/' + 'gateway'] + ' {}/{} {} '.format(ip, mask, gw) + _('needs to be a private route')
            else:
                if priv:
                    path = '_private/os/network/{}/route_list/{}/'.format(role_or_name, new_key)
                else:
                    path = 'os/network/{}/route_list/{}/'.format(role_or_name, new_key)

            new_conf_dict[path+'ip'] = ip
            new_conf_dict[path+'netmask'] = mask
            new_conf_dict[path+'gateway'] = gw
            new_conf_dict[path+'private'] = priv

            route_changes.add("  ".join([ip, mask, gw,  'private' if priv else 'public']))

    old_level_1_keys = { '/'.join(a.split('/')[:-2]) for a in old_conf_dict.keys() }
    new_level_1_keys = { '/'.join(a.split('/')[:-2]) for a in new_conf_dict.keys() }
    old_level_2_keys = { '/'.join(a.split('/')[:-1]) for a in old_conf_dict.keys() }
    new_level_2_keys = { '/'.join(a.split('/')[:-1]) for a in new_conf_dict.keys() }

    if old_conf_dict == new_conf_dict:
        return new_conf_dict, set()

    should_remove = old_level_1_keys - new_level_1_keys
    for level_1_path in should_remove:
        new_conf_dict[level_1_path] = None

    should_remove = old_level_2_keys - new_level_2_keys
    for level_2_path in should_remove:
        level_1_path = '/'.join(level_2_path.split('/')[:-1])
        if level_1_path not in new_conf_dict:
             new_conf_dict[level_2_path] = None

    return new_conf_dict, route_changes


def parse_post_dns(request, iftype, result=None):
    if result is None:
        result = {}
    dns = request.POST.get('dns', None)
    dns_error = ''
    if dns is None:
        return dns_error

    dnslist = []
    dnss = dns.split(',')
    for a_dns in dnss:
        a_dns = a_dns.strip()
        if len(a_dns) > 0:
            if not valid_IP(a_dns):
                dns_error = _('Invalid DNS.')
                raise ValueError, dns_error
            else:
                dnslist.append(compress_IP(a_dns))

    if len(dns) != 0 and len(dnss) == 0:
        dns_error = _('Invalid DNS..')
        raise ValueError, dns_error

    result.update({iftype + '/' + NetworkConf.DNS_LIST: ' '.join(dnslist)})
    return dns_error


def parse_post_ips(request, networkConf, iftype, cfgmap, result=None):
    if result is None:
        result = {}
    ips = {}

    for k, v in cfgmap.items():
        ips[k] = request.POST.get(v, None)
        if ips[k] is None:
            ips.pop(k)

    ret = networkConf.validate_cfg_iface(ips)
    if ret is not None:
        raise ValueError, networkConf.error_msg[iftype + '/' + ret]

    if len(ips) != 0:
        result.update({iftype: ips})
    return ''


def parse_post_admin_ips(request, networkConf, result=None):
    if result is None:
        result = {}
    cfgmap = {
        NetworkConf.PORT: 'admin_port'}
    return parse_post_ips(request, networkConf, NetworkConf.IF_ADMIN, cfgmap, result)

def parse_webconsole_host_white_lists(request, result=None):
    if result is None:
        result = {}

    ret = request.POST.get('host_white_lists', '')
    result.update({'nginx/web_console/host_white_lists':ret.strip()})

def parse_post_snmp(request, prefix, result=None):
    if result is None:
        result = {}
    ips = {}

    cfgmap = {
        NetworkConf.ON: 'snmp_state',
        NetworkConf.PORT: 'snmp_port',
        NetworkConf.TRAP_IP: 'snmp_trap_ip',
        NetworkConf.TRAP_PORT: 'snmp_trap_port',
        NetworkConf.VER: 'snmp_version',
        NetworkConf.COMMUNITY: 'snmp_community',
        NetworkConf.USER: 'snmp_v3user',
        NetworkConf.SEC_LEVEL: 'snmp_v3SecLevel',
        NetworkConf.AUTH_ALGO: 'snmp_v3AuthAlgo',
        NetworkConf.AUTH_PWD: 'snmp_v3AuthPwd',
        NetworkConf.PRIV_ALGO: 'snmp_v3PrivAlgo',
        NetworkConf.PRIV_PWD: 'snmp_v3PrivPwd'
    }

    for k, v in cfgmap.items():
        ips[k] = request.POST.get(v, None)
        if v == 'snmp_trap_ip':
            ips[k] = compress_IP(ips[k])
        if ips[k] is None:
            ips.pop(k)

    if len(ips) != 0:
        result.update({prefix + NetworkConf.SNMP: ips})
    return ''

def check_user_defined_reserved_ports(user_defined_reserved_ports):
    format_error = _('Incorrect format: multiple ports or port ranges must be seperated with commas. Port ranges must be in the format of start port-end port, and port value be a 1024-65000 integer.')
    port_range_error = _('Port value must be an integer from 1024 to 65000.')
    port_count_error = _('The number of ports exceeded 3000.')
    port_duplicate_error = _('Duplicate ports entered.')
    port_start_end_error = _('Start port must be smaller than end port in port ranges.')

    user_defined_reserved_ports = user_defined_reserved_ports.strip()
    error_msg = None
    user_defined_reserved_ports_result = []
    port_set = set()

    if len(user_defined_reserved_ports) > 0:
        if len(user_defined_reserved_ports) > 1024:
            raise ValueError, format_error

        ranges = user_defined_reserved_ports.split(",")
        for port_range in ranges:
            port_range = port_range.strip()
            if port_range in user_defined_reserved_ports_result:
                error_msg = port_duplicate_error
                break

            user_defined_reserved_ports_result.append(port_range)

            port_range = port_range.split('-')
            if len(port_range) > 2:
                error_msg = format_error
                break

            start_port = port_range[0]
            if not start_port.isdigit():
                error_msg = format_error
                break

            start_port = int(start_port)
            if start_port < 1024 or start_port > 65000:
                error_msg = port_range_error
                break

            if len(port_range) == 2:
                end_port = port_range[1]
                if not end_port.isdigit():
                    error_msg = format_error
                    break

                end_port = int(end_port)
                if end_port < 1024 or end_port > 65000:
                    error_msg = port_range_error
                    break

                if start_port > end_port:
                    error_msg = port_start_end_error
                    break

                if end_port - start_port + 1 > 3000:
                    error_msg = port_count_error
                    break

                port_set.update(range(start_port, end_port + 1))
            else:
                port_set.add(start_port)

            if len(port_set) > 3000:
                error_msg = port_count_error
                break

    if error_msg is not None:
        raise ValueError, error_msg

    return user_defined_reserved_ports_result

def parse_user_defined_reserved_ports(request, result=None):
    if result is None:
        result = {}

    user_defined_reserved_ports = request.POST.get('user_defined_reserved_ports', '')
    user_defined_reserved_ports = check_user_defined_reserved_ports(user_defined_reserved_ports)
    result.update({'os/network/user_defined_reserved_ports': ','.join(user_defined_reserved_ports)})

def check_keepalived_ipv4(keepalived_external_vip, dict_key, all_ip, conf_db, result):
    keepalived_external_vip = compress_IP(keepalived_external_vip)
    if keepalived_external_vip:
        if not valid_IP(keepalived_external_vip):
            VIP_error = _('Invalid virtual IP address.')
            raise ValueError, VIP_error
        if not valid_IPv4(keepalived_external_vip):
            VIP_error = _('Virtual IP address only supports IPV4.')
            raise ValueError, VIP_error

        if keepalived_external_vip in all_ip:
            VIP_error = _('IP address conflict.')
            raise ValueError, VIP_error

        all_ip.add(keepalived_external_vip)
        result.update({dict_key : keepalived_external_vip})
    else:
        result.update({dict_key: keepalived_external_vip})

def check_keepalived_netmask(keepalived_external_netmask, dict_key, result):
    if keepalived_external_netmask:
        if not valide_netmask(keepalived_external_netmask):
            mask_error = _('Invalid virtual netmask.')
            raise ValueError, mask_error
        else:
            result.update({dict_key :keepalived_external_netmask})
    else:
        result.update({dict_key :keepalived_external_netmask})

def check_keepalived_vrid(keepalived_external_vrid, dict_key, result):
    if keepalived_external_vrid:
        if int(keepalived_external_vrid) > 255 or int(keepalived_external_vrid) < 1:
            vrid_error = _('VRID range 1~255.')
            raise ValueError, vrid_error
        else:
            result.update({dict_key:keepalived_external_vrid})

def check_keepalived_ipv6(keepalived_external_vip_ipv6, dict_key, all_ip, result):
    keepalived_external_vip_ipv6 = compress_IP(keepalived_external_vip_ipv6)
    if keepalived_external_vip_ipv6:
        if not valid_IP(keepalived_external_vip_ipv6):
            VIP_error = _('Invalid virtual IP address.')
            raise ValueError, VIP_error

        if keepalived_external_vip_ipv6 in all_ip:
            VIP_error = _('IPv6 address conflict.')
            raise ValueError, VIP_error

        all_ip.add(keepalived_external_vip_ipv6)
        result.update({dict_key:keepalived_external_vip_ipv6})
    else:
        result.update({dict_key: keepalived_external_vip_ipv6})

def check_keepalived_ipv6_prefix(keepalived_external_vip_ipv6_prefix, dict_key, result):
    if keepalived_external_vip_ipv6_prefix:
        if not valide_netmask(keepalived_external_vip_ipv6_prefix):
            mask_error = _('Invalid virtual Prefix Length .')
            raise ValueError, mask_error
        else:
            result.update({dict_key:keepalived_external_vip_ipv6_prefix})
    else:
        result.update({dict_key: keepalived_external_vip_ipv6_prefix})


def parse_post_keepalived(request, prefix, result=None):
    if result is None:
        result = {}

    conf_db = ConfDb()
    all_ip = set()
    for _, node in conf_db.get_all('cluster/node_list').items():
        for _, intf in node.items():
            if type(intf) is dict:
                ip = intf.get("ip")
                all_ip.add(ip) if ip else ""
                ip = intf.get("dual_ip")
                all_ip.add(ip) if ip else ""

    only_listen_vip = request.POST.get('only_listen_vip')
    if only_listen_vip is not None:
        result.update({prefix + 'keepalived/external/only_listen_vip': only_listen_vip == 'on'})

    check_keepalived_ipv4(request.POST.get('keepalived_external_vip', None), prefix + 'keepalived/external/virtual_ip', all_ip, conf_db, result)
    check_keepalived_netmask(request.POST.get('keepalived_external_netmask', None), prefix + 'keepalived/external/virtual_netmask', result)
    check_keepalived_vrid(request.POST.get('keepalived_external_vrid', None), prefix + 'keepalived/external/vrrp_id', result)
    check_keepalived_ipv6(request.POST.get('keepalived_external_vip_ipv6', None), prefix + 'keepalived/external/virtual_ip_ipv6', all_ip, result)
    check_keepalived_ipv6_prefix(request.POST.get('keepalived_external_vip_ipv6_prefix', None), prefix + 'keepalived/external/virtual_ip_ipv6_prefix', result)

    check_keepalived_ipv4(request.POST.get('keepalived_external_vip2', None), prefix + 'keepalived/external/virtual2_ip', all_ip, conf_db, result)
    check_keepalived_netmask(request.POST.get('keepalived_external_netmask2', None), prefix + 'keepalived/external/virtual2_netmask', result)
    check_keepalived_vrid(request.POST.get('keepalived_external_vrid2', None), prefix + 'keepalived/external/vrrp2_id', result)
    check_keepalived_ipv6(request.POST.get('keepalived_external_vip2_ipv6', None), prefix + 'keepalived/external/virtual2_ip_ipv6', all_ip, result)
    check_keepalived_ipv6_prefix(request.POST.get('keepalived_external_vip2_ipv6_prefix', None), prefix + 'keepalived/external/virtual2_ip_ipv6_prefix', result)

    return

def clone_shared_config(networkConf, result):
    intfs = ['internal', 'external', 'admin', 'log', 'keepalived']
    for item in intfs:
        shareName = networkConf.get_cfg_iface(item).get('shared_with')
        if shareName == '':
            continue

        shareIntf = result.get(shareName)
        if shareIntf is None:
            continue

        intf = result.get(item)
        if intf is None:
            result.update({item: {}})
            intf = result.get(item)

        intf.update({'ip': shareIntf['ip']})
        if shareIntf.get('netmask'):
            intf.update({'netmask': shareIntf['netmask']})




def update_on_exist(intfs, path, is_move, func):
    path = path.split('/')
    value = intfs.get(path[0])
    if is_move:
        value = None if value is None else value.pop(path[1], None)
    else:
        value = None if value is None else value.get(path[1])

    if value is not None:
        func(value)


def get_network_op_detail(network_conf, abs_values, route_changes):
    """
    获取网络相关操作日志详情
    @param network_conf:
    @param abs_values:
    @param route_changes:
    @return:
    """
    detail_list = []

    # os/network/external/dns_list
    if 'os/network/external/dns_list' in abs_values:
        old_value = network_conf.get_value('os/network/external/dns_list', '', is_abs=True)
        new_value = abs_values['os/network/external/dns_list']
        if new_value != old_value:
            if not new_value:
                detail_list.append({'msg': ugettext_noop('Delete DNS server configuration.')})
            else:
                detail_list.append({
                    'msg': ugettext_noop('Modify DNS server configuration to \'{new}\'.'),
                    'extra': {'new': new_value, },
                })

    # nginx/web_console/port
    if 'nginx/web_console/port' in abs_values:
        old_value = network_conf.get_value('nginx/web_console/port', '', is_abs=True)
        new_value = abs_values['nginx/web_console/port']
        if new_value != old_value:
            detail_list.append({
                'msg': ugettext_noop('Modify management interface port number to \'{new}\'.'),
                'extra': {'new': new_value, },
            })

    # os/network/snmp
    if 'os/network/snmp' in abs_values:
        old_value = network_conf.get_conf().get_all('os/network/snmp', {})
        new_value = abs_values['os/network/snmp']
        old_on = old_value.get('on', '') == 'snmp_on'
        new_on = new_value.get('on', '') == 'snmp_on'
        op = get_op_by_switch(old_on, new_on)
        if op == 'Modify':
            # 只有在开关处于打开状态才判定
            change_set = compare_dict(old_value, new_value)
            if change_set:
                # config change
                change_list = []
                for k, v in change_set:
                    v = list(v)
                    if k.lower().endswith('pwd'):
                        v[0] = v[1] = '*' * 6
                    change_list.append('{0}: OLD({1}) --->  NEW({2})'.format(
                        k, *v
                    ))
                detail_list.append({
                    'op': op,
                    'msg': ugettext_noop('Modify SNMP configuration.'),
                    'info': '\n'.join(change_list)
                })
        elif new_on != old_on:
            detail_list.append({
                'op': op,
                'msg': ugettext_noop('Modify SNMP configuration.'),
            })

    # os/network/ntp_server
    if 'os/network/ntp_server' in abs_values:
        old_value = network_conf.get_value('os/network/ntp_server', '', is_abs=True)
        new_value = abs_values['os/network/ntp_server']
        if new_value != old_value:
            if not new_value:
                detail_list.append({'msg': ugettext_noop('Delete NTP Server.')})
            else:
                detail_list.append({
                    'msg': ugettext_noop('Modify NTP Server from \'{new}\'.'),
                    'extra': {'new': new_value, },
                })

    # os/network/user_defined_reserved_ports
    ports_changes = []
    if 'os/network/user_defined_reserved_ports' in abs_values:
        old_value = network_conf.get_value('os/network/user_defined_reserved_ports', '', is_abs=True)
        new_value = abs_values['os/network/user_defined_reserved_ports']
        if new_value != old_value:
            ports_changes.append('user_defined_reserved_ports: OLD({0}) --->  NEW({1})'.format(old_value, new_value))

    if len(ports_changes):
        detail_list.append({
            'msg': ugettext_noop('Modified reserved ports settings'),
            'info': '\n'.join(ports_changes)
        })

    # route_list
    if route_changes:
        detail_list.append({
            'msg': ugettext_noop('Modify network route.'),
            'info': "\n".join(route_changes),
        })

    # nginx/web_console/host_white_lists
    if 'nginx/web_console/host_white_lists' in abs_values:
        old_value = network_conf.get_value('nginx/web_console/host_white_lists', is_abs=True)
        new_value = abs_values['nginx/web_console/host_white_lists']
        if new_value != old_value and old_value is not None:
            detail_list.append({
                'msg': ugettext_noop('Set WebConsole Host White Lists configuration.'),
            })

    # os/network/keepalived
    change_list = []
    conf = ConfDb()
    for k, v in abs_values.items():
        if k and k.startswith('os/network/keepalived/') and 'route_list' not in k:
            old_value = conf.get_value(k)
            if old_value is None and conf.is_ha():
                old_value = ''
            if v != old_value:
                change_list.append('{0}: OLD({1}) --->  NEW({2})'.format(
                    k.split('/')[-1], old_value, v))
    if len(change_list):
        detail_list.append({
            'msg': ugettext_noop('Modify hot standby configuration.'),
            'info': '\n'.join(change_list)
        })

    return detail_list


def handle_post_for_network_config_box(request, networkConf, intf):
    result = {}
    error = ''
    abs_result = {}

    try:
        parse_post_admin_ips(request, networkConf, result)
        parse_webconsole_host_white_lists(request, abs_result)
        route_conf_dict, route_changes = parse_post_route(request, networkConf)
        abs_result.update(route_conf_dict)
        parse_post_dns(request, 'os/network/' + intf, abs_result)
        parse_post_snmp(request, 'os/network/', abs_result)

        ntp_server = compress_IP(request.POST.get('ntp_server', None))
        abs_result.update({'os/network/ntp_server':ntp_server})

        parse_user_defined_reserved_ports(request, abs_result)

        parse_post_keepalived(request, 'os/network/', abs_result)

    except ValueError as e:
        error = e.message

    if error != '':
        return {'error': error}

    # move admin port onto nginx/web_console/port which will trigger web_console module's action
    update_on_exist(result, 'admin/port', True, lambda x: abs_result.update({'nginx/web_console/port': x}))
    op_list = []
    try:
        op_list = get_network_op_detail(networkConf, abs_result, route_changes)
    except Exception as e:
        logger.error('get networking operation log failed with {}'.format(e))

    logger.info('Apply network config changing: ' + str(abs_result))
    ret = networkConf.apply_cfg_iface(result, abs_values=abs_result)
    success = 1
    if ret['result'] == 'IN_QUEUE':
        success = 0
        logger.info('Apply network config successfully')
    elif ret['result'] == 'SAME_CONFIG':
        logger.info('Apply network config successfully, same config')
    else:
        logger.error('Failed to apply network config {0}, >>>>>\n{1}<<<<<<'.format(ret.get('result'), ret.get('stat')))

    for op_detail in op_list:
        op = op_detail.pop('op', ugettext_noop('Modify'))
        operation_log(request, ugettext_noop('Network'), op, success, op_detail)

    return ret


def query_info(key):
    if key == 'life_id':
        return str(os.getpgid(0))

    return None


def checkRegValidation(regs):
    for reg in regs:
        try:
            re.compile(reg)
        except:
            error = _("Regex(%s) is incorrect") % reg
            return error
    return True


def view_phoenix_log_setting_set(set_value):
    conf = ConfDb()
    log_server = 'sailfish' if conf.get_value('report_type') == 'sailfish' else 'log_server'
    log_conf = LogserverConf()
    error = ''

    log_es_sperate = True
    log_disk_total = 0
    es_disk_total = 0
    cluster_state = get_cluster_node_state_list()
    for node in cluster_state:
        if 'log_archive_server' in node['role'] and log_server in node['role']:
            log_es_sperate = False
        if 'log_archive_server' in node['role']:
            log_disk_total = node['var_size_m']
        if log_server in node['role']:
            es_disk_total = min(es_disk_total, node['var_size_m']) if es_disk_total else node['var_size_m']

    log_disk_total = log_disk_total / 1024
    es_disk_total = es_disk_total / 1024
    origin_log_disk_cur = BaseConf().get_value('rsyslog/logrotate/disk_size', is_abs=True)
    origin_es_disk_cur = BaseConf().get_value('rotate_es/max_access_log_index_size', is_abs=True)

    # for this case: import config
    if not log_es_sperate:
        if origin_log_disk_cur + origin_es_disk_cur > log_disk_total:
            origin_log_disk_cur = 0
            origin_es_disk_cur = 0

    if (set_value.has_key('es_size')):
        es_size = set_value['es_size']
        if log_es_sperate:
            if es_size > es_disk_total:
                return False, _('Disk space {0}G for Log Analysis is bigger than the available {1}G!').format(es_size, es_disk_total)
        else:
            if es_size + origin_log_disk_cur > log_disk_total:
                return False, _('Disk space {0}G for Log Analysis and {1}G for Log Archiving is bigger than the available {2}G!').format(es_size, origin_log_disk_cur, log_disk_total)

        index_count = 1 if es_size <= ES_SIZE_PER_INDEX else (es_size / ES_SIZE_PER_INDEX)
        code, output, cmd_line = BaseConf().set_asp_conf_values(
            {'rotate_es/max_access_log_index_count': index_count,
             'rotate_es/max_access_log_index_size': es_size,
             'rotate_es/max_attack_analyzer_index_count': index_count,
             'rotate_es/max_service_status_index_count': index_count})
        if code == 0:
            logger.info('* Set ES disk size{} count{} successfully.'.format(es_size, index_count))

        es_server_count = len(conf.get_es_servers())
        if es_server_count == 0:
            logger.error('* Set ES Server is NULL! *')
        else:
            index_count = 1 if es_size <= ES_SIZE_PER_INDEX else (es_size / ES_SIZE_PER_INDEX)
            index_count *= es_server_count
            code, output, cmd_line = BaseConf().set_asp_conf_values(
                {'rotate_es/max_access_log_index_count': index_count,
                 'rotate_es/max_access_log_index_size': es_size,
                 'rotate_es/max_service_status_index_count': index_count})
            if code == 0:
                logger.info('* Set ES disk size{} count{} successfully.'.format(es_size, index_count))
            else:
                error = _('Failed to set disk space for Log Analysis. Error code: {0}, cmd: {1}, message: {2}').format(code,
                                                                                                      cmd_line,
                                                                                                      output)
                logger.error(error)

    if (set_value.has_key('log_size')):
        log_size = set_value['log_size']

        if log_es_sperate:
            if log_size > log_disk_total:
                return False, _('Disk space {0}G for Log Archiving is bigger than the available {1}G!').format(log_size, log_disk_total)
        else:
            if log_size + origin_es_disk_cur > log_disk_total:
                return False, _('Disk space {0}G for Log Analysis and {1}G for Log Archiving is bigger than the available {2}G!').format(origin_es_disk_cur, log_size, log_disk_total)

        log_count = 1 if log_size <= LOG_SIZE_PER_INDEX else (log_size / LOG_SIZE_PER_INDEX)
        log_count *= 3
        code, output, cmd_line = BaseConf().set_asp_conf_values(
            {'rsyslog/logrotate/count': log_count,
             'rsyslog/logrotate/disk_size': log_size})
        if code == 0:
            logger.info('* Set log archive disk size{} count{} successfully.'.format(log_size, log_count))
        else:
            error = _('Failed to set disk space for Log Archiving is bigger than the available. Error code: {0}, cmd: {1}, messages: {2}').format(code,
                                                                                                           cmd_line,
                                                                                                           output)
            logger.error(error)

    if (set_value.has_key('enabled_log_export')):
        if set_value['enabled_log_export']:
            log_conf.enable_export_log(set_value['log_export_address'], set_value['log_export_port'], set_value['log_export_proto'], set_value['log_export_direct'])
        else:
            log_conf.disable_export_log()

    if set_value.has_key('enabled_operate_log_export'):
        if set_value['enabled_operate_log_export'] is not None:
            log_conf.enabled_export_operate_log(
                set_value['enabled_operate_log_export'],
                set_value['operate_log_export_address'],
                set_value['operate_log_export_port'],
                set_value['operate_log_export_proto']
            )
        else:
            log_conf.disable_export_operate_log()

    if (set_value.has_key('enabled_parsedlog_export')):
        set_value['parsedlog_export_servers'] = json.loads(set_value['parsedlog_export_servers'])
        if set_value['enabled_parsedlog_export']:
            if verify_parsedlog_export_servers(set_value['parsedlog_export_servers']):
                log_conf.enable_export_parsedlog(1, set_value['parsedlog_export_servers'],
                                                 1 if set_value['parsedlog_rr_mode'] else 0,
                                                 set_value['external_log_level'], set_value['external_log_type'])
            else:
                error = _('Incorrect destination address')
        else:
            log_conf.enable_export_parsedlog(0, set_value['parsedlog_export_servers'],
                                             1 if set_value['parsedlog_rr_mode'] else 0,
                                             set_value['external_log_level'], set_value['external_log_type'])

    if set_value.has_key('enabled_full_traffic_filter_log'):
        BaseConf().set_asp_conf_values({"logserver/full_traffic_filter/enabled": set_value["enabled_full_traffic_filter_log"]})

    if set_value.has_key('enable_receive_parsed_access_log'):
        BaseConf().set_asp_conf_values({"receive_parsed_access_log/enabled": set_value["enable_receive_parsed_access_log"]})

    if (set_value.has_key('log_level')):
        log_conf.set_log_classification(set_value['log_level'], set_value['log_type'])

    if (set_value.has_key('multi_processer')):
        code, output, cmd_line = BaseConf().set_asp_conf_values(
            {'logserver/multi_processer': set_value['multi_processer']})
        if code == 0:
            logger.info('* Set log multi processer {} successfully.'.format(set_value['multi_processer']))
        else:
            error = _(
                'Failed to set log multi processer. Error code: {0}, cmd: {1}, messages: {2}').format(
                code,
                cmd_line,
                output)

    if (set_value.has_key('enabled_archivelog_filter')):
        code, output, cmd_line = BaseConf().set_asp_conf_values(
            {'logserver/log_archive/enabled_archivelog_filter': parse_bool(set_value['enabled_archivelog_filter']),
            'logserver/log_archive/archivelog_filter_percent':set_value['archivelog_filter_percent']})
        if code == 0:
            logger.info('* Set  archivelog filter {} successfully.'.format(set_value['archivelog_filter_percent']))
        else:
            error = _(
                'Failed to set  archivelog filter. Error code: {0}, cmd: {1}, messages: {2}').format(
                code,
                cmd_line,
                output)

    if (set_value.has_key('phoenix_extra_indexes')):
        ip_list = set_value['phoenix_extra_indexes']
        code, output, cmd_line = BaseConf().set_asp_conf_values(
            {'phoenix/phoenix_extra_indexes': ip_list})
        if code == 0:
            logger.info('* Set  phoenix_extra_indexes {} successfully.'.format(ip_list))
        else:
            error = _(
                'Failed to set  phoenix_extra_indexes. Error code: {0}, cmd: {1}, messages: {2}').format(
                code,
                cmd_line,
                output)

    if error:
        return False, error
    else:
        return True, ''


def verify_parsedlog_export_servers(servers):
    try:
        if type(servers) is not list:
            return False
        for server in servers:
            server['ip'] = compress_IP(server['ip'])
            if server['ip'] == '' or server['port'] == '' or server['proto'] == '':
                return False

            if server['proto'] == 'kafka':
                topic = server.get('topic', None)
                if topic is not None:
                    pattern = re.compile(r"^[a-zA-Z0-9\._\-]{,249}$")
                    if pattern.match(topic) is None:
                        return False
                else:
                    return False

        return True
    except Exception as e:
        logger.error("Verify parsedlog export servers error with {}".format(e))
        return False


def is_all_phoenix_online():
    conf_db = ConfDb()
    try:
        ret_phoenix_nodes = query_online_phoenix_nodes()
    except:
        ret_phoenix_nodes = {}

    conf_phoenix_node = conf_db.get_phoenix_servers()
    node_list = ret_phoenix_nodes.get('node_list', [])
    all_phoenix_online = True
    if len(node_list) != len(conf_phoenix_node):
        all_phoenix_online = False
    return all_phoenix_online


def view_phoenix_log_setting_get():
    log_conf = LogserverConf()
    enabled_log_export = log_conf.is_log_export_enabled()
    log_export_address = log_conf.get_export_log_address() or ''
    log_export_port = log_conf.get_export_log_port() or ''
    log_export_proto = log_conf.get_export_log_proto() or 'udp'
    log_export_direct = log_conf.get_export_log_direct() or False

    enabled_operate_log_export = log_conf.is_operate_log_export_enabled()
    operate_log_export_address = log_conf.get_export_operate_log_address()
    operate_log_export_port = log_conf.get_export_operate_log_port()
    operate_log_export_proto = log_conf.get_export_operate_log_proto()

    enabled_parsedlog_export = log_conf.is_parsed_log_export_enable()
    parsedlog_rr_mode = log_conf.is_parsed_log_export_rr()
    log_remote_address = log_conf.get_export_parsedlog_address()
    if type(log_remote_address) is not list:  # old config
        log_remote_port = log_conf.get_export_parsedlog_port()
        log_remote_proto = log_conf.get_export_parsedlog_proto() or "udp"
        log_remote_kafka_topic = ''  # TODO log_conf.get_export_parsedlog_kafka_topic() or "udp"
        parsedlog_export_servers = [{'ip': log_remote_address, 'port': log_remote_port, 'proto': log_remote_proto, 'topic': log_remote_kafka_topic}]
    else:
        parsedlog_export_servers = log_remote_address

    log_level = log_conf.get_export_parsed_level()
    log_type = log_conf.get_export_parsed_type()

    conf = ConfDb()
    log_server = 'sailfish' if conf.get_value('report_type') == 'sailfish' else 'log_server'

    external_log_level = conf.get_value('logserver/parsedlog_export/external_level', 'debug' if is_debug_install() else 'advanced')
    external_log_type = conf.get_value('logserver/parsedlog_export/external_type', 'all')

    es_size = conf.get_value('rotate_es/max_access_log_index_size')
    log_size = conf.get_value('rsyslog/logrotate/disk_size')

    cluster_state = get_cluster_node_state_list()
    has_logarchive = 0
    logarchive_disk = 0
    has_es = 0
    es_disk = 9999999999
    is_master = 0

    base_conf = BaseConf()
    node_id = base_conf.get_value('_private/node_id')

    multi_processer = conf.get_value('logserver/multi_processer')

    enabled_archivelog_filter = conf.get_value('logserver/log_archive/enabled_archivelog_filter', False)
    archivelog_filter_percent = conf.get_value('logserver/log_archive/archivelog_filter_percent', 0)

    from views_proxy import is_upstream_editable
    upstream_editable = is_upstream_editable()

    for node in cluster_state:
        if 'log_archive_server' in node['role']:
            has_logarchive = 1
            logarchive_disk = node['var_size_m']
        if log_server in node['role']:
            has_es += 1
            es_disk = min(es_disk, node['var_size_m']) if es_disk else node['var_size_m']

        if 'master_server' in node['role']:
            if node_id == node['id']:
                is_master = 1

    phoenix_extra_indexes = conf.get_value('phoenix/phoenix_extra_indexes', [''])
    phoenix_extra_indexes = ','.join(phoenix_extra_indexes)

    sailfish_external_node_list = conf.get_value('sailfish/external_node_list',[])
    sailfish_allow_access_list  = conf.get_value('sailfish/allow_access_list',[])
    enabled_full_traffic_filter_log = conf.get_value('logserver/full_traffic_filter/enabled', False)
    enable_receive_parsed_access_log = conf.get_value('receive_parsed_access_log/enabled', False)

    desensitization_conf = log_conf.get_desensitization_conf()

    result = {'es_size': es_size,
              'log_size': log_size,
              'has_logarchive': has_logarchive,
              'logarchive_disk': logarchive_disk / 1024,
              'has_es': has_es,
              'es_disk': es_disk / 1024,
              'enabled_log_export': enabled_log_export,
              'log_export_address': log_export_address,
              'log_export_port': log_export_port,
              'log_export_proto': log_export_proto,
              'log_export_direct': log_export_direct,
              'enabled_operate_log_export': enabled_operate_log_export,
              'operate_log_export_address': operate_log_export_address,
              'operate_log_export_port': operate_log_export_port,
              'operate_log_export_proto': operate_log_export_proto,
              'enabled_parsedlog_export': enabled_parsedlog_export,
              'parsedlog_rr_mode': parsedlog_rr_mode,
              'parsedlog_export_servers': parsedlog_export_servers,
              'log_level': log_level,
              'log_type': log_type,
              'external_log_level': external_log_level,
              'external_log_type': external_log_type,
              'multi_processer': multi_processer,
              'enabled_archivelog_filter': enabled_archivelog_filter,
              'archivelog_filter_percent': archivelog_filter_percent,
              'phoenix_extra_indexes': phoenix_extra_indexes,
              "sailfish_external_node_list": sailfish_external_node_list,
              "sailfish_allow_access_list": sailfish_allow_access_list,
              "enabled_full_traffic_filter_log": enabled_full_traffic_filter_log,
              "enable_receive_parsed_access_log": enable_receive_parsed_access_log,
              "desensitization_conf": desensitization_conf,
             }

    return result


def is_int(s):
    try:
        int(s)
        return True
    except ValueError:
        return False

    return False


def get_op_by_switch(old_enabled, new_enabed):
    op_modify = ugettext_noop('Modify')
    op_open = ugettext_noop('Enable')
    op_close = ugettext_noop('Disable')
    if not old_enabled and new_enabed:
        return op_open
    elif old_enabled and new_enabed:
        return op_modify
    else:
        return op_close


def get_log_settings_op_detail(old_conf_values, conf_values):
    op_details = []
    op_modify = ugettext_noop('Modify')
    op_open = ugettext_noop('Enable')
    op_close = ugettext_noop('Disable')
    if 'es_size' in conf_values:
        op_details.append({
            'msg': ugettext_noop('Set max disk space for log analysis to {size}GB.'),
            'extra': {
                'size': conf_values['es_size'],
            },
        })

    if 'log_size' in conf_values:
        op_details.append({
            'op': op_modify,
            'msg': ugettext_noop('Set max disk space for log archiving to {size}GB.'),
            'extra': {
                'size': conf_values['log_size'],
            }
        })

    if 'enabled_operate_log_export' in conf_values:
        old, new = old_conf_values['enabled_operate_log_export'], conf_values['enabled_operate_log_export']
        op = get_op_by_switch(old, new)
        if op == 'Enable':
            op_details.append({'op': op, 'msg': ugettext_noop('Enable the operate log output configuration.')})
        elif op == 'Disable':
            op_details.append({'op': op, 'msg': ugettext_noop('Disable the operate log output configuration.')})
        else:
            op_details.append({'op': op, 'msg': ugettext_noop('Modify the operate log output configuration.')})

    if 'enabled_log_export' in conf_values:
        old, new = old_conf_values['enabled_log_export'], conf_values['enabled_log_export']
        op = get_op_by_switch(old, new)
        if op == 'Enable':
            op_details.append({'op': op, 'msg': ugettext_noop('Enable the original access log output configuration.')})
        elif op == 'Disable':
            op_details.append({'op': op, 'msg': ugettext_noop('Disable the original access log output configuration.')})
        else:
            op_details.append({'op': op, 'msg': ugettext_noop('Modify the original access log output configuration.')})
        if new:
            old, new = old_conf_values['log_export_direct'], conf_values['log_export_direct']
            if not old and new:
                op_details.append({
                    'op': op_open,
                    'msg': ugettext_noop('Enable the original access log output directly through the proxy node.')
                })
            elif old and not new:
                op_details.append({
                    'msg': ugettext_noop('Disable the original access log output directly through the proxy node.'),
                    'op': op_close,
                })
            else:
                pass

    if 'enabled_parsedlog_export' in conf_values:
        old, new = old_conf_values['enabled_parsedlog_export'], conf_values['enabled_parsedlog_export']
        op = get_op_by_switch(old, new)
        if op == 'Enable':
            op_details.append({'op': op, 'msg': ugettext_noop('Enable the formatted access log output configuration.'), })
        elif op == 'Disable':
            op_details.append({'op': op, 'msg': ugettext_noop('Disable the formatted access log output configuration.'), })
        else:
            op_details.append({'op': op, 'msg': ugettext_noop('Modify the formatted access log output configuration.'), })

    if 'log_level' in conf_values:
        op_details.append({
            'msg': ugettext_noop('Set the report log filter configuration.'),
        })

    if 'enabled_archivelog_filter' in conf_values:
        old, new = old_conf_values['enabled_archivelog_filter'], conf_values['enabled_archivelog_filter']
        op = get_op_by_switch(old, new)
        if op == 'Enable':
            op_details.append({'op': op, 'msg': ugettext_noop('Enable the archive log filter configuration.')})
        elif op == 'Disable':
            op_details.append({'op': op, 'msg': ugettext_noop('Disable the archive log filter configuration.')})
        else:
            op_details.append({'op': op, 'msg': ugettext_noop('Modify the archive log filter configuration.')})

    if 'enabled_full_traffic_filter_log' in conf_values:
        new = conf_values['enabled_full_traffic_filter_log']
        if new:
            op_details.append({'op': 'Enable', 'msg': ugettext_noop('Enable the full traffic log.')})
        else:
            op_details.append({'op': 'Disable', 'msg': ugettext_noop('Disable the full traffic log.')})

    if 'enable_receive_parsed_access_log' in conf_values:
        new = conf_values['enable_receive_parsed_access_log']
        if new:
            op_details.append({'op': 'Enable', 'msg': ugettext_noop('Enable the receive parsed access log.')})
        else:
            op_details.append({'op': 'Disable', 'msg': ugettext_noop('Disable the receive parsed access log.')})

    if 'phoenix_extra_indexes' in conf_values:
        old_indexs, new_indexs = old_conf_values['phoenix_extra_indexes'], conf_values['phoenix_extra_indexes']
        old_indexs = set(filter(lambda d: d, old_indexs.split(',')))
        new_indexs = set(filter(lambda d: d, new_indexs))
        d, n = [], []
        for ip in (old_indexs ^ new_indexs):
            if ip in old_indexs:
                d.append(ip)
            else:
                n.append(ip)

        if n and not d:
            op_msg = ugettext_noop('Modify the storage node extension configuration, add node: {add_nodes}.')
        elif d and not n:
            op_msg = ugettext_noop('Modify the storage node extension configuration, delete node: {del_nodes}.')
        else:
            op_msg = ugettext_noop(
                'Modify the storage node extension configuration, add node: {add_nodes}, delete node: {del_nodes}.'
            )

        op_details.append({
            'op': op_modify,
            'msg': op_msg,
            'extra': {
                'add_nodes': ','.join(n),
                'del_nodes': ','.join(d),
            }
        })

    return op_details


def view_phoenix_log_setting(request):
    conf_value = {}
    old_conf_value = view_phoenix_log_setting_get()
    if request.method == "POST":
        param = json.loads(request.body)

        if 'es_size' in param:
            conf_value['es_size'] = int(str(param['es_size']).split('.')[0])

        if 'log_size' in param:
            conf_value['log_size'] = int(str(param['log_size']).split('.')[0])

        if 'enabled_log_export' in param:
            enabled_log_export = param['enabled_log_export']
            if enabled_log_export is not None:
                conf_value['enabled_log_export'] = enabled_log_export
                conf_value['log_export_address'] = compress_IP(param['log_export_address'] or '')
                conf_value['log_export_port'] = param['log_export_port'] or ''
                conf_value['log_export_proto'] = param['log_export_proto'] or ''
                conf_value['log_export_direct'] = param['log_export_direct']

        if 'enabled_operate_log_export' in param:
            enabled_operate_log_export = param['enabled_operate_log_export']
            if enabled_operate_log_export is not None:
                conf_value['enabled_operate_log_export'] = enabled_operate_log_export
                conf_value['operate_log_export_address'] = compress_IP(param['operate_log_export_address'] or '')
                conf_value['operate_log_export_port'] = param['operate_log_export_port'] or ''
                conf_value['operate_log_export_proto'] = param['operate_log_export_proto'] or ''

        if 'enabled_parsedlog_export' in param:
            enabled_parsedlog_export = param['enabled_parsedlog_export']
            if enabled_parsedlog_export is not None:
                conf_value['enabled_parsedlog_export'] = enabled_parsedlog_export
                conf_value['parsedlog_rr_mode'] = True if param['parsedlog_rr_mode'] == u'true' else False

                servers = param['parsedlog_export_servers'] or '[]'
                conf_value['parsedlog_export_servers'] = json.dumps(servers)

                conf_value['external_log_level'] = param['external_log_level'] or ''
                conf_value['external_log_type'] = param['external_log_type'] or ''

        if 'enabled_full_traffic_filter_log' in param:
            conf_value['enabled_full_traffic_filter_log'] = param['enabled_full_traffic_filter_log']

        if 'enable_receive_parsed_access_log' in param:
            conf_value['enable_receive_parsed_access_log'] = param['enable_receive_parsed_access_log']

        if 'log_level' in param:
            log_level = param['log_level']
            if log_level:
                conf_value['log_level'] = log_level
                conf_value['log_type'] = param['log_type'] or ''

        if 'multi_processer' in param:
            multi_processer = param['multi_processer'] or ''
            if multi_processer is not None:
                conf_value['multi_processer'] = multi_processer

        if 'enabled_archivelog_filter' in param:
            enabled_archivelog_filter = param['enabled_archivelog_filter']
            archivelog_filter_percent = param['archivelog_filter_percent'] or '0'
            if enabled_archivelog_filter is not None:
                conf_value['enabled_archivelog_filter'] = enabled_archivelog_filter
                conf_value['archivelog_filter_percent'] = archivelog_filter_percent
                if conf_value['enabled_archivelog_filter']:
                    if is_int(archivelog_filter_percent) and 0 <= int(archivelog_filter_percent) < 100:
                        conf_value['archivelog_filter_percent'] = archivelog_filter_percent
                    else:
                        return HttpResponse(
                            json.dumps({'saveSuccess': False, 'errorCode': "archivelog_filter_percent error."}),
                            content_type=CONTENT_TYPE_JSON)

        if 'phoenix_extra_indexes' in param:
            phoenix_extra_indexes = param['phoenix_extra_indexes']
            if phoenix_extra_indexes is not None:
                conf_value['phoenix_extra_indexes'] = phoenix_extra_indexes.split(',')

        result, errmsg = view_phoenix_log_setting_set(conf_value)

        op_details = get_log_settings_op_detail(old_conf_value, conf_value)
        op_result = 0 if result else 1

        for op_detail in op_details:
            op = op_detail.pop('op', ugettext_noop('Modify'))
            operation_log(request, ugettext_noop('Log'), op, op_result,
                          op_detail, user=request.POST.get('username'))

        return HttpResponse(json.dumps({'saveSuccess': result, 'errorCode': errmsg}),
                            content_type=CONTENT_TYPE_JSON)

    elif request.method == "GET":

        conf_value = old_conf_value

        conf = ConfDb()
        receive_parsed_access_log_port = conf.get_value('receive_parsed_access_log/port', 20514)
        ip_list = conf.get_ip_list_by_roles('sailfish', inet_interface='log')
        receive_parsed_access_log_udp_ip_port_list = '\n'.join('TCP/UDP {}:{}'.format(i, receive_parsed_access_log_port) for i in ip_list),

        result = {'es_set_size': conf_value['es_size'],
                  'log_set_size': conf_value['log_size'],
                  'has_logarchive': conf_value['has_logarchive'],
                  'logarchive_disk': conf_value['logarchive_disk'],
                  'has_es': conf_value['has_es'],
                  'es_disk': conf_value['es_disk'],
                  'enabled_log_export': conf_value['enabled_log_export'],
                  'log_export_address': conf_value['log_export_address'],
                  'log_export_port': conf_value['log_export_port'],
                  'log_export_proto': conf_value['log_export_proto'],

                  'enabled_operate_log_export': conf_value['enabled_operate_log_export'],
                  'operate_log_export_address': conf_value['operate_log_export_address'],
                  'operate_log_export_port': conf_value['operate_log_export_port'],
                  'operate_log_export_proto': conf_value['operate_log_export_proto'],

                  'log_export_direct': conf_value['log_export_direct'],
                  'enabled_parsedlog_export': conf_value['enabled_parsedlog_export'],
                  'parsedlog_rr_mode': conf_value['parsedlog_rr_mode'],
                  'parsedlog_export_servers': conf_value['parsedlog_export_servers'],
                  'external_log_level': conf_value['external_log_level'],
                  'external_log_type': conf_value['external_log_type'],
                  'log_level': conf_value['log_level'],
                  'log_type': conf_value['log_type'],
                  'multi_processer': conf_value['multi_processer'],
                  'enabled_archivelog_filter': conf_value['enabled_archivelog_filter'],
                  'archivelog_filter_percent': conf_value['archivelog_filter_percent'],
                  'phoenix_extra_indexes': conf_value['phoenix_extra_indexes'],
                  'sailfish_external_node_list': conf_value['sailfish_external_node_list'],
                  'sailfish_allow_access_list': conf_value['sailfish_allow_access_list'],
                  'enabled_full_traffic_filter_log': conf_value['enabled_full_traffic_filter_log'],
                  'enable_receive_parsed_access_log': conf_value['enable_receive_parsed_access_log'],
                  'receive_parsed_access_log_udp_ip_port_list': receive_parsed_access_log_udp_ip_port_list,
                  'desensitization_conf': conf_value['desensitization_conf'],
                  }

        return HttpResponse(json.dumps(result),
                            content_type=CONTENT_TYPE_JSON)


def generate_file_page_log(items, page=0):
    archive_page_creator = Pagination(len(items), page)
    return {
        'pagination': archive_page_creator.create(),
        'cur_page': archive_page_creator.cur_page,
        'logs': archive_page_creator.items_of_cur_page(items)
    }


def view_syslog_report(request):
    log_file_info = get_archive_log_file_info()
    page = to_int(request.GET.get('page'), 0)
    return JsonResponse(generate_file_page_log(log_file_info, page))


def view_archive_log_report(request):
    raw_log_file_info = get_raw_log_file_info()
    page = to_int(request.GET.get('page'), 0)
    return JsonResponse(generate_file_page_log(raw_log_file_info, page))

@check_permission('Operation_Log', 'read')
@login_required
def view_phoenix_operation_log(request):
    if request.method == "GET":
        tab = request.GET.get('tab')
        if tab is None:
            if not has_permission(request.user.username, 'Operation_Log', 'read'):
                return HttpResponse(status=403)
            operate_log = get_operate_log_file_info(request)

            log_file_info = get_archive_log_file_info()
            log_info = generate_file_page_log(log_file_info)

            raw_log_file_info = get_raw_log_file_info()
            raw_log_info = generate_file_page_log(raw_log_file_info)

            show_raw_log = ConfDb().is_archive_log_server()

            show_report_type = current_machine_hardware_name() == 'x86_64'

            return base_render_to_response(request, 'v2/statistic_operation_log.html',
                                           {'operate_log': operate_log,
                                            'log_info': log_info,
                                            'raw_log_info': raw_log_info,
                                            'show_raw_log': show_raw_log,
                                            'show_report_type': show_report_type
                                            })
        else:
            if tab == 'operate_log':
                if not has_permission(request.user.username, 'Operation_Log', 'read'):
                    return HttpResponse(status=403)
                operate_log_info = get_operate_log_file_info(request)
                return JsonResponse(operate_log_info)

            elif tab == 'sys_log':
                if not has_permission(request.user.username, 'System_Log', 'read'):
                    return HttpResponse(status=403)
                return view_syslog_report(request)

            elif tab == 'archive_log':
                if not has_permission(request.user.username, 'Archived_Log', 'read'):
                    return HttpResponse(status=403)
                return view_archive_log_report(request)

            elif tab == 'security_log':
                if not has_permission(request.user.username, 'Security_Log', 'read'):
                    return HttpResponse(status=403)
                from views_phoenix_report_errors import phoenix_errors_report_multi
                return phoenix_errors_report_multi(request)

            elif tab== 'log_settings':
                if not has_permission(request.user.username, 'Log_Settings', 'read'):
                    return HttpResponse(status=403)
                return view_phoenix_log_setting(request)

    elif request.method == "POST":
        param = json.loads(request.body)
        tab = param['tab']
        if tab and tab == 'log_settings':
            if not has_permission(request.user.username, 'Log_Settings', 'write'):
                return HttpResponse(status=403)
            return view_phoenix_log_setting(request)

@login_required
def view_security_log(request):
    try:
        if request.method == "GET":
            page_from = request.GET.get('pagefrom', 0)
            if not has_permission(request.user.username, 'Security_Log', 'read'):
                if page_from:
                    return HttpResponseRedirect("/overview/")
                return HttpResponse(status=403)


            def string_to_timestamp(date_string,format_="%Y-%m-%d %H:%M:%S"):
                s_time = time.strptime(date_string, format_)
                timestamp = int(time.mktime(s_time))
                return timestamp

            report_type = request.GET.get('report_type_cn', '')
            attack_type = request.GET.get('attack_type_cn', '__all__')
            #2022-11-15 17:28:22,2022-11-16 17:28:22
            timebetween=request.GET.get('timebetween')
            page_from=request.GET.get('pagefrom',0)
            date_start=0
            date_end=0
            if timebetween:
                time_start_str, time_end_str = timebetween.split(',')
                if time_start_str == '-1':
                    time_start_str = "2000-01-01 00:00:00"
                date_start = string_to_timestamp(time_start_str)
                if time_end_str == '-1':
                    date_end = int(time.time())
                else:
                    date_end = string_to_timestamp(time_end_str)
            from utils import GetWafSecurityLogType
            inject_attack_types = GetWafSecurityLogType().get_attack_type_info()
            attack_types, _ = GetWafSecurityLogType().get_attack_type()
            for item in inject_attack_types:
                if attack_type == item.get("value"):
                    attack_type = item.get("key")

            return base_render_to_response(request, 'v2/waf/security_log.html',
                                            {
                                            'report_type_cn':report_type,
                                            'attack_type':attack_type,
                                            'date_start':date_start,
                                            'date_end':date_end,
                                            'pageFrom':page_from,
                                            'attack_types':attack_types,
                                            'inject_attack_types': inject_attack_types
                                            })

    except Exception as e:
        logger.error(e)

    return HttpResponse(status=403)



@check_permission('Log_Settings', 'write')
@expert_mode_required
@login_required
def save_sf_external_storage_node(request):
    res = json.loads(request.body)
    new_external_node_list = res.get('ex_node_ip')

    detail_msg = {'msg': ugettext_noop('Add the external big data analysis S node IP {new_external_node_ip}.'),
                  'extra': {'new_external_node_ip': ", ".join(new_external_node_list)}}
    conf = BaseConf()

    sf_external_nodes = conf.get_value(PATH_TO_SAILFISH_EXTERNAL_NODE, [])
    duplicate_ip = [ip for ip in new_external_node_list if ip in sf_external_nodes]
    if duplicate_ip:
        result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False,
                  'error_msg': ugettext_noop('Add external the big data analysis S node failed: Duplicate ip address.')}
    elif len(sf_external_nodes) == 20:
        result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False,
                  'error_msg': ugettext_noop('Add external the big data analysis S node failed: The number of Sailfish external nodes exceeds 20.')}
    elif type(new_external_node_list) is list:
        is_valid = True
        for ip in new_external_node_list:
            if re.match(r'((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$', ip):
                sf_external_nodes.append(ip)
            else:
                is_valid = False
                break

        if is_valid:
            code, output, cmdline = conf.set_asp_conf_values({PATH_TO_SAILFISH_EXTERNAL_NODE: sf_external_nodes})
            if code == 0:
                result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': True, 'error_msg': ''}
            else:
                result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False,
                          'error_msg': ugettext_noop('Add external the big data analysis S node failed: save failed.')}
        else:
            result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False,
                      'error_msg': ugettext_noop('Add external the big data analysis S node failed: Invalid IPV4 address.')}
    else:
        result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False,
                  'error_msg': ugettext_noop('Add external the big data analysis S node failed: Invalid pramameter type.')}

    operation_log(request,
                  ugettext_noop('Log'),
                  "Add",  # 操作
                  '0' if result['save_success'] else '1',  # 操作结果
                  detail_msg
                  )
    return HttpResponse(json.dumps(result), content_type='application/json')

@check_permission('Log_Settings', 'write')
@expert_mode_required
@login_required
def del_external_storage_node(request):
    res = json.loads(request.body)
    ex_storage_node_ip = res.get('ex_node_ip')

    detail_msg = {'msg': ugettext_noop('Delete the external big data analysis S node IP:{ex_node_ip}.'),
                  'extra': {'ex_node_ip': ex_storage_node_ip}}

    conf = BaseConf()
    sf_external_nodes = conf.get_value(PATH_TO_SAILFISH_EXTERNAL_NODE, [])

    if ex_storage_node_ip not in sf_external_nodes:
        result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False,
                  'error_msg': _(
                      'Delete the external big data analysis S node failed: node {0} does not exist.').format(
                      ex_storage_node_ip)}
    else:
        sf_external_nodes.remove(ex_storage_node_ip)
        code, output, cmdline = conf.set_asp_conf_values({PATH_TO_SAILFISH_EXTERNAL_NODE: sf_external_nodes})
        if code == 0:
            result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': True, 'error_msg': ''}
        else:
            result = {'sailfish_external_node_list': sf_external_nodes, 'save_success': False, 'error_msg': ''}
    operation_log(request,
                  ugettext_noop('Log'),
                  ugettext_noop("Delete"),  # 操作
                  '0' if result['save_success'] else '1',  # 操作结果
                  detail_msg
                  )
    return HttpResponse(json.dumps(result), content_type='application/json')


@check_permission('Log_Settings', 'write')
@expert_mode_required
@login_required
def save_sf_allow_access_list(request):
    res = json.loads(request.body)
    ex_query_node = res.get('ex_query_node')
    detail_msg = {'msg': ugettext_noop('Add the external big data analysis S (query) node IP: {ex_query_node}.'),
                  'extra': {'ex_query_node': ", ".join(ex_query_node)}}

    conf = BaseConf()
    allow_access_list = conf.get_value(PATH_TO_SAILFISH_ALLOW_ACCESS_LIST, [])
    duplicate_ip = [ip for ip in ex_query_node if ip in allow_access_list]
    if duplicate_ip:
        result = {'sailfish_allow_access_list': allow_access_list, 'save_success': False,
                  'error_msg': ugettext_noop('Add external big data analysis S (query) node failed: Duplicate ip.')}

    elif len(allow_access_list) == 20:
        result = {'sailfish_allow_access_list': allow_access_list, 'save_success': False,
                  'error_msg': ugettext_noop(
                      'Add external big data analysis S (query) node failed: the number exceeds 20.')}

    elif type(ex_query_node) is list:
        valid_ip_list = True
        for ip in ex_query_node:
            if re.match(r'((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',ip):
                allow_access_list.append(ip)
                continue
            else:
                valid_ip_list = False
                break

        if valid_ip_list:
            code, output, cmd_line = conf.set_asp_conf_values({PATH_TO_SAILFISH_ALLOW_ACCESS_LIST: allow_access_list})
            if code == 0:
                result = {'sailfish_allow_access_list': allow_access_list, 'save_success': True, 'error_msg': ''}
            else:
                result = {'sailfish_allow_access_list': allow_access_list, 'save_success': False,
                          'error_msg': ugettext_noop('Add external big data analysis S (query) node failed: save failed.')}
        else:
            result = {'sailfish_allow_access_list': allow_access_list, 'save_success': False,
                      'error_msg': ugettext_noop('Add external big data analysis S (query) node failed: Invalid IPV4 address.')}
    else:
        result = {'sailfish_allow_access_list': allow_access_list, 'save_success': False,
                  'error_msg': ugettext_noop('Add external big data analysis S (query) node failed: Invalid parameter type.')}
    operation_log(request,
                  ugettext_noop('Log'),
                  ugettext_noop("Add"),  # 操作
                  '0' if result['save_success'] else '1',  # 操作结果
                  detail_msg
                  )
    return HttpResponse(json.dumps(result), content_type='application/json')


@check_permission('Log_Settings', 'write')
@expert_mode_required
@login_required
def delete_sf_allow_access_list(request):
    res = json.loads(request.body)
    ex_query_node = res.get('ex_query_node')
    detail_msg = {'msg': ugettext_noop('Delete the external big data analysis S (query) node IP: {ex_query_node}.'),
                  'extra': {'ex_query_node': ex_query_node}}

    conf = BaseConf()
    access_node_list = conf.get_value(PATH_TO_SAILFISH_ALLOW_ACCESS_LIST, [])

    if ex_query_node not in access_node_list:
        result = {'sailfish_allow_access_list': access_node_list, 'save_success': False,
                  'error_msg': _('Delete external big data analysis S (query) node failed: node {0} does not exist.').format(ex_query_node)}
    else:
        access_node_list.remove(ex_query_node)
        code, output, cmdline = conf.set_asp_conf_values({PATH_TO_SAILFISH_ALLOW_ACCESS_LIST: access_node_list})
        if code == 0:
            result = {'sailfish_allow_access_list': access_node_list, 'save_success': True,
                      'error_msg': ''}
        else:
            result = {'sailfish_allow_access_list': access_node_list, 'save_success': False,
                      'error_msg': _('Delete external big data analysis S (query) node failed: save failed.')}

    operation_log(request, ugettext_noop('Log'),
                  ugettext_noop("Delete"),  # 操作
                  '0' if result['save_success'] else '1',  # 操作结果
                  detail_msg
                  )
    return HttpResponse(json.dumps(result), content_type='application/json')

def get_operate_log_file_info(request, COUNT_PER_PAGE=50):
    str_date_start = request.GET.get('date_start')
    str_date_end = request.GET.get('date_end')
    user = request.user.username

    date_start, date_end = get_date_start_end_param(request)

    cur_page = request.GET.get('page')
    page = to_int(cur_page, 0)

    confdb = ConfDb()
    conf = WebconsoleConf()
    log_entrys = []
    if confdb.get_value('report_type') == 'sailfish':
        s_date_start = date_start + time.timezone
        s_date_end = date_end + SECS_OF_DAY + 1 + time.timezone
        searchquery = 'select * from operation_log where (timestamp between {} and {}) and {} order by timestamp desc limit 1000'.format(
            s_date_start, s_date_end,
            '1' if conf.get_user_role(user) in (['Administrator', 'Viewer'] + conf.get_all_roles()) else "user = '{}'".format(user))

        result, total_count = sailfish_restapi_request(searchquery, count=COUNT_PER_PAGE, offset=page * COUNT_PER_PAGE)
        if total_count > 0:
            column_names = result['column_names']
            column_index = {name: column_names.index(name) for name in column_names}
            for record in result['records']:
                log_entrys.append({
                    'timestamp': int(time.mktime(time.strptime(record[column_index['timestamp']], "%Y-%m-%d %H:%M:%S"))),
                    'user': record[column_index['user']],
                    'src_ip': record[column_index['src_ip']],
                    'module': record[column_index['module']],
                    'action': record[column_index['action']],
                    'result': str(record[column_index['result']]),
                    'detail': record[column_index['detail']],
                    'node_ip': record[column_index['node_ip']],
                })
    else:
        if conf.get_user_role(user) in (['Administrator', 'Viewer'] + conf.get_all_roles()):
            search_condition = ''
        else:
            search_condition = '| spath user | search user={}'.format(user)

        # body = ES_SORT_JSON % (date_start, date_end + SECS_OF_DAY - 1, search_condition)
        # url = "{0}/operation_log/_search?size={1}&from={2}".format(ASP_DB_INDEX_ALIAS, COUNT_PER_PAGE,
        #                                                               page * COUNT_PER_PAGE)
        s_date_start = 'earliest={}'.format(date_start + time.timezone)
        s_date_end = 'latest_time={}'.format(date_end + SECS_OF_DAY + 1 + time.timezone)

        # Step 1: Post search to create a search job
        searchquery = 'search index=operation_log {} {} {} | sort - timestamp'.format(s_date_start, s_date_end, search_condition)
        response, total_count = phoenix_restapi_request(searchquery, offset=page * COUNT_PER_PAGE)
        if total_count > 0:
            data = response.strip()
            data = data.split('\n')
            for rec in data:
                try:
                    log_entrys.append(json.loads(rec))
                except Exception as e:
                    logger.error("failed to json.loads record: {}, error={}".format(rec, str(e)))
                    continue

    op_logs = []
    # Step 2: json.loads(detail) and translate
    if total_count > 0:
        for record in log_entrys:
            try:
                record['detail'] = json.loads(record['detail'])
            except Exception as e:
                logger.error("failed to json.loads record: {}, error={}".format(record['detail'], str(e)))
                continue

            msg = _(record['detail']['msg'])
            if 'spliceMsg' in record['detail']:
                spliceMsg = record['detail']['spliceMsg']
                if spliceMsg:
                    msg = msg + spliceMsg

            if 'extra' in record['detail']:
                extra = record['detail']['extra']
                try:
                    if isinstance(extra, dict):
                        msg = msg.format(**extra)
                    elif isinstance(extra, list):
                        msg = msg.format(*extra)
                except Exception as e:
                    logger.warning(
                        'operation log extra data is not match for:\n{space}msg: {msg}\n{space}actual_extra: {extra}'.format(
                            space=' ' * 4,
                            msg=record['detail']['msg'],
                            extra=record['detail']['extra']
                        ))

            info = None
            if 'info' in record['detail']:
                info = record['detail']['info']
                info = info if type(info) in (str,unicode) else json.dumps(info)
                if info.startswith('Base64:'): # for legacy Base64: log
                    try:
                        info = base64.b64decode(info[7:])
                    except:
                        pass # string starting with Base64: is legal, even if it cannot be b64decode

            one_record = []
            one_record.append(record['timestamp'])
            one_record.append(record['user'])
            one_record.append(record['src_ip'])
            one_record.append(_(record['module']))
            one_record.append(_(record['action']))
            one_record.append(record['result'])
            one_record.append(msg)
            one_record.append(info)

            op_logs.append(one_record)

    # Paging
    if total_count > 10000:
        total_count = 10000

    pagination_creator = Pagination(total_count, cur_page, COUNT_PER_PAGE)
    operate_logs = {
        'op_logs': op_logs,
        'date_start': str_date_start or time_to_date_str(date_start),
        'date_end': str_date_end or time_to_date_str(date_end),
        'pagination': pagination_creator.create(),
        'cur_page': pagination_creator.cur_page
    }

    return operate_logs


@login_required
@check_permission('Operation_Log', 'read')
def download_operation_log(request):
    date_start, date_end = get_date_start_end_param(request)

    hdr = [
        _('Date'),
        _('Username'),
        _('Source IP'),
        _('Module'),
        _('Actions'),
        _('Result'),
        _('Details'),
    ]

    operate_logs = get_operate_log_file_info(request, COUNT_PER_PAGE=10000)
    rows = operate_logs['op_logs']
    for i in xrange(len(rows)):
        rows[i][0] = time.strftime('%Y/%m/%d %H:%M:%S', time.localtime(rows[i][0]))
        rows[i][5] = _('success') if rows[i][5] == '0' else _('failed')

    results = to_safe_csv(hdr, rows)

    response = HttpResponse(results, content_type=CONTENT_TYPE_OCT_STREAM)
    def_file = 'Operation_Log.csv'
    response['Content-Disposition'] = "attachment; filename=%s" % def_file

    op_log_info = "start date:{}, end date:{}.".format(time_to_date_str(date_start), time_to_date_str(date_end))
    operation_log(request, ugettext_noop('Log'), ugettext_noop('Export'), '0', {
                  'msg': ugettext_noop('Operation log has been exported!'),
                  'info':op_log_info}, user=request.POST.get('username'))

    return response


def ua_check(request):
    if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':
        return entry(request)

    template_name = 'v2/ua_check.html'
    context = {
        'layout': get_manifest_info().get('layout')
    }
    return render_to_response(template_name, context, context_instance=RequestContext(request))


class LicenseFileForm(forms.Form):
    licensefile = forms.FileField(
            label=ugettext_lazy('Select File'),
            widget=forms.FileInput(attrs={'onchange': 'auto_upload_file(this)', 'onclick': 'this.value = null',
                                          'class': 'rs-asp-uploadBtn'})
    )


class PhoenixLicenseFileForm(forms.Form):
    phoenixlicensefile = forms.FileField(
            label=ugettext_lazy('Select File'),
            widget=forms.FileInput(attrs={'onchange': 'auto_upload_phoenix_file(this)', 'onclick': 'this.value = null',
                                          'class': 'rs-asp-uploadBtn'})
    )


class SailfishLicenseFileForm(forms.Form):
    sailfishlicensefile = forms.FileField(
        label=ugettext_lazy('Select File'),
                widget=forms.FileInput(attrs={'onchange': 'auto_upload_sailfish_file(this)', 'onclick': 'this.value = null',
                                      'class': 'rs-asp-uploadBtn'})
    )



def license_item_names():
    license_item_names = {
        'product': _('Product Name'),
        'id': _('License ID'),
        'activated_finger_print': _('Activated Fingerprint'),
        'current_finger_print': _('Current Fingerprint'),
        'node_fingerprint': _('Node Fingerprint'),
        'activated_node_fingerprint': _('Activated Node Fingerprint'),
        'license_type': _('License Type'),
        'service_type': _('Service Type'),
        'expiration': _('Expiration Date'),
        'sku': _('SKU'),
        'customer': _('Customer Name'),
        'license_status': _('License Status'),
        'service_status': _('Service Status'),
        'start_date': _('Effective Date'),
        'max_nodes': _('Max Protected Nodes'),
        'max_cpu_cores': _('Max CPU Cores'),
        'max_memory_size': _('Max Memory Size'),
        'max_proxies': _('Max Protected Websites'),
        'license_protected_level': _('Licensed Protection Level'),
        'creation_time': _('Effective Date'),
        'expiration_time': _('Expiration Date'),
        'lic_master': _('License Master'),
        'lic_slaves': _('License Slaves'),
        'used_quota': _('Used Volumn'),
        'max_quota': _('Allowed Daily Volumn'),
        'avg_volume': _('30-Day Average Daily Volume'),
        'max_volume': _('30-Day Max Daily Volume'),
        'quota_exceed_time': _('30-Day Exceeded Volume Count'),
        'search_limit': _('License Status'),
        'alarm_severity': _('Alarm Severity'),
        'supported_modules': _('Supported Modules'),
        'service_start_date': _('Service Effective Date'),
        'service_expiration': _('Service Expiration Date'),
        'max_qps': _('Max QPS'),
        'cluster_fingerprint': _('Cluster Fingerprint'),
        'max_nodes_in_cm': _('Max Proxy Nodes'),
        'rcm_name': _('Centralized License Server')
    }

    return license_item_names


def get_phoenix_license_alert():
    user = PHOENIX_ADMIN
    pwd = PHOENIX_ADMIN_PWD

    lic_master = ConfDb().get_value('phoenix/lic_master', None)

    # query license type and expiration time
    response, cnt = phoenix_restapi_request('| rest splunk_server=local /services/licenser/licenses \
                                            | join type=inner stack_id \
                                                [ rest splunk_server=local /services/licenser/slaves \
                                                | mvexpand active_pool_ids \
                                                | eval pool=active_pool_ids \
                                                | join type=outer pool \
                                                    [ rest splunk_server=local /services/licenser/pools \
                                                    | eval pool=title \
                                                    | fields pool stack_id] \
                                                | fields stack_id, warning_count] \
                                            | fields stack_id, label, warning_count, quota, status',
                                            0, 0, user, pwd, 'json', lic_master)

    if cnt < 0:
        return {}

    j_response = json.loads(response, encoding='utf-8')
    results = j_response.get('results')
    license_type = 'Inactive'
    label = None
    violations = 0
    old_quota = 0
    expired = True
    for result in results:
        stack_id = result.get('stack_id').lower()
        label = result.get('label').lower()
        quota = int(result.get('quota'))
        if stack_id.startswith('fixed-sourcetype') is True or stack_id == 'enterprise':
            if license_type in ('Trial', 'Inactive', 'Free'):
                if "trial" in label:
                    license_type = 'Trial'
                elif "reset" in label:
                    continue
                else:
                    license_type = 'Perpetual'

                if license_type == 'Trial' and old_quota > quota:
                    continue

                old_quota = quota

            violations = int(result.get('warning_count'))
            status = result.get('status').lower()

            if status in ("valid", "from_the_future"):
                expired = False

        elif stack_id == 'download-trial' and license_type == 'Inactive':
            violations = int(result.get('warning_count'))
            status = result.get('status').lower()
            if status in ("valid", "from_the_future"):
                expired = False
        elif stack_id == 'free' and license_type == 'Inactive':
            violations = int(result.get('warning_count'))
            license_type = 'Free'
            status = result.get('status').lower()
            if status in ("valid", "from_the_future"):
                expired = False

    # calculate the alarm severity, 0 indicates normal, 1 indicates severity 'yellow', 2 indicates severity 'red'
    alarm_type = []
    if license_type in ('Trial', 'Inactive', 'Free'):
        alarm_type.append(1)

    if expired is True:
        alarm_type.append(4)

    if violations > 0:
        max_violations = 3 if license_type == 'Free' else 5
        if violations < max_violations:
            alarm_type.append(2)
        else:
            alarm_type.append(3)
    # this list order is license type, expiration time, used quota, max quota, quota exceed time, search limit
    ret = {'license_type': license_type,
           'violations': violations,
           'alarm_type': alarm_type,
           'label': label}

    return ret


def get_phoenix_license_info():

    user = PHOENIX_ADMIN
    pwd = PHOENIX_ADMIN_PWD
    res = {}

    lic_master = ConfDb().get_value('phoenix/lic_master', None)

    # query used quota and max quota
    response, cnt = phoenix_restapi_request('| rest splunk_server=local /services/licenser/pools \
                                             | search [rest splunk_server=local /services/licenser/groups \
                                                      | search is_active=1 \
                                                      | eval stack_id=stack_ids \
                                                      | fields stack_id \
                                                      ] \
                                             | join type=outer stack_id [rest splunk_server=local /services/licenser/stacks \
                                                                        | eval stack_id=title \
                                                                        | eval stack_quota=quota \
                                                                        | fields stack_id stack_quota \
                                                                        ] \
                                             | stats sum(used_bytes) as used max(stack_quota) as total \
                                             | eval usedGB=round(used/1024/1024/1024,3) \
                                             | eval totalGB=round(total/1024/1024/1024,3) \
                                             | fields usedGB, totalGB',
                                            0, 0, user, pwd, 'json', lic_master)
    if cnt < 0:
        return {}

    try:
        j_response = json.loads(response, encoding='utf-8')
    except Exception:
        return {}

    results = j_response.get('results')
    for result in results:
        used_quota = result.get('usedGB')
        max_quota = result.get('totalGB')

    license_slaves = ''
    if lic_master:
        # query license slaves
        response, cnt = phoenix_restapi_request('| rest splunk_server=local /services/licenser/slaves  \
                                                 | stats list(label)  as  slaves', 0, 0, user, pwd, 'json', lic_master)

        try:
            j_response = json.loads(response, encoding='utf-8')
        except Exception:
            logging.debug('get_phoenix_license_info: raise exception 0')
            return {}

        results = j_response.get('results')
        if len(results) > 0:
            for result in results:
                license_slaves = result.get('slaves')
                if type(license_slaves) is not list:
                    license_slaves = license_slaves.split(',')
            conf_phoenix_node = ConfDb().get_phoenix_servers()
            license_slaves = list(set(license_slaves).intersection(set(conf_phoenix_node.values())))

        logging.debug('get_phoenix_license_info: get slaves {0}'.format(license_slaves))

    # query average and max daily usage
    response, cnt = phoenix_restapi_request('| search index=_internal source=*license_usage.log* type="RolloverSummary" earliest=-30d@d \
                                            | eval _time=_time - 43200 \
                                            | bin _time span=1d \
                                            | stats latest(b) AS b by slave, pool, _time \
                                            | stats sum(b) AS volume by _time \
                                            | stats avg(volume) AS avgVolume max(volume) AS maxVolume \
                                            | eval maxVolumeGB=round(maxVolume/1024/1024/1024,3) \
                                            | eval avgVolumeGB=round(avgVolume/1024/1024/1024,3) \
                                            | fields avgVolumeGB, maxVolumeGB', 0, 0, user, pwd, 'json', lic_master)
    avg_volume = 0
    max_volume = 0

    try:
        j_response = json.loads(response, encoding='utf-8')
    except Exception:
        logging.debug('get_phoenix_license_info: raise exception 0')
        return {}

    results = j_response.get('results')
    if results and len(results) > 0:
        for result in results:
            avg_volume = result.get('avgVolumeGB')
            max_volume = result.get('maxVolumeGB')

    logging.debug('get_phoenix_license_info: get average and max daily usage: cnt={0}'.format(cnt))

    # query creation_time expriation_time for valid and feature license
    response, cnt = phoenix_restapi_request('| rest splunk_server=local /services/licenser/licenses \
                                        | join type=inner stack_id \
                                            [ rest splunk_server=local /services/licenser/slaves \
                                            | mvexpand active_pool_ids \
                                            | eval pool=active_pool_ids \
                                            | join type=outer pool \
                                                [ rest splunk_server=local /services/licenser/pools \
                                                | eval pool=title \
                                                | fields pool stack_id] \
                                            | fields stack_id, warning_count] \
                                        | search status="VALID" OR status="FROM_THE_FUTURE" \
                                        | eval in_violation=if(warning_count>4 OR (warning_count>2 AND stack_id=="free"),"Y","N"), max_expiration_time=max(expiration_time) \
                                        | fields creation_time, expiration_time \
                                        | stats min(creation_time) as creation_time, max(expiration_time) as expiration_time',
                                        0, 0, user, pwd, 'json', lic_master)

    try:
        j_response = json.loads(response, encoding='utf-8')
    except Exception:
        logging.debug('get_phoenix_license_info: raise exception 1')
        return {}

    results = j_response.get('results')
    start_time = 0
    end_time = 0
    if results and len(results) > 0:
        for result in results:
            start_time = int(result.get('creation_time'))
            end_time = int(result.get('expiration_time'))


    # query quota exceed times
    response, cnt = phoenix_restapi_request('| rest splunk_server=local /services/licenser/licenses \
                                            | join type=inner stack_id \
                                                [ rest splunk_server=local /services/licenser/slaves \
                                                | mvexpand active_pool_ids \
                                                | eval pool=active_pool_ids \
                                                | join type=outer pool \
                                                    [ rest splunk_server=local /services/licenser/pools \
                                                    | eval pool=title \
                                                    | fields pool stack_id] \
                                                | fields stack_id, warning_count] \
                                            | search status="VALID" \
                                            | eval in_violation=if(warning_count>4 OR (warning_count>2 AND stack_id=="free"),"Y","N") \
                                            | fields stack_id, label, creation_time, expiration_time, max_violations, warning_count, in_violation, quota',
                                            0, 0, user, pwd, 'json', lic_master)

    try:
        j_response = json.loads(response, encoding='utf-8')
    except Exception:
        logging.debug('get_phoenix_license_info: raise exception 2')
        return {}

    results = j_response.get('results')

    if len(results) == 0:
        response, cnt = phoenix_restapi_request('| rest splunk_server=local /services/licenser/licenses \
                                                | join type=inner stack_id \
                                                    [ rest splunk_server=local /services/licenser/slaves \
                                                    | mvexpand active_pool_ids \
                                                    | eval pool=active_pool_ids \
                                                    | join type=outer pool \
                                                        [ rest splunk_server=local /services/licenser/pools \
                                                        | eval pool=title \
                                                        | fields pool stack_id] \
                                                    | fields stack_id, warning_count] \
                                                | eval in_violation=if(warning_count>4 OR (warning_count>2 AND stack_id=="free"),"Y","N") \
                                                | fields stack_id, label, creation_time, expiration_time, max_violations, warning_count, in_violation, quota, status',
                                                0, 0, user, pwd, 'json', lic_master)

        try:
            j_response = json.loads(response, encoding='utf-8')
        except Exception:
            logging.debug('get_phoenix_license_info: raise exception 3')
            return {}

        results = j_response.get('results')

    license_type = 'Inactive'
    old_create_time = 16725196800  #2500/1/1
    old_exp_time = 0
    old_quota = 0

    for result in results:
        stack_id = result.get('stack_id').lower()
        label = result.get('label').lower()
        quota = int(result.get('quota'))
        if stack_id.startswith('fixed-sourcetype') is True or stack_id == 'enterprise':
            if license_type in ('Trial', 'Inactive', 'Free'):
                if "trial" in label:
                    license_type = 'Trial'
                elif "reset" in label:
                   continue
                else:
                    license_type = 'Perpetual'

                if license_type == 'Trial' and old_quota > quota:
                    continue

                old_quota = quota

                quota_exceed_time = result.get('warning_count')
                exp_time = int(result.get('expiration_time'))
                if exp_time >= old_exp_time:
                    old_exp_time = exp_time
                else:
                    exp_time = old_exp_time

                if end_time > 0:
                    expiration_time = time.strftime("%Y-%m-%d", time.localtime(end_time))
                else:
                    expiration_time = time.strftime("%Y-%m-%d", time.localtime(exp_time))

                create_time = int(result.get('creation_time'))
                if create_time <= old_create_time:
                    old_create_time = create_time
                else:
                    create_time = old_create_time

                if start_time > 0:
                    creation_time = time.strftime("%Y-%m-%d", time.localtime(start_time))
                else:
                    creation_time = time.strftime("%Y-%m-%d", time.localtime(create_time))

                in_violation = result.get('in_violation')
                search_limit = 'Limited' if in_violation == 'Y' else 'Normal'

                license_status = result.get('status')
                if license_status == 'EXPIRED':
                    search_limit = 'Expired'

                if license_type == 'Trial':
                    now = time.time()
                    if now > exp_time:
                        search_limit = 'Expired'

                res = {
                    'lic_master': lic_master,
                    'lic_slaves': license_slaves,
                    'used_quota': ("%.2f GB" % float(used_quota)),
                    'max_quota': ("%.2f GB" % float(max_quota)),
                    'avg_volume': ("%.2f GB" % float(avg_volume)),
                    'max_volume': ("%.2f GB" % float(max_volume)),
                    'quota_exceed_time': quota_exceed_time,
                    'license_type': license_type,
                    'creation_time': creation_time,
                    'expiration_time': expiration_time,
                    'search_limit': search_limit,
                    'search_limit_color': 'red' if (search_limit == 'Limited' or search_limit == 'Expired')else 'green'
                }

        elif stack_id == 'download-trial' and license_type in ('Trial', 'Inactive', 'Free'):
            quota_exceed_time = result.get('warning_count')
            exp_time = int(result.get('expiration_time'))
            if exp_time >= old_exp_time:
                old_exp_time = exp_time
            else:
                exp_time = old_exp_time

            if end_time > 0:
                expiration_time = time.strftime("%Y-%m-%d", time.localtime(end_time))
            else:
                expiration_time = time.strftime("%Y-%m-%d", time.localtime(exp_time))

            create_time = int(result.get('creation_time'))
            if create_time <= old_create_time:
                old_create_time = create_time
            else:
                create_time = old_create_time

            if start_time > 0:
                creation_time = time.strftime("%Y-%m-%d", time.localtime(start_time))
            else:
                creation_time = time.strftime("%Y-%m-%d", time.localtime(create_time))

            in_violation = result.get('in_violation')
            search_limit = 'Limited' if in_violation == 'Y' else 'Normal'

            license_status = result.get('status')
            if license_status == 'EXPIRED':
                search_limit = 'Expired'


            res = {
                'lic_master': lic_master,
                'lic_slaves': license_slaves,
                'used_quota': ("%.2f GB" % float(used_quota)),
                'max_quota': ("%.2f GB" % float(max_quota)),
                'avg_volume': ("%.2f GB" % float(avg_volume)),
                'max_volume': ("%.2f GB" % float(max_volume)),
                'quota_exceed_time': quota_exceed_time,
                'license_type': license_type,
                'creation_time': creation_time,
                'expiration_time': expiration_time,
                'search_limit': search_limit,
                'search_limit_color': 'red' if (search_limit == 'Limited' or search_limit == 'Expired') else 'green'
            }
        elif stack_id == 'free' and license_type == 'Inactive':
            license_type = 'Free'
            quota_exceed_time = result.get('warning_count')
            exp_time = int(result.get('expiration_time'))
            if exp_time >= old_exp_time:
                old_exp_time = exp_time
            else:
                exp_time = old_exp_time
            expiration_time = time.strftime("%Y-%m-%d", time.localtime(exp_time))

            create_time = int(result.get('creation_time'))
            if create_time <= old_create_time:
                old_create_time = create_time
            else:
                create_time = old_create_time

            creation_time = time.strftime("%Y-%m-%d", time.localtime(create_time))

            in_violation = result.get('in_violation')
            search_limit = 'Limited' if in_violation == 'Y' else 'Normal'

            res = {
                'lic_master': lic_master,
                'lic_slaves': license_slaves,
                'used_quota': ("%.2f GB" % float(used_quota)),
                'max_quota': ("%.2f GB" % float(max_quota)),
                'avg_volume': ("%.2f GB" % float(avg_volume)),
                'max_volume': ("%.2f GB" % float(max_volume)),
                'quota_exceed_time': quota_exceed_time,
                'license_type': license_type,
                'creation_time': creation_time,
                'expiration_time': expiration_time,
                'search_limit': search_limit,
                'search_limit_color': 'red' if (search_limit == 'Limited' or search_limit == 'Expired') else 'green'
            }

    TYPE_MAPPING = {'Perpetual': _('Perpetual'), 'Trial': _('Trial'), 'Inactive': _('Inactive'), 'Free': _('Free')}
    STATUS_MAPPING = {'Normal': _('Normal'), 'Limited': _('Locked'), 'Expired': _('Expired')}

    if res.get('license_type', ''):
        res['license_type'] = TYPE_MAPPING.get(res['license_type'], _('Trial'))
    if res.get('search_limit', ''):
        res['search_limit'] = STATUS_MAPPING.get(res['search_limit'], _('Normal'))

    return res


def get_sailfish_license_info():
    search_head = ConfDb().get_min_id_sailfish_admin_ip()
    if not search_head:
        return dict()

    start = time.time()
    while True:
        resp = sailfish_request('/sailfish/api/sys/license', body='', sailfish_ip=search_head,
                                sailfish_user='admin')
        if resp and resp.status_code == 200:
            content = resp.content
            logger.info('get sailfish license {} with: {}'.format(resp.status_code, content))
            break

        if abs(time.time() - start) > 5:
            return dict()
        time.sleep(0.3)

    license_data = json.loads(content).get('data', {})

    SF_STATUS_MAPPING = {'NotFound': _('Inactive'), 'NotEffective': _('Not In Effect'), 'Expired': _('Expired'), 'Locked': _('Locked'), 'OK': _('Normal')}

    status = license_data.get('status')
    ret = {
        'search_limit': SF_STATUS_MAPPING.get(status, status),
        'search_limit_color': 'green' if status == 'OK' else 'red'
    }
    lang = 'en' if get_language() == 'en' else 'zh'
    if status != 'NotFound':
        ret.update({
        'used_quota': ("%.2f GB" % float(license_data.get('today_bytes', 0) / (1024.0**3))),
        'max_quota': ("%s GB" % str(license_data.get('daily_quota', 0) / (1024**3))),
        'avg_volume': ("%.2f GB" % float(license_data.get('ave_bytes_30d', 0) / (1024.0**3))),
        'max_volume': ("%.2f GB" % float(license_data.get('max_bytes_30d', 0) / (1024.0**3))),
        'quota_exceed_time': license_data.get('exceeded_30d', 0),
        'license_type': license_data.get('type_name', {}).get(lang, ''),
        'creation_time': license_data.get('expiration', [0,0])[0],
        'expiration_time': license_data.get('expiration', [0,0])[1]
        })

    return ret


def verify_sailfish_license(license_bytes):
    search_head = ConfDb().get_min_id_sailfish_admin_ip()
    if not search_head:
        return dict(result='FAILED', message='sailfish is not online')

    body = base64.b64encode(license_bytes)

    resp = sailfish_request('/sailfish/api/sys/license?verify_only=true&fmt=base64',
                            'POST', body=body,
                            headers={'Content-Type': 'text/plain'},
                            sailfish_ip=search_head,
                            sailfish_user='admin')
    content = resp.content
    logger.info('verify sailfish license {} with: {}'.format(resp.status_code, content))
    if resp.status_code == 200:
        return json.loads(content)

    return dict(result='FAILED', message='verify license sailfish server return non 200 response')


def verify_system_license_with_license_data(license_data, request):
    is_a_lic = False
    is_same_layout = False
    is_same_fingerprint = False
    is_mpp_valid = False
    serial_num = ""
    customer = ""
    valid_date = ""
    expire_date = ""
    is_not_cm_lic = False

    data = license_data
    try:
        data = rsa_sign.asp_decrypt(data)
        license_info = LicenseInfo(data, has_proxy_server=ConfDb().has_proxy_server())
        lic_info_data = license_info.get_dict()
        serial_num = lic_info_data["id"]
        customer = lic_info_data["customer"]
        valid_date = lic_info_data["start_date"]
        expire_date = lic_info_data["expiration"]
        is_a_lic = True

        is_not_cm_lic = not license_info.is_cm_license()

        if not is_layout_valid(lic_info_data["product"]):
            raise Exception("License is invalid, layout do not match, product is: " + lic_info_data["product"])
        is_same_layout = True

        if getattr(license_info, 'finger_print', '') != license_info.current_fingerprint:
            raise Exception("Invalid fingerprint")
        is_same_fingerprint = True

        # If current MPP count > new license max count -> license import failed.
        if license_info.get_max_mpp_count() < WechatConf().get_count() + AlipayConf().get_count() + MPaasConf().get_count():
            raise Exception('Import failed, the number of mini-programs in the configuration file exceeds the permitted range of the license authorization.')
        is_mpp_valid = True

    except Exception as e:
        if 'license_info' in locals().keys():
            license_error = license_info.get_error()
            logger.error('Illegal license file, with error: {0}, with exception: {1}'.format(license_error, e))
        else:
            pass

    return {'is_a_license': is_a_lic, 'is_same_layout': is_same_layout, 'is_mpp_valid': is_mpp_valid,
            'is_same_fingerprint': is_same_fingerprint, 'serial_num': serial_num,
            'customer': customer, 'valid_date': valid_date, 'expire_date': expire_date, "is_not_cm_lic": is_not_cm_lic}


@login_required
def system_license(request):
    error = None
    info = None
    activated = None
    phoenix_activated = None
    license_info = None
    all_supported_modules_detail = None

    nginxConf = NginxConf()

    if request.method == "POST":
        if not has_permission(request.user.username, 'License', 'write'):
            return HttpResponse(status=403)
        data = ''
        form = LicenseFileForm(request.POST, request.FILES)
        file = form.files.get('licensefile')
        if file is not None:
            if form.is_valid():
                data = request.FILES['licensefile'].read()
                ret_data = verify_system_license_with_license_data(data, request)
                if not all([ret_data['is_a_license'], ret_data['is_same_layout'], ret_data['is_same_fingerprint'], ret_data['is_not_cm_lic']]):
                    return HttpResponse(json.dumps(ret_data), content_type=CONTENT_TYPE_PLAIN)

            if data == '':
                error = _('Please enter Activation Key')
            else:
                try:
                    conf = ConfDb()
                    data = rsa_sign.asp_decrypt(data)
                    license_info = LicenseInfo(data, activating=False, has_proxy_server=conf.has_proxy_server())
                    # If current MPP count > new license max count -> license import failed.
                    if license_info.get_max_mpp_count() < WechatConf().get_count() + AlipayConf().get_count() + MPaasConf().get_count():
                        return HttpResponse(json.dumps({'error': _(
                            'Activation failed, the number of mini-programs in the configuration file exceeds the permitted range of the license authorization.')}),
                            content_type=CONTENT_TYPE_PLAIN)

                    license_info = LicenseInfo(data, activating=True, has_proxy_server=conf.has_proxy_server())

                    error = license_info.get_error() or license_info.get_warning()
                    info = license_info.get_info()
                    if license_info.is_valid_license():
                        activated = True
                        operation_log(request, ugettext_noop('License'), ugettext_noop('Activate'), '0', {
                            'msg': ugettext_noop('The license has been activated!')},
                                          user=request.POST.get('username'))

                        # After license activated, generate wechat info by license salt
                        if license_info.is_mpp_supported():
                            WechatConf().gen_mpp_basic_config()
                            AlipayConf().gen_mpp_basic_config()
                            MPaasConf().gen_mpp_basic_config()

                        return HttpResponse(json.dumps({"result":"ok"}), content_type=CONTENT_TYPE_PLAIN)
                except Exception as e:
                    logger.error('Failed to update license, with exception: ' + str(e))
                    error = _('Exception occurs when license is being updated.')
            if not activated:
                operation_log(request, ugettext_noop('License'), ugettext_noop('Activate'), '1', {
                'msg': ugettext_noop('Failed to activate the license')}, user=request.POST.get('username'))

        phoenix_form = PhoenixLicenseFileForm(request.POST, request.FILES)
        phoenix_license_file = phoenix_form.files.get('phoenixlicensefile')
        phoenix_data = ''
        if phoenix_license_file is not None:
            if phoenix_form.is_valid():
                phoenix_data, is_rpl = _deocde_rpl(request.FILES['phoenixlicensefile'].read())
                if is_rpl and phoenix_data.startswith('RSTv1'):
                    if service_mgr_set_asp_config({'phoenix/license_secret': int(time.time())}, 'local', 'web_console_system_license')[0] != 0:
                        return HttpResponse(json.dumps({"error":ugettext_noop('Exception occurs when license is being updated, please refresh the page and try again')}), content_type=CONTENT_TYPE_PLAIN)
                    operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '0', {
                        'msg': ugettext_noop('Try to activate license successfully')}, user=request.POST.get('username'))
                    return HttpResponse(json.dumps({"result":"ok"}), content_type=CONTENT_TYPE_PLAIN)

                phoenix_data += "\n{0}".format(time.time())
            elif phoenix_data == '':
                error = _('Please enter Activation Key')

            try:
                phoenix_lic_file = os.path.join(get_sync_file_path(), 'phoenix_license')

                if os.path.exists(phoenix_lic_file) is True:
                    os.remove(phoenix_lic_file)

                with open(phoenix_lic_file, 'w') as f:
                    f.write(phoenix_data)

                if os.path.exists(phoenix_lic_file) is True:
                    ret = BaseConf().sync_file(phoenix_lic_file)
                    if ret != RESULT.OK:
                        logging.error('Failed to sync statistics license ret = {}'.format(ret))
                        error = _('Failed to activate statistics license. Please check the details in the statistics subsystem.')
                    else:
                        phoenix_activated = True

                if phoenix_activated is True:
                    operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '0', {
                        'msg': ugettext_noop('Try to activate license successfully')}, user=request.POST.get('username'))

                    return HttpResponse(json.dumps({"result":"ok"}), content_type=CONTENT_TYPE_PLAIN)
                else:
                    operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '1', {
                        'msg': ugettext_noop('Try to activate license unsuccessfully')}, user=request.POST.get('username'))


            except Exception as e:
                logger.error('Failed to update statistics license, with exception: ' + str(e))
                error = _('Exception occurs when statistics license is being updated.')

        sailfish_form = SailfishLicenseFileForm(request.POST, request.FILES)
        sailfish_license_file = sailfish_form.files.get('sailfishlicensefile')
        if sailfish_license_file is not None:
            license_bytes = sailfish_license_file.read()
            result = verify_sailfish_license(license_bytes)
            if result.get('result') != 'OK':
                error = _('Verify sailfish license failed')
            else:
                values = dict()
                values.update({'sailfish/license': base64.b64encode(license_bytes)})
                BaseConf().set_asp_conf_values(values)
                operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '0', {
                    'msg': ugettext_noop('Try to activate license successfully')}, user=request.POST.get('username'))
                return HttpResponse(json.dumps({"result":"ok"}), content_type=CONTENT_TYPE_PLAIN)

        if error:
            return HttpResponse(json.dumps({ 'error': error }), content_type=CONTENT_TYPE_PLAIN)

    if not has_permission(request.user.username, 'License', 'read'):
        return HttpResponse(status=403)
    if not license_info:
        license_info = nginxConf.get_license_info()
        error = license_info.get_error() or license_info.get_warning()
        info = license_info.get_info()

    conf = ConfDb()
    is_using_node_fingerprint = conf.is_using_node_fingerprint()

    key_str = license_item_names()
    info_data = license_info.get_dict()
    if license_info.is_valid_license():
        if license_info.is_cm_license():
            info_keys = ['rcm_name', 'id', 'current_finger_print', 'sku', 'customer', 'license_type', 'service_type',
                         'license_status', 'service_status', 'start_date', 'expiration', 'max_qps', 'max_nodes',
                         'license_protected_level', 'supported_modules']

            rcm_name = get_rcm_info()[0]
            if rcm_name:
                info_data['rcm_name'] = rcm_name
            else:
                info_data['rcm_name'] = _("Not Connected")
                info_data['rcm_name_color'] = 'red'
            info_data['max_qps'] = _("Unlimited") if info_data.get('max_qps') == "0" else info_data.get('max_qps')
            key_str['start_date'] = key_str['service_start_date']
            key_str['expiration'] = key_str['service_expiration']
            key_str['current_finger_print'] = key_str['cluster_fingerprint']
            key_str['max_nodes'] = key_str['max_nodes_in_cm']
        else:
            info_keys = ['id', 'activated_finger_print', 'sku', 'customer', 'license_type', 'service_type',
                         'license_status', 'service_status',
                         'start_date', 'expiration', 'max_nodes', 'max_cpu_cores', 'max_memory_size', 'max_proxies',
                         'license_protected_level', 'supported_modules']

            if info_data.get('current_finger_print') != info_data.get('activated_finger_print'):
                info_keys.insert(2, 'current_finger_print')

        if info_data.get('type') != LicenseInfo.LT_Production:
            info_keys.remove('service_type')

        if info_data.get('license_protected_level'):
            before_trans = info_data['license_protected_level']
            if before_trans in ['BE', 'WE', 'LE']:
                info_keys.remove('license_protected_level')
            else:
                info_data['license_protected_level'] = _('Advanced protection')

        all_supported_modules_detail = license_info.get_all_supported_modules_detail()
        info_data['supported_modules'] = ''

        logger.debug('======license data is:')
        logger.debug(license_info.get_dict())

        product_type = get_manifest_info().get('product_type')
        remove_key_list = []
        if product_type == "ApiBotDefender" or product_type == 'ApiSecurityAudit':
            remove_key_list = ['license_protected_level']
        elif product_type == 'Safeplus':
            remove_key_list = ['supported_modules', 'max_proxies']

        for key in remove_key_list:
            if info_data.has_key(key):
                info_data.pop(key)
            if key in info_keys:
                info_keys.remove(key)

        if is_using_node_fingerprint:
            info_data['is_using_node_fingerprint'] = True
            info_keys.remove('id') if 'id' in info_keys else None
            info_data.pop('id') if 'id' in info_data else None
            info_data['activated_node_fingerprint'] = conf.get_node_fingerprint()
            info_keys[info_keys.index('activated_finger_print')] = 'activated_node_fingerprint'

        if license_info.is_evaluative_license() and conf.is_proxy():
            info_data['left_effected_seconds'] = info_data['expiration_int'] - info_data['start_date_int'] \
                                                 + license_info.BONUS_SEC - license_info.get_license_effected_time()

    else:
        info_keys = ['current_finger_print', 'license_status', 'service_status']

        if is_using_node_fingerprint:
            info_data['is_using_node_fingerprint'] = True
            info_data['node_fingerprint'] = conf.get_node_fingerprint()
            info_keys[info_keys.index('current_finger_print')] = 'node_fingerprint'

    return base_render_to_response(request, 'v2/system_license.html',
                                   {
                                       'info': info,
                                       'error': error,
                                       'valid': license_info.is_valid_license(),

                                       'info_keys': info_keys,
                                       'info_data': info_data,

                                       'key_str': key_str,

                                       'all_phoenix_online': is_all_phoenix_online(),
                                       'activated': activated,

                                       'phoenix_data': get_phoenix_license_info(),
                                       'phoenix_license_keys':[
                                                                'lic_master',
                                                                'lic_slaves',
                                                                'used_quota',
                                                                'max_quota',
                                                                'avg_volume',
                                                                'max_volume',
                                                                'quota_exceed_time',
                                                                'license_type',
                                                                'creation_time',
                                                                'expiration_time',
                                                                'search_limit'
                                       ],

                                       'all_sailfish_online': True,
                                       'sailfish_license_keys': [
                                           'used_quota',
                                           'max_quota',
                                           'avg_volume',
                                           'max_volume',
                                           'quota_exceed_time',
                                           'license_type',
                                           'creation_time',
                                           'expiration_time',
                                           'search_limit',
                                       ],
                                       'sailfish_data': get_sailfish_license_info(),
                                       'supported_modules_detail': all_supported_modules_detail
                                   })


@login_required
@csrf_exempt
@check_permission('License', 'write')
def verify_system_license_form(request):
    data = request.FILES['licensefile'].read()
    ret_data = verify_system_license_with_license_data(data, request)

    ok = all([ret_data['is_a_license'], ret_data['is_same_layout'], ret_data['is_same_fingerprint'], ret_data['is_mpp_valid'], ret_data['is_not_cm_lic']])
    if ok:
        operation_log(request, ugettext_noop('License'), ugettext_noop('Activate'), '0', {
            'msg': ugettext_noop('Verify license result: valid license')}, user=request.POST.get('username'))
    else:
        operation_log(request, ugettext_noop('License'), ugettext_noop('Activate'), '1', {
            'msg': ugettext_noop('Verify license result: invalid license')}, user=request.POST.get('username'))
    return HttpResponse(json.dumps(ret_data), content_type=CONTENT_TYPE_PLAIN)


def check_pkg_meta(pkg, suffix='waf'):
    if pkg.size > (1024 << 20): # Bigger than 1G
        raise Exception("The file {} is too big ({} Bytes).".format(pkg.name, pkg.size))
    if os.path.splitext(pkg.name)[1] != '.' + suffix:
        raise Exception("The file {0} extention {1} is invalid".format(pkg.name, os.path.splitext(pkg.name)[1]))


def extract_rulesetpkg(pkg):
    rootdir = get_sync_file_path()
    filepath = os.path.join(rootdir, pkg.name)
    with open(get_upgrade_lib_path(filepath), 'wb') as of:
        for chunk in pkg.chunks():
            of.write(chunk)
    if verifyPatch(filepath):
        logging.info('{} has been successfully verified'.format(filepath))
        try:
            zipFile = extractPatch(filepath, dest=pkg.name+'.zip')
            return zip_digest(zipFile)
        except Exception as e:
            exe_with_output('rm "{}"'.format(zipFile))
            raise Exception('extract zip from waf failed. error = {}'.format(repr(e)))
        finally:
            exe_with_output('rm "{}"'.format(filepath))

    else:
        logging.info('{} can NOT be verified'.format(filepath))
        exe_with_output('rm "{}"'.format(filepath))
        raise Exception('{} can NOT be verified'.format(pkg.name))

def zip_digest(zipFile):
    with zipfile.ZipFile(zipFile, 'r') as zip_ref:
        meta = zip_ref.open("manifest.json", "r").read()
        dict = json.loads(meta, encoding='utf-8')
        local_conf_path = get_waf_conf_path()
        change_log_name = "change_log.txt"
        change_log_name = os.path.join(local_conf_path, change_log_name)
        changelog = zip_ref.open(change_log_name, "r").read()
        dict['changelog'] = changelog
        return dict

def check_manifest(manifest):
    system_waf_engine_version = get_version()[9]
    system_waf_main_version = int(system_waf_engine_version.split('.')[0])
    system_waf_minor_version = int(system_waf_engine_version.split('.')[1])

    waf_ruleset_version = manifest['waf_ruleset_version']
    required_waf_main_version = int(waf_ruleset_version.split('.')[0])
    supported_waf_minor_version = int(waf_ruleset_version.split('.')[1])

    if system_waf_main_version != required_waf_main_version:
        raise ValueError(_("WAF engine version {0} doesn't support the ruleset {1}. Please select  the collect waf version").format(system_waf_engine_version, waf_ruleset_version))

    if system_waf_minor_version > supported_waf_minor_version:
        raise ValueError(_("WAF engine version {0} doesn't support the ruleset {1}. The minimum WAF ruleset version is {2}. Please select correct ruleset").format(system_waf_engine_version, waf_ruleset_version, system_waf_engine_version))

@login_required
@csrf_exempt
@check_permission('Ruleset_Update', 'write')
def verify_ruleset_form(request):
    if request.method == "POST":
        try:
            pkg = request.FILES['rulesetpkg']
            check_pkg_meta(pkg)
            pkg.name = 'waf_rule_temp.waf'
            manifest = extract_rulesetpkg(pkg)
        except Exception as e:
            logging.info(repr(e))
            operation_log(request, ugettext_noop('WAF'), ugettext_noop('Import'), '1', {'msg': ugettext_noop('fail to verify ruleset')})
            return HttpResponse(json.dumps({'error': str(e.message)}), content_type=CONTENT_TYPE_PLAIN)
        else:
            try:
                check_manifest(manifest)
            except ValueError as e:
                operation_log(request, ugettext_noop('WAF'), ugettext_noop('Import'), '1', {'msg': ugettext_noop('fail to verify ruleset')})
                exe_with_output('rm {}'.format(get_sync_file_path(pkg.name + '.zip')))
                return HttpResponse(json.dumps({'warning': str(e.message)}), content_type=CONTENT_TYPE_PLAIN)
            else:
                operation_log(request, ugettext_noop('WAF'), ugettext_noop('Import'), '0', {'msg': ugettext_noop('succeed to verify ruleset')})
                exe_with_output('rm {}'.format(get_sync_file_path(pkg.name + '.zip')))
                return HttpResponse(json.dumps(manifest), content_type=CONTENT_TYPE_PLAIN)

@login_required
@csrf_exempt
@check_permission('Ruleset_Update', 'write')
def upgrade_ruleset(request):
    requestFile = request.FILES['rulesetpkg']
    requestFile.name = 'waf_rule_temp.waf'
    manifest = extract_rulesetpkg(requestFile)

    wafruleset_filepath = get_sync_file_path(sync_wafruleset_filename())
    pre_wafruleset_filepath = get_sync_file_path(sync_wafruleset_filename(pre=1))
    landing_wafruleset_filepath = get_sync_file_path(requestFile.name + '.zip')

    # rotate pre-file for rollback
    exe_with_output('mv -f {} {}'.format(wafruleset_filepath, pre_wafruleset_filepath))
    exe_with_output('mv -f {} {}'.format(landing_wafruleset_filepath, wafruleset_filepath))

    # sync both files in cluster
    conf = BaseConf()
    conf.sync_file(wafruleset_filepath, sync=True)
    conf.sync_file(pre_wafruleset_filepath, sync=True)

    task_finished = NginxConf().set_upgrade_waf_ruleset('upgrade')

    if (task_finished and task_finished[0] == 0):
        operation_log(request, ugettext_noop('WAF'), ugettext_noop('Install'), '0', {
                      'msg': ugettext_noop('Upgrade WAF Ruleset to {new}.'),
                      'extra': {'new': manifest['waf_ruleset_version'], },
                      })
        WAFGrayBox(conf).update()
        return HttpResponse(json.dumps({'success': str(manifest)}), content_type=CONTENT_TYPE_PLAIN)
    else:
        operation_log(request, ugettext_noop('WAF'), ugettext_noop('Install'), '1', {'msg': ugettext_noop('waf ruleset failed to install')})
        return HttpResponse(json.dumps({'installError': str(task_finished)}), content_type=CONTENT_TYPE_PLAIN)

def extract_rollback_version():
    try:
        manifest = zip_digest(get_sync_file_path(sync_wafruleset_filename(pre=1)))
    except:
        return ''
    else:
        return manifest['waf_ruleset_version']

@login_required
@csrf_exempt
@check_permission('Ruleset_Rollback', 'write')
def rollback_ruleset(request):
    rollback_version = extract_rollback_version()
    if rollback_version != '':
        exe_with_output('cp -v -f {} {}'.format(get_sync_file_path(sync_wafruleset_filename(pre=1)),get_sync_file_path(sync_wafruleset_filename())))

        # sync wafruleset.zip in cluster
        wafruleset_filepath = get_sync_file_path(sync_wafruleset_filename())

        conf = BaseConf()
        conf.sync_file(wafruleset_filepath, sync=True)

        task_status = NginxConf().set_upgrade_waf_ruleset('rollback')
        if (task_status and task_status[0] == 0):
            WAFGrayBox(conf).clear()
            try_downgrade_unsupported_strategy_ids()

            operation_log(request, ugettext_noop('WAF'), ugettext_noop('rollback'), '0', {
                          'msg': ugettext_noop('Rollback WAF Ruleset to {v}'),
                          'extra': {'v': rollback_version}
                          })
            return HttpResponse(json.dumps({'success': rollback_version}), content_type=CONTENT_TYPE_PLAIN)

    operation_log(request, ugettext_noop('WAF'), ugettext_noop('rollback'), '1', {'msg': ugettext_noop('Fail to rollback ruleset')})
    return HttpResponse(json.dumps({'error_msg': _('Fail to rollback ruleset')}), content_type=CONTENT_TYPE_PLAIN)


def _deocde_rpl(data):
    if data.startswith('RPL1'):
        conf_db = ConfDb()
        license_info = conf_db.get_license_info(i18n_support=False, care_cluster=False)
        if license_info.is_valid_license():
            if license_info.type == license_info.LT_Evaluation and conf_db.get_value('_private/nginx/license_exhausted/enable'):
                return data, False
            fingerprint = str(conf_db.get_value('cluster/fingerprint', ''))
            passwd = str(license_info.license_id) + fingerprint
            return simple_decrypt_and_verify(get_release_file('conf_shared/pubkey.pem'), passwd, data[4:]) or '', True
        else:
            return data, False
    else:
        return data, False


@login_required
@csrf_exempt
@check_permission('License', 'write')
def verify_phoenix_license_form(request):
    is_a_lic = True
    is_expired = False
    license_type = ''
    start_time = ''
    expire_time = ''
    quota = 0

    data, is_rpl = _deocde_rpl(request.FILES['phoenixlicensefile'].read())

    if is_rpl and data.startswith('RSTv1'):
        t = data.split(',')
        start_time = int(t[1])
        expire_time = int(t[2])
        d = {
            'is_a_license': True,
            'type': 'Reset',
            'start_time': datetime.datetime.fromtimestamp(start_time).strftime('%Y-%m-%d'),
            'expire_time': datetime.datetime.fromtimestamp(expire_time).strftime('%Y-%m-%d'),
            'quota': 0,
            'is_expired': not (start_time <= int(time.time()) <= expire_time)
        }

        if d['is_expired']:
            operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '1', {
                'msg': ugettext_noop('Verify license result: license expired')}, user=request.POST.get('username'))
        else:
             operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '0', {
                'msg': ugettext_noop('Verify license result: valid license')}, user=request.POST.get('username'))

        return HttpResponse(json.dumps(d))

    try:
        root = ET.fromstring(data)

        payload = root.find('payload')
        if payload is None:
            is_a_lic = False
            logger.error('Illegal phoenix license file')
        else:

            guid = payload.find('guid')
            if guid is None:
                is_a_lic = False
                logger.error('Illegal phoenix license file')
            else:

                lic_type = payload.find('type').text.lower()
                if lic_type is None:
                    is_a_lic = False
                    logger.error('Illegal phoenix license file')
                else:
                    sourcetypes = payload.find('sourcetypes')
                    if sourcetypes is None:
                        is_a_lic = False
                        logger.error('Illegal phoenix license file')
                    else:

                        features = payload.find('features')
                        if features is None:
                            is_a_lic = False
                            logger.error('Illegal phoenix license file')
                        else:

                            lic_label = payload.find('label').text.lower()
                            if lic_label is None:
                                is_a_lic = False
                                logger.error('Illegal phoenix license file')
                            else:
                                creation_time = payload.find('creation_time')
                                if creation_time is None:
                                    is_a_lic = False
                                    logger.error('Illegal phoenix license file')
                                else:
                                    creation_time = creation_time.text

                                    start_time = datetime.datetime.fromtimestamp(int(creation_time)).strftime('%Y-%m-%d')

                                    expiration_time = payload.find('expiration_time')
                                    if expiration_time is None:
                                        is_a_lic = False
                                        logger.error('Illegal phoenix license file')
                                    else:
                                        expiration_time = expiration_time.text

                                        expire_time = datetime.datetime.fromtimestamp(int(expiration_time)).strftime('%Y-%m-%d')

                                        quota = payload.find('quota')
                                        if quota is None:
                                            is_a_lic = False
                                            logger.error('Illegal phoenix license file')
                                        else:

                                            quota = int(quota.text) / (1024 * 1024 * 1024)

                                            max_violations = payload.find('max_violations')
                                            if max_violations is None:
                                                is_a_lic = False
                                                logger.error('Illegal phoenix license file')
                                            else:
                                                license_type = 'Inactive'
                                                if lic_type.startswith('fixed-sourcetype') is True or lic_type == "enterprise":
                                                    if license_type in ('Trial', 'Inactive', 'Free'):
                                                        if "trial" in lic_label:
                                                            license_type = 'Trial'
                                                        elif "reset" in lic_label:
                                                            license_type = 'Reset'
                                                        else:
                                                            license_type = 'Perpetual'
                                                elif lic_type == 'free' and license_type == 'Inactive':
                                                    license_type = 'free'
        expiration_time = int(expiration_time)
        now_time = get_UTC_time_now()
        if expiration_time < now_time:
            is_expired = True

    except Exception as e:
        is_a_lic = False
        logger.error('Illegal phoenix license file, with exception: {0}'.format(e))

    if is_a_lic is True:
        if is_expired is False:
            operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '0', {
                'msg': ugettext_noop('Verify license result: valid license')}, user=request.POST.get('username'))
        else:
            operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '1', {
                'msg': ugettext_noop('Verify license result: license expired')}, user=request.POST.get('username'))
    else:
        operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '1', {
            'msg': ugettext_noop('Verify license result: invalid license')}, user=request.POST.get('username'))

    if not is_all_phoenix_online():
        is_a_lic = False
    ret_data = {'is_a_license': is_a_lic, 'type': license_type, 'start_time': start_time,
                'expire_time': expire_time, 'quota': quota, 'is_expired': is_expired}
    return HttpResponse(json.dumps(ret_data))


@login_required
@csrf_exempt
@check_permission('License', 'write')
def verify_sailfish_license_form(request):
    sailfish_license_file = request.FILES['sailfishlicensefile']
    license_bytes = sailfish_license_file.read()
    result = verify_sailfish_license(license_bytes)
    if result.get('result') != 'OK':
        operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '1', {
            'msg': ugettext_noop('Verify license result: invalid license')}, user=request.POST.get('username'))
        return HttpResponse(json.dumps({'result': 'FAILED', 'message': 'verify sailfish license failed'}))

    operation_log(request, ugettext_noop('Statistics_License'), ugettext_noop('Activate'), '0', {
        'msg': ugettext_noop('Verify license result: valid license')}, user=request.POST.get('username'))

    license_data = result.get('data', {})
    lang = 'en' if get_language() == 'en' else 'zh'
    return HttpResponse(json.dumps({
        'is_a_license': True,
        'type': license_data['type_name'][lang],
        'start_time': license_data['expiration'][0],
        'expire_time': license_data['expiration'][1],
        'quota': ("%.2f" % float(license_data.get('daily_quota', 0) / (1024**3))),
        'is_expired': False
    }))


@register.filter
def get_item(dictionary, key):
    return dictionary.get(key)


class CertFileForm(forms.Form):
    certfile = forms.FileField(
            label=ugettext_lazy('Select File'),
            widget=forms.FileInput(attrs={'onchange': 'upload_cert_file(this)', 'class': 'rs-asp-uploadBtn'})
    )

    keyfile = forms.FileField(
            label=ugettext_lazy('Select File'),
            widget=forms.FileInput(attrs={'onchange': 'upload_key_file(this)', 'class': 'rs-asp-uploadBtn'})
    )


class RulesFileForm(forms.Form):
    rulesfile = forms.FileField(
            label=ugettext_lazy('Select File'),
            widget=forms.FileInput(attrs={'onchange': 'upload_rule_file(this)', 'class': 'rs-asp-uploadBtn'})
    )


class ConfigFileForm(forms.Form):
    configfile = forms.FileField(
        label=ugettext_lazy('Select File'),
        widget=forms.FileInput(attrs={'onchange': 'upload_config_file(this)', 'class': 'rs-asp-uploadBtn'})
    )


@login_required
@expert_mode_required
@check_permission('Account_Default_Configurations', 'write')
def system_cert(request):
    if request.method == "POST":
        form = CertFileForm(request.POST, request.FILES)
        cert = ''
        key = ''

        if form.is_valid():
            cert = request.FILES['certfile'].read()
            key = request.FILES['keyfile'].read()

        error = ''
        if cert == '' or key == '':
            error = _('Please upload a valid certificate and private key.')
        else:
            # 'verify certificate and private key'
            cert_dirs = '/tmp/cert_test_{}'.format(int(time.time()))
            if not os.path.exists(cert_dirs):
                os.mkdir(cert_dirs)
            fn_key = os.path.join(cert_dirs, '__test_key.key')
            fn_crt = os.path.join(cert_dirs, '__test_cert.cert')
            asp_certs = AspCerts(key, cert, cert_dirs)
            effected = asp_certs.get_site_certs('test.com', fn_key, fn_crt)

            if effected:
                nginxConf = NginxConf()
                nginxConf.set_ssl_cert(cert, key)
            else:
                error = _('Please upload a valid certificate and private key.')

            shutil.rmtree(cert_dirs, True)

        op_code = '1' if error else '0'
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), op_code, {
            'msg': ugettext_noop('Management interface SSL certificate.')
        })
        return HttpResponse(json.dumps({'error': error}),
                            content_type=CONTENT_TYPE_PLAIN)


def system_cert_download(request):
    conf = ConfDb()
    cert = conf.get_value('cluster/ssl/root_ca_crt', '')
    response = HttpResponse(cert, content_type='application/oct')
    response['Content-Disposition'] = 'attachment; filename=root_ca.crt'
    return response


@login_required
@check_permission('Account_Default_Configurations', 'read')
def system_cert_download2(request):
    return system_cert_download(request)


def update_prepare(type):
    def _prepare():
        upgradeConf = UpgradeConf()

        if type == "upgrade":
            patch = upgradeConf.get_uploaded_patch()
            if not patch:
                return 'error: can not parse patch info'
            metajson = patch.getMetaInfo()
        elif type == "rollback":
            # get meta info from Git
            path = get_release_file()
            ret, metajson, _ = exe_with_output("cd {0}; sudo git show HEAD~0:manifest.json".format(path), False)
            if ret != 0:
                return "error: failed to get previous version information!"
            logger.debug("get previous version's meta-data:\n%s" % str(metajson))
            try:
                metajson = json.loads(metajson)
            except Exception as e:
                return "except: can not json-parse the metadata=%s" % str(metajson)
        else:
            return "error: bug found in source code!"

        build_no = metajson['build_no']
        git_commit = metajson['git_commit']
        target_version = '{0}.{1}'.format(git_commit, build_no)

        upgradeConf.set_asp_conf_values({'_private/upgrade/target_version': target_version})

    def _decorator(func):
        def _wrapper(*args, **kwargs):
            error = _prepare()
            if error:
                return error

            return func(*args, **kwargs)

        return _wrapper

    return _decorator


@update_prepare("upgrade")
def do_upgrade():
    result = {}
    try:
        # get zip file
        upgradeConf = UpgradeConf()
        filename = upgradeConf.get_upgrade_file_path()

        # launch Installer
        logger.info("starting upgrade now!")
        return upgradeConf.exc_asp_conf_command(['upgrade', filename], sync=False)
    except Exception as e:
        result['result'] = e.message

    return result


@update_prepare("rollback")
def do_rollback():
    result = {}

    try:
        logger.info("starting rollback now!")
        return UpgradeConf().exc_asp_conf_command(['rollback'], sync=False)
    except Exception as e:
        result['result'] = e.message

    return result


def is_network_config_changed(a_network_config, b_network_config):
    node_id_a = a_network_config.keys()
    node_id_b = b_network_config.keys()
    all_node_id = list(set(node_id_a).union(set(node_id_b)))
    for node_id in all_node_id:
        if node_id == '0':
            logger.info('there is a node with node_id 0 in configuration file')
            continue
        for role in ('admin', 'external', 'internal', 'log', 'keepalived'):
            for item in ('ip', 'netmask', 'gateway', 'dual_ip', 'dual_netmask', 'dual_gateway'):
                old_value = a_network_config.get(node_id, {}).get(role, {}).get(item, {}).get('_value')
                new_value = b_network_config.get(node_id, {}).get(role, {}).get(item, {}).get('_value')
                if old_value != new_value and (old_value or new_value):
                    logger.info("======first different is config['cluster']['node_list']['{}']['{}']['{}']".format(node_id, role, item))
                    logger.info('======old value is {}'.format(old_value))
                    logger.info('======new value is {}'.format(new_value))
                    return True
    return False

def is_abd_server_changed(a, b):

    a_pg_master = a.get('postgresql', {}).get('master_ip', {}).get('_value')
    b_pg_master = b.get('postgresql', {}).get('master_ip', {}).get('_value')
    if a_pg_master is not None and b_pg_master is not None \
        and a_pg_master != b_pg_master:  # 兼容23.03升级上来， 老的配置没有pg master
        return True

    return False

def is_has_previous_asp():
    msg = _('No previous version')
    run_git_c_no = get_current_git_commit_no()
    etc_asp_git = Git("/etc/asp")
    ret_code, conf_contents, cmd = etc_asp_git.git_command("show HEAD:release/conf_shared/asp_conf.json", False)
    if ret_code != 0:
        return False, msg
    ret_code, manifest_contents, cmd = etc_asp_git.git_command('show HEAD:manifest.json', False)
    if ret_code != 0:
        return False, msg

    try:
       a = json.loads(conf_contents)
       a_conf = a
       a_network_config = a['cluster']['node_list']
       a = json.loads(manifest_contents)
       a_commit = a['git_commit']

       with open('/etc/asp/release/conf_shared/asp_conf.json') as f:
           b = json.load(f)
       b_conf = b
       b_network_config = b['cluster']['node_list']

       with open('/etc/asp/manifest.json') as f:
           b = json.load(f)
       b_commit = b['git_commit']
       a_version = a['cluster_version']

       if a['layout'] == 'abd' and (get_major_version(a_version) in ['23.09', '23.09.1', '23.09.2', '23.09.3', '23.11', '23.11.1']):
           # ABD：不允许回滚到23.09、23.09.1、23.09.2、23.09.3、23.11、23.11.1
           # RAS：不受上述版本号限制，自由回滚
           # cause：ABD缺陷改名及其相关bug修复后不支持回滚到23.09、23.09.1、23.09.2、23.09.3、23.11、23.11.1，RAS因在缺陷改名后重构固暂不受影响
           return False, msg


       if a_commit == b_commit:
           return False, msg

       if is_network_config_changed(a_network_config, b_network_config):
           return False, _('Cannot rollback because NIC configuration or cluster nodes has been changed.')

       if is_abd_server_changed(a_conf, b_conf):
           return False, _('Cannot rollback because API Role changed')

       ret, output, cmdline = exe_with_output('git -C /etc/asp show release/bin/abd_utils/models/app_site_model.py|grep "created_time = BigIntegerField()"', excep_on_exit=False)
       if ret == 0:
           return False, msg
       return True, ''
    except Exception as e:
       logger.error('is_has_previous_asp exception: {}'.format(e))
       return False, msg


def get_previous_asp_info():
    version_name = None
    git_commit = None
    build_time = None
    cluster_version = None

    etc_asp_git = Git("/etc/asp")
    ret, current_manifest_contents, _ = etc_asp_git.git_command('show HEAD:manifest.json', False)
    if ret == 0:
        version_info = json.loads(current_manifest_contents)
        version_name = version_info.get("version_name")
        git_commit = version_info.get("git_commit")
        build_time = version_info.get("build_time")
        cluster_version = version_info.get("cluster_version")

    return [version_name, git_commit, build_time, cluster_version]


@login_required
@csrf_exempt
@check_permission('System_General', 'write')
def system_updatelib(request):
    if request.method == "POST":
        logger.info('start to upload lib file')
        f = request.FILES['lib']
        upgradeConf = UpgradeConf()
        tmp = '/tmp/txsafe/good_bot_ip.csv'
        with open(get_upgrade_lib_path(tmp), 'wb') as of:
            for chunk in f.chunks():
                of.write(chunk)

        dest_file='/etc/phoenix/etc/apps/phoenix/lookups/good_bot_ip.csv'
        bak_file = '/etc/phoenix/etc/apps/phoenix/lookups/good_bot_ip.csv_bak'
        exe_with_output("mv {} {}".format(dest_file, bak_file), False)
        exe_with_output("mv {} {}".format(tmp, dest_file), False)
        logger.info('upload lib file done, start to verify')

        return HttpResponse(json.dumps({"result": 0}), content_type=CONTENT_TYPE_PLAIN)

def get_update_time():
    file = '/etc/asp/.git'
    if not os.path.exists(file):
        return ''

    filetime = time.localtime(os.stat(file).st_mtime)
    return time.strftime('%Y-%m-%d %H:%M:%S', filetime)

@login_required
@csrf_exempt
@check_permission('System_Update', 'read')
def system_update(request):
    error = None
    version = None
    cluster_version = None
    step = 1

    has_previous_asp, can_not_rolle_back_msg = is_has_previous_asp()
    previous_asp_version_name, previous_asp_git_commit, previous_asp_build_time, previous_asp_cluster_version = get_previous_asp_info()

    if previous_asp_cluster_version is None:
        previous_asp_cluster_version = previous_asp_git_commit

    current_is_debug = get_current_is_debug()
    # tempPatchPath = '/tmp/txsafe_update.bin'
    upgradeConf = UpgradeConf()
    baseConf = BaseConf()

    nginxConf = NginxConf()
    license_info = nginxConf.get_license_info()
    canot_upgrade_tip = ''
    can_upgrade_total = True
    if not license_info.is_in_effect():
        canot_upgrade = _(' Cannot upgrade. ')
        if license_info.license_phase == LicenseInfo.LP_No_License:
            canot_upgrade_tip = canot_upgrade + _('The system has not been activated.')
        elif license_info.license_phase == LicenseInfo.LP_Not_In_Effect or license_info.license_phase == LicenseInfo.LP_Expired:
            canot_upgrade_tip = license_info.error_short + canot_upgrade
        if current_is_debug == 0:
            can_upgrade_total = False

    if request.method == "POST" and can_upgrade_total:
        if not has_permission(request.user.username, 'System_Update', 'write'):
            return HttpResponse(status=403)
        try:
            if request.POST.get('action', '') == 'install':
                logger.info('start install')
                upgradeConf.set_asp_conf_values({'_private/upgrade/progress': 'PrepareStage',
                                             '_private/upgrade/target_version': None,
                                             '_private/upgrade/asp_url': None,
                                             '_private/upgrade/error': None})
                operation_log(request, ugettext_noop('System'), ugettext_noop('Install'), '0', {
                    'msg': ugettext_noop('Begin to upgrade patch, upgrade results are subject to actual conditions')}, user=request.POST.get('username'))
                result = do_upgrade()
                if result['result'] != 'IN_QUEUE':
                    upgradeConf.set_asp_conf_values({'_private/upgrade/progress': 'EndStage',
                                                 '_private/upgrade/error': result['result']})
                logger.info('end install == {0}'.format(json.dumps(result)))
                return result

            logger.info('start to upload file')

            f = request.FILES['pkg']
            if f.size > (1024 << 20):
                # Bigger than 1G
                error = _("The file is too big.")
                return {'error': error}
                pass

            with open(upgradeConf.get_upgrade_file_path(), 'wb') as of:
                for chunk in f.chunks():
                    of.write(chunk)
        except IOError as e:
            logger.error("Upload upgrade file exception:{}".format(str(e)))
            if e.errno == errno.ENOSPC:
                error = _("Insufficient disk space")
            else:
                error = str(e)
        except Exception as e:
            logger.error("Upload upgrade file exception:{}".format(str(e)))
            error = str(e)

        if error:
            return {'error': error}

        logger.info('upload file done, start to verify')
        patch = upgradeConf.get_uploaded_patch()
        if patch:
            metajson = patch.getMetaInfo()
            is_debug_patch = metajson['is_debug']
            if current_machine_hardware_name() != metajson.get('machine_hardware_name', 'x86_64'):
                error = _('The current node hardware type is {}, and the upgrade package type is {}, which does not match.').format(current_machine_hardware_name(), metajson.get('machine_hardware_name', 'x86_64'))
            elif is_debug_patch != current_is_debug:
                if current_is_debug:
                    error = _('Release patch cannot be installed on debug patch')
                else:
                    error = _('Debug patch cannot be installed on release patch')

                logger.info("Installation between debug and release patch is not allowed")
                operation_log(request, ugettext_noop('Access'), ugettext_noop('Update'), '1',
                              {'msg': ugettext_noop('Installation between debug and release patch is not allowed')},
                              user=request.POST.get('username'))
            elif get_layout().lower() != metajson['layout'].lower() and (get_layout().lower() != 'waf' or metajson['layout'].lower() != 'ngwaf'):
                error = _('Installation between patches of different layout is not allowed')
                logger.error('Not allow install diffent layout: {} {}'.format(get_layout(), metajson['layout']))
            else:
                build_no = metajson['build_no']
                current_lang = normalize_lang(get_language())
                logger.info("current language:{}".format(repr(current_lang)))
                version = metajson['version_name']
                cluster_version = metajson['cluster_version']
                step = 2
                operation_log(request, ugettext_noop('System'), ugettext_noop('Import'), '0',
                                {'msg': ugettext_noop('Patch has been uploaded'), 'spliceMsg': ' : ' + cluster_version},
                                user=request.POST.get('username'))

            patch.cleanup()
        else:
            error = _('Invalid Patch')
            operation_log(request, ugettext_noop('System'), ugettext_noop('Import'), '1', {
                'msg': ugettext_noop('Invalid Patch')}, user=request.POST.get('username'))

        if error:
            os.remove(upgradeConf.get_upgrade_file_path())

    cluster_nodes = get_cluster_node_list()
    node_num = 0 if not cluster_nodes else len(cluster_nodes)

    update_time = get_update_time()
    return {
        'error': error,
        'version': version,
        'step': step,
        'has_previous_asp': has_previous_asp,
        'previous_asp_version': previous_asp_version_name,
        'previous_asp_build': previous_asp_git_commit,
        'previous_asp_update_time': previous_asp_build_time if not update_time else update_time,
        'in_container': in_container(),
        'in_k8s': in_k8s(),
        'can_upgrade': can_upgrade_total,
        'canot_upgrade_tip': canot_upgrade_tip,
        'cluster_version': cluster_version,
        'previous_asp_cluster_version': previous_asp_cluster_version,
        'notice': can_not_rolle_back_msg,
        'node_num': node_num
    }


@csrf_exempt
def system_update_status(request):
    result = {'result': 'OK', 'stat': {}}
    conf = BaseConf()
    update_stage = conf.get_value('_private/upgrade/progress')
    update_error = conf.get_value('_private/upgrade/error', '')

    # avoid _private/upgrade/progress in asp_conf.json modified by another process, because there is no file lock for asp_conf.json
    if update_stage == 'ConfirmStage':
        try:
            with open('/var/log/asp/Installer/state.json') as f:
                update_stage = json.load(f)['progress']
        except:
            pass

    if update_stage == 'EndStage':
        if update_error:
            result['stat']['status'] = 'FINISHED'
        else:
            cluster_upgrade_finished, update_error = service_mgr_rest_api.get_cluster_upgrade_finished()
            logging.info('Cluster upgrade is finished status ={0}'.format(cluster_upgrade_finished))
            if cluster_upgrade_finished:
                cluster_nodes = get_cluster_node_list()
                for i in cluster_nodes.values():
                    if i.get('upgrade'):
                        break
                else:
                    result['stat']['status'] = 'FINISHED'

    if update_error:
        node_id = ''
        offset_ = update_error.find(': ')
        if update_error.startswith('node ') and offset_ > 0:
            node_id = _('The Node {}: ').format(update_error[5:offset_])
            update_error = update_error[offset_+2:]

        if update_error.startswith('CheckBaseError:'):
            update_error = update_error[15:]

        if update_error.startswith('Missing package'):
            update_error = node_id + _('Please use update patch that includes base packages')
        elif 'A' <= update_error[0] <= 'Z':
            update_error = node_id + _(update_error)
        else:
            update_error = node_id + _('Failed to upgrade, the detail') + '<br>' + _(update_error)

    result['update_error'] = update_error
    result['update_stage'] = update_stage

    return return_query_task_callback(request, result)


@login_required
@expert_mode_required
@csrf_exempt
@check_permission('System_Rollback', 'write')
def system_rollback(request):
    if request.POST.get('action', '') == 'rollback':
        has_previous_asp, can_not_rolle_back_msg = is_has_previous_asp()
        if not has_previous_asp:
            return HttpResponse(json.dumps({'result': 'FAILED', 'error': can_not_rolle_back_msg}), content_type=CONTENT_TYPE_JSON)
        upgradeConf = UpgradeConf()
        logger.info('start rollback')
        upgradeConf.set_asp_conf_values({'_private/upgrade/progress': 'PrepareStage',
                                         '_private/upgrade/target_version': None,
                                         '_private/upgrade/asp_url': 'rollback',
                                         '_private/upgrade/error': None})
        operation_log(request, ugettext_noop('System'), ugettext_noop('Update'), '0', {
            'msg': ugettext_noop('Begin to rollback')}, user=request.POST.get('username'))
        result = do_rollback()
        if result['result'] != 'IN_QUEUE':
            upgradeConf.set_asp_conf_values({'_private/upgrade/progress': 'EndStage',
                                             '_private/upgrade/error': result['result']})
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def normalize_lang(lang):
    normal_lang = locale.normalize(lang)
    ret_lang = ''
    if normal_lang == lang:
        # "Normalize {0} failed.".format(lang)
        ret_lang = normal_lang
    else:
        lan_first_half, lan_second_half = normal_lang.split(".")[0].split("_")
        ret_lang = "-".join([lan_first_half, lan_second_half])
    return ret_lang.lower()



node_status_dict = {
    'ERRNO_SERVICE': {
        'description': ugettext_noop('Service failure is found on this node.'),
        'resolution': ugettext_noop('Reboot the node.')
    },
    'ERRNO_010': {
        'description':
            ugettext_noop('Number of CPU cores or memory space on this node has exceeded limit according to the license.'),
        'resolution':
            ugettext_noop(
                    'Reduce CPU cores or memory space on this node and reactivate license. For more information, please go to \"System\" > \"License\" page on this node.')
    },
    'ERRNO_024': {
        'description': ugettext_noop('Service is abnormal or initializing on this node.'),
        'resolution': ugettext_noop('Waiting for node recovery. If the node does not recover by itself, reboot the node.')
    },
    'Inactive': {
        'description': ugettext_noop('Protection is not enabled because no license is found.'),
        'resolution': ugettext_noop('The system has not been activated.')
    },
    'Offline': {
        'description': ugettext_noop('This node cannot communicate with group.'),
        'resolution': ugettext_noop('Waiting for node recovery. If the node does not recover by itself, reboot the node.')
    },
    'Rollbacking': {
        'description': ugettext_noop('This node is rolling back.'),
        'resolution': ugettext_noop('Please wait for the rollback to complete.')
    },
    'Upgrading': {
        'description': ugettext_noop('This node is being upgraded.'),
        'resolution': ugettext_noop('Please wait for the upgrade to complete.')
    }
}


def get_service_error(status_node_info):
    if not status_node_info:
        return ugettext_noop('Service Monitor'), 'ERRNO_000'

    service_name_map = {
        'asp_webconsole': _("WebConsole"),
        'nginx': 'Nginx',
        'zookeeper': 'Master Node',
        'phoenix': 'Log Analysis',
        'snmpd': 'snmp',
        'rsyslog': 'rsyslog',
        'keepalived': 'keepalived',
        'asp_bta_service': _('BTA'),
        'asp_repu_service': _('Threat Intelligence Service'),
        'ai_service': _('AI intelligent detection'),
        'asp_api_gateway': _('API Gateway'),
        'sailfish_server': _('Sailfish Server'),
        'clickhouse-server': _('Clickhouse Server'),
        'asp_abd_service': _('ABD Service'),
        'flowlearn_service': _('FlowLearn Service'),
        'httpcap': _('HTTP Capture Service'),
        'penguin': _('Penguin Service'),
        'access_log_sender': _('Access Log Sender'),
        'chronyd': _('NTP Service'),
        'llm_service': _('LLM Service detection'),
    }

    for i in range(10):
        port = 20170 + i
        service_name_map['redis-server-{}'.format(port)] = 'Redis({})'.format(port)

    for key, value in status_node_info.items():
        if key not in service_name_map.keys():
            continue
        status = value.get('status')
        if status != 'OK':
            return ugettext_noop(service_name_map[key]), status
        pass
    return None


def get_cluster_node_state_list():

    state_list = []
    cluster_nodes = ClusterNodeStat().get_cluster_node_state(force_update=True)
    cluster_nodes = sorted(cluster_nodes.iteritems(), key=lambda nl: int(nl[0]))
    for node_id, val in cluster_nodes:
        if node_id.startswith('_'):
            continue
        deleted = val.get('_deleted')
        if deleted:
            continue

        ip = val.get('_admin_ip') or ''
        role = ' | '.join(val.get('_role') or [])
        ver = ''
        description = ''
        resolution = ''
        service_error_info = ''

        status_node = val.get('status')
        upgrade_node = val.get('upgrade')
        service_error = None
        zk_client_list = get_all_zookeeper_client(node_id, ip)
        var_disk_usage = 0
        root_disk_usage = 0
        core_dump = False
        cookie_http_only = False
        node_transparent_status = None

        cpu_model = _("CPU Model") + ": "
        cpu_count = _("CPU Cores") + ": "
        max_memory = _("Memory Capacity") + ": "
        max_disk_space = _("Disk Capacity") + ": "

        if not zk_client_list:
            status = 'Offline'
            state = 'ERROR'
        elif upgrade_node:  # ZkClient_1: Upgrade is connecting to ZK
            if upgrade_node.get('_action') == 'rollback':
                status = 'Rollbacking'
            else:
                status = 'Upgrading'
            state = 'WARNING'
        elif 'Log Sender' not in zk_client_list:  # ZkClient_2: Log Sender is not connecting to ZK
            service_error = 'Log Sender', 'ERRNO_006'
        elif not status_node:  # ZkClient_3: Service Mgr is not connecting to ZK
            service_error = 'Service Manager', 'ERRNO_007'
        else:  # Service Mgr is connecting to ZK(Online)
            ver = status_node.get('_cluster_version')
            license_status = status_node.get('_license_status')
            info = status_node.get('_info', {})
            if not isinstance(info, dict):
                # compatible old status
                # old status is a list [version < 17.16]
                info = {}
            service_error = get_service_error(info.get('service', {}))
            if service_error:
                pass
            elif license_status:
                status = license_status
                state = 'WARNING'
            else:
                status = 'Online'
                state = 'OK'

            node_system_status = info.get('system', {})
            node_disk_status = info.get('disk', {})
            core_dump_status = node_system_status.get('core_dump', False)
            if core_dump_status and core_dump_status.get('status', None) != 'OK':
                core_dump = True

            cookie_http_only_status = node_system_status.get('cookie_http_only', {})
            if cookie_http_only_status.get('status', 'OK') != 'OK':
                cookie_http_only = True

            root_disk_status = node_disk_status.get('/', False)
            if root_disk_status and root_disk_status.get('status', 0) != 0:
                root_disk_usage = root_disk_status.get('status')

            var_disk_status = node_disk_status.get('/var', False)
            if var_disk_status and var_disk_status.get('status', 0) != 0:
                var_disk_usage = var_disk_status.get('status')

            node_stats_system = info.get('stats', {}).get('system', {})

            node_transparent_status = node_stats_system.get('node_transparent_status')

            cpu_model += node_stats_system.get("cpu_model", "")
            cpu_count += str(node_stats_system.get("cpu_count", ""))

            m = node_stats_system.get("mem_total_bytes")
            max_memory += str(int(math.ceil(m / 1024.0 ** 3))) + "GB" if m else ""

            d = node_stats_system.get("disk_total_bytes")
            max_disk_space += str(int(math.ceil(d / 1000.0 ** 3))) + "GB" if d else ""

        if service_error:
            status = service_error[1]
            state = 'ERROR'
            service_error_info = service_error[0]
            if status in node_status_dict:
                desc_reso = node_status_dict.get(status)
            else:
                desc_reso = node_status_dict.get('ERRNO_SERVICE')
        else:
            desc_reso = node_status_dict.get(status)

        if desc_reso:
            description += desc_reso['description']
            resolution += desc_reso['resolution']

        var_size_m = val.get('_var_size_m')
        es_size_m = var_size_m

        conf_db = ConfDb()
        is_search_head = conf_db.get_min_phoenix_id() == int(node_id)
        is_sailfish_head = conf_db.get_min_sailfish_id() == int(node_id)

        state_list.append(
            {'id': node_id, 'ip': ip, 'role': role, 'status': status, 'state': state,
             'service_eror': _(service_error_info), 'description': _(description),
             'resolution': _(resolution), 'var_size_m': var_size_m, 'es_size_m': es_size_m, 'version': ver,
             'core_dump': core_dump, 'cookie_http_only': cookie_http_only,
             'machine_hardware_name': val.get('_machine_hardware_name'),
             'var_disk_usage': var_disk_usage, 'root_disk_usage': root_disk_usage,
             'is_search_head': is_search_head,
             'node_transparent_status': node_transparent_status,
             'is_sailfish_head': is_sailfish_head,
             'cpu_model': cpu_model,
             "cpu_count": cpu_count,
             "max_memory": max_memory,
             "max_disk_space": max_disk_space,
             })
    return state_list


@sensitive_post_parameters()
@csrf_protect
@never_cache
def login(request, template_name='registration/login.html',
          redirect_field_name=REDIRECT_FIELD_NAME,
          authentication_form=AuthenticationForm,
          current_app=None, extra_context=None):
    """
    Displays the login form and handles the login action.
    """
    conf = WebconsoleConf()
    captcha = conf.is_captcha_enabled(get_current_is_prod_pkg())
    totp_enabled = conf.is_2fa_enabled()
    if captcha:
        authentication_form = AuthenticationCaptchaForm
    should_kicked = conf.is_kicked_enabled()

    redirect_to = request.REQUEST.get(redirect_field_name, '')
    error_type = 0

    def render_template():
        current_site = get_current_site(request)
        is_build_debug = get_current_is_debug()
        is_prod_pkg = get_current_is_prod_pkg()
        brand = get_brand_str()

        context = {
            'form': form,
            redirect_field_name: redirect_to,
            'site': current_site,
            'layout': get_layout(True),
            'build_commit_no': get_current_git_commit_no(),
            'site_name': current_site.name,
            'captcha': captcha,
            'totp_enabled': totp_enabled,
            'is_build_debug': is_build_debug,
            'is_prod_pkg': is_prod_pkg,
            'brand': brand,
            'error_type': error_type,
            'build_hash_and_layout_hash': get_build_hash_and_layout_hash(),
            'copyright_year': get_copyright()
        }
        if extra_context is not None:
            context.update(extra_context)
        resp = TemplateResponse(request, template_name, context,
                                current_app=current_app)
        resp['X-Login-View'] = 1
        return resp

    is_inner_login = request.POST.get(
        'inner-login', request.GET.get('inner-login', '')
    ) == '1'

    is_mod_pwd_redirect = request.POST.get(
        'modify_pwd_success', request.GET.get('modify_pwd_success', '')
    ) == '1'

    if 'HTTP_X_REAL_IP' in request.META:
        x_real_ip = compress_IP(request.META['HTTP_X_REAL_IP'], 'err')
        if x_real_ip == 'err':
            return HttpResponse(status=400)
        request.META['HTTP_X_REAL_IP'] = x_real_ip

    if request.method == "POST" and error_type == 0:
        username = request.POST.get('username')
        password = request.POST.get('password')
        client_ip = get_client_ip(request)
        form = authentication_form(request, data=request.POST)
        logger.debug('client_ip is {}'.format(client_ip))
        logger.debug('username is {}'.format(username))
        if conf.is_ip_locked(client_ip):
            error_type = 1
            return render_template()
        # 禁用 锁定无法登录
        if conf.is_user_exist(username) and conf.judge_user_state(username=username) != 2:
            error_type = 1
            return render_template()
        if form.is_valid():
            if totp_enabled:
                try:
                    totp_token = int(request.POST.get('totp_token', 0))
                except ValueError:
                    totp_token = 0

                device, config = get_totp(username)
                if not device or not config or not device.verify(totp_token):
                    error_type = 7
                    return render_template()

            if conf.get_password_period(username) == 0:
                return base_render_to_response(request, 'registration/password_modify.html', {
                    'username': username,
                    'token': hashlib.sha512(conf.get_user_password(username)+str(int(uptime.uptime())/300)).hexdigest() })

            # Ensure the user-originating redirection url is safe.
            if not is_safe_url(url=redirect_to, host=request.get_host()):
                redirect_to = resolve_url(settings.LOGIN_REDIRECT_URL)

            conf.remove_ip_failed_info(client_ip)

            # Okay, security check complete. Log the user in.
            auth_login(request, form.get_user())

            # Reset Expire
            request.session.set_expiry(None)

            operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '0', {
                'msg': ugettext_noop('Successfully logged in to WebConsole')}, user=request.POST.get('username'))

            # 将其他人踢下线
            if should_kicked:
                kick_user_session(request)
                request.session['_kicked'] = 0
                request.session.save()

            conf.reset_last_login_time(username)
            if is_inner_login:
                request.session['last_activity'] = time.time()
                role = WebconsoleConf().get_user_role(username)
                response = {"result": 'OK', "current_user": username, "current_role": role}
                return HttpResponse("<script>top.onGetSessionMessage("+json.dumps(response) + ")</script>")

            if request.session.get("last_activity") is None:
                request.session['last_activity'] = time.time()
            return HttpResponseRedirect(redirect_to)

        if 'captcha' in form.errors:
            error_type = 2
        else:
            login_status = BaseConf().get_value('_private/login/%s/status' % username)
            if login_status == 'Server connection timeout.':
                logger.debug('login timeout')
                error_type = 3  # timeout
            elif login_status == 'Remote authentication is not enabled.':
                logger.debug('Remote authentication is not enabled')
                error_type = 4
            elif username == 'admin' and conf.is_admin_user_disabled():
                logger.debug('admin user is disabled.')
                error_type = 9
            else:
                logger.debug('wrong username or password')
                error_type = 1  # password error or locked
                if conf.is_user_exist(username):
                    if not conf.is_user_locked(username):
                        conf.set_fail_time(username)
                        if WebconsoleConf().is_user_locked(username):
                            operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
                                'msg': ugettext_noop('Account is locked')}, user=username)
                        else:
                            operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
                                'msg': ugettext_noop('Failed to log in to WebConsole')}, user=username)

                if conf.add_ip_failed_info(client_ip, username, password):
                    operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '1', {
                        'msg': ugettext_noop('IP is locked')}, user=username)
    else:
        form = authentication_form(request)

    is_kicked = int(request.session.get('_kicked', 0)) == 1
    if error_type == 0 and is_inner_login and not is_mod_pwd_redirect:
        error_type = 6 if not is_kicked else 8

    if is_kicked:
        request.session.set_expiry(12)

    return render_template()


@never_cache
def rcm_login(request, redirect_field_name=REDIRECT_FIELD_NAME):
    redirect_to = request.REQUEST.get(redirect_field_name, '')
    sso_token = ""
    if request.method == "GET":
        sso_token = request.GET.get('sso_token', '')
    elif request.method == "POST":
        sso_token = request.POST.get('sso_token', '')

    if sso_token == '':
        logger.error('no sso_token in rcm_login url')
        return HttpResponseRedirect(resolve_url(settings.LOGIN_URL))

    user = authenticate(username='RCM', password=sso_token)

    if user is not None:
        if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':  # ABD layout
            redirect_to = resolve_url('/index?login=sso')
        else:
            # Ensure the user-originating redirection url is safe.
            if not is_safe_url(url=redirect_to, host=request.get_host()):
                redirect_to = resolve_url(settings.LOGIN_REDIRECT_URL)

        # Okay, security check complete. Log the user in.
        auth_login(request, user)

        operation_log(request, ugettext_noop('Access'), ugettext_noop('Login'), '0', {
            'msg': ugettext_noop('Successfully logged in to WebConsole')}, user=user.get_username())

        # avoid being forced to log out due to timeout
        request.session['last_activity'] = time.time()
        return HttpResponseRedirect(redirect_to)
    else:
        logger.debug('failed to login with rcm sso token')
        return HttpResponseRedirect(resolve_url(settings.LOGIN_URL))

@sensitive_post_parameters()
@csrf_exempt
@django_login_required
def password_change_ajax(request, *args, **kwargs):
    if request.method == "POST":
        mark = 'input[name=old_password]'
        try:
            username = request.user.username
            old_password = request.POST['old_password']
            new_password = request.POST['new_password1']

            conf = WebconsoleConf()
            old_passwd_hash = conf.get_user_password(username)
            if not check_password(old_password, old_passwd_hash):
                raise ValueError(_(' Wrong password! Please try again.'))

            err = old_password_check(username, new_password)
            if err:
                mark = 'input[name=new_password1]'
                raise ValueError(err)

            request.user.set_password(new_password)
            request.user.save()
            update_session_auth_hash(request, request.user)
            conf.set_user_password(request.user.username,
                                      make_password(new_password))

            operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '0',
                          {'msg': ugettext_noop('Change user password: '), 'spliceMsg': request.user.username}, user=request.user.username)

            kick_user_session(request, flush=True)
            result = dict(
                success=True,
                msg='Success'
            )
        except ValueError as e:
            result = dict(
                success=False,
                mark=mark,
                msg=e.message
            )

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)
    pass

def old_password_check(username, newpassword):
    conf = WebconsoleConf()
    cur_passwd = conf.get_user_password(username)
    password_config = conf.get_password_config(True)
    history_check = password_config['history_check']
    if history_check == 0:
        return
    old_passwds = conf.get_user_history_password(username)
    old_passwds.append(cur_passwd)
    for passwd_hash in old_passwds[-history_check:]:
        if check_password(newpassword, passwd_hash):
            return _('New password should be different from the lastest {0} passwords that have been used before').format(history_check)
    return

def password_change_ajax2(request, *args, **kwargs):
    result = { 'success': False, 'msg': '' }
    if request.method == "POST":
        try:
            username = request.POST['username']
            new_password = request.POST['new_password']
            token = request.POST['token']

            conf = WebconsoleConf()
            old_passwd_hash = conf.get_user_password(username)
            now = int(uptime.uptime()) / 300
            if token != hashlib.sha512(old_passwd_hash+str(now)).hexdigest() and token != hashlib.sha512(old_passwd_hash+str(now-1)).hexdigest():
                raise ValueError(_('Password token has expired.'))

            err = old_password_check(username, new_password)
            if err:
                raise ValueError(err)

            conf.set_user_password(username, make_password(new_password))

            kick_user_session(request, flush=True, kicked_user=username)

            operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '0',
                          {'msg': ugettext_noop('Change user password: '), 'spliceMsg': username}, user=username)

            result['success'] = True
        except ValueError as e:
            result['msg'] = e.message

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)


@login_required
def password_change_done(request,
                         template_name='registration/password_change_done.html',
                         current_app=None, extra_context=None):
    is_build_debug = get_current_is_debug()
    is_prod_pkg = get_current_is_prod_pkg()
    brand = get_brand_str()
    context = {
        'title': _('Password has been changed!'),
        'is_build_debug': is_build_debug,
        'is_prod_pkg': is_prod_pkg,
        'brand': brand,
    }
    if extra_context is not None:
        context.update(extra_context)

    if current_app is not None:
        request.current_app = current_app

    return base_render_to_response(request, template_name, context)


@login_required
@csrf_exempt
def user_list(request):
    webConsoleConf = WebconsoleConf()

    if request.method == 'POST':

        if not has_permission(request.user.username, 'Delete_User', 'write'):
            return HttpResponse(status=403)
        result = {'result': ''}
        delete = json.loads(request.body)
        name = delete.get('username')
        role = webConsoleConf.get_user_role(name)
        if name == 'admin':
            return HttpResponse(status=403)

        if not webConsoleConf.is_user_exist(name):
            result = {
                'result': 'error',
                'err_msg': _('User has been deleted already, please refresh this page again.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        if role not in query_user_management_policy(current_user=request.user.username,
                                                    action='roles_allowed_to_update'):
            result = {
                'result': 'error',
                'err_msg': _('You do not have this permission. Please contact your administrator.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        # Reset site operator
        clean_site_operator_by_user(name)


        # delete the user from Django DB
        try:
            user = User.objects.get(username=name)
            user.delete()
        except User.DoesNotExist:
            pass

        # delete the user from config file
        user_exist = webConsoleConf.is_user_exist(name)
        if user_exist:
            webConsoleConf.delete_user(name)
            result = {'result': 'ok'}
            operation_log(request, ugettext_noop('Access'), ugettext_noop('Delete'), '0', {
                'msg': ugettext_noop('User has been deleted: '), 'spliceMsg':name})
        else:
            result = {'result': 'error'}

        #delete user from username in app_site table
        if ConfDb().is_abd_server():
            from abd_utils.repositories.assets.app_site import AppSiteRepository
            app_site_repo = AppSiteRepository()
            app_site_repo.delete_username(name)

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    if not has_permission(request.user.username, 'User_List', 'read'):
        return HttpResponse(status=403)
    users = webConsoleConf.get_all_no_admin_users()
    return base_render_to_response(request, 'v2/user_list.html', {'users': users})


def validate_user_config(name, role, auth_mode, passwd, passwd_2, create_mode):
    if name is None:
        raise ValidationError(ugettext_noop('Username is required.'), code='invalid')

    # rule apply for create user only
    if create_mode:
        if WebconsoleConf().is_user_exist(name):
            raise ValidationError(ugettext_noop('User already exists!'), code='invalid')

        if len(name) < 6 or len(name) > 60:
            raise ValidationError(ugettext_noop('The length of the username should be 6 to 60 characters.'), code='invalid')

        if auth_mode == 'local' and not re.match('^[a-zA-Z\d]\w*$', name):
            raise ValidationError(ugettext_noop('Username must contain only letters, numbers and underscores, and cannot start with underscores!'))

        if auth_mode == 'remote' and not re.match('^[a-zA-Z\d][\w\.\-]*$', name):
            raise ValidationError(ugettext_noop('Username must contain only letters, numbers, dot, hyphen and underscores, and cannot start with dot, hyphen or underscores!'))

    if name.lower().startswith('api_'):
        raise ValidationError(ugettext_noop('The username should not start with API_ or api_.'), code='invalid')

    if role is None:
        raise ValidationError(ugettext_noop('Select a role for this user.'), code='invalid')
    elif role not in (WebconsoleConf().get_all_roles() + ['Administrator', 'Viewer', 'Operator', 'StatisticViewer']):
        raise ValidationError(ugettext_noop('Role can only be administrator or guest.'), code='invalid')

    if auth_mode is None:
        raise ValidationError(ugettext_noop('Select an authentication mode for this user.'), code='invalid')
    elif auth_mode != 'local' and auth_mode != 'remote':
        raise ValidationError(ugettext_noop('Authentication mode can only be Remote or Local.'), code='invalid')

    if passwd is None:
        raise ValidationError(ugettext_noop('Password is required.'), code='invalid')
    elif passwd_2 is None:
        raise ValidationError(ugettext_noop('Incorrect password.'), code='invalid')
    elif passwd != passwd_2:
        raise ValidationError(ugettext_noop('Passwords don\'t match.'), code='invalid')


def query_user_management_policy(current_user, action):
    if current_user == "admin":
        result = ["Administrator", "Operator", "Viewer", "StatisticViewer"]
    else:
        with open(get_release_file('web_admin/src/user_management_policy.json'), 'rb') as fp:
            user_mng_policy = json.loads(fp.read(), 'utf-8')
            current_role = WebconsoleConf().get_user_role(current_user)
            result = user_mng_policy.get(current_role, {}).get(action, [])

    if result:
        result.extend(sorted(WebconsoleConf().get_all_roles()))
    return result


@login_required
@csrf_exempt
@check_permission('Add_User', 'write')
def query_roles_allowed_to_Update(request):
    if request.method == 'GET':
        roles_allowed_to_create = query_user_management_policy(current_user=request.user.username,
                                                               action='roles_allowed_to_update')
        return JsonResponse({'roles': roles_allowed_to_create})


def sha1_str(passwd):
    hexadecimal = hashlib.sha1(passwd).hexdigest()
    sha_password = str()
    for i in range(0, len(hexadecimal)):
        if i % 2 == 0:
            sha_password = sha_password + str(int(hexadecimal[i:i + 2], 16)) + ','
    return str(sha_password)[0:-1]

@login_required
@csrf_exempt
@check_permission('Add_User', 'write')
def get_users_no_admin(request):
    users = WebconsoleConf().get_all_no_admin_users()
    name_of_logged_in_user = request.user.username
    roles_allowed_to_update = query_user_management_policy(current_user=name_of_logged_in_user,
                                                           action="roles_allowed_to_update")
    get_system_account_management_users(users, roles_allowed_to_update)
    for user in users:
        user['config_2fa'] = WebconsoleConf().is_2fa_enabled()

    return HttpResponse(json.dumps(users, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@csrf_exempt
@check_permission('Add_User', 'write')
def create_user(request):

    if request.method == 'POST':
        new_user_name = request.POST.get('user_name')
        role = request.POST.get('role')
        auth_mode = request.POST.get('auth_mode')
        # 有效期 (可能是0也可能是时间戳)
        try:
            validity_period = int(request.POST.get('validity_period'))
        except:
            result = {
                'result': 'error',
                'err_msg': _('Invalid character(s) found ')
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
        # 手动禁用
        manual_disable = request.POST.get('manual_disable') == 'true'

        passwd = passwd_2 = ''
        if auth_mode == 'local':
            passwd = request.POST.get('password')
            passwd_2 = request.POST.get('password_again')

        if WebconsoleConf().get_count_no_admin_users() >= 100:
            result = {
                'result': 'error',
                'err_msg': _('The number of users has exceeded 100.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        if role not in query_user_management_policy(current_user=request.user.username, action='roles_allowed_to_update'):
            result = {
                'result': 'error',
                'err_msg': _("Role {role} doesn't exist, or you do not have this permission. Please contact your administrator.").format(role=role)
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        try:
            validate_user_config(new_user_name, role, auth_mode, passwd, passwd_2, create_mode=True)
        except ValidationError as err:
            result = {'result': 'error', 'err_msg': _(err.message)}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        # in cluster environment, when we delete user from another node,
        # the user is deleted from config file, but it is still in Django DB
        # so here we need to delete it
        User.objects.filter(username=new_user_name).delete()

        device, un_usage_varable = get_totp('admin')    # Don't rename 'un_usage_varable' to '_'

        if WebconsoleConf().is_2fa_enabled() and device:
            totp_algorithm = device.algorithm
            device, config_2fa = get_or_create_totp(new_user_name, totp_algorithm)
        else:
            device = None
            config_2fa = {}

        user = User.objects.create_user(username=new_user_name, password=passwd)
        group = Group.objects.get_or_create(name=role)[0]
        user.groups.add(group)

        WebconsoleConf().set_user(
            username=new_user_name,
            role=role,
            auth_mode=auth_mode,
            validity_period=validity_period,
            manual_disable=manual_disable,
            password=None if auth_mode == 'remote' else make_password(passwd),
            create_user=True,
            config_2fa=config_2fa
        )

        state = WebconsoleConf().judge_user_state(new_user_name)

        result = {'result': 'ok', 'state': state}
        validity_period_info = 0 if validity_period == 0 else datetime.datetime.fromtimestamp(validity_period).date()
        op_log_info = "username:{}, role:{}, auth_mode:{}, validity_period:{}, manual_disable:{}.".format(new_user_name,role,auth_mode,validity_period_info,manual_disable)
        operation_log(request, ugettext_noop('Access'), ugettext_noop('New'), '0', {
            'msg': ugettext_noop('User has been created: '), 'spliceMsg':new_user_name,'info':op_log_info})
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    return BaseTemplateResponse(request, 'v2/user_create.html')


@login_required
@csrf_exempt
@check_permission('Edit_User', 'write')
def modify_exist_user(request):
    conf = WebconsoleConf()

    if request.method == 'POST':
        name_of_target_user = request.POST.get('user_name')
        current_role_of_target_user = conf.get_user_role(name_of_target_user)
        current_user = request.user.username

        if current_role_of_target_user == "Administrator" and current_user != "admin":
            result = {
                'result': 'error',
                'err_msg': _('Only allow admin to modify the administrator account!'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        if not WebconsoleConf().is_user_exist(name_of_target_user):
            result = {
                'result': 'error',
                'err_msg': _('The user does not exist.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        new_role_of_target_user = request.POST.get('role')
        auth_mode = request.POST.get('auth_mode')
        passwd = passwd_2 = ''
        if auth_mode == 'local':
            passwd = request.POST.get('password')
            passwd_2 = request.POST.get('password_again')

        if new_role_of_target_user not in query_user_management_policy(current_user=current_user,
                                                                       action='roles_allowed_to_update'):
            result = {
                'result': 'error',
                'err_msg': _('You do not have this permission. Please contact your administrator.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        try:
            validate_user_config(name_of_target_user, new_role_of_target_user, auth_mode, passwd, passwd_2, create_mode=False)
        except ValidationError as err:
            result = {
                'result': 'error',
                'err_msg': _(err.message),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        if passwd != '':
            err = old_password_check(name_of_target_user, passwd)
            if err:
                result = {
                    'result': 'error',
                    'err_msg': err
                }

                return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        # Reset site operator if target user's role is changed
        if current_role_of_target_user != new_role_of_target_user:
            clean_site_operator_by_user(username=name_of_target_user)

        try:
            user = User.objects.get(username=name_of_target_user)
            user.delete()
        except User.DoesNotExist:
            pass

        user = User.objects.create_user(username=name_of_target_user, password=passwd)
        group = Group.objects.get_or_create(name=new_role_of_target_user)[0]
        user.groups.add(group)

        # 有效期 (可能是0也可能是时间戳)
        try:
            validity_period = int(request.POST.get('validity_period'))
        except:
            result = {
                'result': 'error',
                'err_msg': _('Invalid character(s) found ')
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
        # 手动禁用
        manual_disable = request.POST.get('manual_disable') == 'true'

        if passwd != '':
            WebconsoleConf().set_user(username=name_of_target_user, role=new_role_of_target_user, auth_mode=auth_mode, validity_period=validity_period, manual_disable=manual_disable, password=make_password(passwd), create_user=False)
        else:
            WebconsoleConf().set_user(username=name_of_target_user, role=new_role_of_target_user, auth_mode=auth_mode, validity_period=validity_period, manual_disable=manual_disable, create_user=False)

        state = WebconsoleConf().judge_user_state(name_of_target_user)
        result = {'result': 'ok', 'state': state}
        validity_period_info = 0 if validity_period == 0 else datetime.datetime.fromtimestamp(validity_period).date()
        op_log_info = "username:{}, role:{}, auth_mode:{}, validity_period:{}, manual_disable:{}.".format(name_of_target_user,new_role_of_target_user,auth_mode,validity_period_info,manual_disable)
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '0', {
            'msg': ugettext_noop('User info has been saved:'), 'spliceMsg':name_of_target_user,'info':op_log_info})
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    return BaseTemplateResponse(request, 'v2/user_edit.html')


@login_required
@csrf_exempt
@check_permission('Unlock_User', 'write')
def unlock_user(request):
    result = False
    conf = WebconsoleConf()
    if request.method == 'POST':
        username = request.POST.get('username', '')
        if conf.is_user_exist(username) and conf.reset_last_login_time(username) == 0:
            result = True
    op_code = '0' if result else '1'
    operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), op_code, {
                'msg': ugettext_noop('Release the locked state of {username}'), 'extra': {'username':username}})
    resultObj = {'result': result}
    return HttpResponse(json.dumps(resultObj), content_type=CONTENT_TYPE_JSON)


@login_required
@csrf_exempt
@check_permission('Account_Default_Configurations', 'write')
def set_lock_user_setting(request):
    result = False
    user_list = []
    conf = WebconsoleConf()
    if request.method == 'POST':
        lock_limit_enable = request.POST.get('lock_limit_enable') == 'true'
        try:
            lock_limit_days = int(request.POST.get('lock_limit_days'))
        except:
            lock_limit_days = request.POST.get('lock_limit_days')
        if isinstance(lock_limit_days, int) or lock_limit_days == '':
            if conf.set_lock_user_setting(lock_limit_enable, lock_limit_days):
                result = True

            op_code = '0' if result else '1'
            # info_lock_limit_days = lock_limit_days if lock_limit_days else _('Default Value')

            op_log_info = "lock_limit_enable:{}, lock_limit_days:{}".format(lock_limit_enable,lock_limit_days)
            operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), op_code, {
            'msg': ugettext_noop("Modify the user's long-term inactivity lock"), 'info':op_log_info})

            all_no_admin_users = conf.get_all_no_admin_users()
            name_of_logged_in_user = request.user.username
            roles_allowed_to_update = query_user_management_policy(current_user=name_of_logged_in_user,
                                                            action="roles_allowed_to_update")
            get_system_account_management_users(all_no_admin_users, roles_allowed_to_update)
            user_list = all_no_admin_users

        resultObj = {'result': result, 'user_list': user_list}
        return HttpResponse(json.dumps(resultObj), content_type=CONTENT_TYPE_JSON)


def check_super_token(token, license_info, used_count_should_be=0):
    result = 0
    USERD_COUNT_INDEX = 8  # hide the used_count in this postion
    used_count = 0

    if not token or len(token) < USERD_COUNT_INDEX:
        return 0

    token_conf = WebconsoleConf().get_token()
    if len(token_conf) > USERD_COUNT_INDEX + 1:
        used_count = int(token_conf[USERD_COUNT_INDEX])
        token_conf = token_conf[:USERD_COUNT_INDEX] + token_conf[USERD_COUNT_INDEX + 1:]
        if token_conf != token:
            used_count = 0

    if used_count_should_be != used_count:
        return 0

    if token != token_conf or used_count == 1:
        HALF_HOUR = 1800
        try:
            dec_token = rsa_sign.asp_decrypt(token)
            public_key = license_info.load_public_key()
            token_data = rsa_sign.verify_license(public_key, dec_token)
            lic_id = token_data['license_id']
            timestamp = int(token_data['timestamp'])
            now = int(get_UTC_time_now())
            if now > timestamp:
                diff = now - timestamp
            else:
                diff = timestamp - now

            license_id = license_info.get_dict().get('id')
            if (license_id == lic_id) and (diff <= HALF_HOUR):
                result = 1
                WebconsoleConf().set_token(
                        '{0}{1}{2}'.format(token[:USERD_COUNT_INDEX], used_count + 1, token[USERD_COUNT_INDEX:]))
        except Exception as e:
            logger.error('Exception occurs while validating super token: {0}.'.format(e))
    return result


def password_reset_token(request, template='registration/password_reset_token.html', current_app=None):
    conf = ConfDb()
    license_info = conf.get_license_info()
    license_id = license_info.get_dict().get('id')

    if request.method == 'GET':

        current_site = get_current_site(request)
        is_build_debug = get_current_is_debug()
        is_prod_pkg = get_current_is_prod_pkg()
        brand = get_brand_str()
        context = {
            'license_id': license_id,
            'layout': get_layout(True),
            'build_commit_no': get_current_git_commit_no(),
            'site': current_site,
            'site_name': current_site.name,
            'is_build_debug': is_build_debug,
            'is_prod_pkg': is_prod_pkg,
            'brand': brand,
        }
        return TemplateResponse(request, template=template, context=context, current_app=current_app)

    elif request.method == 'POST':
        token = json.loads(request.body).get('token')
        result = check_super_token(token, license_info)
        resp_data = {'result': result}
        return HttpResponse(json.dumps(resp_data), content_type=CONTENT_TYPE_JSON)


def datetime_To_timestamp(dt):
    import calendar
    return calendar.timegm(dt.timetuple())


def get_UTC_time_now():
    from datetime import *
    dtime = datetime.utcnow()
    timestamp = datetime_To_timestamp(dtime)
    return int(timestamp)


def password_reset_password(request, template='registration/password_reset_password.html', current_app=None):
    current_site = get_current_site(request)
    is_build_debug = get_current_is_debug()
    is_prod_pkg = get_current_is_prod_pkg()
    brand = get_brand_str()

    if request.method == 'GET':
        token_error = ''
        token = str(request.GET.get('token', ''))
        if not token:
            token_error = ugettext_lazy("Your token is invalid.")
        context = {
            'site': current_site,
            'site_name': current_site.name,
            'is_build_debug': is_build_debug,
            'is_prod_pkg': is_prod_pkg,
            'brand': brand,
            'token': token,
            'token_error': token_error,
            'layout': get_layout(True),
            'build_commit_no': get_current_git_commit_no()
        }
        return TemplateResponse(request, template=template, context=context, current_app=current_app)

    elif request.method == 'POST':
        token = str(request.POST.get('token'))
        conf = ConfDb()
        license_info = conf.get_license_info()
        result = check_super_token(token, license_info, 1)
        context = {
            'token_error': '',
            'site': current_site,
            'site_name': current_site.name,
            'is_build_debug': is_build_debug,
            'is_prod_pkg': is_prod_pkg,
            'brand': brand,
        }

        if result == 1:
            username = request.POST.get('username')
            password = request.POST.get('new_password1')
            active_users = get_user_model()._default_manager.filter(
                    username__iexact=username, is_active=True)
            err = old_password_check(username, password)
            if err:
                context['token_error'] = err
                return TemplateResponse(request, template=template, context=context, current_app=current_app)

            for user in active_users:
                if WebconsoleConf().get_user_auth_mode(user) == 'remote':
                    context['token_error'] = _("Modifying remote account is not allowed.")
                    return TemplateResponse(request, template=template, context=context, current_app=current_app)

                if user.has_usable_password():
                    user.set_password(password)
                    user.save()
                    WebconsoleConf().set_user_password(username, make_password(password))
                    kick_user_session(request, flush=True)
                    return HttpResponseRedirect(settings.LOGIN_URL)

            token_error = ugettext_lazy("Username doesn't exist.")
        else:
            token_error = ugettext_lazy("Your token is invalid.")

        context['token_error'] = token_error
        return TemplateResponse(request, template=template, context=context, current_app=current_app)


def dev_mode_countdown():
    value = WebconsoleConf().get_value('developer_mode/enable')
    if value is None or value == -1:
        return 0

    now = int(time.time())
    if value <= now < value + 7200:
        return value + 7200 - now
    else:
        return 0


def is_dev_mode_enable():
    value = WebconsoleConf().get_value('developer_mode/enable')
    if value is None:
        return False

    if value == -1:
        return True

    now = int(time.time())
    return value <= now < value + 7200


def disable_dev_mode():
    WebconsoleConf().set_value('developer_mode/enable', None)


def enable_dev_mode():
    wconf = WebconsoleConf()
    if wconf.get_value('_private/is_debug', is_abs=True):
        wconf.set_value('developer_mode/enable', -1)
    else:
        wconf.set_value('developer_mode/enable', int(time.time()))

@login_required
@adv_operation_code_required
@check_permission('Developer_Mode', 'write')
def enable_developer_mode(request):
    enable_dev_mode()
    return JsonResponse(dict(status='success'))

@login_required
@check_permission('Developer_Mode', 'write')
def disable_developer_mode(request):
    disable_dev_mode()
    return JsonResponse(dict(status='success'))

def get_archive_log_file_info():
    if not os.path.exists(zip_path):
        return []

    try:
        file_info = []
        for parent, _, filenames in os.walk(zip_path):
            for filename in filenames:
                path = os.path.join(parent, filename)
                if os.path.isfile(path) and path.endswith('.zip'):
                    info = {}
                    info['createTime'] = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(os.path.getmtime(path)))

                    file_size = os.path.getsize(path)
                    file_format_size = formatSize(file_size)
                    info['size'] = file_format_size
                    info['name'] = filename
                    file_info.append(info)

        file_info.sort(key=lambda data: data['createTime'], reverse=True)

        return file_info
    except Exception as e:
        logging.info('Get archive log file failed: {0}'.format(str(e)))
        return []


def get_raw_log_file_info():
    if not os.path.exists(raw_log_path):
        return []

    try:
        file_info = []
        for parent, _, filenames in os.walk(raw_log_path):
            if not parent.endswith('archived/'):
                continue
            for filename in filenames:
                if filename.endswith('.gz') or filename.endswith('.log') or filename.endswith('.1'):
                    info = {}
                    path = os.path.join(parent, filename)
                    file_size = os.path.getsize(path)
                    file_format_size = formatSize(file_size)
                    info['createTime'] = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(os.path.getmtime(path)))
                    info['size'] = file_format_size
                    info['name'] = filename
                    file_info.append(info)

        file_info.sort(key=lambda data: data['createTime'], reverse=True)

        return file_info
    except Exception as e:
        logging.info('Get raw log file failed: {0}'.format(str(e)))
        return []


def generate_archive_log_file():
    if not os.path.exists(zip_path):
        os.mkdir(zip_path)

    file_info = get_archive_log_file_info()
    BaseConf().exc_asp_conf_command(values=['collect_log'], timeout=300)
    new_file_info = get_archive_log_file_info()

    return new_file_info, new_file_info != file_info


def formatSize(bytes):
    try:
        bytes = float(bytes)
        kb = bytes / 1024
    except:
        return ''

    if kb >= 1024:
        M = kb / 1024
        if M >= 1024:
            G = M / 1024
            return "%dG" % (G)
        else:
            return "%dM" % (M)
    elif kb <= 1:
        return "1kb"
    else:
        return "%dkb" % (kb)


@login_required
@check_permission('System_Log', 'read')
def generate_log(request):
    if request.method == "GET":
        info, ret = generate_archive_log_file()

        if not ret:
            ret = {'result': 'failed', 'info': info}
            operation_log(request, ugettext_noop('Log'), ugettext_noop('Generate'), '1', {
                'msg': ugettext_noop('System log generate failed'),
                'info': info[0]}, user=request.POST.get('username'))
        else:
            ret = {'result': 'ok', 'info': info}
            operation_log(request, ugettext_noop('Log'), ugettext_noop('Generate'), '0', {
                'msg': ugettext_noop('System Log has been Generated'),
                'info': "size:{}, createTime:{}, filename:{}".format(info[0]["size"],info[0]["createTime"],info[0]["name"])}, user=request.POST.get('username'))

        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Archived_Log', 'read')
def show_archive_log_info(request):
    if request.method == "GET":
        ret = get_archive_log_file_info()

        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def get_support_code():
    try:
        if os.path.exists('/home/<USER>/etc/.support_code_'):
            os.system('sudo /usr/sbin/reset_support_code')
        with open('/home/<USER>/etc/.support_code') as f:
            return f.read().replace('\n','')
        return None
    except Exception as e:
        return None


@login_required
@check_permission('Developer_Mode', 'read')
def developer_mode(request):
    if not is_dev_mode_enable():
        return HttpResponse(status=403)

    conf = NginxConf()
    if request.method == "POST":
        send_reputation_to_dm(request.POST.get('reputation'))

    txsafe_debug_enabled, txsafe_debug_countdown = conf.get_txsafe_debug_enabled()
    proxy_node_list = []

    cluster_nodes = service_mgr_rest_api.get_cluster_node_list()
    for _node_id, val in cluster_nodes.items():
        if _node_id.startswith('_'):
            continue
        deleted = val.get('_deleted')
        if deleted:
            continue
        ip = val.get('_admin_ip')
        proxy_node_list.append(ip)

    dm_ip, _ = ConfDb().get_dataminer_server()

    ignore_bot = ConfDb().get_value('nginx/ignore_bot')
    if ignore_bot is None:
        ignore_bot = []

    ignore_bot = ConfDb().get_value('nginx/ignore_bot')
    if ignore_bot is None:
        ignore_bot = []

    valid_third_part_names = get_valid_third_part_filenames()

    statistics_secret_enabled = ConfDb().get_value('phoenix/enable_license_secret', 0)

    return base_render_to_response(request, 'v2/developer_mode.html',
                                   {'txsafe_debug_enabled': txsafe_debug_enabled,
                                    'txsafe_debug_countdown': txsafe_debug_countdown,
                                    'dev_mode_countdown': dev_mode_countdown(),
                                    'proxy_node_list': proxy_node_list,
                                    'dataminer_ip': dm_ip,
                                    'reputation': get_reputation_from_dm(),
                                    'support_code': get_support_code(),
                                    'enable_web_inspect': 'false' if 'webinspect' in ignore_bot else 'true',
                                    'enable_ns_focus':'false' if 'NSFocus' in ignore_bot else 'true',
                                    'enable_web_driver_ff': 'false' if 'webdriver_firefox' in ignore_bot else 'true',
                                    'enable_web_driver_ie': 'false' if 'webdriver_ie' in ignore_bot else 'true',
                                    'enable_uft_qtp': 'false' if 'uft_qtp' in ignore_bot else 'true',
                                    'enable_fingerprint_browser': 'false' if 'fingerprint_browser' in ignore_bot else 'true',
                                    'valid_third_part_names': valid_third_part_names,
                                    'statistics_secret_enabled': statistics_secret_enabled
                                    }
                                   )


@login_required
@check_permission('Developer_Mode', 'write')
def txsafe_debug(request):
    action = request.POST.get('action')
    if action == 'enable':
        enabled = 1 if int(request.POST.get('enabled')) else 0
        try:
            NginxConf().set_txsafe_debug_enabled(enabled)
        except Exception as e:
            error = e.message

    return HttpResponse()


def send_reputation_to_dm(reputation):
    dm_ip, dm_port = ConfDb().get_dataminer_server()
    if not dm_ip:
        return

    try:
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=3)
        conn.connect()
        conn.request('PUT', '/v1/reputations/', reputation)
        response = conn.getresponse()
        return response.read()
    except Exception as e:
        logging.exception('Cannot get dm server.')


def get_dataminer_location_top(time_start):
    dm_ip, dm_port = ConfDb().get_dataminer_server()

    if not dm_ip:
        return

    try:
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        get_url = '/v1/dataminerdb/?time_start={0}'.format(time_start)
        conn.request('GET', get_url)
        response = conn.getresponse()
        data = json.loads(response.read())
        ret_data = []
        for i in data:
            if i['country'] != 'China':
                tmp = {'doc_count': i['count'], 'key': i['country']}
                ret_data.append(tmp)
            else:
                tmp = {'doc_count': i['count'], 'key': i['province']}
                ret_data.append(tmp)

        return ret_data

    except Exception as e:
        logging.error('get location data error {}'.format(e))
        return


def get_reputation_from_dm():
    RULE_BY_FINGER_PRINT = 1
    RULE_BY_IP = 2
    RULE_BY_USER_ID = 3
    dm_ip, dm_port = ConfDb().get_dataminer_server()

    if not dm_ip:
        return

    try:
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        conn.request('GET', '/v1/reputations/')
        response = conn.getresponse()
        reptu = json.loads(response.read())
        if type(reptu) != dict:
            reptu = json.loads(reptu)
        all_fp = reptu.get('fp')
        all_ip = reptu.get('ip')
        all_user_id = reptu.get('user_id')

        content = []
        for fp, v in all_fp.items():
            content.append('{},{},{}'.format(RULE_BY_FINGER_PRINT, fp, v.get('reput')))
        for ip, v in all_ip.items():
            content.append('{},{},{}'.format(RULE_BY_IP, ip, v.get('reput')))
        for user_id, v in all_user_id.items():
            content.append('{},{},{}'.format(RULE_BY_USER_ID, user_id, v.get('reput')))

        return '\n'.join(content)

    except Exception as e:
        return ''


def get_reputation_data_from_dmdb(db_name, index, get_count, get_filter):
    dm_ip, dm_port = ConfDb().get_dataminer_server()

    if not dm_ip:
        return

    try:
        keyword = get_filter['keyword']
        attack_type = get_filter['attack_type']
        params = urllib.urlencode({'db_name': db_name, 'index': index, 'get_count': get_count, 'filter': keyword,
                                   'attack_type': attack_type})
        headers = {"Content-type": "application/x-www-form-urlencoded", "Accept": "text/plain"}
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        conn.request('POST', '/v1/dataminerdb/', params, headers)
        response = conn.getresponse()
        ip_repu = json.loads(response.read())

        return ip_repu

    except Exception as e:
        logging.error('---------erro {}'.format(e))
        return


def get_attack_event_from_dmdb(index, get_count, attack_type, attack_level, content_filter, start_time):
    dm_ip, dm_port = ConfDb().get_dataminer_server()

    if not dm_ip:
        return

    try:
        params = urllib.urlencode({'db_name': 'attack_event',
                                   'index': index,
                                   'get_count': get_count,
                                   'attack_type': attack_type,
                                   'attack_level': attack_level,
                                   'content_filter': content_filter,
                                   'start_time': start_time})
        headers = {"Content-type": "application/x-www-form-urlencoded", "Accept": "text/plain"}

        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        conn.request('POST', '/v1/dataminerdb/', params, headers)
        response = conn.getresponse()
        ip_repu = json.loads(response.read())
        if type(ip_repu) != dict:
            ip_repu = json.loads(ip_repu)

        return ip_repu

    except Exception as e:
        return


def get_data_miner_summary():
    # not store in db, get data dynamically
    dm_ip, dm_port = ConfDb().get_dataminer_server()

    logging.info('get_data_miner_summary:ip={} . port= {}'.format(dm_ip, dm_port))
    try:
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        conn.request('GET', '/v1/summary/')
        response = conn.getresponse()
        summary_data = json.loads(response.read())
        if type(summary_data) != dict:
            summary_data = json.loads(summary_data)

        return summary_data

    except Exception as e:
        logging.error('get_data_miner_summary Exception:{}'.format(e))
        return


def handle_treatlib_in_data_miner(action, upload_file=''):
    dm_ip, dm_port = ConfDb().get_dataminer_server()
    try:
        params = urllib.urlencode({'action': action, 'file': upload_file})
        headers = {"Content-type": "application/x-www-form-urlencoded", "Accept": "text/plain"}
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        conn.request('POST', '/v1/treatlibhandle/', params, headers)
        response = conn.getresponse()
        return pickle.loads(response.read())
    except Exception as e:
        logging.error('handle treatlib error {}'.format(e))

def import_self_health_check_config(confDb):
    all_upstreams = confDb.get_all('nginx/upstreams/', {})
    for k, v in all_upstreams.items():
        if 'self_health_check_path' not in v:
            confDb.set_value('nginx/upstreams/{0}/enable_self_health_check'.format(k), False)
            confDb.set_value('nginx/upstreams/{0}/self_health_check_path'.format(k), '/healthcheck')

def import_geolib_config(confDb):
    geolib_conf = confDb.get_value("nginx/geolib", {})
    upgrade_interval_mins = int(geolib_conf.get('upgrade_interval_mins', '60'))
    if upgrade_interval_mins < 30:
        geolib_conf['upgrade_interval_mins'] = '30'
        confDb.set_value("nginx/geolib", geolib_conf)

def import_waf_strategy(confDb):
    strategy_changed = {'action': 'import_system_config',
                         'random': datetime.datetime.now().microsecond}
    confDb.set_value('nginx/waf/strategy_changed',strategy_changed)


def delete_waf_strategy_changed(conf):
    if conf['nginx'].get('waf',{}).get('strategy_changed'):
        del conf['nginx']['waf']['strategy_changed']

def delete_waf_resource_file_config(conf):
    if conf['nginx'].get('waf',{}).get('resource_file'):
        del conf['nginx']['waf']['resource_file']

def get_file_size(file_path):
    """获取文件大小和时间"""
    size = os.path.getsize(file_path)
    mtime = os.path.getmtime(file_path)
    return size,mtime

def traverse_files(folder_path,type):
    """遍历文件夹获取文件名和大小"""
    files = []
    for root, dirs, filenames in os.walk(folder_path):
        for filename in filenames:
            file_path = os.path.join(root, filename)
            file_size,file_time = get_file_size(file_path)
            files.append({"file_name": filename, "size": file_size,"is_built_in":False,"time":file_time,"type":type})
    return files

def import_waf_resource_file_config(confDb):
    folder_path="/etc/asp/release/nginx/lua/waf_extend/xml_file/"
    wsdl_path=os.path.abspath(folder_path+"wsdl")
    xsd_path=os.path.abspath(folder_path+"xsd")
    wsdl_file_list=[]
    xsd_file_list=[]
    if os.path.exists(wsdl_path):
        wsdl_file_list=traverse_files(wsdl_path,"wsdl")
    if os.path.exists(xsd_path):
        xsd_file_list=traverse_files(xsd_path,"xsd")

    wsdl_file_list = json.dumps(wsdl_file_list)
    xsd_file_list=json.dumps(xsd_file_list)
    confDb.set_value('nginx/waf/resource_file/wsdl',wsdl_file_list)
    confDb.set_value('nginx/waf/resource_file/xsd',xsd_file_list)

def import_lub_ubb_config(conf, lua={}):
    from asp_utils.zk_client import ZkClient
    base_conf = BaseConf()
    path_list = base_conf.get_global_zk_node_list(ConfDb.PATH_LUA_UBB_SAVE_NODE)
    ret_list = base_conf.global_zk_values('/lua/resource_file/list')
    file_list = ret_list.get('values', None)
    ret_js = base_conf.global_zk_values('/lua/resource_file/js')
    file_js = ret_js.get('values', None)
    ret_html = base_conf.global_zk_values('/lua/resource_file/html')
    file_html = ret_html.get('values', None)

    for path in path_list:
        base_conf.global_zk_values(values=path, action='delete')

    if lua or file_list or file_js or file_html:
        if file_list:
            lua['/lua/resource_file/list'] = file_list
        if file_js:
            lua['/lua/resource_file/js'] = file_js
        if file_html:
            lua['/lua/resource_file/html'] = file_html

        base_conf.global_zk_values(values=lua, action='set')

    rule_trigger_paths = 'nginx/lua_env/ubb_import_system_config'

    now = str(int(time.time()))
    timestamp = "{}_{}".format(rule_trigger_paths, now)

    if isinstance(conf, ConfDb):
        conf.set_value(rule_trigger_paths, timestamp)
    elif isinstance(conf, NginxConf):
        result, exception, err_massage = conf.set_lua_ubb_rule_timestamp(rule_trigger_paths, timestamp)
    else:
        logging.error('handle import_lub_ubb_config error with the type {}'.format(type(conf).__name__))
        

def import_global_ip_black_white_list(confDb):
    ip_black_list = confDb.get_ip_black_list()
    if ip_black_list:
        ip_black_list = delete_expired_ip(ip_black_list)
        confDb.set_values({'ip_black/count': len(ip_black_list), 'ip_black/value': ip_black_list})

    ip_white_list = confDb.get_ip_white_list()
    if ip_white_list:
        ip_white_list = delete_expired_ip(ip_white_list)
        confDb.set_values({'ip_white/count': len(ip_white_list), 'ip_white/value': ip_white_list})


def export_lub_ubb_config():
    lua_ubb = {}
    path_list = BaseConf.get_global_zk_node_list(ConfDb.PATH_LUA_UBB_SAVE_NODE)
    for path in path_list:
        if re.search('/lua/resource_file/.*', path):
            continue
        ret = BaseConf().global_zk_values(values=path, action='get')
        value = ret.get('values', None)
        if value:
            lua_ubb[path] = value

    return lua_ubb


def export_api_bot_defender_config():
    from abd_utils.utils.func_system_config import get_export_api_info
    api_info = get_export_api_info()
    return api_info


def get_dm_slice_end_time():
    dm_ip, dm_port = ConfDb().get_dataminer_server()
    try:
        conn = httplib.HTTPConnection(dm_ip, dm_port, timeout=30)
        conn.connect()
        conn.request('GET', '/v1/slicetime/')
        response = conn.getresponse()
        end_time = json.loads(response.read())
        return end_time
    except Exception as e:
        logging.error('get dm slice end time Exception:{}'.format(e))
        return


ES_SIZE_PER_INDEX = 4
LOG_SIZE_PER_INDEX = 1


def check_for_port_and_protocol_conflicts(import_conf):
    upstreams = import_conf.get_all('nginx/upstreams/').values()

    https_ports = set([i.get('ListenPort') for i in upstreams if i.get('IsHttps')])
    http_ports = set([i.get('http2https_org_port') for i in upstreams if (i.get('IsHttps') and i.get('enable_http2https'))])
    http_ports |= set([i.get('ListenPort') for i in upstreams if not i.get('IsHttps')])

    current_conf = ConfDb()

    # external port should not conflict with admin port
    http_ports |= {'20146'}
    https_ports |= {import_conf.get_value('nginx/web_console/port')}

    https_ports -= {None}
    http_ports -= {None}

    return len(https_ports & http_ports) > 0


def valid_kafka_topics(topics):
    for topic in topics:
        if re.search(r"[^a-zA-Z_\-\d\.]", topic):
            return False

    return True


def check_import_reserved_ports(import_conf, current_conf):
    error = None
    new_listen_port = import_conf.get_nginx_listen_port()
    current_listen_port = current_conf.get_nginx_listen_port()
    check_listen_port = new_listen_port - current_listen_port

    for port in check_listen_port:
        if is_port_already_in_use(port):
            error = _('Port {0} is used already. Please change the port number or try again later.').format(port)
            return error

    try:
        check_user_defined_reserved_ports(import_conf.get_value("os/network/user_defined_reserved_ports", ''))
    except ValueError as e:
        error = e.message

    return error


@login_required
@adv_operation_code_required
@check_permission('Import_System_Settings', 'write')
def import_system_config(request):
    if not get_cluster_node_list():
        return JsonResponse({
            'error': _('The system configuration cannot be imported because the cluster status is abnormal. '
                       'Please try again after the cluster is restored.'),
            'importSuccess': False
        })

    import_success = False
    error = None
    config_form = ConfigFileForm(request.POST, request.FILES)
    config = ''
    kafka_topics = []
    if config_form.is_valid():
        config = request.FILES['configfile'].read()

    error = _('Please upload a valid system file.')
    if config:
        try:
            j = json.loads(config, encoding='utf-8')
            lua = j.get('lua_ubb', {})
            if lua:
                lua = lua.get('_value', {})

            api = j.get('api_info', {})
            if api:
                api = api.get('_value', {})

            sphinx_version = j.get('sphinx_version','')
            if sphinx_version:
                sphinx_version = sphinx_version.get('_value', '')

            j = j['system_settings']['_value']
            encoded_priv_pwd = j.get('os', {}).get('network', {}).get('snmp', {}).get('encoded_priv_pwd', None)
            encoded_auth_pwd = j.get('os', {}).get('network', {}).get('snmp', {}).get('encoded_auth_pwd', None)
            if encoded_priv_pwd is not None:
                decoded_priv_pwd = aes_decrypt(get_asp_aes_key(),encoded_priv_pwd['_value'])
                j['os']['network']['snmp'].update({'priv_pwd' : {'_value': decoded_priv_pwd}})
                j['os']['network']['snmp'].pop('encoded_priv_pwd', None)
            if encoded_auth_pwd is not None:
                decoded_auth_pwd = aes_decrypt(get_asp_aes_key(), encoded_auth_pwd['_value'])
                j['os']['network']['snmp'].update({'auth_pwd' : {'_value': decoded_auth_pwd}})
                j['os']['network']['snmp'].pop('encoded_auth_pwd', None)

            parsedlog_export =j.get('logserver', {}).get('parsedlog_export', {})
            if parsedlog_export and parsedlog_export.get('address', {}) :
                parsedlog_export_address = parsedlog_export['address'].get('_value', {})
                if parsedlog_export_address and type(parsedlog_export_address) == list:
                    kafka_topics = [topic['topic'] for topic in parsedlog_export_address if topic['proto'] == 'kafka']

            repu_service = j.get('repu_service', {})
            if repu_service and repu_service.get('sendout', {}) and repu_service['sendout'].get('topic', {}):
                repu_service_topic = repu_service['sendout']['topic'].get('_value', {})
                if repu_service_topic:
                    kafka_topics.append(repu_service_topic)

            if isinstance(j, list) or isinstance(j, dict):
                error = None
                # Check max proxies
                import_conf = ConfDb(j)
                upstreams = import_conf.get_all('nginx/upstreams/').values()
                max_proxies = NginxConf().get_license_info().max_proxies
                # Check max mpp number
                import_mpps = 0
                for path in ["nginx/wechat_app/app_list", "nginx/alipay_mpp/app_list", "nginx/mpaas_mpp/app_list"]:
                    import_mpps += len(import_conf.get_value(path, {}).keys())
                max_mpps = NginxConf().get_license_info().get_max_mpp_count()
                # Check mobile app number
                import_apps = len(import_conf.get_mobile_certification_pkgname())
                max_apps_str = NginxConf().get_license_info().get_mobile_max_app()
                max_apps = int(max_apps_str) if max_apps_str else 0

                current_conf = ConfDb()
                current_conf_deploy_mode = current_conf.get_deploy_mode()
                import_conf_deploy_mode = import_conf.get_deploy_mode()
                is_deploy_mode_same = current_conf_deploy_mode == import_conf_deploy_mode

                current_conf_build_info = get_current_build_info()

                current_conf_build_info = current_conf_build_info.split('_')
                import_conf_build_info = sphinx_version.split('_')

                is_layout_same = current_conf_build_info[0] == import_conf_build_info[0]
                if current_conf_build_info[0]=='NGWAF' and import_conf_build_info[0]=='WAF':
                    is_layout_same = True

                error = check_import_reserved_ports(import_conf, current_conf)

                if max_proxies and len(upstreams) > max_proxies:
                    error = _('Import failed. The number of protected websites in the settings file has exceeded the licensed limit.')
                elif import_mpps > max_mpps:
                    error = _('Import failed. The number of mini-programs in the settings file has exceeded the licensed limit.')
                elif import_apps > max_apps:
                    error = _('Import failed. The number of mobile apps in the settings file has exceeded the licensed limit.')
                elif not is_layout_same: #import must same layout
                    error = _('Failed to import system settings, the layout in the imported system configuration does not match the current layout')
                elif not is_deploy_mode_same:   # import must same mode
                    error = _('Failed to import system settings, the deployment mode in the imported system configuration does not match the current deployment mode')
                elif check_for_port_and_protocol_conflicts(import_conf):
                    error = _('Sites that use different protocol (http and https) are not allowed to listen on the same port.')
                elif filter(lambda j: is_Local_Loopback(j[0]), reduce(lambda a,b:a+b, (i['UpstreamList'] + i.get('UpstreamList_IPv4',[]) + i.get('UpstreamList_IPv6',[]) for i in import_conf.get_all('nginx/upstreams').values()), [])):
                    error = _('Upstream is not allowed to be set to loopback address.')
                elif not valid_kafka_topics(kafka_topics):
                    error = _("Kafka Topic only supports letters, numbers and characters '-', '_', '.'")
                elif error is None:
                    import_self_health_check_config(import_conf)

                    import_geolib_config(import_conf)
                    import_lub_ubb_config(import_conf, lua)
                    import_global_ip_black_white_list(import_conf)

                    import_waf_strategy(import_conf)
                    import_waf_resource_file_config(import_conf)

                    license_info = current_conf.get_license_info()
                    if license_info and license_info.is_mpp_enabled():
                        WechatConf().reconfig_mpp_info(import_conf)
                        AlipayConf().reconfig_mpp_info(import_conf)
                        MPaasConf().reconfig_mpp_info(import_conf)

                    import_api_success = True
                    try:
                        from abd_utils.utils.func_system_config import import_api_bot_defender_config, import_compat_api_nginx_config, import_compat_api_risk_list_v2
                        import_api_bot_defender_config(api)
                        # merge old version api_nginx conf to new path
                        import_version = import_conf_build_info[1]
                        if import_version < '23.09':
                            import_compat_api_nginx_config(import_conf)
                        else:
                            import_compat_api_risk_list_v2(j)

                    except Exception as e:
                        operation_log(request, ugettext_noop('System'), ugettext_noop('Import'), '1', {'msg': ugettext_noop('Failed to import system settings')})
                        error = _('API conf error. Please upload a valid system file.')
                        logging.exception('Exception in import_system_config:{}'.format(e))
                        import_api_success = False

                    if import_api_success:
                        # Here we update site_acl before restore_system_conf
                        # because after restore_system_conf, update site_acl will fail as it take times to sync system config to zk
                        upstream_key_list = import_conf.get_all('nginx/upstreams/').keys()
                        site_acl = get_sites_4_user(request.user.username)
                        site_key_set = {site['key'] for site in site_acl}
                        upstreams_set = {upstream for upstream in upstream_key_list}
                        for site_key in list(site_key_set.difference(upstreams_set)):
                            clean_site_config_2_blank(site_key)
                        code, output, cmd_line = BaseConf().restore_system_conf(j)
                        if code == 0:

                            update_all_pg_settings_table(import_conf=import_conf)
                            
                            if current_conf.get_value('_private/force_sync_conf', False):
                                # 执行一个同步的空任务， 保证导入的配置产生的任务执行完。
                                service_mgr_rest_api.service_mgr_set_asp_config({}, '127.0.0.1', 'admin', sync=True)
                            
                            operation_log(request, ugettext_noop('System'), ugettext_noop('Import'), '0', {'msg': ugettext_noop('System settings has been imported')})
                            import_success = True
                            logger.info(
                                '* Restore system config successfully.')
                        else:
                            # rollback site_acl when restore_system_conf failed
                            for site in site_acl:
                                assign_site_4_user(site['key'], site['user'])
                            operation_log(request, ugettext_noop('System'), ugettext_noop('Import'), '1', {'msg': ugettext_noop('Failed to import system settings')})
                            error = 'Failed to restore system config, error code: {0}, cmd: {1}, messages: {2}'.format(
                                code,
                                cmd_line,
                                output)
                            logger.error(error)

            clear_global_lru_cache()

        except Exception as e:
            operation_log(request, ugettext_noop('System'), ugettext_noop('Import'), '1', {'msg': ugettext_noop('Failed to import system settings')})
            error = _('Please upload a valid system file.')
            logging.exception('Exception in import_system_config:{}'.format(e))

        return HttpResponse(json.dumps({'error':error, 'importSuccess':import_success}), content_type=CONTENT_TYPE_PLAIN)
    else:
        return HttpResponse(json.dumps({'error': _('Uploaded empty file.'), 'importSuccess': False}), content_type=CONTENT_TYPE_PLAIN)

@login_required
@csrf_exempt
def set_factory_reset(request):
    base_conf = BaseConf()
    node_id = base_conf.get_value('_private/node_id')

    is_master = 0
    cluster_state = get_cluster_node_state_list()
    for node in cluster_state:
        if 'master_server' in node['role']:
            if node_id == node['id']:
                is_master = 1

    return {
        'ClusterNodeList': cluster_state,
        'is_master': is_master,
    }


@login_required
@check_permission('Network_Configuration', 'read')
def get_system_time(request):
    ISFORMAT = "%Y-%m-%d %H:%M:%S %Z"
    date = time.strftime(ISFORMAT)
    zone = time.timezone
    if zone > 0:
        zone = ' UTC-' + str(zone / 3600)
    else:
        zone = ' UTC+' + str(abs(zone) / 3600)

    result = {'date': date + zone}

    return HttpResponse(json.dumps(result, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def return_query_task_callback(request, result):
    result = json.dumps(result, encoding='utf-8', indent=2)
    callback = request.GET.get('callback', 'console.log')
    key = request.GET.get('key', '0')
    host = request.GET.get('host', '')
    text = '''(function(){{\nvar data = {0};\n{1}(data, {2}, "{3}");\n}})();'''.format(result, callback, key, host)

    return HttpResponse(text, content_type=CONTENT_TYPE_JS)


def ajax_query_task_stat_callback(request):
    task_id = request.GET.get('task_id')
    if not task_id:
        return HttpResponse(json.dumps({'result': RESULT.INVALID_PARAMS, 'message': 'NO task ID'}, encoding='utf-8'),
                            content_type=CONTENT_TYPE_JSON)

    ret = service_mgr_rest_api.query_task_status(task_id)
    result = {'result': ret.get('result')}
    if 'stat' in ret:
        result['stat'] = {}
        stat = ret['stat']
        for k in ('status', 'brief_msg', 'result'):
            if k in stat:
                result['stat'][k] = stat[k]

    return return_query_task_callback(request, result)


def smoke_test_task(request):
    params = json.loads(request.body)
    print params
    ret = service_mgr_rest_api.smoke_test_task(params)

    return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Account_Default_Configurations', 'write')
def captcha_enable(request):
    data = {'result': 'Error'}
    wc_conf = WebconsoleConf()
    old_enable = wc_conf.is_captcha_enabled(get_current_is_prod_pkg())
    if request.method == "POST":
        enabled = request.POST.get('enable') == '1'
        WebconsoleConf().set_captcha_enabled(enabled)
        data = {'result': 'OK'}
        if old_enable != enabled:
            op_action = ugettext_noop('Enable') if enabled else ugettext_noop('Disable')
            op_msg = ugettext_noop('Enable login CAPTCHA.') if enabled else ugettext_noop('Disable login CAPTCHA.')
            operation_log(request, ugettext_noop('Access'), op_action, '0', {
                'msg': op_msg,
            })

    return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Account_Default_Configurations', 'write')
def qps_enable(request):
    data = {'result': 'Error'}
    wc_conf = WebconsoleConf()
    old_enable = wc_conf.is_qps_enabled()
    if request.method == "POST":
        reqbody = json.loads(request.body)
        enabled = reqbody.get('enable', False)
        WebconsoleConf().set_qps_enabled(enabled)
        data = {'result': 'OK'}
        if old_enable != enabled:
            op_action = ugettext_noop('Enable') if enabled else ugettext_noop('Disable')
            op_msg = ugettext_noop('Enable QPS_Management.') if enabled else ugettext_noop('Disable QPS_Management.')
            operation_log(request, ugettext_noop('Access'), op_action, '0', {
                'msg': op_msg,
            })

    return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Enhance_Cluster_Security', 'write')
def enhance_cluster_security(request):
    data = {'result': 'Error'}
    op_code = '1'
    if request.method == "POST":
        wc_conf = WebconsoleConf()
        old_enabled = wc_conf.get_value('cluster/enhance_cluster_security', False, is_abs=True)
        old_ip_list = wc_conf.get_value('cluster/ecs_ip_white_list', '', is_abs=True)
        enabled = request.POST.get('enable') == '1'
        ip_list = request.POST.get('ip_list', '')
        ip_array = list()
        for ip_info in ip_list.split(','):
            if '/' in ip_info:
                ip_array.append(compress_IP(ip_info.split('/')[0]) + '/' + ip_info.split('/')[1])
            else:
                ip_array.append(compress_IP(ip_info))
        ip_list = ','.join(ip_array)
        if wc_conf.set_enhance_cluster_security(enabled, ip_list):
            data = {'result': 'OK'}
            op_code = '0'

        if old_enabled != enabled:
            op_action = ugettext_noop('Enable') if enabled else ugettext_noop('Disable')
            op_msg = ugettext_noop('Enable Enhance Cluster Security.') if enabled \
                else ugettext_noop('Disable Enhance Cluster Security.')
            operation_log(request, ugettext_noop('System'), op_action, op_code, {
                'msg': op_msg,
            })
        if set(filter(lambda d: d, ip_list.split(','))) ^ set(filter(lambda d: d, old_ip_list.split(','))):
            operation_log(request, ugettext_noop('System'), ugettext_noop('Modify'), op_code, {
                'msg': ugettext_noop('Modify Cluster Security Enhancement - Cluster IP whitelist.')
            })

    return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Hide_Hardware_Info', 'write')
def hide_hardware_info(request):
    data = {'result': 'Error'}
    op_code = '1'
    if request.method == "POST":
        wc_conf = WebconsoleConf()
        old_enabled = wc_conf.get_value('cluster/hide_hardware_info', False, is_abs=True)
        data = json.loads(request.body)
        enabled = data.get('enable', False)

        if old_enabled == enabled:
            data = {'result': "Unchanged"}
        else:
            if wc_conf.set_value("cluster/hide_hardware_info", enabled, is_abs=True) == 0:
                data = {'result': 'OK'}
                op_code = '0'

            op_action = ugettext_noop('Enable') if enabled else ugettext_noop('Disable')
            op_msg = ugettext_noop('Enable Hide Hardware Info.') if enabled \
                else ugettext_noop('Disable Hide Hardware Info.')
            operation_log(request, ugettext_noop('System'), op_action, op_code, {'msg': op_msg})

    return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Factory_Reset', 'write')
def factory_reset(request):
    data = {}
    if request.method == "POST":
        web_conf = WebconsoleConf()
        password_is_ok = False
        my_ip = ConfDb().get_admin_ip()
        try:
            username = request.POST.get('username')
            password = request.POST.get('password')
            if not (username == 'admin' and web_conf.is_admin_user_disabled()):
                user = User.objects.get(username=username)
                password_is_ok = user.check_password(password)
        except:
            logger.error('Failed to get user {0} while doing factory reset. '.format(username))

        if password_is_ok:
            if has_permission(username, 'Factory_Reset', 'write'):
                non_admin_users = web_conf.get_all_no_admin_users()
                base_conf = BaseConf()
                base_conf.set_asp_conf_values({'_private/web_console/factory_reset': 'start'})
                node_id = base_conf.get_value('_private/node_id')
                try:
                    with open('/tmp/factory_reset_flag', 'w') as f:
                        f.write(str(time.time()))
                except:
                    pass

                result = service_mgr_rest_api.leave_cluster(node_id, get_client_ip(
                    request), request.user.username, is_factory_reset=True)
                data.update(result)

                waf_upload_dir = '/etc/asp/release/nginx/modsecurity/owasp-modsecurity-crs/rules/USER-UPLOAD'
                waf_upload_templete = waf_upload_dir + '/USER-TEMPLATE.conf'
                exe('rm -rf {0}'.format(waf_upload_dir))
                os.mkdir(waf_upload_dir)
                os.mknod(waf_upload_templete)

                for user in non_admin_users:
                    name = user.get("user_name")
                    try:
                        User.objects.get(username=name).delete()
                    except Exception as e:
                        pass
            else:
                data['error'] = _('You do not have this permission. Please contact your administrator.')
        else:
            data['error'] = _('Invalid username or password.')
        op_code = '1' if data.get('error') else '0'
        operation_log(request, ugettext_noop('System'), ugettext_noop('Reset'), op_code, {
            'msg': ugettext_noop('{node_ip} restore the factory configuration.'),
            'extra': {
                'node_ip': my_ip,
            }
        })
        return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)
    else:
        return base_render_to_response(request, 'v2/factory_reset.html')


def factory_reset_status(request):
    if not os.path.exists('/tmp/factory_reset_flag'):
        return HttpResponse(status=403)
    else:
        return HttpResponse(status=404)


@login_required
@check_permission('Command_Line', 'write')
def web_cmdline(request):
    if not WebconsoleConf().is_access_cmdline():
        return HttpResponseRedirect("/overview/")
    #
    # SEE /etc/release/third-parties/butterfly/bin/butterfly.server.py:381
    #
    with open(get_release_file('third-parties/butterfly/bin/token')) as f:
        token = f.read()

    timestamp = time.time()
    nonce = ''.join([random.choice(string.letters + string.digits) for i in range(12)])
    s = 'nonce={}&timestamp={}'.format(nonce, timestamp)
    calc_sign = hmac.new(token.encode('utf8'), s, hashlib.sha256).hexdigest()
    return base_render_to_response(request, 'v2/web_cmdline.html', {
        'web_shel_token': s + '&sign={}'.format(calc_sign)
    })


def get_security_level(threat_data):
    summary_count = 0
    summary_value = 0

    for k, v in threat_data.items():
        summary_count += v[0]
        summary_value += v[1]

    security_level = summary_value / len(threat_data)

    if security_level < 2:
        security_des = 'low'
    elif security_level >= 2 and security_level < 4:
        security_des = 'middle'
    else:
        security_des = 'high'

    security_summary = {'security_level': security_level, 'security_des': security_des}

    return security_summary


ip_reputation_db = 'ip_reputations'
account_reputation_db = 'account_reputations'
reputation_event_db = 'reputation_event'


def convert_threat_level(level_string):
    if level_string == 'high':
        return 3
    elif level_string == 'middle':
        return 2
    elif level_string == 'low':
        return 1
    else:
        return level_string

def check_conf_is_valid(conf):
    invalid_char = ['<', '>', '&', '#']
    for item in conf:
        for ch in item:
            for char in invalid_char:
                if char in ch:
                    return False

    return True

def get_qrcode(request):
    url = request.GET.get('url')
    if url is None:
        return base_render_to_response(request, 'v2/qrcode.html')
    img = qrcode.make(urllib.unquote(url))
    buf = cStringIO.StringIO()
    img.save(buf)
    img_stream = buf.getvalue()
    return HttpResponse(img_stream, content_type='image/png')

def gen_qrcode(request):
    if WebconsoleConf().is_wizard_finished():
        return HttpResponse(status=403)
    return get_qrcode(request)

def get_all_extract_type():
    extract_type = ['stuffing', 'signup', 'scanner', 'crawler']

    summary_data = get_data_miner_summary()
    if summary_data:
        threat_data = summary_data['threat_level']
        for key in threat_data:
            key_str = key.lower()
            if key_str not in extract_type:
                extract_type.append(key)

    return extract_type


@login_required
@expert_mode_required
@check_permission('Labs', 'read')
def labs(request):
    return base_render_to_response(request, 'v2/labs.html')


@login_required
@check_permission('Change_Node_Role', 'write')
@allow_method('post')
def modify_role(request):
    conf = WebconsoleConf()
    try:
        req = json.loads(request.body, encoding='utf-8')
        node_id = req['node_id']
        node_ip = conf.get_conf().get_all('cluster/node_list', {}).get(str(node_id)).get('admin', {}).get('ip', '')
        role = req['role']
        param = {
            'role': role
        }

        hidden_role = set([u'sailfish', u'log_archive_server', u'master_server', u'proxy'])
        old_role = set(conf.get_conf().get_all('cluster/node_list')[str(node_id)].get('role')) - hidden_role
        add_rols = []
        remove_rols = []
        for r in role.split(','):
            if r.startswith('-'): # no this role
                r = r[1:]
                if r in old_role:
                    remove_rols.append(r)
            else:   # have this role
                if r not in old_role:
                    add_rols.append(r)

        add_msg = ','.join(add_rols)
        remove_msg = ','.join(remove_rols)
        if add_rols and remove_rols:
            role_diff = ';'.join(['add: ' + ','.join(add_rols), 'remove: ' + ','.join(remove_rols)])
        elif add_rols:
            role_diff = 'add: ' + ','.join(add_rols)
        elif remove_rols:
            role_diff = 'remove: ' + ','.join(remove_rols)
        else:
            role_diff = ''

        zk_lock = ZkNodeLock(ZkClient.PATH_MODIFY_ROLE_LOCK)
        if not zk_lock.acquire(blocking=False):
            logging.debug('A node is modify role. Please wait and try again')
            return HttpResponse(json.dumps({'result': 1}), content_type='application/json')

        zk_lock.release()

        service_mgr_set_command('modify_role', param, node_id, sync=False)

        operation_log(request, ugettext_noop('Cluster'), ugettext_noop('Modify'), '0', {
            'msg': ugettext_noop('Node {node_ip} role change.'),
            'info': role_diff,
            'extra': {
                'node_id': node_id,
                'node_ip': node_ip,
            }
        })

        return HttpResponse(json.dumps({'result': 0}), content_type='application/json')
    except Exception as e:
        logging.exception('Cluster modify role error:{}'.format(e))
        operation_log(request, ugettext_noop('Cluster'), ugettext_noop('Modify'), '1', {
            'msg': ugettext_noop('Node {node_ip} role change.'),
            'extra': {
                'node_id': node_id,
                'node_ip': node_ip,
            }
        })
        return HttpResponse(json.dumps({'result': 1}), content_type='application/json')


@login_required
@expert_mode_required
@check_permission('Labs', 'write')
@allow_method('post')
def clean_ubb_ip(request):

    str_decode = urllib.unquote(str(request.body)).decode('utf-8')
    req = json.loads(str_decode, encoding='utf-8')
    clean_ip = req.get('clean_ubb_ip')
    error_msg   = ""
    result_code = 0
    if clean_ip:
        clean_ip = clean_ip.strip()

    if not clean_ip:
        error_msg = _("Please enter an IPv4 or IPv4/Mask")
        result_code = -1
    else:
        mask = None
        ret, ver, ip, masklen = netaddr_parse(clean_ip)
        if ver == 4:
            if masklen == 32:
                net_addr = ip
            else:
                mask = ipv4_len2mask(masklen)
                net_addr = ip + '/' + mask
        else:
            error_msg = _('Please enter an IPv4 or IPv4/Mask')
            result_code = -1

    if not result_code:
        if mask:
            value = {'ip':ip, 'mask': mask}
        else:
            value = {'ip':ip}
        result_code, code_stat, error_msg = service_mgr_set_command('clean_ubb_ip', value)
        if result_code == 0:
            operation_log(request, ugettext_noop('Labs'), ugettext_noop('Delete'), '0', {
                'msg': ugettext_noop("malicious IP of User Behavior Rules:"), 'spliceMsg': net_addr})
        else:
            operation_log(request, ugettext_noop('Labs'), ugettext_noop('Delete'), '1', {
                'msg': ugettext_noop("malicious IP of User Behavior Rules:"), 'spliceMsg': net_addr})

    result = json.dumps({
        'result': result_code,
        'error_msg': error_msg
    })

    return HttpResponse(result, content_type='application/json')


@login_required
def labs_manual_rules(request):
    error_code = 0
    error_msg  = ""
    rules_list = []
    err_rule_id = 0

    if request.method == "POST":
        if not has_permission(request.user.username, 'Labs', 'write'):
            return HttpResponse(status=403)
        try:
            str_decode = urllib.unquote(str(request.body)).decode('utf-8')
            req = json.loads(str_decode, encoding='utf-8')
            rules_list = req['labList']
        except Exception as e:
            return HttpResponse(json.dumps(
                {"result": 1, "error_code": ERR_CODE_ADRULES_INVALID_RULE, "error_msg": e, "error_rule_id": 0}),
                                content_type=CONTENT_TYPE_JSON)

        #check num
        if len(rules_list) > MAX_ADRULES_COUNT:
            error_code = ERR_CODE_ADRULES_MAX_LIMIT
            error_msg  = _("The number of rules is more than {0}.".format(MAX_ADRULES_COUNT))
        else:
            #check condition of rule
            for x in rules_list:
                x["enabled"] = bool(re.match(r'true', str(x["enabled"]), re.I) is not None)
                error_code, err_cause = rules_is_valid(x)
                if error_code:
                    err_rule_id = x["rule_id"]
                    error_msg = _('Invalid rule:{0}, cause:{1}.'.format(json.dumps(x, encoding='utf-8'), err_cause))
                    break

        if not error_code:
            rules_list.sort(key=lambda obj: obj.get('rule_id'))
            commit_status = service_mgr_set_asp_config({"cluster/adrules":json.dumps(rules_list, encoding='utf-8')},'IP', 'User')
            if not commit_status or  0 != commit_status[0]:
                error_code = ERR_CODE_ADRULES_TIMEOUT
                error_msg = _("Operation timeout.")

        result = error_code and 1

        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), result, {
            'msg': ugettext_noop('Set user behavior rules.'),
        })

        return HttpResponse(json.dumps({"result": result, "error_code": error_code, "error_msg": error_msg, "error_rule_id": err_rule_id}),
                     content_type=CONTENT_TYPE_JSON)

    elif request.method == "GET":
        if not has_permission(request.user.username, 'Labs', 'read'):
            return HttpResponse(status=403)
        rules_list = []
        asp_conf = ConfDb()
        rules_list_ori = asp_conf.get_value("cluster/adrules", "[]")
        try:
            rules_list_ori = json.loads(rules_list_ori, encoding='utf-8')
        except Exception as e:
            logger.info("Get rule failed:{0}".format(e))
            rules_list_ori = []

        for x in rules_list_ori:
            if not rules_is_valid(x)[0]:
                rules_list.append(x)

                if len(rules_list) >= MAX_ADRULES_COUNT:
                    break

        rules_list.sort(key=lambda obj:obj.get('rule_id'))

        return base_render_to_response(request, 'v2/labs/manual_rules.html',
                                   {
                                       'ruleList': json.dumps(rules_list, encoding='utf-8'),
                                   })
    else:
        return HttpResponse(status=403)

@login_required
@expert_mode_required
def mouse_track_collect(request):
    nginx_conf = NginxConf()
    values = {}

    if request.method == 'POST':
        if not has_permission(request.user.username, 'Labs', 'write'):
            return HttpResponse(status=403)
        request_body = json.loads(request.body)

        if not check_conf_is_valid(request_body):
            return HttpResponse(json.dumps({'saveSuccess': False, 'error_msg': _('Invalid character(s) found!')}),
                                content_type='application/json')

        enable_track_collect = request_body['enable_track_collect']
        track_collect_settings = request_body['upstream_datas']
        logging.debug('request_body ----> {0}'.format(track_collect_settings))

        op_enable_upstreams = set()
        op_close_upstreams = set()
        op_change_upstreams = set()

        upstream_conf_list = nginx_conf.get_all_upstreams_for_show()
        for upstream in upstream_conf_list:
            key = upstream['key']
            name = upstream['name']
            conf = nginx_conf.get_upstream_by_server_key(key)

            if conf.get('enable_track_collect', False) != enable_track_collect:
                if enable_track_collect:
                    op_enable_upstreams.add(name)
                else:
                    op_close_upstreams.add(name)

            conf['enable_track_collect'] = enable_track_collect

            val = track_collect_settings.get(key, [])
            old_val = conf.get('track_collect_settings') or []

            flat_new_val = {'{}-{}'.format(ip, url) for ip, url in val}
            flat_old_val = {'{}-{}'.format(ip, url) for ip, url in old_val}

            if flat_old_val ^ flat_new_val:
                op_change_upstreams.add(name)

            if not val:
                conf['track_collect_settings'] = {}
            else:
                conf['track_collect_settings'] = val

            #TODO: check ip and url is legal

            path = 'nginx/upstreams/' + key + '/'
            for k, v in conf.items():
                #logging.debug('saved------> %s: %s', path + k, v)
                values.update({path + k: v})

        code, output, cmd_line = nginx_conf.set_asp_conf_values(values)
        logging.debug('code = %s', code)

        op_code = '0' if code == 0 else '1'

        def _record(action, ups):
            if action == 'Enable':
                msg = ugettext_noop('Enable the mouse trace collection')
            elif action == 'Disable':
                msg = ugettext_noop('Disable the mouse trace collection')
            elif action == 'Modify':
                msg = ugettext_noop('Apply the mouse trace collection to {upstream_keys}.')

            operation_log(request, ugettext_noop('Labs'), ugettext_noop(action), op_code, {
                'msg': msg,
                'extra': {
                    'upstream_keys': ups,
                }
            })

        if op_enable_upstreams:
            _record('Enable', ', '.join(op_enable_upstreams))
        if op_close_upstreams:
            _record('Disable', ', '.join(op_close_upstreams))
        if op_change_upstreams:
            _record('Modify', ', '.join(op_change_upstreams))

        if code == 0:
            save_f = json.dumps({'save_success': True})
        else:
            save_f = json.dumps({
                'save_success': False,
                'error_msg': _('Failed to save upstream configuration')
            })

        return HttpResponse(save_f, content_type='application/json')
    else:
        if not has_permission(request.user.username, 'Labs', 'read'):
            return HttpResponse(status=403)
        upstream_conf_list = sorted(nginx_conf.get_all_upstreams_for_show(), key=operator.itemgetter('name'))
        upstream_list = []
        track_collect_settings_list = []
        enable_track_collect = False

        for upstream in upstream_conf_list:
            upstream_info = {
                'key': upstream['key'],
                'name': upstream['name'],
                'IsHttps': upstream['IsHttps']
            }
            upstream_list.append(upstream_info)
            enable_track_collect |= upstream.get('enable_track_collect', False)
            collect_conf = upstream.get('track_collect_settings', {})

            if len(collect_conf):
                conf_val = {
                    'key': upstream['key'],
                    'name': upstream['name'],
                    'IsHttps': upstream['IsHttps'],
                    'filterConf': collect_conf
                }

                track_collect_settings_list.append(conf_val)
                logging.debug('=======> {0}'.format(conf_val))

        return base_render_to_response(request, 'v2/labs/mouse_track_collect.html', {
                                            'enable_track_collect': enable_track_collect,
                                            'upstream_list': upstream_list,
                                            'track_collect_settings': track_collect_settings_list
                                        })



@login_required
@expert_mode_required
@check_permission('Labs', 'read')
def labs_sniffer(request):
    return base_render_to_response(request, 'v2/labs/sniffer.html',
                                   {
                                       'mSnifferData': _get_tcpdump_status(),
                                       'tcpdumpStatus': _get_all_tcpdump_status(),
                                   })

@login_required
@expert_mode_required
@check_permission('Labs', 'read')
def waf_rule_upload(request):
    return base_render_to_response(request, 'v2/labs/waf_rule_upload.html')


def check_waf_cache_headers(headers):
    pattern = re.compile(r'^[a-zA-Z0-9_-]+$')
    header_list = headers.split(',')

    if len(headers) > 1024:
        return False

    if len(header_list) > 20 or len(header_list) == 0:
        return False

    if len(set(header_list)) < len(header_list):
        return False

    for header in header_list:
        if not pattern.match(header):
            return False
        if header == "":
            return False
    return True


@login_required
@expert_mode_required
@check_permission('Labs', 'write')
@require_http_methods(['GET', 'POST'])
def waf_cache_settings(request):
    nginxConf = NginxConf()
    rule_waf_cache_enable = nginxConf.get_conf().get_value('nginx/rule_waf_cache/enabled', False)
    waf_cache_mode = nginxConf.get_conf().get_value('nginx/rule_waf_cache/cache_mode', "Regular")
    waf_cache_headers = nginxConf.get_conf().get_value('nginx/rule_waf_cache/cache_headers', "cookie,referer,accept")

    if request.method == 'POST':
        request_body = json.loads(request.body)

        values = {}
        enable = request_body.get('enable', False)
        cache_mode = request_body.get('cache_mode', 'Regular')
        new_cache_headers =  request_body.get('cache_headers', 'cookie,referer,accept')

        if not check_waf_cache_headers(new_cache_headers):
            return JsonResponse({'save_success': False})

        if enable != rule_waf_cache_enable:
            values.update({'nginx/rule_waf_cache/enabled': enable})

        if cache_mode != waf_cache_mode:
            values.update({'nginx/rule_waf_cache/cache_mode': cache_mode})

        if new_cache_headers != waf_cache_headers:
            values.update({'nginx/rule_waf_cache/cache_headers': new_cache_headers})

        if values:
            code, output, cmd_line = nginxConf.set_asp_conf_values(values)
            if code != 0:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop("Modify"), 1, {'msg': ugettext_noop('WAF Cache Speedup')})
                return JsonResponse({'save_success': False})
            else:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop("Modify"), 0, {'msg': ugettext_noop('WAF Cache Speedup')})
        return JsonResponse({'save_success': True})

    return base_render_to_response(request, 'v2/labs/waf_cache_settings.html', {'rule_waf_cache_enable': rule_waf_cache_enable,
                                                                                'waf_cache_mode': waf_cache_mode, 'waf_cache_headers': waf_cache_headers })


@login_required
@expert_mode_required
@check_permission('Labs', 'write')
@require_http_methods(['GET', 'POST'])
def http_protocol_compatibility(request):
    nginxConf = NginxConf()
    uri_relaxed = nginxConf.get_conf().get_value('nginx/http_protocol_compatibility/uri_relaxed', False)

    if request.method == 'POST':
        request_body = json.loads(request.body)

        values = {}
        next_uri_relaxed = request_body.get('uri_relaxed', False)

        if uri_relaxed != next_uri_relaxed:
            values.update({'nginx/http_protocol_compatibility/uri_relaxed': next_uri_relaxed})

        if values:
            code, _, _ = nginxConf.set_asp_conf_values(values)
            if code != 0:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop("Modify"), 1, {'msg': ugettext_noop('HTTP Protocol Compatibility')})
                return JsonResponse({'save_success': False})
            else:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop("Modify"), 0, {'msg': ugettext_noop('HTTP Protocol Compatibility')})
        return JsonResponse({'save_success': True})

    return base_render_to_response(request, 'v2/labs/http_protocol_compatibility.html', {'uri_relaxed': uri_relaxed})

@login_required
@check_permission('WAF_Analysis_And_Process', 'read')
def waf_misreport_analysis(request):

    date_start = int(time.time()) - 60 * 60
    date_end = int(time.time())
    nginxConf = NginxConf()
    conf = WafConf()
    whitelist = conf.get_waf_id_whitelist()

    upstreamKeys = nginxConf.get_all_upstreams_with_key_server()

    return base_render_to_response(request, 'v2/waf/waf_misreport_analysis.html',
                                   {
                                       'date_start': date_start,
                                       'date_end': date_end,
                                       'upstreamList':upstreamKeys,
                                       'id_whitelist': whitelist
                                   })

@has_dm_permission(LicenseInfo.DM_ADVANCED_WAF)
@login_required
@check_permission('WAF_Global_Whitelist', 'read')
def waf_global_whitelist(request):
    date_start = int(time.time()) - 60 * 60
    date_end = int(time.time())

    nginxConf = NginxConf()
    conf = WafConf()
    whitelist = conf.get_waf_id_whitelist()

    upstreamKeys = nginxConf.get_all_upstreams_with_key_server()

    return base_render_to_response(request, 'v2/waf/global_waf_whitelist.html',
                                   {
                                       'date_start': date_start,
                                       'date_end': date_end,
                                       'upstreamList':upstreamKeys,
                                       'id_whitelist': whitelist
                                   })


@login_required
def httpcap_cfg(request):
    if not has_permission(request.user.username, 'HttpCap_Cfg', 'read'):
        return HttpResponse(status=403)

    conf = ConfDb()

    token = ''
    nodes = map(int, conf.get_nodeid_list_by_roles('api_gateway'))
    if nodes:
        ip = conf.get_value('cluster/node_list/{}/admin/ip'.format(min(nodes)))
        d = conf.get_value('api_gateway/token/api_cap_agent')
        if type(d) is dict:
            secret = d.get('secret')
            if secret:
                token = '{}@{}:20167'.format(secret, ip)
    filter_rules = conf.get_value('httpcap_cfg/filter_rules', [])
    filter_enable = conf.get_value('httpcap_cfg/filter_enable', True)
    filter_outbound_enable = conf.get_value('httpcap_cfg/filter_outbound_enable', True)

    v = {'token' : token,
         'filter_rules' : filter_rules,
         'filter_enable' : filter_enable,
         'filter_outbound_enable': filter_outbound_enable
         }

    kmap = (
        {'name': 'timeout_sec', 'min': 5, 'max': 60, 'default': 20},
        {'name': 'cap_mem', 'min': 1, 'max': 20, 'default': 5},
        {'name': 'mtu', 'min': 1500, 'max': 9000, 'default': 1600},
        {'name': 'max_msg_size_kb', 'min': 1, 'max': 30, 'default': 10},
        {'name': 'agent_cap_mem', 'min': 1, 'max': 20, 'default': 5},
        {'name': 'agent_cpu_quota', 'min': 1, 'max': 100, 'default': 20},
        {'name': 'limit_max_throughput_mbit', 'min': 1, 'max': 100000, 'default': _('Default')},
        {'name': 'limit_max_pps_kp', 'min': 1, 'max': 2048, 'default': _('Default')},
        {'name': 'limit_max_tps', 'min': 1, 'max': 30000, 'default': _('Default')},
    )
    for i in kmap:
        name = i['name']
        v[name] = conf.get_value('httpcap_cfg/' + name, '')
        v[name + '_default'] = i['default']
        v[name + '_value'] = v[name] or i['default']
        v[name + '_min'] = i['min']
        v[name + '_max'] = i['max']

    # VXLAN settings
    v["vxlan_ports"] = conf.get_value("httpcap_cfg/vxlan_ports") or []
    v["vxlan_enabled"] = conf.get_value("httpcap_cfg/vxlan_enabled", True)

    # non-response capture switch
    v["no_resp_filter_enabled"] = conf.get_value("httpcap_cfg/no_resp_filter_enabled", False)
    v["proto_gre_enabled"] = conf.get_value("httpcap_cfg/proto_gre_enabled", False)

    old_cfg = copy.deepcopy(v)
    cfg = {}
    new_v = {}
    if request.method == 'POST':
        if not has_permission(request.user.username, 'HttpCap_Cfg', 'write'):
            return HttpResponse(status=403)

        try:
            req_data = json.loads(request.body)
        except:
            req_data = {}

        for i in kmap:
            name = i['name']
            try:
                x = req_data.get(name)
                if x == '':
                    new_v[name] = ''
                elif i['min'] <= int(x) <= i['max']:
                    new_v[name] = int(x)
            except:
                pass

            if name in new_v and new_v[name] != v[name]:
                cfg['httpcap_cfg/'+name] = new_v[name]

        # VXLAN settings
        vxlan_enabled = req_data.get("vxlan_enabled")
        if vxlan_enabled != None and vxlan_enabled != v["vxlan_enabled"]:
            cfg["httpcap_cfg/vxlan_enabled"] = vxlan_enabled
            new_v["vxlan_enabled"] = vxlan_enabled

        if vxlan_enabled:
            try:
                vxlan_ports = [int(item.strip()) for item in req_data.get("vxlan_ports", "").split(",") if item]
                logger.debug("set vxlan ports: {}".format(vxlan_ports))
                if len(vxlan_ports) > 10:
                    raise ValueError("vxlan ports limit 10")
                ports = set()
                for p in vxlan_ports:
                    if p in ports:
                        raise ValueError("port {} repeated".format(p))
                    if p > 65535 or p < 0:
                        raise ValueError("port {} invalid (1 - 65535)".format(p))
                    ports.add(p)

            except Exception as e:
                logger.error("convert vxlan ports failed: {}: {}".format(vxlan_ports, e))
                return JsonResponse({
                    "update": False,
                    "msg": _("Save failed")
                })

            if set(vxlan_ports) != set(v["vxlan_ports"]):
                cfg["httpcap_cfg/vxlan_ports"] = vxlan_ports
                new_v["vxlan_ports"] = vxlan_ports

        # non-response capture switch
        no_resp_filter_enabled = req_data.get("no_resp_filter_enabled")
        if no_resp_filter_enabled is not None and no_resp_filter_enabled != v["no_resp_filter_enabled"]:
            cfg["httpcap_cfg/no_resp_filter_enabled"] = no_resp_filter_enabled
            new_v["no_resp_filter_enabled"] = no_resp_filter_enabled

        # proto gre enabled
        proto_gre_enabled = req_data.get("proto_gre_enabled")
        if proto_gre_enabled is not None and proto_gre_enabled != v["proto_gre_enabled"]:
            cfg["httpcap_cfg/proto_gre_enabled"] = proto_gre_enabled
            new_v["proto_gre_enabled"] = proto_gre_enabled

        v['update'] = False
        v['msg'] = _("No configuration has been changed!")

        x = req_data.get('filter_rules')
        if x is not None:
            ok, msg = filter_rules_check(x)
            if ok:
                cfg['httpcap_cfg/filter_rules'] = x
                new_v['filter_rules'] = x
            else:
                v['msg'] = msg
                return HttpResponse(json.dumps(v, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        f_enabled = req_data.get('filter_enable')
        if f_enabled is not None:
            cfg['httpcap_cfg/filter_enable'] = f_enabled
            new_v['filter_enable'] = f_enabled

        o_enabled = req_data.get('filter_outbound_enable')
        if o_enabled is not None:
            cfg['httpcap_cfg/filter_outbound_enable'] = o_enabled
            new_v['filter_outbound_enable'] = o_enabled

        if cfg:
            if service_mgr_set_asp_config(cfg, 'IP', 'User')[0] == 0:
                v.update(new_v)
                v['msg'] = _("Save Successfully")
                v['update'] = True
                old_cfg = {item: old_cfg[item] for item in new_v if item in old_cfg and old_cfg[item] != new_v[item]}
                new_cfg = {item: new_v[item] for item in old_cfg}
                info = 'OLD: {}\n\nNEW: {}'.format(json.dumps(old_cfg, indent=2), json.dumps(new_cfg, indent=2))
                operation_log(request, ugettext_noop('System'), ugettext_noop('Modify'), '0', {'msg': ugettext_noop('HTTP Capture Configuration'), 'info': info })
            else:
                v['msg'] = _("Save failed")
                operation_log(request, ugettext_noop('System'), ugettext_noop('Modify'), '1', {'msg': ugettext_noop('HTTP Capture Configuration')})
        return HttpResponse(json.dumps(v, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)
    else:
        # vxlan_ports 转换为前端的字符串
        v["vxlan_ports"] = ",".join([str(item) for item in v["vxlan_ports"]])
        return base_render_to_response(request, 'vue_front/httpcap_cfg/httpcap_cfg.html', {'datas': v})


@login_required
@expert_mode_required
@check_permission('Labs', 'read')
@allow_method('get')
def labs_download_ruleFile(request):

    rules_list = ConfDb().get_value("cluster/adrules", "[]")
    try:
        rules_list = json.loads(rules_list, encoding='utf-8')
    except Exception as e:
        logger.info("Download rule failed:{0}".format(e))
        rules_list = []

    for x in rules_list:
        #for upgrade from old version
        if "enabled" not in x.keys():
            x["enabled"] = True
        if "description" not in x.keys():
            x["description"] = ""

    rules_list = json.dumps(rules_list, indent=4,encoding='utf-8')
    response = HttpResponse(rules_list, content_type=CONTENT_TYPE_OCT_STREAM)
    response['Content-Disposition'] = "attachment; filename=rules_config.json"

    return response

@login_required
@expert_mode_required
@check_permission('Labs', 'write')
@allow_method('post')
def labs_upload_ruleFile(request):

    rules_list = []
    error_code = 0
    error_msg = None
    jsonfile = None
    jsonrules = []

    try:
        jsonfile = request.FILES.get('labfile')
        jsonrules = jsonfile.read()
    except Exception as e:
        error_code  = ERR_CODE_ADRULES_INVALID_FILE
        error_msg   = e
        return HttpResponse(json.dumps({"result":1, "error_code":error_code, "error_msg":error_msg}), content_type=CONTENT_TYPE_PLAIN)

    if not jsonrules:
        error_code = ERR_CODE_ADRULES_INVALID_FILE
        error_msg = _('Uploaded empty file.')
    else:
        try:
            rules_list = json.loads(jsonrules, encoding='utf-8')
            if not isinstance(rules_list, list):
                error_code = ERR_CODE_ADRULES_INVALID_FILE
                error_msg = _('The file contains rules in invalid format.')
        except:
            error_code = ERR_CODE_ADRULES_INVALID_FILE
            error_msg = _('The file contains rules in invalid format.')

    if len(rules_list) > MAX_ADRULES_COUNT:
        error_code = ERR_CODE_ADRULES_MAX_LIMIT
        error_msg  = _("The number of rules is more than {0}.".format(MAX_ADRULES_COUNT))

    if not error_msg:
        for one in rules_list:
            error_code, err_cause = rules_is_valid(one)
            if error_code:
                error_msg = _('Invalid rule:{0}, cause:{1}.'.format(json.dumps(one, encoding='utf-8'), err_cause))
                break

    if not error_code:
        rules_list.sort(key=lambda obj: obj.get('rule_id'))

        commit_status = service_mgr_set_asp_config({"cluster/adrules": json.dumps(rules_list, encoding='utf-8')}, 'IP', 'User')
        if not commit_status or 0 != commit_status[0]:
            error_code = ERR_CODE_ADRULES_TIMEOUT
            error_msg = _("Operation timeout.")

    op_code = '0' if not error_code else '1'
    operation_log(request, ugettext_noop('Labs'), ugettext_noop('Upload'), op_code, {
        'msg': ugettext_noop('User behavior rules.')
    })

    if not error_code:
        return HttpResponse(json.dumps({"result": 0, "error_code": "", "error_msg": ""}), content_type=CONTENT_TYPE_PLAIN)
    else:
        return HttpResponse(json.dumps({"result":1, "error_code":error_code, "error_msg":error_msg}), content_type=CONTENT_TYPE_PLAIN)


def update_waf_file_shared_conf(filename, md5, node_info):
    nginxConf = NginxConf()
    c_time = time.time()
    nginxConf.set_waf_rules(md5.hexdigest(), filename, node_info, c_time)

def _test_nginx():
    '''
    Test nginx validation.
    '''
    conf_file = '/etc/asp/release/nginx/modsecurity/test_config/nginx_waf_test.conf'
    cmd_line = 'nginx -t -c {0} 2>&1'.format(conf_file)
    status, output = commands.getstatusoutput(cmd_line)
    syntax_str = "{0} syntax is ok".format(conf_file)
    if syntax_str in output :
        return True
    else :
        logger.error(output)
        return False

@login_required
@check_permission('Labs', 'write')
def import_waf_rules_file(request):
    USER_UPLOAD_DIR = '/etc/asp/release/nginx/modsecurity/owasp-modsecurity-crs/rules/USER-UPLOAD'
    wafRuleFile = request.FILES.get('waf_rules_file')
    wafFileName = request.FILES['waf_rules_file'].name
    if not re.match(r'[\w-]{1,123}\.(conf|data)$', wafFileName):
        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Import'), '1',
                      {'msg': ugettext_noop('waf rule file uploaded unsuccessfully')})
        return HttpResponse(json.dumps({'result':False, 'msg':_("Invalid WAF rule file.")}), content_type=CONTENT_TYPE_PLAIN)
    destWafFile = '{0}/{1}'.format(USER_UPLOAD_DIR, wafFileName)
    with open(destWafFile, 'wb') as destination:
        for chunk in wafRuleFile.chunks():
            destination.write(chunk)
        destination.flush()

    logger.info('***********************************************:{0}'.format(destWafFile))
    if (destWafFile == ''):
        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Import'), '1',
                      {'msg': ugettext_noop('waf rule file uploaded unsuccessfully')})
        return HttpResponse(json.dumps({'result':False, 'msg':_("Uploaded empty file.")}), content_type=CONTENT_TYPE_PLAIN)
    else:
        md5 = hashlib.md5()
        md5.update(destWafFile)
        conf = ConfDb()
        node_info = conf.get_admin_ip()
        if _test_nginx():
            cpWafFile = get_release_file('web_admin/src/web_admin/templates/report/' + wafFileName)
            exe('cp {0} {1}'.format(destWafFile, cpWafFile))

            update_waf_file_shared_conf(wafFileName, md5, node_info)
            operation_log(request, ugettext_noop('Labs'), ugettext_noop('Import'), '0',
                          {'msg': ugettext_noop('waf rule file uploaded successfully')})
            return HttpResponse(json.dumps({'result':True, 'msg':_("WAF rule file has been imported.")}), content_type=CONTENT_TYPE_PLAIN)
        else:
            os.remove(destWafFile)
            operation_log(request, ugettext_noop('Labs'), ugettext_noop('Import'), '1',
                          {'msg': ugettext_noop('waf rule file uploaded unsuccessfully')})
            return HttpResponse(json.dumps({'result':False, 'msg':_("Invalid WAF rule file.")}), content_type=CONTENT_TYPE_PLAIN)

def _clean_delete(d):
    if type(d) == tuple or type(d) == list:
        for i in d:
            _clean_delete(i)
    elif type(d) == dict:
        for k,v in d.items():
            if type(v) == dict:
                if v.get('_deleted') == 1:
                    d.pop(k)
                else:
                    _clean_delete(v)

def get_buildin_ruleset_version():
    filepath = buildin_ruleset_file_pathname()
    if not os.path.exists(filepath):
        return ''
    try:
        with zipfile.ZipFile(filepath, 'r') as zip_ref:
            meta = zip_ref.open("manifest.json", "r").read()
            return json.loads(meta, encoding='utf-8')['waf_ruleset_version']
    except Exception:
        logging.error(Exception)
        return ''


def export_sys_config(tempConf, without_ssl_key=True):
    conf = WebconsoleConf()
    if os.path.exists(tempConf):
        os.remove(tempConf)
    conf = ConfDb({'sphinx_version': {'_value': get_manifest_info().get('cluster_version', '')}})
    conf.fn = tempConf
    mobile = MobileConf(logger)
    mobile.tran_certificate_pwd()

    api_info = export_api_bot_defender_config()
    if api_info:
        conf.set_value('api_info', api_info)

    all_conf = ConfDb().clone().as_mut_ref().get_root()
    sailfish_info = all_conf.get('sailfish', None)

    mobile_info = all_conf['cluster'].get('mobile') if all_conf.get('cluster') else None
    all_conf = conf_mask(all_conf)

    ######waf
    delete_waf_strategy_changed(all_conf)

    delete_waf_resource_file_config(all_conf)
    ########end waf

    snmp_config = all_conf['os'].get('network', {}).get('snmp', None)
    if snmp_config is not None:
        snmp_auth_pwd = snmp_config.get('auth_pwd', None)
        snmp_priv_pwd = snmp_config.get('priv_pwd', None)
        if snmp_auth_pwd is not None:
            encoded_auth_pwd = aes_encrypt(get_asp_aes_key(), snmp_auth_pwd['_value'])
            all_conf['os']['network']['snmp'].update({'encoded_auth_pwd': {'_value': encoded_auth_pwd}})
            all_conf['os']['network']['snmp'].pop('auth_pwd')
        if snmp_priv_pwd is not None:
            encoded_priv_pwd = aes_encrypt(get_asp_aes_key(), snmp_priv_pwd['_value'])
            all_conf['os']['network']['snmp'].update({'encoded_priv_pwd': {'_value': encoded_priv_pwd}})
            all_conf['os']['network']['snmp'].pop('priv_pwd')

    if sailfish_info:
        if type(sailfish_info) == dict and sailfish_info.has_key("servers"):
            sailfish_info.pop("servers")
        all_conf["sailfish"] = sailfish_info

    if mobile_info:
        all_conf['cluster'] = {'mobile': mobile_info}
    _clean_delete(all_conf)

    geolib = all_conf['nginx'].get('geolib')
    if geolib and geolib.get('_value').get('password'):
        geolib['_value']['password'] = ''

    # do not export tool_code_feature
    if all_conf['nginx'].get('tool_code_feature'):
        del all_conf['nginx']['tool_code_feature']

    # clean Certification and CertificationKey for the upstream server before export
    upstream_info = all_conf['nginx'].get('upstreams')
    if without_ssl_key and upstream_info and len(upstream_info.keys()) != 0:
        for upstream_server in upstream_info.keys():
            upstream_info[upstream_server].pop('Certification', None)
            upstream_info[upstream_server].pop('CertificationKey', None)
            upstream_info[upstream_server].pop('cert_file_name', None)
            upstream_info[upstream_server].pop('key_file_name', None)
            upstream_info[upstream_server].pop('gm_sign_certification', None)
            upstream_info[upstream_server].pop('gm_sign_certificationKey', None)
            upstream_info[upstream_server].pop('gm_sign_cert_file_name', None)
            upstream_info[upstream_server].pop('gm_sign_key_file_name', None)
            upstream_info[upstream_server].pop('gm_enc_certification', None)
            upstream_info[upstream_server].pop('gm_enc_certificationKey', None)
            upstream_info[upstream_server].pop('gm_enc_cert_file_name', None)
            upstream_info[upstream_server].pop('gm_enc_key_file_name', None)
            upstream_info[upstream_server].pop('request_protection_list', None)

    def mpp_conf_filter(mppType):
        mpp_conf = all_conf['nginx'].get(mppType, {})
        if mpp_conf:
            mpp_conf.pop('customer_id', None)
            mpp_conf.pop('token_names', None)
            mpp_conf.pop('sdk_version', None)
        if 'app_list' in mpp_conf:
            for app in mpp_conf.get('app_list').get('_value').values():
                app.pop('sdk_key', None)

    mpp_conf_filter('wechat_app')
    mpp_conf_filter('alipay_mpp')
    mpp_conf_filter('mpaas_mpp')

    # remove pii serial id
    api_pii_conf = all_conf.get('nginx', {}).get('api', {}).get('v2', {})
    if api_pii_conf.has_key('pii_custom_last_serial_id'):
        api_pii_conf.pop('pii_custom_last_serial_id')

    api_pii_list = api_pii_conf.get('api_pii_list', {}).get('_value', [])
    for pii in api_pii_list:
        if pii.has_key('serial_id'):
            pii.pop('serial_id')

    conf.set_value("system_settings", all_conf)

    ##########ubb
    lua_ubb = export_lub_ubb_config()
    if lua_ubb:
        conf.set_value('lua_ubb', lua_ubb)
    ########endubb

    conf.save()


def validate_scheduled_export_configs(frequency, transmit_mode):
    def validate_addr(addr):
        try:
            validate_ipv46_address(addr)
        except ValidationError:
            domain_regex = re.compile(
                # max length of the domain is 249: 254 (max email length) minus one
                # period, two characters for the TLD, @ sign, & one character before @.
                r'(?:[A-Z0-9](?:[A-Z0-9-]{0,247}[A-Z0-9])?\.)+(?:[A-Z]{2,6}|[A-Z0-9-]{2,}(?<!-))$', re.IGNORECASE)
            if None == domain_regex.match(addr):
                raise ValidationError(ugettext_noop('Please enter a correct IPv4 or IPv6 or domain address.'),
                                      code='invalid')

    def validate_port(port):
        try:
            port_num = int(port)
            if port_num < 0 or port_num > 65535:
                raise ValidationError(ugettext_noop('Please enter a correct port number.'), code='invalid')
        except Exception:
            raise ValidationError(ugettext_noop('Please enter a correct port number.'), code='invalid')

    def validate_absolute_path(path):
        try:
            if not str(path).startswith("/"):
                raise ValidationError(ugettext_noop('Please enter a correct absolute path.'), code='invalid')
        except Exception:
            raise ValidationError(ugettext_noop('Please enter a correct absolute path.'), code='invalid')

    def validate_frequency(frequency):
        cron_reg = re.compile(r'^(\*|[1-5]?[0-9](-[1-5]?[0-9])?)(\/[1-9]|\/[1-5][0-9])?(,(\*|[1-5]?[0-9](-[1-5]?[0-9])?)(\/[1-9]|\/[1-5][0-9])?)* (\*|([0-9]|1[0-9]|2[0-3])(-[0-9]|-1[0-9]|-2[0-3])?)(\/[1-9]|\/1[0-9]|\/2[0-3])?(,(\*|([0-9]|1[0-9]|2[0-3])(-[0-9]|-1[0-9]|-2[0-3])?)(\/[1-9]|\/1[0-9]|\/2[0-3])?)* (\*|[1-9]|[12][0-9]|3[01])(-[1-9]|-[12][0-9]|-3[01])?(\/[1-9]|\/[12][0-9]|\/30)?(,(\*|[1-9]|[12][0-9]|3[01])(-[1-9]|-[12][0-9]|-3[01])?(\/[1-9]|\/[12][0-9]|\/30)?)* (\*|([1-9]|1[012])(-[1-9]|-1[012])?)(\/[1-9]|\/1[01])?(,(\*|([1-9]|1[012])(-[1-9]|-1[012])?)(\/[1-9]|\/1[01])?)* (\*|[0-6](-[0-6])?)(\/[1-6])?(,(\*|[0-6](-[0-6])?)(\/[1-6])?)*$')
        if not cron_reg.match(frequency):
            raise ValidationError(ugettext_noop('Please enter a valid frequency string.'),
                                      code='invalid')



    try:
        validate_frequency(frequency)
        for transmit_config in transmit_mode:
            protocol = transmit_config.get("protocol")
            if protocol == "sftp":
                remote_addr = transmit_config.get("remote_addr")
                remote_port = transmit_config.get("remote_port")
                target_path = transmit_config.get("target_path")

                validate_addr(remote_addr)
                validate_port(remote_port)
                validate_absolute_path(target_path)
    except ValidationError as e:
        raise e


def conduct_scheduled_export(transmit_config):
    file_name = 'system_settings_{}.json'.format(time.strftime('%Y-%m-%d_%H_%M_%S', time.localtime(time.time())))
    tempConf = '/tmp/' + file_name
    export_sys_config(tempConf)

    protocol = transmit_config.get("protocol")
    op_code = '0'
    log_detail = {
        'msg': "",
        'err_msg': "",
        'err_args': "",
        'file_name': "file_name",
        'recv_addr_port': ""
    }

    if protocol == "sftp":
        remote_addr = transmit_config.get("remote_addr")
        remote_port = transmit_config.get("remote_port")
        user_name = transmit_config.get("user_name")
        user_pwd = transmit_config.get("user_pwd")
        target_path = transmit_config.get("target_path")
        ssh_client = None
        sftp_client = None
        recev_addr_port = "[{0}]:{1}".format(remote_addr, remote_port) if is_valid_ipv6_address(
            remote_addr) else "{0}:{1}".format(remote_addr, remote_port)

        try:
            import paramiko
            # 创建SSH和SFTP客户端
            ssh_client = paramiko.SSHClient()
            ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            # 超时时间设置为10秒
            ssh_client.connect(hostname=remote_addr, port=int(remote_port), username=user_name, password=user_pwd, timeout=10)

            sftp_client = ssh_client.open_sftp()

            # 上传文件到远程服务器
            local_file_path = tempConf
            remote_file_path = os.path.join(target_path, file_name)
            sftp_client.put(local_file_path, remote_file_path)

            # 关闭SFTP和SSH客户端
            sftp_client.close()
            ssh_client.close()

            log_detail = {
                'msg': ugettext_noop('Scheduled Export Successed'),
                'err_msg': "",
                'err_args': "",
                'file_name': file_name,
                'recv_addr_port': recev_addr_port
            }
        except Exception as e:
            op_code = '1'
            log_detail = {
                'msg': ugettext_noop('Scheduled Export Failed'),
                'err_msg': e.message,
                'err_args': str(e),
                'file_name': file_name,
                'recv_addr_port': recev_addr_port
            }
        finally:
            if sftp_client:
                sftp_client.close()
            if ssh_client:
                ssh_client.close()

            if os.path.exists(tempConf):
                os.remove(tempConf)
            if os.path.exists('/tmp/.' + file_name + ".lock"):
                os.remove('/tmp/.' + file_name + ".lock")
            return (op_code, log_detail)
    else:
        return (op_code, log_detail)





@login_required
@check_permission('Export_System_Settings', 'read')
def export_all_config(request):
    if request.method == "GET":
        action = request.GET.get('action')
        if action == 'export':
            tempConf = '/tmp/exportConf.json'
            without_ssl_key = request.GET.get('k') != 'yes'
            export_sys_config(tempConf, without_ssl_key)
            with open(tempConf) as json_file:
                response = HttpResponse(json_file.read(), content_type='application/json')
                response['Content-Disposition'] = "attachment; filename=%s" % (
                    'system_settings_{}.json'.format(time.strftime('%Y-%m-%d_%X', time.localtime(time.time()))))
                operation_log(request, ugettext_noop('System'), ugettext_noop('Export'), '0', {
                    'msg': ugettext_noop('System Config has been exported'), },
                              user=request.POST.get('username'))
                return response
    elif request.method == "POST":
        data = {'result': 'Error'}
        op_code = '1'
        action = request.GET.get("action")
        config = json.loads(request.body)
        is_scheduled_export_enabled = config.get("enabled", False)
        frequency = config.get("frequency")
        transmit_mode = config.get("transmit_mode")
        try:
            if frequency and transmit_mode:
                validate_scheduled_export_configs(frequency, transmit_mode)
        except Exception as e:
            return json_fail_response(e.message)

        if action == "scheduled_export":
            wc_conf = WebconsoleConf()
            # 获取保存前的配置信息
            old_enabled = wc_conf.get_value('cluster/scheduled_export', False, is_abs=True)
            old_scheduled_export_configs = wc_conf.get_value('cluster/scheduled_export_configs', {}, is_abs=True)
            old_frequency = old_scheduled_export_configs.get("frequency", "")
            old_transmit_mode = old_scheduled_export_configs.get("transmit_mode", [{"protocol": "sftp",
                                                                                    "remote_addr": "",
                                                                                    "remote_port": "",
                                                                                    "user_name": "",
                                                                                    "user_pwd": "",
                                                                                    "target_path": ""}])
            # 保存配置参数
            if is_scheduled_export_enabled:
                for i in range(0, len(transmit_mode)):
                    # 密码未被修改
                    if transmit_mode[i].get("user_pwd") == ECHO_PASSWORD:
                        transmit_mode[i]["user_pwd"] = old_transmit_mode[i]["user_pwd"]
                    else:
                        transmit_mode[i]["user_pwd"] = asp_encrypt(transmit_mode[i]["user_pwd"])
                if wc_conf.set_sheduled_export(is_scheduled_export_enabled, frequency, transmit_mode):
                    data = {'result': 'OK'}
                    op_code = '0'
            else:
                if wc_conf.set_sheduled_export(is_scheduled_export_enabled, old_frequency, old_transmit_mode):
                    data = {'result': 'OK'}
                    op_code = '0'

            #写入操作日志
            if old_enabled != is_scheduled_export_enabled:
                op_action = ugettext_noop('Enable') if is_scheduled_export_enabled else ugettext_noop('Disable')
                op_msg = ugettext_noop('Enable Scheduled Export.') if is_scheduled_export_enabled \
                    else ugettext_noop('Disable Scheduled Export.')
                operation_log(request, ugettext_noop('System'), op_action, op_code, {'msg': op_msg,}, user=request.POST.get('username'))

            if old_frequency != frequency and is_scheduled_export_enabled:
                operation_log(request, ugettext_noop('System'), ugettext_noop('Modify'), op_code, {
                    'msg': ugettext_noop('Modify Scheduled Export - Frequency.')}, user=request.POST.get('username'))
            if old_transmit_mode != transmit_mode and is_scheduled_export_enabled:
                operation_log(request, ugettext_noop('System'), ugettext_noop('Modify'), op_code, {
                    'msg': ugettext_noop('Modify Scheduled Export - Transmit Mode.')}, user=request.POST.get('username'))
            return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        elif action == "scheduled_export_test":
            conf_db = ConfDb()
            # 1 执行配置导出并转发
            if ECHO_PASSWORD == transmit_mode[0].get("user_pwd"):
                transmit_mode[0]["user_pwd"] = asp_decrypt(conf_db.get_value("cluster/scheduled_export_configs",{}).get("transmit_mode",[])[0].get("user_pwd"))
            op_code, details = conduct_scheduled_export(transmit_mode[0])

            if op_code == '0':
                file_name = details.get("file_name")
                remote = details.get("recv_addr_port")
                result = {"msg": "{0} has been sent to {1}.".format(file_name, remote), "result": "OK"}

            else:
                err_args = details.get("err_args")
                err_msg = details.get("err_msg")
                if not err_msg == "":
                    msg = err_msg
                else:
                    msg = err_args
                result = {"msg": msg, "result": "Failed"}
            return HttpResponse(json.dumps(result, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    # Invalid request
    return HttpResponseRedirect("/overview/")


def get_scheduled_export_records(request):
    user = request.user.username
    cur_page = request.GET.get('page')
    page = to_int(cur_page, 0)

    confdb = ConfDb()
    log_entrys = []
    if confdb.get_value('report_type') == 'sailfish':
        searchquery = "select * from operation_log WHERE (`action`='Scheduled_Export')  order by timestamp DESC LIMIT 10"
        result, total_count = sailfish_restapi_request(searchquery)
        if total_count > 0:
            column_names = result['column_names']
            column_index = {name: column_names.index(name) for name in column_names}
            for record in result['records']:
                detail = json.loads(record[column_index['detail']])
                file_name = detail.get("file_name", "")
                recv_addr_port = detail.get("recv_addr_port", "")
                err_msg = detail.get("err_msg", "")
                if err_msg == "":
                    err_msg = detail.get("err_args","")
                result = "success" if str(record[column_index['result']]) == "0" else "failed"

                log_entrys.append({
                    'timestamp': record[column_index['timestamp']],
                    'file_name': file_name,
                    'recv_addr_port': recv_addr_port,
                    'result': result,
                    'err_msg': err_msg,
                    'node_ip': record[column_index['node_ip']]
                })
    else:
        search_query = "search index=operation_log | sort - timestamp | search action=Scheduled_Export| head 10"
        response, total_count = phoenix_restapi_request(search_query)
        if total_count > 0:
            data = response.strip()
            data = data.split('\n')
            for rec in data:
                try:
                    record =json.loads(rec)
                    # {u'node_ip': u'**********',
                    # u'timestamp': 1714203919,
                    # u'detail': u'{"msg": "Scheduled Export Failed",
                    #               "file_name": "system_settings_2024-04-27_15_45_17.json",
                    #               "err_msg": "Authentication failed.",
                    #               "recv_addr_port": "***********:25",
                    #               "err_args": "Authentication failed."}',
                    # u'module': u'System',
                    # u'src_ip': u'127.0.0.1',
                    # u'user': u'admin',
                    # u'action': u'Scheduled_Export',
                    # u'result': u'1'}
                    detail = json.loads(record['detail'])
                    file_name = detail.get("file_name", "")
                    recv_addr_port = detail.get("recv_addr_port", "")
                    err_msg = detail.get("err_msg", "")
                    if err_msg == "":
                        err_msg = detail.get("err_args", "")
                    result = "success" if str(record['result']) == "0" else "failed"

                    log_entrys.append({
                        'timestamp': datetime.datetime.fromtimestamp(record['timestamp']).strftime('%Y-%m-%d %H:%M:%S'),
                        'file_name': file_name,
                        'recv_addr_port': recv_addr_port,
                        'result': result,
                        'err_msg': err_msg,
                        'node_ip': record['node_ip']
                    })
                except Exception as e:
                    logger.error("failed to json.loads record: {}, error={}".format(rec, str(e)))
                    continue


    scheduled_export_records = {
        'scheduled_export_records': log_entrys,
        'rows': len(log_entrys),
        'result': "OK"

    }

    return scheduled_export_records

@login_required
def scheduled_config_export_records(request):
    if request.method == "GET":
        # 查询opration_log, 获取最近10次执行记录
        if not has_permission(request.user.username, 'Operation_Log', 'read'):
            return HttpResponse(status=403)
        # operate_log_info = get_operate_log_file_info(request)
        scheduled_export_records = get_scheduled_export_records(request)

        return JsonResponse(scheduled_export_records)



@login_required
@check_permission('System_General', 'read')
def system_general(request):
    # update / callback
    result = system_update(request)

    # version
    version_info = get_version()
    result['version_info'] = str(version_info[0])
    result['build_info'] = str(version_info[4])
    result['waf_engine_version'] = str(version_info[9])
    result['ruleset_version'] = str(version_info[10])
    result['rollback_ruleset_version'] = extract_rollback_version()
    if has_permission(request.user.username, 'System_General', 'write'):
        local_conf_path = get_waf_conf_path()
        change_log_path = "/etc/asp/release/nginx/modsecurity/owasp-modsecurity-crs/"
        change_log_path = os.path.join(change_log_path, local_conf_path, "change_log.txt")

        with open(change_log_path, 'r') as f:
            result['ruleset_change'] = f.read()

    if request.method == "POST":
        if not has_permission(request.user.username, 'System_General', 'write'):
            return HttpResponse(status=403)
        action = request.POST.get('action', '')
        if action == 'install' or action == 'uploadPKG':
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)

    # import/export system file
    nginxConf = NginxConf()
    license_info = nginxConf.get_license_info()
    is_licencse_activated = license_info.is_valid_license()
    result['is_licencse_activated'] = is_licencse_activated

    from views_proxy import is_upstream_editable
    upstream_editable = is_upstream_editable()
    result['is_upstream_editable'] = upstream_editable

    config_form = ConfigFileForm()
    result['config_form'] = config_form

    # factory_reset
    factory_reset_info = set_factory_reset(request)
    for key in factory_reset_info:
        result[key] = factory_reset_info[key]

    conf_db = ConfDb()
    geolib_conf = conf_db.get_value("nginx/geolib", {})
    is_cvst = False
    geo_file = '/var/asp_data/nginx/AIWENGEO_v1.awdb'
    geo_file_ipv6 = '/var/asp_data/nginx/AIWENGEO_IPV6_v1.awdb'
    result['geolib_file_size'] = 0
    result['geolib_update_time'] = ""
    result['geolib_file_size_v6'] = 0
    result['geolib_update_time_v6'] = ""
    if geolib_conf and 'mmdb_cvst' == geolib_conf.get('geolib_type', 'awdb'):
        geo_file = '/var/asp_data/nginx/GeoIP2-City.mmdb'
        geo_file_ipv6 = ''
        is_cvst = True
    if os.path.exists(geo_file):
        result['geolib_file_size'] = '{:,}'.format(os.path.getsize(geo_file)/1024/1024)
        result['geolib_update_time'] = time.strftime("%Y-%m-%d %X", time.localtime(os.path.getmtime(geo_file)))
        if os.path.exists(geo_file_ipv6):
            result['geolib_file_size_v6'] = '{:,}'.format(os.path.getsize(geo_file_ipv6)/1024/1024)
            result['geolib_update_time_v6'] = time.strftime("%Y-%m-%d %X", time.localtime(os.path.getmtime(geo_file_ipv6)))
        logger.info('###############geo_info.{} {} {} {}'.format(result['geolib_update_time'], result['geolib_file_size'], result['geolib_update_time_v6'], result['geolib_file_size_v6']))


    result['custom_geo_ip'] = conf_db.get_value('nginx/custom_geo_ip', get_default_custom_geo())

    result['report_type'] = conf_db.get_value('report_type', '')
    result['cluster_name'] = conf_db.get_cluster_name()

    try:
        power_token = cache.get("power_token")
    except:
        cache.clear()
        power_token = None
    if not power_token:
        cache.set("power_token", str(uuid.uuid4()), 9999999)

    scheduled_export_records = get_scheduled_export_records(request)
    scheduled_export = conf_db.get_value('cluster/scheduled_export', False)
    scheduled_export_configs = conf_db.get_value('cluster/scheduled_export_configs', {})

    return BaseTemplateResponse(
        request, 'v2/system_general.html', {
            'result': result,
            'is_cvst': is_cvst,
            'enhance_cluster_security': conf_db.get_value('cluster/enhance_cluster_security', False),
            'hide_hardware_info': conf_db.get_value('cluster/hide_hardware_info', False),
            'power_token': cache.get("power_token"),
            'ecs_ip_white_list': conf_db.get_value('cluster/ecs_ip_white_list', ''),
            "scheduled_export": scheduled_export,
            "scheduled_export_configs": scheduled_export_configs,
            "scheduled_export_records": scheduled_export_records,
            'error': result['error'],
        }
    )


@login_required
@check_permission('Log_Settings', 'write')
def system_save_report_type(request):
    ret = {'save_success': False, 'error_msg': ''}
    try:
        data = json.loads(request.body)
    except:
        ret['error_msg'] = _('Invalid parameter')
        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    report_type = data.get('report_type')

    if current_machine_hardware_name() == 'x86_64':
        report_type_scope = ('phoenix', 'sailfish')
    else:
        report_type_scope = ('sailfish',)

    if report_type is None or report_type not in report_type_scope:
        ret['error_msg'] = _('Invalid parameter')
        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    service_mgr_set_asp_config({'report_type': report_type}, 'IP', 'User')
    ret['save_success'] = True

    if report_type == 'phoenix':
        msg = ugettext_noop('Modify Report Type To Phoenix')
    else:
        msg = ugettext_noop('Modify Report Type To Sailfish')
    operation_log(request, ugettext_noop('Log'), ugettext_noop('Modify'), 0, {'msg': msg})

    return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

def get_system_account_management_users(all_no_admin_users, roles_allowed_to_update):
    webConsoleConf = WebconsoleConf()
    for user in all_no_admin_users:
        role = user['role']
        if role in roles_allowed_to_update:
            user['editable'] = True
            user['removable'] = True
            user['unlockable'] = True
        else:
            user['editable'] = False
            user['removable'] = False
            user['unlockable'] = False

        # 判断当前用户是否有效 state 三个值 0->禁用 1->锁定 2->有效
        user['state'] = webConsoleConf.judge_user_state(username = user['user_name'])


@login_required
@check_permission('Login_Account', 'read')
def system_account_management(request):

    webConsoleConf = WebconsoleConf()
    name_of_logged_in_user = request.user.username
    roles_allowed_to_update = query_user_management_policy(current_user=name_of_logged_in_user,
                                                           action="roles_allowed_to_update")

    if request.method == 'DELETE':
        if not has_permission(request.user.username, 'Delete_User', 'write'):
            return HttpResponse(status=403)
        result = {'result': ''}
        delete = json.loads(request.body)
        name = delete.get('username')

        if name == 'admin':
            return HttpResponse(status=403)

        # delete the user from Django DB
        try:
            user = User.objects.get(username=name)
            user.delete()
        except User.DoesNotExist:
            pass

        # delete the user from config file
        user_exist = webConsoleConf.is_user_exist(name)
        if user_exist:
            webConsoleConf.delete_user(name)
            result = {'result': 'ok'}
            operation_log(request, ugettext_noop('Access'), ugettext_noop('Delete'), '0', {
                'msg': ugettext_noop('User has been deleted: '), 'spliceMsg':name})
        else:
            result = {'result': 'error'}

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    all_no_admin_users = webConsoleConf.get_all_no_admin_users()
    lock_user_setting = webConsoleConf.get_lock_user_setting()
    get_system_account_management_users(all_no_admin_users, roles_allowed_to_update)
    for user in all_no_admin_users:
        user['config_2fa'] = WebconsoleConf().is_2fa_enabled()
    captcha = webConsoleConf.is_captcha_enabled(get_current_is_prod_pkg())
    qps_enabled = webConsoleConf.is_qps_enabled()
    totp_enabled = webConsoleConf.is_2fa_enabled()
    aaa_config = webConsoleConf.get_aaa_config()
    kick_user_enabled = webConsoleConf.is_kicked_enabled()
    form = CertFileForm()
    web_console_allow_ip = webConsoleConf.get_web_console_allow_ip() or []
    web_console_allow_ip_enabled = webConsoleConf.get_web_console_allow_ip_status()
    session_idle_timeout = webConsoleConf.get_session_idle_timeout()
    password_config = webConsoleConf.get_password_config()
    password_period = webConsoleConf.get_password_expiration_time()
    password_config["period"] = password_period
    login_failed_period, login_failed_counts, lock_period = webConsoleConf.get_login_failure_lock()
    login_failed_period_default, login_failed_counts_default, lock_period_default = webConsoleConf.get_login_failure_lock_default()
    return base_render_to_response(request, 'v2/login_and_account/login_and_account.html',
                                   {
                                       'form': form,
                                       'users': all_no_admin_users,
                                       'lock_user_setting': lock_user_setting,
                                       'captcha': captcha,
                                       'totp_enabled': totp_enabled,
                                       'kick_user_enabled': kick_user_enabled,
                                       'qps_enabled': qps_enabled,
                                       'aaa_config': aaa_config,
                                       'web_console_allow_ip': web_console_allow_ip,
                                       'web_console_allow_ip_enabled': web_console_allow_ip_enabled,
                                       'session_idle_timeout': session_idle_timeout,
                                       'password_config' : password_config,
                                       'login_failed_period': login_failed_period,
                                       'login_failed_counts': login_failed_counts,
                                       'lock_period': lock_period,
                                       'login_failed_period_default': login_failed_period_default,
                                       'login_failed_counts_default': login_failed_counts_default,
                                       'lock_period_default': lock_period_default
                                   })


@login_required
@check_permission('Reboot', 'write')
@allow_method('post')
def power_reboot(request):
    request_token = request.META.get("HTTP_X_POWER_TOKEN")
    try:
        cache_token = cache.get("power_token")
    except:
        cache.clear()
        cache_token = None

    if request_token != cache_token or (request_token is None and cache_token is None):
        logger.error("remote HTTP_X_POWER_TOKEN is not match local cache power_token or all None")
        return HttpResponse(status=403)

    global ASP_POWER_STATUS
    if ASP_POWER_STATUS:
        return HttpResponse(json.dumps({'status' : ASP_POWER_STATUS}, encoding = 'utf-8'), content_type = CONTENT_TYPE_JSON)

    ASP_POWER_STATUS = 'reboot'

    admin_ip = BaseConf().get_value('_private/os/network/admin/ip')
    msg = ugettext_noop('Reboot node')
    temp_admin_ip = ''
    if admin_ip:
        msg = ugettext_noop('Reboot node: ')
        temp_admin_ip = admin_ip

    operation_log(request, ugettext_noop('System'), ugettext_noop('Reboot'), '0', {
        'msg': msg, 'spliceMsg': temp_admin_ip })

    loop = 3
    while loop:
        os.system('sudo reboot')
        time.sleep(10)
        loop = loop - 1

    logger.info('Reboot failed.')

    ASP_POWER_STATUS = ''
    return HttpResponse(json.dumps({'status' : ASP_POWER_STATUS}, encoding = 'utf-8'), content_type = CONTENT_TYPE_JSON)


@login_required
@check_permission('Shutdown', 'write')
@allow_method('post')
def power_shutdown(request):
    request_token = request.META.get("HTTP_X_POWER_TOKEN")
    try:
        cache_token = cache.get("power_token")
    except:
        cache.clear()
        cache_token = None

    if request_token != cache_token or (request_token is None and cache_token is None):
        logger.error("remote HTTP_X_POWER_TOKEN is not match local cache power_token or all None")
        return HttpResponse(status=403)

    global ASP_POWER_STATUS
    if ASP_POWER_STATUS:
        return HttpResponse(json.dumps({'status' : ASP_POWER_STATUS}, encoding = 'utf-8'), content_type = CONTENT_TYPE_JSON)

    ASP_POWER_STATUS = 'shutdown'

    admin_ip = BaseConf().get_value('_private/os/network/admin/ip')
    msg = ugettext_noop('Shutdown node')
    temp_admin_ip = ''
    if admin_ip:
        msg = ugettext_noop('Shutdown node: ')
        temp_admin_ip = admin_ip

    operation_log(request, ugettext_noop('System'), ugettext_noop('Shutdown'), '0',
                  {'msg': msg, 'spliceMsg':temp_admin_ip })

    loop = 3
    while loop:
        os.system("sudo halt -p")
        time.sleep(10)
        loop = loop - 1

    logger.info('Shutdown failed.')

    ASP_POWER_STATUS = ''
    return HttpResponse(json.dumps({'status' : ASP_POWER_STATUS}, encoding = 'utf-8'), content_type = CONTENT_TYPE_JSON)


def test_connection(request):
    return HttpResponse(json.dumps({'status': 'ok'}, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def is_include_sensitive_character(input_s):
    sensitive_character = ["'", '<', '>', '{', '}']

    for c in sensitive_character:
        if c in input_s:
            return True

    return False


def _get_all_tcpdump_status():
    cur = _get_tcpdump_status()

    conf = ConfDb()
    port = conf.get_value('nginx/web_console/port')

    ret = [cur]
    threads = []

    def tcpdump_status_c(ip):
        try:
            session = new_session_with_sign('tcpdump_api', conf.get_value("cluster/key", ''))
            s = session.get('https://{}:{}/system/labs/capture_status_c/'.format(ip, port), verify=False, timeout=3).json()
            ret.append(s)
        except:
            logging.exception('get system/labs/capture_status_c error: {} {}'.format(ip, port))

    for v in get_cluster_node_list(True).values():
        ip = v.get('_admin_ip')
        if ip == cur['node_ip']:
            continue

        t = threading.Thread(target=tcpdump_status_c, args=(ip,))
        t.start()
        threads.append(t)

    for t in threads:
        t.join()

    ret.sort(lambda x,y: cmp(x['node_id'],y['node_id']))
    return ret


@django_login_required
@check_permission('Labs', 'write')
def labs_capture_status(request):
    if request.method == 'GET':
        return HttpResponse(json.dumps(_get_all_tcpdump_status(), encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def _get_tcpdump_status():
    conf = ConfDb()

    tcpdump_status = {
        'node_id': conf.get_value('_private/node_id'),
        'node_ip': conf.get_value('_private/os/network/admin/ip'),
        'file_size': 0,
        'stderr': '',
        'running': False,
    }

    try:
        tcpdump_status['file_size'] = os.path.getsize('/var/www/.cap0')
    except:
        pass

    with os.popen('pgrep -a capture.sh') as f:
        if f.read().find('start') != -1:
            tcpdump_status['running'] = True

    if not tcpdump_status['running']:
        try:
            tcpdump_status['stderr'] = open('/run/.tcpcaperr').read()
        except:
            pass

    return tcpdump_status


# NO @login_required
def labs_capture_status_c(request):
    return HttpResponse(json.dumps(_get_tcpdump_status(), encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Labs', 'write')
def labs_start_capture(request):
    conf = WebconsoleConf()

    if request.method == 'POST':
        statvfs = os.statvfs('/var/www')
        avail_space = statvfs[0] * statvfs[4] # f_bsize * f_bavail
        if avail_space < 300 * 1024 * 1024:
            result = {'error': _("Can't launch sniffer because of insufficient system space")}
            return HttpResponse(json.dumps(result, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        enable_client_2_sphinx = request.POST.get('enable_client_2_sphinx') == 'true'
        client_ip = request.POST.get('client_ip').strip()
        sphinx_port = request.POST.get('client_port').strip()
        enable_sphinx_2_server = request.POST.get('enable_sphinx_2_server') == 'true'
        server_ip = request.POST.get('server_ip').strip()
        duration = request.POST.get('duration')
        cluster = request.POST.get('cluster') == 'true'

        try:
            duration = int(duration)
        except:
            duration = 60

        is_transparent_mode = conf.get_conf().get_value('nginx/transparent_mode/enable', False)
        admin_ip = conf.get_conf().get_value('_private/os/network/admin/ip')
        dual_link_mode = os.path.exists('/sys/class/net/br1/')

        try:
            if not (enable_client_2_sphinx or enable_sphinx_2_server):
                raise Exception(_('Client or server should be enabled first'))

            if not enable_client_2_sphinx:
                client_ip = None
                sphinx_port = None
            elif not enable_sphinx_2_server:
                server_ip = None

            if (client_ip and not valid_IP(client_ip)) \
                    or (server_ip and not valid_IP(server_ip)):
                raise Exception(_('Invalid IP'))

            if sphinx_port and (int(sphinx_port) < 1 or int(sphinx_port) > 65535):
                raise Exception(_('Invalid Port'))

            if is_transparent_mode and dual_link_mode:
                if not (enable_client_2_sphinx and enable_sphinx_2_server):
                    raise Exception(_('Client and server should be enabled, in dual link mode'))
        except Exception as e:
            result = {'error': str(e)}
            return HttpResponse(json.dumps(result, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        if client_ip and sphinx_port:
            client_param = '(src host {0} && dst port {1}) or (dst host {0} && src port {1})'.format(client_ip, sphinx_port)
        elif client_ip:
            client_param = 'host {}'.format(client_ip)
        elif sphinx_port:
            client_param = 'port {}'.format(sphinx_port)
        else:
            client_param = ''

        inf = 'any'
        params = ['not net *********/8', 'not host ::1']
        if is_transparent_mode:
            if enable_client_2_sphinx and enable_sphinx_2_server:
                params.append('not host {}'.format(admin_ip))
                if client_param and server_ip:
                    params.append('({} or host {})'.format(client_param, server_ip))
                elif client_param:
                    params.append('({} or 1==1)'.format(client_param))
                elif server_ip:
                    params.append('(1==1 or host {})'.format(server_ip))
            elif enable_client_2_sphinx:
                assert not dual_link_mode
                inf = 'eth1'
                if client_param:
                    params = [client_param]
                else:
                    params = []
            elif enable_sphinx_2_server:
                assert not dual_link_mode
                inf = 'eth2'
                if server_ip:
                    params = ['host {}'.format(server_ip)]
                else:
                    params = []
            else:
                assert False
        elif conf.get_conf().is_mirror():
            inf = 'eth1'
            params = []
            if client_ip and sphinx_port:
                client_param = '(host {0} && src port {1})'.format(client_ip, sphinx_port)
            elif client_ip:
                client_param = 'host {}'.format(client_ip)
            elif sphinx_port:
                client_param = 'port {}'.format(sphinx_port)
            else:
                client_param = ''

            tmp = []
            for port, cidr_list in get_upstream_port_map(conf.get_conf()).items():
                if len(cidr_list) == 0:
                    tmp.append('tcp port {}'.format(port))
                else:
                    dst_net = ' || '.join(['src net {}'.format(i) for i in cidr_list])
                    dst_net = dst_net.replace(' :', ' 0:') # fix net ::/0 -> net 0::/0, otherwise illegal token: ::/ for libcap
                    tmp.append('(tcp src port {0} && ({1}))'.format(port, dst_net))

            ups = ' or '.join(tmp)
            if server_ip:
                if ups:
                    ups = '(host {}) and ({})'.format(server_ip, ups)
                else:
                    ups = '(host {})'.format(server_ip)

            clt = ups.replace('src', 'dst')
            if client_param:
                if clt:
                    clt = '{} and ({})'.format(client_param, clt)
                else:
                    clt = client_param

            if not enable_client_2_sphinx and enable_sphinx_2_server:
                if ups:
                    params.append(ups)
            elif enable_client_2_sphinx and not enable_sphinx_2_server:
                if clt:
                    params.append(clt)
            elif enable_client_2_sphinx and enable_sphinx_2_server:
                if ups and clt:
                    params.append('(({}) or ({}))'.format(clt, ups))
            else:
                assert False
        else:
            nginxConf = NginxConf()
            upstreams_ip_and_port = map(lambda x:x[:2], filter(lambda x:x[2], reduce(lambda x,y:x+y, [i['UpstreamList'] + i.get('UpstreamList_IPv4',[]) + i.get('UpstreamList_IPv6',[]) for i in nginxConf.get_all_upstreams()], [])))
            if server_ip:
                upstreams_ip_and_port = filter(lambda x:x[0]==server_ip, upstreams_ip_and_port)
            if server_ip and len(upstreams_ip_and_port) == 0:
                ups = 'host {}'.format(server_ip)
            else:
                ups = ' or '.join(['(dst host {0} && dst port {1}) or (src host {0} && src port {1})'.format(x[0],x[1]) for x in upstreams_ip_and_port if x[0]])
            if not enable_client_2_sphinx and enable_sphinx_2_server:
                if ups:
                    params.append(ups)
            elif enable_client_2_sphinx and not enable_sphinx_2_server:
                if client_param and ups:
                    params.append('({}) and not({})'.format(client_param, ups))
                elif client_param:
                    params.append('({})'.format(client_param))
                elif ups:
                    params.append('not({})'.format(ups))
            elif enable_client_2_sphinx and enable_sphinx_2_server:
                if client_param and ups:
                    params.append('({} or {})'.format(client_param, ups))
                elif client_param:
                    params.append('({} or 1==1)'.format(client_param))
                elif ups:
                    params.append('(1==1 or {})'.format(ups))
            else:
                assert False

        if params:
            full_param = '-i {} "{}"'.format(inf, ' and '.join(params))
        else:
            full_param = '-i {}'.format(inf)

        try:
            if cluster:
                args = {
                    'action': 'start',
                    'arg1': str(duration),
                    'arg2': full_param.replace('"','')
                }
                service_mgr_set_command('tcpdump', args, 'exclude_me', sync=False)

            exe("sudo /usr/sbin/capture.sh start {} '{}' >/dev/null 2>&1 &".format(duration, full_param.replace('"','')))
            time.sleep(0.5)
        except Exception as e:
            result = {'error': str(e)}
            return HttpResponse(json.dumps(result, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        result = _get_tcpdump_status()
        result['cmdline'] = 'tcpdump ' + full_param

        if not result['running']:
            result['error'] = 'Failed to start capture<br><br>tcpdump ' + full_param + '<br><br>' + result['stderr']

        return HttpResponse(json.dumps(result, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Labs', 'write')
@allow_method('post')
def labs_stop_capture(request):
    if request.POST.get('cluster') == 'true':
        param = {
            'action': 'stop'
        }
        service_mgr_set_command('tcpdump', param, 'exclude_me', sync=False)

    exe("sudo /usr/sbin/capture.sh stop")
    return HttpResponse(json.dumps(_get_tcpdump_status(), encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Labs', 'read')
def labs_download_capture(request):
    conf = WebconsoleConf()
    my_node_id = conf.get_value('_private/node_id', is_abs=True)
    target_node_id = request.GET.get('node_id', my_node_id)
    if target_node_id == my_node_id:
        return labs_download_capture_c(request)

    ip = get_cluster_node_list().get(target_node_id, {}).get('_admin_ip')
    if ip:
        port = conf.get_value('nginx/web_console/port', is_abs=True)
        try:
            session = new_session_with_sign('tcpdump_api', conf.get_value("cluster/key", '', is_abs=True))
            s = session.get('https://{}:{}/system/labs/download_capture_c/'.format(ip, port), stream=True, verify=False, timeout=5)
            if s.ok:
                response = StreamingHttpResponse(s.iter_content(chunk_size=8192))
                response['Content-Type'] = s.headers.get('Content-Type', '')
                response['Content-Disposition'] = s.headers.get('Content-Disposition', '')
                return response
        except:
            logging.exception('download capture file error')

    return HttpResponse(status=404)


# NO @login_required
def labs_download_capture_c(request):
    if not os.path.exists('/var/www/.cap0'):
        return HttpResponse(status=404)

    t = os.stat('/var/www/.cap0').st_mtime
    t = time.strftime("%Y-%m-%d_%H.%M.%S", time.localtime(t))

    conf = WebconsoleConf()
    output_file_name = '{}_{}.pcap'.format(conf.get_value('_private/os/network/admin/ip', is_abs=True),t)
    response = StreamingHttpResponse(file_iterator('/var/www/.cap0'))
    response['Content-Type'] = 'application/octet-stream'
    response['Content-Disposition'] = 'attachment;filename={0}'.format(output_file_name)
    return response


@login_required
def bot_check(request):
    nc = NginxConf()
    config = nc.get_bot_check()
    if not config:
        config = get_default_bot_check_config()

    try:
        if request.method == 'POST':
            data = json.loads(request.body)
            value = {'category': {}}
            modify = ''

            if isinstance(data, dict) and 'enable' in data:
                value['enable'] = data['enable']
                if config['enable'] != value['enable']:
                    modify += 'enable' + ': OLD{' + json.dumps(config['enable']) + '} ---> NEW{' + json.dumps(value['enable']) + '}\n'
            else:
                return json_fail_response('Invalid Content!')

            if 'category' in data:
                for item in data['category']:
                    if isinstance(item, dict) and 'name' in item:
                        category_name = item['name']
                    else:
                        return json_fail_response('Invalid Content!')

                    if category_name in config['category']:
                        if 'is_good_bot' in item:
                            if item['is_good_bot']:
                                policy = 1
                            else:
                                policy = 2
                        else:
                            return json_fail_response('Invalid Content!')

                        value['category'][category_name] = policy
                        if config['category'][category_name] != policy:
                            modify += category_name + ': OLD{' + json.dumps(config['category'][category_name]) + '} ---> NEW{' + json.dumps(policy) + '}\n'
                    else:
                        return json_fail_response('Invalid Content!')

                nc.set_bot_check(value)

                detail = {'msg': ugettext_noop('Modify Bot Check Settings')}
                if modify:
                    detail['info'] = modify

                operation_log(request, ugettext_noop('Bot_Check'), ugettext_noop("Modify"), '0',
                              detail, user=request.POST.get('username'))
                return json_ok_response('Set successfully.')

            else:
                return json_fail_response('Invalid Content!')

        else:
            category = request.GET.get("key")
            if category:  # get bot list of someone category
                if category in config['category']:
                    fn = get_release_file('ua_patterns.yaml')
                    with open(fn, 'r') as f:
                        ua_patterns = yaml.load(f)
                        array = ua_patterns['user_agent_parsers']
                        bot_list = []
                        for item in array:  # Note : some item have no bot attribute
                            if 'bot_policy' in item and category == item['bot_category']:
                                if 'family_replacement' in item:
                                    bot_list.append(item['family_replacement'])

                    if len(bot_list) > 0:
                        return json_ok_response(json.dumps(bot_list, encoding='utf-8'))
                    else:
                        return json_ok_response('No bot list')

                else:
                    return json_fail_response('Invalid Key!')

            else:  # get bot category info
                value = {'enable': config['enable'], 'category': []}
                counter = {}

                fn = get_release_file('ua_patterns.yaml')
                with open(fn, 'r') as f:
                    ua_patterns = yaml.load(f)
                    array = ua_patterns['user_agent_parsers']
                    for item in array:
                        if 'bot_policy' in item:
                            category = item['bot_category']
                            if category in counter:
                                counter[category] += 1
                            else:
                                counter[category] = 1

                names = config['category'].keys()
                for name in names:
                    item = {'name': name}
                    policy = config['category'][name]
                    if policy == 1:
                        item['is_good_bot'] = True
                    else:
                        item['is_good_bot'] = False
                    item['number'] = counter[name]
                    value['category'].append(item)

                return json_ok_response(json.dumps(value, encoding='utf-8'))

    except:
        return json_fail_response('Internal Error')

@login_required
@allow_method('post')
@check_permission('Expert_Mode', 'write')
def enable_expert_mode(request):
    """
    Expert Mode Switch
    :param request: POST['expert_mode'] == 'true' or 'false'
    :return: json response
    """

    webConsoleConf = WebconsoleConf()

    enable = request.POST.get('expert_mode').lower() == 'true'
    op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
    op_msg = ugettext_noop('Enable expert mode.') if enable else ugettext_noop('Disable expert mode.')

    if webConsoleConf.set_expert_mode(request.user.username, enable):
        logger.error('Set expert mode user to config error')
        operation_log(request, ugettext_noop('Access'), op_action, '1', {'msg': op_msg})
        return json_fail_response('Enabled failed!')

    operation_log(request, ugettext_noop('Access'), op_action, '0', {'msg': op_msg})
    return json_ok_response('Enabled successfully.')


@login_required
@check_permission('Protected_Websites', 'read')
@allow_method('post')
def change_upstream_tab_view(request):
    """Change Upstream List Tab View
    :param request: { tab_view: list | card }
    :return: json_response
    """

    webConsoleConf = WebconsoleConf()
    tab_view = request.POST.get('table_view', 'card').lower()
    if webConsoleConf.set_tab_view(request.user.username, tab_view):
        logger.error('Set tabview to config error')
        return json_fail_response('Set failed!')
    return json_ok_response('Set successfully.')


@login_required
@check_permission('Global_Settings', 'write')
@require_http_methods(['POST'])
def good_bot_settings(request):
    nginxConf = NginxConf()
    goodbotConf = nginxConf.get_value('good_bot', {})

    try:
        postData = json.loads(request.body)
        # 1. 升级周期必须是[1,30]之间的整数
        upgradePeriod = int(postData.get('upgrade_period'))
        if upgradePeriod < 1 or upgradePeriod > 30:
            raise Exception("invalid upgrade period")
        # 2. 日期必须是 "01:00" 这种格式
        upgradeTime = postData.get('upgrade_time', '')
        datetime.datetime.strptime(upgradeTime, '%H:%M')
        # 3. ua 不能为空，且长度不超过256，最多10条
        uaList = postData.get('custom_ua', {}).get('ua_list', [])
        if len(uaList) > 10:
            logger.warning('[goodbot] exceed max custom ua limit')
            raise Exception('invalid data')
        uaSet = set(uaList)
        if len(uaSet) != len(uaList):
            raise Exception("Contains the same configuration items")

        for ua in uaList:
            uaLen = len(ua.strip())
            if uaLen == 0 or uaLen > 256:
                raise Exception("invalid ua")

        if not postData.get('custom_ua', {}).get('enable', False):
            postData['custom_ua']['ua_list'] = goodbotConf.get('custom_ua', {}).get('ua_list', [])
    except Exception as e:
        logger.error('Goodbots configuration data is invalid.\n{}'.format(e))
        return JsonResponse({'saveSuccess': False, 'errorMsg': ''})

    goodbotConf.pop('setting_time', '')
    oldConfig = json.dumps(goodbotConf)
    newConfig = json.dumps(postData)
    goodbotConf.update(postData)

    # 界面上关闭了在线升级，如果之前有告警文件，应该删除
    if not goodbotConf.get('enable_online_upgrade'):
        GoodBotUpgrade(nginxConf.get_conf()).delete_alarm_file()

    # 配置保存时间，作为在线更新情报库的初始时间
    goodbotConf['setting_time'] = datetime.datetime.now().strftime('%Y-%m-%d %H:%M')

    result = nginxConf.set_value('good_bot', goodbotConf)
    if result == 0:
        result = True
        detail = {'msg': ugettext_noop('Modify Policy on Good Bots')}
        if oldConfig != newConfig:
            detail['info'] = 'good_bot: OLD {' + oldConfig + '} ---> NEW {' + newConfig + '}\n'
        operation_log(request, ugettext_noop('good_bots'),
                        ugettext_noop("Modify"), '0', detail,
                        user=request.POST.get('username'))
    else:
        result = False
        logger.error(ugettext_noop("Save goodbot configuration failed."))

    result = json.dumps({
        'saveSuccess': result,
        'errorMsg': ''
    })

    return HttpResponse(result, content_type='application/json')

@login_required
@check_permission('Global_Settings', 'write')
@require_http_methods(['POST'])
def upload_good_bot_file(request):
    uploadFile = request.FILES['upload_good_bots']
    SIZE_5M = 5 * 1024 * 1024
    if uploadFile.size > SIZE_5M:
        return JsonResponse({'success': False, 'msg': _('File size exceed 5M.')})

    try:
        content = rsa_sign._aes_decrypt(get_asp_aes_key(), uploadFile.read())
    except Exception as e:
        return JsonResponse({'success': False, 'msg': _('Invalid upload file.')})

    tmpFie = NamedTemporaryFile()
    tmpFie.write(content)

    with zipfile.ZipFile(tmpFie, 'r') as zipf:
        # 必要的校验
        if zipf.testzip() != None:
            return JsonResponse({'success': False, 'msg': _('Invalid upload file.')})

        filesIncluded = zipf.namelist()
        isValidUploadFile = len(filesIncluded) == 2 and \
                            'version' in filesIncluded and zipf.getinfo('version').file_size < 32 and \
                            'good_bots' in filesIncluded and zipf.getinfo('good_bots').file_size < SIZE_5M
        if not isValidUploadFile:
            return JsonResponse({'success': False, 'msg': _('Invalid upload file.')})

        # 提取版本文件
        version = zipf.open('version', 'r').read().strip()
        if not re.match(r'[0-9]{8}\.[0-9a-z]{8}', version):
            return JsonResponse({'success': False, 'msg': _('Invalid static goodbot version.')})

        # 文件checksum校验 & 名字提取
        md5 = hashlib.md5()
        curStaticBotNames = set()
        with zipf.open('good_bots', 'r') as f:
            for line in f:
                md5.update(line)
                curStaticBotNames.add(line.split(',')[1].strip())
            cksum = md5.hexdigest()[:8]

        if cksum != version[-8:]:
            return JsonResponse({'success': False, 'msg': _('Invalid upload file.')})


        nginxConf = NginxConf()
        goodbotConf = nginxConf.get_value('good_bot', {})
        prevStaticBotNames, dynamicBotNames = get_all_good_bot_names()
        searchEngineConf = goodbotConf.get('search_engines', {})
        for name in (curStaticBotNames - prevStaticBotNames):
            # 新增静态bot，如果当前bot在动态中存在，需要继承动态配置
            botConf = searchEngineConf.get(name, [True, False])
            searchEngineConf[name] = botConf

        for name in (prevStaticBotNames - curStaticBotNames):
            if name not in dynamicBotNames:
                # 删除bot
                searchEngineConf.pop(name, '')
            else:
                # 动态中还存在，应该保留配置
                pass

        goodbotConf['search_engines'] = searchEngineConf
        goodbotConf['version'] = version

        nginxConf.set_value('good_bot', goodbotConf)

    # 同步到其他节点
    fullpath = get_upgrade_lib_path(get_sync_file_path("goodbots.zip"))
    tmpFie.flush()   # 确保写入磁盘
    shutil.copyfile(tmpFie.name, fullpath)

    ret = BaseConf().sync_file(fullpath)
    if ret != RESULT.OK:
        return JsonResponse({'success': False, 'msg': _('Fail to sync file in the cluster')})

    operation_log(request, ugettext_noop('good_bots'), ugettext_noop('Upload'), '0',
                    {'msg': ugettext_noop('Upload good_bots file')})
    return JsonResponse({'success': True, 'version': version})

@login_required
@check_permission('Global_Settings', 'read')
@require_http_methods(['GET'])
def check_dns(request):
    if not ConfDb().get_value('os/network/external/dns_list'):
        return JsonResponse({'success': False, 'message': _('No DNS Server, DNS search is not work.')})
    else:
        ret, __, __ = exe_with_output('host -W 1 update.riversecurity.com')
        if ret != 0:
            return JsonResponse({'success': False, 'message': _('DNS Server is unreachable, DNS search is not work.')})
        else:
            return JsonResponse({'success': True, 'message': ''})

@login_required
@check_permission('Global_Settings', 'read')
@require_http_methods(['GET'])
def check_upgrade_service(request):
    ret = GoodBotUpgrade(ConfDb()).test_request_update()
    return JsonResponse({'success': True if ret else False})

@login_required
@check_permission('Global_Settings', 'write')
def enable_keep_src_ip(request):
    conf_db = ConfDb()
    if conf_db.get_deploy_mode() == ConfDb.DEPLOY_MODE_MIRROR or conf_db.get_deploy_mode() == ConfDb.DEPLOY_MODE_TRANSPARENT or in_container():
        return HttpResponse(status=403)
    if request.method == 'POST':
        enable_keep_src_ip = request.POST.get('enable_keep_src_ip') == 'true'
        nginxConf = NginxConf()
        result, output, error_msg = nginxConf.set_keep_src_ip_switch(enable_keep_src_ip)
        if result == 0:
            result = True
            operation_log(request, ugettext_noop('Global_Settings'), ugettext_noop("Open") if enable_keep_src_ip else ugettext_noop("Close"), '0',
                          {'msg': ugettext_noop('Enable keep source IP') if enable_keep_src_ip else ugettext_noop('Disable keep source IP')},
                          user=request.POST.get('username'))
        else:
            result = False
            logger.error("Save enable_keep_src_ip {} failed.".format(enable_keep_src_ip))

        result = json.dumps({
            'saveSuccess': result,
            'errorMsg': error_msg
        })

        return HttpResponse(result, content_type='application/json')


@login_required
@check_permission('Global_Settings', 'write')
def enable_admin_listen(request):
    if request.method == 'POST':
        enable_admin_listen = request.POST.get('enable_admin_listen') == 'true'
        nginxConf = NginxConf()
        result, output, error_msg = nginxConf.set_admin_listen_switch(enable_admin_listen)
        if result == 0:
            result = True
        else:
            result = False
            logger.error("Save enable_admin_listen {} failed.".format(enable_admin_listen))

        if enable_admin_listen:
            msg = ugettext_noop('Enabled allow access to protected sites through management network adapter')
        else:
            msg = ugettext_noop('Disabled allow access to protected sites through management network adapter')
        operation_log(request, ugettext_noop('Proxy'), ugettext_noop('Modify'), '0', {'msg': msg})

        result = json.dumps({
            'saveSuccess': result,
            'errorMsg': error_msg
        })

        return HttpResponse(result, content_type='application/json')


@login_required
@check_permission('Developer_Mode', 'write')
def save_check_tool(request):
    ignore_bot_tbl = {
        'enable_web_inspect':'webinspect',
        'enable_ns_focus':'NSFocus',
        'enable_web_driver_ff':'webdriver_firefox',
        'enable_web_driver_ie': 'webdriver_ie',
        'enable_uft_qtp': 'uft_qtp',
        'enable_fingerprint_browser': 'fingerprint_browser'
    }
    ignore_bot = []
    for k,v in ignore_bot_tbl.items():
        if request.POST.get(k) == 'false':
            ignore_bot.append(v)

    result, output, error_msg = BaseConf().set_asp_conf_values({'nginx/ignore_bot': ignore_bot})
    if result == 0:
        result = True
    else:
        result = False
        logger.error("Save ignore bot {} failed.".format(ignore_bot))

    result = json.dumps({
        'saveSuccess': result,
        'errorMsg': error_msg
    })

    return HttpResponse(result, content_type='application/json')


@login_required
@check_permission('Developer_Mode', 'write')
def clean_phoenix_reports(request):
    exe_call_asp_ctrl(['clean_phoenix_define_reports'])
    return HttpResponse(True, content_type='application/json')


@login_required
@check_permission('Developer_Mode', 'write')
def phoenix_secret(request):
    service_mgr_set_asp_config({'phoenix/license_secret': int(time.time())}, 'IP', 'User')
    exe_call_asp_ctrl(['clean_phoenix_license_info'])
    return HttpResponse(True, content_type='application/json')

@login_required
@check_permission('Developer_Mode', 'write')
def enable_phoenix_secret(request):
    action = request.POST.get('action')
    if action == 'enable':
        enabled = 1 if int(request.POST.get('enabled')) else 0
        try:
            service_mgr_set_asp_config({'phoenix/enable_license_secret': enabled}, 'IP', 'User')
        except Exception as e:
            error = e.message

    return HttpResponse()


def get_valid_third_part_filenames():
    base_path = '/var/www/'
    third_part_names = [
        'putty.exe',
        'winscp.exe',
        'curl.exe',
        'wireshark.exe',
        'Chrome.exe',
        'websocat.exe',
        'tmp.tar',
    ]
    return [name for name in third_part_names if os.path.exists(os.path.join(base_path, name))]


@login_required
@check_permission('Developer_Mode', 'read')
def download_third_party_files(request, filename):
    base_path = '/var/www/'
    if not os.path.abspath(os.path.join(base_path, filename)):
        return HttpResponse(status=403)

    valid_third_part_names = get_valid_third_part_filenames()
    if filename in valid_third_part_names:
        the_file_name = os.path.join(base_path, filename)
        response = StreamingHttpResponse(file_iterator(the_file_name))
        response['Content-Type'] = 'application/octet-stream'
        response['Content-Disposition'] = 'attachment;filename={0}'.format(filename)
        return response

    return HttpResponse(status=403)
    pass


def supported_browser(request):
    context = {
        'layout': get_layout(True)
    }
    return TemplateResponse(request, 'v2/browser_warning.html', context=context)
    pass


def download_browser(request):
    context = {
        'layout': get_layout(True),
        'build_hash_and_layout_hash': get_build_hash_and_layout_hash(),
    }
    return TemplateResponse(request, 'v2/browser_download.html', context=context)
    pass


def validate_aaa_config(aaa_config):
    enabled = aaa_config['enabled']
    if enabled is None:
        raise ValidationError(ugettext_noop('Enable/Disable info is blank.'), code='invalid')
    elif enabled != '0' and enabled != '1':
        raise ValidationError(ugettext_noop('Incorrect Enable/Disable info.'), code='invalid')

    # do not validate other configurations as we will not save them when disable aaa
    if enabled == '0':
        return

    protocol = aaa_config['protocol']
    if protocol is None:
        raise ValidationError(ugettext_noop('Server protocol is blank.'), code='invalid')
    elif protocol != 'radius' and protocol != '':
        # currently, only support radius protocol, should extend in the future
        raise ValidationError(ugettext_noop('Server protocol should be RADIUS.'), code='invalid')

    password = aaa_config['password']
    if password is None:
        raise ValidationError(ugettext_noop('Server password is blank.'), code='invalid')

    ip_1 = aaa_config['1']['ip']
    if ip_1 is None:
        raise ValidationError(ugettext_noop('IP of primary authentication server is blank.'), code='invalid')
    elif ip_1 != '':
        try:
            validate_ipv4_address(ip_1)
        except ValidationError:
            raise ValidationError(ugettext_noop('Please enter a correct IPv4 address.'), code='invalid')

    port_1 = aaa_config['1']['port']
    if port_1 is None:
        raise ValidationError(ugettext_noop('Port number of primary authentication server is blank.'), code='invalid')
    elif port_1 != '':
        try:
            validate_integer(port_1)
        except ValidationError:
            raise ValidationError(ugettext_noop('Please enter a correct port number.'), code='invalid')

    ip_2 = aaa_config['2']['ip']
    if ip_2 is None:
        raise ValidationError(ugettext_noop('IP of secondary authentication server is blank.'), code='invalid')
    elif ip_2 != '':
        try:
            validate_ipv4_address(ip_2)
        except ValidationError:
            raise ValidationError(ugettext_noop('Please enter a correct IPv4 address.'), code='invalid')

    port_2 = aaa_config['2']['port']
    if port_2 is None:
        raise ValidationError(ugettext_noop('Port number of secondary authentication server is blank.'), code='invalid')
    elif port_2 != '':
        try:
            validate_integer(port_2)
        except ValidationError:
            raise ValidationError(ugettext_noop('Please enter a correct port number.'), code='invalid')


@login_required
@check_permission('Account_Default_Configurations', 'write')
def config_aaa_server(request):

    data = {
        'result': 'ok',
        'err_msg': '',
    }
    old_aaa_config = WebconsoleConf().get_aaa_config()
    if request.method == "GET":
        data = {
            'result': 'ok',
            'aaa_config': old_aaa_config,
        }
    elif request.method == "POST":
        try:
            aaa_config = json.loads(request.body)

            # Make sure the config data from browser is valid
            validate_aaa_config(aaa_config)

            result, err_msg = WebconsoleConf().set_aaa_config(aaa_config)
            if not result:
                data = {
                    'result': 'error',
                    'err_msg': _(err_msg),
                }
                op_result = 1
            else:
                op_result = 0

            old_enabled = old_aaa_config['enabled'] != '0'
            new_enabled = aaa_config['enabled'] != '0'
            op = get_op_by_switch(old_enabled, new_enabled)
            operation_log(request, ugettext_noop('Access'), op, op_result, {
                'msg': ugettext_noop('Set AAA configuration.'),
            })
        except ValueError:
            data = {
                'result': 'error',
                'err_msg': _('Invalid JSON data for remote authentication server.'),
            }
        except ValidationError as err:
            data = {
                'result': 'error',
                'err_msg': _(err.message),
            }

    return HttpResponse(json.dumps(data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


def trans_2_binary_data(base64_str):
    pattern = r'^data:.*?;base64,'
    base64_string = re.sub(pattern, '', base64_str)
    binary_data = base64_string.decode('base64')
    return binary_data



@login_required
@check_permission('Developer_Mode', 'write')
def customize_theme(request):
    file_path_map = {
        'loginLogo': [
            '/etc/asp/release/web_admin/static/sync_file/logo.png',
            settings.STATICFILES_DIR + '/api/oem/logo.png',
            settings.STATICFILES_DIR + '/img/logo.png',
        ],
        'headerBg': [
            '/etc/asp/release/web_admin/static/sync_file/header-bg.png',
            settings.STATICFILES_DIR + '/api/images/header-bg.png',
        ],
        'mainLogo': [
            '/etc/asp/release/web_admin/static/sync_file/logo_brand.png',
            settings.STATICFILES_DIR + '/api/oem/logo_brand.png',
            settings.STATICFILES_DIR + '/img/logo_brand.png',
        ],
        'mainLogoMini': [
            '/etc/asp/release/web_admin/static/sync_file/logo_brand_mini.png',
            settings.STATICFILES_DIR + '/api/oem/logo_brand_mini.png',
            settings.STATICFILES_DIR + '/img/logo_brand_mini.png',
        ],
        'favicon': [
            '/etc/asp/release/web_admin/static/sync_file/favicon.ico',
            settings.STATICFILES_DIR + '/api/oem/favicon.ico',
            settings.STATICFILES_DIR + '/img/favicon.ico',
        ],
    }
    customize_json_file_path = '/etc/asp/release/web_admin/static/sync_file/customize.json'
    ico_base64_header = 'data:image/x-icon;base64,'
    png_base64_header = 'data:image/png;base64,'

    if request.method == 'GET':
        images_data = {}
        for k, v_list in file_path_map.items():
            for file_path in v_list:
                if os.path.exists(file_path):
                    base64_data = open(file_path, 'rb').read().encode('base64')
                    fe_usage_data = (ico_base64_header if k == 'favicon' else png_base64_header) + base64_data
                    images_data[k] = fe_usage_data
                    break

        response_data = {'productName': '', 'companyName': '', 'mode': 'default'}
        storage_json = {}
        if os.path.exists(customize_json_file_path):
            try:
                storage_json = json.load(open(customize_json_file_path))
            except:
                pass

        response_data.update(storage_json)
        response_data.update(images_data)
        return HttpResponse(json.dumps(response_data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    elif request.method == 'POST':
        post_data = json.loads(request.body)
        new_customize = {
            'companyName': post_data.get('companyName'),
            'productName': post_data.get('productName'),
            'mode': post_data.get('mode'),
        }
        new_customize = {k: v for k, v in new_customize.items() if v}
        customize_data = {}
        old_mode = 'default'
        if os.path.exists(customize_json_file_path):
            customize_data = json.load(open(customize_json_file_path))
            old_mode = customize_data.get('mode', 'default')

        mode = post_data.get('mode')
        if mode == 'default':
            # using default images, delete uploaded images
            new_customize['companyName'] = ''
            new_customize['productName'] = ''
            for k, v_list in file_path_map.items():
                filename = v_list[0].split('/')[-1]
                if os.path.exists(v_list[0]):
                    os.remove(v_list[0])
                service_mgr_syncfile_remove(filename)
        elif mode == 'custom':
            # using uploaded images:
            images = {
                'favicon': trans_2_binary_data(post_data.get('favicon', '')),
                'loginLogo': trans_2_binary_data(post_data.get('loginLogo', '')),
                'mainLogo': trans_2_binary_data(post_data.get('mainLogo', '')),
                'mainLogoMini': trans_2_binary_data(post_data.get('mainLogoMini', '')),
                'headerBg': trans_2_binary_data(post_data.get('headerBg', '')),
            }

            for k, v in images.items():
                file_path = file_path_map[k][0]
                if v:
                    if os.path.exists(file_path):
                        os.remove(file_path)
                    with open(file_path, 'wb') as f:
                        f.write(v)
                    filename = file_path.split('/')[-1]
                    service_mgr_syncfile_create(filename, hashlib.md5(open(file_path, 'rb').read()).hexdigest())
        elif mode == 'clean':   # using 1x1 transparent png
            new_customize['companyName'] = None
            new_customize['productName'] = None
            for k, v_list in file_path_map.items():
                filename_full_path = v_list[0]
                filename = filename_full_path.split('/')[-1]
                if os.path.exists(filename_full_path):
                    os.remove(filename_full_path)
                create_1pixel_transparent_png_file(filename_full_path) if filename_full_path.endswith('png') else create_1pixel_transparent_ico_file(filename_full_path)
                service_mgr_syncfile_create(filename, hashlib.md5(open(filename_full_path, 'rb').read()).hexdigest())
        else:
            pass

        customize_data.update(new_customize)

        # storage customize.json
        if os.path.exists(customize_json_file_path):
            os.remove(customize_json_file_path)
        filename = customize_json_file_path.split('/')[-1]
        if mode == 'default':
            service_mgr_syncfile_remove(filename)
        else:
            with open(customize_json_file_path, 'w') as f:
                json.dump(customize_data, f)
            service_mgr_syncfile_create(filename, hashlib.md5(open(customize_json_file_path, 'rb').read()).hexdigest())

        post_data['result'] = 'ok'

        operation_log(request, ugettext_noop('webconsole_custom_theme'), ugettext_noop('Modify'), '0', {
            'msg': ugettext_noop('Modify Webconsole custom theme'), 'info': "customize_theme mode from {} to {}".format(old_mode, mode)
        })
        return HttpResponse(json.dumps(post_data, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)
    else:
        return HttpResponse(status=405)


@csrf_exempt
def remote_user_login_status(request):
    ret = {'result': 'error', 'login_status': ''}

    if request.method == "GET":
        user_name = request.GET.get('user_name')
        if WebconsoleConf().get_user_auth_mode(user_name) == 'remote':
            conf = BaseConf()
            login_status = conf.get_value('_private/login/%s/status' % user_name)
            if login_status:
                ret = {'result': 'ok', 'login_status': _(login_status.encode('utf-8'))}

    return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@csrf_exempt
def is_remote_user(request):
    ret = {'result': 'no'}

    if request.method == "GET":
        user_name = request.GET.get('user_name')
        conf = WebconsoleConf()
        if conf.is_aaa_enabled() and conf.get_user_auth_mode(user_name) == 'remote':
            ret = {'result': 'yes'}

    return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

@login_required
@check_permission('Developer_Mode', 'write')
def debug_console_proxy(request):
    method = request.method
    body = request.body
    url = request.get_full_path()
    rt = None

    HEADER_CONVERT = {
        'HTTP_COOKIE': 'Cookie',
        'HTTP_ORIGIN': 'Origin',
        'HTTP_HOST': 'Host',
        'HTTP_USER_AGENT': 'User-Agent',
        'HTTP_CONNECTION': 'Connection',
        'HTTP_ACCEPT': 'Accept',
        'HTTP_ACCEPT_LANGUAGE': 'Accept-Language',
        'HTTP_ACCEPT_ENCODING': 'Accept-Encoding',
        'HTTP_X_SPLUNK_FORM_KEY': 'X-Splunk-Form-Key',
        'HTTP_X_REQUESTED_WITH': 'X-Requested-With',
        'CONTENT_TYPE': 'Content-Type'
    }

    headers = {}
    for (k, v) in HEADER_CONVERT.items():
        if request.META.get(k, None) is not None:
            headers.update({v: request.META.get(k)})
    try:
        url = url.replace('/debug_console/static/','/static/')
        conn = httplib.HTTPConnection('127.0.0.1', 20147, timeout=60)
        conn.request(method, url, body, headers)
        response = conn.getresponse()

        data = response.read()
        if (response.getheader('Content-Type')).find('text/html') != -1:
            data = data.replace('/static/','/debug_console/static/')
        rt = HttpResponse(data, status=response.status)

        response_headers = response.getheaders()
        for k,v in response_headers:
            rt[k] = v
    except Exception as e:
        rt = HttpResponse(json.dumps('debug_console_proxy err.', encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    return rt


@login_required
@check_permission('Statistics', 'read')
def phoenix_query(request):

    logging.info('Enter phoenix_query..')

    query = request.GET.get('query')
    if query is None:
        query = request.POST.get('query')

    if query is not None:
        st = request.GET.get('start')
        et = request.GET.get('end')
        if st == 'undefined':
            st = None
        if et == 'undefined':
            et = None
        query = query.replace(' ', '+')
        search_query = base64.b64decode(query)
        if not (search_query.startswith('search') or search_query.startswith("|")):
            search_query = 'search ' + search_query

        sh_ip = ConfDb().get_value('screen/sh_ip', '')
        if sh_ip == '':
            sh_ip = None

        response = phoenix_restapi_screen_request(search_query, start_time=st, end_time=et, phoenix_node=sh_ip)

        response = HttpResponse(response, content_type=CONTENT_TYPE_JSON)
        response["Access-Control-Allow-Origin"] = "*"
        return response
    else:
        response = HttpResponse(json.dumps({}), content_type=CONTENT_TYPE_JSON)
        response["Access-Control-Allow-Origin"] = "*"

        return response


@has_dm_permission(LicenseInfo.DM_BIG_SCREEN_DISPLAY)
@login_required
def screen_conf(request):

    big_screen_conf = os.path.join(get_sync_file_path(), 'big_screen_conf')
    if request.method == "POST":
        if not has_permission(request.user.username, 'Big_Screen_Display', 'write'):
            return HttpResponse(status=403)
        conf_data = request.body

        if os.path.exists(big_screen_conf) is True:
            os.remove(big_screen_conf)

        with open(big_screen_conf, 'w') as f:
            f.write(conf_data)

        if os.path.exists(big_screen_conf) is True:
            ret = BaseConf().sync_file(big_screen_conf)
            res = {'result': 0}
            if ret != RESULT.OK:
                logging.error('Failed to sync big screen conf ret = {}'.format(ret))
                res['result'] = -1
            response = HttpResponse(json.dumps(res), content_type=CONTENT_TYPE_JSON)
            response["Access-Control-Allow-Origin"] = "*"
            return response

    elif request.method == "GET":
        if not has_permission(request.user.username, 'Big_Screen_Display', 'read'):
            return HttpResponse(status=403)
        res = ""
        if os.path.exists(big_screen_conf) is True:
            with open(big_screen_conf, 'rb') as f:
                content = f.read()
                res = content
        else:
            res = -1

        response = HttpResponse(res, content_type=CONTENT_TYPE_PLAIN)
        response["Access-Control-Allow-Origin"] = "*"
        return response


@has_dm_permission(LicenseInfo.DM_BIG_SCREEN_DISPLAY)
@login_required
@check_permission('Big_Screen_Display', 'write')
def screen_editor(request):
    license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
    enabled_editor = license_info.is_module_effect(license_info.DM_BIG_SCREEN_EDITOR)
    current_is_debug = get_current_is_debug()
    response = base_render_to_response(request, 'ScreenEditor/editor.html', {})

    if current_is_debug or enabled_editor:
        return response
    else:
        code = request.COOKIES.get('adv')
        wc_conf = WebconsoleConf()
        if not code or not wc_conf.check_adv_operation_code(code):
            logger.error('Verify advanced operation code setfailed!')
            return HttpResponse(_('Verify advanced operation code failed!'), content_type='text/plain; charset=utf-8')
        return response


@has_dm_permission(LicenseInfo.DM_BIG_SCREEN_DISPLAY)
@login_required
@check_permission('Big_Screen_Display', 'read')
def screen_monitor(request):
    return base_render_to_response(request, 'ScreenEditor/monitor.html', {})


@login_required
@check_permission('Big_Screen_Display', 'write')
def screen_documentation(request):
    return base_render_to_response(request, 'ScreenEditor/documentation.html', {})


@login_required
@check_permission('Big_Screen_Display', 'write')
def screen_references(request):
    return base_render_to_response(request, 'ScreenEditor/references.html', {})


@login_required
@check_permission('Big_Screen_Display', 'write')
def screen_notSupport(request):
    return base_render_to_response(request, 'ScreenEditor/notSupport.html', {})


@has_dm_permission(LicenseInfo.DM_BIG_SCREEN_DISPLAY)
@login_required
@check_permission('Big_Screen_Display', 'read')
def screen(request):
    return base_render_to_response(request, 'v2/screen.html', {})


@login_required
@check_permission('Big_Screen_Display', 'read')
def screen_list(request):
    confdb = ConfDb()
    sh_ip = confdb.get_value('screen/sh_ip', '') or confdb.get_min_phoenix_servers() or ''
    return base_render_to_response(request, 'v2/screen_list.html', {'sh_ip': sh_ip})

@login_required
@check_permission('Big_Screen_Display', 'write')
def screen_save_sh_ip(request):
    ret = { 'save_success': False, 'error_msg': '' }
    try:
        data = json.loads(request.body)
    except:
        ret['error_msg'] = _('Invalid parameter')
        return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    sh_ip = data['sh_ip']

    if sh_ip != '':
        if not valid_IPv4(sh_ip):
            ret['error_msg'] = _('Invalid IP address')
            return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        try:
            resp = phoenix_restapi_screen_request('| makeresults', phoenix_node=sh_ip)
            json.loads(resp)
        except:
            ret['error_msg'] = _('IP is unreachable possibly because the cluster safety switch of the specified node is turned on or the port cannot be accessed.')
            return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

    service_mgr_set_asp_config({'screen/sh_ip': sh_ip}, 'IP', 'User')
    ret['save_success'] = True

    operation_log(request, ugettext_noop('Screen'), ugettext_noop('Modify'), 0,
                      {'msg': ugettext_noop('Modify Screen Search Head IP')})

    return HttpResponse(json.dumps(ret, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)


@login_required
@expert_mode_required
@check_permission('Account_Default_Configurations', 'write')
def modify_session_idle_timeout(request):
    result = False
    try:
        data = json.loads(request.body)
        session_idle_timeout = data['session_idle_timeout']
        WebconsoleConf().set_session_idle_timeout(session_idle_timeout)
        result = True
    except:
        pass
    op_code = '0' if result else '1'
    operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), op_code, {
        'msg': ugettext_noop('Modify system session expiration time.')
    })
    return JsonResponse({'result': result})


def convert_unicode_values(d):
    if isinstance(d, dict):
        return {convert_unicode_values(k): convert_unicode_values(v) for k, v in d.items()}
    elif isinstance(d, unicode):
        return d.encode('utf-8')
    else:
        return d

@login_required
@expert_mode_required
@check_permission('Account_Default_Configurations', 'write')
def modify_password_config(request):
    result = False
    err = ''
    old_password_cfg = WebconsoleConf().get_password_config()
    password_period = WebconsoleConf().get_password_expiration_time()
    old_password_cfg["period"] = password_period
    try:
        data = json.loads(request.body)
        password_config = data['password_config']
        err = WebconsoleConf().set_password_config(password_config)
        if not err:
            result = True
    except Exception as e:
        password_config = ''
    op_code = '0' if result else '1'
    operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), op_code, {
        'msg': ugettext_noop('Modify password config.'),
        'info': "{} \n-->\n {}".format(convert_unicode_values(old_password_cfg), convert_unicode_values(password_config))
    })
    return JsonResponse({'result': result, 'err_msg' : str(err)})

def pre_cfg_verify(request):
    if request.method != 'GET':
        return HttpResponse(status=403)
    password_config = WebconsoleConf().get_password_config(True)
    period = WebconsoleConf().get_password_expiration_time() or 90
    password_config["period"] = period
    message = """
            var password_config = {};
        """.format(json.dumps(password_config))
    return HttpResponse(message)




@login_required
@expert_mode_required
@check_permission('Account_Default_Configurations', 'write')
def modify_login_failure(request):
    result = False
    try:
        data = json.loads(request.body)
        login_failed_period = data['login_failed_period']
        login_failed_counts = data['login_failed_counts']
        lock_period = data['lock_period']
        if (login_failed_period and not (1 <= int(login_failed_period) <= 60)) or \
            (login_failed_counts and not (2 <= int(login_failed_counts) <= 10)) or \
            (lock_period and not (1 <= int(lock_period) <= 3000)):
            return JsonResponse({'result': result})
        result = WebconsoleConf().set_login_failure_lock(login_failed_period, login_failed_counts, lock_period)
    except:
        pass
    if result:
        login_failed_period_default, login_failed_counts_default, lock_period_default = WebconsoleConf().get_login_failure_lock_default()
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '0', {
            'msg': ugettext_noop('Login failure lock configuration is set successfully: '
                                 'login failed period is set to {login_failed_period}, '
                                 'login failed count is set to {login_failed_counts}, '
                                 'lock period is set to {lock_period}'),
            'extra': {'login_failed_period': login_failed_period or  str(login_failed_period_default),
                      'login_failed_counts': login_failed_counts or str(login_failed_counts_default),
                      'lock_period': lock_period or str(lock_period_default)
                      }
        })
    else:
        operation_log(request, ugettext_noop('Access'), ugettext_noop('Modify'), '1', {
            'msg': ugettext_noop('Login failure lock configuration setting failed')
        })

    return JsonResponse({'result': result})


@login_required
@expert_mode_required
def webconsole_ai_check(request):
    if request.method == 'POST':
        if not has_permission(request.user.username, 'Labs', 'write'):
            return HttpResponse(status=403)
        enable_ai_ua = True if request.POST.get('ai_ua') == 'true' else False
        code, output, cmd_line = service_mgr_set_asp_config({'nginx/enable_ai_ua': enable_ai_ua},
                                                            'IP', 'User', sync=True)
        if code == 0:
            save_f = {'result': True}
        else:
            save_f = {
                'result': False,
                'error_msg': _('Failed to save upstream configuration')
            }
        op_code = '0' if code == 0 else '1'
        if enable_ai_ua:
            op_action = ugettext_noop('Enable')
            op_msg = ugettext_noop('Enable AI detection - AI-UA module.')
        else:
            op_action = ugettext_noop('Disable')
            op_msg = ugettext_noop('Disable AI detection - AI-UA module.')
        operation_log(request, ugettext_noop('Labs'), op_action, op_code, {
            'msg': op_msg,
        })

        return HttpResponse(json.dumps(save_f, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)
    else:
        if not has_permission(request.user.username, 'Labs', 'read'):
            return HttpResponse(status=403)
        enable_ai_ua = ConfDb().get_value('nginx/enable_ai_ua', False)
        return base_render_to_response(request, 'v2/labs/ai_check.html', {
            'modules': {'ua_enable': enable_ai_ua}
        })


@login_required
@expert_mode_required
@check_permission('Labs', 'read')
def access_mode_detect(request):
    if request.method == 'POST':
        return
    else:
        return base_render_to_response(request, 'v2/labs/access_mode_detect.html', {})

@login_required
@expert_mode_required
@check_permission('Labs', 'write')
@require_http_methods(['GET', 'POST'])
def proxy_detection(request):
    nginxConf = NginxConf()
    proxy_detection = nginxConf.get_conf().get_values('nginx/proxy_detection', {})

    if request.method == 'POST':
        request_body = json.loads(request.body)
        # 先统计出本次修改的所有开关值，最后通过set_asp_conf_values一次性保存
        values = {}
        burp = request_body.get('burp')
        if burp != proxy_detection.get('burp'):
            values.update({'nginx/proxy_detection/burp': burp})

        if values:
            code, output, cmd_line = nginxConf.set_asp_conf_values(values)
            if code != 0:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop("Modify"), 0, {'msg': ugettext_noop('Failed to modify browser proxy detection')})
                return JsonResponse({'save_success': False})
            else:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop("Modify"), 0, {'msg': ugettext_noop('Success to modify browser proxy detection')})

        return JsonResponse({'save_success': True})

    return base_render_to_response(request, 'v2/labs/browser_proxy_detection.html', {'proxy_detection': proxy_detection})

@login_required
def labs_switch_web_filter(request):
    username = request.user.username
    if request.method == 'POST':
        if not has_permission(username, 'Labs', 'write'):
            return HttpResponse(status=403)
        webConsoleConf = WebconsoleConf()

        enable = request.POST.get('enable').lower() == 'true'
        op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
        op_msg = ugettext_noop('Enable web filter.') if enable else ugettext_noop('Disable web filter.')

        def _record(r):
            operation_log(request, ugettext_noop('Labs'), op_action, r, {
                'msg': op_msg,
            })

        if webConsoleConf.set_web_filter_enable(username, enable):
            logger.error('Set expert mode user to config error')
            _record('1')
            return json_fail_response('Enabled failed!')

        if enable is False:
            webConsoleConf.set_value('nginx/web_filter/enabled', False, is_abs=True)

        _record('0')
        return json_ok_response('Enabled successfully.')

    if not has_permission(username, 'Labs', 'read'):
        return HttpResponse(status=403)
    return base_render_to_response(request, 'v2/labs/switch_web_filter.html')


@login_required
@check_permission('Developer_Mode', 'write')
def enable_cmdline_page(request):
    if not is_dev_mode_enable():
        return HttpResponse(status=403)

    data = json.loads(request.body)
    enable = 1 if data.get('cmdline_enable') else 0
    code = WebconsoleConf().set_cmdline_enable(enable)
    result = 'ok' if code == 0 else 'failed'
    obj = json.dumps({ 'result': result })
    return HttpResponse(obj, content_type='application/json')


@login_required
@check_permission('Statistics', 'read')
def statistics_entry(request):
    return base_render_to_response(request, 'report/statistics.html')


@login_required
@check_permission('Statistics', 'read')
def risk_report_entry(request):
    return base_render_to_response(request, 'report/statistics_risk.html')


@login_required
@check_permission('Big_Screen_Display', 'read')
def sailfish_screen_entry(request):
    license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
    if license_info.is_module_effect(license_info.DM_BIG_SCREEN_DISPLAY) and (
            license_info.is_protected_on() or get_current_is_debug()):
        return base_render_to_response(request, 'securityPosture/index.html')
    else:
        return HttpResponse(status=403)

@login_required
@check_permission('Big_Screen_Display', 'read')
def sailfish_screen_redirect(request):
    license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
    if license_info.is_module_effect(license_info.DM_BIG_SCREEN_DISPLAY) and (
            license_info.is_protected_on() or get_current_is_debug()):
        id = request.GET.get('id')
        t = request.GET.get('t')
        mt = request.GET.get('mt')
        params = dict()
        if t:
            params['t'] = t
        if mt:
            params['mt'] = mt
        return HttpResponseRedirect("/static/securityPosture/screens/%s.html?%s" % (id, urllib.urlencode(params)))
    else:
        return HttpResponse(status=403)

@login_required
@expert_mode_required
@check_permission('Labs', 'read')
def network_access_policy_control(request):

    def checkList(li):
        if type(li) is not list:
            return False

        if li:
            if len(li) > MAX_NETWORK_ACCESS_CONTROL_RULES_COUNT:
                return False
            elif len(li) > 0:
                uniqueArr = []
                for item in li:
                    uniqueKey = 'protocol' + item[1] + 'sip' + item[2] + 'sport' + item[3] + 'dip' + item[4] + 'dport' + item[5]
                    if uniqueKey in uniqueArr:
                        return False
                    else:
                        uniqueArr.append(uniqueKey)
        return True

    if request.method == "POST":

        if not has_permission(request.user.username, 'Labs', 'write') or BaseConf().get_value('_private/os/network/bridge_transparent', False):
            return HttpResponse(status=403)

        cmdline = 0
        error_code = 0
        error_msg  = ""
        error_log = ""

        try:
            rules_list = json.loads(request.POST.get('network_access_block_rules'))
        except Exception as e:
            error_log = ugettext_noop('Failed to set network layer access control rules.')
            return HttpResponse(json.dumps(error_log, encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

        if checkList(rules_list):
            error_code, error_msg, cmdline = BaseConf().set_asp_conf_values([('os/network/external/network_access_block_rules', rules_list)])
            if error_code >= 0:
                error_log = ugettext_noop('Set network layer access control rules successfully.')
            else:
                error_log = ugettext_noop('Failed to set network layer access control rules.')
        else:
            # The number of entries is greater than 50 or there are duplicate configuration items.
            error_code = -1
            error_msg  = _("Failed")
            error_log = ugettext_noop('The input parameters of the network layer access control rule are incorrect.')

        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), error_code, {
            'msg': error_log
            })

        result = error_code and 1

        return HttpResponse(json.dumps({"result": result, "error_code": error_code, "error_msg": error_msg, "error_code_line": cmdline}),
                     content_type=CONTENT_TYPE_JSON)

    elif request.method == "GET":

        if not has_permission(request.user.username, 'Labs', 'read') or BaseConf().get_value('_private/os/network/bridge_transparent', False):
            return HttpResponse(status=403)

        network_access_block_rules = BaseConf().get_value('os/network/external/network_access_block_rules',[])

        if request.GET.get('tab') == 'block_statistics':
            # get drop statistics
            arrCounts = []
            try:
                with open('/proc/sys/bypass/block_statistic') as statistics:
                    arrCounts = statistics.read().strip('\n').split('\t')
            except Exception as e:
                return HttpResponse(json.dumps('get block statistics err.', encoding='utf-8'), content_type=CONTENT_TYPE_JSON)

            return HttpResponse(json.dumps({
                                       'network_access_block_rules': network_access_block_rules,
                                       'network_access_block_statistics': arrCounts
                                }), content_type='application/json')
        else: # get conf
            return base_render_to_response(request, 'v2/labs/network_access_policy_control.html',
                                        {
                                            'network_access_block_rules': network_access_block_rules,
                                            'network_access_block_statistics': []
                                        })
    else:
        return HttpResponse(status=403)


def labs_performance_parameters_optimization_handle(request):
        enable = request.POST.get('enable').lower() == 'true'
        perf_params_opt_worker_connections = request.POST.get('perf_params_opt_worker_connections', None)
        op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
        op_msg = ugettext_noop('System parameter settings: On') if enable else ugettext_noop('System parameter settings: Off')

        # 性能均衡参数设置的 worker_connections 配置范围为[50000, 700000]，原因见 DAP-30348
        if (enable and
            (perf_params_opt_worker_connections is None or
             not perf_params_opt_worker_connections.isdigit() or
             int(perf_params_opt_worker_connections) < 50000 or
             int(perf_params_opt_worker_connections) > 700000)):
            return json_fail_response(_('Please enter a integral number greater than or equal to {0} and less than or equal to {1}').format(50000, 700000))

        def _record(r):
            operation_log(request, ugettext_noop('Labs'), op_action, r, {
                'msg': op_msg,
            })

        values = [('os/performance_parameters_optimization/enable', enable)]
        if enable:
            values.append(('os/performance_parameters_optimization/worker_connections', int(perf_params_opt_worker_connections)))

        code, output, cmdline = BaseConf().set_asp_conf_values(values)
        if code != 0:
            _record('1')
            logger.error('Failed to set value. code: {0}, cmd: {1}, Output: >>>>>>\n{2}<<<<<<'.format(code, cmdline, output))
            return json_fail_response('Enabled failed!')

        _record('0')
        return json_ok_response('Enabled successfully.')


@adv_operation_code_required
def labs_processes_optimization_handle(request):
    enable = request.POST.get('processes_optimization_enable').lower() == 'true'
    module_list = json.loads(request.POST.get('processes_optimization_module_list', '[]'))
    data = {'enable': enable, 'module_list': module_list}
    op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
    op_msg = ugettext_noop('Business process settings: On') if enable else ugettext_noop('Business process settings: Off')

    def _record(r):
        operation_log(request, ugettext_noop('Labs'), op_action, r, {
            'msg': op_msg,
        })

    code, output, cmdline = BaseConf().set_asp_conf_values([('os/processes_optimization', data)])
    if code != 0:
        _record('1')
        logger.error('Failed to set value. code: {0}, cmd: {1}, Output: >>>>>>\n{2}<<<<<<'.format(code, cmdline, output))
        return json_fail_response('Enabled failed!')

    _record('0')
    return json_ok_response('Enabled successfully.')


def labs_performance_optimization_dispatch(request):
    if request.POST.get('enable', None) is not None:
        return labs_performance_parameters_optimization_handle(request)
    elif request.POST.get('processes_optimization_enable', None) is not None:
        return labs_processes_optimization_handle(request)
    else:
        return HttpResponse(status=403)


@login_required
@expert_mode_required
def labs_performance_parameters_optimization(request):

    if ConfDb().is_mirror():
        return HttpResponse(status=403)

    username = request.user.username
    if request.method == 'POST':
        if not has_permission(username, 'Labs', 'write'):
            return HttpResponse(status=403)
        return labs_performance_optimization_dispatch(request)

    elif request.method == 'GET':
        if not has_permission(username, 'Labs', 'read'):
            return HttpResponse(status=403)

        performance_parameters_optimization = BaseConf().get_value('os/performance_parameters_optimization/enable',False)
        perf_params_opt_worker_connections = BaseConf().get_value('os/performance_parameters_optimization/worker_connections', DEFAULT_PERF_PARAMS_OPT_WORKER_CONNECTIONS)
        processes_optimization_config = BaseConf().get_value('os/processes_optimization', {"enable": False, "module_list": []})
        processes_optimization = processes_optimization_config['enable']
        processes_optimization_module_list = processes_optimization_config['module_list']
        license_info = ConfDb().get_license_info(i18n_support=False,care_cluster=False)
        has_coreweb = license_info.get_license_protected_level() in ('BE', 'AE', 'LE')
        return base_render_to_response(request, 'v2/labs/performance_parameters_optimization.html',
                                        {
                                            'performance_parameters_optimization': performance_parameters_optimization,
                                            'perf_params_opt_worker_connections': perf_params_opt_worker_connections,
                                            'processes_optimization': processes_optimization,
                                            'processes_optimization_module_list': processes_optimization_module_list,
                                            "has_waf": license_info.is_module_effect(LicenseInfo.DM_ADVANCED_WAF),
                                            "has_ubb": license_info.is_module_effect(LicenseInfo.DM_PROGRAMMABLE_DEFENDING),
                                            "has_coreweb": has_coreweb
                                        })
    else:
        return HttpResponse(status=403)

@login_required
def static_bot_detection(request):
    return base_render_to_response(request, 'vue_front/static_bot_detection/static_bot_detection.html')

@login_required
@expert_mode_required
def labs_advanced_verify(request):
    username = request.user.username
    if request.method == 'POST':
        if not has_permission(username, 'Labs', 'write'):
            return HttpResponse(status=403)
        webConsoleConf = WebconsoleConf()

        enable = request.POST.get('enable').lower() == 'true'
        op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
        op_msg = ugettext_noop('Enable advanced verify.') if enable else ugettext_noop('Disable advanced verify.')

        def _record(r):
            operation_log(request, ugettext_noop('Labs'), op_action, r, {
                'msg': op_msg,
            })

        if webConsoleConf.set_value('nginx/enable_advanced_verify', enable, is_abs=True):
            logger.error('Set advanced verify config error')
            _record('1')
            return json_fail_response('Enabled failed!')

        _record('0')
        return json_ok_response('Enabled successfully.')

    if not has_permission(username, 'Labs', 'read'):
        return HttpResponse(status=403)

    return base_render_to_response(request, 'v2/labs/advanced_verify.html')

@login_required
@expert_mode_required
def custom_hostname(request):
    # DAP-22175

    username = request.user.username
    if request.method == 'POST':
        if not has_permission(username, 'Labs', 'write'):
            return HttpResponse(status=403)
        conf = json.loads(request.body)

        try:
            mappingList = conf.get('mapping_list', [])
            isList = lambda x: isinstance(x, list)
            isPort = lambda x: 0 < int(x) < 65535
            isAddress = lambda x: valid_Domain(x) or valid_IPv4(x) or valid_IPv6_with_bracket(x)
            if not isList(mappingList):
                    raise Exception("invalid data")

            textLen = 0
            for item in mappingList:
                if not isList(item):
                    raise Exception("invalid data")

                textLen += len(item[0]) + len(item[1])
                if textLen > 1500:
                    logger.warning('[custom hostname] exceed max length 1500')
                    raise Exception("invalid data")

                host, _, port = item[0].rpartition(':')
                if not (isAddress(host) and isPort(port)):
                    raise Exception("invalid data")

                ip, _, port = item[1].rpartition(':')
                if not (isAddress(ip) and isPort(port)):
                    raise Exception("invalid data")

        except Exception as e:
            logger.error('[custom hostname] submit invalid data')
            return JsonResponse({'result': 'error', 'message': 'invalid data'})

        # save conf
        ngxConf = NginxConf()
        oldConf = ngxConf.get_value('custom_hostname', {})
        ngxConf.set_value('custom_hostname', conf)

        # record operator log
        detail = { 'msg': ugettext_noop('Custom hostname') }
        oldConfStr = json.dumps(oldConf)
        curConfStr = json.dumps(conf)
        if oldConfStr != curConfStr:
            detail['info'] = 'custom hostname: OLD {' + oldConfStr + '} ---> NEW {' + curConfStr + '}\n'
        operation_log(request, ugettext_noop('Labs'),
                        ugettext_noop("Modify"), '0', detail,
                        user=request.POST.get('username'))

        return JsonResponse({'result': 'success'})

    elif request.method == 'GET':
        if not has_permission(username, 'Labs', 'read'):
            return HttpResponse(status=403)

        return base_render_to_response(request, 'v2/labs/custom_hostname.html',
                                        {
                                            'custom_hostname': NginxConf().get_value('custom_hostname', {})
                                        })
    else:
        return HttpResponse(status=403)

@login_required
@expert_mode_required
@require_http_methods(['GET', 'POST'])
def tool_code_feature(request):
    username = request.user.username
    if request.method == 'POST':
        if not has_permission(username, 'Labs', 'write'):
            return HttpResponse(status=403)

        textLen = len(request.body)
        if textLen > 1600:
            logger.warning('[Tool code feature] exceed max length 1600')
            return JsonResponse({'result': 'error', 'message': 'invalid data'})

        conf = json.loads(request.body)

        # save conf
        ngxConf = NginxConf()
        oldConf = ngxConf.get_value('tool_code_feature', {})
        ngxConf.set_value('tool_code_feature', conf)

        # record operator log
        detail = { 'msg': ugettext_noop('Bot Tools Recognition: Modified Advanced Persistent Bot') }
        oldConfStr = json.dumps(oldConf)
        curConfStr = json.dumps(conf)
        if oldConfStr != curConfStr:
            detail['info'] = 'Tool code feature: OLD {' + oldConfStr + '} ---> NEW {' + curConfStr + '}\n'
        operation_log(request, ugettext_noop('Labs'),
                        ugettext_noop("Modify"), '0', detail,
                        user=request.POST.get('username'))

        return JsonResponse({'result': 'success'})


    if request.method == 'GET':
        if not has_permission(username, 'Labs', 'read'):
            return HttpResponse(status=403)
        ngxConf = NginxConf()
        return base_render_to_response(request, 'v2/labs/tool_code_feature.html',
                                        {
                                            'tool_code_feature': ngxConf.get_value('tool_code_feature', {"enable": False, "list": []}),
                                            'block_bot_tools': ngxConf.get_value('block_bot_tools', {"RuishuBypass": True, "Botgate_bypass": True})
                                        })
    else:
        return HttpResponse(status=403)


@login_required
@expert_mode_required
@require_http_methods(['POST'])
def block_bot_tools(request):
    try:
        status = json.loads(request.body).get("status", {})
    except Exception as e:
        logger.error("receive invalid data in block_bot_tools, \n{}".format(e))
        return JsonResponse({'success': False})

    ngxConf = NginxConf()
    oldStatus = ngxConf.get_value("block_bot_tools", {})
    msg = ugettext_noop("Bot Tools Recognition: Modified Block Bot Tools")
    ret = ngxConf.set_value("block_bot_tools", status)
    if ret == 0:
        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), '0', {'msg': msg, 'info': 'OLD: {} ---> NEW: {}'.format(oldStatus, status)})
        return JsonResponse({'success': True})
    else:
        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), '1', {'msg': msg})
        return JsonResponse({'success': False})


@login_required
@expert_mode_required
@require_http_methods(['GET', 'POST'])
def collect_client_features(request):
    username = request.user.username

    if request.method == 'POST':

        @adv_operation_code_required
        def _handle_post_collect_client_features(request):
            if not has_permission(username, 'Labs', 'write'):
                return HttpResponse(status=403)

            try:
                conf = json.loads(request.body)

                conf.pop('adv_operation_code', None)    # 删除验证码

                start = conf.get('start', None)
                if not start:
                    raise Exception('start cannot be empty.')

                duration = conf.get('duration', 10)
                duration = int(duration)
                if duration < 1 or duration > 60:
                    raise Exception("duration should be 1-60")

                ip_range = conf.get('ip_range', '')
                if len(ip_range) > 1500:
                    raise Exception("ip_range is too long")
                else:
                    ip_range = re.sub('\s+', '', ip_range)
                    ip_list = ip_range.split(',')
                    for ip in ip_list:
                        if ip and not valid_IP_or_CIDR(ip):
                            raise Exception('invalid ip or cidr: {}'.format(ip))

                excldues = conf.get('excldues', '')
                if len(excldues) > 500:
                    raise Exception('excldues is too long')

            except Exception as e:
                logger.error('[collect client features] submit invalid data: {}'.format(e))
                return JsonResponse({'result': 'error', 'message': 'invalid data'})

            # save conf
            ngxConf = NginxConf()
            oldConf = ngxConf.get_value('collect_client_features', {})
            ngxConf.set_value('collect_client_features', conf)

            # record operator log
            detail = { 'msg': ugettext_noop('Full Features') }
            oldConfStr = json.dumps(oldConf)
            curConfStr = json.dumps(conf)
            if oldConfStr != curConfStr:
                detail['info'] = 'Collect client features: OLD {' + oldConfStr + '} ---> NEW {' + curConfStr + '}\n'
            operation_log(
                request, ugettext_noop('Labs'),
                ugettext_noop("Modify"),
                '0',
                detail,
                user=request.POST.get('username')
            )

            return JsonResponse({'result': 'success'})

        return _handle_post_collect_client_features(request)

    elif request.method == 'GET':
        if not has_permission(username, 'Labs', 'read'):
            return HttpResponse(status=403)

        show_customized_collection =  show_customized_collection_countdown()
        customized_collection_list = NginxConf().get_value('customized_collection', []) if show_customized_collection else []

        return base_render_to_response(request, 'v2/labs/collect_client_features.html', {
            'collect_client_features': NginxConf().get_value('collect_client_features', {}),
            'customized_collection': customized_collection_list,
            'show_customized_collection': show_customized_collection,
        })
    else:
        return HttpResponse(status=403)


@login_required
@require_http_methods(['GET'])
@check_permission('Labs', 'get')
def download_client_features(request):

    if os.path.exists('/tmp/client_features/'):
        # 1. 删除历史打包文件
        os.system("rm -rf /tmp/client_features-*")

        # 2. 打包采集文件
        file_name = "client_features-{}.zip".format(time.strftime('%Y-%m-%d_%X', time.localtime(time.time())))
        path = '/tmp/' + file_name
        logger.error('zip file: ' + path)

        os.system("zip -r -P rscf {} /tmp/client_features/".format(path))

        if os.path.exists(path):
            out_put_file_name = quote(file_name)
            logger.error('out_put_file_name: ' + out_put_file_name)
            response = StreamingHttpResponse(file_iterator(path))
            response['Content-Type'] = 'application/octet-stream'
            response['Content-Disposition'] = 'attachment;filename={0}'.format(out_put_file_name)
            return response

    result = {'result': 'FAILED', 'message': 'No collection file.'}
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def show_customized_collection_countdown():
    value = WebconsoleConf().get_value('show_customized_collection/enable')
    if value == -1:
        # debug mode
        return -1

    if value is None:
        return 0

    now = int(time.time())
    if value <= now < value + 1800:   # 30分钟内显示
        return value + 1800 - now
    else:
        return 0


def set_show_customized_collection():
    wconf = WebconsoleConf()
    if wconf.get_value('_private/is_debug', is_abs=True):
        wconf.set_value('show_customized_collection/enable', -1)
    else:
        wconf.set_value('show_customized_collection/enable', int(time.time()))


@login_required
@expert_mode_required
@adv_operation_code_required
@require_http_methods(['POST'])
@check_permission('Labs', 'write')
def customized_collection(request):

    try:
        conf = json.loads(request.body)

        conf = conf.get('data', [])

        for item in conf:
            enable = item.get('enable', None)
            if enable == None:
                raise Exception('Invalid enable field: {}'.format(enable))

            name = item.get('name', '')
            if len(name) <= 0 or len(name) > 5:
                raise Exception('Invalid name len: {}'.format(len(name)))

            js = item.get('js', '')
            if len(js) <= 0 or len(js) > 200:
                raise Exception('Invalid js len: {}'.format(len(js)))

            ast = item.get('ast', '')
            try:
               base64.b64decode(ast)
            except:
                raise Exception('Invalid base64 ast.')

    except Exception as e:
        logger.error('[collect client features] submit invalid data: {}'.format(e))
        return JsonResponse({'result': 'error', 'message': 'invalid data'})

    # save conf
    ngxConf = NginxConf()
    ngxConf.set_value('customized_collection', conf)

    # record operator log
    detail = { 'msg': ugettext_noop('Specific Features') }
    operation_log(
        request, ugettext_noop('Labs'),
        ugettext_noop("Modify"),
        '0',
        detail,
        user=request.POST.get('username')
    )

    return JsonResponse({'result': 'success'})


@login_required
@expert_mode_required
@adv_operation_code_required
@require_http_methods(['POST'])
@check_permission('Labs', 'write')
def show_customized_collection(request):

    set_show_customized_collection()
    return JsonResponse({'result': 'success'})


@login_required
@expert_mode_required
def network_ddos_protect(request):
    if not is_support_current_platform():
        return HttpResponse(status=403)
    username = request.user.username
    if request.method == "GET" and has_permission(username, 'Labs', 'read'):
        return base_render_to_response(request, 'v2/labs/network_ddos_protect.html')
    elif request.method == "POST" and has_permission(username, 'Labs', 'write'):
        request_body = json.loads(request.body)
        enable = request_body.get('enable')
        if type(enable) != bool:
            return HttpResponse(status=403)
        op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
        op_msg = ugettext_noop('Enable Network Ddos Protect.') if enable else ugettext_noop('Disable Network Ddos Protect.')

        def _record(r):
            operation_log(request, ugettext_noop('Labs'), op_action, r, {
                'msg': op_msg,
            })
        web_console_conf = WebconsoleConf()
        if web_console_conf.set_value("os/network_protect/ddos/enabled", enable, is_abs=True):
            logger.error("Set Network Ddos Protect error")
            _record('1')
            return JsonResponse({'save_success': False})
        else:
            _record('0')
            return JsonResponse({'save_success': True})
    else:
        return HttpResponse(status=403)


@login_required
@expert_mode_required
def switch_api_sequence_learning(request):
    username = request.user.username
    web_console_conf = WebconsoleConf()
    sailfish_server_count = len(web_console_conf.get_conf().get_sailfish_servers())
    if sailfish_server_count <= 0:
        return HttpResponse(status=403)
    api_sequence_config = web_console_conf.get_conf().get_api_sequence_leaning_config()
    # api_sequence_learning_enable = web_console_conf.get_value("nginx/api/v2/api_sequence_leaning/enabled", False, is_abs=True)
    if request.method == "GET" and has_permission(username, 'Labs', 'read'):
        return base_render_to_response(request, 'v2/labs/api_sequence_learning.html', api_sequence_config)

    if request.method == "POST" and has_permission(username, 'Labs', 'write'):
        request_body = json.loads(request.body)
        learning_cycle = request_body.get('learning_cycle')
        enabled = request_body.get('enable')
        time_window = request_body.get('time_window')
        user_key = request_body.get("user_key")
        if type(enabled) != bool:
            return HttpResponse(status=403)
        if type(learning_cycle) != int or learning_cycle < 1 or learning_cycle > 60:
            return HttpResponse(status=403)
        if type(time_window) != int or time_window < 1 or time_window > 300:
            return HttpResponse(status=403)
        if user_key not in ("src_ip", "user_agent", "src_ip+user_agent"):
            return HttpResponse(status=403)
        op_action = ugettext_noop('Modify')
        op_msg = ugettext_noop('Calling-Order Self-Learning')
        if web_console_conf.set_value("nginx/api/v2/api_sequence_leaning",
                                      {"enabled": enabled, "time_window": time_window,
                                       "learning_cycle": learning_cycle, "user_key": user_key}, is_abs=True):
            logger.error("Set Api Sequence Learning error")
            operation_log(request, ugettext_noop('Labs'), op_action, '1', {'msg': op_msg})
            return JsonResponse({'save_success': False})
        else:
            operation_log(request, ugettext_noop('Labs'), op_action, '0', {'msg': op_msg})
            return JsonResponse({'save_success': True})
    else:
        return HttpResponse(status=403)


@login_required
@expert_mode_required
def tcp_reset(request):
    username = request.user.username
    conf = ConfDb()
    cfg = {}
    if request.method == 'POST' and has_permission(username, 'Labs', 'write'):
        try:
            conf = json.loads(request.body)
            enable = conf.get('enable')
            if type(enable) != bool:
                raise Exception('enable should be bool')
            duration = conf.get('duration', 300)
            duration = int(duration)
            if duration < 60 or duration > 600000:
                raise Exception("duration should be 60-600000")
            remote_mac_address = conf.get('remote_mac_address', '')
            pattern = r"^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$"
            if remote_mac_address != '' and re.match(pattern, remote_mac_address) == False:
                raise Exception("Invalid Mac Address")

            cfg['httpcap_cfg/tcp_reset/enable'] = enable
            cfg['httpcap_cfg/tcp_reset/duration'] = duration
            cfg['httpcap_cfg/tcp_reset/remote_mac_address'] = remote_mac_address

        except Exception as e:
            logger.error('[tcp reset] submit invalid data: {}'.format(e))
            return JsonResponse({'result': 'error', 'message': 'invalid data'})

        if cfg:
            if service_mgr_set_asp_config(cfg, 'IP', 'User')[0] == 0:
                log_info = {
                    'enable': enable,
                    'duration': duration,
                    'remote_mac_address': remote_mac_address
                }
                operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), '0', {'msg': ugettext_noop('Configuration changed for blocking on mirror deployment'), 'info': log_info })
                result = {'result': 'success'}
                return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
            else:
                operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), '1', {'msg': ugettext_noop('Configuration changed for blocking on mirror deployment')})
                result = {'result': 'failed'}
                return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

    elif request.method == "GET" and has_permission(username, 'Labs', 'read'):
        enable = conf.get_value('httpcap_cfg/tcp_reset/enable', False)
        duration = conf.get_value('httpcap_cfg/tcp_reset/duration', 300)
        mirror_port = usable_device_names(3)[1]
        chaff_port = usable_device_names(3)[2]
        remote_mac_address = conf.get_value('httpcap_cfg/tcp_reset/remote_mac_address', '')

        return base_render_to_response(request, 'v2/labs/tcp_reset.html', {'labs_tcp_reset':
                                                                            {'enable': enable,
                                                                             'duration': duration,
                                                                             'mirror_port': mirror_port,
                                                                             'chaff_port': chaff_port,
                                                                             'remote_mac_address': remote_mac_address}})
    else:
        return HttpResponse(status=403)

@login_required
@expert_mode_required
def reset_ip_blacklists(request):
    username = request.user.username
    if request.method == 'POST' and has_permission(username, 'Labs', 'write'):

        if service_mgr_set_asp_config({'httpcap_cfg/tcp_reset/reset_ip_time': int(time.time())}, 'IP', 'User')[0] == 0:
            operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), '0', {'msg': ugettext_noop('IP blacklist reset for blocking on mirror deployment')})
            result = {'result': 'success'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
        else:
            operation_log(request, ugettext_noop('Labs'), ugettext_noop('Modify'), '1', {'msg': ugettext_noop('IP blacklist reset for blocking on mirror deployment')})
            result = {'result': 'failed'}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
    else:
        return HttpResponse(status=403)


@login_required
@expert_mode_required
def reboot_protection_engine(request):
    username = request.user.username
    if request.method == "GET" and has_permission(username, 'Labs', 'read'):
        return base_render_to_response(request, 'v2/labs/reboot_protection_engine.html')

    elif request.method == 'POST' and has_permission(username, 'Labs', 'write'):

        exe('sudo service nginx reload', False, False)
        time.sleep(3)

        node_ip = BaseConf().get_value('_private/os/network/admin/ip')

        operation_log(request, ugettext_noop('Labs'), ugettext_noop('Reboot'), '0',
                      {'msg': ugettext_noop('Reboot node protection engine: {node_ip}'),
                       'extra': {'node_ip': node_ip}})

        result = {'result': 'success'}
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)
    else:
        return HttpResponse(status=403)


def validate_role_config(role_name, comments, menu_access_right, is_create):
    if role_name is None:
        raise ValidationError(ugettext_noop('Role name is required.'), code='invalid')

    # rule apply for create user only
    if is_create:
        if role_name in ('Administrator', 'Operator', 'Viewer', 'StatisticViewer', 'Auditor'):
            raise ValidationError(ugettext_noop('The role name is reserved'), code='invalid')

        if WebconsoleConf().is_role_exist(role_name):
            raise ValidationError(ugettext_noop('The role name already exists'), code='invalid')

        if len(role_name) < 4 or len(role_name) > 64:
            raise ValidationError(ugettext_noop('The length of the role name should be 4 to 64 characters.'), code='invalid')

        if not re.match('^[a-zA-Z\d]\w*$', role_name):
            raise ValidationError(ugettext_noop('Role name must contain only letters, numbers and underscores, and cannot start with underscores!'))
    else:
        if not WebconsoleConf().is_role_exist(role_name):
            raise ValidationError(ugettext_noop('This role has been deleted. Refresh the page to view roles currently exist.'), code='invalid')

    if len(comments) > 100:
        raise ValidationError(ugettext_noop('The length of the comments should be less than 100 characters.'),
                              code='invalid')

    try:
        if type(menu_access_right) != dict:
            raise ValidationError(ugettext_noop('The format of menu_access_right is wrong.'), code='invalid')
    except Exception:
        raise ValidationError(ugettext_noop('The format of menu_access_right is wrong.'), code='invalid')


def disabled_menu_set_by_license():
    disabled_menu_list = list()

    license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
    if not license_info.is_module_effect(license_info.DM_ADVANCED_WAF):
        disabled_menu_list.extend(['WAF_General_Protection', 'WAF_Power_Protection', 'WAF_Compliance_Detection',
                                   'WAF_Global_Custom_Rule', 'WAF_Global_Whitelist', 'WAF_Flow_Learning',
                                   'WAF_Analysis_And_Process', 'WAF_Ruleset_Upgrade', 'WAF_Settings'])
    if not license_info.is_module_effect(license_info.DM_PROGRAMMABLE_DEFENDING):
        disabled_menu_list.append('Programmable_Defending')
    if not (hasattr(license_info, 'mobile_max_app') and int(license_info.mobile_max_app) > 0):
        disabled_menu_list.append('Mobile_Protection')
    if not license_info.is_mpp_enabled():
        disabled_menu_list.append('Mpp_Settings')
    if not license_info.is_module_effect(license_info.DM_API_MONITOR):
        disabled_menu_list.append('API_Means')
    if not (license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS_APP) or license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS_BIZ) or license_info.is_module_effect(
                license_info.DM_BOT_THREAT_AWARENESS)):
        disabled_menu_list.append('Business_Threat_Awareness')
    if not license_info.is_module_effect(license_info.DM_BIG_SCREEN_DISPLAY):
        disabled_menu_list.append('Big_Screen_Display')
    if not license_info.is_module_effect(license_info.DM_FILE_TRANSFER_MONITOR):
        disabled_menu_list.append('File_Monitoring')

    # NGWAF的WE license无信誉库
    if is_product_category_NGWAF() and license_info.get_license_protected_level() == 'WE':
        disabled_menu_list.append('Reputation')

    return set(disabled_menu_list)


def disabled_menu_set_by_deploy_mode():
    disabled_menu_list = list()
    conf = ConfDb()

    if conf.get_deploy_mode() != ConfDb.DEPLOY_MODE_MIRROR:
        disabled_menu_list.append('System_HttpCap_Cfg')

    return set(disabled_menu_list)


def get_role_menu_list():
    menu_list = []
    file_path = get_release_file('web_admin/src/menus_' + get_product_type().lower() + '.md')
    if os.path.exists(file_path):
        with open(file_path, 'r') as fp:
            disabled_menu_set = disabled_menu_set_by_license().union(disabled_menu_set_by_deploy_mode())
            for line in fp.readlines()[2:]:
                arr = line.split('|')
                menuid = arr[1].strip()
                name = arr[2].strip().replace('"', '')
                if '>' not in name:
                    trans_name = _(name)
                else:
                    trans_name_list = [_(sub_name.strip()) for sub_name in name.split('>')]
                    trans_name = ' > '.join(trans_name_list)
                access_right_range = arr[3].strip()
                if menuid not in disabled_menu_set and access_right_range != '--':
                    menu_list.append({
                        'menuid': menuid,
                        'name': trans_name,
                        'access_right_range': access_right_range.split(',')
                    })
    return menu_list


@login_required
@check_permission('Role_Management', 'read')
@role_management_enabled
def get_all_menus_for_role(request):
    return HttpResponse(json.dumps({'result': 'ok', 'menu_list': get_role_menu_list()}),
                        content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Role_Management', 'write')
@role_management_enabled
def create_role(request):
    if request.method == 'POST':
        try:
            params = json.loads(request.body)
        except:
            return json_fail_response('bad json')
        role_name = params.get('role_name')
        comments = params.get('comments')
        menu_access_right = params.get('menu_access_right')

        conf = WebconsoleConf()
        if conf.get_count_of_roles() >= 100:
            result = {
                'result': 'error',
                'message': _('The number of roles has exceeded 100.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        try:
            validate_role_config(role_name, comments, menu_access_right, is_create=True)
        except ValidationError as err:
            result = {'result': 'error', 'message': _(err.message)}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        menu_access_right["Risk_Report_Setting"] = "r-"

        if conf.set_role(role_name, comments, menu_access_right) == 0:
            result = {'result': 'ok'}
            Group.objects.get_or_create(name=role_name)
            op_log_info = "role_name: {}\ncomments: {}\nmenu_access_right: {}".format(
                role_name, comments, menu_access_right)
            operation_log(request, ugettext_noop('Roles'), ugettext_noop('New'), '0', {
                'msg': ugettext_noop('Role has been created: '), 'spliceMsg': role_name, 'info': op_log_info})
        else:
            result = {
                'result': 'error',
                'message': _('Fail to create role!'),
            }

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Role_Management', 'write')
@role_management_enabled
def delete_role(request):
    if request.method == 'POST':
        try:
            params = json.loads(request.body)
        except:
            return json_fail_response('bad json')
        role_name = params.get('role_name')

        conf = WebconsoleConf()
        if not conf.is_role_exist(role_name):
            result = {
                'result': 'error',
                'message': _('Role has been deleted already, please refresh this page again.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        if role_name in conf.get_all_used_roles_set():
            result = {
                'result': 'error',
                'message': _('Could not delete role used by user.'),
            }
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        if conf.delete_role(role_name) == 0:
            result = {'result': 'ok'}
            Group.objects.filter(name=role_name).delete()
            operation_log(request, ugettext_noop('Roles'), ugettext_noop('Delete'), '0', {
                'msg': ugettext_noop('Role has been deleted: '), 'spliceMsg': role_name})
        else:
            result = {
                'result': 'error',
                'message': _('Fail to delete role!'),
            }

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Role_Management', 'write')
@role_management_enabled
def edit_role(request):
    if request.method == 'POST':
        try:
            params = json.loads(request.body)
        except:
            return json_fail_response('bad json')
        role_name = params.get('role_name')
        comments = params.get('comments')
        menu_access_right = params.get('menu_access_right')

        conf = WebconsoleConf()
        try:
            validate_role_config(role_name, comments, menu_access_right, is_create=False)
        except ValidationError as err:
            result = {'result': 'error', 'message': _(err.message)}
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        menu_access_right["Risk_Report_Setting"] = "r-"

        old_roles_config = conf.get_all_roles_config().get(role_name, {})
        if conf.set_role(role_name, comments, menu_access_right) == 0:
            result = {'result': 'ok'}
            op_log_info = "role_name: {}\nbefore edit: \ncomments: {}\nmenu_access_right: {}\n" \
                          "after edit: \ncomments: {}\nmenu_access_right: {}".format(
                role_name, old_roles_config.get('comments', ''), old_roles_config.get('menus', {}),
                comments, menu_access_right)
            operation_log(request, ugettext_noop('Roles'), ugettext_noop('Modify'), '0', {
                'msg': ugettext_noop('Role has been edited: '), 'spliceMsg': role_name, 'info': op_log_info})
        else:
            result = {
                'result': 'error',
                'message': _('Fail to edit role!'),
            }

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@login_required
@check_permission('Role_Management', 'read')
@role_management_enabled
def get_all_roles(request):
    all_roles_config = WebconsoleConf().get_all_roles_config()
    all_menus_list = get_role_menu_list()
    result_all_roles = {}
    for role, value in all_roles_config.items():
        comments = value['comments']
        menus = list()
        menus_access_right_dict = value['menus']
        for menu in all_menus_list:
            menus.append({
                'menuid': menu['menuid'],
                'name': menu['name'],
                'access_right_range': menu['access_right_range'],
                'access_right': menus_access_right_dict.get(menu['menuid'], '--')
            })
        result_all_roles[role] = {'comments': comments, 'menus': menus}

    return HttpResponse(json.dumps({'result': 'ok', 'all_roles': result_all_roles}), content_type=CONTENT_TYPE_JSON)


@login_required
@expert_mode_required
def role_management_switch(request):
    username = request.user.username
    if request.method == "GET" and has_permission(username, 'Labs', 'read'):
        return base_render_to_response(request, 'v2/labs/role_management_switch.html')
    elif request.method == "POST" and has_permission(username, 'Labs', 'write'):
        request_body = json.loads(request.body)
        enable = request_body.get('enable')
        if type(enable) != bool:
            return HttpResponse(status=403)

        op_action = ugettext_noop('Enable') if enable else ugettext_noop('Disable')
        op_msg = ugettext_noop('Enable Role Management.') if enable else ugettext_noop('Disable Role Management.')
        if WebconsoleConf().set_role_management_enable(enable):
            logger.error("Set Role Management Switch error")
            operation_log(request, ugettext_noop('Labs'), op_action, '1', {
                'msg': op_msg,
            })
            err_msg = _('Fail to enable role management!') if enable else _('Fail to disable role management!')
            return HttpResponse(json.dumps({'result': 'error', 'message': err_msg}), content_type=CONTENT_TYPE_JSON)
        else:
            operation_log(request, ugettext_noop('Labs'), op_action, '0', {
                'msg': op_msg,
            })
            return HttpResponse(json.dumps({'result': 'ok'}), content_type=CONTENT_TYPE_JSON)
    else:
        return HttpResponse(status=403)


def get_left_qps_quota(exclude_user=None):
    all_quota = len(ConfDb().get_nodeid_list_by_roles('proxy')) * 10000
    reserved_quota = 0
    for k, v in WebconsoleConf().get_all_qps_config().items():
        if k != exclude_user and v['quota'] != 0:
            reserved_quota += v['quota']
    return all_quota - reserved_quota


@login_required
@check_permission('Qps_Management', 'read')
def get_all_qps_info(request):
    all_qps_info = dict()
    for oper in get_users_with_role('Operator'):
        qps_config = WebconsoleConf().get_qps_config(oper)
        status = _('Normal')
        if qps_config['is_limited']:
            status = _('Transparent')
        elif qps_config['date_array']:
            status = _('Exceed limit for {} day(s)').format(len(qps_config['date_array']))
        sites = list()
        for site in get_sites_4_user(oper):
            if site.get('alias', ''):
                sites.append(site['alias'] + '@' + site['name'])
            else:
                sites.append(site['name'])
        all_qps_info[oper] = {
            'quota': qps_config['quota'],
            'is_limited': qps_config['is_limited'],
            'sites': sites,
            'status': status
        }

    return HttpResponse(json.dumps({'result': 'ok', 'left_qps_quota': get_left_qps_quota(), 'all_qps_info': all_qps_info}),
                        content_type=CONTENT_TYPE_JSON)


def validate_qps_config(operator_name, quota, is_limited):
    if operator_name is None or quota is None or is_limited is None:
        return {
            'result': 'error',
            'message': _('Missing necessary parameter.')
        }

    if (type(operator_name) != str and type(operator_name) != unicode) or operator_name not in get_users_with_role('Operator'):
        return {
            'result': 'error',
            'message': _('The operator does not exist.')
        }

    if type(quota) != int or quota < 0:
        return {
            'result': 'error',
            'message': _('The quota parameter is wrong.')
        }
    if quota != 0 and quota > get_left_qps_quota(operator_name):
        return {
            'result': 'error',
            'message': _('The reserved quota has exceeded limit.')
        }

    if type(is_limited) != bool:
        return {
            'result': 'error',
            'message': _('The is_limited parameter is wrong.')
        }


@login_required
@check_permission('Qps_Management', 'write')
def update_qps(request):
    if request.method == 'POST':
        try:
            params = json.loads(request.body)
        except:
            return json_fail_response('bad json')
        operator_name = params.get('operator_name')
        quota = params.get('quota')
        is_limited = params.get('is_limited')

        result = validate_qps_config(operator_name, quota, is_limited)
        if result:
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

        conf = WebconsoleConf()
        old_config = conf.get_qps_config(operator_name)
        if conf.set_qps_config(operator_name, quota, is_limited) == 0:
            result = {'result': 'ok'}
            op_log_info = "operator_name: {}\n\nbefore update: \nquota: {}\nis_limited: {}\n" \
                          "\nafter update: \nquota: {}\nis_limited: {}".format(
                operator_name, old_config.get('quota', 0), old_config.get('is_limited', False), quota, is_limited)
            operation_log(request, ugettext_noop('QPS_Management'), ugettext_noop('Modify'), '0', {
                'msg': ugettext_noop('QPS has been updated: '), 'spliceMsg': operator_name, 'info': op_log_info})
        else:
            result = {
                'result': 'error',
                'message': _('Failed to update QPS.'),
            }
            operation_log(request, ugettext_noop('QPS_Management'), ugettext_noop('Modify'), '1', {
                'msg': ugettext_noop('Failed to update QPS: '), 'spliceMsg': operator_name})
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

def try_downgrade_unsupported_strategy_ids():
    confdb = ConfDb()

    # 读取当前策略文件，获取有效 id
    now_ids = []
    with open('/etc/asp/release/nginx/modsecurity/owasp-modsecurity-crs/waf_default_site_strategies.json') as file:
        data = json.load(file)
        now_ids = [item['id'] for item in data]

    # 获取当前策略配置
    site_strategy_used_list = confdb.get_value('nginx/waf/site_strategy_used_list', {})

    # 获取不合规模版 id
    unsupported_ids = []
    for i in site_strategy_used_list:
        if int(i) < 1000 and int(i) not in now_ids:
            unsupported_ids.append(i)

    # id 均合规
    if len(unsupported_ids) == 0:
        return

    logging.info('downgrade waf_ruleset: unsupported_ids {}'.format(unsupported_ids))

    default_strategy_id = 1
    if get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit':
        default_strategy_id = 9

    # 更新策略列表
    for key, value in site_strategy_used_list.items():
        if key in unsupported_ids:
            # 将配置移动/新增到默认策略模版中
            if str(default_strategy_id) in site_strategy_used_list:
                site_strategy_used_list[str(default_strategy_id)].extend(value)
            else:
                site_strategy_used_list[str(default_strategy_id)] = value

            # 移除不存在策略模版
            del site_strategy_used_list[key]

    values = [('nginx/waf/site_strategy_used_list', site_strategy_used_list)]

    # 更新站点 waf 策略模版
    for host, value in confdb.get_all('nginx/upstreams/', {}).items():
        if value.get('waf_strategy') in unsupported_ids:
            values.append(('nginx/upstreams/{0}/waf_strategy'.format(host), str(default_strategy_id)))

    logging.info('downgrade waf_ruleset: set_asp_conf_values {}'.format(values))
    BaseConf().set_asp_conf_values(values)

def meta_json(request):
    data = {
        'lang': 'en-US' if get_language() == 'en' else 'zh-hans',
        'copyright_year': get_manifest_info().get('copyright_year')
    }
    return HttpResponse(json.dumps(data), content_type=CONTENT_TYPE_JSON)


def priority_static(request, file_list):
    for file_path in file_list:
        if file_path.endswith('.json'):
            content_type = 'application/json'
        elif file_path.endswith('.ico'):
            content_type = 'image/x-icon'
        else:
            content_type = 'image/png'
        if os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                return HttpResponse(f.read(), content_type=content_type)
    return HttpResponse({}, status=404)
