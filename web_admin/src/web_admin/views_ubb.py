# -*- coding:utf-8 -*-

# This module includes following parts:
# 1. dynamic challenge
# 2. load ubb access policy
# 3. web black list
# 4. web high frequency
# 5. web bad_behavior
# 6. web reputation
# 7. web new_comer
# 8. manual rule
# 9. timer
# 10. create adv_filter code
# 11. others
# 12. common functions about rule
# 13. about auto list and black ip
# 14. about share memory
# 15. app black list
# 16. app high frequency
# 17. app reputation
# 18. operation on nginx error log
# 19. upload source files
# 20. ubb counter
# 21. ubb import/export
# 22. ubb sort

import hashlib
import json
import logging
import os
import re
import socket
import shutil
import urllib2
import urllib
import copy
import ssl
import zipfile
import datetime
import tempfile
from Conf_Base import BaseConf
import time

import module_ctrls.ubbv2.ubb_util as ubb_util
from module_ctrls.ubbv2.ubb_util import UBB_ZIP_FILE_NAME, UBB_RESOURCE_FILE_FOLDER, TMP_RESOURCE_DIR, default_share_dict
from asp_conf_ctrl import AspConfCtrl, ConfDb
from asp_utils.license_info import LicenseInfo
from asp_utils.utils import lua_code_invalid, get_release_file, get_sync_file_path, get_upgrade_lib_path, get_language, remove_lua_comments, RESULT, captcha_get_path, get_layout
from asp_utils.zk_client import ZkClient
from django.http import HttpResponse, FileResponse
from django.utils.translation import ugettext as _, ugettext_noop
from module_ctrls.ubbv2.advfilter_parser import get_advfilter_lua_code
from service_mgr_rest_api import service_mgr_set_command
from web_admin.Conf_Webconsole import WebconsoleConf
from web_admin.decorators import allow_method, check_permission, has_permission
from web_admin.decorators import has_dm_permission, adv_operation_code_required
from web_admin.operation_log import operation_log
from web_admin.view_lib import login_required
from web_admin.views import base_render_to_response, export_lub_ubb_config, import_lub_ubb_config,CONTENT_TYPE_JSON, CONTENT_TYPE_PLAIN

from Conf_Nginx import NginxConf

logger = logging.getLogger('view_ubb')

WEB_AUTO_LIST_TYPE = ['ip', 'fp', 'ip_c', 'account', 'account_hostname']
APP_AUTO_LIST_TYPE = ['ip', 'app_fp', 'ip_c', 'account', 'account_hostname']

MAX_SHAREDICT_NUM = 40
MAX_SHAREDICT_SIZE = 1024

MAX_DESC_LENGTH = 20

MAX_FILTER_ITEMS = 20


def _share_memory_invalid(share_dict):
    name_pattern = re.compile(r'^\w{1,64}$')
    value_pattern = re.compile(r'^\d+[mM]$')
    description_pattern = re.compile(r'.{0,100}$')

    if len(share_dict) > MAX_SHAREDICT_NUM:
        return True, "Number of shared dict exceeds limit"

    for name, info in share_dict.iteritems():
        v_name = name_pattern.match(name)
        if v_name is None:
            return True, "Invalid shared dict name: {}".format(name)

        v_value = value_pattern.match(info['size'])
        if v_value is None or \
                int(info['size'][:-1]) > MAX_SHAREDICT_SIZE or int(info['size'][:-1]) <= 0:
            return True, "Invalid shared dict size: {}".format(info['size'])

        v_description = description_pattern.match(info['description'])
        if v_description is None:
            return True, "length of share memory {}'s description exceeds its limitation".format(name)

    return False, ''


# =============================== dynamic challenge ===================================================
captcha_config = captcha_get_path()

@has_dm_permission(LicenseInfo.DM_DYNAMIC_CHANLLENGE)
@login_required
def labs_captcha(request):
    """
download Interface:
    request:
        method: GET
        URL: "system/labs/captcha/?action=download"

    response: zip format file

upload Interface:
    request:
        method: POST
        URL: "system/labs/captcha/?action=upload"
        Body: zip format file
        name: "template"

    response: json format string
        on Success:
        {
            'error': 0,
            'version': "string",
            'templatesCount': integer,
            'modifyTime': "string",
            'isDefault': True/False,
        }
        on Failed:
        {'error': !=0, 'errorMsg': "error message"}

restore Interface:
    request:
        method: GET
        URL: "system/labs/captcha/?action=restore"

    response:
        on Success:
        {
            'error': 0,
            'version': "string",
            'templatesCount': integer,
            'modifyTime': "string",
            'isDefault': True/False,
        }
        on Failed:
        {'error': !=0, 'errorMsg': "error message"}
submit Interface:
    request:
        method: GET
        URL: "system/labs/captcha/"

    response: HTML content
        json format variables:
        {
            'enableCaptcha': True/False,
            'css': "string",
            'policy': {
                'threshold': 0~100,

                'percent': 0~100,
                'action': "challenge"/"block_code"/"redirect"/"pass"
                'action_value': "comma seperated integer string",
                'mandatory': True/False,
                'delay': integer,
            },
            'templateInfo': {
                'version': "string",
                'templatesCount': integer,
                'modifyTime': "string",
                'isDefault': True/False,
            }
        }

    request:
        method: POST
        URL: "system/labs/captcha/"
        body: json format
            {
                'enableCaptcha': True/False,
                'policy': {
                    #same as above
                },
                'css': "string",
            }

    response:
        on Success:
        {'error': 0}
        on Failed:
        {'error': !=0, 'errorMsg': "error message"}

$.ajax({
    type: 'GET',
    url: '/system/labs/captcha/?action=download',
    success: function(data){
        console.log(data)
    },
    error: function(){
        console.log("error")
    },
});

$.ajax({
    type: 'GET',
    url: '/system/labs/captcha/?action=restore',
    success: function(data){
        console.log(data)
    },
    error: function(){
        console.log("error")
    },
});

$.ajax({
    type: 'POST',
    url: '/system/labs/captcha/',
    data: JSON.stringify({
        'enableCaptcha': true,
        'policy': {
            'threshold': 70,
            'percent': 100,
            'action': "challenge",
            'action_value': "1,2,3",
            'mandatory': true,
            'delay': 0,
        },
        'css': "{string}",
    }),
    dataType: 'json',
    success: function(data){
        console.log(data)
    },
    error: function(){
        console.log("error")
    },
});

formData = new FormData($('form')[0]);
formData.append('file',$(':file')[0].files[0]);
$.ajax({
    type: 'POST',
    url: '/system/labs/captcha/?action=upload',
    contentType: false,
    processData: false,
    data: formData,
    success: function(data){
        console.log(data)
    },
    error: function(){
        console.log("error")
    },
});
    """

    conf = WebconsoleConf()

    action = request.POST.get('operate') if request.method == 'POST' else request.GET.get('operate')
    logger.info("action={}".format(action))
    if action == 'detail':
        if not has_permission(request.user.username, 'Programmable_Defending', 'read'):
            return HttpResponse(status=403)
        if not ConfDb().is_proxy():
            return HttpResponse(status=403)
        result = labs_captcha_template_detail(request)
        return base_render_to_response(request, 'v2/labs/captcha_template_detail.html', {'result': result})

    if not has_permission(request.user.username, 'Programmable_Defending', 'write'):
        return HttpResponse(status=403)
    if action == 'download':
        return labs_captcha_template_download(request)
    elif action == 'upload':
        return labs_captcha_template_upload(request)
    elif action == 'restore':
        return labs_captcha_template_restore(request)
    else:
        return labs_captcha_commit(request)


def labs_captcha_error(error_id, error_msg):
    result = {'error': error_id, 'errorMsg': _(error_msg)}
    return HttpResponse(json.dumps(result), content_type='application/json')


def labs_captcha_template_download(request):
    _, zipf = captcha_get_template_zip()
    if not os.path.isfile(zipf):
        return labs_captcha_error(-1, "File not found")

    try:
        with open(zipf, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/zip')
            response['Content-Disposition'] = "attachment; filename=templates.zip"
            return response
    except Exception as e:
        logger.error(e)
        return labs_captcha_error(-1, e.message)


@adv_operation_code_required
def labs_captcha_template_upload(request):
    def get_version(fn):
        try:
            with zipfile.ZipFile(fn, 'r') as zipf:
                f = zipf.open("manifest.json", "r")
                meta = f.read()
                manifest = json.loads(meta, encoding='utf-8')

                return manifest.get('version', '')
        except Exception as e:
            logging.error(e)
            return None

    result = {'error': 0}
    if request.method != "POST":
        return labs_captcha_error(-1, "Invalid HTTP method")

    try:
        f = request.FILES['template']
        if f.size > (1024 * 1024 * 100):  # Bigger than 100M
            raise Exception(ugettext_noop("The file is big more than 100M."))

        # check upload one
        tempfn = tempfile.mktemp()
        with open(tempfn, 'wb') as of:
            for chunk in f.chunks():
                of.write(chunk)

        cust_ver = None
        with zipfile.ZipFile(tempfn, 'r') as zipf:
            if zipf.testzip() != None:
                raise Exception(ugettext_noop("Invalid zip file."))

            get_file_size = lambda fname: zipf.getinfo(fname).file_size
            get_file_extension = lambda fname: os.path.splitext(fname)[1][1:]
            expected_file_size = 1024 * 1024 * 20
            try:
                arch = zipf.namelist()
                if len(arch) > 500:
                    raise Exception(ugettext_noop("zip include file exceed 500."))
                templates = []
                for i in arch:
                    if get_file_size(i) > expected_file_size:
                        raise Exception(ugettext_noop("Single file size exceed 20M."))
                    if get_file_extension(i) in ['exe', 'dll', 'php', 'asp', 'aspx', 'apk', 'sys']:
                        raise Exception(ugettext_noop('zip include invalid file.'))
                    found = re.match("^\d+/$", i)
                    if found:
                        templates += [found.group(0)]

                if len(templates) <= 0:
                    raise Exception(ugettext_noop("No valid template found."))

                for i in templates:
                    zipf.getinfo(i + "customize.lua")
                    zipf.getinfo(i + "tlp.html")

                cust_ver = zipf.open("manifest.json", "r")
                cust_ver = cust_ver.read()
                cust_ver = json.loads(cust_ver, encoding='utf-8')
                cust_ver = cust_ver.get('version', '')

                zipf.getinfo("base.css")
                zipf.getinfo("resources/")
            except Exception as e:
                operation_log(request, ugettext_noop('reCaptcha'), ugettext_noop('Import'), '1',
                              {'msg': ugettext_noop('Upload reCaptcha template')})
                raise Exception(e.message)

        # check whether the version is match
        def_ver = get_version(captcha_config['defaultFile'])
        if cust_ver != def_ver:
            raise Exception(ugettext_noop("Version Mismatch."))

        if os.path.isfile(tempfn):
            os.remove(tempfn)

        # sync file to all nodes in cluster
        md5 = hashlib.md5()
        fullpath = get_sync_file_path("captchaUpload.zip")
        with open(get_upgrade_lib_path(fullpath), 'wb') as of:
            for chunk in f.chunks():
                of.write(chunk)
                md5.update(chunk)

        ret = BaseConf().sync_file(fullpath)
        if ret != RESULT.OK:
            operation_log(request, ugettext_noop('reCaptcha'), ugettext_noop('Import'), '1',
                          {'msg': ugettext_noop('Upload reCaptcha template')})
            raise Exception("Sync file failed")

        # update template
        nginx_conf = NginxConf()
        ret, output, error_msg = nginx_conf.set_captcha_template(
            {'timestamp': time.time(), 'path': fullpath, 'md5': md5.hexdigest()})
        if ret != 0:
            raise Exception(error_msg)

        operation_log(request, ugettext_noop('reCaptcha'), ugettext_noop('Import'), '0',
                      {'msg': ugettext_noop('Upload reCaptcha template')})

        info = captcha_get_template_info()
        result.update({'templateInfo': info})
        return HttpResponse(json.dumps(result), content_type='text/plain')

    except Exception as e:
        logger.error(e)
        operation_log(request, ugettext_noop('reCaptcha'), ugettext_noop('Import'), '1',
                      {'msg': ugettext_noop('Upload reCaptcha template')})
        return labs_captcha_error(-1, e.message)


def labs_captcha_template_restore(request):
    nginx_conf = NginxConf()
    ret, output, error_msg = nginx_conf.set_captcha_template(
        {'timestamp': time.time(), 'path': captcha_config['defaultFile']})
    result = {'error': ret, 'errorMsg': error_msg}

    if result.get('error') == 0:
        info = captcha_get_template_info()
        result.update({'templateInfo': info})
    return HttpResponse(json.dumps(result), content_type='application/json')


def labs_captcha_commit(request):
    nginx_conf = NginxConf()
    if request.method == 'POST':
        ret = False
        error_msg = ''

        logger.info("data:{}".format(request.POST.copy()))
        try:
            passed_data = json.loads(str(request.body))
            logger.info("passed_data:{}".format(passed_data))
            captcha_enabled = passed_data.get('enableCaptcha', False)
            policy = passed_data.get('policy', {})
            css = passed_data.get('css')

            old_enabled = nginx_conf.get_captcha_enable_switch()
            if captcha_enabled != old_enabled:
                ret, output, error_msg = nginx_conf.set_captcha_enable_switch(captcha_enabled)
                if ret:
                    raise Exception(error_msg)
                logging.info("Captcha switch state updated")

            ret, output, error_msg = nginx_conf.set_captcha_policy(policy)
            if ret:
                raise Exception(error_msg)
            logging.info("Captcha policy updated")

            ret, output, error_msg = nginx_conf.set_captcha_css(css)
            if ret:
                raise Exception(error_msg)
            logging.info("Captcha css updated")

            result = json.dumps({
                'error': ret,
                'errorMsg': error_msg,
            })
        except Exception as e:
            result = json.dumps({
                'error': -1,
                'errorMsg': _(e.message),
            })
        finally:
            return HttpResponse(result, content_type='application/json')

    else:
        captcha_info = captcha_get_info(nginx_conf)
        logging.info("info: {}".format(captcha_info))
        return base_render_to_response(request, 'v2/labs/captcha.html', {"captcha_info": captcha_info})


def labs_captcha_template_detail(request):
    nginx_conf = NginxConf()
    result = captcha_get_detail(nginx_conf)
    return result


def captcha_get_template_zip():
    isDefault = False
    templatesZip = captcha_config['customFile']
    if not os.path.isfile(templatesZip):
        isDefault = True
        templatesZip = captcha_config['defaultFile']

    return isDefault, templatesZip


def captcha_get_template_info():
    info = {}
    info['isDefault'], templatesZip = captcha_get_template_zip()

    try:
        info['modifyTime'] = datetime.datetime.fromtimestamp(os.path.getmtime(templatesZip)).strftime(
            "%Y-%m-%d %H:%M:%S")

        templatePath = captcha_config['templatePath']
        templateList = [d for d in os.listdir(templatePath) if os.path.isdir(os.path.join(templatePath, d))]
        templateList = [d for d in templateList if d.isdigit()]
        info['templatesCount'] = len(templateList)
        info['templateList'] = templateList

        with open(os.path.join(templatePath, 'manifest.json')) as metaf:
            manifest = json.load(metaf, encoding='utf-8')
            info['version'] = manifest.get('version', '')

    except Exception as e:
        logger.error(e)

    return info


def captcha_get_info(nginx_conf):
    captcha_enabled = nginx_conf.get_captcha_enable_switch()
    policy = nginx_conf.get_captcha_policy()
    css = nginx_conf.get_captcha_css()
    template_info = captcha_get_template_info()

    # checked captcha license
    license_info = ConfDb().get_license_info(i18n_support=False, care_cluster=False)
    enabled = license_info.is_module_effect(LicenseInfo.DM_DYNAMIC_CHANLLENGE)
    if not enabled:
        captcha_enabled = False

    return {
        'enableCaptcha': captcha_enabled,
        'css': css,
        'policy': policy,
        'templateInfo': template_info
    }


from os import listdir
from os.path import isdir, join


def captcha_get_detail(nginx_conf):
    path = get_release_file("nginx/lua/captcha/templates")
    templates = [f for f in listdir(path) if isdir(join(path, f)) and f.isdigit()]
    description = [join(path, f, "description.json") for f in templates]
    group = zip(templates, description)

    result = {}
    for e in group:
        try:
            template = e[0]
            description = e[1]
            with open(description) as f:
                content = f.read()
            content = json.loads(content)
            type = content['type']
            if not result.get(type):
                result[type] = {}
            result[type][template] = content
            # del content['type']
        except Exception as e:
            logger.error(e)

    return result


# =============================== load ubb access policy ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'read')
def ubb_scenes(request):
    # key: IP / FP / UA / COOKIE_ID  -- single
    # dev_type : [ PC | MAC | MOBILE ]  -- checkbox -- KEY=FP dev_type
    # action: block_code / redirect / pass -- single
    # block_code: return code
    # redirect: page path
    # pass:action_value = ''
    try:
        nginx_conf = NginxConf()
        upstream_conf_list = nginx_conf.get_ubb_sites_list()

        # remove duplicate upstream
        # upstream_conf_list = list(set(upstream_conf_list))
        lua_info = get_lua_info(nginx_conf)

        source_files = ubb_util.get_resource_file_in_zk()
        # if source_files:
        #     source_files = json.loads(source_files, encoding='utf-8')

        counter_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_COUNTER_LIST)
        if counter_rules:
            counter_rules = json.loads(counter_rules, encoding='utf-8')
            rule_fields_compat(counter_rules)

        black_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BLACKLIST_LIST)
        if black_rules:
            black_rules = json.loads(black_rules, encoding='utf-8')
            random_actions_compat(black_rules)
            rule_fields_compat(black_rules)

        high_freq_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_HIGH_FREQ_LIST)
        if high_freq_rules:
            high_freq_rules = json.loads(high_freq_rules, encoding='utf-8')
            random_actions_compat(high_freq_rules)
            high_freq_burst_compat(high_freq_rules)
            high_freq_time_type_compat(high_freq_rules)
            high_freq_autolisttype_compat(high_freq_rules)
            rule_fields_compat(high_freq_rules)

        bad_behavior_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST)
        if bad_behavior_rules:
            bad_behavior_rules = json.loads(bad_behavior_rules, encoding='utf-8')
            random_actions_compat(bad_behavior_rules)
            rule_fields_compat(bad_behavior_rules)

        reputation_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_REPUTATION_LIST)
        if reputation_rules:
            reputation_rules = json.loads(reputation_rules, encoding='utf-8')
            random_actions_compat(reputation_rules)
            rule_fields_compat(reputation_rules)

        new_comer_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_NEW_COMER_LIST)
        if new_comer_rules:
            new_comer_rules = json.loads(new_comer_rules, encoding='utf-8')
            random_actions_compat(new_comer_rules)
            rule_fields_compat(new_comer_rules)

        sniping_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_SNIPING_LIST)
        if sniping_rules:
            sniping_rules = json.loads(sniping_rules, encoding='utf-8')
            random_actions_compat(sniping_rules)
            rule_fields_compat(sniping_rules)

        app_counter_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_COUNTER_LIST)
        if app_counter_rules:
            app_counter_rules = json.loads(app_counter_rules, encoding='utf-8')
            rule_fields_compat(app_counter_rules)

        app_black_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST)
        if app_black_rules:
            app_black_rules = json.loads(app_black_rules, encoding='utf-8')
            random_actions_compat(app_black_rules)
            rule_fields_compat(app_black_rules)

        app_high_freq_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST)
        if app_high_freq_rules:
            app_high_freq_rules = json.loads(app_high_freq_rules, encoding='utf-8')
            random_actions_compat(app_high_freq_rules)
            high_freq_burst_compat(app_high_freq_rules)
            high_freq_autolisttype_compat(app_high_freq_rules, APP_AUTO_LIST_TYPE)
            rule_fields_compat(app_high_freq_rules)

        app_reputation_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_REPUTATION_LIST)
        if app_reputation_rules:
            app_reputation_rules = json.loads(app_reputation_rules, encoding='utf-8')
            random_actions_compat(app_reputation_rules)
            rule_fields_compat(app_reputation_rules)

        site_shield_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_SITE_SHIELD_LIST)
        if site_shield_rules:
            site_shield_rules = json.loads(site_shield_rules, encoding='utf-8')

        datas = {
            'source_files': source_files,
            'counter_rules': counter_rules,
            'black_rules': black_rules,
            'high_freq_rules': high_freq_rules,
            'bad_behavior_rules': bad_behavior_rules,
            'reputation_rules': reputation_rules,
            'upstream_conf_list': upstream_conf_list,
            'new_comer_rules': new_comer_rules,
            'sniping_rules': sniping_rules,
            'site_shield_rules': site_shield_rules,
            'lua_info': lua_info,
            'app_counter_rules': app_counter_rules,
            'app_black_rules': app_black_rules,
            'app_high_freq_rules': app_high_freq_rules,
            'app_reputation_rules': app_reputation_rules,
            'captcha_info': captcha_get_info(nginx_conf),
            'current_time': time.time() * 1000,  # ms
        }

    except Exception as e:
        logger.exception(e)
        datas = None

    return base_render_to_response(request, 'v2/ubb_scenes.html', {'datas': datas})


def get_lua_info(nginx_conf):
    lua_global_switch = nginx_conf.get_lua_global_switch()
    # after upgrade, the default lua_global_switch value is True
    if lua_global_switch is None:
        lua_global_switch = True

    lua_manual_ubb_enable = nginx_conf.get_lua_manual_ubb_switch()
    enableCaptcha = nginx_conf.get_captcha_enable_switch()

    shm_info = nginx_conf.get_lua_share_memory_info()
    shm_size = nginx_conf.get_lua_share_memory_size()

    shm_updated_setting = get_share_memory_setting(shm_size, shm_info)

    is_en_version = get_language() == 'en'

    rule = AspConfCtrl.get_zk_lua_info()
    if not rule:
        cfg_file = get_release_file(ConfDb.PATH_LUA_EXPERT_FILE)
        try:
            with open(cfg_file) as fp:
                rule = fp.read()
            if is_en_version:
                rule = remove_lua_comments(rule)
        except IOError:
            return HttpResponse(json.dumps({
                'error': True,
                'errorMsg': _(r'No rule file or template found.'),
            }), content_type='application/json')

    appRule = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_RULE)
    if not appRule:
        cfg_file = get_release_file(ConfDb.PATH_LUA_MUBB_EXPERT_FILE)
        try:
            with open(cfg_file) as fp:
                appRule = fp.read()
            if is_en_version:
                appRule = remove_lua_comments(appRule)
        except IOError:
            return HttpResponse(json.dumps({
                'error': True,
                'errorMsg': _(r'No appRule file or template found.'),
            }), content_type='application/json')

    mppRule = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MPPUBB_RULE)
    if not mppRule:
        cfg_file = get_release_file(ConfDb.PATH_LUA_MPPUBB_EXPERT_FILE)
        try:
            with open(cfg_file) as fp:
                mppRule = fp.read()
            if is_en_version:
                mppRule = remove_lua_comments(mppRule)
        except IOError:
            return HttpResponse(json.dumps({
                'error': True,
                'errorMsg': _(r'No mppRule file or template found.'),
            }), content_type='application/json')

    return {
        'enableLuaInterface': lua_global_switch,
        'ruleContent': rule,
        'appRuleContent': appRule,
        'mppRuleContent': mppRule,
        'shareDict': shm_updated_setting,
        'maxShareDict': MAX_SHAREDICT_NUM,
        'enableCaptcha': enableCaptcha,
        'lua_manual_ubb_switch': lua_manual_ubb_enable
    }


def random_actions_compat(rules):
    # be compatible with old version(before 19.07)
    #   before 19.07:
    #      'action': 'redirect',  'action_value': 'page.html', 'delay': 10
    #   Since 19.07:
    #      'action_list':[{"action":"block_code", "action_value":400,     "delay":0, "weight": 1}]
    #
    for rule in rules:
        if 'action_list' not in rule.keys():
            rule.setdefault(u'action_list', [
                {"action": rule['action'], "action_value": rule['action_value'], "delay": rule['delay'], "weight": 1}])

        for key in ['action', 'action_value', 'delay']:
            if key in rule.keys():
                rule.pop(key)


def get_share_memory_setting(shm_size, shm_info):
    # return old config share memory + default_share_dict
    rt = copy.deepcopy(default_share_dict)

    if shm_info:
        for shm_name, info in shm_info.items():
            if shm_name in rt:
                rt[shm_name].update(info)
            else:
                rt[shm_name] = info

    if shm_size:
        for shm_name, size in shm_size.items():
            if shm_name in rt:
                rt[shm_name]['size'] = size
            else:
                rt[shm_name] = {'size': size, 'description': '', 'timestamp': None, 'level': None}

    # request for LRU timestamp on each shm
    for shm_name in rt.keys():
        rt[shm_name]['description'] = _(rt[shm_name]['description'])
        result = get_shm_lru_timestamp(shm_name)
        if result['is_success']:
            rt[shm_name]['timestamp'] = result['timestamp']
            rt[shm_name]['level'] = result['level']
        else:
            logging.error(result['error_msg'])
            break

    return rt


def rule_fields_compat(rules):
    # be compatible with old version(before 19.07)
    #   before 19.07:
    #      has no field 'filter_conditions'
    #
    #   Since 19.07:
    #      'filter_conditions':[
    #     {
    #      ‘key’: ‘is_ajax’,
    #      ‘type’: ‘boolean’,
    #      ‘value’: True,
    #      ‘operation’: ‘==’
    #     },
    #     {
    #      "operation": "==",
    #      "type": "enum",
    #      "value": "PhantomJS,App_Scan,LoadRunner,Web_Inspect",
    #      "key": "bot_type"
    #     }
    # ]

    for rule in rules:
        # add new key into rule
        fields_need_compat = {
            'filter_conditions': []
        }
        for key, value in fields_need_compat.items():
            if key not in rule.keys():
                rule.setdefault(key, value)

        ubb_util.to_be_compatible(rule)


def high_freq_burst_compat(high_freq_rules):
    # be compatible with old version(before 19.05)
    for rule in high_freq_rules:
        if 'burst' not in rule.keys():
            rule['burst'] = 2


def high_freq_time_type_compat(rules):
    time_dic = {'day': 864000, 'hour': 3600, 'minute': 60}
    fields_need_compat = {
        'freq_limit_type': 'rate',
        'time_window_type': 'minute',
        'time_window_value': ''
    }
    for rule in rules:
        for key, value in fields_need_compat.items():
            if key not in rule.keys():
                rule.setdefault(key, value)

        freq_time = rule.get('freq_time', None)
        if freq_time in time_dic:
            # freq_time_value: 1/min = 1/60 by default
            freq = float(rule.get('high_freq', 1)) / time_dic[freq_time]
            if freq < 0.001:
                rule['high_freq'] = 0.001
            else:
                rule['high_freq'] = float('%.3f' % freq)

            rule.pop('freq_time')


def high_freq_autolisttype_compat(high_freq_rules, list_type=WEB_AUTO_LIST_TYPE):
    # be compatible with old version(before 19.05)
    # auto_list_type compat
    for rule in high_freq_rules:
        auto_list_type = rule.get('auto_list_type', None)
        if auto_list_type is None:
            # set default value with 'ip'
            rule['auto_list_type'] = 'ip'

            auto_list_time = rule.get('auto_list', 0)
            if auto_list_time > 0:
                high_freq_key = rule.get('key', None)
                if high_freq_key in list_type:
                    rule['auto_list_type'] = high_freq_key


# operation must avoid ' ' and it should be a single English word
def record_operation_log(request, operation_result, operation, message, sub_message, info=''):
    is_success = '0' if operation_result else '1'
    splice_msg = '' if sub_message == '' else ': ' + '{}'.format(sub_message)

    detail = {'msg': message, 'spliceMsg': splice_msg}
    if info:
        detail['info'] = info
    operation_log(request, ugettext_noop('UBBv2'), operation, is_success, detail)

# =============================== web counter scene ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_counter_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)

            rule = dict()
            # rule base info
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')

            # trigger condition
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)

            rule['key'] = data.get('key', None)
            rule['key_value'] = data.get('key_value', None)
            rule['counter'] = data.get('counter', None)
            rule['condition'] = data.get('condition', {})

            # action
            rule['percent'] = data.get('percent', 0)
            rule['action_list'] = data.get('action_list', [])

            # auto_list
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)

            # ubb counter
            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'

            is_valid_counter_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            counter_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_COUNTER_LIST)
            counter_rules = [] if not counter_rules else json.loads(counter_rules, encoding='utf-8')
            operation, result = save_rule(counter_rules, rule, 'COUNTER')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('counter rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('counter rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_counter_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid counter rule :{}'.format(rule))

    check_func_dict = {
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=1999, min_v=1100)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.STATISTIC_KEY_RANGE['web'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "key_value": (lambda x: ubb_util.ubb_invalid_fields_list(x, rule['key'], rule['ubb_scene_type'])),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "counter": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.COUNTER_ID_RANGE)),
        "condition": ubb_util.ubb_invalid_counter_condition,
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('Counter'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_counter_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_COUNTER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'COUNTER')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('counter rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('counter rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_counter_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_COUNTER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            result = save_rule_status(rules, rule_id, enable, 'COUNTER')
            operation = ugettext_noop('Enable') if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('counter rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('counter rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_counter_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            scene = data.get('type')
            enable = data.get('enable')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_COUNTER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'COUNTER' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all counter rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all counter rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== web black list  ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_black_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)

            # {'upstream_key':'*******_80','id': 2002, 'enable': True, 'host': 'www.c.com', 'path': '/lua', 'key': 'cookie_id',
            #                     'key_value': '**************',
            #                     'dev_type': ['mobile', 'mac'], 'percent': 80,
            #                       'action_list':[{"action":"block_code", "action_value":400,     "delay":0, "weight": 1}]
            #                        or    'action': 'redirect',  'action_value': 'page.html', 'delay': 10
            # }
            rule = dict()
            rule['dev_type'] = data.get('dev_type', None)
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['key'] = data.get('key', None)
            rule['key_value'] = data.get('key_value', None)
            rule['percent'] = data.get('percent', 0)
            rule['action_list'] = data.get('action_list', [])
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            
            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'
            if rule['key'] != 'ip':
                rule['is_key_regx'] = data.get('is_key_regx', False)

            is_valid_blacklist_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            black_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BLACKLIST_LIST)
            black_rules = [] if not black_rules else json.loads(black_rules, encoding='utf-8')
            operation, result = save_rule(black_rules, rule, 'BLACK')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('blacklist rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('blacklist rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_blacklist_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid blacklist rule :{}'.format(rule))

    check_func_dict = {
        "dev_type": ubb_util.ubb_invalid_dev_type,
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=2999, min_v=2000)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.BLACKLIST_KEY_RANGE['web'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "key_value": (lambda x: ubb_util.ubb_invalid_string(x, length=100000)),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "is_key_regx": ubb_util.ubb_invalid_bool,
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('Blacklist'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_black_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BLACKLIST_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'BLACK')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('blacklist rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('blacklist rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_black_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BLACKLIST_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            result = save_rule_status(rules, rule_id, enable, 'BLACK')
            operation = ugettext_noop('Enable') if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('blacklist rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('blacklist rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_black_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            scene = data.get('type')
            enable = data.get('enable')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BLACKLIST_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'BLACK' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all blacklist rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all blacklist rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== web high frequency ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_high_freq_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule = dict()
            freq_limit_type = data.get('freq_limit_type', None)
            rule['dev_type'] = data.get('dev_type', None)
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['key'] = data.get('key', None)
            rule['key_value'] = data.get('key_value', None)
            rule['high_freq'] = data.get('high_freq', None)
            rule['percent'] = data.get('percent', None)
            rule['action_list'] = data.get('action_list', [])
            rule['auto_list'] = data.get('auto_list', 0)
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            rule['freq_limit_type'] = freq_limit_type
            if freq_limit_type == 'time_window':
                rule['time_window_type'] = data.get('time_window_type', None)
                rule['time_window_value'] = data.get('time_window_value', -1)
            else:
                rule['burst'] = data.get('burst', 2)

            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'
            is_valid_high_freq_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            high_frep_rules_list = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_HIGH_FREQ_LIST)
            high_frep_rules_list = [] if not high_frep_rules_list else json.loads(high_frep_rules_list,
                                                                                  encoding='utf-8')
            position = [i for i, value in enumerate(high_frep_rules_list) if value['id'] == rule['id']]
            if position and 'freq_time' in high_frep_rules_list[position[0]]:
                high_frep_rules_list[position[0]].pop('freq_time')

            operation, result = save_rule(high_frep_rules_list, rule, 'HIGH_FREQ')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('high frequent rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('high frequent rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_high_freq_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid high freq rule:{}.'.format(rule))

    check_func_dict = {
        "dev_type": ubb_util.ubb_invalid_dev_type,
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=3000, max_v=3999)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "high_freq": (lambda x: ubb_util.ubb_invalid_number_range(x, max_v=65535, min_v=0.001)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.STATISTIC_KEY_RANGE['web'])),
        "key_value": (lambda x: ubb_util.ubb_invalid_fields_list(x, rule['key'], rule['ubb_scene_type'])),
        "enable": ubb_util.ubb_invalid_bool,
        "freq_limit_type": (lambda x: ubb_util.ubb_invalid_str_option(x, ['rate', 'time_window'])),
        "time_window_type": (lambda x: ubb_util.ubb_invalid_str_option(x, ['minute', 'hour', 'day', 'second'])),
        "time_window_value": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=1, max_v=65535)),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "burst": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=0, max_v=65535)),
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('High Frequence'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_high_freq_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_HIGH_FREQ_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'HIGH_FREQ')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('high frequent rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('high frequent rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_high_freq_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_HIGH_FREQ_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = save_rule_status(rules, rule_id, enable, 'HIGH_FREQ')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('high frequent rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('high frequent rule status'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_high_freq_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            scene = data.get('type')
            enable = data.get('enable')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_HIGH_FREQ_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'HIGH_FREQ' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all high frequent rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all high frequent rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== web bad_behavior ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_bad_behavior_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # {'upstream_key': '*******_80', 'id': 4001, 'enable': True, 'host': 'www.a.com', 'path': '/lua', 'min_event': 1,
            #                            'dev_type': ['PC', 'mac', 'mobile'], 'percent': 80, 'action': 'block_code',
            #                            'action_value': 404, 'delay': 10}
            rule = dict()
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['min_event'] = data.get('min_event', 0)
            rule['percent'] = data.get('percent', None)
            rule['action_list'] = data.get('action_list', [])
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)
            rule['operate_behavior'] = data.get('operate_behavior', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'
            is_valid_bad_behavior_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            bad_behavior_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST)
            bad_behavior_rules = [] if not bad_behavior_rules else json.loads(bad_behavior_rules, encoding='utf-8')
            operation, result = save_rule(bad_behavior_rules, rule, 'BAD_BEHAVIOR')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('bad behavior rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('bad behavior rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_bad_behavior_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid bad behavior rule :{}'.format(rule))

    check_func_dict = {
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=4000, max_v=4999)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "min_event": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=1000000)),
        "operate_behavior": (lambda x: ubb_util.ubb_invalid_str_option(x, ['notouch', 'touch'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('Bad Behavior'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_bad_behavior_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'BAD_BEHAVIOR')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('bad behavior rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('bad behavior rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_bad_behavior_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if rules:
                result = save_rule_status(rules, rule_id, enable, 'BAD_BEHAVIOR')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('bad behavior rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('bad behavior rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_bad_behavior_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            scene = data.get('type')
            enable = data.get('enable')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'BAD_BEHAVIOR' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all bad behavior rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all bad behavior rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== web reputation ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_reputation_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule = dict()
            rule['dev_type'] = data.get('dev_type', None)
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)
            rule['key'] = data.get('key', None)
            if rule['key'] not in ('ip', 'fp', 'user_name'):
                raise Exception('不合法的key:{}'.format(rule['key']))

            rule['threat_value_min'] = data.get('threat_value_min', None)
            rule['threat_value_max'] = data.get('threat_value_max', None)
            rule['threat_value'] = data.get('threat_value', None)

            rule['percent'] = data.get('percent', None)
            rule['action_list'] = data.get('action_list', [])
            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'
            is_valid_reputation_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            reputation_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_REPUTATION_LIST)
            reputation_rules = [] if not reputation_rules else json.loads(reputation_rules, encoding='utf-8')
            operation, result = save_rule(reputation_rules, rule, 'REPUTATION')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('reputation rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('reputation rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_reputation_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid bad reputation rule :{}'.format(rule))

    check_func_dict = {
        "dev_type": ubb_util.ubb_invalid_dev_type,
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=5000, max_v=5999)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ['ip', 'fp', 'user_name'])),
        "threat_value_min": (lambda x: ubb_util.ubb_invalid_threat_value(x, max_v=100, min_v=0,
                                                                         valid_key=rule['key'] in (
                                                                             'ip', 'fp', 'user_name'))),
        "threat_value_max": (lambda x: ubb_util.ubb_invalid_threat_value(x, max_v=100, min_v=0,
                                                                         valid_key=rule['key'] in (
                                                                             'ip', 'fp', 'user_name'))),
        "threat_value": (lambda x: ubb_util.ubb_invalid_threat_value(x, max_v=64, min_v=0,
                                                                     valid_key=rule['key'] in (
                                                                         'ip', 'fp', 'user_name'))),
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('Reputation'))

    if rule['key'] != 'user_name' and rule['threat_value_min'] > rule['threat_value_max']:
        raise Exception(_('The minimum of threat should be less than the maximum of threat'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_reputation_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_REPUTATION_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'REPUTATION')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('reputation rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('reputation rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_reputation_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_REPUTATION_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = save_rule_status(rules, rule_id, enable, 'REPUTATION')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('The rule does not exist')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('reputation rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('reputation rule status'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_reputation_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            scene = data.get('type')
            enable = data.get('enable')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_REPUTATION_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'REPUTATION' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('The rule does not exist')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all reputation rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all reputation rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== web new_comer ===================================================
@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_new_comer_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # {'upstream_key': '*******_80', 'id': 4001, 'enable': True, 'host': 'www.a.com', 'path': '/lua', 'min_event': 1,
            #                            'dev_type': ['PC', 'mac', 'mobile'], 'percent': 80, 'action': 'block_code',
            #                            'action_value': 404, 'delay': 10}

            # {"id":6000, "enable":true, "host":"[fd22:4ba5:5a2b:1012::34]", "path":"dfsafsfsaf", "percent":100, "action":"block_code",
            #             "action_value":400, "delay":0,  "is_path_regx":false, "key":"ua", "hold_time":7}
            rule = dict()
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['key'] = data.get('key', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)
            rule['hold_time'] = data.get('hold_time', 0)
            rule['percent'] = data.get('percent', None)
            rule['action_list'] = data.get('action_list', [])
            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'
            is_valid_new_comer_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            new_comer_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_NEW_COMER_LIST)
            new_comer_rules = [] if not new_comer_rules else json.loads(new_comer_rules, encoding='utf-8')
            operation, result = save_rule(new_comer_rules, rule, 'NEW_COMER')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('new comer rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('new comer rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_new_comer_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid new comer rule :{}'.format(rule))

    check_func_dict = {
        "enable": ubb_util.ubb_invalid_bool,
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=6000, max_v=6999)),
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "path": ubb_util.ubb_invalid_path_host,
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ['ip', 'cookie_id', 'fp', 'user_name'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "hold_time": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=1, max_v=30)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('New Comer'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_new_comer_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_NEW_COMER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'NEW_COMER')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('new comer rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('new comer rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_new_comer_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_NEW_COMER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if rules:
                result = save_rule_status(rules, rule_id, enable, 'NEW_COMER')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('new comer rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('new comer rule status'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_new_comer_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / NEW_COMER
            scene = data.get('type')
            enable = data.get('enable')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_NEW_COMER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'NEW_COMER' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all new comer rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all new comer rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== sniping  ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_sniping_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)

            # {'id': 7002, 'enable': True, 'host': 'www.c.com', 'target_path': '/lua', 'prepare_path': '/prepare', 'key': 'cookie_id',
            # 'exception_type': 'ontime', 'start_time': '23:23:13', 'duration': 500,
            #                     'percent': 100,
            #                       'action_list':[{"action":"block_code", "action_value":400,     "delay":0, "weight": 1}]
            # }
            rule = dict()
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['target_path'] = data.get('target_path', None)
            rule['is_target_path_regx'] = data.get('is_target_path_regx', False)
            rule['prepare_path'] = data.get('prepare_path', None)
            rule['is_prepare_path_regx'] = data.get('is_prepare_path_regx', False)
            rule['start_time'] = data.get('start_time', '00:00:00')
            rule['duration'] = data.get('duration', 3600)
            rule['exception_type'] = data.get('exception_type', None)
            rule['key'] = data.get('key', None)

            rule['percent'] = data.get('percent', 0)
            rule['action_list'] = data.get('action_list', [])

            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)

            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'web'
            is_valid_sniping_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            sniping_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_SNIPING_LIST)
            sniping_rules = [] if not sniping_rules else json.loads(sniping_rules, encoding='utf-8')
            operation, result = save_rule(sniping_rules, rule, 'SNIPING')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('sniping rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('sniping rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_sniping_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid sniping rule :{}'.format(rule))

    check_func_dict = {
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=7999, min_v=7000)),
        "action_list": ubb_util.ubb_invalid_action_list,
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ['ip', 'fp', 'user_name', 'cookie_id'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "duration": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=65535, min_v=0)),
        "exception_type": (lambda x: ubb_util.ubb_invalid_str_option(x, ['prepare', 'ontime'])),
        "start_time": (
            lambda x: ubb_util.ubb_invalid_string_match_regex(x, '(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]')),
        "target_path": ubb_util.ubb_invalid_path_host,
        "prepare_path": ubb_util.ubb_invalid_path_host if rule.get("exception_type", None) == "ontime" else (
            lambda x: (False, "Success")),
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_target_path_regx": ubb_util.ubb_invalid_bool,
        "is_prepare_path_regx": ubb_util.ubb_invalid_bool,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, WEB_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in WEB_AUTO_LIST_TYPE)),
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('Sniping'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_sniping_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_SNIPING_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'SNIPING')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('sniping rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('sniping rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_sniping_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_SNIPING_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            result = save_rule_status(rules, rule_id, enable, 'SNIPING')
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('sniping rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('sniping rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_sniping_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            scene = data.get('type')
            enable = data.get('enable')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_SNIPING_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'SNIPING' and rules:
                result = save_all_rules_status(rules, enable, 'SNIPING')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all sniping rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all sniping rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== manual rule ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@adv_operation_code_required
@check_permission('Programmable_Defending', 'write')
def save_manual_rule(request):
    rt = {
        'save_success': True,
        'error_msg': ''
    }

    if request.method == "POST":
        try:
            data = json.loads(request.body)
            params = {
                'web': ['rule_content', ZkClient.PATH_LUA_RULE, 'Web', 'MANUAL'],
                'app': ['app_rule_content', ZkClient.PATH_LUA_MUBB_RULE, 'App', 'APP_MANUAL'],
                'mpp': ['mpp_rule_content', ZkClient.PATH_LUA_MPPUBB_RULE, 'Mpp', 'MPP_UBB_MANUAL']
            }
            all_cfg_same = True
            for key in params.keys():
                rule = data.get(params[key][0])
                old_content = AspConfCtrl.get_zk_lua_info(params[key][1])

                if old_content != rule:
                    all_cfg_same = False
                    invalid, error = lua_code_invalid(rule)
                    if invalid:
                        logger.error('*********save_lua_rule_content {0} SyntaxWarning:{1}'.format(key, error))
                        err_info = error.split('lua:')
                        err_info = "line:{}".format(err_info[1]) if len(err_info) > 1 else error
                        error_msg = "{} {}:{}".format(_("{} Manual Rule".format(params[key][2])),
                                                      _('Syntax error found in rule file'), err_info)
                        raise SyntaxWarning(error_msg)

                    rt_update_fail, error_msg_update = update_rule(params[key][3], rule)
                    if rt_update_fail:
                        logger.error('*********save_lua_{0}_manual_rule_content err:{1}'.format(key, error_msg_update))
                        raise Exception

            if all_cfg_same:
                error_msg = _('please submit only once')
                raise SyntaxWarning(error_msg)

        except SyntaxWarning as e:
            logger.exception(e)
            rt = {
                'save_success': False,
                'error_msg': e.message
            }
        except Exception as e:
            logger.exception(e)
            rt = {
                'save_success': False,
                'error_msg': _("Operation failed")
            }
        finally:
            record_operation_log(request, rt['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('UBBv2 advance rules'), '')

    return HttpResponse(json.dumps(rt), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def enable_manual_rule(request):
    rt = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }

    if request.method == "POST":
        try:
            nginx_conf = NginxConf()
            data = json.loads(request.body)
            switch = data.get('lua_manual_ubb_switch', None)
            old_switch = nginx_conf.get_lua_manual_ubb_switch()

            if switch != old_switch:
                rt_sw, useless, error_msg_sw = nginx_conf.set_lua_manual_ubb_switch(switch)

                response = not rt_sw
                error_msg = error_msg_sw
                rt = {
                    'save_success': response,
                    'error_msg': error_msg
                }
            else:
                logger.info("Lua rule switch not changed")
                rt = {
                    'save_success': False,
                    'error_msg': _("The new submission is the same as the current one.")
                }

        except Exception as e:
            logger.exception(e)
            rt = {
                'save_success': False,
                'error_msg': _('Failed')
            }
        finally:
            operation = 'Enable' if switch else 'Disable'
            record_operation_log(request, rt['save_success'], ugettext_noop(operation), 'UBBv2 advance rules', '')

    return HttpResponse(json.dumps(rt), content_type=CONTENT_TYPE_JSON)


# =============================== timer ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def set_timer(request):
    rt = {
        'save_success': True,
        'error_msg': ''
    }
    if request.method == 'POST':
        timer = {}
        try:
            data = json.loads(request.body)
            rule_type = data.get('rule_type', None)
            timer['id'] = data.get('rule_id', 0)
            ubb_util.receive_and_validate_timer(timer, data)

            zk_paths = {
                'COUNTER': ZkClient.PATH_LUA_COUNTER_LIST,
                'BLACK': ZkClient.PATH_LUA_BLACKLIST_LIST,
                'HIGH_FREQ': ZkClient.PATH_LUA_HIGH_FREQ_LIST,
                'BAD_BEHAVIOR': ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST,
                'REPUTATION': ZkClient.PATH_LUA_REPUTATION_LIST,
                'NEW_COMER': ZkClient.PATH_LUA_NEW_COMER_LIST,
                'SNIPING': ZkClient.PATH_LUA_SNIPING_LIST,
                'APP_MANUAL': ZkClient.PATH_LUA_MUBB_RULE,
                'APP_COUNTER': ZkClient.PATH_LUA_MUBB_COUNTER_LIST,
                'APP_BLACK': ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST,
                'APP_HIGH_FREQ': ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST,
                'APP_REPUTATION': ZkClient.PATH_LUA_MUBB_REPUTATION_LIST,
                'MPP_UBB_MANUAL': ZkClient.PATH_LUA_MPPUBB_RULE

            }
            if rule_type in zk_paths.keys():
                rules_in_zk = AspConfCtrl.get_zk_lua_info(zk_paths[rule_type])
            else:
                raise Exception('Invalid rule type: {} '.format(rule_type))

            rules = [] if not rules_in_zk else json.loads(rules_in_zk, encoding='utf-8')
            if rules:
                position = [i for i, value in enumerate(rules) if value['id'] == timer['id']]
                if position:
                    rules[position[0]].update(timer)
                    err, err_massage = update_rule(rule_type, rules)
                    if err:
                        raise Exception(err_massage)
                else:
                    raise Exception('Invalid request: rule {} does not exist'.format(timer['id']))

            else:
                raise Exception('Invalid request: There is not any rules in zk')

            record_operation_log(request, rt['save_success'], ugettext_noop('Update'), ugettext_noop('trigger timer'),
                                 timer['id'])

        except Exception as e:
            logger.exception(e)
            rt = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, rt['save_success'], ugettext_noop('Modify'), ugettext_noop('trigger timer'),
                                 '')

    return HttpResponse(json.dumps(rt), content_type=CONTENT_TYPE_JSON)


# =============================== adv_filter code ===================================================
@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def get_adv_filter_lua_code(request):
    rt = None

    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            adv_filter = data.get('advfilter_code', None)
            app_or_web = data.get('ubb_scene_type', 'web')

            rt, fields_set = get_advfilter_lua_code(adv_filter, app_or_web, operation='check')

            # delete duplicate excape word
            rt = rt.replace('%%', '%')
            result = {
                'result': 'ok',
                'message': '',
                'lua_code': rt
            }
        except Exception as e:
            logger.exception(e)
            msg = _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            logger.error(msg)
            result = {
                'result': 'failed',
                'message': msg,
                'lua_code': rt
            }

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== others ===================================================
def validate_rule(rule, check_func_dict, scene_name):
    for k, v in rule.items():
        if k in check_func_dict:
            try:
                check_func_dict[k](v)
            except Exception as e:
                logging.error(e.message)
                raise Exception(_('{0} Scene Error: key({}) = v({}), msg:{}').format(scene_name, k, v, e.message))

        elif k not in ('ubb_scene_type',):
            raise Exception(_('key:{0} is not used in {1} scene').format(k, scene_name))


def ubb_host_filter(host):
    wildcard = ['_']

    if host:
        for i in wildcard:
            last_underscore_index = host.rfind("_") 
            if last_underscore_index != -1:  
                host = host[:last_underscore_index] 

    return host


# =============================== common functions about rule ===================================================
def get_and_validate_ubb_filters(data, app_or_web):
    ubb_filters = data.get('filter_conditions', [])
    is_advfilter = data.get('is_advfilter', False)

    if is_advfilter:
        get_advfilter_lua_code(ubb_filters, app_or_web, operation='check')

    else:
        if len(ubb_filters) > MAX_FILTER_ITEMS:
            raise Exception(_('The rule filter number shall never exceed the maximum: 10'))
        for one_filter in ubb_filters:
            is_valid_one_ubb_filter(one_filter, app_or_web)

    return ubb_filters, is_advfilter


def is_valid_one_ubb_filter(one_filter, app_or_web):
    value = one_filter.get('value', None)
    operation = one_filter.get('operation', None)
    key_type = one_filter.get('type', None)
    key = one_filter.get('key', None)

    if not (key_type and key and operation):
        raise Exception(_('The type, field and operator of the filter cant be empty'))

    ubb_util.ubb_invalid_string(key, length=255)

    if key_type == 'number':
        ubb_util.validate_filter_type_num(value, key, app_or_web)
        ubb_util.ubb_invalid_str_option(operation, ['>', '<', '~=', '==', '>=', '<='])

    elif key_type == 'boolean':
        ubb_util.validate_filter_type_bool(value, key, app_or_web)
        ubb_util.ubb_invalid_str_option(operation, ['='])

    elif key_type == 'string':
        if operation == 'empty':
            if value:
                raise Exception(_('The length of {} should be 0').format(key))

        else:
            ubb_util.validate_filter_type_string(value, key, app_or_web)
            ubb_util.ubb_invalid_str_option(operation, ['include', 'exclude', 'full_match', 'start_with', 'end_with',
                                                        'regular_exp', 'in_list', 'not_in_list'])

        if operation == 'regular_exp':
            ubb_util.ubb_validate_reg_ex(value)

        if operation in ['in_list', 'not_in_list']:
            # in_list(ua_os, 'ua_list.txt')
            ubb_util.valid_filter_type_list_resource_file(value)

    elif key_type == 'enum':
        ubb_util.validate_filter_type_enum(value, key, app_or_web)
        ubb_util.ubb_invalid_str_option(operation, ['==', '~='])

    elif key_type == 'ip':
        ubb_util.ubb_invalid_str_option(operation, ['~=', '=', 'in_list', 'not_in_list'])
        if operation in ['in_list', 'not_in_list']:
            # in_list(local_ip, 'ip_list.txt')
            ubb_util.valid_filter_type_list_resource_file(value)
        else: # [~=, =] :eg: local_ip = ******* or, local_ip=*******,*******,*******
            ubb_util.validate_filter_type_ip(value, key, app_or_web)
    else:
        raise Exception(_('Invalid data type'))


def save_all_rules_status(rules, enable, scene):
    is_find = False
    for rule in rules:
        if rule.get('enable') != enable:
            is_find = True
            rule['enable'] = enable

    if is_find:
        err, error_msg = update_rule(scene, rules)
        if not err:
            result = {
                'save_success': True,
                'error_msg': ''
            }
        else:
            result = {
                'save_success': False,
                'error_msg': error_msg
            }
    else:
        txt = _('Enable')
        if not enable:
            txt = _('Disable')
        result = {
            'save_success': False,
            'error_msg': _('Rules in current scene are {}').format(txt)
        }

    return result


def save_rule_status(rules, rule_id, enable, scene):
    is_find = False
    status = {
        True: _('Open'),
        False: _('Close')
    }
    result = {
        'save_success': False,
        'error_msg': _('Rule {} is already {}').format(rule_id, status[enable])
    }

    for rule in rules:
        if rule.get('id', None) == rule_id:
            if rule.get('enable') != enable:
                is_find = True
                rule['enable'] = enable
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Rule {} is already {}').format(rule_id, status[enable])
                }
            break

    if is_find:
        err, error_msg = update_rule(scene, rules)
        if not err:
            result = {
                'save_success': True,
                'error_msg': ''
            }
        else:
            result = {
                'save_success': False,
                'error_msg': error_msg
            }

    return result


def delete_rule(rules, rule_id, scene):
    result = {
        'save_success': False,
        'error_msg': 'Invalid rule ID'
    }
    for rule in rules:
        if rule.get('id', None) == rule_id:
            rules.remove(rule)

            err, error_msg = update_rule(scene, rules)
            if not err:
                result = {
                    'save_success': True,
                    'error_msg': ''
                }
            else:
                result = {
                    'save_success': False,
                    'error_msg': error_msg
                }
            break

    return result


def save_rule(current_rules, new_rule, rule_scene):
    position = [i for i, value in enumerate(current_rules) if value['id'] == new_rule['id']]

    # modify the rule
    if position:
        current_rules[position[0]] = new_rule
        operation = 'Update'
    # add the rule
    else:
        current_rules.append(new_rule)
        operation = 'Add'

    err, error_msg = update_rule(rule_scene, current_rules)
    if not err:
        result = {
            'save_success': True,
            'error_msg': ''
        }
    else:
        result = {
            'save_success': False,
            'error_msg': error_msg
        }

    return operation, result


def sort_dict_list_by_id_list(dict_list, id_list):
    """
    sort_dict_list_by_id_list
    
    :param dict_list: dict list. id is the key in one dict
    :param id_list: input ordered id list 
    :return: ordered_dict_list
    :raises: ValueError invalid len or key 
    """
    # 检查长度是否一致
    if len(dict_list) != len(id_list):
        raise ValueError("different list len (rule_list: %d, id_list: %d)" % 
                        (len(dict_list), len(id_list)))
    
    # create id dict
    id_to_dict = {}
    for d in dict_list:
        if 'id' not in d:
            raise ValueError("Missing id: %s" % str(d))
        id_to_dict[d['id']] = d 
    
    # check id is valid
    dict_ids = set(id_to_dict.keys()) 
    list_ids = set(id_list)
    if dict_ids != list_ids:
        missing_in_dict = list_ids - dict_ids
        missing_in_list = dict_ids - list_ids 
        raise ValueError("ID not matched \n rule missing ID: %s\n ids list missing ID: %s" % 
                        (sorted(missing_in_dict), sorted(missing_in_list)))
    
    # order the dict list by id list
    return [id_to_dict[id_] for id_ in id_list]


def update_rule(rule_scene, rules):
    from asp_utils.zk_client import ZkClient
    nginx_conf = NginxConf()

    rule_paths = {
        # scene_name : (saving_rule_path, trigger_rule_update_path)
        'MANUAL': (ZkClient.PATH_LUA_RULE, 'nginx/lua_env/lua_code_change'),
        'COUNTER': (ZkClient.PATH_LUA_COUNTER_LIST, 'nginx/lua_env/counter_scene'),
        'BLACK': (ZkClient.PATH_LUA_BLACKLIST_LIST, 'nginx/lua_env/blacklist_scene'),
        'HIGH_FREQ': (ZkClient.PATH_LUA_HIGH_FREQ_LIST, 'nginx/lua_env/high_freq_scene'),
        'BAD_BEHAVIOR': (ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST, 'nginx/lua_env/bad_behavior_scene'),
        'REPUTATION': (ZkClient.PATH_LUA_REPUTATION_LIST, 'nginx/lua_env/reputation_scene'),
        'NEW_COMER': (ZkClient.PATH_LUA_NEW_COMER_LIST, 'nginx/lua_env/new_comer_scene'),
        'SNIPING': (ZkClient.PATH_LUA_SNIPING_LIST, 'nginx/lua_env/sniping_scene'),
        'APP_MANUAL': (ZkClient.PATH_LUA_MUBB_RULE, 'nginx/lua_env/app_manual'),
        'APP_COUNTER': (ZkClient.PATH_LUA_MUBB_COUNTER_LIST, 'nginx/lua_env/app_counter_scene'),
        'APP_BLACK': (ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST, 'nginx/lua_env/app_blacklist_scene'),
        'APP_HIGH_FREQ': (ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST, 'nginx/lua_env/app_high_freq_scene'),
        'APP_REPUTATION': (ZkClient.PATH_LUA_MUBB_REPUTATION_LIST, 'nginx/lua_env/app_reputation_scene'),
        'MPP_UBB_MANUAL': (ZkClient.PATH_LUA_MPPUBB_RULE, 'nginx/lua_env/mpp_manual'),
        'SITE_SHIELD': (ZkClient.PATH_LUA_SITE_SHIELD_LIST, 'nginx/lua_env/ubb_site_shield')
    }

    saving_rule_path = 0
    trigger_rule_update_path = 1

    rules_to_save = rules if rule_scene == 'MANUAL' or rule_scene == 'APP_MANUAL' or rule_scene == 'MPP_UBB_MANUAL' else json.dumps(rules)
    # update rules in Zookeeper(/lua)
    rt = BaseConf.set_global_zk_values([(rule_paths[rule_scene][saving_rule_path], rules_to_save)])

    if rt:
        timestamp = '{:.6f}'.format(time.time())
        # update timestamp in Zookeeper(/shared_conf)
        result, exception, err_massage = nginx_conf.set_lua_ubb_rule_timestamp(
            rule_paths[rule_scene][trigger_rule_update_path], timestamp)
    else:
        # Saving rules in Zookeeper(/lua): failed
        result = -1
        err_massage = _('Error: Fail to save rule in Zookeeper')

    if result != -1:
        logging.debug("rules are updated successfully")
        err = False

    else:
        logging.debug("Updating rules is failed !")
        logging.error('err_massage: {}'.format(err_massage))
        err = True

    return err, err_massage


# =============================== about auto list and black ip ===================================================
def clean_shm(data):
    result = {
        'save_success': True,
        'error_msg': ''
    }

    try:
        result_code, _, error_msg = service_mgr_set_command('clean_ubbv2_shm', data)
        if result_code != 0:
            result['save_success'] = False
            result['error_msg'] = _("No Response")
            logging.error("remove_auto_list: notify nginx failed.response : {}".format(error_msg))

    except Exception as e:
        logging.exception('remove_auto_list: failed to request {} exception:{}'.format(data, str(e)))
        result['error_msg'] = str(e)
        result['save_success'] = False

    return result


def is_ipv6(ip):
    try:
        socket.inet_pton(socket.AF_INET6, ip)
    except socket.error:  # not a valid ip
        return False
    return True


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def remove_black_ip(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            ip_black = data.get('ip_black', None)

            ipc = re.compile(r'^\d+\.\d+\.\d+\.\d+/24$')
            ip = re.compile(r'^\d+\.\d+\.\d+\.\d+$')
            ipv6 = is_ipv6(ip_black)

            result = {
                'save_success': False,
                'error_msg': _('Invalid IP')
            }

            if ip_black:
                invalid_ip = False
                if ip_black == '0.0.0.0':
                    target_type = 'auto_ip_all'
                elif ip.findall(ip_black) or ipv6:
                    target_type = 'auto_ip'
                elif ipc.findall(ip_black):
                    target_type = 'auto_ipc'
                else:
                    invalid_ip = True

                if not invalid_ip:
                    send_data = {
                        'type': target_type,
                        'value': ip_black,
                    }
                    rt = clean_shm(send_data)
                    if rt.get('save_success', False):
                        result = rt
                        record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                             ugettext_noop('Auto_black_ip'), ip_black)

                    else:
                        record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                             ugettext_noop('Auto_black_ip'), ip_black)

                        logger.error('remove_black_ip error: {0}'.format(rt))
                        result['error_msg'] = _("Failed to remove auto black IP")
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def remove_black_fp(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            fp_black = data.get('fp_black')

            result = {
                'save_success': False,
                'error_msg': _('Invalid FP')
            }

            data = {
                'type': 'auto_fp',
                'value': fp_black
            }
            rt = clean_shm(data)
            if rt.get('save_success', False):
                result = rt
                record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                     ugettext_noop('Auto_black_fp'), fp_black)
            else:
                record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                     ugettext_noop('Auto_black_fp'), fp_black)
                result['error_msg'] = _("Failed to remove auto black FP")
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def remove_black_account(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            account_black = data.get('account_black')

            result = {
                'save_success': False,
                'error_msg': _('Invalid Account')
            }

            data = {
                'type': 'auto_account',
                'value': account_black
            }
            rt = clean_shm(data)
            if rt.get('save_success', False):
                result = rt
                record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                     ugettext_noop('Auto_black_account'), account_black)
            else:
                record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                     ugettext_noop('Auto_black_account'), account_black)
                result['error_msg'] = _("Failed to remove auto black Account")
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def remove_black_account_hostname(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            account_hostname_black = data.get('account_hostname_black')

            result = {
                'save_success': False,
                'error_msg': _('Invalid Account and Hostname')
            }

            data = {
                'type': 'auto_account_hostname',
                'value': account_hostname_black
            }
            rt = clean_shm(data)
            if rt.get('save_success', False):
                result = rt
                record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                     ugettext_noop('Auto_black_Accout_Hostname'), account_hostname_black)
            else:
                record_operation_log(request, result['save_success'], ugettext_noop('Remove'),
                                     ugettext_noop('Auto_black_Accout_Hostname'), account_hostname_black)
                result['error_msg'] = _("Failed to remove auto black Account_Hostname")
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== about share memory ===================================================

def get_shm_lru_timestamp(name_of_shm):
    try:
        conf = ConfDb()
        if not conf.is_proxy():
            result = {
                'is_success': False,
                'error_msg': _("This server is not the Proxy server")
            }
            return result

        server_ip = conf.get_admin_ip()
        server_port = conf.get_web_port()
        if not server_ip or not server_port:
            logging.error('clean_ubbv2_shm: failed to query server info')
            raise Exception(_("Cant get the infomation of server"))
        path = '/lua/ubb/share_memory.lua'

        url = 'https://{0}:{1}{2}'.format(server_ip, server_port, path)
        shm_data = {
            'type': 'lru_timestamp',
            'value': name_of_shm
        }

        try:
            _create_unverified_https_context = ssl._create_unverified_context
        except AttributeError:
            # Legacy Python that doesn't verify HTTPS certificates by default
            pass
        else:
            # Handle target environment that doesn't support HTTPS verification
            ssl._create_default_https_context = _create_unverified_https_context

        request = urllib2.Request(url, data=urllib.urlencode(shm_data))
        response = urllib2.urlopen(request)
        rsp = response.read()
        if rsp:
            rsp = json.loads(rsp)
            is_success = rsp.get('save_success')
            timestamp = rsp.get('timestamp')
            time_dif = rsp.get('time_dif')
            message = rsp.get('error_msg')
            if is_success:
                if 60 * 60 * 24 > time_dif:
                    level = 'less_than_a_day'
                elif 60 * 60 * 24 * 30 > time_dif:
                    level = 'more_than_a_day'
                else:
                    level = 'more_than_a_month'
                    timestamp = None

                result = {
                    'is_success': True,
                    'level': level,
                    'timestamp': timestamp
                }
            else:
                result = {
                    'is_success': False,
                    'error_msg': message,
                }
            return result
        else:
            raise Exception(_("No Response from share memory"))
    except Exception as e:
        logging.error('get_shm_lru_timestamp Exception: {}'.format(e))
        result = {
            'is_success': False,
            'error_msg': e.message,
        }
        return result


def pop_unused_field_in_dict(target_dict, unused_field_list):
    try:
        for field in unused_field_list:
            target_dict.pop(field, None)
    except Exception as e:
        logger.error(e)


def parse_shm_dict(shm_dict):
    """
    去除共享内存字典中不需要的字段，解析生成共享内存大小和备注信息的字典
    @param shm_dict: shm_dict:{'shm_name':{'is_built_in':True,'level':,'timestamp':,'size':100M,'description':'description'}}
    @return: shm_info_dict: {'shm_name1':{'is_built_in':True,'description':'this is description'},'shm_name2':{}...}
             shm_size_dict: {'shm_name1':'10M','shm_name2':'15M'}
    """
    for shm_name in shm_dict:
        pop_unused_field_in_dict(shm_dict[shm_name], ['timestamp', 'level'])
    shm_size_dict = {shm_name: shm.pop('size', None) for (shm_name, shm) in shm_dict.items()}
    shm_info_dict = shm_dict
    return shm_info_dict, shm_size_dict


def get_dict_single_changes(old_dict, new_dict):
    """
    1.判断字典是否改变（添加、删除、修改），找出改变的键值对所对应的键值
    2.默认new_dict相对old_dict只有一个键值对改变
    @param old_dict:
    @param new_dict:
    @return:
    """

    def generate_changed_result_dict(changed_flag=False, operation_type='', changed_key=''):
        return {'changed_flag': changed_flag,
                'operation_type': operation_type,
                'changed_key': changed_key
                }

    if old_dict == new_dict:
        return generate_changed_result_dict()

    old_dict_key_set = set(old_dict.keys())
    new_dict_key_set = set(new_dict.keys())

    if old_dict_key_set == new_dict_key_set:
        for key in old_dict.keys():
            if old_dict[key] != new_dict[key]:
                return generate_changed_result_dict(True, 'modify', key)

    old_diff_new = old_dict_key_set - new_dict_key_set
    new_diff_old = new_dict_key_set - old_dict_key_set
    empty_set = set()
    if old_diff_new == empty_set:
        return generate_changed_result_dict(True, 'Add', new_diff_old.pop())
    if new_diff_old == empty_set:
        return generate_changed_result_dict(True, 'Delete', old_diff_new.pop())

    return generate_changed_result_dict(True, 'Modify', new_diff_old.pop())


def first_time_add_or_modify(new_shm_size_dict):
    # 首次添加或修改共享内存
    default_shm_size_dict = {shm_name: shm['size'] for (shm_name, shm) in default_share_dict.items()}

    if len(new_shm_size_dict) > len(default_shm_size_dict):
        operation_type = 'Add'
        changed_shm_name = (set(new_shm_size_dict.keys()) - set(default_shm_size_dict.keys())).pop()
    else:
        operation_type = 'Modify'
        changed_result = get_dict_single_changes(default_shm_size_dict, new_shm_size_dict)
        changed_shm_name = changed_result['changed_key']

    return operation_type, changed_shm_name


def update_share_memory_setting(new_shm_dict):
    """
    更新ubb申请的动态内存
    @param new_shm_dict: {'shm_name1':{'is_built_in':True,'size':'10M','description':'','timestamp':'','level':''},'shm_name2':{}...}
    @return: update_shm_result_dict
    """

    def generate_update_shm_result_dict(save_success=False, error_msg='', operation_type='', changed_shm_name=''):
        return {'save_success': save_success,
                'error_msg': error_msg,
                'operation_type': operation_type,
                'changed_shm_name': changed_shm_name
                }

    is_invalid, error_msg = _share_memory_invalid(new_shm_dict)
    if is_invalid:
        return generate_update_shm_result_dict(False, error_msg, '', '')

    nginx_conf = NginxConf()
    conf_shm_info_dict = nginx_conf.get_lua_share_memory_info()
    conf_shm_size_dict = nginx_conf.get_lua_share_memory_size()
    conf_shm = get_share_memory_setting(conf_shm_size_dict, conf_shm_info_dict)
    old_shm_info_dict, old_shm_size_dict = parse_shm_dict(conf_shm)

    new_shm_info_dict, new_shm_size_dict = parse_shm_dict(new_shm_dict)

    # 首次添加或修改共享内存，old_shm_size_dict和old_shm_info_dict为空
    if not old_shm_size_dict or not old_shm_info_dict:
        operation_type, changed_shm_name = first_time_add_or_modify(new_shm_size_dict)

    else:
        shm_size_changes = get_dict_single_changes(old_shm_size_dict, new_shm_size_dict)
        shm_info_changes = get_dict_single_changes(old_shm_info_dict, new_shm_info_dict)
        # shm_size或shm_info改变
        if shm_size_changes['changed_flag'] or shm_info_changes['changed_flag']:
            operation_type = shm_size_changes['operation_type'] if shm_size_changes['changed_flag'] else \
                shm_info_changes['operation_type']
            changed_shm_name = shm_size_changes['changed_key'] if shm_size_changes['changed_flag'] else \
                shm_info_changes['changed_key']
        # 没有改变，直接返回结果，不提交更改
        else:
            error_msg = _('The new submission is the same as the current one.')
            logger.info(error_msg)
            return generate_update_shm_result_dict(False, error_msg, '', '')

    ret, useless, error_msg = nginx_conf.set_lua_share_memory_size(new_shm_size_dict)
    if not ret:
        ret, useless, error_msg = nginx_conf.set_lua_share_memory_info(new_shm_info_dict)
    return generate_update_shm_result_dict(not ret, error_msg, operation_type, changed_shm_name)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_dict_share_data(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            share_dict = data.get('shareDict', [])

            update_shm_result_dict = update_share_memory_setting(share_dict)
            if update_shm_result_dict.get('save_success', False):
                result = {
                    'save_success': update_shm_result_dict['save_success'],
                    'error_msg': update_shm_result_dict['error_msg']
                }
            else:
                logger.error('save_dict_share_data error: {0}'.format(update_shm_result_dict))
                result = {
                    'save_success': False,
                    'error_msg': _('Fail to set shared dict.')
                }
            record_operation_log(request, result['save_success'],
                                 ugettext_noop(update_shm_result_dict['operation_type']),
                                 ugettext_noop('share memory'), update_shm_result_dict['changed_shm_name'])
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }

            record_operation_log(request, result['save_success'], '',
                                 ugettext_noop('share memory'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_dict_share_data(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            share_memory_info = data.get('shareDict', {})

            update_shm_result_dict = update_share_memory_setting(share_memory_info)
            if update_shm_result_dict.get('save_success', False):
                result = {
                    'save_success': update_shm_result_dict['save_success'],
                    'error_msg': update_shm_result_dict['error_msg']
                }
            else:
                logger.error('delete_dict_share_data error: {0}'.format(update_shm_result_dict))
                result = {
                    'save_success': False,
                    'error_msg': _('Fail to set shared dict.')
                }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('share memory'), update_shm_result_dict['changed_shm_name'])
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('share memory'), '')
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def flush_share_memory(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    # use translate
    ugettext_noop('reset share memory')

    if request.method == "POST":
        try:
            data = json.loads(request.body)
            name_of_shm = data.get('shareDict', None)
            nginx_conf = NginxConf()
            old_shm = nginx_conf.get_lua_share_memory_size()

            if name_of_shm not in old_shm:
                if name_of_shm not in default_share_dict:
                    raise Exception(_('Share memory {0} dose not exist').format(name_of_shm))
            data = {
                'type': 'common_shm',
                'value': name_of_shm
            }
            result = clean_shm(data)
            record_operation_log(request, result['save_success'], ugettext_noop('Recover'),
                                 ugettext_noop('share memory'),
                                 name_of_shm)

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': e.message
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Recover'),
                                 ugettext_noop('share memory'),
                                 '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def get_ubbv2_shm_free_space(request):
    if request.method == "POST":
        result = {
            'is_success': False,
            'error_msg': _('Fail to query share memory')
        }
        try:
            data = json.loads(request.body)
            shm_list = data.get('share_memory', None)
            shm_str = ','.join(shm_list)

            conf = ConfDb()
            if not conf.is_proxy():
                result = {
                    'is_success': False,
                    'error_msg': _('This server is not the Proxy server')
                }
                raise Exception(_("This server is not the Proxy server"))

            server_ip = conf.get_admin_ip()
            server_port = conf.get_web_port()
            if not server_ip or not server_port:
                logging.error('clean_ubbv2_shm: failed to query server info')
                result = {
                    'is_success': False,
                    'error_msg': _('Fail to query server information')
                }
                raise Exception(_('Fail to query server information'))

            path = '/lua/ubb/share_memory.lua'

            url = 'https://{0}:{1}{2}'.format(server_ip, server_port, path)

            shm_data = {
                'type': 'free_space',
                'value': shm_str
            }

            try:
                _create_unverified_https_context = ssl._create_unverified_context
            except AttributeError:
                # Legacy Python that doesn't verify HTTPS certificates by default
                pass
            else:
                # Handle target environment that doesn't support HTTPS verification
                ssl._create_default_https_context = _create_unverified_https_context

            shm_request = urllib2.Request(url, data=urllib.urlencode(shm_data))
            response = urllib2.urlopen(shm_request)
            rsp = response.read()
            if rsp:
                rsp = json.loads(rsp)
                is_success = rsp.get('save_success')
                share_dic = rsp.get('shm_free_space')
                error_msg = rsp.get('error_msg')

                result = {
                    'is_success': is_success,
                    'share_memory': share_dic,
                    'error_msg': error_msg
                }
                record_operation_log(request, result['is_success'], ugettext_noop('Query'),
                                     ugettext_noop('share memory'), '')

            else:
                result = {
                    'is_success': False,
                    'error_msg': _("No Response from share memory")
                }
                raise Exception(_("No Response from share memory"))

        except Exception as e:
            logging.error('get_ubbv2_shm_free_space Exception: {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Query'), ugettext_noop('share memory'),
                                 '')
        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== app counter scene ===================================================
APP_ACTIONS = ['block_code', 'pass', 'transpare', 'forward', 'none']


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_counter_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)

            rule = dict()
            # rule base info
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')

            # trigger condition
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)

            rule['key'] = data.get('key', None)
            rule['key_value'] = data.get('key_value', None)
            rule['counter'] = data.get('counter', None)
            rule['condition'] = data.get('condition', {})

            # action
            rule['percent'] = data.get('percent', 0)
            rule['action_list'] = data.get('action_list', [])

            # auto_list
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)

            # ubb counter
            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'app'

            is_valid_app_counter_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            counter_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_COUNTER_LIST)
            counter_rules = [] if not counter_rules else json.loads(counter_rules, encoding='utf-8')
            operation, result = save_rule(counter_rules, rule, 'APP_COUNTER')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP counter rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP counter rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_app_counter_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid app counter rule :{}'.format(rule))

    check_func_dict = {
        "dev_type": ubb_util.ubb_invalid_dev_type,
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=101999, min_v=101100)),
        "action_list": (lambda x: ubb_util.ubb_invalid_action_list(x, action_option=APP_ACTIONS)),
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, APP_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in APP_AUTO_LIST_TYPE)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.STATISTIC_KEY_RANGE['app'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "key_value": (lambda x: ubb_util.ubb_invalid_fields_list(x, rule['key'], rule['ubb_scene_type'])),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "counter": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.COUNTER_ID_RANGE)),
        "condition": ubb_util.ubb_invalid_counter_condition,
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, _('APP Counter'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_app_counter_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_COUNTER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'APP_COUNTER')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP counter rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP counter rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_counter_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_COUNTER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            result = save_rule_status(rules, rule_id, enable, 'APP_COUNTER')
            operation = ugettext_noop('Enable') if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP counter rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP counter rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_app_counter_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            scene = data.get('type')
            enable = data.get('enable')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_COUNTER_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'APP_COUNTER' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all APP counter rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all APP counter rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== app black list  ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_black_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)

            # {'upstream_key':'*******_80','id': 2002, 'enable': True, 'host': 'www.c.com', 'path': '/lua', 'key': 'cookie_id',
            #                     'key_value': '**************',
            #                     'dev_type': ['mobile', 'mac'], 'percent': 80,
            #                       'action_list':[{"action":"block_code", "action_value":400,     "delay":0, "weight": 1}]
            #                        or    'action': 'redirect',  'action_value': 'page.html', 'delay': 10
            # }
            rule = dict()
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['key'] = data.get('key', None)
            rule['key_value'] = data.get('key_value', None)
            rule['percent'] = data.get('percent', 0)
            rule['action_list'] = data.get('action_list', [])
            rule['is_path_regx'] = data.get('is_path_regx', False)

            # auto_list
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)

            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'app'

            if rule['key'] != 'ip':
                rule['is_key_regx'] = data.get('is_key_regx', False)
            is_valid_app_blacklist_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            black_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST)
            black_rules = [] if not black_rules else json.loads(black_rules, encoding='utf-8')
            operation, result = save_rule(black_rules, rule, 'APP_BLACK')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP blacklist rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP blacklist rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_app_blacklist_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid blacklist rule :{}'.format(rule))

    check_func_dict = {
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=102999, min_v=102000)),
        "action_list": (lambda x: ubb_util.ubb_invalid_action_list(x, action_option=APP_ACTIONS)),
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, APP_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in APP_AUTO_LIST_TYPE)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.BLACKLIST_KEY_RANGE['app'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "key_value": (lambda x: ubb_util.ubb_invalid_string(x, length=100000)),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "is_key_regx": ubb_util.ubb_invalid_bool,
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }
    validate_rule(rule, check_func_dict, _('APP Blacklist'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_app_black_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'APP_BLACK')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP blacklist rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP blacklist rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_black_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            result = save_rule_status(rules, rule_id, enable, 'APP_BLACK')
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP blacklist rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP blacklist rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_app_black_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            scene = data.get('type')
            enable = data.get('enable')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'APP_BLACK' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all APP blacklist rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all APP blacklist rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== app high frequency ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_high_freq_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule = dict()
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['key'] = data.get('key', None)
            rule['key_value'] = data.get('key_value', None)
            rule['high_freq'] = data.get('high_freq', None)
            rule['percent'] = data.get('percent', None)
            rule['action_list'] = data.get('action_list', [])
            rule['auto_list'] = data.get('auto_list', 0)
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            freq_limit_type = data.get('freq_limit_type', None)
            rule['freq_limit_type'] = freq_limit_type
            if freq_limit_type == 'time_window':
                rule['time_window_type'] = data.get('time_window_type', None)
                rule['time_window_value'] = data.get('time_window_value', -1)
            else:
                rule['burst'] = data.get('burst', 2)

            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'app'

            is_valid_app_high_freq_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            high_frep_rules_list = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST)
            high_frep_rules_list = [] if not high_frep_rules_list else json.loads(high_frep_rules_list,
                                                                                  encoding='utf-8')
            operation, result = save_rule(high_frep_rules_list, rule, 'APP_HIGH_FREQ')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP high frequent rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP high frequent rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_app_high_freq_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid high freq rule:{}.'.format(rule))

    check_func_dict = {
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=103000, max_v=103999)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "high_freq": (lambda x: ubb_util.ubb_invalid_number_range(x, max_v=65535, min_v=0.001)),
        "action_list": (lambda x: ubb_util.ubb_invalid_action_list(x, action_option=APP_ACTIONS)),
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, APP_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in APP_AUTO_LIST_TYPE)),
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ubb_util.STATISTIC_KEY_RANGE['app'])),
        "key_value": (lambda x: ubb_util.ubb_invalid_fields_list(x, rule['key'], rule['ubb_scene_type'])),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "freq_limit_type": (lambda x: ubb_util.ubb_invalid_str_option(x, ['rate', 'time_window'])),
        "time_window_type": (lambda x: ubb_util.ubb_invalid_str_option(x, ['minute', 'hour', 'day', 'second'])),
        "time_window_value": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=1, max_v=65535)),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "burst": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=0, max_v=65535)),
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))
    }

    validate_rule(rule, check_func_dict, 'APP High Freq')


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_app_high_freq_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'APP_HIGH_FREQ')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP high frequent rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP high frequent rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_high_freq_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = save_rule_status(rules, rule_id, enable, 'APP_HIGH_FREQ')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP high frequent rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP high frequent rule status'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_app_high_freq_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            scene = data.get('type')
            enable = data.get('enable')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'APP_HIGH_FREQ' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('Invalid rule status')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all APP high frequent rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all APP high frequent rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== app reputation ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_reputation_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule = dict()
            rule['enable'] = data.get('enable', None)
            rule['id'] = data.get('id', 0)
            rule['desc'] = data.get('desc', '')
            rule['is_host_regx'] = data.get('is_host_regx', False)
            rule['host'] = ubb_host_filter(data.get('host', '')) if not rule['is_host_regx'] else data.get('host', '')
            rule['path'] = data.get('path', None)
            rule['is_path_regx'] = data.get('is_path_regx', False)
            rule['auto_list_type'] = data.get('auto_list_type', None)
            rule['auto_list'] = data.get('auto_list', 0)
            rule['key'] = data.get('key', None)

            if rule['key'] not in ('ip', 'app_fp', 'user_name'):
                raise Exception('不合法的key:{}'.format(rule['key']))

            rule['threat_value_min'] = data.get('threat_value_min', 0)
            rule['threat_value_max'] = data.get('threat_value_max', 0)
            rule['threat_value'] = data.get('threat_value', 0)

            rule['percent'] = data.get('percent', None)
            rule['action_list'] = data.get('action_list', [])

            rule['statistic_counter'] = data.get('statistic_counter', [])
            rule['ubb_scene_type'] = 'app'
            is_valid_app_reputation_rule(rule)

            ubb_util.receive_and_validate_timer(rule, data)

            rule['filter_conditions'], rule['is_advfilter'] = get_and_validate_ubb_filters(data, rule['ubb_scene_type'])

            reputation_rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_REPUTATION_LIST)
            reputation_rules = [] if not reputation_rules else json.loads(reputation_rules, encoding='utf-8')
            operation, result = save_rule(reputation_rules, rule, 'APP_REPUTATION')
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP reputation rule'), rule['id'])

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _(e.args[0]).format(*e.args[1:]) if len(e.args) > 1 else _(e.message)
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP reputation rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def is_valid_app_reputation_rule(rule):
    if not rule or type(rule) != dict:
        raise Exception('Invalid bad reputation rule :{}'.format(rule))

    check_func_dict = {
        "id": (lambda x: ubb_util.ubb_invalid_int_range(x, min_v=105000, max_v=105999)),
        "action_list": (lambda x: ubb_util.ubb_invalid_action_list(x, action_option=APP_ACTIONS)),
        "percent": (lambda x: ubb_util.ubb_invalid_int_range(x, max_v=100, min_v=10, step=10)),
        "desc": (lambda x: ubb_util.ubb_invalid_string(x, length=MAX_DESC_LENGTH, min_lenth=0)),
        "enable": ubb_util.ubb_invalid_bool,
        "auto_list_type": (lambda x: ubb_util.ubb_invalid_str_option(x, APP_AUTO_LIST_TYPE)),
        "auto_list": (lambda x: ubb_util.ubb_invalid_auto_list(x, rule['auto_list_type'] in APP_AUTO_LIST_TYPE)),
        "path": ubb_util.ubb_invalid_path_host,
        "host": (lambda x: ubb_util.ubb_invalid_path_host(x, length=140)),
        "is_path_regx": ubb_util.ubb_invalid_bool,
        "is_host_regx": ubb_util.ubb_invalid_bool,
        "key": (lambda x: ubb_util.ubb_invalid_str_option(x, ['ip', 'app_fp', 'user_name'])),
        "threat_value_min": (
            lambda x: ubb_util.ubb_invalid_threat_value(x, max_v=100, min_v=0, valid_key=rule['key'] in (
                'ip', 'app_fp', 'user_name'))),
        "threat_value_max": (
            lambda x: ubb_util.ubb_invalid_threat_value(x, max_v=100, min_v=0, valid_key=rule['key'] in (
                'ip', 'app_fp', 'user_name'))),
        "threat_value": (lambda x: ubb_util.ubb_invalid_threat_value(x, max_v=64, min_v=0,
                                                                     valid_key=rule['key'] in (
                                                                         'ip', 'app_fp', 'user_name'))),
        "statistic_counter": (lambda counter: ubb_util.ubb_invalid_counter(counter, rule['ubb_scene_type']))

    }

    validate_rule(rule, check_func_dict, 'APP Reputation')

    if rule['key'] != 'user_name' and rule['threat_value_min'] > rule['threat_value_max']:
        raise Exception(_('The minimum of threat should be less than the maximum of threat'))


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_app_reputation_rule(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_REPUTATION_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = delete_rule(rules, rule_id, 'APP_REPUTATION')
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP reputation rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Delete'),
                                 ugettext_noop('APP reputation rule'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_app_reputation_rules_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            enable = data.get('enable')
            rule_id = data.get('id')
            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_REPUTATION_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')
            if rules:
                result = save_rule_status(rules, rule_id, enable, 'APP_REPUTATION')
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('The rule does not exist')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('APP reputation rule'), rule_id)
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('APP reputation rule status'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_all_app_reputation_status(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }
    if request.method == "POST":
        try:
            data = json.loads(request.body)
            scene = data.get('type')
            enable = data.get('enable')

            rules = AspConfCtrl.get_zk_lua_info(ZkClient.PATH_LUA_MUBB_REPUTATION_LIST)
            rules = [] if not rules else json.loads(rules, encoding='utf-8')

            if scene == 'APP_REPUTATION' and rules:
                result = save_all_rules_status(rules, enable, scene)
            else:
                result = {
                    'save_success': False,
                    'error_msg': _('The rule does not exist')
                }
            operation = 'Enable' if enable else 'Disable'
            record_operation_log(request, result['save_success'], ugettext_noop(operation),
                                 ugettext_noop('all APP reputation rules'), '')
        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }
            record_operation_log(request, result['save_success'], ugettext_noop('Modify'),
                                 ugettext_noop('status of all APP reputation rules'), '')

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


# =============================== operation on nginx error log  ===================================================

@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def ubb_clear_log(request):
    result = {
        'save_success': False,
        'error_msg': _('Invalid request')
    }

    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            index = data.get('index', 0)

            conf = WebconsoleConf()
            conf.set_ubb_query_log_index(index)

            result = {
                'save_success': True,
                'error_msg': ''
            }

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@check_permission('Programmable_Defending', 'write')
@allow_method('post')
def ubb_query_log(request):
    result = {
        'save_success': False
    }

    if request.method == "POST":
        try:
            data = json.loads(request.body)
            # type: BLACK / HIGH_FREQ / BAD_BEHAVIOR
            conf = WebconsoleConf()
            query_log_index = conf.get_ubb_query_log_index()

            start = data.get('index', 0)
            start = query_log_index if not start else start
            retrieve_log(start, result)

        except Exception as e:
            logger.exception(e)
            result = {
                'save_success': False,
                'error_msg': _('Failed')
            }

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)


def isLevelLog(line):
    log_levels = ["[debug]", "[info]", "[notice]", "[warn]", "[error]", "[crit]", "[alert]", "[emerg]"]
    rt = False

    for level in log_levels:
        if line.find(level) > 0:
            rt = True
            break

    return rt


def filterLevelLog(line, log_level="[error]"):
    log_levels = ["[debug]", "[info]", "[notice]", "[warn]", "[error]", "[crit]", "[alert]", "[emerg]"]
    rt = False

    log_level_index = log_levels.index(log_level)

    for level in log_levels:
        if line.find(level) > 0:
            current_level_index = log_levels.index(level)
            if current_level_index >= log_level_index:
                rt = True
            break

    return rt


def retrieve_log(start=0, results={}):
    fn = '/var/log/asp/nginx/error.log'
    MAX_BYTE = 1024 * 200  # about 1000 lines

    try:
        with open(fn, 'rb') as f:
            f.seek(0, 2)  # end
            length = f.tell()

            if length < start:  # log rotate
                start = 0
                conf = WebconsoleConf()
                conf.set_ubb_query_log_index(start)
            else:
                if length - start > MAX_BYTE:
                    start = length - MAX_BYTE

            if start == 0:
                start = length - MAX_BYTE  # 1000 lines
                if start < 0:
                    start = 0

            f.seek(start)
            content = f.read()

            pos = f.tell()
            content = content.decode('utf-8', 'ignore').encode('utf-8')
            content = content.splitlines()

            rt_content = []

            for i, line in enumerate(content):
                if line.find("[error]") < 0:
                    isLevelLogline = isLevelLog(line)
                    if len(rt_content) > 0 and not isLevelLogline:
                        last = rt_content.pop()
                        last += '\n' + line
                    else:
                        last = line
                    rt_content.append(last)
                else:
                    rt_content.append(line)

            rt_content = filter(lambda x: x.find("[lua]") >= 0, rt_content)
            rt_content = filter(filterLevelLog, rt_content)

        results.update({'save_success': True, 'errorCount': len(rt_content), 'logs': rt_content, 'index': pos})
    except Exception as e:
        results.update({'save_success': False, 'error_msg': str(e)})


# =============================== resource files upload  ===================================================
@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def upload_resource_file(request):
    result = {
        'is_success': False,
        'error_msg': ''
    }

    if request.method == "POST":
        file_type = request.POST.get('type')
        try:
            f = request.FILES['source_file']
            file_size = f.size
            file_name = f.name

            if file_size > ubb_util.MAX_UBB_RESOURCE_FILE_SIZE:
                raise Exception(_("Resource file size has exceeded the maximum"))

            file_content = f.read()

            file_info, file_info_list = ubb_util.prepare_resource_file(file_name, file_type, file_size, file_content)

            # write file content and zip resource file
            full_sync_path = ubb_util.zip_resource_file_folder(file_info['file_name'], file_info['type'], file_content)
            rt = BaseConf().sync_file(full_sync_path)

            if rt == RESULT.OK:
                rt1 = BaseConf.set_global_zk_values(
                    [(ZkClient.PATH_LUA_SOURCE_FILE + '/' + file_info['type'], json.dumps(file_info_list))])
                if not rt1:
                    logging.error('Update resource file {}:{} from zk failed.'.format(file_info['type'],
                                                                                      file_info['file_name']))
                    raise Exception(_('Sync resource file {}:{} from zk failed.').format(file_info['type'],
                                                                                         file_info[
                                                                                             'file_name']))

                result['is_success'] = True
                result['info'] = file_info
                record_operation_log(request, result['is_success'], ugettext_noop('Upload'),
                                     ugettext_noop(file_info['type']),
                                     file_info['file_name'])
            else:
                logging.error(
                    'Update resource file {}:{} failed.'.format(file_info.get('type'),
                                                                file_info.get('file_name')))
                raise Exception(_('Update resource file {}:{} failed.').format(file_info['type'],
                                                                               file_info['file_name']))

        except Exception as e:
            result['error_msg'] = _(e.message) or _('Update resource file failed.')
            logger.error('Upload resource file failed: {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Upload'),
                                 ugettext_noop(file_type),
                                 file_name)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def delete_resource_file(request):
    result = {
        'is_success': False,
        'error_msg': ''
    }
    if request.method == "POST":
        data = json.loads(request.body)
        file_name = data.get('file_name', None)
        file_type = data.get('file_type', None)

        try:
            if not file_name or not file_type:
                raise Exception(_('Cant find the resource file'))
            
            if ubb_util.is_built_in_resource_file(file_name, file_type):
                raise Exception(_('You do not have this permission. Please contact your administrator.'))

            # check and refresh file info-list on zk
            file_info_list = delete_resource_file_info(file_name, file_type)

            if os.path.isfile(TMP_RESOURCE_DIR):
                os.remove(TMP_RESOURCE_DIR)
            elif os.path.isdir(TMP_RESOURCE_DIR):
                shutil.rmtree(TMP_RESOURCE_DIR)

            if os.path.isdir(UBB_RESOURCE_FILE_FOLDER):
                shutil.copytree(UBB_RESOURCE_FILE_FOLDER, TMP_RESOURCE_DIR)

            file_type_path = TMP_RESOURCE_DIR + file_type
            file_path = file_type_path + '/' + file_name

            # delete file
            if os.path.isfile(file_path):
                os.remove(file_path)

            if len(ubb_util.get_resource_file_in_zk()) > 0:
                # build zip file
                full_sync_path = get_sync_file_path(UBB_ZIP_FILE_NAME)
                if os.path.isfile(full_sync_path):
                    os.remove(full_sync_path)

                with zipfile.ZipFile(full_sync_path, 'a') as zf:
                    for dirpath, dirnames, filenames in os.walk(TMP_RESOURCE_DIR):
                        fpath = dirpath.replace(TMP_RESOURCE_DIR, '')
                        for filename in filenames:
                            zf.write(os.path.join(dirpath, filename), os.path.join(fpath, filename))
            else:
                full_sync_path = get_sync_file_path('ubb_resource_nothing_left')
                if os.path.isfile(full_sync_path):
                    os.remove(full_sync_path)
                fd = open(full_sync_path, 'w')
                fd.close()

            rt = BaseConf().sync_file(full_sync_path)

            if rt == RESULT.OK:
                rt1 = BaseConf.set_global_zk_values(
                    [(ZkClient.PATH_LUA_SOURCE_FILE + '/' + file_type, json.dumps(file_info_list))])
                if not rt1:
                    logging.error('Delete resource file {}:{} from zk failed.'.format(file_type, file_name))
                    raise Exception(
                        _('Delete resource file {}:{} from zk failed.').format(file_type, file_name))

                result['is_success'] = True
                result['info'] = file_info_list
                record_operation_log(request, result['is_success'], ugettext_noop('Delete'), ugettext_noop(file_type),
                                     file_name)
            else:
                raise Exception(_('Fail to sync file in the cluster'))

        except Exception as e:
            result['error_msg'] = e.message or _('Delete resource file failed')
            logger.error('Delete resource file failed with {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Delete'), ugettext_noop(file_type),
                                 file_name)

        return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'read')
def download_resource_file(request):
    result = {
        'is_success': False,
        'error_msg': ''
    }
    if request.method == "GET":
        file_name = request.GET.get('file_name')
        file_type = request.GET.get('file_type')
        if not file_name or not file_type:
            raise Exception(_('Cant find the resource file'))

        try:
            file_info_list = ubb_util.get_resource_file_in_zk((file_type,))
            file_name_exists = False
            for file_exists in file_info_list:
                if file_exists['file_name'] == file_name:
                    file_name_exists = True
                    break

            if not file_name_exists:
                logging.error('Download resource file {} : {} not in zk.'.format(file_type, file_name))
                raise Exception(_('Cant find the {} name resource file').format(file_name))

            file_type_path = UBB_RESOURCE_FILE_FOLDER + '/' + file_type
            file_path = file_type_path + '/' + file_name

            if not os.path.isfile(file_path):
                logging.error('Download resource file {} : {} not in dir.'.format(file_type, file_name))
                raise Exception(_('Cant find the {} resource file: {}').format(file_type, file_name))

            response = FileResponse(open(file_path, 'rb'))
            response['content-type'] = 'application/octet-stream'
            response['Content-Disposition'] = 'attachment; filename="{}"'.format(file_name)
            result['is_success'] = True
            record_operation_log(request, result['is_success'], ugettext_noop('Download'), ugettext_noop(file_type),
                                 file_name)
            return response
        except Exception as e:
            result['error_msg'] = e.message or _('Fail to download resource file')
            logger.error('Download resource file failed with {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Download'), ugettext_noop(file_type),
                                 file_name)
            return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)


def delete_resource_file_info(file_name, file_type):
    file_index = -1
    file_info_list = ubb_util.get_resource_file_in_zk((file_type,))
    for i in range(0, len(file_info_list)):
        if file_info_list[i]['file_name'] == file_name:
            file_index = i
            break

    if file_index > -1:
        del file_info_list[file_index]

    else:
        logging.error('Delete resource file {}:{} does not exist.'.format(file_name, file_name))
        raise Exception(_('The resource file does not exist. Please try it again after reload it'))

    return file_info_list


# =============================== import/export ===================================================
@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
@adv_operation_code_required
def import_ubb_config(request):
    # TODO参考的def upload_resource_file 接口
    result = {
        'result': 'failed',
        'error': ''
    }

    if request.method == "POST":
        ubb_settings = {}
        try:
            f = request.FILES['ubb_file']
            file_size = f.size
            file_name = f.name
            # TODO上传逻辑
            config = f.read()

            ubb_settings = json.loads(config, encoding='utf-8')
            ubb_version = ubb_settings.get('version')
            if ubb_version != '1.0':
                raise Exception(_('The version of the imported file is not supported'))
            
            layout = ubb_settings.get('layout')
            if layout != get_layout():
                raise Exception(_('The layout of the imported file is not supported'))
            
            nginx_conf = NginxConf()
            conf_db = nginx_conf.get_conf()
            deploy = ubb_settings.get('deploy_mode')
            if deploy != conf_db.get_deploy_mode():
                raise Exception(_('The deploy mode of the imported file is not supported'))

            ubb_rules = ubb_settings.get('ubb_rules', {})
            ubb_config = ubb_settings.get('ubb_config', {})
            captcha_config = ubb_settings.get('captcha_config', {})


            ubb_config_exist = nginx_conf.get_lua_env()
            captcha_config_exist = nginx_conf.get_captcha_conf()

            import_lub_ubb_config(conf_db, ubb_rules)
            nginx_conf.set_lua_env(ubb_config, ubb_config_exist)
            nginx_conf.set_captcha_conf(captcha_config, captcha_config_exist)
            result['result'] = 'ok'
            record_operation_log(request, result['result'] == 'ok', ugettext_noop('Import'), file_name, '', ubb_settings)

        except Exception as e:
            result['error'] = _('Invalid upload file.')
            logger.error('Upload resource file failed: {}'.format(e))
            record_operation_log(request, result['result'] == 'ok', ugettext_noop('Import'),
                                 file_name, '', ubb_settings)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_PLAIN)


@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'read')
def export_ubb_config(request):
    # TODO参考的 def download_resource_file。导出ubb_file前端不需要传file_name 和 file_type
    """
        deploy_mode choices:
            0   unset
            1   inline
            2   mirror deploy
            3   transparent
            4   HA
            5   routeProxy
            6   plugin
            7   HARouteProxy
    """
    
    result = {
        'is_success': False,
        'error_msg': ''
    }

    if request.method == "GET":
        try:
            # TODO下载逻辑
            json_name = 'ubb_settings_{}.json'.format(time.strftime('%Y-%m-%d_%X', time.localtime(time.time())))
            ubb_rules = export_lub_ubb_config()
            nginx_conf = NginxConf()
            ubb_config = nginx_conf.get_lua_env()
            captcha_config = nginx_conf.get_captcha_conf()
            lua_ubb = {
                "version": '1.0',
                "layout": get_layout(),
                "deploy_mode": nginx_conf.get_conf().get_deploy_mode(),
                "ubb_rules": ubb_rules,
                "ubb_config": ubb_config,
                "captcha_config": captcha_config
            }

            response = HttpResponse(json.dumps(lua_ubb), content_type='application/json')
            response['Content-Disposition'] = "attachment; filename=%s" % (json_name)

            result['is_success'] = True
            record_operation_log(request, result['is_success'], ugettext_noop('Export'), json_name, '', lua_ubb)
            return response

        except Exception as e:
            result['error_msg'] = e.message or _('Export Exception')
            logger.error('Export  ubb_setting failed with {}'.format(e))
            record_operation_log(request, result['is_success'], ugettext_noop('Export'), json_name, '')
        
    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)

# =============================== ubb sort ===================================================
@has_dm_permission(LicenseInfo.DM_PROGRAMMABLE_DEFENDING)
@login_required
@check_permission('Programmable_Defending', 'write')
def save_sort(request):
    result = {
        'is_success': False,
        'error_msg': ''
    }

    if request.method == "POST":
        try:
            data = json.loads(request.body)
            scene = data.get('type')   # ubb场景类型: 参考def save_all_counter_rules_status中的type
            rule_ids = data.get('list')    # 列表

            scene_config = {
                 # scene_name : (saving_rule_path, 'translated_sene_name')
                'COUNTER': (ZkClient.PATH_LUA_COUNTER_LIST, 'counter rule'),
                'BLACK': (ZkClient.PATH_LUA_BLACKLIST_LIST,  'blacklist rule'),
                'HIGH_FREQ': (ZkClient.PATH_LUA_HIGH_FREQ_LIST, 'high frequent rule'),
                'BAD_BEHAVIOR': (ZkClient.PATH_LUA_BAD_BEHAVIOR_LIST, 'bad behavior rule'),
                'REPUTATION': (ZkClient.PATH_LUA_REPUTATION_LIST, 'reputation rule'),
                'NEW_COMER': (ZkClient.PATH_LUA_NEW_COMER_LIST,  'new comer rule'),
                'SNIPING': (ZkClient.PATH_LUA_SNIPING_LIST,    'sniping rule'),
                'APP_COUNTER': (ZkClient.PATH_LUA_MUBB_COUNTER_LIST,  'APP counter rule'),
                'APP_BLACK': (ZkClient.PATH_LUA_MUBB_BLACKLIST_LIST,  'APP blacklist rule'),
                'APP_HIGH_FREQ': (ZkClient.PATH_LUA_MUBB_HIGH_FREQ_LIST, 'APP high frequent rule'),
                'APP_REPUTATION': (ZkClient.PATH_LUA_MUBB_REPUTATION_LIST,'APP reputation rule'),
            }
            saving_rule_path = 0
            translated_sene_name = 1

            if scene in scene_config.keys():
                rules_in_zk = AspConfCtrl.get_zk_lua_info(scene_config[scene][saving_rule_path])
            else:
                raise Exception('Invalid rule scene: {} '.format(scene))

            rules = [] if not rules_in_zk else json.loads(rules_in_zk, encoding='utf-8')
            if rules:
                sorted_list = sort_dict_list_by_id_list(rules, rule_ids)
                if not sorted_list:
                    raise Exception('Invalid rule ids: {} '.format(rule_ids))

                err, error_msg = update_rule(scene, sorted_list)
                result = {
                    'is_success': not err,
                    'error_msg': error_msg if err else ''
                }

        except Exception as e:
            logger.exception(e)
            result = {
                'is_success': False,
                'error_msg': _('Failed')
            }
        
        record_operation_log(request, result['is_success'], ugettext_noop('Modify'),
                         _("Changed {} priority").format(_(scene_config[scene][translated_sene_name])), '', rule_ids)

    return HttpResponse(json.dumps(result), content_type=CONTENT_TYPE_JSON)