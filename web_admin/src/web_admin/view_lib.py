'''
Created on Oct 5, 2015

@author: hongyong<PERSON><PERSON>
'''
import _strptime # workaround for https://bugs.python.org/issue7980
import time
import json
from django.contrib.auth import logout
from django.contrib.auth.decorators import login_required as django_login_required
from django.contrib import messages
from django.conf import settings
from django.contrib.auth.views import redirect_to_login
from django.utils.translation import ugettext as _
from django.http import HttpResponseRedirect, HttpResponse, HttpRequest
from Conf_Webconsole import WebconsoleConf
from asp_conf_ctrl import GetConfDb
from asp_utils.utils import get_version
from asp_utils.CommonLibs import compress_IP

SESSION_NOTEDIT_TIMEOUT = getattr(settings, 'SESSION_NOTEDIT_TIMEOUT', 6)

CONTENT_TYPE_JSON = 'application/json'

SECS_OF_DAY = 24 * 60 * 60


def parse_ngx_utc_time(s, str_format='%Y-%m-%d %H:%M:%S'):
    d = time.strptime(s, str_format)
    d = time.mktime(d)
    return int(d)


def date_str_to_time(date):  # return UTC second
    if date:
        d = time.strptime(date, '%Y-%m-%d')
        est = 3600 if time.tzname[0] == 'EST' else 0
        return int(time.mktime(d) - time.timezone + est)


def time_to_date_str(date):
    if date:
        d = time.gmtime(date)
        d = time.strftime('%Y-%m-%d', d)  # %H:%M:%S
        return d
    return ''


def time_to_time_str(tm):
    if tm:
        d = time.gmtime(tm)
        d = time.strftime('%Y%m%d%H%M%S', d)  # %H:%M:%S
        return d
    return ''


def get_date_start_end_param(request):
    date_start = request.GET.get('date_start')
    date_end = request.GET.get('date_end')
    date_start = date_str_to_time(date_start)
    date_end = date_str_to_time(date_end)

    now = int(time.time()) - time.timezone
    if not date_start:
        date_start = now - now % SECS_OF_DAY - SECS_OF_DAY * 7

    if not date_end:
        date_end = now

    return date_start, date_end


def format_local_str(obj):
    content = _(obj['msg'])
    content = content.split('%%')
    out = []
    variables = obj
    for i in range(len(content)):
        if i % 2 == 1:
            name = content[i].strip()
            value = variables.get(name)
            out.append(str(value))
        else:
            out.append(content[i])
    return ''.join(out)


class Pagination():

    def __init__(self, count, cur_page, COUNT_PER_PAGE=50):
        self.cur_page = to_int(cur_page, 0)
        self.page_count = int(count / COUNT_PER_PAGE)
        if self.page_count * COUNT_PER_PAGE < count:
            self.page_count += 1

        if self.cur_page < 0:
            self.cur_page = 0
        if self.cur_page >= self.page_count:
            self.cur_page = self.page_count - 1
        if self.page_count == 0:
            self.cur_page = 0

        self.COUNT_PER_PAGE = COUNT_PER_PAGE

    def items_of_cur_page(self, items_list):
        return items_list[self.cur_page * self.COUNT_PER_PAGE: (self.cur_page + 1) * self.COUNT_PER_PAGE]

    def create(self, ONE_SIDE_PAGE=2):
        '''
        Create pagination string arrays, like:
        << ... ONE_SIDE_PAGE CUR_PAGE ONE_SIDE_PAGE ... >>
        return [(page text, page numb), ... ], self.cur_page
        '''

        if self.page_count <= 1:
            return

        if self.page_count <= (ONE_SIDE_PAGE + 1) * 2 + 4:
            items = [(i + 1, i) for i in range(self.page_count)]
        elif self.cur_page <= ONE_SIDE_PAGE + 2:
            end = self.cur_page + 1 + ONE_SIDE_PAGE
            items = [(i + 1, i) for i in range(end)]
            items.append(('...', end))
            items.append((self.page_count, self.page_count - 1))
        elif self.cur_page >= self.page_count - (ONE_SIDE_PAGE + 2 + 1):
            start = self.cur_page - (ONE_SIDE_PAGE)
            items = [(i + 1, i) for i in range(start, self.page_count)]
            items.insert(0, ('...', start - 1))
            items.insert(0, ('1', 0))
        else:
            items = [(i + 1, i) for i in range(self.cur_page - ONE_SIDE_PAGE, self.cur_page + ONE_SIDE_PAGE + 1)]
            items.insert(0, ('...', self.cur_page - ONE_SIDE_PAGE - 1))
            items.insert(0, ('1', 0))
            items.append(('...', self.cur_page + ONE_SIDE_PAGE + 1))
            items.append((self.page_count, self.page_count - 1))

        if self.cur_page == 0:
            items.insert(0, ('&laquo;', -1))
        else:
            items.insert(0, ('&laquo;', self.cur_page - 1))

        if self.cur_page == self.page_count - 1:
            items.append(('&raquo;', - 1))
        else:
            items.append(('&raquo;', self.cur_page + 1))

        return items


def to_int(val, default):
    try:
        return int(val)
    except:
        return default


def json_result(msg, result='ok', extra_values=None):
    if extra_values is None:
        extra_values = {}
    results = {'result': result, 'message': msg}
    results.update(extra_values)
    return json.dumps(results)


def json_ok_response(msg, result='ok', extra_values=None):
    if extra_values is None:
        extra_values = {}
    return HttpResponse(json_result(msg, result), content_type=CONTENT_TYPE_JSON)


def json_fail_response(msg, result='failed', extra_values=None, status=None):
    if extra_values is None:
        extra_values = {}
    return HttpResponse(json_result(msg, result), status=status, content_type=CONTENT_TYPE_JSON)


def get_client_ip(request):
    ip = request.META.get('HTTP_X_REAL_IP')
    if not ip:
        ip = request.META.get('REMOTE_ADDR')
    return ip

smart_conf = GetConfDb()

def session_idle_logout(fn):
    def wrapped(*args, **kwargs):
        request = args[0]  # type: HttpRequest
        if request.user.username and not WebconsoleConf().is_user_exist(request.user.username):
            return redirect_to_login(request.path, settings.LOGIN_URL, 'next')
        if request.user.is_authenticated():
            now = time.time()
            is_kicked = int(request.session.get('_kicked', 0)) == 1
            # Timeout if idle time period is exceeded.
            if request.session.has_key('last_activity') and \
                    abs(now - request.session['last_activity']) > \
                    smart_conf.db().get_session_idle_timeout_seconds():
                logout(request)
                messages.add_message(request, messages.ERROR,
                                     _('Your session has been timed out.'))
            elif request.session.has_key('last_activity') and \
                    abs(now - request.session['last_activity']) < \
                    SESSION_NOTEDIT_TIMEOUT:
                pass
            elif is_kicked:
                return redirect_to_login(
                    request.path, settings.LOGIN_URL, 'next')
            #sewl : session expired whitelist
            elif 'sewl' in request.REQUEST and smart_conf.db().get_value('web_console/session_idle_all', False):
                pass
            else:
                request.session['last_activity'] = now
                request.session['remote_addr'] = get_client_ip(request)
        return fn(*args, **kwargs)

    return wrapped


def wizard_status_check(fn):
    def wrapped(*args, **kwargs):
        if not WebconsoleConf().is_wizard_finished():
            return HttpResponseRedirect("/wizard/wizard_home")
        else:
            request = args[0]  # type: HttpRequest
            if 'HTTP_X_REAL_IP' in request.META:
                x_real_ip = compress_IP(request.META['HTTP_X_REAL_IP'], 'err')
                if x_real_ip == 'err':
                    return HttpResponse(status=400)
                request.META['HTTP_X_REAL_IP'] = x_real_ip
            return fn(*args, **kwargs)
    return wrapped


def login_required(fn):
    @wizard_status_check
    @session_idle_logout
    @django_login_required
    def wrapped(*args, **kwargs):
        return fn(*args, **kwargs)

    return wrapped


def is_local_request(request):
    remote_addr = request.META.get('REMOTE_ADDR')
    x_forwarded_from = request.META.get("HTTP_X_FORWARDED_ASP")
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    x_real_ip = request.META.get("HTTP_X_REAL_IP")

    if remote_addr != "127.0.0.1":
        return False

    if x_forwarded_from == "asp" or x_forwarded_for or x_real_ip:
        return False

    return True


def botgate_user_to_sf_user(user_name):
    if WebconsoleConf().get_user_role(user_name) in ['Operator', 'StatisticViewer']:
        return '_botgate_%s' % user_name
    else:
        return 'guest'

def get_current_build_no():
    build_no = get_version()[1]
    try:
        build_no = int(build_no)
        return build_no
    except:
        return 0

def get_current_git_commit_no():
    return get_version()[2]

def get_current_build_info():
    return get_version()[4]


def get_current_is_debug():
    return get_version()[5]


def get_current_is_prod_pkg():
    return get_version()[6]

def get_current_has_phoenix_pkg():
    return get_version()[8]
