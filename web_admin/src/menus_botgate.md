| 菜单ID | 菜单名 | 自定义角色可选权限  | 备注 |
| ----- | ----- | ---------------- | --- |
| Overview | "Overview" | rw,r- | 系统概要 |
| Config_Proxy | "Protection" | rw,r-,-- | 保护配置 |
| WAF_General_Protection | "WAF > General Protection" | rw,r-,-- | WAF > 通用防护 |
| WAF_Power_Protection | "WAF > Power Protection" | rw,r-,-- | WAF > 高级防护 |
| WAF_Compliance_Detection | "WAF > Compliance Detection" | rw,r-,-- | WAF > 合规检测 |
| WAF_Global_Custom_Rule | "WAF > Global Custom Rule" | rw,r-,-- | WAF > 全局自定义规则 |
| WAF_Global_Whitelist | "WAF > Global Whitelist" | rw,r-,-- | WAF > 全局白名单 |
| WAF_Flow_Learning | "WAF > Flow self-learning" | rw,r-,-- | WAF > 流量自学习 |
| WAF_Analysis_And_Process | "WAF > Analysis And Process" | rw,r-,-- | WAF > 误报分析 |
| WAF_Ruleset_Upgrade | "WAF > Ruleset Upgrade" | rw,r-,-- | WAF > 规则库升级 |
| WAF_Settings | "WAF > Settings" | rw,r-,-- | WAF > 设置 |
| Programmable_Defending | "Programmable Defending" | rw,r-,-- | 可编程对抗 |
| Mobile_Protection | "Mobile Protection" | rw,r-,-- | 移动保护 |
| Mpp_Settings | "Mini Program Protection" | rw,r-,-- | 小程序保护 |
| API_Means | "API Means" | -- | API安全管控 |
| Business_Threat_Awareness | "Business Threat Awareness" | rw,r-,-- | 业务威胁感知 |
| Business_Data_Collection | "Business Data Collection" | rw,r-,-- | 业务数据采集 |
| Reputation | "Reputation" | rw,r-,-- | 信誉库 |
| Big_Screen_Display | "Big Screen Display" | r-,-- | 大屏展示 |
| Static_Bot_Detection | "staticBotDetection" | -- | Bot检测 |
| Threat_Intelligence | "Threat Intelligence" | -- |威胁情报 |
| Network_DDOS_Protect | "Network_Ddos_Protect" | -- | 网络层DDoS防护 |
| Statistics | "Statistics" | r-,-- | 报表分析 |
| Web_Filter_Settings | "Web Filter > Settings" | -- | 网页过滤 > 设置 |
| Web_Filter_Abnormal_Requests | "Web Filter > Abnormal Requests" | -- | 网页过滤 > 异常请求 |
| Web_Filter_Blocked_Requests | "Web Filter > Blocked Requests" | -- | 网页过滤 > 过滤报告 |
| Web_Filter_Requested_URLs | "Web Filter > Requested URLs" | -- | 网页过滤 > 访问报告 |
| System_General | "System > General" | rw,r-,-- | 系统 > 通用 |
| System_License | "System > License" | rw,r-,-- | 系统 > 授权许可 |
| System_Network_Configuration | "System > Network Configuration" | rw,r-,-- | 系统 > 网络配置 |
| LLM_Model_Configuration | "System > LLM Settings"  | rw,r-,-- | 系统 > 大模型配置 |
| System_Account_Management | "System > Login/Account" | -- | 系统 > 登录与账户管理 |
| System_API_Gateway | "System > API Gateway" | -- | 系统 > 系统API接口 |
| System_Log | "System > Logs" | rw,r-,-- | 系统 > 日志 |
| System_Alarm | "System > Alarms" | rw,r-,-- | 系统 > 告警 |
| System_Emergency_Mode | "System > Emergency Mode" | rw,r-,-- | 系统 > 紧急模式 |
| System_HttpCap_Cfg | "System > HTTP Capture Configuration" | -- | 系统 > 流量采集设置 |
| System_Labs | "System > Labs" | -- | 系统 > 实验室 |
| System_Command_Line | "System > Command Line" | rw,-- | 系统 > 命令行 |
| Developer_Mode | "Developer Mode" | -- | 开发者模式 |
| File_Monitoring | "File Monitoring" | rw,r-,-- | 文件传输监测 |
| LLM_Protection | "LLM Protection"   | rw,r-,-- | 大模型安全防护 |
| AI_Threat_Analyst | "AI Threat Analyst"   | rw,r-,-- | 智能威胁研判助手 |
