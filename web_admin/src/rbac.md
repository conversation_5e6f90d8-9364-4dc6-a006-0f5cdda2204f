| 自动生成 | Name                             | Administrator | Operator | Viewer | StatisticViewer | Botgate下所属菜单ID            | Safeplus下所属菜单ID             | 备注       |
| ------- |----------------------------------| ------------- |----------|--------| --------------- | ---------------------------- | ------------------------------ |----------- |
| N       | `__wizard_setup__`               | rw            | rw       | rw     | rw              | rw                           | rw                             | Wizard 阶段的功能 |
| N       | `__guest_function__`             | rw            | rw       | rw     | rw              | rw                           | rw                             | 无需登陆即可使用的功能 |
| N       | `Password_Change`                | rw            | rw       | rw     | rw              | rw                           | rw                             | 用户登录后修改自己的密码 |
| N       | `API_Batch_Import`               | rw            | --       | --     | --              | API_Means                    | API_Means                      | API列表批量导入 |
| N       | `API_Gateway`                    | --            | --       | --     | --              | System_API_Gateway           | System_API_Gateway             | 系统API接口     |
| N       | `API_Means`                      | rw            | rw       | r-     | r-              | API_Means                    | API_Means                      | API资产管理     |
| N       | `API_Advance_Means`              | rw            | --       | --     | --              | API_Means                    | API_Means                      | API高级资产管理权限，仅Admin具有 |
| N       | `API_Pii_Desensitized`           | rw            | --       | rw     | rw              | API_Means                    | API_Means                      | API脱敏按钮的展示操作 |
| N       | `API_Add`                        | rw            | rw       | --     | --              | API_Means                    | API_Means                      | API资产添加权限，仅Admin、operator具有 |
| N       | `API_Read`                       | rw            | rw       | rw     | rw              | API_Means                    | API_Means                      | API read接口：Get/Post方法 |
| N       | `API_Ignore`                     | rw            | r-       | r-     | r-              | API_Means                    | API_Means                      | API忽略列表操作权限 |
| N       | `API_Param_Learning`             | rw            | --       | r-     | --              | API_Means                    | API_Means                      | API参数自学习    |
| N       | `API_Setting`                    | rw            | r-       | r-     | --              | API_Means                    | API_Means                      | API功能相关设置        |
| N       | `Access_Control`                 | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 访问控制策略    |
| N       | `Account_Default_Configurations` | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 其他配置      |
| N       | `Add_User`                       | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 创建用户      |
| N       | `Add_Website`                    | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 添加站点      |
| N       | `Advanced_Operation_Code`        | rw            | --       | --     | --              | r-                           | r-                             | 高级操作验证码   |
| N       | `Alarm_Mail_Settings`            | rw            | --       | --     | --              | System_Alarm                 | System_Alarm                   | 邮件告警设置    |
| N       | `Alarm_SMS_Settings`             | rw            | --       | --     | --              | System_Alarm                 | System_Alarm                   | 短信告警设置    |
| N       | `Alarm_Advanced_Settings`        | rw            | --       | --     | --              | System_Alarm                 | System_Alarm                   | 告警高级设置    |
| N       | `Alarm_Settings`                 | rw            | --       | --     | --              | System_Alarm                 | System_Alarm                   | 告警设置      |
| N       | `Archived_Log`                   | rw            | --       | --     | --              | System_Log                   | System_Log                     | 归档日志      |
| N       | `Big_Screen_Display`             | rw            | rw       | r-     | --              | Big_Screen_Display           | Big_Screen_Display             | 大屏展示      |
| N       | `Business_Threat_Awareness`      | rw            | --       | r-     | --              | Business_Threat_Awareness    | Business_Threat_Awareness      | 业务威胁感知    |
| N       | `Change_Node_Role`               | --            | --       | --     | --              | Overview                     | Statistics_Report_Overview     | 变更节点角色    |
| N       | `Change_Website_Name`            | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 修改站点名称    |
| N       | `Cluster_Node_List`              | r-            | r-       | r-     | --              | Overview                     | Statistics_Report_Overview     | 集群节点列表    |
| N       | `Command_Line`                   | rw            | --       | --     | --              | System_Command_Line          | System_Command_Line            | 命令行       |
| N       | `Data_Collection`                | rw            | rw       | r-     | --              | Business_Data_Collection     | Business_Data_Collection       | 业务数据采集    |
| N       | `Data_Collection_Priority`       | rw            | --       | --     | --              | Business_Data_Collection     | Business_Data_Collection       | 业务数据采集优先级调整 |
| N       | `Data_Collection_Weak_Password`  | rw            | rw       | r-     | --              | Business_Data_Collection     | Business_Data_Collection       | 弱口令字典     |
| N       | `Delete_Node`                    | --            | --       | --     | --              | Overview                     | Statistics_Report_Overview     | 删除节点      |
| N       | `Delete_User`                    | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 删除用户      |
| N       | `Delete_Website`                 | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 删除站点      |
| N       | `Developer_Mode`                 | --            | --       | --     | --              | Developer_Mode               | Developer_Mode                 | 开发者模式     |
| N       | `Edit_2fa`                       | r-            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 登录身份验证码   |
| N       | `Edit_User`                      | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 编辑用户      |
| N       | `Unlock_User`                    | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 解锁用户      |
| N       | `Edit_Website_Config`            | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 保存站点配置    |
| N       | `Eliminate_Alarm`                | rw            | --       | --     | --              | System_Alarm                 | System_Alarm                   | 消除告警      |
| N       | `Enhance_Cluster_Security`       | rw            | --       | --     | --              | System_General               | System_General                 | 增强集群安全性   |
| N       | `Hide_Hardware_Info`             | rw            | --       | --     | --              | System_General               | System_General                 | 隐藏硬件配置   |
| N       | `Expert_Mode`                    | rw            | rw       | --     | --              | rw                           | rw                             | 专家模式      |
| N       | `Export_System_Settings`         | rw            | --       | --     | --              | System_General               | System_General                 | 系统配置导出    |
| N       | `Export_Website`                 | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 导出站点      |
| N       | `Factory_Reset`                  | rw            | --       | --     | --              | System_General               | System_General                 | 恢复出厂设置    |
| N       | `File_Monitoring`                | rw            | r-       | r-     | --              | File_Monitoring              | File_Monitoring                | 文件传输监测  |
| N       | `Full_Flow_Collection`           | rw            | --       | r-     | --              | Config_Proxy                 | Config_Proxy                   | 全流量采集     |
| N       | `Global_Settings`                | rw            | r-       | r-     | --              | Config_Proxy                 | Config_Proxy                   | 全局设置      |
| N       | `HttpCap_Cfg`                    | rw            | --       | r-     | --              | System_HttpCap_Cfg           | System_HttpCap_Cfg             | 流量采集设置    |
| N       | `Import_System_Settings`         | rw            | --       | --     | --              | System_General               | System_General                 | 系统配置导入    |
| N       | `Import_Website`                 | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 导入站点      |
| N       | `Labs`                           | --            | --       | --     | --              | System_Labs                  | System_Labs                    | 实验室       |
| N       | `License`                        | rw            | --       | r-     | --              | System_License               | System_License                 | 授权许可      |
| N       | `Log_Settings`                   | rw            | --       | r-     | --              | System_Log                   | System_Log                     | 日志设置      |
| N       | `Login_Account`                  | r-            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 登录与账户管理   |
| N       | `Mobile_Certificate`             | rw            | rw       | r-     | --              | Mobile_Protection            | Mobile_Protection              | 移动证书      |
| N       | `Mobile_Integrity_Checking`      | rw            | rw       | r-     | --              | Mobile_Protection            | Mobile_Protection              | 完整性校验     |
| N       | `Mobile_Other_Configurations`    | rw            | --       | r-     | --              | Mobile_Protection            | Mobile_Protection              | 其他配置      |
| N       | `Mobile_Protection`              | rw            | rw       | r-     | --              | Mobile_Protection            | Mobile_Protection              | 移动保护      |
| N       | `Navigation_Alarm`               | r-            | r-       | r-     | --              | System_Alarm                 | System_Alarm                   | 告警        |
| N       | `Network_DDOS_Protect`           | --            | --       | --     | --              | Network_DDOS_Protect         | Network_DDOS_Protect           | 网络层DDOS防护 |
| N       | `Network_Configuration`          | rw            | --       | r-     | --              | System_Network_Configuration | System_Network_Configuration   | 网络配置      |
| N       | `Node_Status`                    | r-            | r-       | r-     | --              | Overview                     | Statistics_Report_Overview     | 节点状态      |
| N       | `Operation_Log`                  | rw            | --       | r-     | --              | System_Log                   | System_Log                     | 操作日志      |
| N       | `Permission_List`                | r-            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 权限列表      |
| N       | `Programmable_Defending`         | rw            | --       | r-     | --              | Programmable_Defending       | Programmable_Defending         | 可编程对抗     |
| N       | `Protected_Websites`             | r-            | r-       | r-     | --              | Config_Proxy                 | Config_Proxy                   | 站点列表展示    |
| N       | `Protection_List`                | rw            | rw       | r-     | --              | Config_Proxy                 | Config_Proxy                   | 保护设置列表    |
| N       | `Reboot`                         | rw            | --       | --     | --              | System_General               | System_General                 | 重启        |
| N       | `Rename_Cluster`                 | rw            | --       | --     | --              | System_General               | System_General                 | 重命名集群     |
| N       | `Reputation`                     | rw            | --       | r-     | --              | Reputation                   | Reputation                     | 信誉库       |
| N       | `Ruleset_Rollback`               | rw            | --       | --     | --              | WAF_Ruleset_Upgrade          | WAF_Ruleset_Upgrade            | 回滚WAF规则库  |
| N       | `Ruleset_Update`                 | rw            | --       | --     | --              | WAF_Ruleset_Upgrade          | WAF_Ruleset_Upgrade            | 升级WAF规则库  |
| N       | `Search_Alarm`                   | rw            | rw       | r-     | --              | System_Alarm                 | System_Alarm                   | 告警查询      |
| N       | `Security_Log`                   | rw            | --       | --     | --              | System_Log                   | Statistics_Report_Security_Log | 安全日志      |
| N       | `Show_Website`                   | r-            | r-       | r-     | --              | Config_Proxy                 | Config_Proxy                   | 展示站点      |
| N       | `Shutdown`                       | rw            | --       | --     | --              | System_General               | System_General                 | 关机        |
| N       | `Statistics`                     | rw            | rw       | r-     | r-              | Statistics                   | Statistics_Report_Statistics   | 报表分析      |
| N       | `System_General`                 | rw            | --       | --     | --              | System_General               | System_General                 | 通用        |
| N       | `System_Log`                     | rw            | --       | --     | --              | System_Log                   | System_Log                     | 系统日志      |
| N       | `System_Navigator`               | r-            | r-       | r-     | --              | r-                           | r-                             | 系统菜单      |
| N       | `System_Rollback`                | rw            | --       | --     | --              | System_General               | System_General                 | 回滚版本      |
| N       | `System_Statistics`              | r-            | r-       | r-     | r-              | Overview                     | Statistics_Report_Overview     | 系统概要报表    |
| N       | `System_Update`                  | rw            | --       | --     | --              | System_General               | System_General                 | 升级版本      |
| N       | `Threat_Intelligence`            | --            | --       | --     | --              | Threat_Intelligence          | Threat_Intelligence            | 威胁情报        |
| N       | `Update_Geo_Lib`                 | rw            | --       | --     | --              | System_General               | System_General                 | 地理位置库升级   |
| N       | `User_List`                      | r-            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 用户列表      |
| N       | `WAF_Config`                     | rw            | r-       | r-     | --              | WAF_Settings                 | WAF_Settings                   | WAF设置      |
| N       | `WAF_Flow_Learning`              | rw            | --       | r-     | --              | WAF_Flow_Learning            | WAF_Flow_Learning              | 流量自学习     |
| N       | `WAF_Strategy`                   | rw            | rw       | r-     | --              | rw                           | rw                             | WAF       |
| N       | `Web_Filter`                     | rw            | --       | --     | --              | --                           | --                             | 网页过滤      |
| N       | `Mpp_Settings`                   | rw            | r-       | r-     | --              | Mpp_Settings                 | Mpp_Settings                   | 微信小程序保护  |
| N       | `Emergency_Mode`                 | rw            | --       | r-     | --              | System_Emergency_Mode        | System_Emergency_Mode          | 紧急模式      |
| N       | `WAF_Analysis_And_Process`       | rw            | --       | --     | --              | WAF_Analysis_And_Process     | WAF_Analysis_And_Process       | WAF辅助分析和处理 |
| N       | `Static_Bot_Detection`           | rw            | --       | r-     | --              | Static_Bot_Detection         | Static_Bot_Detection           | Bot检查     |
| N       | `Enable_Site_Conf`               | rw            | rw       | --     | --              | Config_Proxy                 | Config_Proxy                   | 启用或禁用站点配置 |
| N       | `API_Scanner`                    | rw            | --       | --     | --              | API_Means                    | API_Means                      | API扫描器配置 |
| N       | `WAF_Compliance_Detection`       | rw            | rw       | r-     | --              | WAF_Compliance_Detection     | WAF_Compliance_Detection       | WAF合规检测    |
| N       | `WAF_General_Protection`         | rw            | rw       | r-     | --              | WAF_General_Protection       | WAF_General_Protection         | WAF通用防护    |
| N       | `WAF_Global_Custom_Rule`         | rw            | rw       | r-     | --              | WAF_Global_Custom_Rule       | WAF_Global_Custom_Rule         | WAF全局自定义规则 |
| N       | `WAF_Global_Whitelist`           | rw            | --       | --     | --              | WAF_Global_Whitelist         | WAF_Global_Whitelist           | WAF全局白名单   |
| N       | `WAF_Power_Protection`           | rw            | rw       | r-     | --              | WAF_Power_Protection         | WAF_Power_Protection           | WAF高级防护    |
| N       | `WAF_Rules`                      | rw            | rw       | r-     | --              | r-                           | r-                             | WAF规则库对规则的描述文件，日志查询，保护站点及通用防护页面均有调用，不可修改，当作资源文件 |
| N       | `Role_Management`                | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | 自定义角色管理    |
| N       | `Help_Document`                  | r-            | r-       | --     | --              | r-                           | r-                             | 查看帮助文档    |
| N       | `Qps_Management`                 | rw            | --       | --     | --              | System_Account_Management    | System_Account_Management      | QPS配额管理    |
| N       | `REPORT`                         | rw            | --       | --     | --              | API_Means                    | API_Means                      | 报告          |
| N       | `LLM_Protection`                 | rw            | rw       | r-     | --              | LLM_Protection               | LLM_Protection                 | 大模型安全防护  |
| N       | `LLM_SETTING`                    | rw            | r-       | r-     | --              | LLM_Setting                  | LLM_Setting                    | 大模型安全防护设置  |
| N       | `Risk_Report_Setting`            | rw            | r-       | r-     | r-              | Risk_Report_Setting          | Risk_Report_Setting            | 风险报表设置 |
| N       | `LLM_Model_Configuration`        | rw            | --       | r-     | --              | LLM_Model_Configuration      | LLM_Model_Configuration        | 大模型配置 |
| N       | `AI_Threat_Analyst`              | rw            | rw       | r-     | --              | AI_Threat_Analyst            | AI_Threat_Analyst              | 智能威胁研判助手 |