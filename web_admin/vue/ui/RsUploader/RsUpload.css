.rs-uploader__wrapper {
    display: inline-block;
    position: relative;
    width: 360px;
    height: 24px;
}

.rs-uploader__wrapper > .rs-uploader__file_wrapper {
    /* position: absolute;
    left: 0px;
    right: 80px; */
    position: relative;
    display: inline-block;
    width: 75%;
    height: 100%;
    border: 1px solid #d2d2d2;
    border-radius: 3px;
    overflow: hidden;
    vertical-align: middle;
}

.rs-uploader__wrapper > .rs-uploader__file_wrapper:hover {
    border-color: #cacaca;
}

.rs-uploader__wrapper > .rs-uploader__file_wrapper > .rs-file-selector {
    display: inline-flex;
    align-items: stretch;
    width: 100%;
    justify-content: space-between;
    flex-direction: row;
    height: 100%;
}

.rs-uploader__wrapper > .rs-uploader__file_wrapper > .rs-file-selector > input[type="text"] {
    background-color: white !important;
    border: 0px !important;
    height: 100%;
    border: 0px;
    flex-basis: auto;
    width: 100%;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}

.rs-uploader__wrapper > .rs-uploader__file_wrapper > .rs-file-selector > button {
    background-color: #ddebee;
    color: #313741;
    box-sizing: border-box;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 2px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    text-align: center;
    width: 80px;
}

.rs-uploader__wrapper > .rs-uploader__file_wrapper > input[type="file"] {
    position: absolute;
    top: 0px;
    left: 0px;
    outline: none;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
    z-index: 1;
}

.rs-uploader__wrapper .rs-uploader__button {
    /* position: absolute;
    right: 0px;
    top: 0px; */
    min-width: 70px;
    margin: 0px;
    vertical-align: middle;
    display: inline-block;
}


.rs-uploader__wrapper[onlyButton=true] {
    width: auto;
    min-width: 60px;
}

.rs-uploader__wrapper[onlyButton=true] .rs-uploader__file_wrapper {
    width: 100%;
    display: none;
}

.rs-uploader__wrapper[onlyButton=true] .rs-uploader__button {
    position: inherit;
    width: 100%;
}

.running {
    color: #dc2a0b;
}

.circle {
    display: inline-block;
    animation: rotate 3s linear infinite; /* related ... setTimeout(getTcpdumpStatus, 3000); */
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(359deg); }
}

.rs-uploader__wrapper[drop=true] {
    width: 500px;
    height: 200px;
}

.rs-uploader__wrapper[drop=true] > .rs-uploader__file_wrapper {
    width: 100%;
    border: 1px dashed #adafb3;
}

.rs-uploader__wrapper[drop=true] .rs-file-selector input {
    display: none;
}

.rs-uploader__wrapper[drop=true] .rs-file-selector button {
    width: 100%;
    background-color: transparent;
}
.rs-uploader__wrapper[drop] .rs-file-drop-wapper{
    position: absolute;
    width: 100%;
    bottom: 0px;
    left: 0px;
}

.rs-uploader__wrapper[drop] .rs-file-drop-area {
    display: flex;
    justify-content: center; 
}

.rs-uploader__wrapper[drop] .rs-file-drop-area li {
    display: inline-block;
    padding: 10px;
    text-align: center;
    margin-bottom: 0;
}

.rs-uploader__wrapper[drop] .rs-file-drop-area li i {
    display: block;
    width: 100%;
    font-size: 40px;
    margin-bottom: 5px;
}

.rs-uploader__wrapper[drop=true][disabled] > .rs-uploader__file_wrapper {
    background-color: #f4f5f5;
}

.rs-uploader__wrapper[drop=true][disabled] .rs-file-selector button {
    color: gray;
}

.rs-uploader__wrapper[drop=true][disabled] input[type="file"] {
    cursor: auto!important;
}
