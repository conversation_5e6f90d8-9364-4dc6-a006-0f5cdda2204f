var DEFAULT_CHUNK_SIZE = 10 * 1024 * 1024;

var RsUpload = {
    template: '\
        <div rsid="RsUploader" class="rs-uploader__wrapper" :disabled="disabled" :drop="drop" :onlyButton="onlyButton">\
            <div class="rs-uploader__file_wrapper">\
                <div class="rs-file-selector">\
                    <input type="text" :placeholder="placeholder" :value="fileName" readonly />\
                    <button rsid="browse" type="button">{[$data._trans.browse]}</button>\
                </div>\
                <input :name="name" :accept="accept" :disabled="disabled" @change="onFileChange" type="file" ref="uploadFileInput" size="1" />\
            </div>\
            <div class="rs-file-drop-wapper" v-if="drop && selectFile">\
                <ul class="rs-file-drop-area">\
                    <li class="rs-file-drop-item" v-tooltip="fileName">\
                        <i class="fa fa-file-alt"></i>\
                        <span>{[ fileNameShow ]}</span>\
                    </li>\
                </ul>\
            </div>\
            <button v-if="!onlyChooseFile" :disabled="loading || disabled" @click="onUpload" class="rs-uploader__button">\
                <i class="fa fa-upload" v-show="needIcon"></i>{[ label ]}\
            </button>\
            <p v-if="drop"><span v-if="accept">{[$data._trans.typeDescription]}</span><span v-if="maxSize">{[$data._trans.sizeDescription]}</span></p>\
        </div>\
    ',

    props: {
        accept: { type: String, default: '' },
        name: { type: String, required: true },     // 非断点续传时，必填
        action: { type: String, required: false },   
        params: { type: Object },
        placeholder: { type: String, default: gettext('Please select file.') },
        label: { type: String, default: gettext('Upload') },
        onlyChooseFile: { type: Boolean, default: false },
        onlyButton: { type: Boolean, default: false },
        disabled: { type: Boolean, default: false },
        needIcon: { type: Boolean, default: false },
        timeout: { type: Number, default: 180000 },
        drop: { type: Boolean, default: false }, // 是否支持拖拽上传
        maxSize: { type: String, default: '' }, // 最大文件大小，eg: 1B, 1KB, 1MB, 1GB    

        // 断线续传配置
        loadingGroupName: { type: String, default: '' },
        chunktUpload: {
            type: Object,
            default: {
                uploadChunkAction: '',
                chunkSize: DEFAULT_CHUNK_SIZE
            }
        }
    },

    data: function() {
        return {
            selectFile: null,
            size: 0,
            fileName: '',
            loading: false,
            uploadFormData: null,
            hasUploadFile: true, // 解决IE上传两次的问题
            _trans: {
                browse: this.drop? gettext('Click or drag the file here') : gettext('Browse'),
                typeErrorTxt: gettext('Only files with [ {0} ] extension are supported.'),
                typeDescription: utils.formatString(gettext('File types supported: {0}'), this.accept),
                sizeDescription: this.accept ? utils.formatString(gettext('(Maximum size {0})'), this.maxSize) : utils.formatString(gettext('Maximum file size: {0}'), this.maxSize)
            },

            // 断线续传配置
            uploadChunkConfig: {
                chunkSize: DEFAULT_CHUNK_SIZE, // 分片大小
                currentChunk: 0, // 当前分片索引
                totalChunks: 0, // 总分片数
                uploadedChunks: [], // 已上传分片索引数组
                uploading: false,
                progress: 0,
                cancelToken: null, // 用于取消请求
                fileId: '', // 文件唯一标识（通常由后端生成，这里为了简化使用文件名+大小）
            }
        }
    },

    computed: {
        isSupportUploadChunk: function() {
            return this.chunktUpload && this.chunktUpload.uploadChunkAction;
        },

        uploadChunkProcessText: function() {
            return utils.formatString(gettext('Uploading: {0}%'), this.uploadChunkConfig.progress);
        },

        fileNameShow: function() {
            var result = this.fileName.length > 20 ? this.fileName.substr(0, 10) + '...' + this.fileName.substr(this.fileName.length - 10): this.fileName;
            return result;   
        }
    },
    
    methods: {
        onFileChange: function(evt) {
            var fileInput = evt.currentTarget;
            var file = fileInput.files[0];
            this.$emit('change', file); // 用于外部业务调用
            if (!file) {
                this.selectFile = null;
                this.size = 0;
                this.fileName = '';
                return;
            }

            this.selectFile = file;
            this.size = this.selectFile.size;
            this.fileName = file.name;

            if (this.isSupportUploadChunk) {
                // 断点续传
                this.handleFileChangeForChunkUpload();

            } else {
                // 普通上传
                this.handleFileChangeForNormal();
            }
        },


        onUpload: function() {
            if (this.isSupportUploadChunk) {
                this.handleUploadForChunkUpload();
            } else {
                this.handleUploadForNormal();
            }
        },



        /**-------------------------- 断点续传 -------------------- */
        handleFileChangeForChunkUpload: function() {
            // 断点续传
            if (this.chunktUpload.chunkSize) this.uploadChunkConfig.chunkSize = this.chunktUpload.chunkSize;
            // 计算总分片数
            this.uploadChunkConfig.totalChunks = Math.ceil(this.size / this.uploadChunkConfig.chunkSize);
            this.uploadChunkConfig.fileId = this.fileName + new Date().getTime(); // 生成文件唯一标识
            this.loadProgress(); // 加载之前的上传进度
        },

        // 从localStorage加载进度
        loadProgress: function() {
            var savedProgress = localStorage.getItem(this.uploadChunkConfig.fileId);
            if (savedProgress) {
                var data = JSON.parse(savedProgress);
                this.uploadChunkConfig.currentChunk = data.currentChunk;
                this.uploadChunkConfig.uploadedChunks = data.uploadedChunks;
                this.uploadChunkConfig.progress = data.progress;
            } else {
                this.uploadChunkConfig.uploadedChunks = [];
                this.uploadChunkConfig.currentChunk = 0;
                this.uploadChunkConfig.progress = 0;
            }
        },

        // 保存上传进度到localStorage
        saveProgress: function() {
            var _this = this;
            var data = {
                currentChunk: _this.uploadChunkConfig.currentChunk,
                uploadedChunks: _this.uploadChunkConfig.uploadedChunks,
                progress: _this.uploadChunkConfig.progress
            };

            localStorage.setItem(this.uploadChunkConfig.fileId, JSON.stringify(data));
        },

        handleUploadForChunkUpload: function() {
            if (!this.selectFile) return;
        
        
            // 从当前分片开始上传
            this.$emit('uploading');
            this.uploadChunkConfig.uploading = true;
            this.waitForUpload(true);
            this.updateWaitProcessMessage();
            this.uploadChunks(this.uploadChunkConfig.currentChunk); 
        },

        uploadChunks: function(i) {
            if (i > this.uploadChunkConfig.totalChunks) return;

            // 如果该分片已上传则跳过
            if (this.uploadChunkConfig.uploadedChunks.indexOf(i) > -1) {
                this.uploadChunks(++i);
                return;
            }


            var _this = this;
            try {
                var start = i * _this.uploadChunkConfig.chunkSize;
                var end = Math.min(_this.size, start + _this.uploadChunkConfig.chunkSize);
                var chunk = _this.selectFile.slice(start, end);
        
                var formData = new FormData();
                formData.append('chunk', chunk);
                formData.append('fileName', _this.fileName);
                formData.append('chunkNumber', i);
                formData.append('totalChunks', _this.uploadChunkConfig.totalChunks);
                formData.append('fileId', _this.uploadChunkConfig.fileId); // 使用文件标识，服务器知道是同一个文件
        
                _this.$http.post(_this.chunktUpload.uploadChunkAction, formData, {
                    mask: false,
                    cancelToken: _this.$http.createCancelToken(function(c) {
                        _this.uploadChunkConfig.cancelToken = c;
                    })
                }).then(function(response) {
                    var resp = response.data;
                    if (resp.status === 'uploading') {
                        _this.updateChunkProcessUI(i, resp.uploaded);
                        _this.uploadChunks(++i);

                    } else if (resp.status === 'complete') {
                        _this.mergeChunks();

                    } else {
                        _this.waitForUpload(false);
                        _this.$emit('uploading', resp);
                        _this.resetChunkUploadConfig();
                    }

                }).catch(function() {
                    _this.waitForUpload(false);
                    _this.$emit('uploading', { status: 'error' });
                    _this.resetChunkUploadConfig();
                });

            } catch (error) {
                _this.waitForUpload(false);
                if (_this.$http.isCancel(error)) {
                    console.log('Upload canceled');

                } else {
                    _this.resetChunkUploadConfig();
                    _this.clearFileValue();
                }
            }
        },

        updateChunkProcessUI: function(i, uploadedCount) {
            var loaded = (i * this.uploadChunkConfig.chunkSize) + uploadedCount;
            this.uploadChunkConfig.progress = Math.floor((loaded / this.size) * 100);
            this.uploadChunkConfig.uploadedChunks.push(i);
            this.saveProgress(); // 保存进度
            this.updateWaitProcessMessage();
        },

        // 更新动画上的进度值
        updateWaitProcessMessage: function(msg) {
            utils.page.updateWaitContent({
                group: this.loadingGroupName,
                message: msg || this.uploadChunkProcessText
            });
        },

        // 所有分片上传完成，发送合并请求
        mergeChunks: function() {
            var _this = this;
            try {
                _this.updateWaitProcessMessage(gettext('Installing now...'));
                _this.$http.post(_this.action, {
                    fileName: _this.fileName,
                    fileId: _this.uploadChunkConfig.fileId,
                    totalChunks: _this.uploadChunkConfig.totalChunks
                }, {
                    timeout: _this.timeout,
                    mask: false
                })
                .then(function(response) {
                    var resp = response.data;
                    _this.$emit('upload-finish', resp);
                })
                .finally(function() {
                    _this.waitForUpload(false);
                    localStorage.removeItem(_this.uploadChunkConfig.fileId);
                    _this.resetChunkUploadConfig();
                    _this.clearFileValue();
                });

            } catch (error) {
                console.error('Merge error', error);
            };
        },

        // 暂停上传
        pauseUpload: function() {
            if (this.uploadChunkConfig.cancelToken) {
                this.uploadChunkConfig.cancelToken.cancel('用户暂停上传');
                this.uploadChunkConfig.cancelToken = null;
            }
            this.uploadChunkConfig.uploading = false;
        },

        // 重置状态
        resetChunkUploadConfig: function() {
            this.uploadChunkConfig.uploading = false;
            this.uploadChunkConfig.currentChunk = 0;
            this.uploadChunkConfig.uploadedChunks = [];
            this.uploadChunkConfig.progress = 0;
            this.uploadChunkConfig.cancelToken = null;
        },

        waitForUpload: function(wait) {
            utils.page.wait(wait, this.loadingGroupName ? { group: this.loadingGroupName } : null);
        },
        



        /**-------------------------- 普通上传 -------------------- */

        handleFileChangeForNormal: function() {
            if (this.onlyButton && this.hasUploadFile) {
                this._submit();
            }
        },

        handleUploadForNormal: function() {
            this.hasUploadFile = true;
            if (this.onlyButton) {
                this.$refs.uploadFileInput.click();
            } else {
                this._submit();
            }
        },

        _submit: function() {
            if (!this.selectFile) {
                this.$alert(gettext('Please select file.'));
                return;
            }

            var typeCheck = this.checkSuffix(this.selectFile);
            if(!typeCheck) return;

            this.uploadFormData = new FormData();
            this.uploadFormData.append(this.name, this.selectFile);
            if (this.params) {
                for (var key in this.params) {
                    this.uploadFormData.append(key, this.params[key]);
                }
            }

            this.$emit('uploading');
            this.loading = true;
            var _this = this;
            this.$http.post(this.action, this.uploadFormData, {
                timeout: this.timeout,
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            }).then(function(response) {
                var result = response.data;
                _this.$emit('upload-finish', result);
                _this.hasUploadFile = false;
                
            }).finally(function() {
                _this.loading = false;
                _this.clearFileValue();
                _this.hasUploadFile = false;
            });
        },

        checkFile: function() {
            if (!this.selectFile) {
                this.$alert(gettext('Please select file.'));
                return false;
            }

            var typeCheck = this.checkSuffix(this.selectFile) && this.checkSize(this.selectFile);
            if (!typeCheck) return false;

            return true;
        },

        checkSize: function(file) {
            if(!this.maxSize) return true;
            var maxSizeNumber = parseInt(this.maxSize);
            if (isNaN(maxSizeNumber) || maxSizeNumber <= 0) {
                this.$alert(gettext('Invalid max size configuration.'));
                return false;
            }
            if(this.maxSize.indexOf('KB') > -1) {
                maxSizeNumber = maxSizeNumber * 1024; // 转换为字节

            } else if(this.maxSize.indexOf('MB') > -1) {
                maxSizeNumber = maxSizeNumber * 1024 * 1024; // 转换为字节

            } else if(this.maxSize.indexOf('GB') > -1) {
                maxSizeNumber = maxSizeNumber * 1024 * 1024 * 1024; // 转换为字节
            }
            var errorMsg = utils.formatString(gettext('File size exceeds the limit of {0}.'), this.maxSize);
            if (file.size > maxSizeNumber) {
                this.$alert(errorMsg);
                return false;
            }
            return true;
        },

        checkSuffix: function(file, acceptConfig) {
            if (!this.accept && !acceptConfig) return true;
            var acceptString = this.accept || acceptConfig;
            var value = file.name;
            var errorMsg = utils.formatString(this.$data._trans['typeErrorTxt'], acceptString);
            var startIndex = value.lastIndexOf('.');
            if (startIndex < 0) {
                this.$alert(errorMsg);
                return false;
            }

            var suffix = value.substr(startIndex, value.length).toLowerCase();
            var _accept = acceptString.replace(/\s+/g, '');
            var acceptArr = _accept.split(',');
            if (acceptArr.indexOf(suffix) < 0) {
                this.$alert(errorMsg);
                return false;
            }

            return true;
        },

        clearFileValue: function() {
            this.$refs.uploadFileInput.value = '';
            this.uploadFormData = null;
            this.selectFile = null;
            this.fileName = '';
            this.noFile = true;
            this.size = 0;
        }
    }
};
