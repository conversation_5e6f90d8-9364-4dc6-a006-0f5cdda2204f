var RsTabPane = {
    template: '\
        <div rsid="RsTabPane" class="tab-content" v-show="visible && show" :active="visible"><slot></slot></div>\
    ',

    props: {
        name: { type: String, defaut: '' },
        label: { type: String, default: '' },
        show: { type: Boolean, default: true },
        direction: {type: String, default: 'Left'},
        tabAttrs: { type: Object, default: null }
    },

    data: function() {
        return {
            visible: false,
            initPanes: []
        }
    },

    mounted: function() {
        this.updateNav();
        this.addAttributes2Tab();
    },

    computed: {
        moveTo: function() {
            return 'fade' + this.direction;
        }
    },

    watch: {
        label: function() {
            this.updateNav();
        },

        visible: function(b) {
            var event = b ? 'show' : 'hide';
            var _this = this;
            this.$children.forEach(function(kid) {
                _this.triggerInitPaneEmit(b, kid);
                kid.$emit(event);
            });

            this.$emit(event);
            this.triggerInitPaneEmit(b, this);
        },

        show: function() {
            this.updateNav();
        },

        tabAttrs: function() {
            this.addAttributes2Tab();
        }
    },

    methods: {
        updateNav: function() {
            this.$parent.updateNav();
        },

        triggerInitPaneEmit: function(isShow, elem) {
            if (isShow && this.initPanes.indexOf(elem) < 0) {
                this.initPanes.push(elem);
                elem.$emit('init');
            }
        },

        addAttributes2Tab: function() {
            if (this.tabAttrs) this.$parent.addAttributes2Tab(this.name, this.tabAttrs);
        }
    }
};

var RsTabs = {
    template: '\
        <div rsid="RsTabs" class="rs-tabs" :second="second">\
            <div rsid="TabBox" class="rs-tabs__header">\
                <div v-for="(item, index) in tabHeaders" \
                    class="tab" \
                    v-show="item.show" \
                    :active="item.name == current" \
                    :key="index" \
                    :ref="tabFlag + item.name" \
                    @click="selectTab(index)">{[ item.label ]}</div>\
                    <slot name="headerOthers"></slot>\
            </div>\
            <div class="rs-tabs__body"><slot></slot></div>\
        </div>\
    ',

    props: {
        activeTab: { type: [String, Number], default: 0 },
        keepId: { type: String, default: '' },
        second: { type: Boolean, default: false } // 是否为二级标签页
    },

    data: function() {
        return {
            current: this.getCurrent(),
            tabHeaders: [],
            activePane: null,
            _lastIndex: 0,
            tabFlag: '_tab_' 
        }
    },

    model: {
        prop: 'activeTab',
        event: 'tab-change'
    },
    
    mounted: function() {
        this.getTabPanes();
    },

    watch: {
        activeTab: function(val) {
            this.current = val;
        },

        current: function(val) {
            this.updateStatus();
            if (this.keepId) sessionStorage.setItem(this.keepId, this.current);
        }
    },
    
    methods: {
        getCurrent: function() {
            var tab = this.activeTab || 0;
            // TODO 将rs.js中的rs.isRefresh的逻辑移出来 或者 应该放到base.js中？？？
            if (this.keepId && rs.isRefresh && sessionStorage.getItem(this.keepId)) {
                return sessionStorage.getItem(this.keepId);
            }
            
            return tab;
        },

        isShowTab: function(tab) {
            return tab.show;
        },

        getTabPanes: function() {
            return this.$children.filter(function(item) {
                return item.$options.name === 'rs-tab-pane';
            });
        },

        updateNav: function() {
            var _this = this;
            this.tabHeaders = [];
            this.getTabPanes().forEach(function(pane, index) {
                if (!pane.name) pane.name = index;
                _this.tabHeaders.push({ label: pane.label, name: pane.name, show: pane.show });
                _this.updateStatus();
            });

            this.focusTab();
        },

        /**
         * 1、默认选中第一个页签
         * 2、当某页签因为某些条件被隐藏时，需要检查隐藏的这个页签是否为当前选中页签，若是，则需要重新定位到其他页签去
         */
        focusTab: function() {
            var _this = this;
            if (this.current) {
                var header = this.tabHeaders.filter(function(h) {
                    return _this.current == h.name;
                })[0];

                // 选中的页签被隐藏了，需要重定向到其他页签去
                if (!header || !header.show) {
                    this.focusShowTab();
                }

            } else {
                this.focusShowTab();
            }
        },

        focusShowTab: function() {
            var showHeader = this.tabHeaders.filter(function(h) {
                return h.show;
            })[0];

            this.current = showHeader.name;
        },

        updateStatus: function() {
            var _this = this;
            var currentIndex = this.$data._lastIndex;
            this.getTabPanes().forEach(function(pane, index) {
                var founded = pane.name === _this.current;
                if (founded) {
                    pane.direction = index > currentIndex ? 'Right' : 'Left';
                    pane.visible = true;
                    _this.activePane = pane;
                    _this.$data._lastIndex = index;
                } else {
                    pane.visible = false;
                }
            });
            
            this.$emit('tab-change', this.current);
        },

        selectTab: function(index) {
            this.current = this.tabHeaders[index].name;
        },

        addAttributes2Tab: function(name, attrsMap) {
            var flag = this.tabFlag;
            this.$nextTick(function() {
                var findHeaders = this.$refs[flag + name];
                if (!findHeaders) return;
                var header = findHeaders[0];
                if (!header) return;
                for (var attrName in attrsMap) {
                    var attrValue = attrsMap[attrName];
                    if (attrValue == null) {
                        header.removeAttribute(attrName);
                    } else {
                        header.setAttribute(attrName, attrValue);
                    }
                }
            });
        }
    }
}