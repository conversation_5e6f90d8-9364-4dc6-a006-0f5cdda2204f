.rs-tabs .rs-tabs__header {
    margin-bottom: 15px;
    border-bottom: 1px solid #d6d7d9;
    font-size: 13px;
    font-weight: normal;
}

.rs-tabs .rs-tabs__header > .tab {
    display: inline-block;
    padding: 6px 12px;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    color: #00a6c8;
    cursor: pointer;
    transition: background-color 0.2s ease-out, background-position-y 0.2s ease-in;
    line-height: 18px;
    font-weight: 400;
    background: linear-gradient(0deg, #fff 0%, #fff 80%, transparent 100%) no-repeat;
    background-position-y: 40px;
}

.rs-tabs .rs-tabs__header > .tab:hover {
    background-position-y: 0px;
}

.rs-tabs .rs-tabs__header > .tab[active] {
    background-color: #00a6c8;
    color: #FFF !important;
    font-weight: 400;
}

.rs-tabs .rs-tabs__header > .tab[active]:hover {
    background-position-y: 40px;
}

.rs-tabs[second] .rs-tabs__header {
    display: table;
    border: 1px solid #d6d7d9;
    background: #f5f5f5;
}
.rs-tabs[second] .rs-tabs__header .tab {
    color: #9e9e9e !important;
}
.rs-tabs[second] .rs-tabs__header .tab[active] {
    background: #fff;
    color: #212121 !important;
}

/* .rs-tabs .rs-tabs__body > .tab-content:not([active]) {
    display: none;
} */