# -*- coding: utf-8 -*-

'''
Created on Jun 25, 2015

@author: <PERSON><PERSON><PERSON>
'''
from distutils.errors import DistutilsFileError
from distutils.file_util import move_file
import json
import os
import io
import csv
import codecs
import sys
import subprocess
import shutil
import re
import tempfile
import logging.config
import _strptime # workaround for https://bugs.python.org/issue7980
import time
import multiprocessing
import random
import binascii
import gzip
import socket
import struct
import md5
import base64
from binascii import hexlify
import ctypes
import hashlib
import string
from datetime import datetime, timedelta
from CommonLibs import compress_IP
from netaddr import cidr_merge, IPNetwork
from xml.dom.minidom import parse
import tornado
import ssl
from CommonLibs import valid_IP
import zipfile
import psutil
import bisect
from cpu_memory_info import get_cpu_count_from_cgroup, get_memory_limit_from_cgroup

DM_SEND_PORT = 20170

class RESULT():
    OK = 'OK'
    UNKNOWN = 'UNKNOWN'
    IN_QUEUE = 'IN_QUEUE'
    BAD_COMMAND = 'BAD_COMMAND'
    EXCEPTION = 'EXCEPTION'
    EXE_FAILED = 'EXE_FAILED'
    VALIDATE_FAILED = 'VALIDATE_FAILED'
    NOT_FOUND = 'NOT_FOUND'
    NOT_FRESH = 'NOT_FRESH'
    VERSION_MISMATCH = 'VERSION_MISMATCH'
    INVALID_PARAMS = 'INVALID_PARAMS'
    UNEXPECTED = 'UNEXPECTED'
    NO_DATA = 'NO_DATA'
    PASSWORD_REQUIRED = 'PASSWORD_REQUIRED'
    PASSWORD_INCORRECT = 'PASSWORD_INCORRECT'
    NOT_CONNECTED_TO_ZK = 'NOT_CONNECTED_TO_ZK'
    LICENSE_CPU_MEM_EXCEED = 'LICENSE_CPU_MEM_EXCEED'
    LOG_ARCHIVE_SERVER_EXIST = 'LOG_ARCHIVE_SERVER_EXIST'
    BIGDATASERVER_EXIST = 'BIGDATASERVER_EXIST'
    DATAMINERSERVER_EXIST = 'DATAMINERSERVER_EXIST'
    REPUTATION_SERVER_EXIST = 'REPUTATION_SERVER_EXIST'
    CANNOT_CONNECT_TO_SERVER = 'CANNOT_CONNECT_TO_SERVER'
    FAILED_TO_ALLOCATE_NODE_ID = 'FAILED_TO_ALLOCATE_NODE_ID'
    LAYOUT_MISMATCH = 'LAYOUT_MISMATCH'
    LANGUAGE_MISMATCH = 'LANGUAGE_MISMATCH'
    TRANSPARENT_MODE_ENABLE = 'TRANSPARENT_MODE_ENABLE'
    ADAPTER_NOT_MATCH = 'ADAPTER_NOT_MATCH'
    MODEL_CATEGORY_NOT_MATCH = 'MODEL_CATEGORY_NOT_MATCH'
    IP_CONFLICT = 'IP_CONFLICT'
    IP_VERSION_NOTMATCH = 'IP_VERSION_NOT_MATCH'
    IS_JOIN_CLUSTER = 'IS_JOIN_CLUSTER'
    CREATE_TEMP_NODE_FAIL = 'CREATE_TEMP_NODE_FAIL'
    FILE_NONET = 'FILE_NOENT'
    NOT_STARTED = 'NOT_STARTED'
    ROUTE_ERROR = 'ROUTE_ERROR'
    IS_MODIFY_ROLE = 'IS_MODIFY_ROLE'

TCP_CONNECT_TIMEOUT = 10


DEFAULT_SSL_CIPHERS = 'kEECDH:!RC4:!eNULL:!aNULL:!DES:DES-CBC3-SHA'
DEFAULT_PROXY_SSL_CIPHERS = 'DEFAULT'
DEFAULT_SSL_PROTOCOLS = 'TLSv1, TLSv1.1, TLSv1.2, TLSv1.3'
DEFAULT_PROXY_SSL_PROTOCOLS = 'SSLv3, TLSv1, TLSv1.1, TLSv1.2'
DEFAULT_SECURITY_SSL_PROTOCOLS = 'TLSv1.2, TLSv1.3'
DEFAULT_SECURITY_SSL_CIPHERS = 'kEECDH:!RC4:!eNULL:!aNULL:!DES:!3DES:!SHA1:!ECDHE-RSA-AES128-SHA:!ECDHE-RSA-AES128-SHA256:!ECDHE-RSA-AES256-SHA:!ECDHE-RSA-AES256-SHA384:!ECDHE-RSA-AES256-GCM-SHA384:!ECDHE-RSA-AES128-GCM-SHA256'
DEFAULT_SSL_CHINA_SECURITY_CIPHERS = 'ECC-SM4-SM3:ECDHE-SM4-SM3'

DEFAULT_PERF_PARAMS_OPT_WORKER_CONNECTIONS = 125000

CORE_PATH = '/tmp/core'
CORE_SIMPLE_PATH = '/tmp/core_simple'

class AccessTimeParser():
    def __init__(self):
        self._last_str = None
        self._last_time = None

    def parse(self, s):
        # Parse input format: '06/Sep/2015:13:41:14 +0800'
        date, hours, minutes, sec = s.split(':')
        sec, _, timezone = sec.partition(' ')

        if date == self._last_str:
            t = self._last_time
        else:
            t = time.strptime(date, '%d/%b/%Y')
            t = int(time.mktime(t))
            self._last_str = date
            self._last_time = t

        t = t + (int(hours) * 60 + int(minutes)) * 60 + int(sec)

        # Parse timezone manually
        log_zone = -(int(timezone) / 100)
        local_zone = time.timezone / 3600
        if log_zone != local_zone:
            est = 3600 if time.tzname[0] == 'EST' else 0
            t += (log_zone - local_zone) * 3600 + est
        return t

def error_exit(msg, code=1):
    logging.error(msg)
    sys.exit(code)


def get_release_file(name=None):
    '''
    asp_conf_ctrl.py is in asp/release/bin/, it's parent dir is 'release'
    '''
    root = os.path.abspath(os.path.join(__file__, '../../../'))
    if name:
        return os.path.join(root, name)
    else:
        return root

def get_sync_file_path(relativepath = None):
    dir_path = get_release_file('web_admin/static/sync_file')
    if relativepath:
        return os.path.join(dir_path, relativepath)
    else:
        return dir_path

def get_template_file(name=None):
    '''
    asp_conf_ctrl.py is in asp/release/bin/, it's parent dir is 'release'
    '''
    root = os.path.abspath(os.path.join(__file__, '../../../../template'))
    if name:
        return os.path.join(root, name)
    else:
        return root


def get_data_file(name):
    '''
    asp_conf_ctrl.py is in asp/release/bin/, it's parent dir is 'release'
    '''
    if not os.path.exists('/var/asp_data'):
        if name.startswith('/'):
            name = os.path.split(name)[1]
        return get_release_file(name)

    return os.path.join('/var/asp_data', name)


def get_sibling_file(fn_brother, name):
    return os.path.abspath(os.path.join(os.path.dirname(__file__), name))


g_path_variables = {
    'asp_release_dir': get_release_file(),
    'asp_template_dir': get_template_file()
}


def set_path_variable(name, value):
    global g_path_variables
    g_path_variables[name] = value


def replace_path_variable(text):
    global g_path_variables

    content = text.split('%%')
    out = []
    for i in range(len(content)):
        if i % 2 == 1:
            # Macro
            name = content[i]
            value = g_path_variables.get(name.strip())
            if value:
                out.append(str(value))
            elif not value:
                raise Exception('Cannot find value for %%{0}%%'.format(name))
        else:
            out.append(content[i])
    return ''.join(out)


def insert_or_replace_text_in_file(re_pattern_str, text, fn, replace_with_reg_ex=True):
    if replace_text_in_file(re_pattern_str, text, fn, replace_with_reg_ex):
        return
    insert_line_for_file(text, fn)


def exe(cmd_line, error_on_exit=True, excep_on_exit=True):
    cmd_line = replace_path_variable(cmd_line)

    try:
        logging.debug('Execute: ' + cmd_line)
        ret = subprocess.call(cmd_line, shell=True)
        if ret != 0 and error_on_exit:
            raise Exception('!!! The return code is not 0, but: ' + str(ret))
        return ret
    except Exception as e:
        logging.exception('Got exception while execute: ' + cmd_line)
        if excep_on_exit:
            raise e


def check_output_ex(*popenargs, **kwargs):
    # Refer subprocess.check_output
    process = subprocess.Popen(stdout=subprocess.PIPE, stderr=subprocess.STDOUT, *popenargs, **kwargs)
    output, outerr = process.communicate()
    retcode = process.poll()
    if retcode:
        cmd = kwargs.get("args")
        if cmd is None:
            cmd = popenargs[0]
        raise subprocess.CalledProcessError(retcode, cmd, output=output)
    return output


def exe_with_output(cmd_line, excep_on_exit=True, quiet=False):
    """
    :param cmd_line:
    :param excep_on_exit:
    :param quiet:
    :rtype: (int, str, str)
    """
    cmd_line = replace_path_variable(cmd_line)

    try:
        if not quiet:
            logging.debug('Execute: {}'.format(cmd_line[:250]).replace('Ph0en1x!', '*******'))
        return [0, check_output_ex(cmd_line, shell=True), cmd_line]
    except subprocess.CalledProcessError as e:
        return [e.returncode, e.output, e.cmd]
    except Exception as e:
        if not quiet:
            logging.error('Got exception while execute: ' + cmd_line)
        if excep_on_exit:
            raise e
        return [-1, str(e), cmd_line]


def exe_with_input_output(cmd_line, excep_on_exit=True, input_value=None):
    if input_value is None:
        return exe_with_output(cmd_line, excep_on_exit)

    cmd_line = replace_path_variable(cmd_line)
    try:
        logging.debug('Execute: \"{}\" with input'.format(cmd_line[:250]).replace('Ph0en1x!', '*******'))
        # Refer subprocess.check_output
        process = subprocess.Popen(
            cmd_line, shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            stdin=subprocess.PIPE,
        )
        process.stdin.write(input_value)
        output, outerr = process.communicate()
        retcode = process.poll()
        if retcode:
            return retcode, "stdout: {}, stderr: {}".format(output, outerr), cmd_line
        return [0, output, cmd_line]
    except subprocess.CalledProcessError as e:
        return [e.returncode, e.output, e.cmd]
    except Exception as e:
        logging.error('Got exception while execute: \"{}\" with error: {}'.format(cmd_line, e))
        if excep_on_exit:
            raise e
        return [-1, str(e), cmd_line]


def safe_move_files(src, dstDir):
    try:
        for _file in src:
            move_file(_file, dstDir, verbose=0)
        return True
    except DistutilsFileError as e:
        logging.error('Move file error: {0}'.format(str(e)))
        return False


def remove_file(fn, error_on_exit=True):
    fn = replace_path_variable(fn)
    if not os.path.exists(fn):
        return
    try:
        logging.debug('Remove: ' + fn)
        os.remove(fn)
    except Exception as e:
        logging.error('Failed to remove: {} {}'.format(fn, e))
        if error_on_exit:
            raise e


def remove_dir(folder, ignore_errors=True):
    folder = replace_path_variable(folder)

    def print_error_on_remove(self, fn, info):
        logging.error('Failed to remove: {0}, error: {1}'.format(fn, info))

    logging.debug('Remove Dir: ' + folder)
    shutil.rmtree(folder, ignore_errors=ignore_errors, onerror=print_error_on_remove)


# exception can be a list for multi sub folder name or a str for one sub folder name
def remove_dir_but_exception(folder, exception, ignore_errors=True):
    if not exception:
        return remove_dir(folder, ignore_errors)

    ret, release_sub_dirs, cmd_line = exe_with_output('ls -l ' + folder)
    if ret != 0:
        logging.error('List error. cmd: {0} return: {1}'.format(cmd_line, ret))
        sys.exit(1)
    else:
        release_sub_dirs = release_sub_dirs.split('\n')
        release_sub_dirs.remove(release_sub_dirs[0])
        release_sub_dirs.remove(release_sub_dirs[-1])
        for item in release_sub_dirs:
            sub_dir = item.split(' ')
            sub_dir = sub_dir[-1]
            if (isinstance(exception, list) and sub_dir in exception) or \
                    (isinstance(exception, str) and sub_dir == exception):
                continue
            sub_full_path = os.path.join(folder, sub_dir)
            if item.startswith('d'):
                remove_dir(sub_full_path, ignore_errors)
            else:
                remove_file(sub_full_path, ignore_errors)


def copy_files(src, dst):
    src = replace_path_variable(src)
    dst = replace_path_variable(dst)
    logging.debug('Copy: ' + src)
    logging.debug('To: ' + dst)
    if os.path.isdir(src):
        copytree(src, dst, [])
    else:
        shutil.copy(src, dst)


def read_file(fn):
    fn = replace_path_variable(fn)
    with open(fn, 'rb') as fp:
        return fp.read()


def save_file(fn, content):
    fn = replace_path_variable(fn)
    with open(fn, 'wb') as fp:
        if type(content) == unicode:
            content = content.encode('utf-8')
        fp.write(content)


def is_file_content_same(fn, content):
    if type(content) == unicode:
        content = content.encode('utf-8')
    fn = replace_path_variable(fn)
    value = read_file(fn)
    return content == value


def insert_line_for_file(text, fn):
    fn = replace_path_variable(fn)
    value = read_file(fn)
    if text not in value:
        if not value.endswith('\n'):
            value += '\n'
        if not text.endswith('\n'):
            text += '\n'
        save_file(fn, value + text)


def replace_text_in_file(re_pattern_str, text, fn, replace_with_reg_ex=True):
    fn = replace_path_variable(fn)
    value = read_file(fn)

    if replace_with_reg_ex:
        ret = re.sub(re_pattern_str, text, value, flags=re.DOTALL | re.MULTILINE)
    else:
        ret = value.replace(re_pattern_str, text)
    if ret == value:
        logging.debug('Nothing was replaced in file: ' + fn)
        return False

    save_file(fn, ret)
    return True


def parse_nginx_access_log_format(fn, log_format_prefix='log_format\s+main'):
    value = read_file(fn)

    values = re.findall(log_format_prefix + '\s+(.*?);', value, flags=re.DOTALL | re.MULTILINE)
    return values[0]


def is_service_running(name):
    return exe('service {0} status >/dev/null'.format(name), False) == 0


def is_service_running_ex(name):
    cmd_line = 'service {0} status'.format(name)
    code, output, _ = exe_with_output(cmd_line)
    return [code, code == 0, output]


def copytree(src, dst, copied_lists):
    src = replace_path_variable(src)
    dst = replace_path_variable(dst)
    """Recursively copy a directory tree using copy2().

    """
    names = os.listdir(src)

    if not os.path.exists(dst):
        os.makedirs(dst)
    for name in names:
        srcname = os.path.join(src, name)
        dstname = os.path.join(dst, name)
        if os.path.isdir(srcname):
            copytree(srcname, dstname, copied_lists)
        else:
            shutil.copy2(srcname, dstname)
            copied_lists.append(dstname)

    shutil.copymode(src, dst)
    shutil.copystat(src, dst)


def get_manifest_info():
    fn = get_release_file('../manifest.json')
    if not os.path.exists(fn):
        fn = get_release_file('../../manifest.json')
    if not os.path.exists(fn):
        return {}

    return load_json(fn, {})


def get_build_hash_and_layout_hash():
    info = get_manifest_info()
    return '{}.{}.layout{}'.format(info['git_commit'], info['build_no'], hashlib.sha1(info['layout']).hexdigest()[:6])


def get_version():
    '''
    Return [version, build_no, git_commit, build_time]
    version: 1.0.1
    build_no: 123456823
    git_commit: w3e1a9
    build_time: 2015-07-32T12:03:05
    is_debug: 1
    '''
    info = get_manifest_info()
    return [info.get('version_name'),
            info.get('build_no'),
            info.get('git_commit'),
            info.get('build_time'),
            info.get('cluster_version'),
            info.get('is_debug'),
            info.get('is_prod_pkg'),
            info.get('layout'),
            'phoenix' in info,
            info.get('waf_engine_version'),
            info.get('ruleset_version'),
            info.get('ruleset_change'),
            info.get('threat_intelligence_engine_version'),
            info.get('threat_intelligence_version'),
            info.get('llm_engine_version'),
            info.get('llm_corpus_version'),
            ]


def get_sw_version():
    manifest = get_manifest_info()
    return '{0}.{1}'.format(manifest['git_commit'], manifest['build_no'])


def is_sw_version_same(v1, v2):
    return v1.split('.')[0] == v2.split('.')[0]


def is_web_filter_installed():
    info = get_manifest_info()
    return info.get('web_filter_enabled') == 1


def get_language():
    info = get_manifest_info()
    return info.get('language')


def get_product_type():
    info = get_manifest_info()
    return info.get('product_type')


def get_license_product_category():
    info = get_manifest_info()
    return info.get('license_product_category')

def is_product_category_NGWAF():
    # 为后续扩展，不使用相等判断。
    return 'NGWAF' in get_license_product_category()


def is_layout_valid(product):
    if not product:
        logging.error('Product info not exist in license.')
        return False
    license_product_category = get_manifest_info().get('license_product_category')
    if is_product_category_NGWAF():
        is_valid = (product.find('-' + license_product_category + '-') > 0 or product.find('-' + license_product_category + 'XC' + '-') > 0 or product.find('-' + 'WAF' + '-') > 0)
        return is_valid

    is_valid = (product.find('-' + license_product_category + '-') > 0 or product.find('-' + license_product_category + 'XC' + '-') > 0)
    return is_valid


def is_primary_protection():
    # 存在动态时，动态是否为初级防护，Safeplus layout 和NGWAF是初级防护
    return get_product_type() == 'Safeplus' or is_product_category_NGWAF()

def is_dynamic_protection(license_info):
    # 是否有动态防护
    product_type = get_product_type()
    if product_type == 'Botgate':
        if not is_product_category_NGWAF():
            return True
        if license_info.get_license_protected_level() == 'LE':
            return True
    if product_type == 'Safeplus':
        if license_info.get_license_protected_level() == 'LE':
            return True
    return False

def get_is_safeplus_or_ngwaf_we(license_info):
    # 界面展示权限，NGWAF中WE和Safeplus layout一致。
    return get_product_type() == 'Safeplus' or is_ngwaf_we(license_info)

def get_is_botgate_or_ngwaf_le(license_info):
    return is_botgate(license_info) or is_ngwaf_le(license_info)

def is_ngwaf_we(license_info):
    if is_product_category_NGWAF():
        if license_info.get_license_protected_level() == "WE":
            return True
    return False

def is_ngwaf_le(license_info):
    # ngwaf中NE的protected_level已提前处理为了LE。
    if is_product_category_NGWAF():
        if license_info.get_license_protected_level() != "WE":
            return True
    return False

def is_botgate(license_info):
    if get_product_type() == 'Botgate':
        if not is_ngwaf_we(license_info):
            return True
    return False


def get_layout(short_name=False):
    info = get_manifest_info()
    layout = info.get('layout', '')
    if short_name:
        if info.get('product_type') == 'ApiBotDefender' or info.get('product_type') == 'ApiSecurityAudit':
            return 'layout_p'
        else:
            return 'layout_' + layout[0]
    else:
        return layout


def get_layout_and_ext_info():
    info = get_manifest_info()
    return ':{}-{}-{}'.format(info.get('layout'), info.get('is_debug'), info.get('is_prod_pkg'))

def get_bidding_ver():
    info = get_manifest_info()
    return info.get('bidding_ver')

def is_debug_install():
    info = get_manifest_info()
    return info.get('is_debug')

def dynamic_rename_keys(keyname, var_info, length):
    value = base64.encodestring(md5.md5(keyname + var_info).digest())
    value = filter(str.isalnum, value)
    for i in range(len(value)):
        if not str.isdigit(value[i:i+1]):
            break
    return value[i:i+length]

def getVersionLog():
    p = subprocess.Popen('cd {} && git log -1 --pretty=format:%h && cd -'.format(get_release_file()),
                         shell=True, stdout=subprocess.PIPE)
    out = p.stdout.read()
    return out

def getGitCommitId():
    version = getVersionLog()
    if version:
        return version[0:7]
    else:
        raise 'Failed to get version with git'

def get_commit_id(commitIdFromGitLog=False):
    if commitIdFromGitLog:
        commit_id = getGitCommitId()
    else:
        manifest = get_manifest_info()
        commit_id = manifest['git_commit']
    return commit_id


def rename_txsafe_resources_ex(commitIdFromGitLog=False, conf=None):
    if conf is None:
        from asp_conf_ctrl import ConfDb
        conf = ConfDb()
    default_fields = {k: v['default'] for k, v in conf.get_default_token_fields().items()}
    token_rename = conf.get_value('nginx/token_rename', default={})
    token_rename_enable = token_rename.get('enable', False)
    if token_rename_enable:
        token_fields = token_rename.get('fields', default_fields)
    else:
        token_fields = default_fields
    txsafe_res_path = token_fields.get('ras_path', '4QbVtADbnLVIc')
    default_entry_js_name = 'e.FxJzG50F'
    entry_js_name = token_fields.get('entry_js', default_entry_js_name)
    default_primary_corejs_name = 'p.FxJzG50F'
    primary_corejs_name = token_fields.get('primary_corejs', default_primary_corejs_name)
    default_gm_primary_corejs_name = 'gp.FxJzG50F'
    gm_primary_corejs_name = token_fields.get('gm_primary_corejs', default_gm_primary_corejs_name)
    default_essential_corejs_name = 'd.FxJzG50F'
    essential_corejs_name = token_fields.get('essential_corejs', default_essential_corejs_name)
    default_gm_essential_corejs_name = 'gd.FxJzG50F'
    gm_essential_corejs_name = token_fields.get('gm_essential_corejs', default_gm_essential_corejs_name)
    default_advanced_corejs_name = 'c.FxJzG50F'
    advanced_corejs_name = token_fields.get('advanced_corejs', default_advanced_corejs_name)
    default_gm_advanced_corejs_name = 'gc.FxJzG50F'
    gm_advanced_corejs_name = token_fields.get('gm_advanced_corejs', default_gm_advanced_corejs_name)
    default_data_collection = 'jW39ezbWPr'
    data_collection = token_fields.get('data_collection', default_data_collection)
    default_flash_font = '4rJFe6jNL52p'
    flash_font = token_fields.get('flash_font', default_flash_font)
    default_recaptcha_path = 'IEwlsiow'
    recaptcha_path = token_fields.get('recaptcha_path', default_recaptcha_path)
    client_features_path = token_fields.get('client_features_path', 'Zjg3MTRhZDMw')

    commit_id = get_commit_id(commitIdFromGitLog)
    primary_corejs_name += '.{}'.format(commit_id)
    essential_corejs_name += '.{}'.format(commit_id)
    advanced_corejs_name += '.{}'.format(commit_id)
    default_primary_corejs_name += '.{}'.format(commit_id)
    default_essential_corejs_name += '.{}'.format(commit_id)
    default_advanced_corejs_name += '.{}'.format(commit_id)

    return {
        'expected_token_rename_names': default_fields.keys(),
        'txsafe_resource_dir': txsafe_res_path,
        'entry_js_name': entry_js_name,
        'default_entry_js_name': default_entry_js_name,
        'primary_corejs_name': primary_corejs_name,
        'default_primary_corejs_name': default_primary_corejs_name,
        'gm_primary_corejs_name': gm_primary_corejs_name,
        'default_gm_primary_corejs_name': default_gm_primary_corejs_name,
        'essential_corejs_name': essential_corejs_name,
        'default_essential_corejs_name': default_essential_corejs_name,
        'gm_essential_corejs_name': gm_essential_corejs_name,
        'default_gm_essential_corejs_name': default_gm_essential_corejs_name,
        'advanced_corejs_name': advanced_corejs_name,
        'default_advanced_corejs_name': default_advanced_corejs_name,
        'gm_advanced_corejs_name': gm_advanced_corejs_name,
        'default_gm_advanced_corejs_name': default_gm_advanced_corejs_name,
        'data_collection': data_collection,
        'default_data_collection': default_data_collection,
        'flash_font': flash_font,
        'default_flash_font': default_flash_font,
        'recaptcha_path': recaptcha_path,
        'default_recaptcha_path': default_recaptcha_path,
        'client_features_path': client_features_path,
    }


# fix token used for bidding purpose, need to:
# 1. rename url token, cookie token, etc.
# 2. rename reload status code,
# 3. rename the attributes in DOM,
# 4. rename identifiers in javascript,
# note: rename token feature on webconsole should be hidden when it is bidding version

FIX_TOKEN_APPROACH_DEFAULT = 0
FIX_TOKEN_APPROACH_BIDDING_A = 1
FIX_TOKEN_APPROACH_BIDDING_B = 2
FIX_TOKEN_APPROACH_BIDDING_C = 3

EXT_DICT_FOR_BUILDING = {
    "JS_IDENTIFIER_PREFIX":     ["_$",          "o0",           "l1",       "Z2"],          # 2 characters, otherwise need to change cpp code
    "DOM_DATA_TS_TAG":          ["datas-ts",    "sec_encode",   "l22l",     "z2xx"],        # lower case
    "TOKEN_INDEX_PREFIX":       ["=",           "%",            "_",        "&"],
    "REMOVE_NODE_TAG":          ["r='m'",       "hid='d'",      "l='o'",    "z='Z'"],       # REMOVE_NODE_ATTRIBUTE='REMOVE_NODE_VALUE'
    "REMOVE_NODE_ATTRIBUTE":    ["r",           "hid",          "l",        "z"],
    "REMOVE_NODE_VALUE":        ["m",           "d",            "'o'",      "'Z'"],
    "SZ_JAVASCRIPT_VOID0":      ["javascript: void(0);", "javascript: o0_sec();", "javascript: l1314();", "javascript: zZ2xX();"],
}

RELOAD_STATUS_CODE = ["412", "456", "482", "494"]

# the url token, cookie token name will be construct by the chars in FIX_TOKEN_CHOICES
FIX_TOKEN_CHOICES = ["", "oO0xX", "1234567890", "zZ2yY"]


def get_fix_token_approach(bidding_ver=None):
    """
    During building phase, the 'bidding_ver' in manifest.json is not updated, so input bidding_ver is need.
    After setup, we can get 'bidding_ver' from manifest.json
    """
    if not bidding_ver:
        bidding_ver = get_bidding_ver()

    if bidding_ver:
        bidding_ver = bidding_ver.lower()

    if bidding_ver == 'a':
        return FIX_TOKEN_APPROACH_BIDDING_A
    elif bidding_ver == 'b':
        return FIX_TOKEN_APPROACH_BIDDING_B
    elif bidding_ver == 'c':
        return FIX_TOKEN_APPROACH_BIDDING_C
    else:
        return FIX_TOKEN_APPROACH_DEFAULT


def get_ext_dict_for_building(bidding_ver):
    approach_index = get_fix_token_approach(bidding_ver)
    ext_keys_dict = {k: v[approach_index] for k, v in EXT_DICT_FOR_BUILDING.items()}
    return ext_keys_dict


def is_rename_to_fix_token():
    """
    :return: 0 - NO;
             >0 - YES;
    """
    return get_fix_token_approach()


def get_reload_status_code():
    return RELOAD_STATUS_CODE[get_fix_token_approach()]


def get_fix_token_choices():
    return FIX_TOKEN_CHOICES[get_fix_token_approach()]


def get_js_identifier_prefix(bidding_ver):
    return EXT_DICT_FOR_BUILDING["JS_IDENTIFIER_PREFIX"][get_fix_token_approach(bidding_ver)]


def is_under_source_repo():
    s = os.path.abspath(os.path.dirname(__file__))
    return s.endswith('/shared/bin/asp_utils')


def __set_crontab_task(task, user):
    tempfn = tempfile.mktemp()
    task = task + '\n'
    save_file(tempfn, task)
    exe(' '.join(['crontab', '-u', user, tempfn]))
    remove_file(tempfn)


def add_crontab_task(fn, user='root'):
    retcode, cur_task, _ = exe_with_output(' '.join(['crontab', '-l', '-u', user]))
    if retcode != 0 and 'no crontab for' not in cur_task:
        raise Exception('!!! Failed to get crontab task list, retcode = ' + str(retcode))
    if 'no crontab for' in cur_task:
        cur_task = ''
    cur_task = set(filter(lambda x:x, map(lambda x:x.strip(), cur_task.split('\n'))))
    content = set(filter(lambda x:x, map(lambda x:x.strip(), read_file(fn).split('\n'))))

    if len(cur_task | content) != len(cur_task):
        logging.debug('add to task')
        task = '\n'.join(cur_task | content)
        __set_crontab_task(task, user)
    else:
        logging.debug(fn + " is already in crontab task.")


def rm_user_all_crontab_task(user='root'):
    retcode, cur_task, _ = exe_with_output(' '.join(['crontab', '-l', '-u', user]))
    if retcode != 0 and 'no crontab for' not in cur_task:
        logging.info('!!! Failed to get crontab task list, retcode = ' + str(retcode))
        return
    if 'no crontab for' in cur_task:
        return
    __set_crontab_task("", user)


def rm_crontab_task(fn, user='root'):
    retcode, cur_task, _ = exe_with_output(' '.join(['crontab', '-l', '-u', user]))
    if retcode != 0 and 'no crontab for' not in cur_task:
        logging.info('!!! Failed to get crontab task list, retcode = ' + str(retcode))
        return
    if 'no crontab for' in cur_task:
        return

    cur_task = set(filter(lambda x:x, map(lambda x:x.strip(), cur_task.split('\n'))))
    content = set(filter(lambda x:x, map(lambda x:x.strip(), read_file(fn).split('\n'))))

    if len(cur_task - content) != len(cur_task):
        task = '\n'.join(cur_task - content)
        __set_crontab_task(task, user)
    else:
        logging.debug(fn + ' is not in crontab task. Do nothing.')


# Return available disk size in KB
def get_avail_space(path='/'):
    statvfs = os.statvfs(path)
    size = statvfs[0] * statvfs[3] / 1024
    return size


# Return Total disk size in KB
def get_total_space(path='/'):
    statvfs = os.statvfs(path)
    size = statvfs[0] * statvfs[2] / 1024
    if path == '/var' and not is_disk_partition('/var'):
        return max(0, size - 10*1024*1024)
    else:
        return size


# Return dir is disk partition
def is_disk_partition(dir_name):
    return dir_name in [i.split()[1] for i in open('/proc/mounts').read().split('\n') if i]


def is_disk_mounted(device):
    if not os.path.exists(device):
        raise Exception('{0} is not a valid device'.format(device))

    # Example output:
    # riversec@box:~$ lsblk -r -n -o NAME,TYPE,MOUNTPOINT /dev/sda | grep -vw disk | awk '{print $1,$NF}'
    # sda1 /boot
    # sda2 part
    # sda5_crypt /
    # vg_var-lv_var /var
    cmd = "lsblk -r -n -o NAME,TYPE,MOUNTPOINT " + device + " | grep -vw disk | awk '{print $1,$NF}'"
    blk_info = subprocess.check_output(cmd, shell=True).strip('\n')
    if blk_info == '':
        return False
    partitions = blk_info.split('\n')
    partitions = map(lambda line: line.split(' '), partitions)
    pt_list = [pi[0] for pi in partitions]
    mp_list = [pi[1] for pi in partitions]

    if ''.join(mp_list).find('/') != -1:
        return True

    for partition in pt_list:
        if mount_after_reboot(partition):
            return True
    return False


def mount_after_reboot(name):
    # name & uuid should not be empty
    if name == '' or not re.match(r'^[\w,-]+$', name):
        return False
    cmd = "sudo blkid | grep -E '^/dev/(\w+-*\w*/)*" + name + ":.*$' | awk -F\\\" '{print $2}'"
    uuid = subprocess.check_output(cmd, shell=True).strip('\n')
    if uuid == '':
        return False
    cmd = "cat /etc/fstab | grep -Pv \"^\s*#\""
    mnt_msg = subprocess.check_output(cmd, shell=True).strip('\n')
    if re.search(r'%s\s+|%s' % (name, uuid), mnt_msg) is None:
        return False
    return True


def start_service_on_boot(service):
    if os.path.exists("/usr/sbin/chkconfig"):
        exe("/usr/sbin/chkconfig --add %s" % service)
        exe("/usr/sbin/chkconfig --level 345 %s on" % service)
    else:
        exe("/usr/sbin/update-rc.d -f %s defaults" % service)


def remove_service_on_boot(service):
    if os.path.exists("/usr/sbin/chkconfig"):
        exe("/usr/sbin/chkconfig --del %s" % service, False)
    else:
        exe("/usr/sbin/update-rc.d -f %s remove" % service)


def format_utc_date_time(t):
    return time.strftime('%Y-%m-%d %H:%M:%S', time.gmtime(t))


def load_json(fn, def_val):
    try:
        with open(fn, 'rb') as fp:
            status = json.load(fp, encoding='utf-8')
            return status
    except Exception as e:
        logging.warning('Failed to open file: {0}, {1}'.format(fn, str(e)))
        return def_val


def save_json(fn, obj, indent=1):
    with open(fn, 'wb') as f:
        s = json.dumps(obj, indent=indent)
        f.write(s)


def get_rs_servers_ssl_ciphers():
    if ssl.get_protocol_name(5) == 'PROTOCOL_TLSv1_2' or os.path.exists('/opt/libssl/_ssl.x86_64-linux-gnu.so'):
        return 'ALL:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!SRP:!RC5:!SHA1:!TLSv1:!kRSA'
    else:
        return 'ALL:!aNULL:!eNULL:!EXPORT:!DES:!RC4:!MD5:!PSK:!aECDH:!SRP:!RC5:!SHA1:!TLSv1'
        # 较老的python版本(<2.7.9)不能配置!kRSA
        # !kRSA表示禁用RSA作为密钥交换算法
        # python(<2.7.9)时python的_ssl.so未开启ecdh
        # 所以老版本python配置!kRSA时会造成没有算法套件可用


class _SSLERR0Filter(logging.Filter):
    """
    当Tornado 4.3 在 UOS/Kylin上跑，会出现SSL握手错误
    这个错误可以忽略，所以在logging加filter忽略错误
    此问题在 tornado 6.0 解决
    https://github.com/tornadoweb/tornado/pull/2518/commits/e63980fb6b41ac5f8fda278331229fbcffd7f46d
    """
    def __init__(self, name=''):
        super(_SSLERR0Filter, self).__init__(name)
        self.enable = tornado.version == '4.3' and current_machine_hardware_name() != 'x86_64'

    def filter(self, record):
        """
        :type record: logging.LogRecord
        """
        if not self.enable:
            return True
        a = record.filename.endswith('iostream.py') and (record.lineno == 546 or record.lineno == 511)
        b = record.filename.endswith('ioloop.py') and (record.lineno == 629 or record.lineno == 883)
        return not (a or b)

def config_logging(log_fn=None, max_fn_size=1024 * 1024 * 5, level='DEBUG', is_console_on=True,
                   log_format='%(levelname)s [%(asctime)s] [%(module)s] [%(process)d] [%(thread)d] %(message).10240s',
                   level_console='DEBUG', extra_loggers=None):
    if log_fn and not os.path.exists('/var/log/asp'):
        # Under source code?
        log_fn = get_release_file(os.path.split(log_fn)[1])

    extra_loggers = extra_loggers if isinstance(extra_loggers, dict) else {}
    for name in extra_loggers:
        extra_loggers[name].setdefault('handlers', [])
        extra_loggers[name].setdefault('level', level)
        extra_loggers[name].setdefault('filters', ['ssl_err0_filter'])

    LOGGING = {
        'version': 1,
        'disable_existing_loggers': True,
        'formatters': {
            'verbose': {
                'format': log_format
            },

            'simple': {
                'format': '%(asctime)s %(process)d %(levelname)s %(filename)s:%(lineno)d %(funcName)s() - %(message).10240s'
            },
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'simple',
                'level': level_console,
                'stream': 'ext://sys.stdout',
            },
        },
        'filters': {
            'ssl_err0_filter': {
                '()': _SSLERR0Filter,
                'name': 'ssl_err0_filter'
            }
        },
        'loggers': extra_loggers,
        'root': {
            'level': level,
            'handlers': [],
        },
    }

    if is_console_on:
        LOGGING.get('root').get('handlers').append('console')
        for name in LOGGING.get('loggers'):
            if 'console' not in LOGGING.get('loggers')[name]['handlers']:
                LOGGING.get('loggers')[name]['handlers'].append('console')

    if log_fn:
        LOGGING.get('handlers')['file'] = {
            'class': 'logging.handlers.RotatingFileHandler',
            'formatter': 'verbose',
            'filename': log_fn,
            'maxBytes': max_fn_size,
            'backupCount': 3
        }

        LOGGING.get('root').get('handlers').append('file')
        for name in LOGGING.get('loggers'):
            if 'console' not in LOGGING.get('loggers')[name]['handlers']:
                LOGGING.get('loggers')[name]['handlers'].append('console')

        logging.info('Using log file: ' + log_fn)

    logging.config.dictConfig(LOGGING)


MAX_LIMIT = 99999


def get_mem_size():
    total_mem_size = MAX_LIMIT
    try:
        ONE_GB = 1024 * 1024 * 1024 * 1.0
        page_size = os.sysconf('SC_PAGESIZE')
        num_pages = os.sysconf('SC_PHYS_PAGES')
        total_mem_size = (get_memory_limit_from_cgroup() if in_container() and not in_k8s() else num_pages * page_size) / ONE_GB
        total_mem_size = round(total_mem_size, 2)
    except Exception as e:
        print 'Exception occurs while reading mem info: {0}'.format(str(e))

    print 'total mem = {0} GB'.format(total_mem_size)
    return total_mem_size


def get_total_memory(conf):
    if in_container() and not in_k8s():
        return get_memory_limit_from_cgroup()

    mem_installed_bytes = conf.get_value('_private/mem_installed_bytes')
    return mem_installed_bytes if mem_installed_bytes else psutil.virtual_memory().total


def get_total_disk(conf):
    disk_installed_bytes = conf.get_value('_private/disk_installed_bytes')
    return disk_installed_bytes if disk_installed_bytes else get_disk_mounted_bytes()


def get_cpu_model(conf):
    cpu_model = conf.get_value('_private/cpu_model')
    return cpu_model if cpu_model else ''

def get_cpu_core_num():
    total_core_num = MAX_LIMIT
    try:
        total_core_num = get_cpu_count_from_cgroup() if in_container() and not in_k8s() else multiprocessing.cpu_count()
    except Exception as e:
        print 'Exception occurs while reading cpu info: {0}'.format(str(e))

    print 'total cpu core num = {0}'.format(total_core_num)
    return total_core_num


def get_cpu_model_from_proc():
    try:
        common_cmd = 'cat /proc/cpuinfo | grep "model name" | uniq'

        code, out, _ = exe_with_output(common_cmd)
        if code == 0 and out:
            # 返回的out可能如下，需要能处理下面第二种CPU型号中有':'的场景
            # 'model name	: Intel(R) Xeon(R) CPU E5-2678 v3 @ 2.50GHz'
            # 'model name   : Hygon C86-3G (OPN:3350)'
            colon_index = out.find(':')
            return out[colon_index + 1:].strip()
        logging.error("Failed to get cpu model with common_cmd, code:{}, out:{}".format(code, out))
    except Exception as e:
        logging.error("Failed to get cpu model, error: {}".format(e))


def rand_key(length):
    a = []
    for _ in range(length):
        n = random.randint(0, 255)
        a.append(chr(n))
    return binascii.hexlify(''.join(a))


def rand_key2():
    a = 'AaBbDdEeFfGgHhiJjLMmNnQRrTtYy345678'
    r = ''
    for i in range(16):
        if i > 0 and i % 4 == 0:
            r += '.'
        r += a[random.randint(0, len(a)-1)]
    return r


def generate_alert(title, message, solution_url, solution_title, alert_level):
    return {'title': title,
            'message': message,
            'solution_url': solution_url,
            'solution_title': solution_title,
            'alert_level': alert_level,
            }


def read_latest_date(fn):
    if fn.endswith('.gz'):
        file_open = gzip.GzipFile
    else:
        file_open = open

    time_parser = AccessTimeParser()
    with file_open(fn, 'rb') as fp:
        offs = -100
        while True:
            fp.seek(offs, 2)
            lines = fp.readlines()
            if len(lines) > 1:
                last = lines[-1]
                break
            offs *= 2

        try:
            date = re.findall(r'\[(\d\d/\w+/\d\d\d\d:.*?)\]', last)
            if date:
                date = time_parser.parse(date[0])

                return date
        except:
            pass

def get_file_md5sum(file):
    code, output, cmd_line = exe_with_output('md5sum {0}'.format(file), False)
    if code == 0:
        return output.split()[0]
    return 0

def get_time_str():
    return time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

def get_upgrade_lib_path(filepath):
    dirpath = os.path.dirname(filepath)
    if not os.path.exists(dirpath):
        os.makedirs(dirpath)
    return filepath

def ipv4_len2mask(masklen):
    if masklen < 1 and masklen > 32:
        return len
    else:
        mask = (~((1 << (32 - masklen)) - 1)) & 0xFFFFFFFF
        mask = socket.inet_ntoa(struct.pack('I', socket.htonl(mask)))
        return mask

def ip_to_int(ip):
    ipv6_pattern = r'^(?![Ff][Ee][89AaBb][0-9a-fA-F]:)(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$'
    ipv4_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

    try:
        if re.match(ipv6_pattern, ip):
            return int(hexlify(socket.inet_pton(socket.AF_INET6, ip)), 16), 6
        elif re.match(ipv4_pattern, ip):
            return int(hexlify(socket.inet_pton(socket.AF_INET, ip)), 16), 4
        else:
            return -1, 0
    except Exception as e:
        return -1, 0

    #*******/24 -> *******/*************
def netaddr_build(ip):
    v = ip.split('/')
    if len(v) != 2:
        return ip
    #ipv4
    if '.' not in v[0]:
        return ip

    try:
         masklen = int(v[1])
         if masklen < 1 and masklen > 32:
             return ip
         mask = (~((1 << (32 - masklen)) - 1)) & 0xFFFFFFFF
         mask = socket.inet_ntoa(struct.pack('I', socket.htonl(mask)))
         ip = v[0] + '/' + mask

    except:
        return ip

    return ip

def mask_to_int(mask):
    if str(mask).isdigit():
        mask_int = int(mask)
        if 0 <= mask_int <= 128:
            return ((1 << mask_int) - 1) << (128 - mask_int), 6
        else:
            return -1, 6
    else:
        mask_int, _ = ip_to_int(mask)
        if 0 < mask_int <= 0xFFFFFFFF:
            if len(bin((1 << 32) - mask_int).split('1')) == 2:
                return mask_int, 4
        return -1, 4

ipv4_regex = re.compile(r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$')
ipv6_regex = re.compile(r'^(?![Ff][Ee][89AaBb][0-9a-fA-F]:)(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$')
def netaddr_parse(ip):
    try:
        mask = None
        v = ip.split('/')
        ip = v[0]
        if len(v) == 2:
            mask = v[1]

        if ipv4_regex.match(ip):
            masklen = None
            if mask is not None:
                ip_val = int(hexlify(socket.inet_pton(socket.AF_INET, ip)), 16)
                # network mask
                if '.' in mask:
                    mask_val, ver = mask_to_int(mask)
                    if mask_val < 0 or ver != 4:
                        return -1, 0, '', 0
                    masklen = bin(mask_val).count('1')
                # mask len
                else:
                    masklen = int(mask)
                    if (masklen < 1 or masklen > 32):
                        return -1, 0, '', 0

                    mask_val = (~((1 << (32 - masklen)) - 1)) & 0xFFFFFFFF

                if (ip_val & mask_val) != ip_val:
                    # return -1, 0, '', 0
                    ip_val = ip_val & mask_val
                    ip = str((ip_val >> 24) & 0xFF) + '.' + str((ip_val >> 16) & 0xFF) + '.' + str(
                        (ip_val >> 8) & 0xFF) + '.' + str(ip_val & 0xFF)

            return int(hexlify(socket.inet_pton(socket.AF_INET, ip)), 16), 4, ip, masklen if masklen else 32
        elif ipv6_regex.match(ip):
            if mask is not None:
                mask = int(mask)

            if mask is not None and (mask < 1 or mask > 128):
                return -1, 0, '', 0

            return int(hexlify(socket.inet_pton(socket.AF_INET6, ip)), 16), 6, ip, mask if mask else 128
        else:
            return -1, 0, '', 0
    except Exception as e:
        return -1, 0, '', 0


def generate_server_id(server_name_type, server_name, listen_port, enable_business_path, business_path):
    # generate site_id
    if server_name_type == 'Regex':
        server_name_or_md5 = hashlib.md5(server_name).hexdigest()
    elif server_name_type == 'Domain':
        server_name_or_md5 = server_name
    else:
        server_name_or_md5 = compress_IP(server_name)

    server_id = server_name_or_md5 + '_' + str(listen_port)
    if enable_business_path and business_path:
        server_id += '_' + hashlib.md5(business_path).hexdigest()

    return server_id, server_name_or_md5


def is_valid_regex(regex_str):
    ret = True
    site_name_error = "pass"
    normalized_site_name = ""

    regex = 'r' + "'" + regex_str + "'"
    try:
        # check regex by python
        re.match(regex, 'www.testweb.com')

        # check regex by so, boost and pcre
        regex_consistence = ctypes.cdll.LoadLibrary(get_release_file('regex_consistence.so'))
        if isinstance(regex_str, unicode):
            normalized_site_name = regex_str.encode("utf-8")
        else:
            normalized_site_name = regex_str
        boost_check = bool(regex_consistence.check_boost_regex(normalized_site_name, len(normalized_site_name)))
        pcre_check = bool(regex_consistence.check_pcre_regex(normalized_site_name, len(normalized_site_name)))
        if not boost_check or not pcre_check:
            site_name_error = 'Regex {} is incorrect'.format(normalized_site_name)
            ret = False
    except Exception, e:
        site_name_error = 'Regex {} is incorrect'.format(normalized_site_name)
        ret = False

    return ret, site_name_error


def lua_code_invalid(lua_code):
    if lua_code is None or not lua_code.strip():
        return [-1, 'Lua file is empty!']

    fd, tmp_src = tempfile.mkstemp(suffix='.lua')
    os.close(fd)
    fd, tmp_out = tempfile.mkstemp(suffix='.out')
    os.close(fd)
    save_file(tmp_src, lua_code)

    cmd_line = ' '.join([get_release_file('nginx/luajit'), '-b', tmp_src, tmp_out])
    try:
        logging.debug('Execute: ' + cmd_line[:250])
        return [0, check_output_ex(cmd_line, shell=True, cwd=get_release_file('nginx/'))]
    except subprocess.CalledProcessError as e:
        return [e.returncode, e.output]
    except Exception as e:
        logging.error('Got exception while execute: ' + cmd_line)
        return [-1, str(e)]
    finally:
        remove_file(tmp_src)
        remove_file(tmp_out)

def exchange_mask_2_prefix(mask):
    try:
        if '.' in mask:
            arr = map(int, mask.split('.'))
            if len(arr) == 4 and '.'.join(map(str, arr)) == mask:
                binstr = ''
                for i in arr:
                    if 0 <= i <= 255:
                        binstr += '{:08b}'.format(i)
                    else:
                        return None

                if '01' in binstr:
                    return None
                else:
                    return binstr.count('1')
        else:
            n = int(mask)
            if 0 <= n <= 128:
                return n
    except:
        pass

    return None

def get_ipv4_prefix_length(ip, mask_or_prefixlen):
    prefixlen = exchange_mask_2_prefix(mask_or_prefixlen)
    if prefixlen != None and (':' not in ip) and (prefixlen < 0 or prefixlen > 32):
        return None

    return prefixlen

try:
    from functools import lru_cache

except ImportError:
    # backport of Python's 3.3 lru_cache, written by Raymond Hettinger and
    # licensed under MIT license, from:
    # <http://code.activestate.com/recipes/578078-py26-and-py30-backport-of-python-33s-lru-cache/>
    # Should be removed when Django only supports Python 3.2 and above.

    from collections import namedtuple
    from functools import update_wrapper
    from threading import RLock

    _CacheInfo = namedtuple("CacheInfo", ["hits", "misses", "maxsize", "currsize"])

    class _HashedSeq(list):
        __slots__ = 'hashvalue'

        def __init__(self, tup, hash=hash):
            self[:] = tup
            self.hashvalue = hash(tup)

        def __hash__(self):
            return self.hashvalue

    def _make_key(args, kwds, typed,
                 kwd_mark = (object(),),
                 fasttypes = {int, str, frozenset, type(None)},
                 sorted=sorted, tuple=tuple, type=type, len=len):
        'Make a cache key from optionally typed positional and keyword arguments'
        key = args
        if kwds:
            sorted_items = sorted(kwds.items())
            key += kwd_mark
            for item in sorted_items:
                key += item
        if typed:
            key += tuple(type(v) for v in args)
            if kwds:
                key += tuple(type(v) for k, v in sorted_items)
        elif len(key) == 1 and type(key[0]) in fasttypes:
            return key[0]
        return _HashedSeq(key)

    def lru_cache(maxsize=100, typed=False):
        """Least-recently-used cache decorator.

        If *maxsize* is set to None, the LRU features are disabled and the cache
        can grow without bound.

        If *typed* is True, arguments of different types will be cached separately.
        For example, f(3.0) and f(3) will be treated as distinct calls with
        distinct results.

        Arguments to the cached function must be hashable.

        View the cache statistics named tuple (hits, misses, maxsize, currsize) with
        f.cache_info().  Clear the cache and statistics with f.cache_clear().
        Access the underlying function with f.__wrapped__.

        See:  http://en.wikipedia.org/wiki/Cache_algorithms#Least_Recently_Used

        """

        # Users should only access the lru_cache through its public API:
        #       cache_info, cache_clear, and f.__wrapped__
        # The internals of the lru_cache are encapsulated for thread safety and
        # to allow the implementation to change (including a possible C version).

        def decorating_function(user_function):

            cache = dict()
            stats = [0, 0]                  # make statistics updateable non-locally
            HITS, MISSES = 0, 1             # names for the stats fields
            make_key = _make_key
            cache_get = cache.get           # bound method to lookup key or return None
            _len = len                      # localize the global len() function
            lock = RLock()                  # because linkedlist updates aren't threadsafe
            root = []                       # root of the circular doubly linked list
            root[:] = [root, root, None, None]      # initialize by pointing to self
            nonlocal_root = [root]                  # make updateable non-locally
            PREV, NEXT, KEY, RESULT = 0, 1, 2, 3    # names for the link fields

            if maxsize == 0:

                def wrapper(*args, **kwds):
                    # no caching, just do a statistics update after a successful call
                    result = user_function(*args, **kwds)
                    stats[MISSES] += 1
                    return result

            elif maxsize is None:

                def wrapper(*args, **kwds):
                    # simple caching without ordering or size limit
                    key = make_key(args, kwds, typed)
                    result = cache_get(key, root)   # root used here as a unique not-found sentinel
                    if result is not root:
                        stats[HITS] += 1
                        return result
                    result = user_function(*args, **kwds)
                    cache[key] = result
                    stats[MISSES] += 1
                    return result

            else:

                def wrapper(*args, **kwds):
                    # size limited caching that tracks accesses by recency
                    key = make_key(args, kwds, typed) if kwds or typed else args
                    with lock:
                        link = cache_get(key)
                        if link is not None:
                            # record recent use of the key by moving it to the front of the list
                            root, = nonlocal_root
                            link_prev, link_next, key, result = link
                            link_prev[NEXT] = link_next
                            link_next[PREV] = link_prev
                            last = root[PREV]
                            last[NEXT] = root[PREV] = link
                            link[PREV] = last
                            link[NEXT] = root
                            stats[HITS] += 1
                            return result
                    result = user_function(*args, **kwds)
                    with lock:
                        root, = nonlocal_root
                        if key in cache:
                            # getting here means that this same key was added to the
                            # cache while the lock was released.  since the link
                            # update is already done, we need only return the
                            # computed result and update the count of misses.
                            pass
                        elif _len(cache) >= maxsize:
                            # use the old root to store the new key and result
                            oldroot = root
                            oldroot[KEY] = key
                            oldroot[RESULT] = result
                            # empty the oldest link and make it the new root
                            root = nonlocal_root[0] = oldroot[NEXT]
                            oldkey = root[KEY]
                            oldvalue = root[RESULT]
                            root[KEY] = root[RESULT] = None
                            # now update the cache dictionary for the new links
                            del cache[oldkey]
                            cache[key] = oldroot
                        else:
                            # put result in a new link at the front of the list
                            last = root[PREV]
                            link = [last, root, key, result]
                            last[NEXT] = root[PREV] = cache[key] = link
                        stats[MISSES] += 1
                    return result

            def cache_info():
                """Report cache statistics"""
                with lock:
                    return _CacheInfo(stats[HITS], stats[MISSES], maxsize, len(cache))

            def cache_clear():
                """Clear the cache and cache statistics"""
                with lock:
                    cache.clear()
                    root = nonlocal_root[0]
                    root[:] = [root, root, None, None]
                    stats[:] = [0, 0]

            wrapper.__wrapped__ = user_function
            wrapper.cache_info = cache_info
            wrapper.cache_clear = cache_clear
            return update_wrapper(wrapper, user_function)

        return decorating_function


def cidr_normalization(cidr):
    '''
    1.2.3.4/24 -> 1.2.3.0/24
    02A::1/64  -> 2a::/64
    1.2.3.4/32 -> 1.2.3.4
    2::3/128   -> 2::3
    1.2.3.4    -> 1.2.3.4
    a.com      -> None
    '''
    try:
        r = str(cidr_merge((cidr, cidr))[0])
    except:
        return None

    if ':' in r:
        if r.endswith('/128'):
            r = r[:-4]
    else:
        if r.endswith('/32'):
            r = r[:-3]
    return r


def get_upstream_port_map(conf):
    port_cidr_map = {}
    for site in conf.get_all('nginx/upstreams/').values():
        if 'ListenPort' not in site or not site.get('enable_site_conf', True):
            continue

        port = site['ListenPort']
        if port not in port_cidr_map:
            port_cidr_map[port] = []

        tmpSite = site.get('UpstreamList', [])
        if len(tmpSite) > 0:
            for upstream in tmpSite:
                host_net_domain = upstream[0]
                if host_net_domain == '0.0.0.0':
                    host_net_domain = '0.0.0.0/0'
                elif host_net_domain == '::':
                    host_net_domain = '::/0'

                host_or_net = cidr_normalization(host_net_domain)
                if host_or_net:
                    port_cidr_map[port] = cidr_merge(port_cidr_map[port] + [host_or_net])
        else:
            port_cidr_map[port] = ['0.0.0.0/0', '::/0']

    for k in port_cidr_map:
        port_cidr_map[k] = sorted(map(cidr_normalization, port_cidr_map[k]))

    return port_cidr_map


def get_upstream_port_svrname_cidr_key_map(conf):
    port_svrname_cidr_key_map = {}
    for site in conf.get_all('nginx/upstreams/').values():
        if 'ListenPort' not in site or not site.get('enable_site_conf', True):
            continue

        if not site['IsHttps']:
            continue

        key = site.get('CertificationKey', '')
        if key == '':
            continue

        port = site['ListenPort']
        if port not in port_svrname_cidr_key_map:
            port_svrname_cidr_key_map[port] = {}

        svr_name = site['ServerName']
        port_svrname_cidr_key_map[port][svr_name] = {'key': key}

        cidrs = []
        tmpSite = site.get('UpstreamList', [])
        if len(tmpSite) > 0:
            for upstream in tmpSite:
                host_net_domain = upstream[0]
                if host_net_domain == '0.0.0.0':
                    host_net_domain = '0.0.0.0/0'
                elif host_net_domain == '::':
                    host_net_domain = '::/0'

                host_or_net = cidr_normalization(host_net_domain)
                if host_or_net:
                    cidrs = cidr_merge(cidrs + [host_or_net])
        else:
            cidrs = ['0.0.0.0/0', '::/0']
        port_svrname_cidr_key_map[port][svr_name]['cidrs'] = sorted(map(cidr_normalization, cidrs))

    return port_svrname_cidr_key_map


def parse_bool(v):
    """
    Force Convert Bool
    :param v: Any
    :rtype: bool
    """
    if isinstance(v, unicode):
        v = v.encode('utf8')

    if isinstance(v, str):
        if v in ["1", "t", "T", "true", "TRUE", "True", "on"]:
            return True
        elif v in ["0", "f", "F", "false", "FALSE", "False", "off"]:
            return False
        else:
            raise ValueError("value {} cannot convert to bool".format(v))

    return bool(v)

def ugettext_noop(s):
    return s

# try:
#     from django.utils.translation import ugettext as django_trans
#     django_trans('test_str')
#     ugettext = django_trans
# except: # call not from webconsole raise exception or  pypy env not install django
try:
    import gettext
    locale_path = '/etc/asp/release/web_admin/src/web_admin/locale/'
    if not os.path.exists(locale_path):
        locale_path = get_release_file('../../../web_admin/layouts/riversec/locale/')

    _translator = {}
    ugettext = None
    for lang in [get_language(), 'zh', 'en']:
        try:
            if lang not in _translator:
                _translator[lang] = gettext.translation('django', locale_path, [lang])
                if ugettext is None:
                    ugettext = ugettext_en = ugettext_zh = _translator[lang].ugettext
        except:
            pass

    if 'en' in _translator:
        ugettext_en = _translator['en'].ugettext

    if 'zh' in _translator:
        ugettext_zh = _translator['zh'].ugettext
except:
    pass

if ugettext is None:
    ugettext = ugettext_en = ugettext_zh = lambda x:x  # compile env


@lru_cache()
def _generate_token_rename_fields(conf, salt=None):
    """
    Generate Rename Token Fields
    1. indexs = MD5(salt + token_name)
    2. rand_value = [(string.letters + string.digits)[index] for index in indexs]
    3. token_value = rand_value[:token_size]
    :param salt:
    :return:
    """
    default_token_fields = conf.get_default_token_fields()
    default_fields = {k: v['default'] for k, v in default_token_fields.items()}
    fields = default_fields.copy()
    if salt:
        choices = string.letters + string.digits
        choice_size = len(choices)
        for name, info in default_token_fields.items():
            size = info['size']
            md5 = hashlib.md5()
            md5.update('{}{}'.format(salt, name))
            indexes = [int(binascii.b2a_hex(i), base=16) for i in md5.digest()]
            value = ''.join([choices[indexes[i] % choice_size] for i in range(size)])
            fields[name] = value

    if is_rename_to_fix_token():
        fields = conf.get_fix_token_fields(default_token_fields, get_fix_token_choices())

    return fields, default_fields


def generate_token_rename(enable, seed, salt=None):
    """
    1、在向导流程中，无法从配置获取salt的值，因此需要传入salt；
    2、其他情况均能够从配置中读取到salt的值，故可以不传入salt；
    3、参数seed只允许传入'none'、'license_salt'、'cross_cluster'和'cluster_key';
    4、不建议这样：如果seed为'none'，salt不为None，就会用salt值计算fields，相当于seed为'none'，计算fields时依然加了salt。
    """
    assert seed in ("none", "license_salt", "cluster_key", "cross_cluster")
    key = seed
    from asp_conf_ctrl import ConfDb
    conf = ConfDb()
    salts = {
        'none': None,
        seed: salt
    }

    if not salt:
        license_info = conf.get_license_info(i18n_support=False, care_cluster=False)
        salts['license_salt'] = license_info.get_dict().get('mobile_salt')
        salts['cluster_key'] = conf.get_value('cluster/key')
        if seed == 'cross_cluster':
            key = 'license_salt'
        salt = salts.get(key, None) if enable else None
        if not salt:
            seed = 'none'

    fields, default_fields = _generate_token_rename_fields(conf, salt)
    default_cookie_token_name = default_fields.get('cookie_token', '')
    default_cookie_token_value = default_fields.get('cookie_value', '')

    """
    1、从salts中排除当前使用的seed，计算其他情况的cookie_token和cookie_value；
    2、cookie_token: ras使用的cookie名称的前缀；
    3、cookie_value: 用户cookie被加密后value部分的前缀。
    """
    salts.pop(key)
    used_cookie_token = []
    for k in set(salts.values()):
        used_fields, _ = _generate_token_rename_fields(conf, k)
        used_cookie_token.append({
            'cookie_token': used_fields.get('cookie_token', default_cookie_token_name),
            'cookie_value': used_fields.get('cookie_value', default_cookie_token_value)
        })

    token_rename = {
        'enable': enable,
        'fields': fields,
        'seed': seed,
        'used_cookie_token': used_cookie_token
    }

    return token_rename


def get_nginx_pids():
        code, output, cmdline = exe_with_output('pidof nginx', False)
        if code == 0:
            return set(output.split())
        return set()

def get_phy_and_bond_ifaces_name():
    return sorted([nic for nic in os.listdir('/sys/class/net/')
                   if (os.path.exists('/sys/class/net/{}/device'.format(nic))
                        or os.path.exists('/sys/class/net/{}/bonding'.format(nic)))])

def get_all_ifaces_name():
    all_ifaces_name = list()
    for nic in os.listdir('/sys/class/net/'):
        if not os.path.isdir('/sys/class/net/{}'.format(nic)):
            continue
        if os.path.exists('/sys/class/net/{}/device'.format(nic)) \
                or os.path.exists('/sys/class/net/{}/bonding'.format(nic)):
            all_ifaces_name.append(nic)
            continue
        for f_name in os.listdir('/sys/class/net/{}/'.format(nic)):
            if f_name.startswith('lower_'):
                all_ifaces_name.append(nic)
                continue
    return sorted(all_ifaces_name)


def get_iface_media(iface_name):
    code, output, cmdline = exe_with_output('ethtool {}'.format(iface_name), False)
    port_media = 'other'
    supported_ports_media = 'other'
    if code == 0:
        for line in output.split('\n'):
            if line.strip().startswith('Port'):
                port_media = line.split(':')[1].strip().lower()
            if line.strip().startswith('Supported ports'):
                if 'FIBRE' in line:
                    supported_ports_media = 'fibre'
                elif 'TP' in line:
                    supported_ports_media = 'twisted pair'
    if port_media == 'twisted pair' or port_media == 'fibre':
        return port_media
    return supported_ports_media


def get_file_content(file_path):
    if os.path.exists(file_path):
        try:
            return open(file_path).read()
        except:
            return ''
    return ''


def cidr_to_netmask(cidr):
    network, net_bits = cidr.split('/')
    host_bits = 32 - int(net_bits)
    netmask = socket.inet_ntoa(struct.pack('!I', (1 << 32) - (1 << host_bits)))
    return network, netmask


def get_iface_status_and_ip(iface):
    output = exe_with_output('ip addr show {}'.format(iface))[1]
    status = 'unused'
    if 'UP' in output and 'LOWER_UP' in output:
        status = 'ok'
    elif 'UP' in output and 'LOWER_UP' not in output:
        status = 'disconnected'

    speed = get_file_content('/sys/class/net/{}/speed'.format(iface)).strip()
    duplex = get_file_content('/sys/class/net/{}/duplex'.format(iface)).strip()
    if not speed or not duplex or speed == '-1' or duplex == 'unknown':
        speed = ''
    else:
        speed = '{}Mb/{}'.format(speed, duplex.capitalize())

    ip_addresses = list()
    for l in output.split('\n'):
        l = l.strip()
        if l.startswith('inet') and 'link' not in l and 'host' not in l:
            if l.startswith('inet6'):
                ip_addresses.append(' / '.join(l.split()[1].split('/')))
            else:
                ip_addresses.append(' / '.join(cidr_to_netmask(l.split()[1])))

    return {'status': [status, speed], 'ip': ip_addresses}


def get_iface_ips(iface):
    output = exe_with_output('ip addr show {}'.format(iface))[1]

    ip_v4_addresses = list()
    ip_v6_addresses = list()
    for l in output.split('\n'):
        l = l.strip()
        if l.startswith('inet') and 'link' not in l and 'host' not in l:
            if l.startswith('inet6'):
                ip_v6_addresses.append(l.split()[1].split('/'))
            else:
                ip_v4_addresses.append(list(cidr_to_netmask(l.split()[1])))
    return ip_v4_addresses, ip_v6_addresses


def get_all_ifaces_info(internal=False):
    all_ifaces_info = dict()
    all_ifaces_name = get_all_ifaces_name()
    for iface in all_ifaces_name:
        all_ifaces_info[iface] = dict()
        ipv4, ipv6 = get_iface_ips(iface)
        all_ifaces_info[iface]['ipv4'] = ipv4
        all_ifaces_info[iface]['ipv6'] = ipv6
        if not internal:
            iface_status_and_ip = get_iface_status_and_ip(iface)
            all_ifaces_info[iface]['media'] = get_iface_media(iface)
            all_ifaces_info[iface]['status'] = iface_status_and_ip['status']
            all_ifaces_info[iface]['mac_address'] = get_file_content('/sys/class/net/{}/address'.format(iface)).strip()
            all_ifaces_info[iface]['mtu'] = get_file_content('/sys/class/net/{}/mtu'.format(iface)).strip()
            all_ifaces_info[iface]['rx_bytes'] = get_file_content('/sys/class/net/{}/statistics/rx_bytes'.format(iface)).strip()
            all_ifaces_info[iface]['rx_packets'] = get_file_content('/sys/class/net/{}/statistics/rx_packets'.format(iface)).strip()
            all_ifaces_info[iface]['rx_errors'] = get_file_content('/sys/class/net/{}/statistics/rx_errors'.format(iface)).strip()
            all_ifaces_info[iface]['rx_dropped'] = get_file_content('/sys/class/net/{}/statistics/rx_dropped'.format(iface)).strip()
            all_ifaces_info[iface]['tx_bytes'] = get_file_content('/sys/class/net/{}/statistics/tx_bytes'.format(iface)).strip()
            all_ifaces_info[iface]['tx_packets'] = get_file_content('/sys/class/net/{}/statistics/tx_packets'.format(iface)).strip()
            all_ifaces_info[iface]['tx_errors'] = get_file_content('/sys/class/net/{}/statistics/tx_errors'.format(iface)).strip()
            all_ifaces_info[iface]['tx_dropped'] = get_file_content('/sys/class/net/{}/statistics/tx_dropped'.format(iface)).strip()

    for iface in all_ifaces_name:
        if '.' in iface:
            parent_iface, vlan_id = iface.split('.')
            all_ifaces_info[parent_iface]['ipv4'].extend([[vlan_id, item[0], item[1]] for item in all_ifaces_info[iface]['ipv4']])
            all_ifaces_info[parent_iface]['ipv6'].extend([[vlan_id, item[0], item[1]] for item in all_ifaces_info[iface]['ipv6']])
            all_ifaces_info.pop(iface)
        else:
            all_ifaces_info[iface]['ipv4'] = [['', item[0], item[1]] for item in all_ifaces_info[iface]['ipv4']]
            all_ifaces_info[iface]['ipv6'] = [['', item[0], item[1]] for item in all_ifaces_info[iface]['ipv6']]

    return {k:all_ifaces_info[k] for k in all_ifaces_info if k in get_phy_and_bond_ifaces_name()}


def _proc1_cmdline_argv0():
    try:
        with open('/proc/1/cmdline') as f:
            data = f.read()
            return data.split('\0')[0]
    except Exception as e:
        logging.exception('failed to read /proc/1/cmdline')
        return '-'


def in_container():
    a = _proc1_cmdline_argv0()
    if a in ('-', '/sbin/init', '/usr/lib/systemd/systemd'):
        return False
    return True


def is_docker_building():
    a = _proc1_cmdline_argv0()
    if a in ('-', '/sbin/init', '/usr/lib/systemd/systemd', '/usr/bin/dumb-init'):
        return False
    return True


def get_default_bot_check_config():
    config = {
        "enable": (get_product_type() == 'ApiBotDefender' or get_product_type() == 'ApiSecurityAudit'),
        "category": {
            "search_engine": 1,
            "search_engine_optimization": 1,
            "monitor": 1,
            "health_check": 1,
            "rss_reader": 1,
            "snapshot": 1,
            "preview": 1,
            "platform_inside": 1,
            "content_extractor": 1,
            "virus_scanner": 1,
            "test_tool": 2,
            "vulnerability_scanner": 2,
            "malicious_attacker": 2,
            "uncategorized": 2
        }
    }

    return config


def in_k8s():
    return in_container() and (os.path.exists("/var/run/secrets/kubernetes.io/") or os.getenv('KUBERNETES_SERVICE_HOST') is not None)


def get_os_flags():
    return {
        'chn_os': not_ubuntu(),
        'container': in_container(),
        'k8s': in_k8s(),
        'arch': os.uname()[4],
    }


def allocated_cpu_count():
    n = multiprocessing.cpu_count()
    if in_container():
        try:
            if os.path.exists("/sys/fs/cgroup/cpu.max"):
                # CgroupV2
                max_us, period_us = open('/sys/fs/cgroup/cpu.max').read().split()
            else:
                # CgroupV1
                max_us = open("/sys/fs/cgroup/cpu/cpu.cfs_quota_us").read().strip()
                period_us = open("/sys/fs/cgroup/cpu/cpu.cfs_period_us").read().strip()

            if float(max_us) > 0:
                # has cpu limits
                x = int(float(max_us)/float(period_us))
                if x <= 1:
                    x = 1
                if x > n:
                    x = n
                return x, str(x)
        except:
            pass
    return n, 'auto'

# dict, list key为中文时， 编码需要一致才能查询
def ensure_unicode(s):
    if isinstance(s, unicode):
        return s
    return s.decode('utf8')


def convert_ip_list_to_dict(ip_list):
    ip_dict = {
        'ipv4': list(),
        'ipv6': list(),
        'vlan': dict()
    }
    for ip_entry in ip_list:
        if ip_entry[0] == '':
            if ':' in ip_entry[1]:
                ip_dict['ipv6'].append(ip_entry[1:])
            else:
                ip_dict['ipv4'].append(ip_entry[1:])
        else:
            vlan_id = ip_entry[0]
            if ip_dict['vlan'].get(vlan_id):
                if ':' in ip_entry[1]:
                    ip_dict['vlan'][vlan_id]['ipv6'].append(ip_entry[1:])
                else:
                    ip_dict['vlan'][vlan_id]['ipv4'].append(ip_entry[1:])
            else:
                ip_dict['vlan'][vlan_id] = {'ipv4': list(), 'ipv6': list()}
                if ':' in ip_entry[1]:
                    ip_dict['vlan'][vlan_id]['ipv6'].append(ip_entry[1:])
                else:
                    ip_dict['vlan'][vlan_id]['ipv4'].append(ip_entry[1:])

    return ip_dict


def list_diff(new_list, old_list):
    diff_list = []
    for item in new_list:
        if item not in old_list:
            diff_list.append(('+', item))
    for item in old_list:
        if item not in new_list:
            diff_list.append(('-', item))

    return diff_list


def dict_diff(dict1, dict2):
    """
    Compares two dictionaries and returns the differences between them.
    """
    diff = {}

    # Find keys that are in dict1 but not in dict2
    for key in dict1:
        if key not in dict2:
            diff[key] = ('+', dict1[key])

    # Find keys that are in dict2 but not in dict1
    for key in dict2:
        if key not in dict1:
            diff[key] = ('-', dict2[key])

    # Find keys that are in both dictionaries but have different values
    for key in dict1:
        if key in dict2 and dict1[key] != dict2[key]:
            diff[key] = ('!', (dict1[key], dict2[key]))

    return diff


def diff_config_op_log(old_config, new_config):
    return "\n".join(diff_config(old_config, new_config))


def diff_config(old_config, new_config):
    if type(old_config) != dict or type(new_config) != dict:
        if old_config != new_config:
            yield "OLD{{{old}}} ---> NEW{{{new}}}".format(old=old_config, new=new_config)
        return

    for key in set(old_config.keys() + new_config.keys()):
        for diff_message in diff_config(old_config.get(key), new_config.get(key)):
            if diff_message.startswith("OLD{"):
                diff_message = "{key}: ".format(key=key) + diff_message
            else:
                diff_message = "{key}.".format(key=key) + diff_message
            yield diff_message

def to_safe_csv(header, rows):
    iostr = io.BytesIO()
    iostr.write(codecs.BOM_UTF8)
    writer = csv.writer(iostr, delimiter=',')
    writer.writerow(header)
    maxlen = len(header)
    for row in rows:
        for i in xrange(maxlen):
            if isinstance(row[i], unicode):
                row[i] = row[i].encode('utf8')
            if isinstance(row[i], str) and len(row[i]) > 1 and row[i][0] == '=':
                row[i] = '\xe2\x80\x8b' + row[i] # Zero Width Space (U+200B)
        writer.writerow(row[:maxlen])
    retstr = iostr.getvalue()
    iostr.close()
    return retstr

def get_all_good_bot_names():
    static = set()
    dynamic = set()
    with open('/etc/asp/release/good_bots', 'r') as f:
        for line in f:
            fields = line.strip().split(',')
            if (len(fields) == 2):
                static.add(fields[1])
    with open('/etc/asp/release/nginx/lua/good_bot/good_bot_auto_detector.lua', 'r') as f:
        for name in re.findall("search_engine_domain_expr\['([a-z0-9]+)'\]", f.read()):
            dynamic.add(name)

    return [static, dynamic]

def get_good_bot_version():
    md5 = hashlib.md5()
    good_bots = '/etc/asp/release/good_bots'
    with open(good_bots, 'r') as f:
        md5.update(f.read())
        chksum = md5.hexdigest()[:8]

    return '{tm}.{chksum}'.format(
        tm = time.strftime("%Y%m%d", time.localtime(os.path.getmtime(good_bots))),
        chksum=chksum)

def get_default_custom_geo():
    # DAP-25475 将默认内网地址，country 设置为”中国“
    country = ''
    if get_language() == 'zh':
        country = '中国'

    return [
        {'ip': '10.0.0.0/8', 'country': country, 'province': '', 'city': '', 'intranet': True},
        {'ip': '**********/12', 'country': country, 'province': '', 'city': '', 'intranet': True},
        {'ip': '***********/16', 'country': country, 'province': '', 'city': '', 'intranet': True},
        {'ip': 'FD00::/8', 'country': country, 'province': '', 'city': '', 'intranet': True}
    ]

def validate_custom_geo_info(data):
    if not isinstance(data, list):
        raise Exception("invalid format")

    if not (0 < len(data) <= 500):
        raise Exception("At least 1, up to 500")

    ips = []
    for item in data:
        if not isinstance(item, dict):
            raise Exception("invalid format")

        support_keys = set(['ip', 'country', 'province', 'city', 'intranet', 'old_ip'])
        if set(item.keys()) - support_keys:
            raise Exception("include not supported data field")

        ip_with_prefix = item.get('ip', '')

        if not (0 < len(ip_with_prefix) <= 50):
            raise Exception("invalid ip")
        if ip_with_prefix in ips:
            raise Exception("duplicated ip")
        ips.append(ip_with_prefix)

        ip, _, prefix = ip_with_prefix.partition('/')
        if not valid_IP(ip):
            raise Exception("invalid ip format")
        if ':' in ip_with_prefix:
            max_ip_prefix_len = 128
        else:
            max_ip_prefix_len = 32

        if prefix.isdigit():
            prefix = int(prefix)
        elif prefix == '':
            prefix = max_ip_prefix_len
        else:
            raise Exception("invalid ip prefix")

        if not (8 <= prefix <= max_ip_prefix_len):
            raise Exception("invalid ip prefix")

        # 长度小于30，不包含 不可见ASCII字符
        valid_geo = lambda s : len(s) <= 30 and re.search(r'[\x00-\x1f\x7f]', s) is None

        if not valid_geo(item['country']):
            raise Exception("invalid country")

        if not valid_geo(item['province']):
            raise Exception("invalid province")

        if not valid_geo(item['city']):
            raise Exception("invalid city")

        if not isinstance(item['intranet'], bool):
            raise Exception("invalid intranet")

def get_default_global_get_src_ip_strategy():
    return [
        {
            "src_ip_customed_name": "",
            "src_ip_from_type": "X-Real-IP",
            "xff_position": "last"
        },
        {
            "src_ip_customed_name": "",
            "src_ip_from_type": "X-Forwarded-For",
            "xff_position": "last"
        },
        {
            "src_ip_customed_name": "",
            "src_ip_from_type": "__SRCIP_TCPIP__",
            "xff_position": "last"
        }
    ]


def os_id():
    osid = [i.strip().split('=')[1].strip('"') for i in open('/etc/os-release').readlines() if i.startswith('ID=')][0]
    return osid


def current_machine_hardware_name():
    if os_id() in ('kylin', 'uos'):
        return 'kylin_' + os.uname()[4]
    else:
        return os.uname()[4]


def is_ubuntu():
    return os_id() == 'ubuntu'


def not_ubuntu():
    return os_id() != 'ubuntu'


# retrun the year of copyright
def get_copyright():
    return get_manifest_info().get('copyright_year', 2025)


def captcha_get_path():
    captcha_config = {
        "customFile": get_release_file("nginx/lua/captcha/custom.zip"),
        "defaultFile": get_release_file("nginx/lua/captcha/default_{}.zip".format(get_language())),
        "templatePath": get_release_file("nginx/lua/captcha/templates"),
    }
    return captcha_config


def captcha_change_template(new_one, checksum):
    captcha_config = captcha_get_path()
    try:
        # check the new one
        if new_one.startswith(get_sync_file_path()) and checksum != None:    # not default
            md5 = hashlib.md5()
            with open(new_one, 'rb') as f:
                md5.update(f.read())
                checksum_file = md5.hexdigest()
                if checksum_file != checksum:
                    raise Exception("checksum error for template file {}".format(new_one))

        # remove old one
        custom = captcha_config['customFile']
        if os.path.isfile(custom):
            os.remove(custom)
        templates = captcha_config['templatePath']
        if os.path.isdir(templates):
            shutil.rmtree(templates)

        # create new one
        os.mkdir(templates)
        with zipfile.ZipFile(new_one, 'r') as zipf:
            zipf.extractall(templates)

        if new_one.startswith(get_sync_file_path()):    # not default
            shutil.copy(new_one, custom)

        logging.info("successfully change the template to {}".format(new_one))
    except Exception as e:
        logging.error(e)
        return False

    return True


def captcha_get_version(fn):
    '''
    return value:
        None: something error occur
        string: version read
    '''
    try:
        with zipfile.ZipFile(fn, 'r') as zipf:
            f = zipf.open("manifest.json", "r")
            meta = f.read()
            manifest = json.loads(meta, encoding='utf-8')

            return manifest.get('version', '')
    except Exception as e:
        logging.error(e)
        return None


def captcha_cleanup():
    try:
        config = captcha_get_path()
        if os.path.isfile(config['customFile']):
            cust_ver = captcha_get_version(config['customFile'])
            if cust_ver != None:
                def_ver = captcha_get_version(config["defaultFile"])
                if def_ver == cust_ver:
                    logging.info("captcha: same version: {}".format(cust_ver))
                    return
                else:
                    logging.info("captcha: mismatch version: custum={}, default={}".format(cust_ver, def_ver))
            else:
                logging.error("captcha: invalid version in {}".format(config['customFile']))

        captcha_change_template(config["defaultFile"], None)
    except Exception as e:
        logging.error(e)


def remove_lua_comments(lua_code):
    if lua_code:
        # remove multi-line comments
        lua_code = re.sub(r'\-\-\[\[(.*?)\]\]', '', lua_code, flags=re.DOTALL)

        # remove single-line comments
        lua_code = re.sub(r'--(.*?)\n', '\n', lua_code)

    return lua_code


def remove_lua_file_comments(file_path):
    with open(file_path, 'r') as file:
        lua_code = file.read()

    lua_code = remove_lua_comments(lua_code)

    with open(file_path, 'w') as file:
        file.write(lua_code)

def remove_lua_comments_in_folder(folder_path):
    for root, dirs, files in os.walk(folder_path):
        for file in files:
            if file.endswith(".lua"):
                file_path = os.path.join(root, file)
                remove_lua_file_comments(file_path)


def get_mem_installed_bytes():
    mem_installed_bytes = psutil.virtual_memory().total
    try:
        out = exe_with_output('dmesg | grep Memory:')[1]
        installed_mem = out.split(':')[1].strip().split(' ')[0].split('/')[1]
        logging.debug('installed_mem = {}'.format(installed_mem))

        unit = installed_mem[-1].lower()
        if unit in '0123456789':
            mem_installed_bytes = int(installed_mem[:-1])
        elif unit == 'k':
            mem_installed_bytes = int(installed_mem[:-1]) * 1024
        elif unit == 'm':
            mem_installed_bytes = int(installed_mem[:-1]) * 1024 * 1024
        elif unit == 'g':
            mem_installed_bytes = int(installed_mem[:-1]) * 1024 * 1024 * 1024
        elif unit == 't':
            mem_installed_bytes = int(installed_mem[:-1]) * 1024 * 1024 * 1024 * 1024
    except Exception as e:
        logging.error('Failed to get installed memory as {}'.format(str(e)))

    def is_arm_physical_machine():
        try:
            with open("/proc/cpuinfo", "r") as f:
                cpuinfo = f.read()
            code, output, cmd_line = exe_with_output('dmidecode -s system-product-name', False)
            product_name = output.strip().lower()
            vm_names_array = ["vmware", "virtualbox", "kvm", "hyper-v", "amazon", "google"]
            if "hypervisor" in cpuinfo.lower():
                return False
            elif any(vm_name in product_name for vm_name in vm_names_array):
                return False
            elif 'aarch' not in os.uname()[4]:
                return False
            else:
                return True
        except IOError:
            return False

    if is_arm_physical_machine():
        mem_gb_array = [8 * i for i in range(1, 65)]
        mem_in_gb = min(mem_gb_array, key=lambda x: abs(x - mem_installed_bytes / (1024.0 * 1024 * 1024)))
        mem_installed_bytes = mem_in_gb * 1024 * 1024 * 1024

    return mem_installed_bytes


def get_disk_mounted_bytes():
    mounted_disk_bytes = 0

    for disk in psutil.disk_partitions():
        mounted_disk_bytes += psutil.disk_usage(disk.mountpoint).total

    return mounted_disk_bytes


def get_disk_installed_bytes():
    disk_installed_bytes = 0

    try:
        code, out, _ = exe_with_output('lsblk -bnro TYPE,SIZE')
        if code != 0 or out is None:
            logging.info("Failed to get disk capacity, code:{}, out:{}".format(code, out))
            return get_disk_mounted_bytes()

        lines = out.split("\n")
        for line in lines:
            l = line.split()
            if len(l) == 2 and l[0] == "disk":
                disk_installed_bytes += int(l[1])
        return disk_installed_bytes

    except Exception as e:
        logging.exception('Failed to get installed disk as {}'.format(str(e)))
        return get_disk_mounted_bytes()


def get_transparent_mode_effective_br_num():
    effective_br_num = 0
    while os.path.exists('/sys/class/net/br' + str(effective_br_num)):
        effective_br_num += 1
    return effective_br_num


def merge_ip(ipnetwork, valid_list, range_list, key_list):
    ''' this function algorithm from davie '''

    cur = {"start": ipnetwork.first, "end": ipnetwork.last}
    insert_pos = bisect.bisect_left(key_list, cur['start'])

    if insert_pos > 0 and cur["end"] <= range_list[insert_pos - 1]["end"]:
        #print("已经存在大的，丢弃当前小的【0】")
        return -1

    while insert_pos < len(range_list):
        e = range_list[insert_pos]
        if cur['end'] < e["start"]:
            #print("比当前的小，就插入在这里")  # 列表有序，故后续的不用再遍历了
            break
        elif e["start"] <= cur['start'] and cur['end'] <= e["end"]:   # 新加的范围更小：原有的包含新加的
            #print("已经存在大的，丢弃当前小的")
            insert_pos = -1
            break
        elif cur['start'] <= e["start"] and e["end"] <= cur['end']:   # 新加的范围更大：新加的包含原有的
            # print("删除当前的，保留新的，并继续合并后续的项")  # insert_pos 保持不变
            del range_list[insert_pos]   # 比remove() 遍历效率高？
            del key_list[insert_pos]
            del valid_list[insert_pos]
        else:  # 往后继续查找，这里是否可以折半尽快找到插入的位置
            insert_pos = insert_pos + 1

    if insert_pos >= 0:
        key_list.insert(insert_pos, cur['start'])
        range_list.insert(insert_pos, cur)

    return insert_pos

def parse_upload_ip_list(ip_list, range_list, key_list, enable_validity = False, validity_duration = 0):
    g_expire_timestamp = 0
    invalid_list = []
    valid_list = []

    now = int(time.time())
    if enable_validity and validity_duration > 0:
        g_expire_timestamp = now + validity_duration

    pattern = '|'.join(map(re.escape, [',', '，']))
    for ip in ip_list:
        try:
            expire_timestamp = g_expire_timestamp
            ip_timestamp = re.split(pattern, ip.encode('utf-8'))
            ip_timestamp[0] = IPNetwork(ip_timestamp[0])
            if not enable_validity and len(ip_timestamp) >= 2:
                if ip_timestamp[1].strip() != '':
                    expire_timestamp = int(float(ip_timestamp[1]))
        except:
            invalid_list.append(ip)
        else:
            insert_pos = merge_ip(ip_timestamp[0], valid_list, range_list, key_list)
            if insert_pos >= 0:
                valid_list.insert(insert_pos, str(ip_timestamp[0]) + ',' + str(expire_timestamp) if expire_timestamp != 0 else str(ip_timestamp[0]))

    return valid_list, invalid_list


def merge_exist_ip(exist_ip_list, ip_list, range_list, key_list):
    now = time.time()
    for exist_ip in exist_ip_list:
        ip_timestamp = exist_ip.split(',')
        expire_timestamp = 0
        if len(ip_timestamp) == 2:
            expire_timestamp = int(ip_timestamp[1])
            if expire_timestamp != 0 and now > expire_timestamp:
                continue

        insert_pos = merge_ip(IPNetwork(ip_timestamp[0]), ip_list, range_list, key_list)
        if insert_pos >= 0:
            ip_list.insert(insert_pos, exist_ip)


def delete_expired_ip(ip_list):
    valid_ip_list = []
    now = int(time.time())
    for ip in ip_list:
        ip_timestamp_list = ip.split(',')
        expire_timestamp = 0

        if len(ip_timestamp_list) == 2:
            expire_timestamp = int(ip_timestamp_list[1])

        if expire_timestamp != 0 and now > expire_timestamp:
            continue

        valid_ip_list.append(ip)

    return valid_ip_list


def is_port_already_in_use(port, confdb = None):
    try:
        s = None

        # 1024以下端口只有root用户才有权限，且1024以下端口不会用做随机端口
        if int(port) < 1024:
            return False

        if confdb:
            if hasattr(confdb, 'get_nginx_listen_port'):
                all_listen_port = confdb.get_nginx_listen_port()
                if port in all_listen_port:
                    return False
            else: # 单元测试的confdb
                return False

        # 1. 先删除站点，马上创建同端口站点，这时候可能nginx reload还没有完成，该端口还是被nginx占用的，处于LISTEN状态，可以复用
        # 2. 被nginx用作随机端口并且处于ESTABLISHED状态的不能复用
        # 3. 处理TIME_WAIT状态的端口，如果是nginx占用的，可以复用，但是无法知道TIME_WAIT状态是被谁占用的
        code, out, _ = exe_with_output('netstat -antp | grep ":{}\\b" | grep nginx | grep -v ESTABLISHED'.format(port), False)
        if code == 0 and len(out) > 0:
            return False

        # nginx不会监听在127.0.0.1，排除端口被127.0.0.1占用的
        code, out, _ = exe_with_output('netstat -ant | grep "127.0.0.1:{}\\b"'.format(port), False)
        if code == 0 and len(out) > 0:
            return False

        # 使用bind来尝试绑定端口，对于TIME_WAIT状态的端口可能产生误报，只有等TIME_WAIT状态消失后才可用
        # 可能的解决方法是使用root权限来bind，但webconsole不是root权限
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEPORT, 1)
        s.bind(('0.0.0.0', int(port)))
        return False
    except Exception as e:
        return True
    finally:
        if s is not None:
            s.close()

class NetworkInterface:
    IP = 'ip'
    NETMASK = 'netmask'
    GATEWAY = 'gateway'
    IP6 = 'ipv6'
    NETMASK6 = 'prefix'
    GATEWAY6 = 'ipv6_gateway'
    PORT = 'port'
    ROUTE_LIST = 'route_list'
    DNS_LIST = 'dns_list'

    IF_EXTERNAL = 'external'
    IF_INTERNAL = 'internal'
    IF_ADMIN = 'admin'
    IF_LOG = 'log'
    IF_KEEPALIVED = 'keepalived'

    SNMP = 'snmp'
    ON = 'on'
    PORT = 'port'
    TRAP_IP = 'trap_ip'
    TRAP_PORT = 'trap_port'
    VER = 'version'
    COMMUNITY = 'community'
    USER = 'user'
    SEC_LEVEL = 'sec_level'
    AUTH_ALGO = 'auth_algo'
    AUTH_PWD = 'auth_pwd'
    PRIV_ALGO = 'priv_algo'
    PRIV_PWD = 'priv_pwd'

    NTP_SERVER = 'ntp_server'

    USER_DEFINED_RESERVED_PORTS = 'user_defined_reserved_ports'

    ADAPTERS = [IF_ADMIN, IF_EXTERNAL, IF_INTERNAL, IF_LOG, IF_KEEPALIVED]

    def __init__(self, prefix='_private/os/network/'):
        from asp_conf_ctrl import ConfDb
        self.__conf = ConfDb()
        self.prefix = prefix

    def get_conf(self):
        return self.__conf

    def get_value(self, name, def_val=None, is_abs=False):
        if is_abs:
            prefix = ''
        else:
            prefix = self.prefix
        return self.get_conf().get_value(prefix + name, default=def_val)

    def get_cfg_iface(self, path=''):
        return self.get_conf().get_all(self.prefix + path)

    def clone_cfg_iface(self):
        cfg = self.get_cfg_iface()
        cfg = cfg.copy()

        def merge_router(if_name):
            shared = self.get_conf().get_all('os/network/{}'.format(if_name))
            routers = dict(cfg[if_name].pop('route_list', {}), **shared.pop('route_list', {}))
            for k in routers:
                routers[k].setdefault('private', False)
            shared.update(
                {'route_list': routers}
            )
            return shared

        # Merge dns and routes and admin.port onto 'cfg' from shared path
        cfg[self.IF_EXTERNAL].update(merge_router(self.IF_EXTERNAL))
        cfg[self.IF_ADMIN].update(merge_router(self.IF_ADMIN))
        cfg[self.IF_INTERNAL].update(merge_router(self.IF_INTERNAL))
        cfg[self.IF_ADMIN].update({'port': self.get_value('nginx/web_console/port', is_abs=True)})
        cfg.get(self.IF_LOG) and cfg[self.IF_LOG].update(merge_router(self.IF_LOG))
        cfg.get(self.IF_KEEPALIVED) and cfg[self.IF_KEEPALIVED].update(merge_router(self.IF_KEEPALIVED))

        #convert (ip,dual_ip) -> wizard(ip,ipv6)
        for eth in [self.IF_EXTERNAL, self.IF_ADMIN, self.IF_INTERNAL]:
            if cfg[eth].get('dual_ip'):
                cfg[eth]['ipv6']   = cfg[eth].get('dual_ip')
                cfg[eth]['prefix'] = cfg[eth].get('dual_netmask')
                cfg[eth]['ipv6_gateway'] = cfg[eth].get('dual_gateway') if cfg[eth].get('dual_gateway') else ''
            elif cfg[eth].get('ip') and ':' in cfg[eth].get('ip'):
                cfg[eth]['ipv6'] = cfg[eth].get('ip')
                cfg[eth]['prefix'] = cfg[eth].get('netmask')
                cfg[eth]['ipv6_gateway'] = cfg[eth].get('gateway') if cfg[eth].get('gateway') else ''
                cfg[eth]['ip'] = ''
                cfg[eth]['netmask'] = ''
                cfg[eth]['gateway'] = ''

            if cfg[eth].has_key('dual_ip'):
                cfg[eth].pop('dual_ip')
                cfg[eth].pop('dual_netmask')
            if cfg[eth].has_key('dual_gateway'):
                cfg[eth].pop('dual_gateway')

        # single adapter's gateway should show in admin adapter
        if (cfg[self.IF_ADMIN].get('shared_with') == cfg[self.IF_INTERNAL].get('shared_with') == self.IF_EXTERNAL) \
                and not cfg[self.IF_EXTERNAL].get('shared_with'):
            cfg[self.IF_ADMIN].setdefault("gateway", cfg[self.IF_EXTERNAL].pop('gateway', None))

        cfg[self.SNMP] = self.get_conf().get_all('os/network/snmp')

        cfg[self.NTP_SERVER] = ''
        ntp_server = self.get_value('os/network/ntp_server', is_abs=True)
        if ntp_server:
            cfg[self.NTP_SERVER] = ntp_server

        cfg[self.USER_DEFINED_RESERVED_PORTS] = ''
        user_defined_reserved_ports = self.get_value('os/network/user_defined_reserved_ports', is_abs=True)
        if user_defined_reserved_ports:
            cfg[self.USER_DEFINED_RESERVED_PORTS] = user_defined_reserved_ports

        # convert dns list
        dns = cfg[self.IF_EXTERNAL].get('dns_list')
        if dns is not None:
            dns = ', '.join(dns.split(' '))
            cfg[self.IF_EXTERNAL]['dns_list'] = dns

        dns = cfg[self.IF_ADMIN].get('dns_list')
        if dns is not None:
            dns = ', '.join(dns.split(' '))
            cfg[self.IF_ADMIN]['dns_list'] = dns

        return cfg


def get_ifaces_roles(networkConf, internal=False):
    cfg_iface = networkConf.clone_cfg_iface()
    iface_roles = dict()
    for name in [NetworkInterface.IF_ADMIN, NetworkInterface.IF_EXTERNAL, NetworkInterface.IF_INTERNAL, NetworkInterface.IF_LOG,
                 NetworkInterface.IF_KEEPALIVED]:
        iface = cfg_iface.get(name)
        if iface:
            if name != NetworkInterface.IF_KEEPALIVED or iface.get('on'):
                nic_name = iface.get('adapter_name')
                if iface_roles.get(nic_name) is None:
                    iface_roles[nic_name] = dict()

                if iface_roles[nic_name].get('roles') is None:
                    iface_roles[nic_name]['roles'] = [name]
                else:
                    iface_roles[nic_name]['roles'].append(name)
    confdb = networkConf.get_conf()
    if confdb.get_deploy_mode() == confdb.DEPLOY_MODE_MIRROR:
        from module_ctrls.network.network import usable_device_names
        ETH0 = usable_device_names(1)[0]
        if internal:
            iface_roles[ETH0]['roles'] = [NetworkInterface.IF_ADMIN, NetworkInterface.IF_EXTERNAL, NetworkInterface.IF_INTERNAL,
                                            NetworkInterface.IF_LOG, NetworkInterface.IF_KEEPALIVED]
        else:
            iface_roles[ETH0]['roles'] = [NetworkInterface.IF_ADMIN, NetworkInterface.IF_LOG]
    elif confdb.get_deploy_mode() == confdb.DEPLOY_MODE_TRANSPARENT:
        if internal:
            iface_roles['eth0']['roles'] = [NetworkInterface.IF_ADMIN, NetworkInterface.IF_EXTERNAL, NetworkInterface.IF_INTERNAL,
                                            NetworkInterface.IF_LOG, NetworkInterface.IF_KEEPALIVED]
        else:
            iface_roles['eth0']['roles'] = [NetworkInterface.IF_ADMIN]
            for i in range(get_transparent_mode_effective_br_num()):
                iface_roles['eth'+str(i*2+1)] = {'roles': [NetworkInterface.IF_EXTERNAL]}
                iface_roles['eth'+str(i*2+2)] = {'roles': [NetworkInterface.IF_INTERNAL]}

    return iface_roles


def is_iface_editable(iface_name, confdb):
    from bond_util import get_bond_info, get_bond_info_in_cfg
    # all bond members are not editable
    bond_info = get_bond_info()
    all_bond_members = list()
    for k, v in bond_info.items():
        all_bond_members.extend(v['members'])
    bond_info_in_cfg = get_bond_info_in_cfg()
    all_bond_members_in_cfg = list()
    for k, v in bond_info_in_cfg.items():
        all_bond_members_in_cfg.extend(v['members'])
    if iface_name in set(all_bond_members).union(set(all_bond_members_in_cfg)):
        return False

    # eth1, eth2, eth3 and eth4 of transparent mode
    if confdb.get_deploy_mode() == confdb.DEPLOY_MODE_TRANSPARENT and iface_name in ('eth' + str(i+1) for i in range(2*get_transparent_mode_effective_br_num())):
        return False

    return True


def get_vip(conf_db):
    ipv4_vip = list()
    ipv6_vip = list()
    if conf_db.is_ha():
        if conf_db.get_value('os/network/keepalived/external/virtual_ip', ''):
            ipv4_vip.append([conf_db.get_value('os/network/keepalived/external/virtual_ip', ''),
                             conf_db.get_value('os/network/keepalived/external/virtual_netmask', '')])
        if conf_db.get_value('os/network/keepalived/external/virtual2_ip', ''):
            ipv4_vip.append([conf_db.get_value('os/network/keepalived/external/virtual2_ip', ''),
                             conf_db.get_value('os/network/keepalived/external/virtual2_netmask', '')])
        if conf_db.get_value('os/network/keepalived/external/virtual_ip_ipv6', ''):
            ipv6_vip.append([conf_db.get_value('os/network/keepalived/external/virtual_ip_ipv6', ''),
                             conf_db.get_value('os/network/keepalived/external/virtual_ip_ipv6_prefix', '')])
        if conf_db.get_value('os/network/keepalived/external/virtual2_ip_ipv6', ''):
            ipv6_vip.append([conf_db.get_value('os/network/keepalived/external/virtual2_ip_ipv6', ''),
                             conf_db.get_value('os/network/keepalived/external/virtual2_ip_ipv6_prefix', '')])
    return ipv4_vip, ipv6_vip


def get_vip_list(confdb):
    ipv4_vip, ipv6_vip = get_vip(confdb)
    vip = list()
    for ip in ipv4_vip:
        vip.append(['', ip[0], ip[1]])
    for ip in ipv6_vip:
        vip.append(['', ip[0], ip[1]])
    return vip


def api_seq_user_key_changed(old_value, new_value):
    def get_user_key(value):
        user_key = "src_ip+user_agent"
        if type(value) == dict:
            user_key = value.get('user_key', user_key)
        return user_key
    old_user_key = get_user_key(old_value)
    new_user_key = get_user_key(new_value)
    return old_user_key != new_user_key


def get_flowlearn_master_ip(cluster_nodes):
    min_id=9999
    flowlearn_master_ip='127.0.0.1'

    for key, val in cluster_nodes.items():
        if key.startswith('_'):
            continue
        deleted = val.get('_deleted')
        if deleted:
            continue

        flowlearn_service_status = val.get('status',{}).get('_info',{}).get('service',{}).get('flowlearn_service',{}).get(
                'status', 'ERR')
        if flowlearn_service_status != 'OK':
            continue

        all_role = val.get('_role')
        if all_role is None:
            continue
        if 'flowlearn_server' in all_role:
            if min_id>int(key):
                min_id=int(key)
                flowlearn_master_ip=val.get("_admin_ip")
    return flowlearn_master_ip


def get_default_gateways():
    def _get_route(n):
        arr = [i.strip().split() for i in os.popen('ip -{} route list scope global match default'.format(n)).readlines()]
        return [(i[2],i[4]) for i in arr]

    default_gateways = dict()
    has_gateway = False
    has_ipv6_gateway = False
    for ip, dev in _get_route(4):
        has_gateway = True
        if default_gateways.get(dev):
            if default_gateways.get(dev).get('gateway') is None:
                default_gateways[dev]['gateway'] = list()
            default_gateways[dev]['gateway'].append(ip)
        else:
            default_gateways[dev] = {'gateway': [ip]}
    for ip, dev in _get_route(6):
        has_ipv6_gateway = True
        if default_gateways.get(dev):
            if default_gateways.get(dev).get('ipv6_gateway') is None:
                default_gateways[dev]['ipv6_gateway'] = list()
            default_gateways[dev]['ipv6_gateway'].append(ip)
        else:
            default_gateways[dev] = {'ipv6_gateway': [ip]}
    return default_gateways, has_gateway, has_ipv6_gateway


def get_all_master_ips(conf_db):
    all_master_ips = dict()
    for k, v in conf_db.get_master_servers().items():
        if v.get('admin_ip'):
            all_master_ips[k] = v['admin_ip']
    return all_master_ips


def construct_ifaces_info(networkConf, internal=False):
    ifaces_info = get_all_ifaces_info(internal)
    ifaces_roles = get_ifaces_roles(networkConf, internal)
    if not internal:
        from bond_util import get_bond_info
        bond_info = get_bond_info()
    else:
        bond_info = dict()
    default_gateways, has_gateway, has_ipv6_gateway = get_default_gateways()
    for iface in ifaces_info.keys():
        roles = ifaces_roles.get(iface, {}).get('roles', list())

        # make sure the primary IP is the first IP
        if NetworkInterface.IF_EXTERNAL in roles or NetworkInterface.IF_INTERNAL in roles:
            role = NetworkInterface.IF_EXTERNAL if NetworkInterface.IF_EXTERNAL in roles else NetworkInterface.IF_INTERNAL
            ip = networkConf.get_value('{}/ip'.format(role), '')
            dual_ip = networkConf.get_value('{}/dual_ip'.format(role), '')
            primary_ipv4 = ip if ':' not in ip else ''
            primary_ipv6 = ''
            if ':' in ip:
                primary_ipv6 = ip
            elif ':' in dual_ip:
                primary_ipv6 = dual_ip
            ipv4_list = list()
            ipv6_list = list()
            for ip_item in ifaces_info[iface]['ipv4']:
                if ip_item[1] == primary_ipv4:
                    ipv4_list.insert(0, ip_item)
                else:
                    ipv4_list.append(ip_item)
            for ip_item in ifaces_info[iface]['ipv6']:
                if ip_item[1] == primary_ipv6:
                    ipv6_list.insert(0, ip_item)
                else:
                    ipv6_list.append(ip_item)
            ifaces_info[iface]['ipv4'] = ipv4_list
            ifaces_info[iface]['ipv6'] = ipv6_list

        confdb = networkConf.get_conf()
        if not internal:
            if bond_info.get(iface):
                ifaces_info[iface]['bond_info'] = 'Mode{} ({})'.format(bond_info[iface]['mode'],
                                                                       ', '.join(bond_info[iface]['members']))
            else:
                ifaces_info[iface]['bond_info'] = ''
            if NetworkInterface.IF_EXTERNAL in roles:
                ifaces_info[iface]['vip'] = get_vip_list(confdb)
            else:
                ifaces_info[iface]['vip'] = list()
        else:
            if NetworkInterface.IF_EXTERNAL in roles:
                ipv4_vip, ipv6_vip = get_vip(confdb)
                ifaces_info[iface]['ipv4_vip'] = ipv4_vip
                ifaces_info[iface]['ipv6_vip'] = ipv6_vip
            else:
                ifaces_info[iface]['ipv4_vip'] = list()
                ifaces_info[iface]['ipv4_vip'] = list()
        ifaces_info[iface]['roles'] = roles
        ifaces_info[iface]['is_editable'] = is_iface_editable(iface, confdb)

        adapter_role = confdb.get_adapter_role(iface)
        adapter_conf_path = '_private/os/network/'+adapter_role if adapter_role else '_private/os/network/other_adapters/'+iface
        service_list = confdb.get_value(adapter_conf_path+'/service_list', [])
        ifaces_info[iface]['access_webconsole'] = 'webconsole' in service_list
        ifaces_info[iface]['access_webconsole_ipv6'] = 'webconsole_ipv6' in service_list
        ifaces_info[iface]['access_nginx'] = {'display': len(roles) == 0 and confdb.get_deploy_mode() == confdb.DEPLOY_MODE_INLINE, 'enabled': 'nginx' in service_list}

        if not has_gateway and (NetworkInterface.IF_ADMIN in roles or NetworkInterface.IF_EXTERNAL in roles
                                or NetworkInterface.IF_INTERNAL in roles or NetworkInterface.IF_LOG in roles
                                or NetworkInterface.IF_KEEPALIVED in roles):
            ifaces_info[iface]['gateway'] = ['']
        else:
            ifaces_info[iface]['gateway'] = default_gateways.get(iface, {}).get('gateway', list())
        if not has_ipv6_gateway and roles:
            ifaces_info[iface]['ipv6_gateway'] = ['']
        else:
            ifaces_info[iface]['ipv6_gateway'] = default_gateways.get(iface, {}).get('ipv6_gateway', list())
        if NetworkInterface.IF_ADMIN in roles:
            ifaces_info[iface]['could_add_route'] = True
            if len(confdb.get_all_admin_ip()) > 1:
                ifaces_info[iface]['master_ips'] = get_all_master_ips(confdb)
            else:
                ifaces_info[iface]['master_ips'] = None
        else:
            ifaces_info[iface]['could_add_route'] = False
            ifaces_info[iface]['master_ips'] = None

        ifaces_info[iface]['max_ip_number'] = 30
        ifaces_info[iface]['could_add_vlan'] = True

    return ifaces_info


def get_cert_expiration(crt_file):
    try:
        output = subprocess.check_output(['openssl', 'x509', '-in', crt_file, '-noout', '-enddate'])
        return datetime.strptime(output.strip(), 'notAfter=%b %d %H:%M:%S %Y GMT')
    except Exception as e:
        logging.error('Failed to get_cert_expiration({}): {}'.format(crt_file, e))
        return None


def need_generate_ssl_cert(crt):
    if not os.path.exists(crt):
        return True

    manifest = get_manifest_info()
    if not manifest['is_prod_pkg']:
        return False

    expired_time = get_cert_expiration(crt)
    if expired_time is None:
        return False

    time_diff = expired_time - datetime.utcnow()
    return time_diff < timedelta(days=730)

class OneClickSwitch(object):
    @staticmethod
    def validate_one_click_switch(body):
        try:
            data = body
            if not isinstance(body, dict):
                data = json.loads(body)
            if set(data.keys()) != {'action', 'sites'}:
                return None
        except Exception:
            return None

        # 验证 action
        if data['action'] not in ['transparent', 'monitor', 'block', 'disable']:
            return None

        # 验证 sites
        # 1. 当前支持最多 1000 个站点
        sites = data['sites']
        if type(sites) != list or len(sites) > 1000:
            return None
        # 2. serverKey 格式为 domain_port 或 domain_port_hash(business_path)，粗略限定长度不超过256（UI输入框长度限定是110）
        if list(filter(lambda site: len(site) > 256 or len(site.split('_')) > 3, sites)):
            return None

        return data


    @staticmethod
    def get_action_conf(action):
        if action == 'transparent':
            return [('enabled', False), ('learning_mode', False)]
        elif action == 'monitor':
            return [('enabled', True), ('learning_mode', True)]
        elif action == 'block':
            return [('enabled', True), ('learning_mode', False)]
        elif action == 'disable':
            return [('enable_site_conf', False)]


if __name__ == '__main__':
    rm_user_all_crontab_task('root')

