# -*- coding: utf-8 -*-
import abc
import inspect
import types

class Model(object):  
    def __init__(self, **kwargs):
        cls = self.__class__

        type_mapping = {
            'Field': lambda cls, key, value: getattr(cls, key).validate(value),
            'type': lambda cls, key, value: getattr(cls, key)(**value),
            'list': lambda cls, key, value: [getattr(cls, key)(**v) for v in value]
        }
        # 验证所有传入的参数
        for key, value in kwargs.items():
            # 执行Field验证
            func = None
            if hasattr(cls, key):
                func = type_mapping.get(getattr(cls, key).__class__.__name__, None)
                
            if callable(func):
                func(cls, key, value)
                
            # 执行自定义验证方法
            validator_name = 'validate_{key}'.format(key=key)
            if hasattr(cls, validator_name):
                validator = getattr(cls, validator_name)
                # 检查是否为有效的方法
                if not (inspect.isfunction(validator) or 
                        inspect.ismethod(validator) or 
                        isinstance(validator, types.UnboundMethodType)):
                    continue
                
                # 调用验证方法 - Python 2中需要显式传递self
                validator(self, value)
            
            # 所有验证通过后设置实例属性
            setattr(self, key, value)
    
    def to_dict(self):
        """转换为字典，只包含实例属性"""
        return {k: v for k, v in self.__dict__.items() 
                if not k.startswith('__') and not callable(v)}


class ValidationError(Exception):
    """Validation error exception"""
    def __init__(self, message, field_name=None, path=None):
        self.message = message
        self.field_name = field_name
        self.path = path or []
        super(ValidationError, self).__init__(': '.join(self.path + [message]) if self.path else message)

class Field(object):
    __metaclass__ = abc.ABCMeta

    def __init__(self, default=None, alias=None, required=True, 
                 min_length=None, max_length=None, 
                 min_value=None, max_value=None,
                 choices=None, item_type=None,
                 **kwargs):
        """
        增强型字段验证基类
        
        :param default: 默认值（可为可调用对象）
        :param alias: 字段别名（用于序列化/反序列化）
        :param required: 是否必填
        :param min_length: 最小长度（字符串/列表）
        :param max_length: 最大长度（字符串/列表）
        :param min_value: 最小值（数字）
        :param max_value: 最大值（数字）
        :param choices: 枚举选项（列表/元组）
        :param item_type: 列表项类型（ListField专用）
        """
        self.default = default
        self.alias = alias
        self.required = required
        self.min_length = min_length
        self.max_length = max_length
        self.min_value = min_value
        self.max_value = max_value
        self.choices = choices
        self.item_type = item_type
        self.name = None  # 由元类设置
        self.params = kwargs

    def _validate_required(self, value):
        """Check required and return default if not required"""
        if value is None:
            if self.required:
                raise ValidationError("This field is required")
            return self.get_default()
        return value

    def _validate_length(self, value):
        """Validate length for strings and lists"""
        if isinstance(value, (str, unicode, list, tuple, set)):
            length = len(value)
            if self.min_length is not None and length < self.min_length:
                raise ValidationError("Length must be at least %d" % self.min_length)
            if self.max_length is not None and length > self.max_length:
                raise ValidationError("Length must be at most %d" % self.max_length)

    def _validate_value_range(self, value):
        """Validate value range for numbers"""
        if isinstance(value, (int, float)):
            if self.min_value is not None and value < self.min_value:
                raise ValidationError("Value must be at least %d" % self.min_value)
            if self.max_value is not None and value > self.max_value:
                raise ValidationError("Value must be at most %d" % self.max_value)

    def _validate_choices(self, value):
        """Validate value is in allowed choices"""
        if self.choices is not None:
            if value not in self.choices:
                raise ValidationError("Value must be one of: %s" % str(self.choices))

    def _validate_nested_model(self, value):
        """
        如果 item_type 是 Model 子类，且 value 是 dict，则实例化并校验
        """
        if (isinstance(self.item_type, type) and 
            issubclass(self.item_type, Model) and 
            isinstance(value, dict)):
            # 实例化嵌套模型并校验
            self.item_type(**value)
        
        return value
    
    def _validate_list_items(self, value):
        if not isinstance(value, list) or not self.item_type:
            return

        results = []
        for i, item in enumerate(value):
            try:
                # 情况1: item_type 是 Model 子类，item 是 dict
                data = self._validate_nested_model(item)
                if data:
                    results.append(data)

                # 情况2: item_type 是 Field 实例
                elif hasattr(self.item_type, 'validate'):
                    validated_item = self.item_type.validate(item)
                    results.append(validated_item)

                # 情况3: item_type 是普通类型（如 int, str）
                else:
                    if not isinstance(item, self.item_type):
                        expected = getattr(self.item_type, '__name__', str(self.item_type))
                        raise ValidationError(
                            "Item at index %d has invalid type, expected %s" % (i, expected),
                            field_name=self.name
                        )
                    results.append(item)

            except ValidationError:
                raise
            except Exception:
                expected = getattr(self.item_type, '__name__', str(self.item_type))
                raise ValidationError(
                    "Item at index %d has invalid type, expected %s" % (i, expected),
                    field_name=self.name
                )
        return results  # 可选：替换原 value

    def validate(self, value):
        """验证字段值，无效时抛出ValueError"""
        value = self._validate_required(value)
        if value is None:  # 已返回默认值且非必填
            return value

        # 长度校验（字符串、列表）
        self._validate_length(value)

        # 数值范围校验（数字）
        self._validate_value_range(value)

        # 枚举选项校验
        self._validate_choices(value)

        # 列表项类型校验
        self._validate_list_items(value)



class AppSite(Model):
    # 定义字段验证
    name = Field(min_length=1, max_length=128)
    demo = Field(min_length=1, max_length=128, item_type=Field(choices=[1,2,3]))
    
    # 自定义验证方法
    def validate_name(self, name):
        """额外验证：名称不能包含特殊字符"""
        if not name.isalnum():
            raise ValidationError("Name must be alphanumeric")
    
    def validate_url(self, url):
        """URL字段的自定义验证"""
        if not url.startswith(('http://', 'https://')):
            raise ValidationError("URL must start with http:// or https://")

class ApiInfo(Model):
    api_id = Field(max_length=27)
    app_site = AppSite
    lst = Field(item_type=AppSite)

# 使用示例
if __name__ == "__main__":
    # 正确示例
    site = AppSite(
        name="MySite",
        url="https://example.com",
        age=1,
        demo=[1,2,3]
    )
    print(site.to_dict())
    
    # 错误示例（demo 不在枚举中）
    try:
        AppSite(name="", url="https://example.com", demo=[1,2,4])
    except ValidationError as e:
        print("Validation error: {e}".format(e=e))
    

    # 错误示例（demo为空）
    try:
        AppSite(name="", url="https://example.com", demo=[])
    except ValidationError as e:
        print("Validation error: {e}".format(e=e))
    

    # 错误示例（名称为空）
    try:
        AppSite(name="", url="https://example.com")
    except ValidationError as e:
        print("Validation error: {e}".format(e=e))
    

    # 错误示例（名称包含特殊字符）
    try:
        AppSite(name="My@Site", url="https://example.com")
    except ValidationError as e:
        print("Validation error: {e}".format(e=e))
    
    # 错误示例（URL格式错误）
    try:
        AppSite(name="MySite", url="ftp://example.com")
    except ValidationError as e:
        print("Validation error: {e}".format(e=e))
    
    # 错误示例（无效字段）
    try:
        AppSite(name="MySite", invalid_field="test")
    except ValidationError as e:
        print("Validation error: {e}".format(e=e))

    # 嵌套模型
    try:
        api_info = ApiInfo(api_id='a'*27, app_site={
            'name': 's001',
            'url': 'http://example.com'
        })
        print(api_info.to_dict())
    except ValidationError as e:
        print("Validation error api info: {e}".format(e=e))

    # 嵌套模型 错误示例（名称包含特殊字符）
    try:
        api_info = ApiInfo(api_id='a'*27, app_site={
            'name': 's-001',
            'url': 'http://example.com'
        })
        print(api_info.to_dict())
    except ValidationError as e:
        print("Validation error app_site name: {e}".format(e=e))

    # 嵌套模型 错误示例（URL格式错误）
    try:
        api_info = ApiInfo(api_id='a'*27, app_site={
            'name': 's001',
            'url': 'ht://example.com'
        })
        print(api_info.to_dict())
    except ValidationError as e:
        print("Validation error app_site url: {e}".format(e=e))

    # 数组嵌套模型
    try:
        api_info = ApiInfo(api_id='a'*27, app_site={
            'name': 's001',
            'url': 'http://example.com'
        },
        lst=[{'name': 's001', 'url': 'http://example.com'}]
        )
        print(api_info.to_dict())
    except ValidationError as e:
        print("Validation error lst: {e}".format(e=e))

    # 数组嵌套模型  错误示例（名称包含特殊字符）
    try:
        api_info = ApiInfo(api_id='a'*27, app_site={
            'name': 's001',
            'url': 'http://example.com'
        },
        lst=[{'name': 's-001', 'url': 'http://example.com'}]
        )
        print(api_info.to_dict())
    except ValidationError as e:
        print("Validation error lst name: {e}".format(e=e))

    # 数组嵌套模型 错误示例（URL格式错误）
    try:
        api_info = ApiInfo(api_id='a'*27, app_site={
            'name': 's001',
            'url': 'http://example.com'
        },
        lst=[{'name': 's001', 'url': 'ftp://example.com'}]
        )
        print(api_info.to_dict())
    except ValidationError as e:
        print("Validation error lst url: {e}".format(e=e))