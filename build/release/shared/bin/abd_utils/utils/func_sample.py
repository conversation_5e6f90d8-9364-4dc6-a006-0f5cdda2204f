#!/usr/bin/env python
# -*- coding: utf-8 -*-
import logging
import urllib
import copy
from collections import OrderedDict
import json

from abd_utils.utils.func_base import (
    API_DIR, 
    ErrMsgException, 
    create_data_dict, 
    smart_conf, 
    decompress_data, 
    compress_data, 
    BOT_TYPES, 
    escape_sql_str
)
from abd_utils.models.api_info_model import ApiInfoModel
from abd_utils.utils.func_pii import get_custom_pii_list
from abd_utils.utils.func_risk import get_all_customize_risk
from abd_utils.utils.func_api_tag import PATH_TO_API_MANUAL_TAG_LIST, BusinessTypeSettings
from service_mgr_rest_api import service_mgr_set_asp_config
import module_ctrls.ubbv2.ubb_util as ubb_util
from asp_utils.utils import ugettext
from asp_utils.sailfish_util import query_from_sailfish_sql
from abd_utils.utils.func_pii_builtin_rule import DEFAULT_PII_BUILTIN_LIST
from abd_utils.utils.func_pii_keyword_rule import DEFAULT_PII_KEYWORD_LIST
from abd_utils.repositories.utils import get_array
from asp_utils.utils import ugettext
from abd_utils.utils.func_datetime_convert import get_start_end_time_by_time_filter

SAMPLE_COLLECTION_FILE = API_DIR + 'sample_collection.json'
PATH_TO_SAMPLE_COLLECTION_PERIOD = 'nginx/api/sample_collection/period'
PATH_TO_SAMPLE_COLLECTION_RISK_LIST = 'nginx/api/sample_collection/risk_list'
PATH_TO_SAMPLE_COLLECTION_RISK_LIST_VERSION = 'nginx/api/sample_collection/risk_version'

# new version add new risks in DEFAULT_SAMPLE_COLLECTION_RISK_LIST, DEFAULT_SAMPLE_COLLECTION_RISK_LIST_VERSION increment 1

DEFAULT_SAMPLE_COLLECTION_RISK_LIST_VERSION = 17
DEFAULT_SAMPLE_COLLECTION_PII_BUILD_IN_LIST = [{'enabled': False, 'class': 'pii', 'name': pii['pii_type'], 'type': pii['class']} for pii in DEFAULT_PII_BUILTIN_LIST]
DEFAULT_SAMPLE_COLLECTION_PII_KEYWORD_LIST = [{'enabled': False, 'class': 'pii', 'name': pii['pii_type'], 'type': pii['id']} for pii in DEFAULT_PII_KEYWORD_LIST]

DEFAULT_SAMPLE_COLLECTION_RISK_LIST = [
    {
        'enabled': False,
        'name': ugettext('All'),
        'type': 'pii_leakage',
        'class': 'pii'
    },
    {
        'enabled': False,
        'name': ugettext('All'),
        'type': 'all_risk_leakage',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('All'),
        'type': 'all_business_types_leakage',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Plaintext Passwords'),
        'type': 'plain_pwd',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Weak Passwords'),
        'type': 'weak_pwd',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Lack of Authentications'),
        'type': 'unauth',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Parameter Non-compliance'),
        'type': 'invalid_args',
        'class': 'attack'
    },
    {
        'enabled': False,
        'name': ugettext('Password in URL'),
        'type': 'url_args_pwd',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('PIIs in URL'),
        'type': 'url_args_pii',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('URL contains authentication information'),
        'type': 'url_args_auth',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Inconsistent Desensitization Policies'),
        'type': 'redaction_policy',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Single Response of Multiple PII Types'),
        'type': 'multi_pii_leakage',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Unchecked SMS Interfaces'),
        'type': 'send_any_sms',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Leakage of Debug Information '),
        'type': 'debug_info_leakage',
        'class': 'risk'
    },
     {
        'enabled': False,
        'name': ugettext('Plaintext Password in Responses'),
        'type': 'plain_pwd_leakage',
        'class': 'risk'
    },
     {
        'enabled': False,
        'name': ugettext('Password in Responses'),
        'type': 'encode_pwd_leakage',
        'class': 'risk'
    },
        {
        'enabled': False,
        'name': ugettext('Inappropriate Prompt of Failed Logins'),
        'type': 'unreasonable_login',
        'class': 'risk'
    },
        {
        'enabled': False,
        'name': ugettext('Directory Browsing Not Disabled'),
        'type': 'undisabled_dir',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Password in Cookies '),
        'type': 'pwd_in_cookie',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Single Response of Multiple PIIs'),
        'type': 'excess_pii_leakage',
        'class': 'risk'
    },
    {
        'enabled' : False,
        'name' : ugettext('Unchecked File Downloads'),
        'type' : 'file_path',
        'class': 'risk'
    },
    {
        'enabled' : False,
        'name' : ugettext('Vulnerable Applications Accessible from the Extranet'),
        'type' : 'pubnet_has_vulapp',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Modifiable Response Data'),
        'type': 'data_quantity_modified',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Unchecked Command Executions'),
        'type': 'command_execut',
        'class': 'risk'
    },
    {
        'enabled' : False,
        'name' : ugettext('Traversable Parameters'),
        'type' : 'args_traversal',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext("Server-Side Request Forgery"),
        'type': 'server_side_request_forgery',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Unchecked Database Queries'),
        'type': 'sql_inquire',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Unlimited Login Rate'),
        'type': 'login_api_lacks_limit',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('JWT without Signature Algorithm'),
        'type': 'jwt_none_alg',
        'class': 'risk'
    },
        {
        'enabled': False,
        'name': ugettext('JWT with Weak Signature Algorithm'),
        'type': 'jwt_weak_alg',
        'class': 'risk'
    },
            {
        'enabled': False,
        'name': ugettext('JWT with Long-Term Validity'),
        'type': 'jwt_long_exp_time',
        'class': 'risk'
    },
    {
        'enabled': False,
        'name': ugettext('Login'),
        'type': 'login',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Registration'),
        'type': 'register',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Password Transmission'),
        'type': 'pwd_trans',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Upload'),
        'type': 'upload',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Download'),
        'type': 'download',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Human-Bot Verification'),
        'type': 'human_verify',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('SMS Sending'),
        'type': 'send_sms',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Kibana Business Component'),
        'type': 'kibana',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('ClickHouse Business Component'),
        'type': 'clickhouse',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Elasticsearch Business Component'),
        'type': 'elasticsearch',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Spring Boot Business Component'),
        'type': 'spring_boot',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Apache Druid Business Component'),
        'type': 'apache_druid',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Apache CouchDB Business Component'),
        'type': 'apache_couchdb',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Hadoop Business Component'),
        'type': 'hadoop',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Jenkins Business Component'),
        'type': 'jenkins',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Grafana Business Component'),
        'type': 'grafana',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('F5 BigIP Middleware'),
        'type': 'f5_bigip',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Kubernetes Business Component'),
        'type': 'kubernetes',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('ETCD Business Component'),
        'type': 'etcd',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Internet Data Collection'),
        'type': 'internet_data_collect',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Internet Data Exposure'),
        'type': 'internet_data_expose',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('PIIs Outbound Transmission'),
        'type': 'pii_data_outgoing',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Intranet PIIs Access'),
        'type': 'intranet_pii_access',
        'class': 'business_types'
    },
    {
        'enabled': False,
        'name': ugettext('Intranet PIIs Human-Bot Access'),
        'type': 'intranet_human_verify',
        'class': 'business_types'
    }

]
DEFAULT_SAMPLE_COLLECTION_RISK_LIST.extend(DEFAULT_SAMPLE_COLLECTION_PII_BUILD_IN_LIST)
DEFAULT_SAMPLE_COLLECTION_RISK_LIST.extend(DEFAULT_SAMPLE_COLLECTION_PII_KEYWORD_LIST)

DEFAULT_SAMPLE_COLLECTION_RISK_TYPES = [risk['type'] for risk in DEFAULT_SAMPLE_COLLECTION_RISK_LIST]
DEFAULT_TYPE_RISK_DICT = {risk['type']:risk for risk in DEFAULT_SAMPLE_COLLECTION_RISK_LIST}

DEFAULT_SAMPLE_COLLECTION_PERIOD = {
    'enabled': True,
    'freq': 3600
}

API_SAMPLE_COLLECTION_PERIOD_CONFIG = OrderedDict([
    ('enabled', {
        'value': lambda x: x.get('enabled', DEFAULT_SAMPLE_COLLECTION_PERIOD['enabled']),
        'validation_func': lambda x: ubb_util.ubb_invalid_bool(x)
    }),
    ('freq', {
        'value': lambda x: int(x.get('freq', DEFAULT_SAMPLE_COLLECTION_PERIOD['freq'])),
        'validation_func': lambda x: api_validate_sample_collection_freq(x)
    })
])

API_SAMPLE_COLLECTION_RISK_CONFIG = OrderedDict([
    ('enabled', {
        'value': lambda x: x.get('enabled', False),
        'validation_func': lambda x: ubb_util.ubb_invalid_bool(x)
    }),
    ('name', {
        'value': lambda x: get_full_sample_collection_name_by_type(x['type']),
        'validation_func': lambda x: ubb_util.ubb_invalid_string(x, length=100, min_lenth=0)
    }),
    ('type', {
        'value': lambda x: x.get('type', ''),
        'validation_func': lambda x: api_validate_risk_type(x)
    }),
    ('class', {
        'value': lambda x: get_full_sample_collection_class_by_type(x['type']),
        'validation_func': lambda x: ubb_util.ubb_invalid_string(x, length=100, min_lenth=0)
    })

])


def get_full_sample_collection_name_by_type(risk_type):
    default_type_risk_dict = DEFAULT_TYPE_RISK_DICT.get(risk_type)
    if default_type_risk_dict:
        return default_type_risk_dict['name']
    
    all_customized_risk = get_all_customize_risk()
    if all_customized_risk:
        customized_risk_type_dict = {risk['risk_type']:risk for risk in all_customized_risk}
        risk_conf = customized_risk_type_dict.get(risk_type)
        if risk_conf:
            return risk_conf['risk_name']

    custom_pii_list = get_custom_pii_list()
    for pii in custom_pii_list:
        if pii['id'] == risk_type:
            return pii['pii_type']

    business_type_settings = BusinessTypeSettings()
    business_list = business_type_settings.get_setting_list()
    for business in business_list:
        if business['name'] == risk_type:
            return business['name']

    raise Exception("unknown type")


def api_validate_risk_type(type):
    ubb_util.ubb_invalid_string(type, length=100, min_lenth=0)
    custom_pii_list = get_custom_pii_list()
    customized_risk_list = get_all_customize_risk()
    custom_pii_type = [pii['id'] for pii in custom_pii_list]
    business_type_settings = BusinessTypeSettings()
    business_list = business_type_settings.get_setting_list()
    business_name_list = [bus['name'] for bus in business_list]
    customized_risk_type = [risk['risk_type'] for risk in customized_risk_list]
    if type not in DEFAULT_SAMPLE_COLLECTION_RISK_TYPES and type not in custom_pii_type and type not in business_name_list and type not in customized_risk_type:
        raise ErrMsgException(ugettext('Invalid Risk Type'))


def update_sample_collect_conf_due_del_manual_business(business_name):
    conf_db = smart_conf.db()
    risk_list = conf_db.get_value(PATH_TO_SAMPLE_COLLECTION_RISK_LIST, [])
    if risk_list:
        risk_dict_list = decompress_data(risk_list)
        for risk_dict in risk_dict_list:
            if risk_dict['name'] == business_name:
                risk_dict_list.remove(risk_dict)
                risk_list = compress_data(risk_dict_list)
                conf_db.set_value(PATH_TO_SAMPLE_COLLECTION_RISK_LIST, risk_list)
                ret, _, _ = service_mgr_set_asp_config({PATH_TO_SAMPLE_COLLECTION_RISK_LIST: risk_list}, 'IP', 'User',
                                                       sync=True)
                break


def get_full_sample_collection_class_by_type(risk_type):
    default_type_risk_dict = DEFAULT_TYPE_RISK_DICT.get(risk_type)
    if default_type_risk_dict:
        return default_type_risk_dict['class']
    
    custom_risk_list = get_all_customize_risk()
    for risk in custom_risk_list:
        if risk['risk_type'] == risk_type:
            return 'risk'

    custom_pii_list = get_custom_pii_list()
    for pii in custom_pii_list:
        if pii['id'] == risk_type:
            return "pii"

    business_type_settings = BusinessTypeSettings()
    business_list = business_type_settings.get_setting_list()
    for business in business_list:
        if business['name'] == risk_type:
            return 'business_types'

    raise Exception("unknown type")


def api_validate_sample_collection_freq(freq):
    if freq not in (300, 3600, 86400):
        raise ErrMsgException(ugettext('Invalid Frequency'))


def api_validate_sample_collection_risk_list(risk_dict_list):
    new_risk_dict_list = []
    for risk in risk_dict_list:
        risk = create_data_dict(risk, API_SAMPLE_COLLECTION_RISK_CONFIG)
        new_risk_dict_list.append(risk)
    return new_risk_dict_list


def merge_default_sample_risk_list(risk_dict_list):

    conf_db = smart_conf.db()
    new_risk_dict_list = []
    old_risk_version = conf_db.get_value(PATH_TO_SAMPLE_COLLECTION_RISK_LIST_VERSION, 0)

    if DEFAULT_SAMPLE_COLLECTION_RISK_LIST_VERSION > old_risk_version:
        # new version adds new risks
        customized_risks = conf_db.get_value("nginx/api/v2/customize_api_risk_list", [])
        if customized_risks:
            customized_type_risk_dict = {customized_risk['risk_type']:customized_risk for customized_risk in customized_risks}
        else:
            customized_type_risk_dict = {}

        old_type_risk_dict = {risk['type']:risk for risk in risk_dict_list}
        for default_risk in DEFAULT_SAMPLE_COLLECTION_RISK_LIST:
            risk_type = default_risk['type']
            if risk_type not in old_type_risk_dict.keys():
                new_risk_dict_list.append(default_risk)
            else:
                old_risk_dict = old_type_risk_dict.get(risk_type)
                # support conf adds some keys
                diff_keys = list(set(default_risk.keys()) - set(old_risk_dict.keys()))
                if diff_keys:
                    for key in diff_keys:
                        old_risk_dict[key] = default_risk[key]

                old_risk_dict['name'] = default_risk['name']
                old_risk_dict['class'] = default_risk['class']
                new_risk_dict_list.append(old_risk_dict)
        for customized_risk in customized_type_risk_dict.keys():
            if not customized_type_risk_dict[customized_risk]['is_show']:
                continue
            risk_type = customized_risk
            if risk_type not in old_type_risk_dict.keys():
                new_risk_dict_list.append({
                    'type': customized_type_risk_dict[risk_type]['risk_type'],
                    'name': customized_type_risk_dict[risk_type]['risk_name'],
                    'class': 'risk',
                    'enabled': False
                })
            else:
                new_risk_dict_list.append(
                    {
                        'type': old_type_risk_dict[risk_type]['type'],
                        'name': old_type_risk_dict[risk_type]['name'],
                        'class': 'risk',
                        'enabled': old_type_risk_dict[risk_type]['enabled']
                    }
                )

        # remove all default tag from DEFAULT_SAMPLE_COLLECTION_RISK_LIST since version 14
        if old_risk_version < 10:
            for risk_dict in risk_dict_list:
                if risk_dict['class'] == 'tag':
                    risk_dict['tag'] = 'business_types'
                    new_risk_dict_list.append(risk_dict)

        zk_risk_list = compress_data(new_risk_dict_list)
        ret, _, _ = service_mgr_set_asp_config({PATH_TO_SAMPLE_COLLECTION_RISK_LIST: zk_risk_list}, 'IP', 'User', sync=True)

        if ret == 0:
            service_mgr_set_asp_config(
                {PATH_TO_SAMPLE_COLLECTION_RISK_LIST_VERSION: DEFAULT_SAMPLE_COLLECTION_RISK_LIST_VERSION},
                'IP', 'User', sync=True)

    else:
        new_risk_dict_list = risk_dict_list

    return new_risk_dict_list


def update_reference_business_name(old_name, new_name):
    collection_list = get_sample_risk_dict_list()
    new_collection_list = []
    business_name_dict = {}
    for collection in collection_list:
        if collection['class'] == 'business_types':
            business_name_dict[collection['name']] = collection
        else:
            new_collection_list.append(collection)

    old_item = business_name_dict.pop(old_name)
    if new_name in business_name_dict.keys():
        new_item = business_name_dict[new_name]
        new_item['enabled'] = new_item['enabled'] | old_item['enabled']
    else:
        old_item['name'] = new_name
        old_item['type'] = new_name
        business_name_dict[new_name] = old_item

    for key, item in business_name_dict.items():
        new_collection_list.append(item)

    save_sample_risk_dict_list(new_collection_list)


def get_sample_risk_dict_list():
    conf_db = smart_conf.db()
    risk_list = conf_db.get_value(PATH_TO_SAMPLE_COLLECTION_RISK_LIST, [])
    if risk_list:
        risk_dict_list = decompress_data(risk_list)
    else:
        risk_dict_list = copy.deepcopy(DEFAULT_SAMPLE_COLLECTION_RISK_LIST)

    risk_dict_list = merge_default_sample_risk_list(risk_dict_list)

    merge_custom_pii_sample_risk_list(risk_dict_list)
    merge_business_type_list(risk_dict_list)

    return risk_dict_list


def merge_business_type_list(risk_dict_list):
    business_type_settings = BusinessTypeSettings()
    setting_list = business_type_settings.get_setting_list()
    business_types = set()
    business_risk_dict_list = []
    for business in setting_list:
        if business['name'] not in business_types:
            business_risk_dict_list.append({"enabled": False, "type": business['name'], "name": business['name'], "class": "business_types"})
            business_types.add(business['name'])

    defect_business_name_list = []
    for risk in risk_dict_list:
        if risk['class'] == 'business_types':
            defect_business_name_list.append(risk['name'])

    for business_risk_dict in business_risk_dict_list:
        if business_risk_dict['name'] not in defect_business_name_list:
            risk_dict_list.append(business_risk_dict)

    return risk_dict_list


def merge_custom_pii_sample_risk_list(risk_dict_list):
    default_risk_type_list = [risk['type'] for risk in risk_dict_list]
    custom_pii_list = get_custom_pii_list()
    if not custom_pii_list:
        return

    custom_pii_risk_dict_list = [{"class": "pii", "enabled": False, "type": pii['id'], "name": pii['pii_type']} for pii
                                 in custom_pii_list]
    for custom_pii_risk_dict in custom_pii_risk_dict_list:
        if custom_pii_risk_dict['type'] not in default_risk_type_list:
            risk_dict_list.append(custom_pii_risk_dict)

    return risk_dict_list


def get_sample_collection_conf():
    conf_db = smart_conf.db()
    sample_conf = conf_db.get_value(PATH_TO_SAMPLE_COLLECTION_PERIOD, DEFAULT_SAMPLE_COLLECTION_PERIOD.copy())
    risk_dict_list = get_sample_risk_dict_list()
    sample_conf['risk_dict_list'] = risk_dict_list
    return sample_conf


def save_sample_risk_dict_list(risk_dict_list):
    ret = 0
    if risk_dict_list:
        risk_dict_list = merge_default_sample_risk_list(risk_dict_list)
        risk_list = compress_data(risk_dict_list)
        ret, _, _ = service_mgr_set_asp_config({PATH_TO_SAMPLE_COLLECTION_RISK_LIST: risk_list}, 'IP', 'User',
                                               sync=True)

    return ret


def create_condition(field_name, vals, field_type):
    if not vals:
        return ''
    
    # inject escape
    vals = [escape_sql_str(str(v)) for v in vals]
    if field_type == 'array':
        if len(vals) == 1:
            if vals[0] == 'no':
                return 'empty({})'.format(field_name)
            
            if vals[0] == 'any':
                return 'notEmpty({})'.format(field_name)
        
        # 单选 或 多选 eg: hasAny(pii_value, ['15608185555', '15608185556'])
        val_str_array = """['{}']""".format("', '".join(vals))
        return 'hasAny({}, {})'.format(field_name, val_str_array)

    elif field_type == 'string':
        if len(vals) == 1:
            if vals[0] == 'no':
                return " {} = '' ".format(field_name)
            
            if vals[0] == 'any':
                return " {} != '' ".format(field_name)
            
        
        # 单选 或 多选 eg: hasAny(pii_value, ['15608185555', '15608185556'])
        val_str_array = """['{}']""".format("', '".join(vals))
        return '{} in {}'.format(field_name, val_str_array)

    
def format_sample_data(api_id, sample_result_list, ignore_defects):
    
    from func_risk import format_risk_data
    # 如果删除了已忽略的缺陷信息后该样例无pii无risk无bot，则去掉此条样例，但无pii无risk无bot的定时样例需要保留
    for sample in sample_result_list:
        for ignore_info in ignore_defects:
            # 去除“已忽略”的缺陷信息
            if ignore_info['defect_name'] in sample['api_risk_name'] and api_id == ignore_info['api_id']:
                index = sample['api_risk_name'].index(ignore_info['defect_name'])
                sample['api_risk_name'].pop(index)
                sample['api_risk_value'].pop(index)
                if len(sample['api_risk_value_position']) >= index + 1:
                    sample['api_risk_value_position'].pop(index)

        sample['defect'] = format_risk_data(sample)
        if sample['bot_kind'] in BOT_TYPES:
            sample['bot_kind'] = BOT_TYPES.get(sample['bot_kind'])

        upstream_addr = sample['upstream_addr']
        #健康检查不过, 无上游可用时upstream记的值类似这样 10.10.71.243_80_list
        if upstream_addr and ':' in upstream_addr:
            dst_ip, dst_port = upstream_addr.rsplit(':', 1)
            if dst_ip[0] == '[':
                dst_ip = dst_ip[1:-1]
            
            sample['dst_ip'] = dst_ip
            sample['dst_port'] = dst_port

        del sample['api_risk_name']
        del sample['api_risk_value']
        del sample['api_risk_value_position']
    
    from abd_utils.utils.func_pii import format_pii_datas
    format_pii_datas(sample_result_list)
    return sample_result_list

def gen_samples_where(api_id, **kwargs):
    conditions = [
        create_condition('api_risk_name', kwargs.get('defects', []), 'array'),
        create_condition('pii_type', kwargs.get('piis', []), 'array'),
        create_condition('bot_category', kwargs.get('bots', []), 'string'),
        create_condition('api_industry_data_tag',kwargs.get('industries', []), 'array'),
    ]

    conditions = [condition for condition in conditions if condition]

    conditions = ' AND '.join(conditions)
    if conditions.strip():
        conditions = ' AND ' + conditions

    # 排除首条样例
    request_uuid = kwargs.get('request_uuid', -1)
    if request_uuid is not None and request_uuid != -1:
        conditions += ' AND request_uuid != {}'.format(request_uuid)

    api_map = ApiInfoModel.select(ApiInfoModel.api_map).where(ApiInfoModel.id == api_id).dicts().get()
    api_ids = get_array(api_map['api_map'])
    api_ids.append(api_id)
    # inject escape
    api_ids = [escape_sql_str(str(v)) for v in api_ids]
    api_ids = "','".join(api_ids)
    api_ids = "'" + api_ids + "'"

    return api_ids, conditions

def get_first_sample(sailfish_username, **kwargs):
    api_id = kwargs.get('id', '')
    ignore_defects = kwargs.get('ignore_defects', [])

    api_ids, conditions = gen_samples_where(api_id, **kwargs)
    first_sample_sql = '''
       SELECT
            request_uuid as id,
            api_id_sample as api_id,
            toUnixTimestamp(timestamp) as created,
            if(http_host!='', http_host, hostname) as hostname,
            path,
            is_https,
            src_ip as source_ip,
            src_port as source_port,
            upstream_addr,
            bot_name,
            bot_category as bot_kind,
            pii_type,
            pii_value,
            pii_position,
            api_risk_name,
            api_risk_value,
            api_risk_value_position,
            status as resp_status,
            api_snap_reqheader as req_header,
            api_snap_reqbody as req_body,
            api_snap_respheader as resp_header,
            api_snap_respbody as resp_body,
            api_industry_data_tag as industry_data_tag,
            business_username,
            lengthUTF8(api_snap_respbody) as content_len,
            lengthUTF8(api_snap_reqheader) as reqheader_len,
            lengthUTF8(api_snap_reqbody) as reqbody_len,
            lengthUTF8(api_snap_respheader) as respheader_len
        from
            first_api_sample
        PREWHERE
            api_id in [{api_ids}]
            {conditions}
        ORDER BY
            timestamp
        LIMIT
            1
'''.format(api_ids=api_ids, conditions=conditions)

    ret, err_msg, first_sample = query_from_sailfish_sql(first_sample_sql, sailfish_user=sailfish_username)
    if ret != 0:
        return ret, 1, []

    format_sample_data(api_id, first_sample, ignore_defects)

    return ret, 1, first_sample

def get_samples(sailfish_username, **kwargs):
    api_id = kwargs.get('id', '')
    time_filter = kwargs.get('time_filter', '')
    day = kwargs.get('day', 7)
    page = kwargs.get('page', 1)
    page_size = kwargs.get('page_size', 50)
    ignore_defects = kwargs.get('ignore_defects', [])
    
    if time_filter:
        _, _, last_time = get_start_end_time_by_time_filter(time_filter)
    else:
        last_time = -1
    
    api_ids, conditions = gen_samples_where(api_id, **kwargs)

    sql_count_template = '''
        SELECT count() as count
        from api_sample  
        PREWHERE 
            api_id_sample in [{api_ids}]
            {conditions}
            AND timestamp > minus({latest_time}, toIntervalDay({day}))
            AND timestamp <= {latest_time}
    '''

    latest_time = 'now()' if last_time == -1 else 'toDateTime({})'.format(last_time)
    sql_count = sql_count_template.format(api_ids=api_ids, conditions=conditions, latest_time=latest_time, day=day)

    
    ret, err_msg, result = query_from_sailfish_sql(sql_count, sailfish_user=sailfish_username)
    if ret != 0:
        return ret, 0, []

    total_count = result[0]['count']
    if total_count == 0: # 近7天查不到数据， 查最近一条样例时间的前7天数据
        select_sample_max_time_sql = '''
        SELECT toUnixTimestamp(max(timestamp)) as max_time
        from api_sample  
        PREWHERE 
            api_id_sample in [{}]
            {}
            AND timestamp <= {}
    '''.format(api_ids, conditions, latest_time)
        
        ret, err_msg, result = query_from_sailfish_sql(select_sample_max_time_sql, sailfish_user=sailfish_username)
        if ret != 0:
            return ret, 0, []
        
        if result[0]['max_time'] == 0:
            # 所有时间都未查到数据
            return ret, 0, []

        latest_time = 'toDateTime({})'.format(result[0]['max_time'])
        sql_count = sql_count_template.format(api_ids=api_ids, conditions=conditions, latest_time=latest_time, day=day)
        ret, err_msg, result = query_from_sailfish_sql(sql_count, sailfish_user=sailfish_username)
        if ret != 0:
            return ret, 0, []
        
        total_count = result[0]['count']
    
    # 优化思路:
    # 样例表有很多字段，有的字段查询开销较大（比如响应体、响应头），有的较小（比如timestamp，uuid）
    # 可以先用一个子查询筛选出指定分页的uuid和timestamp，只有100个，然后再从完整的样例表中，基于uuid和timestamp补充完整的样例字段
    sample_sql = '''
        SELECT
            request_uuid as id,
            api_id_sample as api_id,
            toUnixTimestamp(timestamp) as created,
            if(http_host!='', http_host, hostname) as hostname,
            path,
            is_https,
            src_ip as source_ip,
            src_port as source_port,
            upstream_addr,
            bot_name,
            bot_category as bot_kind,
            pii_type,
            pii_value,
            pii_position,
            api_risk_name,
            api_risk_value,
            api_risk_value_position,
            status as resp_status,
            api_snap_reqheader as req_header,
            api_snap_reqbody as req_body,
            api_snap_respheader as resp_header,
            api_snap_respbody as resp_body,
            api_industry_data_tag as industry_data_tag,
            business_username,
            lengthUTF8(api_snap_respbody) as content_len,
            lengthUTF8(api_snap_reqheader) as reqheader_len,
            lengthUTF8(api_snap_reqbody) as reqbody_len,
            lengthUTF8(api_snap_respheader) as respheader_len
        from
            api_sample
        PREWHERE
            api_id in [{api_ids}]
            {conditions}
            AND (request_uuid, timestamp) GLOBAL in (
                            SELECT
                                request_uuid,
                                timestamp
                            from
                                api_sample
                            PREWHERE
                                api_id_sample in [{api_ids}]
                                {conditions}
                                AND timestamp > minus({latest_time}, toIntervalDay({day}))
                                AND timestamp <= {latest_time}
                            ORDER BY
                                timestamp DESC
                            LIMIT
                                {page_size} OFFSET {offset}
                            )
        ORDER BY
            timestamp DESC
        LIMIT
            {page_size}
'''.format(api_ids=api_ids, conditions=conditions, latest_time=latest_time, day=day, page_size=page_size, offset=(page - 1) * page_size)

    ret, err_msg, api_samples = query_from_sailfish_sql(sample_sql, sailfish_user=sailfish_username)
    if ret != 0:
        return ret, total_count, []

    format_sample_data(api_id, api_samples, ignore_defects)

    return ret, total_count, api_samples
