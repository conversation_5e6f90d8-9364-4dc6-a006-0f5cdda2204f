{"01f49432-f59c-11eb-8fd3-00505693753b": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": true, "sql_statement": ["SELECT", "  key,", "  key_type as kt,", "  max(toInt16(round(score))) as score,", "  51 as threat_type", "from", "  threat_event_api", "WHERE", "  timestamp > toStartOfHour(now() - 3600) - 1", "  AND group_name = 'source' AND threat_type>10", "group BY", "  key,", "  kt"]}}, "name": "api_reputation_set", "summary": {"cron_type": "sql", "frequency": "30 * * * *", "status": "disabled"}, "timestamp": "1628133596.14", "type": "cron_task"}, "0bfc7804-6150-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  threat_event_api_local (", "    timestamp,", "    group_name,", "    app_site_id,", "    event_ct_rt,", "    event_bytes_out_rt,", "    event_rsp_time_rt,", "    event_dc_fp_rt,", "    event_dc_user_rt,", "    event_dc_path_rt,", "    event_sr_rt,", "    reason,", "    score,", "    threat_type,", "    event_ct,", "    event_dc_fp,", "    event_dc_user,", "    event_dc_path,", "    event_bytes_out,", "    event_sr,", "    event_rsp_time,", "    event_inj,", "    event_invalid_args_ct,", "    event_sensitive_info_ct,", "    key,", "    key_type", "  )", "SELECT", "  slot,", "  'source' as group_name,", "  app_site_id,", "  (count - summary_avg_count) / arrayReduce(", "    'max',", "    [summary_std_count,summary_avg_count*toFloat64(dictGetString('default.api_parameters','count_std_threshold',tuple('ip'))),0.01]", "  ) as event_ct_rt,", "  (avg_bytes_out - summary_avg_avg_bytes_out) / arrayReduce(", "    'max',", "    [summary_std_avg_bytes_out,summary_avg_avg_bytes_out*toFloat64(dictGetString('default.api_parameters','bytes_out_std_threshold',tuple('ip'))),0.01]", "  ) as event_bytes_out_rt,", "  (rsp_time - summary_avg_rsp_time) / arrayReduce(", "    'max',", "    [summary_std_rsp_time,summary_avg_rsp_time*toFloat64(dictGetString('default.api_parameters','rsp_time_std_threshold',tuple('ip'))),0.01]", "  ) as event_rsp_rt,", "  (dc_fp - summary_avg_dc_fp) / arrayReduce(", "    'max',", "    [summary_std_dc_fp,summary_avg_dc_fp*toFloat64(dictGetString('default.api_parameters','dc_fp_std_threshold',tuple('ip'))),0.01]", "  ) as event_dc_fp_rt,", "  (dc_user - summary_avg_dc_user) / arrayReduce(", "    'max',", "    [summary_std_dc_user,summary_avg_dc_user*toFloat64(dictGetString('default.api_parameters','dc_user_std_threshold',tuple('ip'))),0.01]", "  ) as event_dc_user_rt,", "  (dc_path - summary_avg_dc_path) / arrayReduce(", "    'max',", "    [summary_std_dc_path,summary_avg_dc_path*toFloat64(dictGetString('default.api_parameters','dc_path_std_threshold',tuple('ip'))),0.01]", "  ) as event_dc_path_rt,", "  (success_rate - summary_avg_success_rate) / arrayReduce(", "    'max',", "    [summary_std_success_rate,summary_avg_success_rate*toFloat64(dictGetString('default.api_parameters','success_rate_std_threshold',tuple('ip'))),0.01]", "  ) as event_sr_rt,", "  trim(", "    LEADING ','", "    FROM", "      concat(", "        if(", "          summary_data_volume > 2,", "          concat(", "            multiIf(", "              event_ct_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'count_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              'ip_count_high',", "              -1 * event_ct_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'count_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              'ip_count_low',", "              ''", "            ),", "            multiIf(", "              event_bytes_out_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'bytes_out_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_avg_bytes_out_high',", "              -1 * event_bytes_out_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'bytes_out_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_avg_bytes_out_low',", "              ''", "            ),", "            multiIf(", "              event_rsp_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'rsp_time_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_rsp_time_high',", "              -1 * event_rsp_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'rsp_time_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_rsp_time_low',", "              ''", "            ),", "            multiIf(", "              event_dc_path_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_path_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_dc_path_high',", "              -1 * event_dc_path_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_path_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_dc_path_low',", "              ''", "            ),", "            multiIf(", "              event_dc_fp_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_fp_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_dc_fp_high',", "              -1 * event_dc_fp_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_fp_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_dc_fp_low',", "              ''", "            ),", "            multiIf(", "              event_dc_user_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_user_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_dc_user_high',", "              -1 * event_dc_user_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_user_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_dc_user_low',", "              ''", "            ),", "            multiIf(", "              event_sr_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'success_rate_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_success_rate_high',", "              -1 * event_sr_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'success_rate_range_parameter',", "                  tuple('ip')", "                )", "              ),", "              ',ip_success_rate_low',", "              ''", "            )", "          ),", "          ''", "        ),", "        if(inj > 0, ',Inject', ''),", "        if(invalid_args_ct > 0, ',invalid_args_req', ''),", "        if(sensitive_info_ct > 99999999999999999990, ',sensitive_info_req', ''),", "        if(dc_user>3,',multiple_user_on_ip','')", "      )", "  ) as reason,", "  arrayReduce(", "    'min',", "    [100,round(if(summary_data_volume>2, arrayReduce(", "    'max',", "    [abs(event_ct_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'count_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_ct_rt > 0, 5, 1) + arrayReduce(", "    'max',", "    [abs(event_bytes_out_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'bytes_out_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_bytes_out_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_rsp_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'rsp_time_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_rsp_rt > 0, 10, 0.1) + arrayReduce(", "    'max',", "    [abs(event_dc_fp_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_fp_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_dc_fp_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_dc_user_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_user_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_dc_user_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_dc_path_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_path_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_dc_path_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_sr_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'success_rate_range_parameter',", "              tuple('ip')", "            )", "          )-1,0]", "  ) * if(event_sr_rt > 0, 1, 15),", "  0", ") + sqrt(inj) * 10 + if(sensitive_info_ct > 0, 0, 0) + sqrt(invalid_args_ct) * 5 + if(dc_user>3,arrayReduce('min',[40,5*(dc_user-3)]) ,0),", "2", ") ]", ") as score,", "arrayJoin(", "  arrayFilter(", "    x -> x is not NULL,", "    [if(event_ct_rt>1 AND event_rsp_rt>1 AND count>100000, 57, NULL), if((count>1000 AND event_ct_rt>2) OR (event_bytes_out_rt>2 AND count>1000), 53,NULL ), if((dc_path>50 AND event_dc_path_rt>1) OR (summary_avg_dc_path=0 AND dc_path>100), 54,NULL), if(count>100 AND success_rate<0.4 AND event_sr_rt<-1,51,NULL), if(inj>0,56,NULL), if(sensitive_info_ct>99999999999999999999999990,46,NULL), if(invalid_args_ct>0,47,NULL), if(dc_user>3,45,NULL)]", "  )", ") as threat_type,", "count,", "dc_fp,", "dc_user,", "dc_path,", "avg_bytes_out,", "success_rate,", "rsp_time,", "inj,", "invalid_args_ct,", "sensitive_info_ct,", "src_ip as key,", "1 as kt", "FROM", "  (", "    SELECT", "      COUNT(*) as count,", "      avg(body_bytes_out) as avg_bytes_out,", "      COUNT(DISTINCT path) as dc_path,", "      COUNT(DISTINCT user) as dc_user,", "      COUNT(DISTINCT fp) as dc_fp,", "      countIf(", "        status = 200", "        OR business_success = 1", "      ) / count as success_rate,", "      coalesce(avg(upstream_response_time), 0) as rsp_time,", "      src_ip,", "      countIf(has(attack_type, 'INVALID_API_ARGS')) as invalid_args_ct,", "      countIf(notEmpty(pii_type)) as sensitive_info_ct,", "      app_id as app_site_id,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday,", "      toStartOfHour(timestamp) as slot,", "      sum(", "        if(hasAny(attack_type, ['Inject', 'AIWAF']), 1, 0)", "      ) as inj", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 HOUR)", "      and toStartOfHour(now()) -1", "    GROUP BY", "      src_ip,", "      app_site_id,", "      date_hour,", "      date_wday,", "      slot", "  ) as now_data LEFT ALL", "  JOIN (", "    SELECT", "      key,", "      app_site_id,", "      COUNT() as summary_data_volume,", "      avg(summary_count) as summary_avg_count,", "      STDDEV_POP(summary_count) as summary_std_count,", "      avg(summary_avg_bytes_out) as summary_avg_avg_bytes_out,", "      STDDEV_POP(summary_avg_bytes_out) as summary_std_avg_bytes_out,", "      avg(summary_dc_fp) as summary_avg_dc_fp,", "      STDDEV_POP(summary_dc_fp) as summary_std_dc_fp,", "      avg(summary_dc_user) as summary_avg_dc_user,", "      STDDEV_POP(summary_dc_user) as summary_std_dc_user,", "      avg(summary_dc_path) as summary_avg_dc_path,", "      STDDEV_POP(summary_dc_path) as summary_std_dc_path,", "      avg(summary_success / summary_count) as summary_avg_success_rate,", "      STDDEV_POP(summary_success / summary_count) as summary_std_success_rate,", "      if(isFinite(avgIf(summary_rsp_time, summary_rsp_time>0)), avgIf(summary_rsp_time, summary_rsp_time>0),1000) as summary_avg_rsp_time,", "      if(isFinite(STDDEV_POPIf(summary_rsp_time, summary_rsp_time>0)), STDDEV_POPIf(summary_rsp_time, summary_rsp_time>0),10000) as summary_std_rsp_time,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday", "    from", "      summary_data_api", "    WHERE", "      key_type = 1", "      AND timestamp > toStartOfDay(now() -14 * 86400)", "    GROUP BY", "      key,", "      app_site_id,", "      date_hour,", "      date_wday", "  ) as baseline_data ON now_data.src_ip = baseline_data.key", "  AND now_data.app_site_id = baseline_data.app_site_id", "  AND now_data.date_hour = baseline_data.date_hour", "  AND now_data.date_wday = baseline_data.date_wday", "HAVING", "  score >= 10"]}}, "name": "api_threat_ip_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "8 * * * *", "status": "disabled"}, "timestamp": "**********.74", "type": "cron_task"}, "0ca7d50a-1580-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_account_summary_hour --fields 'timestamp, app_site_id, api_id, account, total'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  api_id,", "  if(", "    length(business_username) > 0,", "    business_username,", "    business_submit_username", "  ) as account,", "  count() as total", "from", "  access_log", "WHERE", "  (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "  AND notEmpty(account)", "  AND notEmpty(api_id)", "GROUP BY", "  time,", "  app_id,", "  api_id,", "  account", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_hour_资产账号按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "**********.2", "type": "cron_task"}, "0f40188c-6460-11eb-bbbf-00505694620f": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  threat_event_api_local (", "    timestamp,", "    group_name,", "    app_site_id,", "    reason,", "    score,", "    threat_type,", "    event_ct,", "    event_dc_fp,", "    event_dc_ip,", "    event_dc_path,", "    event_bytes_out,", "    event_sr,", "    event_rsp_time,", "    event_inj,", "    event_invalid_args_ct,", "    event_sensitive_info_ct,", "    key,", "    key_type", "  )", "SELECT", "  slot,", "  'source' as group_name,", "  app_site_id,", "  trim(", "    LEADING ','", "    FROM", "      concat(", "        '',if(dc_ip>3,',multiple_ip_on_user','')", "      )", "  ) as reason,", "  arrayReduce(", "    'min',", "    [100, round(if(dc_ip>3,5*(dc_ip-3) ,0),", "2", ") ]", ") as score,", "arrayJoin(", "  arrayFilter(", "    x -> x is not NULL,", "    [ if(dc_ip>3,44,NULL)]", "  )", ") as threat_type,", "count,", "dc_fp,", "dc_ip,", "dc_path,", "avg_bytes_out,", "success_rate,", "rsp_time,", "inj,", "invalid_args_ct,", "sensitive_info_ct,", "user as key,", "3 as kt", "FROM", "  (", "    SELECT", "      COUNT(*) as count,", "      avg(body_bytes_out) as avg_bytes_out,", "      COUNT(DISTINCT path) as dc_path,", "      COUNT(DISTINCT src_ip) as dc_ip,", "      COUNT(DISTINCT fp) as dc_fp,", "      countIf(", "        status = 200", "        OR business_success = 1", "      ) / count as success_rate,", "      coalesce(avg(upstream_response_time), 0) as rsp_time,", "      user,", "      countIf(has(attack_type, 'INVALID_API_ARGS')) as invalid_args_ct,", "      countIf(notEmpty(pii_type)) as sensitive_info_ct,", "      app_id as app_site_id,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday,", "      toStartOfHour(timestamp) as slot,", "      sum(", "        if(hasAny(attack_type, ['Inject', 'AIWAF']), 1, 0)", "      ) as inj", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND notEmpty(user)", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 HOUR)", "      and toStartOfHour(now()) -1", "    GROUP BY", "      user,", "      app_site_id,", "      date_hour,", "      date_wday,", "      slot)", "HAVING", "  score >= 10"]}}, "name": "api_threat_user_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "8 * * * *", "status": "disabled"}, "timestamp": "**********.8", "type": "cron_task"}, "10421df2-d4de-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_account_summary_day --fields 'timestamp, app_site_id, api_id, account, total'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  account,", "  sum(total) as total", "from", "  asset_account_summary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  account", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_资产账号按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "**********.31", "type": "cron_task"}, "109e9e0e-3a79-11ef-b09c-00505693e4f3": {"detail": {"statement": {"cmd_args": "", "cmd_name": "kafkaSender", "cmd_switch": false, "sql_statement": ["insert into", "  api_baseline_risks_local (", "    timestamp,", "    src_ip,", "    app_name,", "    api_endpoint,", "    count_summary,", "    low,", "    up,", "    anomaly_tag,", "    type_score,", "    windows_type", "  )", "SELECT", "  t,", "  src_ip,", "  app_name,", "  api_endpoint,", "  array(tag_summary) as count_summary,", "  low,", "  up,", "  array(anomaly_status) as anomaly_tag,", "  type_score,", "  '1' as windows_type", "from", "  (", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(round(c, 2)) as tag_summary,", "      if(", "        round(moving_avg, 2) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        round(moving_avg, 2) + 3 * round(moving_stddev, 2),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRD01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRD02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          body_bytes_out_count /(1024 * 1024) as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN moving_stddev != 0", "            AND c > round(", "              round(moving_avg, 2) + 3 * round(moving_stddev, 2),", "              2", "            ) THEN 'BRD01'", "            WHEN body_bytes_out_count = 0 THEN 'BRD02'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              total,", "              body_bytes_out_total as body_bytes_out_count", "            from", "              asset_ipsummary_hour t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfHour(now()) - INTERVAL 24 HOUR", "                and toStartOfHour(now())", "              )", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND (notEmpty(app_name))", "      AND (low != up)", "    ORDER BY", "      t DESC", "    union ALL", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(c) as tag_summary,", "      if(", "        round(moving_avg) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        round(moving_avg) + 3 * round(moving_stdd<PERSON>, 2),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRA01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRA02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          request_count as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN moving_stddev != 0", "            AND c > round(moving_avg) + 3 * round(moving_stddev) THEN 'BRA01'", "            WHEN moving_stddev = 0", "            AND request_count = 0 THEN 'BRA02'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              total as request_count,", "              total", "            from", "              asset_ipsummary_hour t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfHour(now()) - INTERVAL 24 HOUR", "                and toStartOfHour(now())", "              )", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND c > 1000", "      AND (", "        notEmpty(app_name)", "        AND notEmpty(api_endpoint)", "      )", "    ORDER BY", "      t DESC", "    UNION ALL", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(round(c, 2)) as tag_summary,", "      if(", "        round(moving_avg, 2) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        round(moving_avg, 2) + 3 * round(moving_stddev, 2),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRB01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRB02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRB03',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          response_time_count as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN c > moving_avg + 3 * moving_stddev THEN 'BRB01'", "            WHEN c != 0", "            AND c > total * 60 THEN 'BRB02'", "            WHEN response_time_count = 0 THEN 'BRB03'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              response_time_total as response_time_count,", "              total", "            from", "              asset_ipsummary_hour t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfHour(now()) - INTERVAL 24 HOUR", "                and toStartOfHour(now())", "              )", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND (", "        notEmpty(app_name)", "        AND notEmpty(api_endpoint)", "      )", "    ORDER BY", "      tag_summary DESC", "    UNION ALL", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(c) as tag_summary,", "      if(", "        round(moving_avg, 2) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        if(", "          round(moving_avg, 2) + 2 * round(moving_stddev, 2) > 100,", "          100,", "          round(moving_avg, 2) + 2 * round(moving_stddev, 2)", "        ),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRC01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRC02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          round(success_count * 100 / request_count, 2) as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 24 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN c = 0 THEN 'BRC02'", "            WHEN c < 30 THEN 'BRC01'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              success_total as success_count,", "              total as request_count", "            from", "              asset_ipsummary_hour t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfHour(now()) - INTERVAL 24 HOUR", "                and toStartOfHour(now())", "              )", "              AND total > 1000", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND (low != up)", "      AND (", "        notEmpty(app_name)", "        AND notEmpty(api_endpoint)", "      )", "    ORDER BY", "      t DESC", "  )"]}}, "name": "REP_基线异常记录(24小时窗口)", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "0 * * * *", "status": "enabled"}, "timestamp": "1727319927.65", "type": "cron_task"}, "175be358-6130-11f0-aca4-00505693a305": {"detail": {"statement": {"cmd_args": "", "cmd_name": "kafkaSender", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  first_api_sample_local(", "    timestamp,", "    request_uuid,", "    sample_source,", "    status,", "    api_snap_respheader,", "    api_snap_respbody,", "    api_snap_reqheader,", "    api_snap_reqbody,", "    pii_type,", "    pii_value,", "    api_risk_name,", "    api_risk_value,", "    api_risk_value_position,", "    bot_category,", "    bot_name,", "    api_id_sample,", "    api_industry_data_tag,", "    hostname,", "    path,", "    upstream_addr,", "    src_ip,", "    src_port,", "    is_https,", "    pii_position,", "    http_host,", "    business_username", "  ) -- 步骤6: 插入目标表（排除已存在记录）", "SELECT", "  timestamp,", "  request_uuid,", "  sample_source_value as sample_source,", "  status,", "  api_snap_respheader,", "  api_snap_respbody,", "  api_snap_reqheader,", "  api_snap_reqbody,", "  pii_type,", "  pii_value,", "  api_risk_name,", "  api_risk_value,", "  api_risk_value_position,", "  bot_category,", "  bot_name,", "  api_id_sample,", "  api_industry_data_tag,", "  hostname,", "  path,", "  upstream_addr,", "  src_ip,", "  src_port,", "  is_https,", "  pii_position,", "  http_host,", "  business_username", "FROM", "  (", "    SELECT", "      *,", "      arrayConcat(pii_type, api_risk_name, ['']) AS sample_source_array", "    FROM", "      api_sample", "    WHERE", "      timestamp >= ${hour_window_start_time}", "      AND timestamp < now()", "    ORDER BY", "      timestamp ASC", "  ) ARRAY", "  JOIN sample_source_array AS sample_source_value", "WHERE", "  (request_uuid) IN (", "    SELECT", "      DISTINCT request_uuid", "    FROM", "      (", "        SELECT", "          api_id_sample,", "          sample_source_value,", "          request_uuid,", "          row_number() OVER (", "            PARTITION BY api_id_sample,", "            sample_source_value", "            ORDER BY", "              timestamp", "          ) AS rn", "        FROM", "          (", "            SELECT", "              api_id_sample,", "              request_uuid,", "              timestamp,", "              arrayConcat(pii_type, api_risk_name, ['']) AS sample_source_array", "            FROM", "              api_sample", "            WHERE", "              timestamp >= ${hour_window_start_time}", "              AND timestamp < now()", "          ) ARRAY", "          JOIN sample_source_array AS sample_source_value", "      )", "    WHERE", "      rn = 1", "      AND (api_id_sample, sample_source_value) GLOBAL NOT IN (", "        SELECT", "          api_id_sample,", "          sample_source", "        FROM", "          first_api_sample", "      )", "  )", "  AND (api_id_sample, sample_source_value) GLOBAL NOT IN (", "    SELECT", "      api_id_sample,", "      sample_source", "    FROM", "      first_api_sample", "  )", "LIMIT", "  1 BY api_id_sample, sample_source"]}}, "name": "abd_样例首条采样", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "7 * * * *", "status": "enabled"}, "timestamp": "1755756837.7", "type": "cron_task"}, "1d2eb8fa-1bd7-11ee-83d3-0050569324de": {"detail": {"statement": {"cmd_args": "--table piiaccess_source_summary_day --fields 'timestamp,app_site_id,name,type,address,total,high,middle,low'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  name,", "  type,", "  address,", "  sum(total) as total,", "  sum(high) as high,", "  sum(middle) as middle,", "  sum(low) as low", "FROM", "  piiaccess_source_summary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  name,", "  type,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_敏感信息来源按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1687960721.48", "type": "cron_task"}, "23e409b8-d69d-11ed-a2f3-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_ipsummary_day --fields 'timestamp,app_site_id,api_id,src_ip,net_type,country,province,city,total,success_total,response_time_total,body_bytes_out_total'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  src_ip,", "  net_type,", "  country,", "  province,", "  city,", "  sum(total) as total,", "  sum(success_total) as success_total,", "  sum(response_time_total) as response_time_total,", "  sum(body_bytes_out_total) as body_bytes_out_total", "FROM", "  asset_ipsummary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  src_ip,", "  net_type,", "  country,", "  province,", "  city", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_资产访问来源按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1719372932.5", "type": "cron_task"}, "2429eb02-6054-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    key_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_user,", "    summary_dc_path,", "    summary_req_len,", "    summary_dc_fp,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    key", "  )", "SELECT", "  summary_count,", "  slot,", "  group_name,", "  duration_type,", "  kt,", "  date_workhour,", "  summary_avg_bytes_out,", "  summary_dc_account,", "  summary_dc_path,", "  summary_req_len,", "  summary_dc_fp,", "  summary_rsp_time,", "  summary_success,", "  app_site_id,", "  src_ip", "from", "  (", "    SELECT", "      COUNT(*) as summary_count,", "      toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "      'source' as group_name,", "      'hour' as duration_type,", "      1 as kt,", "      if(", "        toDayOfWeek(slot) < 6", "        AND toHour(slot) >= 8", "        AND toHour(slot) < 20,", "        1,", "        0", "      ) as date_workhour,", "      avg(body_bytes_out) as summary_avg_bytes_out,", "      COUNT(DISTINCT user) as summary_dc_account,", "      COUNT(DISTINCT path) as summary_dc_path,", "      avg(req_len) as summary_req_len,", "      COUNT(DISTINCT fp) as summary_dc_fp,", "      coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) as summary_success,", "      app_id as app_site_id,", "      src_ip", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "      and toStartOfHour(now()) - 1", "    group BY", "      src_ip,", "      app_site_id,", "      slot", "  ) as details", "  INNER JOIN (", "    SELECT", "      if(", "        count() > 1000,", "        arrayElement(arrayReverseSort(groupArray(summary_count)), 1000),", "        0", "      ) as count_threshold,", "      app_site_id,", "      slot", "    from", "      (", "        SELECT", "          COUNT(*) as summary_count,", "          toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "          'source' as group_name,", "          'hour' as duration_type,", "          1 as kt,", "          if(", "            toDayOfWeek(slot) < 6", "            AND toHour(slot) >= 8", "            AND toHour(slot) < 20,", "            1,", "            0", "          ) as date_workhour,", "          avg(body_bytes_out) as summary_avg_bytes_out,", "          COUNT(DISTINCT user) as summary_dc_account,", "          COUNT(DISTINCT path) as summary_dc_path,", "          avg(req_len) as summary_req_len,", "          COUNT(DISTINCT fp) as summary_dc_fp,", "          coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "          sum(", "            if(", "              status != 200", "              AND business_success != 1,", "              0,", "              1", "            )", "          ) as summary_success,", "          app_id as app_site_id,", "          src_ip", "        from", "          access_log", "        WHERE", "          is_api = 1", "          AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "          and toStartOfHour(now()) - 1", "        group BY", "          src_ip,", "          app_site_id,", "          slot", "      )", "    group BY", "      app_site_id,", "      slot", "  ) as total ON details.app_site_id = total.app_site_id", "  AND details.slot = total.slot", "WHERE", "  summary_count > count_threshold"]}}, "name": "api_summary_ip_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "disabled"}, "timestamp": "1688273624.63", "type": "cron_task"}, "25ad233a-6069-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    key_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_user,", "    summary_dc_path,", "    summary_req_len,", "    summary_dc_ip,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    key", "  )", "SELECT", "  summary_count,", "  slot,", "  group_name,", "  duration_type,", "  kt,", "  date_workhour,", "  summary_avg_bytes_out,", "  summary_dc_user,", "  summary_dc_path,", "  summary_req_len,", "  summary_dc_ip,", "  summary_rsp_time,", "  summary_success,", "  app_site_id,", "  fp", "from", "  (", "    SELECT", "      COUNT(*) as summary_count,", "      if(", "    timestamp < toStartOfDay(now() -86400) + 72000,", "    toStartOfDay(now() -86400) + 28800,", "    toStartOfDay(now() -86400) + 72000", "  ) as slot,", "      'source' as group_name,", "      'day' as duration_type,", "      2 as kt,", "      if(", "        toHour(timestamp) >= 8", "        AND toHour(timestamp) < 20,", "        1,", "        0", "      ) as date_workhour,", "      avg(body_bytes_out) as summary_avg_bytes_out,", "      COUNT(DISTINCT user) as summary_dc_user,", "      COUNT(DISTINCT path) as summary_dc_path,", "      avg(req_len) as summary_req_len,", "      COUNT(DISTINCT src_ip) as summary_dc_ip,", "      coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) as summary_success,", "      app_id as app_site_id,", "      fp", "    from", "      access_log", "    WHERE", "      is_api = 1 AND notEmpty(fp)", "      AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800", "  and toStartOfDay(now()) + 28799", "    group BY", "      fp,", "      app_site_id,", "      slot,", "      date_workhour", "  ) as details", "  INNER JOIN (", "    SELECT", "      if(", "        count() > 1000,", "        arrayElement(arrayReverseSort(groupArray(summary_count)), 1000),", "        0", "      ) as count_threshold,", "      app_site_id,", "      slot,", "      date_workhour", "    from", "      (", "        SELECT", "          COUNT(*) as summary_count,", "          if(", "    timestamp < toStartOfDay(now() -86400) + 72000,", "    toStartOfDay(now() -86400) + 28800,", "    toStartOfDay(now() -86400) + 72000", "  ) as slot,", "          'source' as group_name,", "          'day' as duration_type,", "          2 as kt,", "          if(", "            toHour(timestamp) >= 8", "            AND toHour(timestamp) < 20,", "            1,", "            0", "          ) as date_workhour,", "          avg(body_bytes_out) as summary_avg_bytes_out,", "          COUNT(DISTINCT user) as summary_dc_user,", "          COUNT(DISTINCT path) as summary_dc_path,", "          avg(req_len) as summary_req_len,", "          COUNT(DISTINCT src_ip) as summary_dc_ip,", "          coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "          sum(", "            if(", "              status != 200", "              AND business_success != 1,", "              0,", "              1", "            )", "          ) as summary_success,", "          app_id as app_site_id,", "          fp", "        from", "          access_log", "        WHERE", "          is_api = 1 AND notEmpty(fp)", "          AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800", "  and toStartOfDay(now()) + 28799", "        group BY", "          fp,", "          app_site_id,", "          slot,", "          date_workhour", "      )", "    group BY", "      app_site_id,", "      slot,", "      date_workhour", "  ) as total ON details.app_site_id = total.app_site_id", "  AND details.slot = total.slot AND details.date_workhour = total.date_workhour", "WHERE", "  summary_count > count_threshold"]}}, "name": "api_summary_fp_day", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 8 * * *", "status": "disabled"}, "timestamp": "1688273616.8", "type": "cron_task"}, "27cf6dfc-1599-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_ipsummary_hour --fields 'timestamp,app_site_id,api_id,src_ip,net_type,country,province,city,total,success_total,response_time_total,body_bytes_out_total'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id as app_site_id,", "  api_id,", "  src_ip,", "  dictGetString(", "    trans,", "    'target',", "    If(", "      is_local_ip,", "      '内网',", "      If(country IN ('中国', 'China'), '境内', '境外')", "    )", "  ) as type,", "  country,", "  province,", "  city,", "  count() as total,", "  countIf(status < 400) as success_total,", "  coalesce(sum(upstream_response_time), 0) as response_time_total,", "  coalesce(sum(body_bytes_out), 0) as body_bytes_out_total", "FROM", "  access_log", "WHERE", "  notEmpty(api_id)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "GROUP BY", "  app_site_id,", "  api_id,", "  src_ip,", "  type,", "  country,", "  province,", "  city", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_hour_资产访问来源按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "1726706803.6", "type": "cron_task"}, "3115ba2e-d4df-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table piisummary_day --fields 'timestamp,app_site_id,api_id,name,level,total_request,intranet_request,domestic_request,oversea_request,total_tag,request_pii_type_total,response_pii_type_total,positions,request_total,response_total,pii_value_sample,recent_detected_time,first_detected_time'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  name,", "  level,", "  sum(total_request) as total_request,", "  sum(intranet_request) as intranet_request,", "  sum(domestic_request) as domestic_request,", "  sum(oversea_request) as oversea_request,", "  sum(total_tag) as total_tag,", "  sum(request_pii_type_total) as request_pii_type_total,", "  sum(response_pii_type_total) as response_pii_type_total,", "  groupUniqArrayArray(positions) as positions,", "  sum(request_total) as request_total,", "  sum(response_total) as response_total,", "  any(pii_value_sample) as pii_value_sample,", "  max(recent_detected_time) as recent_detected_time,", "  min(first_detected_time) as first_detected_time", "FROM", "  piisummary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  name,", "  level", "ORDER BY", "  total_request DESC", "LIMIT", "  1000"]}}, "name": "abd_day_敏感信息按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1691743254.96", "type": "cron_task"}, "329d66e0-15ac-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_access_summary_day --fields 'timestamp,app_site_id,api_id,endpoint,method,type,total'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type,", "  sum(total) as total", "from", "  asset_access_summary_hour", "WHERE", "  type = 'access'", "  AND timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type,", "  sum(total) as total", "from", "  asset_access_summary_hour", "WHERE", "  type = 'fail'", "  AND timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type,", "  sum(total) as total", "from", "  asset_access_summary_hour", "WHERE", "  type = 'pii'", "  AND timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type,", "  sum(total) as total", "from", "  asset_access_summary_hour", "WHERE", "  type = 'defect'", "  AND timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  api_id,", "  endpoint,", "  method,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000"]}}, "name": "abd_day_资产访问趋势按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1692172098.51", "type": "cron_task"}, "33f82d3c-1597-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table piisummary_hour --fields 'timestamp,app_site_id,api_id,name,level,total_request,intranet_request,domestic_request,oversea_request,total_tag,request_pii_type_total,response_pii_type_total,positions,request_total,response_total,pii_value_sample,recent_detected_time,first_detected_time'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "      ${hour_window_start_time} as time,", "      app_id,", "      api_id,", "      tupleElement(pii_type_pos_level, 1) as pii_type,", "      tupleElement(pii_type_pos_level, 3) as pii_level,", "      uniq(request_uuid) as total_request,", "      uniqIf(request_uuid, is_local_ip) as intranet_request,", "      uniqIf(request_uuid, (is_local_ip=0 AND country IN ('中国', 'China'))) as domestic_request,", "      total_request - intranet_request - domestic_request as oversea_request,", "      count() as total_tag,", "      countIf(startsWith(tupleElement(pii_type_pos_level, 2), 'REQ') > 0) as request_pii_type_total,", "      countIf(startsWith(tupleElement(pii_type_pos_level, 2), 'RESP') > 0) as response_pii_type_total,", "      groupUniqArray(tupleElement(pii_type_pos_level, 2)) as positions,", "      uniqIf(request_uuid, startsWith(tupleElement(pii_type_pos_level, 2), 'REQ') > 0) as request_total,", "      uniqIf(request_uuid, startsWith(tupleElement(pii_type_pos_level, 2), 'RESP') > 0) as response_total,", "      arrayElement(", "        groupUniqArray(1)(tupleElement(pii_type_pos_level, 4)),", "        1", "      ) as pii_value_sample,", "      max(timestamp) as recent_detected_time,", "      min(timestamp) as first_detected_time", "FROM", "      (", "        SELECT", "          timestamp,", "          request_uuid,", "          app_id,", "          api_id,", "          is_local_ip,", "          country,", "          arrayZip(pii_type, pii_position, pii_level, pii_value) as pii_info_arr", "        FROM", "          access_log", "        WHERE", "          timestamp BETWEEN ${hour_window_start_time}", "          and ${hour_window_end_time}", "      ) ARRAY", "      JOIN pii_info_arr as pii_type_pos_level", "GROUP BY", "      app_id,", "      api_id,", "      pii_type,", "      pii_level", "ORDER BY", "      total_request DESC", "LIMIT", "      1000"]}}, "name": "abd_hour_敏感信息按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "1726714428.7", "type": "cron_task"}, "460cb1f2-159a-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_access_summary_hour --fields 'timestamp,app_site_id,api_id,endpoint,method,type,total'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  'access' as type,", "  count() as total", "from", "  access_log", "WHERE", "  (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "  AND notEmpty(api_id)", "GROUP BY", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  'fail' as type,", "  countIf(", "    (", "      status >= 400", "      AND status <= 599", "    )", "    OR (", "      business_name != ''", "      AND business_success = 0", "    )", "  ) as total", "from", "  access_log", "WHERE", "  (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "  AND notEmpty(api_id)", "GROUP BY", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  'pii' as type,", "  count() as total", "from", "  access_log", "WHERE", "  (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "  AND notEmpty(pii_type)", "GROUP BY", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  'defect' as type,", "  uniq(request_uuid) as total", "from", "  access_log ARRAY", "  JOIN api_risk_name", "WHERE", "  notEmpty(api_risk_name)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "GROUP BY", "  app_id,", "  api_id,", "  api_endpoint,", "  action,", "  type", "ORDER BY", "  total DESC", "LIMIT", "  10000"]}}, "name": "abd_hour_资产访问趋势按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "1692171738.95", "type": "cron_task"}, "4a3660e8-6043-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_user,", "    summary_dc_ip,", "    summary_req_len,", "    summary_dc_fp,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    hostname,", "    path,", "    action,", "    api_id", "  )", "SELECT", "  COUNT(*) as summary_count,", "  toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "  'path' as group_name,", "  'hour' as duration_type,", "  if(", "    toDayOfWeek(slot) < 6", "    AND toHour(slot) >= 8", "    AND toHour(slot) < 20,", "    1,", "    0", "  ) as date_workhour,", "  avg(body_bytes_out) as summary_avg_bytes_out,", "  COUNT(DISTINCT user) as summary_dc_account,", "  COUNT(DISTINCT src_ip) as summary_dc_ip,", "  avg(req_len) as summary_req_len,", "  COUNT(", "    DISTINCT coalesce(fingerprint_browser, mobile_device_fingerprint)", "  ) as summary_dc_fp,", "  coalesce(avg(upstream_response_time),0) as summary_rsp_time,", "  sum(", "    if(", "      status != 200", "      AND business_success != 1,", "      0,", "      1", "    )", "  ) as summary_success,", "  anyHeavy(app_id) as app_site_id,", "  hostname,", "  multiIf(", "        notEmpty(argMax(api_name, timestamp)),", "        argMax(api_name, timestamp),", "        notEmpty(argMax(api_endpoint, timestamp)),", "        argMax(api_endpoint, timestamp),", "        anyHeavy(path)", "      ) as path,", "  arrayStringConcat(groupUniqArray(action),',') as action,", "  api_id", "from", "  access_log", "WHERE", "  is_api = 1 AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour) and toStartOfHour(now()) - 1", "group BY", "  hostname,", "  api_id,", "  slot"]}}, "name": "api_summary_path_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "disabled"}, "timestamp": "1688273646.01", "type": "cron_task"}, "5302882a-6069-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    key_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_fp,", "    summary_dc_path,", "    summary_req_len,", "    summary_dc_ip,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    key", "  )", "SELECT", "  summary_count,", "  slot,", "  group_name,", "  duration_type,", "  kt,", "  date_workhour,", "  summary_avg_bytes_out,", "  summary_dc_fp,", "  summary_dc_path,", "  summary_req_len,", "  summary_dc_ip,", "  summary_rsp_time,", "  summary_success,", "  app_site_id,", "  user", "from", "  (", "    SELECT", "      COUNT(*) as summary_count,", "      if(", "        timestamp < toStartOfDay(now() -86400) + 72000,", "        toStartOfDay(now() -86400) + 28800,", "        toStartOfDay(now() -86400) + 72000", "      ) as slot,", "      'source' as group_name,", "      'day' as duration_type,", "      3 as kt,", "      if(", "        toHour(timestamp) >= 8", "        AND toHour(timestamp) < 20,", "        1,", "        0", "      ) as date_workhour,", "      avg(body_bytes_out) as summary_avg_bytes_out,", "      COUNT(DISTINCT fp) as summary_dc_fp,", "      COUNT(DISTINCT path) as summary_dc_path,", "      avg(req_len) as summary_req_len,", "      COUNT(DISTINCT src_ip) as summary_dc_ip,", "      coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) as summary_success,", "      app_id as app_site_id,", "      user", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND notEmpty(user)", "      AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800", "      and toStartOfDay(now()) + 28799", "    group BY", "      user,", "      app_site_id,", "      slot,", "      date_workhour", "  ) as details", "  INNER JOIN (", "    SELECT", "      if(", "        count() > 1000,", "        arrayElement(", "          arrayReverseSort(groupArray(summary_count)),", "          1000", "        ),", "        0", "      ) as count_threshold,", "      app_site_id,", "      slot,", "      date_workhour", "    from", "      (", "        SELECT", "          COUNT(*) as summary_count,", "          if(", "            timestamp < toStartOfDay(now() -86400) + 72000,", "            toStartOfDay(now() -86400) + 28800,", "            toStartOfDay(now() -86400) + 72000", "          ) as slot,", "          'source' as group_name,", "          'day' as duration_type,", "          3 as kt,", "          if(", "            toHour(timestamp) >= 8", "            AND toHour(timestamp) < 20,", "            1,", "            0", "          ) as date_workhour,", "          avg(body_bytes_out) as summary_avg_bytes_out,", "          COUNT(DISTINCT fp) as summary_dc_fp,", "          COUNT(DISTINCT path) as summary_dc_path,", "          avg(req_len) as summary_req_len,", "          COUNT(DISTINCT src_ip) as summary_dc_ip,", "          coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "          sum(", "            if(", "              status != 200", "              AND business_success != 1,", "              0,", "              1", "            )", "          ) as summary_success,", "          app_id as app_site_id,", "          user", "        from", "          access_log", "        WHERE", "          is_api = 1", "          AND notEmpty(user)", "          AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800", "          and toStartOfDay(now()) + 28799", "        group BY", "          user,", "          app_site_id,", "          slot,", "          date_workhour", "      )", "    group BY", "      app_site_id,", "      slot,", "      date_workhour", "  ) as total ON details.app_site_id = total.app_site_id", "  AND details.slot = total.slot", "  AND details.date_workhour = total.date_workhour", "WHERE", "  summary_count > count_threshold"]}}, "name": "api_summary_user_day", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 8 * * *", "status": "disabled"}, "timestamp": "1688273613.58", "type": "cron_task"}, "5c371a14-d4de-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table asset_list_summary_day --fields 'timestamp,api_id,api_type,auth_scheme_id,risks,pii_infos,business_types,api_auto_tag_id,defect_name,business_attack_count,hole_attack_count,attack_count,bot_count,internet_count,intranet_count,domestic_count,oversea_count,access_count'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  api_id,", "  if(notEmpty(anyHeavyIf(api_type, api_type != '' and api_type != 'restful')),", "     anyHeavyIf(api_type, api_type != '' and api_type != 'restful'),", "     anyHeavyIf(api_type, api_type != '')) as api_type,", "  anyHeavy(auth_scheme_id) as auth_scheme_id,", "  groupUniqArrayArray(risks),", "  groupUniqArrayArray(pii_infos),", "  groupUniqArrayArray(business_types),", "  anyHeavyIf(api_auto_tag_id, api_auto_tag_id !=0),", "  groupUniqArrayArray(defect_name),", "  sum(business_attack_count) as business_attack_count,", "  sum(hole_attack_count) as hole_attack_count,", "  sum(attack_count) as attack_count,", "  sum(bot_count) as bot_count,", "  sum(internet_count) as internet_count,", "  sum(intranet_count) as intranet_count,", "  sum(domestic_count) as domestic_count,", "  sum(oversea_count) as oversea_count,", "  sum(access_count) as access_count", "FROM", "  asset_list_summary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  api_id", "LIMIT", "  10000"]}}, "name": "abd_day_资产清单按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1689968689.23", "type": "cron_task"}, "5c396868-d4df-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table defect_name_summary_day --fields 'timestamp,endpoint,api_id,app_site_id,name,level,total,recent_detected_time,first_detected_time,source'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  endpoint,", "  api_id,", "  app_site_id,", "  name,", "  level,", "  sum(total) as total,", "  max(recent_detected_time) as recent_detected_time,", "  min(first_detected_time) as first_detected_time,", "  source", "FROM", "  defect_name_summary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  endpoint,", "  api_id,", "  app_site_id,", "  name,", "  level,", "  source", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_缺陷按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1688026777.85", "type": "cron_task"}, "65afb396-6067-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    key_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_fp,", "    summary_dc_path,", "    summary_req_len,", "    summary_dc_ip,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    key", "  )", "SELECT", "  summary_count,", "  slot,", "  group_name,", "  duration_type,", "  kt,", "  date_workhour,", "  summary_avg_bytes_out,", "  summary_dc_fp,", "  summary_dc_path,", "  summary_req_len,", "  summary_dc_ip,", "  summary_rsp_time,", "  summary_success,", "  app_site_id,", "  user", "from", "  (", "    SELECT", "      COUNT(*) as summary_count,", "      toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "      'source' as group_name,", "      'hour' as duration_type,", "      3 as kt,", "      if(", "        toDayOfWeek(slot) < 6", "        AND toHour(slot) >= 8", "        AND toHour(slot) < 20,", "        1,", "        0", "      ) as date_workhour,", "      avg(body_bytes_out) as summary_avg_bytes_out,", "      COUNT(DISTINCT fp) as summary_dc_fp,", "      COUNT(DISTINCT path) as summary_dc_path,", "      avg(req_len) as summary_req_len,", "      COUNT(DISTINCT src_ip) as summary_dc_ip,", "      coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) as summary_success,", "      app_id as app_site_id,", "      user", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND notEmpty(user)", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "      and toStartOfHour(now()) - 1", "    group BY", "      user,", "      app_site_id,", "      slot", "  ) as details", "  INNER JOIN (", "    SELECT", "      if(", "        count() > 1000,", "        arrayElement(", "          arrayReverseSort(groupArray(summary_count)),", "          1000", "        ),", "        0", "      ) as count_threshold,", "      app_site_id,", "      slot", "    from", "      (", "        SELECT", "          COUNT(*) as summary_count,", "          toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "          'source' as group_name,", "          'hour' as duration_type,", "          3 as kt,", "          if(", "            toDayOfWeek(slot) < 6", "            AND toHour(slot) >= 8", "            AND toHour(slot) < 20,", "            1,", "            0", "          ) as date_workhour,", "          avg(body_bytes_out) as summary_avg_bytes_out,", "          COUNT(DISTINCT fp) as summary_dc_fp,", "          COUNT(DISTINCT path) as summary_dc_path,", "          avg(req_len) as summary_req_len,", "          COUNT(DISTINCT src_ip) as summary_dc_ip,", "          coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "          sum(", "            if(", "              status != 200", "              AND business_success != 1,", "              0,", "              1", "            )", "          ) as summary_success,", "          app_id as app_site_id,", "          user", "        from", "          access_log", "        WHERE", "          is_api = 1", "          AND notEmpty(user)", "          AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "          and toStartOfHour(now()) - 1", "        group BY", "          user,", "          app_site_id,", "          slot", "      )", "    group BY", "      app_site_id,", "      slot", "  ) as total ON details.app_site_id = total.app_site_id", "  AND details.slot = total.slot", "WHERE", "  summary_count > count_threshold"]}}, "name": "api_summary_user_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "disabled"}, "timestamp": "1688273610.53", "type": "cron_task"}, "6bfa748a-6454-11ef-bf64-309c23993eda": {"detail": {"statement": {"cmd_args": "", "cmd_name": "kafkaSender", "cmd_switch": false, "sql_statement": ["insert into", "  api_baseline_risks_local (", "    timestamp,", "    src_ip,", "    app_name,", "    api_endpoint,", "    count_summary,", "    low,", "    up,", "    anomaly_tag,", "    type_score,", "    windows_type", "  )", "SELECT", "  t,", "  src_ip,", "  app_name,", "  api_endpoint,", "  array(tag_summary) as count_summary,", "  low,", "  up,", "  array(anomaly_status) as anomaly_tag,", "  type_score,", "  '2' as windows_type", "from", "  (", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(round(c, 2)) as tag_summary,", "      if(", "        round(moving_avg, 2) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        round(moving_avg, 2) + 3 * round(moving_stddev, 2),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRD01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRD02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          body_bytes_out_count /(1024 * 1024) as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN moving_stddev != 0", "            AND c > round(", "              round(moving_avg, 2) + 3 * round(moving_stddev, 2),", "              2", "            ) THEN 'BRD01'", "            WHEN body_bytes_out_count = 0 THEN 'BRD02'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              total,", "              body_bytes_out_total as body_bytes_out_count", "            from", "              asset_ipsummary_day t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfDay(now()) - INTERVAL 7 Day", "                and toStartOfDay(now())", "              )", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND (notEmpty(app_name))", "      AND (low != up)", "    ORDER BY", "      t DESC", "    union ALL", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(c) as tag_summary,", "      if(", "        round(moving_avg) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        round(moving_avg) + 3 * round(moving_stdd<PERSON>, 2),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRA01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRA02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          request_count as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN moving_stddev != 0", "            AND c > round(moving_avg) + 3 * round(moving_stddev) THEN 'BRA01'", "            WHEN moving_stddev = 0", "            AND request_count = 0 THEN 'BRA02'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              total as request_count,", "              total", "            from", "              asset_ipsummary_day t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfDay(now()) - INTERVAL 7 Day", "                and toStartOfDay(now())", "              )", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND c > 100", "      AND (", "        notEmpty(app_name)", "        AND notEmpty(api_endpoint)", "      )", "    ORDER BY", "      t DESC", "    UNION ALL", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(round(c, 2)) as tag_summary,", "      if(", "        round(moving_avg, 2) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        round(moving_avg, 2) + 3 * round(moving_stddev, 2),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRB01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRB02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRB03',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          response_time_count as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN c > moving_avg + 3 * moving_stddev THEN 'BRB01'", "            WHEN c != 0", "            AND c > total * 60 THEN 'BRB02'", "            WHEN response_time_count = 0 THEN 'BRB03'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              response_time_total as response_time_count,", "              total", "            from", "              asset_ipsummary_day t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfDay(now()) - INTERVAL 7 Day", "                and toStartOfDay(now())", "              )", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND (", "        notEmpty(app_name)", "        AND notEmpty(api_endpoint)", "      )", "    ORDER BY", "      tag_summary DESC", "    UNION ALL", "    SELECT", "      timestamp as t,", "      src_ip,", "      app_name,", "      api_endpoint,", "      toString(c) as tag_summary,", "      if(", "        round(moving_avg, 2) - 2 * round(moving_stddev, 2) as low_v < 0,", "        0,", "        low_v", "      ) as low,", "      round(", "        if(", "          round(moving_avg, 2) + 2 * round(moving_stddev, 2) > 100,", "          100,", "          round(moving_avg, 2) + 2 * round(moving_stddev, 2)", "        ),", "        2", "      ) as up,", "      anomaly_status,", "      multiIf(", "        anomaly_status = 'BRC01',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        anomaly_status = 'BRC02',", "        dictGetString('baseline_risks', 'score', tuple(anomaly_status)),", "        '0'", "      ) as type_score", "    from", "      (", "        SELECT", "          timestamp,", "          src_ip,", "          app_name,", "          api_endpoint,", "          round(success_count * 100 / request_count, 2) as c,", "          AVG(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) AS moving_avg,", "          STDDEV_POP(c) OVER (", "            PARTITION BY src_ip,", "            app_name,", "            api_endpoint", "            ORDER BY", "              timestamp ROWS BETWEEN 7 PRECEDING", "              AND CURRENT ROW", "          ) as moving_stddev,", "          CASE", "            WHEN c = 0 THEN 'BRC02'", "            WHEN c < 30 THEN 'BRC01'", "            ELSE '0'", "          END AS anomaly_status", "        FROM", "          (", "            SELECT", "              timestamp,", "              src_ip,", "              app_name,", "              api_endpoint,", "              success_total as success_count,", "              total as request_count", "            from", "              asset_ipsummary_day t3 any", "              left join (", "                SELECT", "                  app_name,", "                  api_endpoint,", "                  api_id", "                from", "                  (", "                    SELECT", "                      id as api_id,", "                      app_site_id,", "                      endpoint as api_endpoint", "                    from", "                      api_info", "                    group by", "                      id,", "                      endpoint,", "                      app_site_id", "                  ) t1 any", "                  left join (", "                    SELECT", "                      id as app_site_id,", "                      name as app_name", "                    from", "                      app_site", "                    group by", "                      id,", "                      name", "                  ) t2 ON t1.app_site_id = t2.app_site_id", "              ) t4 ON t3.api_id = t4.api_id", "            WHERE", "              (", "                timestamp BETWEEN toStartOfDay(now()) - INTERVAL 7 Day", "                and toStartOfDay(now())", "              )", "              AND total > 1000", "          )", "      )", "    WHERE", "      anomaly_status != '0'", "      AND moving_stddev != 0", "      AND (low != up)", "      AND (", "        notEmpty(app_name)", "        AND notEmpty(api_endpoint)", "      )", "    ORDER BY", "      t DESC", "  )"]}}, "name": "REP_基线异常记录(7天窗口)", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "0 0 * * *", "status": "enabled"}, "timestamp": "1727320245.76", "type": "cron_task"}, "75bbdf36-5630-11ef-8ac2-00505693a305": {"detail": {"statement": {"attachment": {"app": "api", "dashboard": "a181670c-5924-11ef-8ac2-00505693a305", "dashboardFormat": "docx", "filter": "", "format": "pdf", "timeFrame": ["-1M/M", "/M"], "timeout": 3600}, "email_body": "（可选）ABD防护平台收益内容：\n\n1.实现API资产管理：利用API交互特征智能过滤静态资源，识别出需要管控的API资产，实现API资产的安全管控，为后续所有的API安全策略实施提供API资产管理列表。\n\n2.识别API缺陷：ABD可以识别API中由于设计不足导致的缺陷，例如未经身份验证的接口、不安全的密码传输等，这些缺陷可能会被攻击者利用，造成数据泄露、业务中断等危害。\n\n3.监测API攻击事件：ABD可以通过监测API的访问行为，识别出异常访问和恶意攻击。这可以帮助企业及时发现攻击者利用漏洞对企业应用进行攻击，使得企业的业务更安全。\n\n4.规范API访问（反代部署）：ABD可以规范API的访问，只有经过身份验证和授权的用户才能访问API，从而保护企业的敏感数据和访问安全合规。\n\n5.监测常见Web攻击：ABD可以监测常见的攻击事件，如SQL注入、跨站脚本攻击等常见攻击。通过对流量中的特征检测，可以有效提升企业的数据安全和网络安全。\n\n6.实践API安全管控（反代部署）：通过对ABD系统化的操作实践过程，帮助相关人员更深入API的安全性问题，助力相关人员全面形成API安全管控系统性思维，从而提升企业API的安全性，减少风险造成的损失。 \n\n7.填补API安全空白：多数企业缺乏专业API安全治理设备，API BD可填补企业API安全空白，弥补企业业务安全漏洞，助力企业网络安全开放，合规使用。", "email_subject": "", "emial_body": "", "receiver": "", "send_by_email": false}}, "name": "API资产风险月报", "summary": {"cron_type": "periodic_report", "freq_expr_type": "normal", "frequency": "15 0 1 * *", "status": "enabled"}, "timestamp": "1723195160.54", "type": "cron_task"}, "75ecfe90-1581-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table piiindustry_summary_hour --fields 'timestamp,app_site_id,api_ids,tag,total'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id as app_site_id,", "  groupUniqArray(api_id) as api_ids,", "  tag,", "  count() as total", "from", "  access_log ARRAY", "  JOIN api_industry_data_tag as tag", "WHERE", "  notEmpty(api_industry_data_tag)", "  AND timestamp BETWEEN ${hour_window_start_time}", "  and ${hour_window_end_time}", "GROUP BY", "  timestamp,", "  app_site_id,", "  tag", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_hour_行业分级按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "disabled"}, "timestamp": "1687935626.85", "type": "cron_task"}, "848d00e4-6066-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    key_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_user,", "    summary_dc_path,", "    summary_req_len,", "    summary_dc_ip,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    key", "  )", "SELECT", "  summary_count,", "  slot,", "  group_name,", "  duration_type,", "  kt,", "  date_workhour,", "  summary_avg_bytes_out,", "  summary_dc_account,", "  summary_dc_path,", "  summary_req_len,", "  summary_dc_ip,", "  summary_rsp_time,", "  summary_success,", "  app_site_id,", "  fp", "from", "  (", "    SELECT", "      COUNT(*) as summary_count,", "      toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "      'source' as group_name,", "      'hour' as duration_type,", "      2 as kt,", "      if(", "        toDayOfWeek(slot) < 6", "        AND toHour(slot) >= 8", "        AND toHour(slot) < 20,", "        1,", "        0", "      ) as date_workhour,", "      avg(body_bytes_out) as summary_avg_bytes_out,", "      COUNT(DISTINCT user) as summary_dc_account,", "      COUNT(DISTINCT path) as summary_dc_path,", "      avg(req_len) as summary_req_len,", "      COUNT(DISTINCT src_ip) as summary_dc_ip,", "      coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) as summary_success,", "      app_id as app_site_id,", "      fp", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND notEmpty(fp)", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "      and toStartOfHour(now()) - 1", "    group BY", "      fp,", "      app_site_id,", "      slot", "  ) as details", "  INNER JOIN (", "    SELECT", "      if(", "        count() > 1000,", "        arrayElement(", "          arrayReverseSort(groupArray(summary_count)),", "          1000", "        ),", "        0", "      ) as count_threshold,", "      app_site_id,", "      slot", "    from", "      (", "        SELECT", "          COUNT(*) as summary_count,", "          toStartOfInterval(timestamp, INTERVAL 1 hour) as slot,", "          'source' as group_name,", "          'hour' as duration_type,", "          2 as kt,", "          if(", "            toDayOfWeek(slot) < 6", "            AND toHour(slot) >= 8", "            AND toHour(slot) < 20,", "            1,", "            0", "          ) as date_workhour,", "          avg(body_bytes_out) as summary_avg_bytes_out,", "          COUNT(DISTINCT user) as summary_dc_account,", "          COUNT(DISTINCT path) as summary_dc_path,", "          avg(req_len) as summary_req_len,", "          COUNT(DISTINCT src_ip) as summary_dc_ip,", "          coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "          sum(", "            if(", "              status != 200", "              AND business_success != 1,", "              0,", "              1", "            )", "          ) as summary_success,", "          app_id as app_site_id,", "          fp", "        from", "          access_log", "        WHERE", "          is_api = 1", "          AND notEmpty(fp)", "          AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "          and toStartOfHour(now()) - 1", "        group BY", "          fp,", "          app_site_id,", "          slot", "      )", "    group BY", "      app_site_id,", "      slot", "  ) as total ON details.app_site_id = total.app_site_id", "  AND details.slot = total.slot", "WHERE", "  summary_count > count_threshold"]}}, "name": "api_summary_fp_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "disabled"}, "timestamp": "1688273619.78", "type": "cron_task"}, "a4fd5510-d5c9-11ed-b810-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table attacked_asset_summary_day_local --fields 'timestamp,related_app_id,related_api_id,risk_level,event_category,intranet,domestic,total,oversea'\n", "cmd_name": "updateSailfishSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time},", "  related_app_id,", "  related_api_id,", "  risk_level,", "  event_category,", "  countIf(", "    tupleElement(related_ip_loc_tuple2 [1], 5)", "  ) as intranet_count,", "  countIf(", "    tupleElement(related_ip_loc_tuple2 [1], 5) = 0", "    and tupleElement(related_ip_loc_tuple2 [1], 2) = '中国'", "  ) as domestic_count,", "  count() as total,", "  total - intranet_count - domestic_count as oversea_count", "FROM", "  api_attack_event FINAL", "PREWHERE", "  (", "    timestamp BETWEEN ${day_window_start_time}", "    and ${day_window_end_time}", "  )", "GROUP BY", "  related_app_id,", "  related_api_id,", "  risk_level,", "  event_category", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_被攻击应用资产按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1690472748.44", "type": "cron_task"}, "a7862c74-6133-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  threat_event_api_local (", "    timestamp,", "    group_name,", "    app_site_id,", "    event_ct_rt,", "    event_bytes_out_rt,", "    event_rsp_time_rt,", "    event_dc_ip_rt,", "    event_sr_rt,", "    reason,", "    score,", "    event_ct,", "    event_dc_ip,", "    event_bytes_out,", "    event_sr,", "    event_rsp_time,", "    event_inj,", "    event_invalid_args_ct,", "    event_sensitive_info_ct,", "    path,", "    hostname,", "    action,", "    api_id", "  )", "SELECT", "  slot,", "  'path' as group_name,", "  app_site_id,", "  (count - summary_avg_count) / arrayReduce(", "    'max',", "    [summary_std_count,summary_avg_count*toFloat64(dictGetString('default.api_parameters','count_std_threshold',tuple('path'))),0.01]", "  ) as event_ct_rt,", "  (avg_bytes_out - summary_avg_avg_bytes_out) / arrayReduce(", "    'max',", "    [summary_std_avg_bytes_out,summary_avg_avg_bytes_out*toFloat64(dictGetString('default.api_parameters','bytes_out_std_threshold',tuple('path'))),0.01]", "  ) as event_bytes_out_rt,", "  (rsp_time - summary_avg_rsp_time) / arrayReduce(", "    'max',", "    [summary_std_rsp_time,summary_avg_rsp_time*toFloat64(dictGetString('default.api_parameters','rsp_time_std_threshold',tuple('path'))),0.01]", "  ) as event_rsp_rt,", "  (dc_ip - summary_avg_dc_ip) / arrayReduce(", "    'max',", "    [summary_std_dc_ip,summary_avg_dc_ip*toFloat64(dictGetString('default.api_parameters','dc_ip_std_threshold',tuple('path'))),0.01]", "  ) as event_dc_ip_rt,", "  (success_rate - summary_avg_success_rate) / arrayReduce(", "    'max',", "    [summary_std_success_rate,summary_avg_success_rate*toFloat64(dictGetString('default.api_parameters','success_rate_std_threshold',tuple('path'))),0.01]", "  ) as event_sr_rt,", "  trim(", "    LEADING ','", "    FROM", "      concat(", "        if(", "          summary_data_volume > 2,", "          concat(", "            multiIf(", "              event_ct_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'count_range_parameter',", "                  tuple('path')", "                )", "              ),", "              'path_count_high',", "              -1 * event_ct_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'count_range_parameter',", "                  tuple('path')", "                )", "              ),", "              'path_count_low',", "              ''", "            ),", "            multiIf(", "              event_bytes_out_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'bytes_out_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_avg_bytes_out_high',", "              -1 * event_bytes_out_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'bytes_out_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_avg_bytes_out_low',", "              ''", "            ),", "            multiIf(", "              event_rsp_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'rsp_time_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_rsp_time_high',", "              -1 * event_rsp_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'rsp_time_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_rsp_time_low',", "              ''", "            ),", "            multiIf(", "              event_dc_ip_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_ip_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_dc_ip_high',", "              -1 * event_dc_ip_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'dc_ip_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_dc_ip_low',", "              ''", "            ),", "            multiIf(", "              event_sr_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'success_rate_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_success_rate_high',", "              -1 * event_sr_rt > toFloat64(", "                dictGetString(", "                  'default.api_parameters',", "                  'success_rate_range_parameter',", "                  tuple('path')", "                )", "              ),", "              ',path_success_rate_low',", "              ''", "            )", "          ),", "          ''", "        ),", "        if(inj > 0, ',Inject', ''),", "        if(invalid_args_ct > 0, ',invalid_args_req', ''),", "        if(sensitive_info_ct > 0.8*count, ',sensitive_info_req', ''),", "        if(dc_pii_type > 2, ',too_many_pii_types', ''),", "        if(pii_values > 50, ',too_many_pii_values', '')", "      )", "  ) as reason,", "  arrayReduce(", "    'min',", "    [100, round(if(summary_data_volume >2,arrayReduce(", "    'max',", "    [abs(event_ct_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'count_range_parameter',", "              tuple('path')", "            )", "          )-1,0]", "  ) * if(event_ct_rt > 0, 5, 1) + arrayReduce(", "    'max',", "    [abs(event_bytes_out_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'bytes_out_range_parameter',", "              tuple('path')", "            )", "          )-1,0]", "  ) * if(event_bytes_out_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_rsp_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'rsp_time_range_parameter',", "              tuple('path')", "            )", "          )-1,0]", "  ) * if(event_rsp_rt > 0, 10, 0.1) + arrayReduce(", "    'max',", "    [abs(event_dc_ip_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_ip_range_parameter',", "              tuple('path')", "            )", "          )-1,0]", "  ) * if(event_dc_ip_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_sr_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'success_rate_range_parameter',", "              tuple('path')", "            )", "          )-1,0]", "  ) * if(event_sr_rt > 0, 1, 15),", "  0", ") + sqrt(inj) * 10 + sqrt(invalid_args_ct) * 5 + if(sensitive_info_ct > 0.8*count, 30, 0)+ if(dc_pii_type > 2, 5 * dc_pii_type - 10, 0) + if(pii_values > 50, sqrt(pii_values), 0),", "2", ") ]", ") as score,", "count,", "dc_ip,", "avg_bytes_out,", "success_rate,", "rsp_time,", "inj,", "invalid_args_ct,", "sensitive_info_ct,", "path,", "hostname,", "action,", "api_id", "FROM", "  (", "    SELECT", "      COUNT(*) as count,", "      anyHeavy(app_id) as app_site_id,", "      avg(body_bytes_out) as avg_bytes_out,", "      COUNT(DISTINCT src_ip) as dc_ip,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) / count as success_rate,", "      coalesce(avg(upstream_response_time), 0) as rsp_time,", "      countIf(has(attack_type, 'INVALID_API_ARGS')) as invalid_args_ct,", "      countIf(notEmpty(pii_type)) as sensitive_info_ct,", "      hostname,", "      argMax(", "        if(notEmpty(api_endpoint), api_endpoint, path),", "        timestamp", "      ) as path,", "      arrayStringConcat(groupUniqArray(action), ',') as action,", "      api_id,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday,", "      toStartOfHour(timestamp) as slot,", "      sum(", "        if(hasAny(attack_type, ['Inject', 'AIWAF']), 1, 0)", "      ) as inj,", "      uniq(pii_type) as dc_pii_type,", "      uniq(arrayJoin(pii_value)) as pii_values", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 Hour)", "      and toStartOfHour(now()) - 1", "    GROUP BY", "      hostname,", "      api_id,", "      date_hour,", "      date_wday,", "      slot", "  ) as now_data LEFT ALL", "  JOIN (", "    SELECT", "      hostname,", "      api_id,", "      COUNT(*) as summary_data_volume,", "      avg(summary_agg_count) as summary_avg_count,", "      STDDEV_POP(summary_agg_count) as summary_std_count,", "      avg(summary_avg_bytes_out) as summary_avg_avg_bytes_out,", "      STDDEV_POP(summary_avg_bytes_out) as summary_std_avg_bytes_out,", "      avg(summary_dc_ip) as summary_avg_dc_ip,", "      STDDEV_POP(summary_dc_ip) as summary_std_dc_ip,", "      avg(summary_success / summary_agg_count) as summary_avg_success_rate,", "      STDDEV_POP(summary_success / summary_agg_count) as summary_std_success_rate,", "      if(", "        isFinite(avgIf(summary_rsp_time, summary_rsp_time > 0)),", "        avgIf(summary_rsp_time, summary_rsp_time > 0),", "        1000", "      ) as summary_avg_rsp_time,", "      if(", "        isFinite(", "          STDDEV_POPIf(summary_rsp_time, summary_rsp_time > 0)", "        ),", "        STDDEV_POPIf(summary_rsp_time, summary_rsp_time > 0),", "        10000", "      ) as summary_std_rsp_time,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday", "    from", "      (", "        SELECT", "          hostname,", "          api_id,", "          sum(summary_count) as summary_agg_count,", "          sum(summary_avg_bytes_out * summary_count) / sum(summary_count) as summary_avg_bytes_out,", "          max(summary_dc_ip) as summary_dc_ip,", "          sum(summary_success) as summary_success,", "          toStartOfHour(timestamp) as timestamp,", "          sum(summary_rsp_time * summary_count) / sum(summary_count) as summary_rsp_time", "        from", "          summary_data_api", "        WHERE", "          group_name = 'path'", "          AND timestamp >= toStartOfDay(now() - 14 * 86400)", "        group BY", "          hostname,", "          api_id,", "          timestamp", "      )", "    GROUP BY", "      hostname,", "      api_id,", "      date_hour,", "      date_wday", "  ) as baseline_data ON now_data.hostname = baseline_data.hostname", "  AND now_data.api_id = baseline_data.api_id", "  AND now_data.date_hour = baseline_data.date_hour", "  AND now_data.date_wday = baseline_data.date_wday", "HAVING", "  score >= 20"]}}, "name": "api_threat_path_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "8 * * * *", "status": "disabled"}, "timestamp": "1688273633.19", "type": "cron_task"}, "a7ea4e9e-d606-11ed-81bd-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table bot_summary_day --fields 'timestamp, app_site_id, category, type, total'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  category,", "  type,", "  sum(total) as total", "from", "  bot_summary_hour", "WHERE", "  timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "group by", "  app_site_id,", "  category,", "  type", "ORDER BY", "  total", "LIMIT", "  1000"]}}, "name": "abd_day_BOT访问按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1687956825.22", "type": "cron_task"}, "af825e14-4125-11ee-b7f4-005056937edb": {"detail": {"statement": {"cmd_args": "--delete_all --table asset_attack_summary_cache --fields 'api_id,attack_count,business_attack_count,hole_attack_count'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  api_id,", "  count() as attack_count,", "  countIf(", "    dictGetString(api_attack_type, 'business_type', event_type) == dictGetString(trans, 'target', '业务行为攻击')", "  ) as business_attack_count,", "  attack_count - business_attack_count as hole_attack_count", "from", "  api_attack_event FINAL ARRAY", "  JOIN related_api_id as api_id", "PREWHERE timestamp > ${hour_window_end_time} - 86400 * 7", "group by", "  api_id", "order by", "  attack_count DESC", "LIMIT 100000"]}}, "name": "abd_cache_资产攻击缓存", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "1692734479.49", "type": "cron_task"}, "b0ac8020-d4de-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table defect_access_source_summary_day --fields 'timestamp,app_site_id,name,type,address,total,high,middle,low'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  name,", "  type,", "  address,", "  sum(total) as total,", "  sum(high) as high,", "  sum(middle) as middle,", "  sum(low) as low", "FROM", "  defect_access_source_summary_hour", "WHERE", "  type = 'ip'", "  AND timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  name,", "  type,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000", "UNION ALL", "SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  name,", "  type,", "  address,", "  sum(total) as total,", "  sum(high) as high,", "  sum(middle) as middle,", "  sum(low) as low", "FROM", "  defect_access_source_summary_hour", "WHERE", "  type = 'account'", "  AND timestamp >= ${day_window_start_time}", "  AND timestamp < ${day_window_end_time}", "GROUP BY", "  app_site_id,", "  name,", "  type,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_缺陷访问来源按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "**********.34", "type": "cron_task"}, "b2eb138e-0903-11ee-bf07-005056930bdc": {"detail": {"statement": {"cmd_args": "--table piiaccess_source_summary_hour --fields 'timestamp,app_site_id,name,type,address,total,high,middle,low'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  src_ip as name,", "  if(", "    is_local_ip,", "    'intranet',", "    if(country IN ('中国', 'China'), 'domestic', 'overseas')", "  ) as type,", "  if(", "    is_local_ip,", "    if(getMacro('language') = 'zh', '内网', 'Private Network'),", "    country || '-' || province || '-' || city", "  ) as address,", "  count() as total,", "  countIf(has(pii_level, '高') OR has(pii_level, 'high')) as high,", "  countIf(has(pii_level, '中') OR has(pii_level, 'middle')) as middle,", "  countIf(has(pii_level, '低') OR has(pii_level, 'low')) as low", "FROM", "  access_log", "WHERE", "  notEmpty(pii_type)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "GROUP BY", "  app_id,", "  name,", "  type,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000", "UNION ALL", "SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  if(empty(business_submit_username), business_username, business_submit_username) as name,", "  'account' as type,", "  if(", "    is_local_ip,", "    if(getMacro('language') = 'zh', '内网', 'Private Network'),", "    country || '-' || province || '-' || city", "  ) as address,", "  count() as total,", "  countIf(has(pii_level, '高') OR has(pii_level, 'high')) as high,", "  countIf(has(pii_level, '中') OR has(pii_level, 'middle')) as middle,", "  countIf(has(pii_level, '低') OR has(pii_level, 'low')) as low", "FROM", "  access_log", "WHERE", "  notEmpty(pii_type)", "  AND notEmpty(name)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "GROUP BY", "  app_id,", "  name,", "  type,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_hour_敏感信息来源按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "**********.66", "type": "cron_task"}, "b32a5d52-157c-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--refresh_asset_cache --table asset_list_summary_hour --fields 'timestamp,api_id,api_type,auth_scheme_id,risks,pii_infos,business_types,api_auto_tag_id,defect_name,business_attack_count,hole_attack_count,attack_count,bot_count,internet_count,intranet_count,domestic_count,oversea_count,access_count'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  api_id,", "  api_type,", "  auth_scheme_id,", "  risks,", "  pii_info,", "  business_types,", "  api_auto_tag_id,", "  defect_name,", "  business_attack_count,", "  hole_attack_count,", "  attack_count,", "  bot_count,", "  internet_count,", "  intranet_count,", "  domestic_count,", "  oversea_count,", "  access_count", "FROM", "  (", "    SELECT", "      api_id,", "      api_type,", "      auth_scheme_id,", "      pii_info,", "      business_types,", "      api_auto_tag_id,", "      business_attack_count,", "      hole_attack_count,", "      attack_count,", "      bot_count,", "      intranet_count,", "      internet_count,", "      domestic_count,", "      oversea_count,", "      access_count", "    FROM", "      (", "        SELECT", "          api_id,", "          countIf(isNotNull(is_good_bot)) as bot_count,", "          count() as access_count,", "          countIf(is_local_ip) as intranet_count,", "          minus(access_count, intranet_count) as internet_count,", "          countIf(", "            is_local_ip = 0", "            and country IN ('中国', 'China')", "          ) as domestic_count,", "          minus(internet_count, domestic_count) as oversea_count", "        from", "          access_log", "        WHERE", "          notEmpty(api_id)", "          AND (", "            timestamp BETWEEN ${hour_window_start_time}", "            and ${hour_window_end_time}", "          )", "        GROUP BY", "          api_id", "      ) e", "      LEFT JOIN (", "        SELECT", "          api_id,", "          api_type,", "          auth_scheme_id,", "          pii_info,", "          business_types,", "          api_auto_tag_id,", "          business_attack_count,", "          hole_attack_count,", "          attack_count", "        FROM(", "            SELECT", "              api_id,", "              if(notEmpty(anyHeavyIf(api_type, api_type != '' and api_type != 'restful')),", "                 anyHeavyIf(api_type, api_type != '' and api_type != 'restful'),", "                 anyHeavyIf(api_type, api_type != '')) as api_type,", "              anyHeavy(auth_scheme_id) as auth_scheme_id,", "              groupUniqArrayArray(", "                arrayMap(", "                  (x, y) -> concat(x, '|', y),", "                  pii_type,", "                  pii_position", "                )", "              ) as pii_info,", "              arrayFilter(", "                x -> notEmpty(x),", "                array(anyHeavyIf(api_auto_tag, api_auto_tag != ''))", "              ) as business_types,", "              anyHeavyIf(api_auto_tag_id, api_auto_tag_id != 0) as api_auto_tag_id", "            from", "              access_log", "            WHERE", "              notEmpty(api_id)", "              AND (", "                timestamp BETWEEN ${hour_window_start_time}", "                and ${hour_window_end_time}", "              )", "            GROUP BY", "              api_id", "          ) c", "          LEFT JOIN (", "            SELECT", "              api_id,", "              business_attack_count,", "              hole_attack_count,", "              plus(business_attack_count, hole_attack_count) as attack_count", "            FROM(", "                SELECT", "                  api_id,", "                  sum(event_count) business_attack_count", "                FROM", "                  (", "                    SELECT", "                      related_api_id as api_id,", "                      dictGetString(api_attack_type, 'business_type', event_type) as attack_bus_type,", "                      count() as event_count", "                    from", "                      api_attack_event FINAL ARRAY", "                      JOIN related_api_id", "                    PREWHERE", "                      (", "                        timestamp BETWEEN ${hour_window_start_time}", "                        and ${hour_window_end_time}", "                      )", "                    GROUP BY", "                      related_api_id,", "                      attack_bus_type", "                  )", "                WHERE", "                  attack_bus_type = dictGetString(trans, 'target', '业务行为攻击')", "                GROUP BY", "                  api_id", "              ) a FULL", "              OUTER JOIN (", "                SELECT", "                  api_id,", "                  sum(event_count) hole_attack_count", "                FROM", "                  (", "                    SELECT", "                      related_api_id as api_id,", "                      dictGetString(api_attack_type, 'business_type', event_type) as attack_bus_type,", "                      count() as event_count", "                    from", "                      api_attack_event FINAL ARRAY", "                      JOIN related_api_id", "                    PREWHERE", "                      (", "                        timestamp BETWEEN ${hour_window_start_time}", "                        and ${hour_window_end_time}", "                      )", "                    GROUP BY", "                      related_api_id,", "                      attack_bus_type", "                  )", "                WHERE", "                  attack_bus_type = '漏洞攻击'", "                GROUP BY", "                  api_id", "              ) b ON a.api_id = b.api_id", "          ) d ON c.api_id = d.api_id", "      ) f ON e.api_id = f.api_id", "  ) g", "  LEFT JOIN (", "    SELECT", "      api_id,", "      pii_type,", "      defect_name,", "      arrayConcat(pii_type, defect_name) as risks", "    FROM", "      (", "        SELECT", "          api_id,", "          groupUniqArrayArray(pii_type) as pii_type", "        from", "          access_log", "        WHERE", "          (", "            timestamp BETWEEN ${hour_window_start_time}", "            and ${hour_window_end_time}", "          )", "        group by", "          api_id", "      ) a FULL", "      OUTER JOIN (", "        SELECT", "          api_id,", "          groupUniqArrayArray(api_risk_name) as defect_name", "        from", "          access_log", "        WHERE", "          (", "            timestamp BETWEEN ${hour_window_start_time}", "            and ${hour_window_end_time}", "          )", "        group by", "          api_id", "      ) b ON a.api_id = b.api_id", "  ) h ON g.api_id = h.api_id", "ORDER BY", "  access_count DESC", "LIMIT", "  10000"]}}, "name": "abd_hour_资产清单按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "6 * * * *", "status": "enabled"}, "timestamp": "1726708713.64", "type": "cron_task"}, "bb3b4996-157e-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table bot_summary_hour --fields 'timestamp, app_site_id, category, type, total'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  bot_name,", "  dictGetString(", "    'default.api_bot',", "    'bot_cname',", "    tuple(bot_category)", "  ) as category,", "  count() as total", "from", "  access_log", "WHERE", "  isNotNull(is_good_bot)", "  AND notEmpty(api_id)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "group by", "  app_id,", "  bot_name,", "  category", "ORDER BY", "  total", "LIMIT", "  1000"]}}, "name": "abd_hour_BOT访问按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "1687935866.29", "type": "cron_task"}, "c3e9ce16-1586-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table defect_name_summary_hour --fields 'timestamp,endpoint,api_id,app_site_id,name,level,total,recent_detected_time,first_detected_time,source'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} AS time,", "  endpoint AS api_endpoint,", "  api_asset_id AS api_id,", "  app_site_id AS app_id,", "  risk_name,", "  risk_level,", "  uniq(scan_item_uuid) AS total,", "  max(timestamp) AS recent_detected_time,", "  min(timestamp) AS first_detected_time,", "  1 AS source", "FROM", "  scanner_assets ARRAY", "  JOIN api_risk_name AS risk_name,", "  api_risk_level AS risk_level", "WHERE", "  notEmpty(api_risk_name)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    AND ${hour_window_end_time}", "  )", "GROUP BY", "  endpoint,", "  app_site_id,", "  api_asset_id,", "  risk_name,", "  risk_level,", "  source", "UNION ALL", "SELECT", "  ${hour_window_start_time} AS time,", "  api_endpoint,", "  api_id,", "  app_id,", "  risk_name,", "  risk_level,", "  uniq(request_uuid) AS total,", "  max(timestamp) AS recent_detected_time,", "  min(timestamp) AS first_detected_time,", "  0 AS source", "FROM", "  access_log ARRAY", "  JOIN api_risk_name AS risk_name,", "  api_risk_level AS risk_level", "WHERE", "  notEmpty(api_risk_name)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    AND ${hour_window_end_time}", "  )", "GROUP BY", "  api_endpoint,", "  app_id,", "  api_id,", "  risk_name,", "  risk_level,", "  source", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_hour_缺陷按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "1694500849.77", "type": "cron_task"}, "d4c081a8-d5d0-11ed-b810-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table attack_source_summary_day_v2_local --fields 'timestamp,related_app_id,key_type,key_val,address,total'", "cmd_name": "updateSailfishSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  related_app_id,", "  'ip' as type,", "  key_val,", "  If(", "    tupleElement(related_ip_loc_tuple2 [1], 5),", "    dictGetString(trans, 'target', '内网'),", "    concat(", "      tupleElement(related_ip_loc_tuple2 [1], 4),", "      '-',", "      tupleElement(related_ip_loc_tuple2 [1], 3),", "      '-',", "      tupleElement(related_ip_loc_tuple2 [1], 2)", "    )", "  ) as address,", "  count() as total", "from", "  api_attack_event FINAL", "PREWHERE", "  key_type = 'IP'", "  AND (", "    timestamp BETWEEN ${day_window_start_time}", "    and ${day_window_end_time}", "  )", "GROUP BY", "  related_app_id,", "  type,", "  key_val,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  10000", "UNION ALL", "SELECT", "  ${day_window_start_time} as time,", "  related_app_id,", "  'account' as type,", "  key_val,", "  If(", "    tupleElement(related_ip_loc_tuple2 [1], 5),", "    dictGetString(trans, 'target', '内网'),", "    concat(", "      tupleElement(related_ip_loc_tuple2 [1], 4),", "      '-',", "      tupleElement(related_ip_loc_tuple2 [1], 3),", "      '-',", "      tupleElement(related_ip_loc_tuple2 [1], 2)", "    )", "  ) as address,", "  count() as total", "from", "  api_attack_event FINAL", "PREWHERE", "  key_type = dictGetString(trans, 'target', '账号')", "  AND (", "    timestamp BETWEEN ${day_window_start_time}", "    and ${day_window_end_time}", "  )", "GROUP BY", "  related_app_id,", "  type,", "  key_val,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_攻击来源按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "**********.02", "type": "cron_task"}, "e11116a0-645f-11eb-bbbf-00505694620f": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  threat_event_api_local (", "    timestamp,", "    group_name,", "    app_site_id,", "    event_ct_rt,", "    event_bytes_out_rt,", "    event_rsp_time_rt,", "    event_dc_ip_rt,", "    event_dc_user_rt,", "    event_dc_path_rt,", "    event_sr_rt,", "    reason,", "    score,", "    threat_type,", "    event_ct,", "    event_dc_ip,", "    event_dc_user,", "    event_dc_path,", "    event_bytes_out,", "    event_sr,", "    event_rsp_time,", "    event_inj,", "    key,", "    key_type", "  )", "SELECT", "  slot,", "  'source' as group_name,", "  app_site_id,", "  (count - summary_avg_count) / arrayReduce(", "    'max',", "    [summary_std_count,summary_avg_count*toFloat64(dictGetString('default.api_parameters','count_std_threshold',tuple('fp'))),0.01]", "  ) as event_ct_rt,", "  (avg_bytes_out - summary_avg_avg_bytes_out) / arrayReduce(", "    'max',", "    [summary_std_avg_bytes_out,summary_avg_avg_bytes_out*toFloat64(dictGetString('default.api_parameters','bytes_out_std_threshold',tuple('fp'))),0.01]", "  ) as event_bytes_out_rt,", "  (rsp_time - summary_avg_rsp_time) / arrayReduce(", "    'max',", "    [summary_std_rsp_time,summary_avg_rsp_time*toFloat64(dictGetString('default.api_parameters','rsp_time_std_threshold',tuple('fp'))),0.01]", "  ) as event_rsp_rt,", "  (dc_ip - summary_avg_dc_ip) / arrayReduce(", "    'max',", "    [summary_std_dc_ip,summary_avg_dc_ip*toFloat64(dictGetString('default.api_parameters','dc_ip_std_threshold',tuple('fp'))),0.01]", "  ) as event_dc_ip_rt,", "  (dc_user - summary_avg_dc_user) / arrayReduce(", "    'max',", "    [summary_std_dc_user,summary_avg_dc_user*toFloat64(dictGetString('default.api_parameters','dc_user_std_threshold',tuple('fp'))),0.01]", "  ) as event_dc_user_rt,", "  (dc_path - summary_avg_dc_path) / arrayReduce(", "    'max',", "    [summary_std_dc_path,summary_avg_dc_path*toFloat64(dictGetString('default.api_parameters','dc_path_std_threshold',tuple('fp'))),0.01]", "  ) as event_dc_path_rt,", "  (success_rate - summary_avg_success_rate) / arrayReduce(", "    'max',", "    [summary_std_success_rate,summary_avg_success_rate*toFloat64(dictGetString('default.api_parameters','success_rate_std_threshold',tuple('fp'))),0.01]", "  ) as event_sr_rt,", "  trim(", "    LEADING ','", "    FROM", "      concat(if(summary_data_volume>2,concat(", "        multiIf(", "          event_ct_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'count_range_parameter',", "              tuple('fp')", "            )", "          ),", "          'fp_count_high',", "          -1 * event_ct_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'count_range_parameter',", "              tuple('fp')", "            )", "          ),", "          'fp_count_low',", "          ''", "        ),", "        multiIf(", "          event_bytes_out_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'bytes_out_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_avg_bytes_out_high',", "          -1 * event_bytes_out_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'bytes_out_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_avg_bytes_out_low',", "          ''", "        ),", "        multiIf(", "          event_rsp_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'rsp_time_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_rsp_time_high',", "          -1 * event_rsp_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'rsp_time_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_rsp_time_low',", "          ''", "        ),", "        multiIf(", "          event_dc_path_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_path_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_dc_path_high',", "          -1 * event_dc_path_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_path_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_dc_path_low',", "          ''", "        ),", "        multiIf(", "          event_dc_ip_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_ip_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_dc_ip_high',", "          -1 * event_dc_ip_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_ip_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_dc_ip_low',", "          ''", "        ),", "        multiIf(", "          event_dc_user_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_user_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_dc_user_high',", "          -1 * event_dc_user_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_user_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_dc_user_low',", "          ''", "        ),", "        multiIf(", "          event_sr_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'success_rate_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_success_rate_high',", "          -1 * event_sr_rt > toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'success_rate_range_parameter',", "              tuple('fp')", "            )", "          ),", "          ',fp_success_rate_low',", "          ''", "        )),''),", "        if(inj > 0, ',Inject', '')", "      )", "  ) as reason,", "  if(summary_data_volume>2,arrayReduce(", "    'max',", "    [abs(event_ct_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'count_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_ct_rt > 0, 5, 1) + arrayReduce(", "    'max',", "    [abs(event_bytes_out_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'bytes_out_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_bytes_out_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_rsp_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'rsp_time_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_rsp_rt > 0, 10, 0.1) + arrayReduce(", "    'max',", "    [abs(event_dc_ip_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_ip_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_dc_ip_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_dc_user_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_user_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_dc_user_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_dc_path_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'dc_path_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_dc_path_rt > 0, 10, 1) + arrayReduce(", "    'max',", "    [abs(event_sr_rt)/toFloat64(", "            dictGetString(", "              'default.api_parameters',", "              'success_rate_range_parameter',", "              tuple('fp')", "            )", "          )-1,0]", "  ) * if(event_sr_rt > 0, 1, 15),0) + inj * 10 as score,", "  /* arrayJoin(", "    arrayFilter(", "      x -> x is not NULL,", "      [if(event_ct_rt>1 AND event_rsp_rt>1 AND count>100000, 57, NULL), if((count>1000 AND event_ct_rt>2) OR (event_bytes_out_rt>2 AND count>1000), 53,NULL ), if((dc_path>50 AND event_dc_path_rt>1) OR (summary_avg_dc_path=0 AND dc_path>100), 54,NULL), if(count>100 AND success_rate<0.4 AND event_sr_rt>1,51,NULL), if(inj>0,56,NULL)]", "    )", "  ) as threat_type, */", "  coalesce(arrayJoin(arrayFilter(x -> x is not NULL, [NULL])),0) as threat_type,", "  count,", "  dc_ip,", "  dc_user,", "  dc_path,", "  avg_bytes_out,", "  success_rate,", "  rsp_time,", "  inj,", "  fp as key,", "  2 as kt", "FROM", "  (", "    SELECT", "      COUNT(*) as count,", "      avg(body_bytes_out) as avg_bytes_out,", "      COUNT(DISTINCT path) as dc_path,", "      COUNT(DISTINCT src_ip) as dc_ip,", "      COUNT(DISTINCT user) as dc_user,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) / count as success_rate,", "      avg(upstream_response_time) as rsp_time,", "      fp,", "      app_id as app_site_id,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday,", "      toStartOfHour(timestamp) as slot,", "      sum(", "        if(hasAny(attack_type, ['Inject', 'AIWAF']), 1, 0)", "      ) as inj", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND notEmpty(fp)", "      AND timestamp BETWEEN toStartOfHour(now() - INTERVAL 1 HOUR)", "      and toStartOfHour(now()) -1 AND notEmpty(fp)", "    GROUP BY", "      fp,", "      app_site_id,", "      date_hour,", "      date_wday,", "      slot", "  ) as now_data LEFT ALL", "  JOIN (", "    SELECT", "      key,", "      app_site_id,count() as summary_data_volume,", "      avg(summary_count) as summary_avg_count,", "      STDDEV_POP(summary_count) as summary_std_count,", "      avg(summary_avg_bytes_out) as summary_avg_avg_bytes_out,", "      STDDEV_POP(summary_avg_bytes_out) as summary_std_avg_bytes_out,", "      avg(summary_dc_ip) as summary_avg_dc_ip,", "      STDDEV_POP(summary_dc_ip) as summary_std_dc_ip,", "      avg(summary_dc_user) as summary_avg_dc_user,", "      STDDEV_POP(summary_dc_user) as summary_std_dc_user,", "      avg(summary_dc_path) as summary_avg_dc_path,", "      STDDEV_POP(summary_dc_path) as summary_std_dc_path,", "      avg(summary_success / summary_count) as summary_avg_success_rate,", "      STDDEV_POP(summary_success / summary_count) as summary_std_success_rate,", "      if(isFinite(avgIf(summary_rsp_time, summary_rsp_time>0)), avgIf(summary_rsp_time, summary_rsp_time>0),1000) as summary_avg_rsp_time,", "      if(isFinite(STDDEV_POPIf(summary_rsp_time, summary_rsp_time>0)), STDDEV_POPIf(summary_rsp_time, summary_rsp_time>0),10000) as summary_std_rsp_time,", "      toHour(timestamp) as date_hour,", "      toDayOfWeek(timestamp) as date_wday", "    from", "      summary_data_api", "    WHERE", "      key_type = 2", "      AND timestamp > toStartOfDay(now() -14 * 86400)", "    GROUP BY", "      key,", "      app_site_id,", "      date_hour,", "      date_wday", "  ) as baseline_data ON now_data.fp = baseline_data.key", "  AND now_data.app_site_id = baseline_data.app_site_id", "  AND now_data.date_hour = baseline_data.date_hour", "  AND now_data.date_wday = baseline_data.date_wday", "HAVING", "  score >= 10"]}}, "name": "api_threat_fp_hour", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "8 * * * *", "status": "disabled"}, "timestamp": "1688273628.83", "type": "cron_task"}, "e6d5dfc6-d4dd-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table attack_name_summary_day_local --fields 'timestamp, related_app_id, related_api_id, title, risk_level, event_category, total'\n", "cmd_name": "updateSailfishSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  related_app_id,", "  arrayDistinct(groupArrayArray(related_api_id)),", "  title,", "  risk_level,", "  event_category,", "  count() as total", "FROM", "  api_attack_event FINAL", "PREWHERE", "  (", "    timestamp BETWEEN ${day_window_start_time}", "    and ${day_window_end_time}", "  )", "GROUP BY", "  related_app_id,", "  title,", "  risk_level,", "  event_category", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_攻击名称按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "enabled"}, "timestamp": "1690472760.27", "type": "cron_task"}, "e8b3d97e-6068-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    key_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_user,", "    summary_dc_path,", "    summary_req_len,", "    summary_dc_fp,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    key", "  )", "SELECT", "  summary_count,", "  slot,", "  group_name,", "  duration_type,", "  kt,", "  date_workhour,", "  summary_avg_bytes_out,", "  summary_dc_account,", "  summary_dc_path,", "  summary_req_len,", "  summary_dc_fp,", "  summary_rsp_time,", "  summary_success,", "  app_site_id,", "  src_ip", "from", "  (", "    SELECT", "      COUNT(*) as summary_count,", "      if(", "        timestamp < toStartOfDay(now() -86400) + 72000,", "        toStartOfDay(now() -86400) + 28800,", "        toStartOfDay(now() -86400) + 72000", "      ) as slot,", "      'source' as group_name,", "      'day' as duration_type,", "      1 as kt,", "      if(", "        toDayOfWeek(slot) < 6", "        AND toHour(slot) >= 8", "        AND toHour(slot) < 20,", "        1,", "        0", "      ) as date_workhour,", "      avg(body_bytes_out) as summary_avg_bytes_out,", "      COUNT(DISTINCT user) as summary_dc_account,", "      COUNT(DISTINCT path) as summary_dc_path,", "      avg(req_len) as summary_req_len,", "      COUNT(DISTINCT fp) as summary_dc_fp,", "      coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "      sum(", "        if(", "          status != 200", "          AND business_success != 1,", "          0,", "          1", "        )", "      ) as summary_success,", "      app_id as app_site_id,", "      src_ip", "    from", "      access_log", "    WHERE", "      is_api = 1", "      AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800", "      and toStartOfDay(now()) + 28799", "    group BY", "      src_ip,", "      app_site_id,", "      slot", "  ) as details", "  INNER JOIN (", "    SELECT", "      if(", "        count() > 1000,", "        arrayElement(", "          arrayReverseSort(groupArray(summary_count)),", "          1000", "        ),", "        0", "      ) as count_threshold,", "      app_site_id,", "      slot", "    from", "      (", "        SELECT", "          COUNT(*) as summary_count,", "          if(", "            timestamp < toStartOfDay(now() -86400) + 72000,", "            toStartOfDay(now() -86400) + 28800,", "            toStartOfDay(now() -86400) + 72000", "          ) as slot,", "          'source' as group_name,", "          'day' as duration_type,", "          1 as kt,", "          if(", "            toDayOfWeek(slot) < 6", "            AND toHour(slot) >= 8", "            AND toHour(slot) < 20,", "            1,", "            0", "          ) as date_workhour,", "          avg(body_bytes_out) as summary_avg_bytes_out,", "          COUNT(DISTINCT user) as summary_dc_account,", "          COUNT(DISTINCT path) as summary_dc_path,", "          avg(req_len) as summary_req_len,", "          COUNT(DISTINCT fp) as summary_dc_fp,", "          coalesce(avg(upstream_response_time), 0) as summary_rsp_time,", "          sum(", "            if(", "              status != 200", "              AND business_success != 1,", "              0,", "              1", "            )", "          ) as summary_success,", "          app_id as app_site_id,", "          src_ip", "        from", "          access_log", "        WHERE", "          is_api = 1", "          AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800", "          and toStartOfDay(now()) + 28799", "        group BY", "          src_ip,", "          app_site_id,", "          slot", "      )", "    group BY", "      app_site_id,", "      slot", "  ) as total ON details.app_site_id = total.app_site_id", "  AND details.slot = total.slot", "WHERE", "  summary_count > count_threshold"]}}, "name": "api_summary_ip_day", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 8 * * *", "status": "disabled"}, "timestamp": "1688273607.49", "type": "cron_task"}, "e9afeb02-15aa-11ee-ae20-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table piiindustry_summary_day --fields 'timestamp,app_site_id,api_ids,tag,total'\n", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${day_window_start_time} as time,", "  app_site_id,", "  groupUniqArrayArray(api_ids),", "  tag,", "  sum(total) as total", "from", "piiindustry_summary_hour", "WHERE", "  timestamp BETWEEN ${day_window_start_time}", "  AND ${day_window_end_time}", "GROUP BY", "  timestamp,", "  app_site_id,", "  tag", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_day_行业分级按天摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "10 * * * *", "status": "disabled"}, "timestamp": "1687953455.26", "type": "cron_task"}, "eb86e7c6-7b1d-11ef-8176-309c23993eda": {"detail": {"statement": {"cmd_args": "", "cmd_name": "kafkaSender", "cmd_switch": false, "sql_statement": ["insert into", "  api_baseline_risks_local (", "    timestamp,", "    src_ip,", "    app_name,", "    api_endpoint,", "    count_summary,", "    anomaly_tag,", "    type_score,", "    windows_type", "  )", "SELECT", "  t,", "  src_ip,", "  app_name,", "  api_endpoint,", "  groupArray(tag_summary) as count_summary,", "  groupArray(anomaly_status) as anomaly_tag,", "  arrayMax(", "    arrayMap(", "      x -> toUInt16(", "        dictGetString('default.baseline_risks', 'score', tuple(x))", "      ),", "      anomaly_tag", "    )", "  ) as type_score,", "  '1' as windows_type", "from", "  (", "    SELECT", "      toStartOfInterval(timestamp, interval 1 hour) as t,", "      src_ip,", "      multiIf(", "        notEmpty(inject_attack_type),", "        'ATT01',", "        has(attack_type, 'INVALID_API_ARGS'),", "        'ATT02',", "        api_seq_id > 0,", "        'ATT03',", "        isNotNull(manual_rule_id),", "        'ATT04',", "        'ATT05'", "      ) as anomaly_status,", "      app_name,", "      api_endpoint,", "      count() as tag_summary", "    from", "      access_log", "    WHERE", "      (", "        timestamp BETWEEN toStartOfHour(now()) - INTERVAL 1 HOUR", "        and toStartOfHour(now())", "      )", "      AND is_api = 1", "      AND notEmpty(api_attack_rule_ids)", "    group by", "      t,", "      src_ip,", "      anomaly_status,", "      app_name,", "      api_endpoint", "    UNION ALL", "    SELECT", "      toStartOfInterval(timestamp, interval 1 hour) as t,", "      src_ip,", "      case", "        WHEN tag_summary > 10", "        AND uniqExact(arrayJoin(pii_value)) > 50 THEN 'PII02'", "      end as anomaly_status,", "      app_name,", "      api_endpoint,", "      countIf(", "        notEmpty(", "          arrayMap(x -> position(x, 'RESP_') > 0, pii_position)", "        )", "      ) as tag_summary", "    from", "      access_log", "    WHERE", "      (", "        timestamp BETWEEN toStartOfHour(now()) - INTERVAL 1 HOUR", "        and toStartOfHour(now())", "      )", "      AND is_api = 1", "      AND notEmpty(pii_type)", "      AND notEmpty(", "        arrayMap(x -> position(x, 'RESP_') > 0, pii_position)", "      )", "    group by", "      t,", "      src_ip,", "      app_name,", "      api_endpoint", "    HAVING", "      notEmpty(anomaly_status)", "  )", "group by", "  t,", "  src_ip,", "  app_name,", "  api_endpoint"]}}, "name": "REP_基线异常记录(1小时窗口)", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "1 * * * *", "status": "enabled"}, "timestamp": "1727320029.9", "type": "cron_task"}, "f06e8d7c-d4d7-11ed-86f7-000c29c4b973": {"detail": {"statement": {"cmd_args": "--table defect_access_source_summary_hour --fields 'timestamp,app_site_id,name,type,address,total,high,middle,low'", "cmd_name": "updatePostgresqlSummary", "cmd_switch": true, "sql_statement": ["SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  src_ip as name,", "  'ip' as type,", "  if(", "    is_local_ip,", "    if(getMacro('language') = 'zh', '内网', 'Private Network'),", "    country || '-' || province || '-' || city", "  ) as address,", "  count() as total,", "  countIf(", "    has(api_risk_level, '高')", "    OR has(api_risk_level, 'high')", "  ) as high,", "  countIf(", "    has(api_risk_level, '中')", "    OR has(api_risk_level, 'middle')", "  ) as middle,", "  countIf(", "    has(api_risk_level, '低')", "    OR has(api_risk_level, 'low')", "  ) as low", "FROM", "  access_log", "WHERE", "  notEmpty(api_risk_name)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "GROUP BY", "  app_id,", "  name,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000", "UNION ALL", "SELECT", "  ${hour_window_start_time} as time,", "  app_id,", "  if(", "    empty(business_submit_username),", "    business_username,", "    business_submit_username", "  ) as name,", "  'account' as type,", "  if(", "    is_local_ip,", "    if(getMacro('language') = 'zh', '内网', 'Private Network'),", "    country || '-' || province || '-' || city", "  ) as address,", "  count() as total,", "  countIf(", "    has(api_risk_level, '高')", "    OR has(api_risk_level, 'high')", "  ) as high,", "  countIf(", "    has(api_risk_level, '中')", "    OR has(api_risk_level, 'middle')", "  ) as middle,", "  countIf(", "    has(api_risk_level, '低')", "    OR has(api_risk_level, 'low')", "  ) as low", "FROM", "  access_log", "WHERE", "  notEmpty(api_risk_name)", "  AND (", "    timestamp BETWEEN ${hour_window_start_time}", "    and ${hour_window_end_time}", "  )", "  AND notEmpty(name)", "GROUP BY", "  app_id,", "  name,", "  address", "ORDER BY", "  total DESC", "LIMIT", "  1000"]}}, "name": "abd_hour_缺陷访问来源按时摘要", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 * * * *", "status": "enabled"}, "timestamp": "**********.98", "type": "cron_task"}, "f1b50f22-6048-11eb-86d7-0050569493e7": {"detail": {"statement": {"cmd_args": "", "cmd_name": "setReputation", "cmd_switch": false, "sql_statement": ["INSERT INTO", "  summary_data_api_local (", "    summary_count,", "    timestamp,", "    group_name,", "    duration_type,", "    date_workhour,", "    summary_avg_bytes_out,", "    summary_dc_user,", "    summary_dc_ip,", "    summary_req_len,", "    summary_dc_fp,", "    summary_rsp_time,", "    summary_success,", "    app_site_id,", "    hostname,", "    path,", "    action,", "    api_id", "  )", "SELECT", "  COUNT(*) as summary_count,", "  if(timestamp < toStartOfDay(now()-86400) + 72000, toStartOfDay(now()-86400) + 28800, toStartOfDay(now()-86400) + 72000 ) as slot,", "  'path' as group_name,", "  'day' as duration_type,", "  if(", "    toDayOfWeek(slot) < 6", "    AND toHour(slot) >= 8", "    AND toHour(slot) < 20,", "    1,", "    0", "  ) as date_workhour,", "  avg(body_bytes_out) as summary_avg_bytes_out,", "  COUNT(DISTINCT user) as summary_dc_account,", "  COUNT(DISTINCT src_ip) as summary_dc_ip,", "  avg(req_len) as summary_req_len,", "  COUNT(", "    DISTINCT coalesce(fingerprint_browser, mobile_device_fingerprint)", "  ) as summary_dc_fp,", "  coalesce(avg(upstream_response_time),0) as summary_rsp_time,", "  sum(", "    if(", "      status != 200", "      AND business_success != 1,", "      0,", "      1", "    )", "  ) as summary_success,", "  anyHeavy(app_id) as app_site_id,", "  hostname,", "  multiIf(", "        notEmpty(argMax(api_name, timestamp)),", "        argMax(api_name, timestamp),", "        notEmpty(argMax(api_endpoint, timestamp)),", "        argMax(api_endpoint, timestamp),", "        anyHeavy(path)", "      ) as path,", "  arrayStringConcat(groupUniqArray(action),',') as action,", "  api_id", "from", "  access_log", "WHERE", "  is_api = 1 AND timestamp BETWEEN toStartOfDay(now() - 86400) + 28800 and toStartOfDay(now()) + 28799", "group BY", "  hostname,", "  api_id,", "  slot"]}}, "name": "api_summary_path_day", "summary": {"cron_type": "sql", "freq_expr_type": "normal", "frequency": "5 8 * * *", "status": "disabled"}, "timestamp": "1688273650.44", "type": "cron_task"}}